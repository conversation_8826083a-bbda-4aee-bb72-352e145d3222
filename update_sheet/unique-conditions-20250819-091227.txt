================================================================================
PHÂN TÍCH ĐIỀU KIỆN DUY NHẤT TỪ CÁC FILE LOG
================================================================================
Thư mục log: /root/projects/onepay/tool-project/analyze-log/all-logs
Thư mục kết quả: /root/projects/onepay/tool-project/update_sheet
Thời gian: 09:12:27 19/8/2025
Tổng số file xử lý: 23
Tổng số điều kiện: 3820
Điều kiện duy nhất: 638

THỐNG KÊ THEO LOẠI TOÁN TỬ:
--------------------------------------------------
===: 2 lần
==: 10 lần
!==: 3 lần
!=: 3805 lần
--------------------------------------------------

=== (2 điều kiện duy nhất):
--------------------------------------------------------------------------------
1. (l=!0,k.call(h,q))}}var h=this,l=!1;return{resolve:f(this.Ac),reject:f(this.mb)}};e.prototype.Ac=function(f){if(f === this)this.mb(new TypeError("A Promise cannot resolve to itself"));else if(f instanceof e)this.Dc(f);else{a:switch(typeof f){case "object":var h=null!=f
   Files: log-paygate-generalv2-********-181228.txt

2. (this.cardTypeBank === 'bank_account_number' && !(cardNo != null
   Files: log-paygate-dienmayxanh-********-181228.txt

== (8 điều kiện duy nhất):
--------------------------------------------------------------------------------
1. (token || _auth == 1) && _b != 16; else noBankSelected
   Files: log-paygate-generalv2-********-181228.txt

2. <div *ngIf="bnpl.code == 'homepaylater' && ((selectedBnpl != bnpl) || (d_bnpl_number == 1))"
   Files: log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt

3. <div *ngIf="bnpl.code == 'homepaylater' && (selectedBnpl != bnpl)"
   Files: log-paygate-promotion-********-181230.txt

4. <div *ngIf="bnpl.code == 'insta' && ((selectedBnpl != bnpl) || (d_bnpl_number == 1))"
   Files: log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt

5. $e.prototype.xa=function(a,b){var c=this.nb[a];c||(c=[],this.nb[a]=c);c.push(b);c=this.ob[a];if(!c&&this.L){try{var d=this.g,e=Ge(this.L).__WA_RES__;if(e){var g=JSON.parse(e);if(g&&g.requestId == a){var f=d.location.hash;if(f){var h=encodeURIComponent("__WA_RES__")+"=";e=-1;do if(e=f.indexOf(h,e),-1!=e){var l=0<e?f.substring(e-1,e):"";if(""==l||"?"==l||"#"==l||"&"==l){var k=f.indexOf("&",e+1);-1==k&&(k=f.length);f=f.substring(0,e)+f.substring(k+1)}else e++}while(-1!=e&&e<f.length)}var q=f;q=q||"";if(q!=
   Files: log-paygate-generalv2-********-181228.txt

6. cardInfoEnable == 'card_name' && _b != 19
   Files: log-paygate-traveloka-********-181230.txt

7. cardInfoEnable == 'card_name' && _b != 19 && _b != 16
   Files: log-paygate-traveloka-********-181230.txt

8. cardInfoEnable == 'card_number' && _b != 19
   Files: log-paygate-traveloka-********-181230.txt

!== (3 điều kiện duy nhất):
--------------------------------------------------------------------------------
1. b){if("object" !== typeof b||null===b)return a;for(var c in b)b.hasOwnProperty(c)&&void 0!==b[c]&&(null==b[c]?a[c]=null:null==a[c]||"object"!==typeof a[c]||"object"!==typeof b[c]||Array.isArray(b[c])||Array.isArray(a[c])?a[c]=b[c]:yc(a[c],b[c]));return a}function zc(a){var b=0,c;if(0==a.length)return b;for(c=0;c<a.length;c++){var d=a.charCodeAt(c);b=(b<<5)-b+d;b&=b}return b}function Ac(a){console.error("DEVELOPER_ERROR in "+a.aa+": "+a.errorMessage)};var Bc=function(a){Bc[" "](a);return a};Bc[" "]=function(){};var Cc=Tb&&$b&&0<$b.brands.length?!1:I("Trident")||I("MSIE"),Dc=I("Gecko")&&!(-1!=Zb().toLowerCase().indexOf("webkit")
   Files: log-paygate-generalv2-********-181228.txt

2. if ((typeof document !== "undefined" && document !== null ? (_ref = document.selection) != null ? _ref.createRange : void 0 : void 0) != null) {
   Files: log-paygate-iframe-********-181229.txt

3. if(-1 !== window.navigator.userAgent.indexOf("OPR/")||b||c)return a.o.push(35),!1;if(a.qa)return!0;if(google.payments.api.DisablePaymentRequest&&!google.payments.api.EnablePaymentRequest)return a.o.push(3),!1;b=window.navigator.userAgent.match(/Android/i);c=window.navigator.userAgent.match(/Chrome\/([0-9]+)\./i);if(!(null!=b
   Files: log-paygate-generalv2-********-181228.txt

!= (625 điều kiện duy nhất):
--------------------------------------------------------------------------------
1. _auth != '1' -->
   Files: log-paygate-generalv2-********-181228.txt

2. _auth != '1'"
   Files: log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-promotion-********-181230.txt

3. _auth != 0
   Files: log-paygate-generalv2-********-181228.txt

4. _auth != 0"
   Files: log-paygate-generalv2-********-181228.txt

5. _auth != 1"
   Files: log-paygate-apple-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-promotion-********-181230.txt

6. _b != 1"
   Files: log-paygate-traveloka-********-181230.txt

7. _b != 12 && _b != 3
   Files: log-paygate-traveloka-********-181230.txt

8. _b != 18)">
   Files: log-paygate-domestic-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

9. _b != 2 )">
   Files: log-paygate-mafc-********-181229.txt

10. _b != 2 || (_b == 2 && !isOffTechcombank && checkTwoEnabled)
   Files: log-paygate-token-********-181230.txt

11. _b != 2) || ((_b == 67 || _b == 2)
   Files: log-paygate-token-********-181230.txt

12. _b != 2)">
   Files: log-paygate-domestic-********-181228.txt, log-paygate-generalv2-********-181228.txt

13. _b != 22 && _b != 24 && _b != 8 && _b != 18
   Files: log-paygate-traveloka-********-181230.txt

14. _b != 57)">
   Files: log-paygate-promotion-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

15. _b != 6 && _b != 2
   Files: log-paygate-domestic-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt

16. _b != 6 && _b != 2 && _b != 5 && _b != 48 && _b != 67 && _b != 57
   Files: log-paygate-promotion-********-181230.txt

17. _b != 6 && _b != 2 && _b != 67 && _b != 57
   Files: log-paygate-promotion-********-181230.txt

18. _formCard.csc != null
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt

19. _idInvoice != 0) {
   Files: log-paygate-generalv2-********-181228.txt

20. _locale != 'vi'"
   Files: log-paygate-generalv2-********-181228.txt

21. _paymentService.getCurrentPage() != 'otp'"
   Files: log-paygate-traveloka-********-181230.txt

22. _re.body != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

23. _re.body?.state != 'canceled')
   Files: log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-traveloka-********-181230.txt

24. _re.body.authorization.links != null
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt

25. _re.body.links != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-traveloka-********-181230.txt

26. _re.body.links.cancel != null) {
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt

27. _re.body.links.merchant_return != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt

28. _re.body.links.merchant_return.href != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt

29. _re.links != null
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-invoice-********-181229.txt, log-paygate-traveloka-********-181230.txt

30. _re.links.merchant_return != null
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-invoice-********-181229.txt, log-paygate-traveloka-********-181230.txt

31. _re.links.merchant_return.href != null) {
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-invoice-********-181229.txt, log-paygate-traveloka-********-181230.txt

32. _re.status != '201')
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt

33. _re.status != '201') {
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-upos-********-181230.txt

34. _showAVS != true"
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

35. _showCardName != true"
   Files: log-paygate-general-fee-outside-********-181228.txt, log-paygate-invoice-********-181229.txt

36. _showEmail != true"
   Files: log-paygate-invoice-********-181229.txt

37. _showNameOnCard != true"
   Files: log-paygate-apple-********-181227.txt, log-paygate-general-fee-inside-********-181228.txt

38. _showPhone != true"
   Files: log-paygate-invoice-********-181229.txt

39. _showTP != true"
   Files: log-paygate-ita-********-181229.txt

40. _val.value != null
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt

41. -1 != a.indexOf('"')
   Files: log-paygate-generalv2-********-181228.txt

42. -1 != a.indexOf("'")
   Files: log-paygate-generalv2-********-181228.txt

43. -1 != a.indexOf("\x00")
   Files: log-paygate-generalv2-********-181228.txt

44. -1 != a.indexOf("&")
   Files: log-paygate-generalv2-********-181228.txt

45. -1 != a.indexOf("<")
   Files: log-paygate-generalv2-********-181228.txt

46. -1 != a.indexOf(">")
   Files: log-paygate-generalv2-********-181228.txt

47. -1 != e
   Files: log-paygate-generalv2-********-181228.txt

48. -1 != e.indexOf("/.")){d=0==e.lastIndexOf("/",0);e=e.split("/");for(var g=[],f=0;f<e.length;){var h=e[f++];"."==h?d&&f==e.length&&g.push(""):".."==
   Files: log-paygate-generalv2-********-181228.txt

49. -1 != e){var l=0<e?f.substring(e-1,e):"";if(""==l||"?"==l||"#"==l||"&"==l){var k=f.indexOf("&",e+1);-1==k&&(k=f.length);f=f.substring(0,e)+f.substring(k+1)}else e++}while(-1!=e
   Files: log-paygate-generalv2-********-181228.txt

50. -1 != Function.prototype.bind.toString().indexOf("native code")?va:wa
   Files: log-paygate-generalv2-********-181228.txt

51. !(filteredDataOtherMobile.length > 0 && ((filteredDataDeeplink.length > 0 && deeplink_status) && (qr_version2 != 'None')))
   Files: log-paygate-general-********-181228.txt

52. .brand != null
   Files: log-paygate-vinfast-********-181231.txt

53. .brand.id != null
   Files: log-paygate-vinfast-********-181231.txt

54. .href != null
   Files: log-paygate-vinfast-********-181231.txt

55. .issuer != null
   Files: log-paygate-vinfast-********-181231.txt

56. .issuer.swift_code != null
   Files: log-paygate-vinfast-********-181231.txt

57. .links != null
   Files: log-paygate-vinfast-********-181231.txt

58. .links.approval != null
   Files: log-paygate-vinfast-********-181231.txt

59. .links.approval.href != null
   Files: log-paygate-vinfast-********-181231.txt

60. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72' , '73' , '74' , '75'].indexOf(this._b.toString()) != -1) {
   Files: log-paygate-megags-********-181230.txt

61. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72' , '73', '74', '75'].indexOf(this._b.toString()) != -1) {
   Files: log-paygate-domestic-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

62. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75'].indexOf(this._b.toString()) != -1) {
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-tns-********-181230.txt

63. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75'].indexOf(this._b.toString()) != -1) {// duongpxt 19255: Them vietbank napas id 72
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-traveloka-********-181230.txt

64. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73','74','75'].indexOf(this._b.toString()) != -1) {
   Files: log-paygate-enetviet-********-181228.txt

65. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73'].indexOf(this._b.toString()) != -1) {
   Files: log-paygate-token-********-181230.txt

66. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73'].indexOf(this._b.toString()) != -1) {// duongpxt 19255: Them vietbank napas id 72
   Files: log-paygate-apple-********-181227.txt, log-paygate-traveloka-********-181230.txt

67. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '74', '75'].indexOf(this._b.toString()) != -1) {
   Files: log-paygate-promotion-********-181230.txt

68. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72','73','74','75'].indexOf(this._b.toString()) != -1) {
   Files: log-paygate-vinfast-********-181231.txt

69. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '73','74','75'].indexOf(this._b.toString()) != -1) {
   Files: log-paygate-promotion-********-181230.txt

70. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71'].indexOf(this._b.toString()) != -1) {
   Files: log-paygate-general-fee-outside-********-181228.txt

71. 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt

72. 'total_paid': this.data?.amount ? (item.diff_amount != 0 ? this._util.formatMoney(parseFloat(item.diff_amount) + parseFloat(this.data?.amount), 0, '.', ',') : this._util.formatMoney(parseFloat(this.data?.amount), 0, '.', ',')) : 0,
   Files: log-paygate-promotion-********-181230.txt

73. 'total_paid': this.data?.amount ? (item.diff_amount != 0 ? this._util.formatMoney(parseFloat(item.diff_amount) + parseFloat(this.data?.amount), 0, '.', ',') : this._util.formatMoney(parseFloat(this.data?.amount), 0, '.', ',')) :0,
   Files: log-paygate-apple-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt

74. "_blank" != h
   Files: log-paygate-generalv2-********-181228.txt

75. "_top" != A(d)
   Files: log-paygate-generalv2-********-181228.txt

76. "_top" != h
   Files: log-paygate-generalv2-********-181228.txt

77. "" != g[0])&&g.pop(),d&&f==e.length&&g.push("")):(g.push(h),d=!0)}d=g.join("/")}else d=e}c?(O(b),b.M=d):c=""!==a.H.toString();c?Uc(b,a.H.clone()):c=!!a.L;c&&(a=a.L,O(b),b.L=a);return b};N.prototype.clone=function(){return new N(this)};
   Files: log-paygate-generalv2-********-181228.txt

78. "/" != c.charAt(0)
   Files: log-paygate-generalv2-********-181228.txt

79. "function" != typeof a
   Files: log-paygate-generalv2-********-181228.txt

80. "function" != typeof a.ea){if("undefined"!==typeof r.Map&&a instanceof
   Files: log-paygate-generalv2-********-181228.txt

81. "function" != typeof d.prototype[a]
   Files: log-paygate-generalv2-********-181228.txt

82. "function" != typeof Object.seal)return!1;try{var h=Object.seal({x:4}),l=new a(v([[h,"s"]]));if("s"!=l.get(h)||1!=l.size||l.get({x:4})||l.set({x:4},"t")!=l||2!=l.size)return!1;var k=t(l,"entries").call(l),q=k.next();if(q.done||q.value[0]!=h||"s"!=q.value[1])return!1;q=k.next();return q.done||4!=q.value[0].x||"t"!=q.value[1]||!k.next().done?!1:!0}catch(y){return!1}}())return a;var b=new r.WeakMap,c=function(h){this[0]=
   Files: log-paygate-generalv2-********-181228.txt

83. "plain" != h
   Files: log-paygate-generalv2-********-181228.txt

84. "s" != q.value[1])return!1;q=k.next();return q.done||4!=q.value[0].x||"t"!=q.value[1]||!k.next().done?!1:!0}catch(y){return!1}}())return a;var b=new r.WeakMap,c=function(h){this[0]=
   Files: log-paygate-generalv2-********-181228.txt

85. "short" != h
   Files: log-paygate-generalv2-********-181228.txt

86. "t" != q.value[1]
   Files: log-paygate-generalv2-********-181228.txt

87. ( (test[0] << 8) | test[1]) != code) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

88. (_b != 12 && _b != 18 && _b != 6 && _b != 2 && _b != 5 && _b != 48 && _b != 67 && _b != 57)
   Files: log-paygate-promotion-********-181230.txt

89. (_b != 18 && _b != 6 && _b != 2 )
   Files: log-paygate-mafc-********-181229.txt

90. (_b != 18 && _b != 6 && _b != 2 && _b != 57)
   Files: log-paygate-promotion-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

91. (_b != 18 && _b != 6 && _b != 2)
   Files: log-paygate-domestic-********-181228.txt, log-paygate-generalv2-********-181228.txt

92. (((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b) || (promotionList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token
   Files: log-paygate-promotion-********-181230.txt

93. (((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b) || (promotionList.length > 0 && !_b)) && ((type != 4 || uniqueTokenBank) || (type == 4 && !_b))) && !token
   Files: log-paygate-promotion-********-181230.txt

94. (((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token
   Files: log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-token-********-181230.txt

95. ((((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b) || (promotionList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token)
   Files: log-paygate-promotion-********-181230.txt

96. ((((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b) || (promotionList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token) && !version2
   Files: log-paygate-promotion-********-181230.txt

97. ((((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b) || (promotionList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token) && version2
   Files: log-paygate-promotion-********-181230.txt

98. ((((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token)
   Files: log-paygate-promotion-********-181230.txt

99. (a=a.userAgent)?a:""}var $b,ac=x.navigator;$b=ac?ac.userAgentData||null:null;function I(a){return-1 != Zb().indexOf(a)};var bc={},J=function(a,b){if(b!==bc)throw Error("SafeHtml is not meant to be built directly")
   Files: log-paygate-generalv2-********-181228.txt

100. (feeService['atm'] && feeService['atm']['fee'] != 0) || atm != 0
   Files: log-paygate-enetviet-********-181228.txt

101. (this.bnplDetail.phoneNumber?.length != 10
   Files: log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt

102. (this.fundiinDetail.phoneNumber?.length != 10
   Files: log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt

103. {{plan.type != '1'
   Files: log-paygate-apple-********-181227.txt

104. } else if (_re.body != null
   Files: log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-invoice-********-181229.txt, log-paygate-traveloka-********-181230.txt

105. } else if (_re.body.links != null
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt

106. } else if (['oceanbank_mobile_account'].indexOf(id) != -1) {
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

107. } else if (['shb_customer_id'].indexOf(id) != -1) {
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

108. } else if (['techcombank_account', 'vib_account', 'dongabank_account', 'tpbank_account', 'bidv_account', 'pvcombank_account', 'shb_account', 'oceanbank_online_account'].indexOf(id) != -1) {
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

109. } else if (idInvoice != null
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt

110. } else if (params['locale'] != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

111. } else if (this._b != 18) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

112. } else if (this._res_polling.merchant != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

113. } else if (this._res_polling.payments != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

114. } else if (this._res_post.links != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

115. } else if (this._res.merchant != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

116. } else if (this._res.payments != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

117. } else if (this._res.payments != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

118. } else if (this.res.payments[this.res.payments.length - 1].instrument.issuer.brand != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-promotion-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

119. } else if(params['locale'] != null
   Files: log-paygate-homecredit-********-181229.txt

120. }),l=new a([[f,2],[h,3]]);if(2 != l.get(f)
   Files: log-paygate-generalv2-********-181228.txt

121. }else if(params['locale'] != null
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-invoice-********-181229.txt

122. *ngIf="(_b != 12
   Files: log-paygate-promotion-********-181230.txt

123. *ngIf="(_b != 18
   Files: log-paygate-domestic-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

124. *ngIf="item.checkbox_bnpl || (item.mobileWallet != 1
   Files: log-paygate-promotion-********-181230.txt

125. *ngIf="item.checkbox_domescard || (item.mobileWallet != 1
   Files: log-paygate-promotion-********-181230.txt

126. *ngIf="item.checkbox_international || (item.mobileWallet != 1
   Files: log-paygate-promotion-********-181230.txt

127. *ngIf="item.checkbox_mobileWallet || (item.bnpl != 1
   Files: log-paygate-promotion-********-181230.txt

128. *ngIf="item.checkbox_qr || (item.mobileWallet != 1
   Files: log-paygate-promotion-********-181230.txt

129. && ((homecreidtDetail['phoneNumber'].length != 10) || !this.homecreidtDetail['phoneNumber'].startsWith('0'))
   Files: log-paygate-apple-********-181227.txt, log-paygate-promotion-********-181230.txt

130. && ((this._b != 20
   Files: log-paygate-dienmayxanh-********-181228.txt

131. <div class="form-field form-field-small margin-right-2" [class.visible_hidden]="!valid_card" *ngIf="d_card_date && (_b != 3
   Files: log-paygate-domestic-********-181228.txt, log-paygate-mafc-********-181229.txt

132. <img src="assets/img/banklogo/{{cbtest.value != null?cbtest.value:combobankValue}}_logo.png" style="height:25px;margin-right: 10px;"/>{{cbtest.value!=null?getBankName(cbtest.value):getBankName(combobankValue)}}
   Files: log-paygate-homecredit-********-181229.txt

133. <mat-tab [label]="'bank_card_number' | translate" *ngIf="(_b != 67
   Files: log-paygate-token-********-181230.txt

134. || (brand_id != 'amex'
   Files: log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt

135. || (id != 'amex'
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt

136. $('[draggable != true]', _.$slideTrack).off('dragstart', _.preventDefault);
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

137. $('[draggable != true]', _.$slideTrack).on('dragstart', _.preventDefault);
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

138. 1 != b.length?-1:a.indexOf(b,0)
   Files: log-paygate-generalv2-********-181228.txt

139. 1 != l.size
   Files: log-paygate-generalv2-********-181228.txt

140. 2 != l.size)return!1;var k=t(l
   Files: log-paygate-generalv2-********-181228.txt

141. 3 != l.get(h))return!1;l.delete(f);l.set(h
   Files: log-paygate-generalv2-********-181228.txt

142. 4 != q.value[0].x
   Files: log-paygate-generalv2-********-181228.txt

143. a.i):b};var sf="actions.google.com amp-actions.sandbox.google.com amp-actions-staging.sandbox.google.com amp-actions-autopush.sandbox.google.com payments.developers.google.com payments.google.com".split(" "),X=function(a,b,c,d){this.gb=b;Rd(a);this.Jb=null;this.s=a.environment||"TEST";tf||(tf=-1 != sf.indexOf(window.location.hostname)
   Files: log-paygate-generalv2-********-181228.txt

144. auth = paramUserName != null ? paramUserName : ''
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

145. b),g;if(g=0<=e)z(null != d.length),Array.prototype.splice.call(d
   Files: log-paygate-generalv2-********-181228.txt

146. bankid != 31) {
   Files: log-paygate-homecredit-********-181229.txt

147. binAtm[firstSixDigits] != bank_code){
   Files: log-paygate-iframe-********-181229.txt

148. bnpl.code != 'homepaylater'"
   Files: log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-promotion-********-181230.txt

149. bnpl.code != 'kbank'
   Files: log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-promotion-********-181230.txt

150. bnpl.code != 'kbank'"
   Files: log-paygate-apple-********-181227.txt

151. bnplDetail.method != 'PL'"
   Files: log-paygate-apple-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-promotion-********-181230.txt

152. bnplDetail.method != 'SP'"
   Files: log-paygate-apple-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-promotion-********-181230.txt

153. c?(d=a.O,O(b),b.O=d):c=null != a.j
   Files: log-paygate-generalv2-********-181228.txt

154. c.origin != ff(b))throw Error("channel mismatch");var d=c.data;if(d.redirectEncryptedCallbackData)return L=3,gf(b
   Files: log-paygate-generalv2-********-181228.txt

155. c=null != (e=C.buy[d])?e:c
   Files: log-paygate-generalv2-********-181228.txt

156. c=null != (f=C.checkout[d])?f:c
   Files: log-paygate-generalv2-********-181228.txt

157. c=null != (g=C.book[d])?g:c
   Files: log-paygate-generalv2-********-181228.txt

158. c=null != (h=C.donate[d])?h:c
   Files: log-paygate-generalv2-********-181228.txt

159. c=null != (k=C.pay[d])?k:c
   Files: log-paygate-generalv2-********-181228.txt

160. c=null != (l=C.order[d])?l:c
   Files: log-paygate-generalv2-********-181228.txt

161. c=null != (q=C.subscribe[d])?q:c
   Files: log-paygate-generalv2-********-181228.txt

162. cardNo != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

163. color != this.defaultColor.color5) {
   Files: log-paygate-generalv2-********-181228.txt

164. color != this.defaultColor.color5) { // tìm xem màu có trong config không
   Files: log-paygate-generalv2-********-181228.txt

165. const tokenListsFilter = tokenLists.filter(item => item.id != tokenData);
   Files: log-paygate-apple-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-traveloka-********-181230.txt

166. csc != null
   Files: log-paygate-dienmayxanh-********-181228.txt

167. d_card_date && (_b != 3 && _b != 19 && _b != 12 && _b != 18)
   Files: log-paygate-promotion-********-181230.txt

168. d_card_date && (_b != 3 && _b != 19 && _b != 18)
   Files: log-paygate-domestic-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

169. dataPassed.body != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

170. dataPassed.body.themes.allow_save_token != true ? dataPassed.body.themes.allow_save_token : true
   Files: log-paygate-domestic-********-181228.txt

171. dataPassed.body.themes.border_color != true ? dataPassed.body.themes.border_color : ''
   Files: log-paygate-domestic-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-vinfast-********-181231.txt

172. dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

173. document.activeElement.id != 'email'"
   Files: log-paygate-apple-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-promotion-********-181230.txt

174. document.activeElement.id != 'fullname'"
   Files: log-paygate-apple-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-promotion-********-181230.txt

175. document.activeElement.id != 'fullName'"
   Files: log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt

176. document.activeElement.id != 'phoneNumber'"
   Files: log-paygate-apple-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-promotion-********-181230.txt

177. document.documentMode != null
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt

178. e.type != 'ewallet') || (regex.test(strTest)
   Files: log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt

179. e),b.Ja=c,a.push(b));return b};ke.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.v))return!1;var e=this.v[a];b=le(e,b,c,d);return-1<b?(je(e[b]),z(null != e.length),Array.prototype.splice.call(e
   Files: log-paygate-generalv2-********-181228.txt

180. e[g];null != f&&(b||(b=a),b+=(b.length>a.length?"&":"")+encodeURIComponent(d)+"="+encodeURIComponent(String(f)))}}return b};var kb=Array.prototype.indexOf?function(a,b){z(null!=a.length);return Array.prototype.indexOf.call(a
   Files: log-paygate-generalv2-********-181228.txt

181. Ec=-1 != Zb().toLowerCase().indexOf("webkit")
   Files: log-paygate-generalv2-********-181228.txt

182. Ef);else for(var Ff in Ef)if("prototype" != Ff)if(Object.defineProperties){var Gf=Object.getOwnPropertyDescriptor(Ef,Ff);Gf&&Object.defineProperty(Y,Ff,Gf)}else Y[Ff]=Ef[Ff];Y.pa=Ef.prototype;var Z=function(a
   Files: log-paygate-generalv2-********-181228.txt

183. else if (this._res_post.links != null
   Files: log-paygate-invoice-********-181229.txt

184. else if (this._res.payments != null
   Files: log-paygate-traveloka-********-181230.txt

185. errorCode != '253'
   Files: log-paygate-apple-********-181227.txt, log-paygate-invoice-********-181229.txt, log-paygate-traveloka-********-181230.txt

186. errorCode != 'overtime'"
   Files: log-paygate-apple-********-181227.txt, log-paygate-invoice-********-181229.txt, log-paygate-traveloka-********-181230.txt

187. event.target != elementBg) {
   Files: log-paygate-generalv2-********-181228.txt

188. f.initCustomEvent("unhandledrejection",!1,!0,f));f.promise=this;f.reason=this.W;return l(f)};e.prototype.kc=function(){if(null != this.na){for(var f=0;f<this.na.length;++f)g.vb(this.na[f]);
   Files: log-paygate-generalv2-********-181228.txt

189. f)};e.prototype.Vb=function(f,h){if(0 != this.oa)throw Error("Cannot settle("+f+", "+h+"): Promise already settled in state"+this.oa);this.oa=f;this.W=h;2===this.oa&&this.Bc();this.kc()};e.prototype.Bc=function(){var f=this;d(function(){if(f.qc()){var h=p.console;"undefined"!==
   Files: log-paygate-generalv2-********-181228.txt

190. feeService['amex_d']['fee'] != 0"
   Files: log-paygate-enetviet-********-181228.txt, log-paygate-tns-********-181230.txt

191. feeService['amex_i']['fee'] != 0"
   Files: log-paygate-tns-********-181230.txt

192. feeService['atm']['fee'] != 0"
   Files: log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt

193. feeService['atm']['fee'] != 0) || atm != 0"></span>
   Files: log-paygate-enetviet-********-181228.txt

194. feeService['pp']['fee'] != 0"
   Files: log-paygate-enetviet-********-181228.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt

195. feeService['qr']['fee'] != 0"
   Files: log-paygate-enetviet-********-181228.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt

196. feeService['vietqr']['fee'] != 0"
   Files: log-paygate-enetviet-********-181228.txt

197. feeService['visa_mastercard_d']['fee'] != 0"
   Files: log-paygate-enetviet-********-181228.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt

198. feeService['visa_mastercard_i']['fee'] != 0"
   Files: log-paygate-tns-********-181230.txt

199. filteredDataOtherMobile.length > 0 && ((filteredDataDeeplink.length > 0 && deeplink_status) && (qr_version2 != 'None'))
   Files: log-paygate-general-********-181228.txt

200. for(var a in this.U){var b=this.U[a];b.port1&&Le(b.port1);b.port2&&Le(b.port2)}this.U=null}};T.prototype.isConnected=function(){return null != this.C};
   Files: log-paygate-generalv2-********-181228.txt

201. function rd(a,b){var c=null != navigator.language?navigator.language.substring(0,5):"en"
   Files: log-paygate-generalv2-********-181228.txt

202. g[f].capture != b))return!0;return!1})};
   Files: log-paygate-generalv2-********-181228.txt

203. g[f].type != d
   Files: log-paygate-generalv2-********-181228.txt

204. homecreditDetail['phoneNumber'].length != 10
   Files: log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt

205. homecreditDetail['phoneNumber'].length != 11)
   Files: log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt

206. idInvoice != 0)
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-upos-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

207. idInvoice != 0) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-upos-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

208. if ( (data >>> length) != 0) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

209. if ( this._b != 9
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-homecredit-********-181229.txt

210. if (_formCard.card_number != null) cardNo = _formCard.card_number.replace(/\s+/g
   Files: log-paygate-invoice-********-181229.txt

211. if (_formCard.email != ''
   Files: log-paygate-invoice-********-181229.txt

212. if (_formCard.exp_date != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

213. if (_formCard.name != null
   Files: log-paygate-dienmayxanh-********-181228.txt

214. if (_formCard.phone != ''
   Files: log-paygate-invoice-********-181229.txt

215. if (_idInvoice != null
   Files: log-paygate-generalv2-********-181228.txt

216. if (_length % 3 != 0) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

217. if (_modules[6][c] != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

218. if (_modules[r][6] != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

219. if (_modules[row][col] != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

220. if (_re != null
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-invoice-********-181229.txt, log-paygate-traveloka-********-181230.txt

221. if (_re.body != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-homecredit-********-181229.txt

222. if (_re.body?.state != "success") {
   Files: log-paygate-promotion-********-181230.txt

223. if (_re.body.authorization != null
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt

224. if (_re.body.links != null
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt

225. if (_re.body.return_url != null) {
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt

226. if (_re.status != '200'
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-upos-********-181230.txt

227. if (_val.value != '') {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

228. if (_val.value != null
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt

229. if (_val.value != null) {
   Files: log-paygate-dienmayxanh-********-181228.txt

230. if (!(_formCard.address != null
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt

231. if (!(_formCard.card_name != null
   Files: log-paygate-invoice-********-181229.txt

232. if (!(_formCard.card_number != null
   Files: log-paygate-dienmayxanh-********-181228.txt

233. if (!(_formCard.city != null
   Files: log-paygate-invoice-********-181229.txt

234. if (!(_formCard.country != null
   Files: log-paygate-invoice-********-181229.txt

235. if (!(_formCard.name != null
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt

236. if (!(_formCard.otp != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

237. if (!(_formCard.password != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

238. if (!(_formCard.province != null
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt

239. if (!(!isNaN(_formCard.country) != null
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt

240. if (!(formCardName != null
   Files: log-paygate-dienmayxanh-********-181228.txt

241. if (!(postalCode != null
   Files: log-paygate-invoice-********-181229.txt

242. if (!(s_paygate != null
   Files: log-paygate-promotion-********-181230.txt

243. if (!(strInstrument != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

244. if (!(this._otp != null
   Files: log-paygate-vinfast-********-181231.txt

245. if ('otp' != this._paymentService.getCurrentPage()) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

246. if (($target.prop('selectionStart') != null) && $target.prop('selectionStart') !== $target.prop('selectionEnd')) {
   Files: log-paygate-iframe-********-181229.txt

247. if (($target.prop('selectionStart') != null) && $target.prop('selectionStart') !== value.length) {
   Files: log-paygate-iframe-********-181229.txt

248. if ((cardNo != null
   Files: log-paygate-ita-********-181229.txt

249. if ((tem = ua.match(/version\/(\d+)/i)) != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-upos-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

250. if ((tem = ua.match(/version\/(\d+)/i)) != null) M.splice(1
   Files: log-paygate-ita-********-181229.txt

251. if ((this._b != 3
   Files: log-paygate-mafc-********-181229.txt

252. if ((this.bnplDetail.phoneNumber?.length != 10
   Files: log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt

253. if ((this.fundiinDetail.phoneNumber?.length != 10
   Files: log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt

254. if ((this.homecreditDetail['phoneNumber'].length != 10
   Files: log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt

255. if (['mastercard', 'visa', 'amex', 'jcb', 'cup', 'card', 'np_card', 'atm'].indexOf(id) != -1) {
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

256. if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1) {
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

257. if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(item.brand_id) != -1
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt

258. if (appcode != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt

259. if (bankid != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt

260. if (cardNo != null
   Files: log-paygate-invoice-********-181229.txt

261. if (code != this.codeSelected) {
   Files: log-paygate-generalv2-********-181228.txt

262. if (count != 1) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

263. if (count != numChars) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

264. if (currency != "VND") return false; // napas accept VND only
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-promotion-********-181230.txt, log-paygate-token-********-181230.txt

265. if (dataPassed.body != null) {
   Files: log-paygate-dienmayxanh-********-181228.txt

266. if (event.data?.state != 'success'
   Files: log-paygate-promotion-********-181230.txt

267. if (event.origin != window.origin) return; // chỉ nhận message từ OnePay
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-promotion-********-181230.txt, log-paygate-token-********-181230.txt

268. if (expdate != null
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-invoice-********-181229.txt

269. if (fee != null) {
   Files: log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-tns-********-181230.txt

270. if (idInvoice != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-upos-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

271. if (imgCard != null) {
   Files: log-paygate-dienmayxanh-********-181228.txt

272. if (imgCard != null) imgCard.src = imgCardSrc;
   Files: log-paygate-homecredit-********-181229.txt

273. if (imglogo != null) {
   Files: log-paygate-invoice-********-181229.txt

274. if (latestPayment?.instrument?.type != "vietqr") {
   Files: log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-traveloka-********-181230.txt

275. if (message != ''
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-invoice-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-token-********-181230.txt

276. if (network != 'napas'
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-promotion-********-181230.txt, log-paygate-token-********-181230.txt

277. if (network != "napas") return true;
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-promotion-********-181230.txt, log-paygate-token-********-181230.txt

278. if (params['locale'] != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

279. if (params['locale'] != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

280. if (payment.state != 'authorization_required') {
   Files: log-paygate-traveloka-********-181230.txt

281. if (paymentId != null) {
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-upos-********-181230.txt

282. if (res?.status != 200
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-upos-********-181230.txt

283. if (res.body != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-traveloka-********-181230.txt

284. if (res.body?.state != 'created') {
   Files: log-paygate-traveloka-********-181230.txt

285. if (s_paygate.substring(0, 1) != '^'
   Files: log-paygate-promotion-********-181230.txt

286. if (strInstrument.substring(0, 1) != '^'
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

287. if (tem != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-upos-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

288. if (tem != null) return tem.slice(1).join(' ').replace('OPR'
   Files: log-paygate-ita-********-181229.txt

289. if (test.length != 2
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

290. if (this._b != 18
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

291. if (this._b != 3
   Files: log-paygate-domestic-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-promotion-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

292. if (this._b != 68
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt

293. if (this._b != 9
   Files: log-paygate-apple-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt

294. if (this._i_exp_date.length != 3) {
   Files: log-paygate-dienmayxanh-********-181228.txt

295. if (this._idInvoice != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

296. if (this._idInvoice != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt

297. if (this._inCardName != null
   Files: log-paygate-traveloka-********-181230.txt

298. if (this._inExpDate.length != 3) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

299. if (this._inExpMonth.length != 3) {
   Files: log-paygate-traveloka-********-181230.txt

300. if (this._inExpYear.length != 3) {
   Files: log-paygate-traveloka-********-181230.txt

301. if (this._merchantid != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

302. if (this._paymentService.getCurrentPage() != 'otp') {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

303. if (this._paymentService.getInvoiceDetail() != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

304. if (this._res != null
   Files: log-paygate-dienmayxanh-********-181228.txt

305. if (this._res != null) {
   Files: log-paygate-vinfast-********-181231.txt

306. if (this._res_polling.payments != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

307. if (this._res_post != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

308. if (this._res_post.authorization != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

309. if (this._res_post.links != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

310. if (this._res_post.return_url != null)
   Files: log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt

311. if (this._res_post.return_url != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

312. if (this._res.authorization != null
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt

313. if (this._res.billing != null
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt

314. if (this._res.billing.address.city != null
   Files: log-paygate-invoice-********-181229.txt

315. if (this._res.billing.address.city != null) {
   Files: log-paygate-dienmayxanh-********-181228.txt

316. if (this._res.billing.address.city != null) this._i_city = this._res.billing.address.city;
   Files: log-paygate-homecredit-********-181229.txt

317. if (this._res.billing.address.country_code != null
   Files: log-paygate-invoice-********-181229.txt

318. if (this._res.billing.address.country_code != null) {
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt

319. if (this._res.billing.address.line1 != null
   Files: log-paygate-invoice-********-181229.txt

320. if (this._res.billing.address.line1 != null) {
   Files: log-paygate-dienmayxanh-********-181228.txt

321. if (this._res.billing.address.line1 != null) this._i_address = this._res.billing.address.line1;
   Files: log-paygate-homecredit-********-181229.txt

322. if (this._res.billing.address.postal_code != null
   Files: log-paygate-invoice-********-181229.txt

323. if (this._res.billing.address.postal_code != null) {
   Files: log-paygate-dienmayxanh-********-181228.txt

324. if (this._res.billing.address.postal_code != null) this._i_postal_code = this._res.billing.address.postal_code;
   Files: log-paygate-homecredit-********-181229.txt

325. if (this._res.billing.address.state != null
   Files: log-paygate-invoice-********-181229.txt

326. if (this._res.billing.address.state != null) {
   Files: log-paygate-dienmayxanh-********-181228.txt

327. if (this._res.billing.address.state != null) this._i_state = this._res.billing.address.state;
   Files: log-paygate-homecredit-********-181229.txt

328. if (this._res.links != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

329. if (this._res.merchant != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

330. if (this._res.merchant.address_details != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

331. if (this._res.payments != null
   Files: log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt

332. if (this._res.payments[0].authorization != null
   Files: log-paygate-homecredit-********-181229.txt

333. if (this._res.payments[this._res.payments.length - 1].authorization != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

334. if (this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-promotion-********-181230.txt

335. if (this._state != null
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt

336. if (this._translate.currentLang != language) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

337. if (this.cardName != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

338. if (this.current != 'card'
   Files: log-paygate-dienmayxanh-********-181228.txt

339. if (this.current != 'card') {
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt

340. if (this.current != 'csc') {
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt

341. if (this.current != 'date'
   Files: log-paygate-dienmayxanh-********-181228.txt

342. if (this.current != 'date') {
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt

343. if (this.current != 'name'
   Files: log-paygate-dienmayxanh-********-181228.txt

344. if (this.current != 'name') {
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt

345. if (this.current != 'phone'
   Files: log-paygate-dienmayxanh-********-181228.txt

346. if (this.current_f1 != 'card') {
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt

347. if (this.current_f1 != 'date') {
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt

348. if (this.current_f1 != 'name') {
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt

349. if (this.currentMethod != 9) {
   Files: log-paygate-enetviet-********-181228.txt

350. if (this.htmlDesc != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

351. if (this.oldBankId != 0) {
   Files: log-paygate-generalv2-********-181228.txt

352. if (this.oldBankId != this._b) this.previewCardService.setResetData(true)
   Files: log-paygate-generalv2-********-181228.txt

353. if (this.paymentType != PaymentType.ApplePay) return false;
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-promotion-********-181230.txt, log-paygate-token-********-181230.txt

354. if (this.paymentType != PaymentType.ApplePay) return;
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-promotion-********-181230.txt, log-paygate-token-********-181230.txt

355. if (this.res != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-mafc-********-181229.txt, log-paygate-tns-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

356. if (this.translate.currentLang != language) {
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt

357. if (this.type != 7) {
   Files: log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt

358. if (this.type != 9) {
   Files: log-paygate-enetviet-********-181228.txt

359. if (this.url_new_invoice != null
   Files: log-paygate-apple-********-181227.txt

360. if (this.url_new_invoice != null) {
   Files: log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt

361. if (this.valueDate.length != 3) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

362. if (typeof ret != 'undefined') return ret;
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

363. if (ua.indexOf('safari') != -1
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

364. if (ua.indexOf("safari") != -1
   Files: log-paygate-vinfast-********-181231.txt

365. if (userLang != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

366. if (v.length != 3) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt

367. if (value != null) {
   Files: log-paygate-dienmayxanh-********-181228.txt

368. if( direction != 'vertical' ) {
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

369. if((tem= ua.match(/version\/(\d+)/i)) != null) M.splice(1
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt

370. if(params['locale'] != null
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt

371. if(tem != null) return tem.slice(1).join(' ').replace('OPR'
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt

372. if(this._res_polling.links != null
   Files: log-paygate-homecredit-********-181229.txt, log-paygate-ita-********-181229.txt

373. if(userLang != null
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt

374. if(value != null){
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

375. item.bnpl != 1)"
   Files: log-paygate-promotion-********-181230.txt

376. item.domescard != 1
   Files: log-paygate-promotion-********-181230.txt

377. item.international != 1
   Files: log-paygate-promotion-********-181230.txt

378. item.qr != 1
   Files: log-paygate-promotion-********-181230.txt

379. item.qr != 1)"
   Files: log-paygate-promotion-********-181230.txt

380. item['feeService']['fee'] != 0
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt

381. item['feeService']['fee'] != 0"
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt

382. k.head != h[1]
   Files: log-paygate-generalv2-********-181228.txt

383. k.next != k.head
   Files: log-paygate-generalv2-********-181228.txt

384. l.set({x:4},"t") != l
   Files: log-paygate-generalv2-********-181228.txt

385. latestPayment?.state != "authorization_required") {
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-upos-********-181230.txt

386. let urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
   Files: log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt

387. let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
   Files: log-paygate-apple-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

388. let userName = _formCard.name != null ? _formCard.name : ''
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

389. m.isReadyToPay=function(a){var b=Kd(a);return new r.Promise(function(c){(void 0 != b.hasEnrolledInstrument?b.hasEnrolledInstrument():b.canMakePayment()).then(function(d){window.sessionStorage.setItem("google.payments.api.storage.isreadytopay.result",d.toString());var e={result:d};2<=a.apiVersion&&a.existingPaymentMethodRequired&&(e.paymentMethodPresent=d);c(e)
   Files: log-paygate-generalv2-********-181228.txt

390. merchantId != 'TESTENETVIET'"
   Files: log-paygate-general-fee-inside-********-181228.txt

391. message != null
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-invoice-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-token-********-181230.txt

392. message != undefined) {
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-invoice-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-token-********-181230.txt

393. N.prototype.resolve=function(a){var b=this.clone(),c=!!a.X;c?Sc(b,a.X):c=!!a.ha;if(c){var d=a.ha;O(b);b.ha=d}else c=!!a.O;c?(d=a.O,O(b),b.O=d):c=null != a.j;d=a.M;if(c)Tc(b,a.j);else if(c=!!a.M){if("/"!=d.charAt(0))if(this.O
   Files: log-paygate-generalv2-********-181228.txt

394. notify != ''"
   Files: log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt

395. null != a
   Files: log-paygate-generalv2-********-181228.txt

396. null != a.buttonRadius
   Files: log-paygate-generalv2-********-181228.txt

397. null != a){c=ea[b];if(null==c)return a[b];c=a[c];return void 0!==c?c:a[b]}},u=function(a,b,c){if(b)a:{var d=a.split(".");a=1===d.length;var e=d[0];e=!a&&e in r?r:p;for(var g=0;g<d.length-1;g++){var f=d[g];if(!(f in e))break a;e=e[f]}d=d[d.length-1];c=da&&"es6"===c?e[d]:null;b=b(c);null!=b&&(a?ba(r,d,{configurable:!0,writable:!0,value:b}):b!==c&&(void 0===ea[d]&&(a=1E9*Math.random()>>>0,ea[d]=
   Files: log-paygate-generalv2-********-181228.txt

398. null != b
   Files: log-paygate-generalv2-********-181228.txt

399. null != b.buttonRadius
   Files: log-paygate-generalv2-********-181228.txt

400. null != c
   Files: log-paygate-generalv2-********-181228.txt

401. null != f
   Files: log-paygate-generalv2-********-181228.txt

402. null != window.navigator.userAgent.match(/Android|iPhone|iPad|iPod|BlackBerry|IEMobile/i)?(this.o.push(37),a=!1):(a=window.navigator.userAgent.match(/Chrome\/([0-9]+)\./i),"PaymentRequest"in window&&null!=a&&70<=Number(a[1])&&"Google Inc."==window.navigator.vendor?(this.o.push(98),this.Za.paymentDataCallbacks&&this.o.push(97),a=!0):(this.o.push(36),a=!1))
   Files: log-paygate-generalv2-********-181228.txt

403. P.prototype.pb=function(a){a&&!this.F&&(Q(this),this.D=null,this.h.forEach(function(b,c){var d=c.toLowerCase();if(c != d
   Files: log-paygate-generalv2-********-181228.txt

404. params['b'] != ''
   Files: log-paygate-generalv2-********-181228.txt

405. params['b'] != undefined) {
   Files: log-paygate-generalv2-********-181228.txt

406. params['url_redirect'] != '' ? params['url_redirect'] : ''
   Files: log-paygate-promotion-********-181230.txt

407. plan.applicable != 'premium_users'"
   Files: log-paygate-apple-********-181227.txt

408. plan.interest_rate != '0%'? ('/' && ('month' | translate)) : ''}}
   Files: log-paygate-apple-********-181227.txt

409. plan.processing_fee_rate != '0%'? ('/' && ('month' | translate)) : ''}}
   Files: log-paygate-apple-********-181227.txt

410. plan.type != '1'"
   Files: log-paygate-apple-********-181227.txt

411. q.value[0] != h
   Files: log-paygate-generalv2-********-181228.txt

412. qr_error != null"
   Files: log-paygate-token-********-181230.txt

413. qr_version2 != 'None'
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt

414. qr_version2 != 'None'"
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt

415. qr_version2 != 'None')">
   Files: log-paygate-general-********-181228.txt

416. qr_version2 != 'qrV1'
   Files: log-paygate-general-********-181228.txt

417. res?.status != 201) return;
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-upos-********-181230.txt

418. res.body.links != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-traveloka-********-181230.txt

419. res.body.links.merchant_return != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-traveloka-********-181230.txt

420. res.body.links.merchant_return.href != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-traveloka-********-181230.txt

421. return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

422. return csc != null
   Files: log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt

423. return this._i_name != ''
   Files: log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt

424. return this._i_token_otp != null
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt

425. return typeof _map[key] != 'undefined'
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

426. return"object" != b?b:a?Array.isArray(a)?"array":b:"null"},ta=function(a){var b=sa(a)
   Files: log-paygate-generalv2-********-181228.txt

427. s_paygate.substr(s_paygate.length - 1) != '$') {
   Files: log-paygate-promotion-********-181230.txt

428. selectedBnpl.status != 'disabled'"
   Files: log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-promotion-********-181230.txt

429. strInstrument.substr(strInstrument.length - 1) != '$') {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

430. t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

431. Tb=null != Yb?Yb:!1
   Files: log-paygate-generalv2-********-181228.txt

432. techcombank != 2) || (bankId == 2
   Files: log-paygate-dienmayxanh-********-181228.txt

433. themeConfig.enable_payment_method != 'false'
   Files: log-paygate-promotion-********-181230.txt

434. this._b != 11
   Files: log-paygate-apple-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

435. this._b != 12
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

436. this._b != 16
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

437. this._b != 17
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

438. this._b != 18
   Files: log-paygate-domestic-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

439. this._b != 18)
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

440. this._b != 18) this.previewCardService.setDisplayCard(true)
   Files: log-paygate-generalv2-********-181228.txt

441. this._b != 18) this.previewCardService.setDisplayCard(true) // tab 0 , thẻ atm thì hiện
   Files: log-paygate-generalv2-********-181228.txt

442. this._b != 19
   Files: log-paygate-domestic-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

443. this._b != 19) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

444. this._b != 2
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt

445. this._b != 2) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
   Files: log-paygate-vinfast-web-********-181231.txt

446. this._b != 20
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

447. this._b != 20) || ((dCardExp.getTime() - dCurrent.getTime()) < 0
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt

448. this._b != 25
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

449. this._b != 25)
   Files: log-paygate-homecredit-********-181229.txt

450. this._b != 25) {
   Files: log-paygate-dienmayxanh-********-181228.txt

451. this._b != 25) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt

452. this._b != 25) {//9vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt

453. this._b != 27
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

454. this._b != 3))
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt

455. this._b != 3)) && this._util.checkMod10(cardNo) && this.checkBin(cardNo)
   Files: log-paygate-traveloka-********-181230.txt

456. this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

457. this._b != 33
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

458. this._b != 36
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

459. this._b != 36)
   Files: log-paygate-apple-********-181227.txt, log-paygate-traveloka-********-181230.txt

460. this._b != 39
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

461. this._b != 43
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

462. this._b != 44
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

463. this._b != 44) {
   Files: log-paygate-dienmayxanh-********-181228.txt

464. this._b != 44) {//9vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
   Files: log-paygate-dienmayxanh-********-181228.txt

465. this._b != 45
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

466. this._b != 48
   Files: log-paygate-promotion-********-181230.txt

467. this._b != 5
   Files: log-paygate-promotion-********-181230.txt

468. this._b != 5) || (this._b==5
   Files: log-paygate-mafc-********-181229.txt

469. this._b != 54
   Files: log-paygate-apple-********-181227.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-traveloka-********-181230.txt

470. this._b != 57
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

471. this._b != 57) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
   Files: log-paygate-domestic-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-promotion-********-181230.txt

472. this._b != 59
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

473. this._b != 6
   Files: log-paygate-domestic-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

474. this._b != 61
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

475. this._b != 63
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

476. this._b != 64
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

477. this._b != 67
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

478. this._b != 67)
   Files: log-paygate-general-fee-outside-********-181228.txt

479. this._b != 68
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

480. this._b != 69
   Files: log-paygate-domestic-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

481. this._b != 69) {
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-traveloka-********-181230.txt

482. this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-vinfast-********-181231.txt

483. this._b != 69) {//9vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
   Files: log-paygate-dienmayxanh-********-181228.txt

484. this._b != 72
   Files: log-paygate-apple-********-181227.txt, log-paygate-enetviet-********-181228.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

485. this._b != 72 )
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt

486. this._b != 72)
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt

487. this._b != 72) || ((dCardExp.getTime() - dCurrent.getTime()) < 0
   Files: log-paygate-dienmayxanh-********-181228.txt

488. this._b != 73
   Files: log-paygate-apple-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

489. this._b != 74
   Files: log-paygate-enetviet-********-181228.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

490. this._b != 75)
   Files: log-paygate-enetviet-********-181228.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

491. this._b != 9
   Files: log-paygate-domestic-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

492. this._i_csc != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-ita-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

493. this._i_name != ''
   Files: log-paygate-promotion-********-181230.txt

494. this._idInvoice != 0) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

495. this._merchantid != null
   Files: log-paygate-upos-********-181230.txt

496. this._paymentService.getCurrentPage() != 'error') {
   Files: log-paygate-invoice-********-181229.txt

497. this._paymentService.getState() != 'error') {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

498. this._res_polling.links != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

499. this._res_polling.links.merchant_return != null //
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

500. this._res_polling.links.merchant_return != null){
   Files: log-paygate-homecredit-********-181229.txt, log-paygate-ita-********-181229.txt

501. this._res_polling.links.merchant_return != null//
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

502. this._res_polling.merchant_invoice_reference != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

503. this._res_polling.payments != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

504. this._res_polling.payments[this._res_polling.payments.length - 1].instrument.brand != 'amigo') {
   Files: log-paygate-apple-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

505. this._res_polling.payments[this._res_polling.payments.length - 1].instrument.issuer.brand.id != 'amigo') {
   Files: log-paygate-promotion-********-181230.txt

506. this._res_post != null
   Files: log-paygate-vinfast-********-181231.txt

507. this._res_post.authorization.links != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

508. this._res_post.authorization.links.approval != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

509. this._res_post.authorization.links.approval != null)
   Files: log-paygate-homecredit-********-181229.txt

510. this._res_post.authorization.links.approval != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

511. this._res_post.authorization.links.approval.href != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

512. this._res_post.links != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

513. this._res_post.links.cancel != null)
   Files: log-paygate-homecredit-********-181229.txt

514. this._res_post.links.cancel != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

515. this._res_post.links.merchant_return != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

516. this._res_post.links.merchant_return.href != null
   Files: log-paygate-vinfast-********-181231.txt

517. this._res_post.links.merchant_return.href != null)
   Files: log-paygate-invoice-********-181229.txt

518. this._res_post.links.merchant_return.href != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

519. this._res_post.links.merchant_return.href != null){
   Files: log-paygate-homecredit-********-181229.txt

520. this._res.authorization.links != null
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt

521. this._res.authorization.links.approval != null) {
   Files: log-paygate-dienmayxanh-********-181228.txt

522. this._res.billing.address != null) {
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt

523. this._res.billing.address.city != 'null') this._i_city = this._res.billing.address.city;
   Files: log-paygate-invoice-********-181229.txt

524. this._res.billing.address.country_code != 'null') {
   Files: log-paygate-invoice-********-181229.txt

525. this._res.billing.address.line1 != 'null') this._i_address = this._res.billing.address.line1;
   Files: log-paygate-invoice-********-181229.txt

526. this._res.billing.address.postal_code != 'null') this._i_postal_code = this._res.billing.address.postal_code;
   Files: log-paygate-invoice-********-181229.txt

527. this._res.billing.address.state != 'null') this._i_state = this._res.billing.address.state;
   Files: log-paygate-invoice-********-181229.txt

528. this._res.links != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

529. this._res.links != null//
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

530. this._res.links.cancel != null) {
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt

531. this._res.links.merchant_return != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

532. this._res.links.merchant_return != null //
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

533. this._res.links.merchant_return != null//
   Files: log-paygate-homecredit-********-181229.txt

534. this._res.links.merchant_return.href != null
   Files: log-paygate-vinfast-********-181231.txt

535. this._res.links.merchant_return.href != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

536. this._res.merchant_invoice_reference != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

537. this._res.merchant_invoice_reference != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

538. this._res.merchant.avs != null
   Files: log-paygate-invoice-********-181229.txt

539. this._res.merchant.id != 'OPTEST' ? this._res.on_off_bank : ''
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt

540. this._res.payments != null
   Files: log-paygate-vinfast-********-181231.txt

541. this._res.payments[0].authorization != null
   Files: log-paygate-homecredit-********-181229.txt

542. this._res.payments[0].authorization.links != null
   Files: log-paygate-homecredit-********-181229.txt

543. this._res.payments[0].authorization.links != null) {
   Files: log-paygate-homecredit-********-181229.txt

544. this._res.payments[0].authorization.links.approval != null
   Files: log-paygate-homecredit-********-181229.txt

545. this._res.payments[0].authorization.links.approval.href != null) {
   Files: log-paygate-homecredit-********-181229.txt

546. this._res.payments[0].instrument != null
   Files: log-paygate-homecredit-********-181229.txt

547. this._res.payments[0].instrument.issuer != null
   Files: log-paygate-homecredit-********-181229.txt

548. this._res.payments[0].instrument.issuer.brand != null
   Files: log-paygate-homecredit-********-181229.txt

549. this._res.payments[0].instrument.issuer.brand.id != null
   Files: log-paygate-homecredit-********-181229.txt

550. this._res.payments[0].instrument.issuer.swift_code != null
   Files: log-paygate-homecredit-********-181229.txt

551. this._res.payments[0].links != null
   Files: log-paygate-homecredit-********-181229.txt

552. this._res.payments[0].links.cancel != null
   Files: log-paygate-homecredit-********-181229.txt

553. this._res.payments[0].links.cancel.href != null
   Files: log-paygate-homecredit-********-181229.txt

554. this._res.payments[this._res.payments.length - 1].authorization != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt

555. this._res.payments[this._res.payments.length - 1].authorization != null ) {
   Files: log-paygate-vinfast-web-********-181231.txt

556. this._res.payments[this._res.payments.length - 1].authorization.links != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

557. this._res.payments[this._res.payments.length - 1].authorization.links != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt

558. this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

559. this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

560. this._res.payments[this._res.payments.length - 1].instrument != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

561. this._res.payments[this._res.payments.length - 1].instrument.brand != 'amigo') {
   Files: log-paygate-apple-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

562. this._res.payments[this._res.payments.length - 1].instrument.issuer != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

563. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

564. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != 'amigo') {
   Files: log-paygate-promotion-********-181230.txt

565. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

566. this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

567. this._res.payments[this._res.payments.length - 1].links != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

568. this._res.payments[this._res.payments.length - 1].links.cancel != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

569. this._res.payments[this._res.payments.length - 1].links.cancel.href != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

570. this._res.payments[this._res.payments.length - 1].links.update != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

571. this._res.payments[this._res.payments.length - 1].links.update.href != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

572. this._state != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-upos-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

573. this._tranref != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-upos-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

574. this._url_cancel_webtest + (idInvoice != null ? idInvoice : "");
   Files: log-paygate-upos-********-181230.txt

575. this._util.getElementParams(url_redirect, 'vpc_TxnResponseCode') != '99') {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

576. this.amount_promotion = promotionPassData.data.discount_amount != 0 ? (this._util.formatCurrency(promotionPassData.data.discount_amount).toString() + ' ' + currency) : ''
   Files: log-paygate-promotion-********-181230.txt

577. this.amount_promotion = promotionPassData.data.discount_amount != 0 ? (this._util.formatCurrency(promotionPassData.data.discount_amount).toString() + ' ' + this.currency) : ''
   Files: log-paygate-promotion-********-181230.txt

578. this.auth_method != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

579. this.bnplDetail.phoneNumber?.length != 10 ?
   Files: log-paygate-apple-********-181227.txt, log-paygate-promotion-********-181230.txt

580. this.bnplDetail.phoneNumber?.length != 11) ?
   Files: log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt

581. this.bnplDetail.phoneNumber?.length != 11) || !this.bnplDetail.phoneNumber.match(/^[*]{8,9
   Files: log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt

582. this.c_csc = (!(_val.value != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

583. this.c_token_otp_csc = !(_val.value != null
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt

584. this.challengeCode != '') {
   Files: log-paygate-general-fee-inside-********-181228.txt, log-paygate-mafc-********-181229.txt

585. this.currentMethod != selected) {
   Files: log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt

586. this.d_vrbank = (this._res.qr_data != null
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt

587. this.data?.merchant.id != 'OPTEST' ? this.data?.on_off_bank : ''
   Files: log-paygate-generalv2-********-181228.txt

588. this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

589. this.filteredDataOtherWithoutZalo = this.filteredDataOther.filter(item => item.code != 'zalopay');
   Files: log-paygate-traveloka-********-181230.txt

590. this.fundiinDetail.phoneNumber?.length != 11) ?
   Files: log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt

591. this.fundiinDetail.phoneNumber?.length != 11) || !this.fundiinDetail.phoneNumber.match(/^[*]{8,9
   Files: log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt

592. this.homecreditDetail['phoneNumber'].length != 11)
   Files: log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt

593. this.listTokenBnpl = this.listTokenBnpl.filter(item => item.id != result.id);
   Files: log-paygate-apple-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-promotion-********-181230.txt

594. this.qr_error != null"
   Files: log-paygate-token-********-181230.txt

595. this.qr_version2 != 'None') {
   Files: log-paygate-general-********-181228.txt

596. this.requireAvs = this.isAvsCountry = country != undefined
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-invoice-********-181229.txt, log-paygate-promotion-********-181230.txt, log-paygate-token-********-181230.txt

597. this.res.links != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-mafc-********-181229.txt, log-paygate-tns-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

598. this.res.links.merchant_return != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-mafc-********-181229.txt, log-paygate-tns-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

599. this.res.links.merchant_return.href != null) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-mafc-********-181229.txt, log-paygate-tns-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

600. this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-promotion-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

601. this.themeConfig.merchant.id != 'OPTEST' ? this.themeConfig.on_off_bank : ''
   Files: log-paygate-token-********-181230.txt

602. this.type.toString().length != 0) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

603. this.value = (this.listTokenBnpl.length != 0) ? this.listTokenBnpl[0]['id'] : "add";
   Files: log-paygate-apple-********-181227.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-promotion-********-181230.txt

604. throw count + ' != ' + numChars
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

605. timeLeft != 'Infinity'"
   Files: log-paygate-homecredit-********-181229.txt, log-paygate-ita-********-181229.txt

606. type != 2 && ((onePaymentMethod && !token ) || token) && d_vietqr !== 1
   Files: log-paygate-traveloka-********-181230.txt

607. typeof cacheData != "object") {
   Files: log-paygate-upos-********-181230.txt

608. U.prototype.rc=function(a){var b=this;Id(this.Fb);this.ba(a.Ga().then(function(c){if("TIN" != b.s
   Files: log-paygate-generalv2-********-181228.txt

609. u("Array.from",function(a){return a?a:function(b,c,d){c=null != c?c:function(h){return h
   Files: log-paygate-generalv2-********-181228.txt

610. undefined" != typeof r.Symbol
   Files: log-paygate-generalv2-********-181228.txt

611. urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
   Files: log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-vinfast-********-181231.txt

612. urlCalcel = this.getUrlCancel() + (idInvoice != null ? idInvoice : '');
   Files: log-paygate-homecredit-********-181229.txt

613. urlCancel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-web-********-181231.txt

614. userName = myParam != null ? myParam : ''
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt

615. userName = paramUserName != null ? paramUserName : ''
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

616. v != null
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

617. var source = arguments[i] != null ? arguments[i] : {
   Files: log-paygate-bachhoaxanh-********-181227.txt, log-paygate-domestic-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-iframe-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

618. var userName = _formCard.name != null ? _formCard.name : ''
   Files: log-paygate-dienmayxanh-********-181228.txt, log-paygate-homecredit-********-181229.txt

619. void 0 != a.buttonRadius
   Files: log-paygate-generalv2-********-181228.txt

620. void 0 != a.buttonVariant
   Files: log-paygate-generalv2-********-181228.txt

621. void 0 != b.buttonRadius
   Files: log-paygate-generalv2-********-181228.txt

622. while (buffer.getLengthInBits() % 8 != 0) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

623. while (data != 0) {
   Files: log-paygate-apple-********-181227.txt, log-paygate-bachhoaxanh-********-181227.txt, log-paygate-dienmayxanh-********-181228.txt, log-paygate-domestic-********-181228.txt, log-paygate-enetviet-********-181228.txt, log-paygate-general-********-181228.txt, log-paygate-general-fee-inside-********-181228.txt, log-paygate-general-fee-outside-********-181228.txt, log-paygate-generalv2-********-181228.txt, log-paygate-homecredit-********-181229.txt, log-paygate-invoice-********-181229.txt, log-paygate-ita-********-181229.txt, log-paygate-mafc-********-181229.txt, log-paygate-megags-********-181230.txt, log-paygate-mpayvn-********-181230.txt, log-paygate-promotion-********-181230.txt, log-paygate-tns-********-181230.txt, log-paygate-token-********-181230.txt, log-paygate-traveloka-********-181230.txt, log-paygate-vinfast-********-181231.txt, log-paygate-vinfast-web-********-181231.txt

624. window.addEventListener("message",function(e){-1 != sf.indexOf(window.location.hostname)
   Files: log-paygate-generalv2-********-181228.txt

625. x:4}),l=new a(v([[h,"s"]]));if("s" != l.get(h)
   Files: log-paygate-generalv2-********-181228.txt

