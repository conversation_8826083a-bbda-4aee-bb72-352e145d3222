const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

class CaseSensitiveUpdater {
    constructor() {
        this.excelFile = 'Theme - Config.xlsx';
        this.logFile = this.findSimpleConditionsFile();
        this.sheetName = 'Case sensitive';
        
        // Mapping từ tên project trong log sang tên cột trong Excel
        this.projectMapping = {
            'iframe': 'paygate-iframe',
            'apple': 'paygate-apple',
            'bachhoaxanh': 'paygate-bachhoaxanh',
            'dienmayxanh': 'paygate-dienmayxanh',
            'domestic': 'paygate-domestic',
            'generalv2': 'paygate-generalv2',
            'general': 'paygate-general',
            'paygate-general-fee-inside': 'paygate-general-fee-inside',
            'paygate-general-fee-outside': 'paygate-general-fee-outside',
            'homecredit': 'paygate-homecredit',
            'invoice': 'paygate-invoice',
            'ita': 'paygate-ita',
            'mafc': 'paygate-mafc',
            'megags': 'paygate-megags',
            'mpayvn': 'paygate-mpayvn',
            'promotion': 'paygate-promotion',
            'tns': 'paygate-tns',
            'token': 'paygate-token',
            'traveloka': 'paygate-traveloka',
            'upos': 'paygate-upos',
            'vinfast': 'paygate-vinfast',
            'paygate-vinfast-web': 'paygate-vinfast-web',
            'enetviet': 'paygate-enetviet'
        };
    }

    /**
     * Tự động tìm file simple-conditions-* trong thư mục analyze-log
     */
    findSimpleConditionsFile() {
        try {
            // Tìm trong thư mục hiện tại trước
            const currentDir = __dirname;
            let searchDirs = [currentDir];

            // Thêm thư mục analyze-log nếu tồn tại
            const analyzeLogDir = path.join(currentDir, '..', 'analyze-log');
            if (fs.existsSync(analyzeLogDir)) {
                searchDirs.push(analyzeLogDir);
            }

            let allFiles = [];

            // Tìm trong tất cả các thư mục
            for (const dir of searchDirs) {
                try {
                    const files = fs.readdirSync(dir);
                    const simpleConditionsFiles = files
                        .filter(file => file.startsWith('simple-conditions-') && file.endsWith('.txt'))
                        .map(file => ({
                            name: file,
                            fullPath: path.join(dir, file),
                            dir: dir
                        }));

                    allFiles.push(...simpleConditionsFiles);
                } catch (err) {
                    // Bỏ qua lỗi đọc thư mục
                }
            }

            if (allFiles.length === 0) {
                throw new Error('Không tìm thấy file simple-conditions-*.txt trong thư mục hiện tại hoặc analyze-log');
            }

            // Sắp xếp theo tên file (mới nhất cuối cùng)
            allFiles.sort((a, b) => a.name.localeCompare(b.name));

            if (allFiles.length > 1) {
                console.log(`⚠️  Tìm thấy ${allFiles.length} file simple-conditions-*.txt:`);
                allFiles.forEach(file => console.log(`   - ${file.name} (${file.dir})`));
                console.log(`📄 Sử dụng file mới nhất: ${allFiles[allFiles.length - 1].name}`);
            }

            // Sử dụng file cuối cùng (mới nhất theo tên)
            const selectedFile = allFiles[allFiles.length - 1];
            console.log(`📖 Sẽ đọc file: ${selectedFile.fullPath}`);

            return selectedFile.fullPath;
        } catch (error) {
            console.error('❌ Lỗi khi tìm file simple-conditions:', error.message);
            throw error;
        }
    }

    /**
     * Tạo timestamp cho tên file
     */
    generateTimestamp() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');

        return `${year}${month}${day}-${hours}${minutes}${seconds}`;
    }

    /**
     * Đọc và parse file log để lấy danh sách điều kiện
     */
    parseLogFile() {
        console.log('📖 Reading log file...');
        const content = fs.readFileSync(this.logFile, 'utf8');
        const lines = content.split('\n');
        
        const conditions = [];
        let startParsing = false;
        
        for (const line of lines) {
            // Bắt đầu parse từ dòng có pattern "1. condition [projects]"
            if (line.match(/^\d+\.\s+.+\s+\[.+\]$/)) {
                startParsing = true;
                
                // Parse dòng: "123. condition === value [project1, project2, ...]"
                const match = line.match(/^(\d+)\.\s+(.+?)\s+\[([^\]]+)\]$/);
                if (match) {
                    const [, number, condition, projectsStr] = match;
                    const projects = projectsStr.split(',').map(p => p.trim());
                    
                    conditions.push({
                        number: parseInt(number),
                        condition: condition.trim(),
                        projects: projects
                    });
                }
            }
        }
        
        console.log(`✅ Parsed ${conditions.length} conditions from log file`);
        return conditions;
    }

    /**
     * Đọc Excel file và lấy cấu trúc sheet
     */
    readExcelSheet() {
        console.log('📊 Reading Excel sheet...');
        const workbook = XLSX.readFile(this.excelFile);
        const worksheet = workbook.Sheets[this.sheetName];
        
        if (!worksheet) {
            throw new Error(`Sheet "${this.sheetName}" not found!`);
        }
        
        // Chuyển đổi thành array 2D
        const data = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: '' });
        
        // Tìm header row (row 4 - index 3)
        const headerRow = data[3] || [];
        
        // Tạo mapping từ tên cột sang index
        const columnMapping = {};
        headerRow.forEach((header, index) => {
            if (header && typeof header === 'string') {
                columnMapping[header] = index;
            }
        });
        
        console.log(`✅ Found ${Object.keys(columnMapping).length} columns in Excel sheet`);
        
        return {
            workbook,
            worksheet,
            data,
            headerRow,
            columnMapping
        };
    }

    /**
     * Kiểm tra xem điều kiện từ log có khớp với điều kiện trong Excel không
     */
    isConditionMatch(logCondition, excelCondition) {
        if (!excelCondition || typeof excelCondition !== 'string') {
            return false;
        }
        
        // Exact match
        if (logCondition === excelCondition) {
            return true;
        }
        
        // Kiểm tra xem logCondition có nằm trong excelCondition không
        // Ví dụ: "instrument.issuer.brand.id == 'amigo'" nằm trong 
        // "this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id == 'amigo'"
        if (excelCondition.includes(logCondition)) {
            return true;
        }
        
        return false;
    }

    /**
     * Tìm dòng trống đầu tiên (cột B không có điều kiện)
     */
    findFirstEmptyRow(data) {
        for (let rowIndex = 6; rowIndex < data.length; rowIndex++) { // Bắt đầu từ row 7 (index 6)
            const row = data[rowIndex];
            const condition = row[1]; // Cột B

            // Nếu cột B trống hoặc không có điều kiện
            if (!condition || condition === '' || condition === null || condition === undefined) {
                return rowIndex;
            }
        }
        return -1; // Không tìm thấy dòng trống
    }

    /**
     * Cập nhật sheet với dữ liệu từ log
     */
    updateSheet() {
        const logConditions = this.parseLogFile();
        const { workbook, worksheet, data, headerRow, columnMapping } = this.readExcelSheet();

        console.log('🔄 Updating sheet...');

        let updatedRows = 0;
        let newRows = 0;
        let filledEmptyRows = 0;
        const processedConditions = new Set();

        // Duyệt qua từng điều kiện trong log
        for (const logCond of logConditions) {
            let foundMatch = false;

            // Tìm xem có điều kiện nào trong Excel khớp không
            for (let rowIndex = 6; rowIndex < data.length; rowIndex++) { // Bắt đầu từ row 7 (index 6)
                const row = data[rowIndex];
                const excelCondition = row[1]; // Cột B (index 1) chứa điều kiện

                if (this.isConditionMatch(logCond.condition, excelCondition)) {
                    foundMatch = true;

                    // Cập nhật các cột project
                    for (const project of logCond.projects) {
                        const excelColumnName = this.projectMapping[project];
                        if (excelColumnName && columnMapping[excelColumnName] !== undefined) {
                            const colIndex = columnMapping[excelColumnName];
                            row[colIndex] = true;
                        }
                    }

                    updatedRows++;
                    processedConditions.add(logCond.condition);
                    break;
                }
            }

            // Nếu không tìm thấy match và chưa xử lý điều kiện này
            if (!foundMatch && !processedConditions.has(logCond.condition)) {
                // Tìm dòng trống đầu tiên
                const emptyRowIndex = this.findFirstEmptyRow(data);

                if (emptyRowIndex !== -1) {
                    // Sử dụng dòng trống có sẵn
                    const row = data[emptyRowIndex];

                    // Cột A để trống (không set gì cả)
                    row[1] = logCond.condition; // Cột B - điều kiện
                    // Cột C (index 2) để trống cho Note

                    // Reset tất cả cột project về false trước
                    for (let i = 3; i < row.length; i++) {
                        row[i] = false;
                    }

                    // Đánh dấu TRUE cho các project có trong log
                    for (const project of logCond.projects) {
                        const excelColumnName = this.projectMapping[project];
                        if (excelColumnName && columnMapping[excelColumnName] !== undefined) {
                            const colIndex = columnMapping[excelColumnName];
                            row[colIndex] = true;
                        }
                    }

                    filledEmptyRows++;
                } else {
                    // Không có dòng trống, tạo dòng mới
                    const newRow = new Array(headerRow.length).fill('');

                    // Cột A để trống
                    newRow[1] = logCond.condition; // Cột B - điều kiện
                    // Cột C để trống cho Note

                    // Khởi tạo tất cả cột project là false
                    for (let i = 3; i < newRow.length; i++) {
                        newRow[i] = false;
                    }

                    // Đánh dấu TRUE cho các project có trong log
                    for (const project of logCond.projects) {
                        const excelColumnName = this.projectMapping[project];
                        if (excelColumnName && columnMapping[excelColumnName] !== undefined) {
                            const colIndex = columnMapping[excelColumnName];
                            newRow[colIndex] = true;
                        }
                    }

                    data.push(newRow);
                    newRows++;
                }

                processedConditions.add(logCond.condition);
            }
        }

        // Cập nhật worksheet với dữ liệu mới
        const newWorksheet = XLSX.utils.aoa_to_sheet(data);
        workbook.Sheets[this.sheetName] = newWorksheet;

        // Lưu file với timestamp
        const timestamp = this.generateTimestamp();
        const outputFile = this.excelFile.replace('.xlsx', `_updated_${timestamp}.xlsx`);
        XLSX.writeFile(workbook, outputFile);

        console.log(`✅ Update completed!`);
        console.log(`   📝 Updated existing rows: ${updatedRows}`);
        console.log(`   📋 Filled empty rows: ${filledEmptyRows}`);
        console.log(`   ➕ New rows added: ${newRows}`);
        console.log(`   💾 Saved to: ${outputFile}`);

        return {
            updatedRows,
            filledEmptyRows,
            newRows,
            outputFile
        };
    }
}

// Chạy script
try {
    const updater = new CaseSensitiveUpdater();
    updater.updateSheet();
} catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
}
