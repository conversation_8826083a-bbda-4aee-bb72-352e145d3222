const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Script để trích xuất tất cả các điều kiện so sánh từ NHIỀU repo paygate-* cùng lúc
 * Phiên bản batch processing - tự động tìm và xử lý tất cả repo có prefix "paygate-*"
 *
 * Cách hoạt động: Tìm tất cả repo paygate-*, sau đó gọi extract-comparisons.js cho từng repo
 */

/**
 * Tìm tất cả các thư mục có prefix "paygate-*" trong thư mục cha
 */
function findPaygateRepos(parentPath) {
    const repos = [];
    
    try {
        const items = fs.readdirSync(parentPath);
        
        for (const item of items) {
            const itemPath = path.join(parentPath, item);
            const stat = fs.statSync(itemPath);
            
            // Kiểm tra nếu là thư mục và có prefix "paygate-"
            if (stat.isDirectory() && item.startsWith('paygate-')) {
                // Kiểm tra xem có thư mục src không
                const srcPath = path.join(itemPath, 'src');
                if (fs.existsSync(srcPath)) {
                    repos.push({
                        name: item,
                        path: itemPath,
                        srcPath: srcPath
                    });
                }
            }
        }
    } catch (error) {
        console.error(`❌ Lỗi khi đọc thư mục ${parentPath}:`, error.message);
    }
    
    return repos;
}

/**
 * Xử lý tất cả các repo paygate
 */
function processAllPaygateRepos(parentPath) {
    console.log(`🔍 Tìm kiếm các repo paygate-* trong: ${parentPath}`);
    
    const repos = findPaygateRepos(parentPath);
    
    if (repos.length === 0) {
        console.log('❌ Không tìm thấy repo nào có prefix "paygate-" với thư mục src');
        return;
    }
    
    console.log(`✅ Tìm thấy ${repos.length} repo paygate:`);
    repos.forEach((repo, index) => {
        console.log(`   ${index + 1}. ${repo.name} (${repo.srcPath})`);
    });
    
    console.log('\n🚀 Bắt đầu xử lý từng repo...\n');
    
    // Xử lý từng repo
    for (let i = 0; i < repos.length; i++) {
        const repo = repos[i];
        console.log(`\n${'='.repeat(80)}`);
        console.log(`📦 Đang xử lý repo ${i + 1}/${repos.length}: ${repo.name}`);
        console.log(`${'='.repeat(80)}`);
        
        console.log(`🎯 Đường dẫn: ${repo.srcPath}`);
        
        // Gọi script gốc cho repo này
        try {
            const command = `node extract-comparisons.js "${repo.srcPath}"`;
            console.log(`🔧 Chạy lệnh: ${command}`);

            const output = execSync(command, {
                encoding: 'utf8',
                stdio: 'pipe'
            });

            console.log(output);
            console.log(`✅ Hoàn thành xử lý ${repo.name}`);
        } catch (error) {
            console.log(`❌ Lỗi khi xử lý ${repo.name}:`);
            console.log(error.stdout || error.message);
        }
    }
    
    console.log(`\n${'='.repeat(80)}`);
    console.log(`🎉 HOÀN THÀNH! Đã xử lý ${repos.length} repo paygate`);
    console.log(`${'='.repeat(80)}`);
}



// Sử dụng script
const inputPath = process.argv[2] || './'; // Thư mục cha chứa các repo paygate-*

console.log(`🎯 Thư mục cha: ${inputPath}`);

// Kiểm tra xem đường dẫn có tồn tại không
if (!fs.existsSync(inputPath)) {
    console.error(`❌ Lỗi: Đường dẫn "${inputPath}" không tồn tại!`);
    console.log('\n📝 Ví dụ:');
    console.log('  - Xử lý tất cả repo paygate: node extract-comparisons-batch.js /path/to/parent/folder/');
    console.log('  - Sử dụng thư mục hiện tại: node extract-comparisons-batch.js ./');
    console.log('  - Sử dụng default: node extract-comparisons-batch.js');
    console.log(`    (sẽ tìm repo paygate-* trong: ${process.argv[2] || './'})`);
    process.exit(1);
}

// Chạy script cho tất cả repo paygate
processAllPaygateRepos(inputPath);
