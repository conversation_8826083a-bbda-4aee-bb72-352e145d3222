const fs = require('fs');
const path = require('path');

/**
 * Script để trích xuất tất cả các điều kiện so sánh (==, ===, !=, !==) từ các file TypeScript, JavaScript và HTML
 * và ghi kết quả vào file log.txt
 */

class ComparisonExtractor {
    constructor(inputPath, outputFile, ignoreFiles = []) {
        this.inputPath = inputPath;
        this.outputFile = outputFile;
        this.fileResults = []; // Kết quả từng file
        this.ignoredFiles = []; // Danh sách file bị bỏ qua
        this.globalStats = {
            '==': 0,
            '===': 0,
            '!=': 0,
            '!==': 0,
            total: 0,
            totalFiles: 0,
            processedFiles: [],
            ignoredCount: 0
        };
        this.supportedExtensions = ['.ts', '.js', '.html'];
        this.ignoreFiles = ignoreFiles; // Danh sách file cần ignore
    }

    /**
     * <PERSON><PERSON><PERSON> tra xem file có nên bị ignore không
     */
    shouldIgnoreFile(filePath) {
        const fileName = path.basename(filePath);

        // Kiểm tra exact match
        if (this.ignoreFiles.includes(fileName)) {
            return true;
        }

        // Kiểm tra pattern match (wildcard)
        for (const ignorePattern of this.ignoreFiles) {
            if (ignorePattern.includes('*')) {
                // Chuyển wildcard pattern thành regex
                const regexPattern = ignorePattern
                    .replace(/\./g, '\\.')  // Escape dấu chấm
                    .replace(/\*/g, '.*');  // Thay * bằng .*
                const regex = new RegExp(`^${regexPattern}$`);

                if (regex.test(fileName)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Tìm tất cả file có extension được hỗ trợ trong thư mục
     */
    findSupportedFiles(dirPath) {
        const files = [];

        const scanDirectory = (currentPath) => {
            try {
                const items = fs.readdirSync(currentPath);

                items.forEach(item => {
                    const fullPath = path.join(currentPath, item);
                    const stat = fs.statSync(fullPath);

                    if (stat.isDirectory()) {
                        // Đệ quy vào thư mục con
                        scanDirectory(fullPath);
                    } else if (stat.isFile()) {
                        const ext = path.extname(item).toLowerCase();
                        if (this.supportedExtensions.includes(ext)) {
                            // Kiểm tra xem file có bị ignore không
                            if (this.shouldIgnoreFile(fullPath)) {
                                this.ignoredFiles.push(fullPath);
                                this.globalStats.ignoredCount++;
                            } else {
                                files.push(fullPath);
                            }
                        }
                    }
                });
            } catch (error) {
                console.warn(`Không thể đọc thư mục ${currentPath}: ${error.message}`);
            }
        };

        // Kiểm tra input path là file hay thư mục
        try {
            const stat = fs.statSync(dirPath);
            if (stat.isFile()) {
                const ext = path.extname(dirPath).toLowerCase();
                if (this.supportedExtensions.includes(ext)) {
                    // Kiểm tra xem file có bị ignore không
                    if (this.shouldIgnoreFile(dirPath)) {
                        this.ignoredFiles.push(dirPath);
                        this.globalStats.ignoredCount++;
                    } else {
                        files.push(dirPath);
                    }
                }
            } else if (stat.isDirectory()) {
                scanDirectory(dirPath);
            }
        } catch (error) {
            console.error(`Không thể truy cập ${dirPath}: ${error.message}`);
        }

        return files;
    }

    /**
     * Đọc một file và trích xuất các điều kiện so sánh
     */
    extractComparisonsFromFile(filePath) {
        const fileResult = {
            filePath: filePath,
            fileName: path.basename(filePath),
            comparisons: [],
            uniqueComparisons: new Set(),
            stats: {
                '==': 0,
                '===': 0,
                '!=': 0,
                '!==': 0,
                total: 0
            }
        };

        try {
            console.log(`Đang xử lý file: ${filePath}`);
            const content = fs.readFileSync(filePath, 'utf8');
            const lines = content.split('\n');

            lines.forEach((line, index) => {
                const lineNumber = index + 1;
                const trimmedLine = line.trim();

                // Bỏ qua các dòng comment cho JS/TS
                if (filePath.endsWith('.js') || filePath.endsWith('.ts')) {
                    if (trimmedLine.startsWith('//') || trimmedLine.startsWith('*') || trimmedLine.startsWith('/*')) {
                        return;
                    }
                }

                // Bỏ qua các dòng comment HTML
                if (filePath.endsWith('.html')) {
                    if (trimmedLine.startsWith('<!--') || trimmedLine.includes('<!--')) {
                        return;
                    }
                }

                let match;
                const regex = /(===|!==|==|!=)/g;

                while ((match = regex.exec(line)) !== null) {
                    const operator = match[1];

                    // Trích xuất điều kiện so sánh thuần túy
                    let pureComparison = this.extractPureComparison(line, match.index, operator);

                    // Nếu đây là file HTML và điều kiện bị cắt ngắn, thử tìm toàn bộ attribute
                    if (fileResult.fileName.endsWith('.html') && (pureComparison.length < 10 || pureComparison.includes('&'))) {
                        const htmlComparison = this.extractHtmlAttributeComparison(line, match.index, operator);
                        if (htmlComparison && htmlComparison.length > pureComparison.length) {
                            pureComparison = htmlComparison;
                        }
                    }

                    // Kiểm tra duplicate trong file này
                    if (!fileResult.uniqueComparisons.has(pureComparison)) {
                        fileResult.uniqueComparisons.add(pureComparison);

                        fileResult.comparisons.push({
                            lineNumber: lineNumber,
                            operator: operator,
                            pureComparison: pureComparison,
                            fullLine: line.trim()
                        });

                        fileResult.stats[operator]++;
                        fileResult.stats.total++;

                        // Cập nhật thống kê global
                        this.globalStats[operator]++;
                        this.globalStats.total++;
                    }
                }
            });

            console.log(`  → Tìm thấy ${fileResult.stats.total} điều kiện so sánh duy nhất`);
            return fileResult;

        } catch (error) {
            console.error(`Lỗi khi đọc file ${filePath}: ${error.message}`);
            return null;
        }
    }

    /**
     * Xử lý tất cả các file
     */
    extractComparisons() {
        console.log(`Đang quét các file trong: ${this.inputPath}`);
        const files = this.findSupportedFiles(this.inputPath);

        if (files.length === 0) {
            console.log('Không tìm thấy file nào có extension được hỗ trợ (.ts, .js, .html)');
            return false;
        }

        console.log(`Tìm thấy ${files.length} file(s) để xử lý:`);
        files.forEach(file => console.log(`  - ${file}`));

        if (this.ignoredFiles.length > 0) {
            console.log(`\n🚫 Đã bỏ qua ${this.ignoredFiles.length} file(s):`);
            this.ignoredFiles.forEach(file => console.log(`  - ${file}`));
        }
        console.log('');

        this.globalStats.totalFiles = files.length;
        this.globalStats.processedFiles = files;

        files.forEach(filePath => {
            const result = this.extractComparisonsFromFile(filePath);
            if (result) {
                this.fileResults.push(result);
            }
        });

        console.log(`\nHoàn thành xử lý ${this.fileResults.length}/${files.length} file(s)`);
        console.log(`Tổng cộng tìm thấy ${this.globalStats.total} điều kiện so sánh duy nhất`);

        return this.fileResults.length > 0;
    }

    /**
     * Trích xuất điều kiện so sánh thuần túy (chỉ phần so sánh)
     */
    extractPureComparison(line, startIndex, operator) {
        // Cách tiếp cận đơn giản hơn: tìm từ vị trí operator ra hai bên
        // cho đến khi gặp các ký tự phân cách chính

        let beforeStart = startIndex - 1;
        let afterEnd = startIndex + operator.length;

        // Tìm điểm bắt đầu (đi ngược) - chỉ dừng lại ở các ký tự thực sự phân cách expressions
        let parenCount = 0;
        let bracketCount = 0;
        let braceCount = 0;

        while (beforeStart >= 0) {
            const char = line[beforeStart];

            // Đếm các dấu ngoặc để đảm bảo cân bằng
            if (char === ')') parenCount++;
            else if (char === '(') parenCount--;
            else if (char === ']') bracketCount++;
            else if (char === '[') bracketCount--;
            else if (char === '}') braceCount++;
            else if (char === '{') braceCount--;

            // Chỉ dừng lại khi không trong ngoặc và gặp ký tự phân cách thực sự
            if (parenCount === 0 && bracketCount === 0 && braceCount === 0) {
                // Dừng lại ở logical operators
                if (char === '&' && beforeStart > 0 && line[beforeStart - 1] === '&') {
                    beforeStart++;
                    break;
                }
                if (char === '|' && beforeStart > 0 && line[beforeStart - 1] === '|') {
                    beforeStart++;
                    break;
                }

                // Dừng lại ở các ký tự phân cách statement
                if (char === ',' || char === ';' || char === '\t' || char === '{') {
                    beforeStart++;
                    break;
                }

                // Trong HTML, dừng lại ở attribute boundary
                if (char === '"' && beforeStart > 0 && line[beforeStart - 1] === '=') {
                    beforeStart++;
                    break;
                }

                // Dừng lại ở đầu dòng hoặc sau khoảng trắng nhiều
                if (char === ' ' && beforeStart > 0) {
                    const prevChar = line[beforeStart - 1];
                    if (prevChar === ' ' || prevChar === '\t') {
                        beforeStart++;
                        break;
                    }
                }
            }

            beforeStart--;
        }
        beforeStart = Math.max(0, beforeStart);

        // Tìm điểm kết thúc (đi tiến) - tương tự như beforeStart
        parenCount = 0;
        bracketCount = 0;
        braceCount = 0;

        while (afterEnd < line.length) {
            const char = line[afterEnd];

            // Đếm các dấu ngoặc để đảm bảo cân bằng
            if (char === '(') parenCount++;
            else if (char === ')') parenCount--;
            else if (char === '[') bracketCount++;
            else if (char === ']') bracketCount--;
            else if (char === '{') braceCount++;
            else if (char === '}') braceCount--;

            // Chỉ dừng lại khi không trong ngoặc và gặp ký tự phân cách thực sự
            if (parenCount === 0 && bracketCount === 0 && braceCount === 0) {
                // Dừng lại ở logical operators
                if (char === '&' && afterEnd < line.length - 1 && line[afterEnd + 1] === '&') {
                    break;
                }
                if (char === '|' && afterEnd < line.length - 1 && line[afterEnd + 1] === '|') {
                    break;
                }

                // Dừng lại ở các ký tự phân cách statement
                if (char === ',' || char === ';' || char === '\t' || char === '}') {
                    break;
                }

                // Trong HTML, dừng lại ở kết thúc attribute
                if (char === '"' && afterEnd < line.length - 1 &&
                    (line[afterEnd + 1] === '>' || line[afterEnd + 1] === ' ')) {
                    afterEnd++;
                    break;
                }

                // Dừng lại ở khoảng trắng nhiều
                if (char === ' ' && afterEnd < line.length - 1) {
                    const nextChar = line[afterEnd + 1];
                    if (nextChar === ' ' || nextChar === '\t') {
                        break;
                    }
                }
            }

            afterEnd++;
        }

        // Lấy phần gốc từ line để giữ nguyên format và spacing
        const originalComparison = line.substring(beforeStart, afterEnd).trim();

        // Decode HTML entities để giữ nguyên ký tự gốc
        const decodedComparison = this.decodeHtmlEntities(originalComparison);

        return decodedComparison;
    }

    /**
     * Trích xuất điều kiện so sánh từ HTML attribute (ví dụ: *ngIf="condition")
     */
    extractHtmlAttributeComparison(line, operatorIndex, operator) {
        // Tìm attribute chứa điều kiện này
        // Tìm dấu " trước operator
        let attrStart = operatorIndex;
        while (attrStart > 0 && line[attrStart] !== '"') {
            attrStart--;
        }

        // Tìm dấu " sau operator
        let attrEnd = operatorIndex + operator.length;
        while (attrEnd < line.length && line[attrEnd] !== '"') {
            attrEnd++;
        }

        if (attrStart > 0 && attrEnd < line.length) {
            // Lấy nội dung trong attribute (bỏ dấu ")
            const attributeContent = line.substring(attrStart + 1, attrEnd);

            // Decode HTML entities
            const decodedContent = this.decodeHtmlEntities(attributeContent);

            return decodedContent;
        }

        return null;
    }

    /**
     * Decode HTML entities để giữ nguyên ký tự gốc
     */
    decodeHtmlEntities(text) {
        const htmlEntities = {
            '&apos;': "'",
            '&quot;': '"',
            '&amp;': '&',
            '&lt;': '<',
            '&gt;': '>',
            '&#39;': "'",
            '&#34;': '"',
            '&#38;': '&',
            '&#60;': '<',
            '&#62;': '>'
        };

        return text.replace(/&[a-zA-Z0-9#]+;/g, (entity) => {
            return htmlEntities[entity] || entity;
        });
    }

    /**
     * Ghi kết quả vào file log
     */
    writeToLog() {
        try {
            console.log(`Đang ghi kết quả vào file: ${this.outputFile}`);

            let logContent = '';

            // Header
            logContent += '='.repeat(100) + '\n';
            logContent += 'TRÍCH XUẤT ĐIỀU KIỆN SO SÁNH TỪ CÁC FILE TYPESCRIPT/JAVASCRIPT/HTML\n';
            logContent += '='.repeat(100) + '\n';
            logContent += `Thư mục/File nguồn: ${this.inputPath}\n`;
            logContent += `Thời gian: ${new Date().toLocaleString('vi-VN')}\n`;
            logContent += `Tổng số file xử lý: ${this.globalStats.totalFiles}\n`;
            logContent += `Tổng số file bị bỏ qua: ${this.globalStats.ignoredCount}\n`;
            logContent += `Tổng số điều kiện tìm thấy: ${this.globalStats.total}\n\n`;

            // Thống kê tổng quan
            logContent += 'THỐNG KÊ TỔNG QUAN THEO LOẠI TOÁN TỬ:\n';
            logContent += '-'.repeat(50) + '\n';
            logContent += `Strict equality (===): ${this.globalStats['===']} lần\n`;
            logContent += `Loose equality (==): ${this.globalStats['==']} lần\n`;
            logContent += `Strict inequality (!==): ${this.globalStats['!==']} lần\n`;
            logContent += `Loose inequality (!=): ${this.globalStats['!=']} lần\n`;
            logContent += '-'.repeat(50) + '\n\n';

            // Danh sách file đã xử lý
            logContent += 'DANH SÁCH FILE ĐÃ XỬ LÝ:\n';
            logContent += '-'.repeat(50) + '\n';
            this.fileResults.forEach((fileResult, index) => {
                logContent += `${index + 1}. ${fileResult.fileName} (${fileResult.stats.total} điều kiện)\n`;
                logContent += `   Đường dẫn: ${fileResult.filePath}\n`;
            });

            // Danh sách file bị bỏ qua
            if (this.ignoredFiles.length > 0) {
                logContent += '\nDANH SÁCH FILE BỊ BỎ QUA:\n';
                logContent += '-'.repeat(50) + '\n';
                this.ignoredFiles.forEach((ignoredFile, index) => {
                    logContent += `${index + 1}. ${path.basename(ignoredFile)}\n`;
                    logContent += `   Đường dẫn: ${ignoredFile}\n`;
                });
            }
            logContent += '\n';

            // Chi tiết từng file
            logContent += '='.repeat(100) + '\n';
            logContent += 'CHI TIẾT TỪNG FILE:\n';
            logContent += '='.repeat(100) + '\n\n';

            this.fileResults.forEach((fileResult, fileIndex) => {
                logContent += `📁 FILE ${fileIndex + 1}: ${fileResult.fileName}\n`;
                logContent += `📍 Đường dẫn: ${fileResult.filePath}\n`;
                logContent += `📊 Thống kê: ${fileResult.stats.total} điều kiện duy nhất\n`;
                logContent += `   - === : ${fileResult.stats['===']} lần\n`;
                logContent += `   - == : ${fileResult.stats['==']} lần\n`;
                logContent += `   - !== : ${fileResult.stats['!==']} lần\n`;
                logContent += `   - != : ${fileResult.stats['!=']} lần\n`;
                logContent += '-'.repeat(80) + '\n';

                if (fileResult.comparisons.length > 0) {
                    // Nhóm theo toán tử cho file này
                    const groupedByOperator = this.groupByOperatorForFile(fileResult);

                    Object.keys(groupedByOperator).forEach(operator => {
                        if (groupedByOperator[operator].length > 0) {
                            logContent += `\n${operator.toUpperCase()} (${groupedByOperator[operator].length} điều kiện):\n`;

                            groupedByOperator[operator].forEach((comp, index) => {
                                logContent += `  ${index + 1}. [Dòng ${comp.lineNumber}] ${comp.pureComparison}\n`;
                            });
                        }
                    });
                } else {
                    logContent += '\n❌ Không tìm thấy điều kiện so sánh nào trong file này.\n';
                }

                logContent += '\n' + '='.repeat(80) + '\n\n';
            });

            // Tóm tắt tất cả điều kiện duy nhất
            logContent += '📋 TÓM TẮT TẤT CẢ ĐIỀU KIỆN DUY NHẤT:\n';
            logContent += '='.repeat(100) + '\n\n';

            // Tập hợp tất cả điều kiện duy nhất từ tất cả file
            const allUniqueComparisons = new Map(); // operator -> Set of comparisons

            this.fileResults.forEach(fileResult => {
                fileResult.comparisons.forEach(comp => {
                    if (!allUniqueComparisons.has(comp.operator)) {
                        allUniqueComparisons.set(comp.operator, new Set());
                    }
                    allUniqueComparisons.get(comp.operator).add(comp.pureComparison);
                });
            });

            // Hiển thị tóm tắt
            ['===', '==', '!==', '!='].forEach(operator => {
                if (allUniqueComparisons.has(operator) && allUniqueComparisons.get(operator).size > 0) {
                    logContent += `${operator.toUpperCase()} (${allUniqueComparisons.get(operator).size} điều kiện duy nhất):\n`;
                    logContent += '-'.repeat(60) + '\n';

                    Array.from(allUniqueComparisons.get(operator)).forEach((comparison, index) => {
                        logContent += `${index + 1}. ${comparison}\n`;
                    });
                    logContent += '\n';
                }
            });

            // Ghi file
            fs.writeFileSync(this.outputFile, logContent, 'utf8');
            console.log(`Đã ghi thành công vào file: ${this.outputFile}`);

            return true;

        } catch (error) {
            console.error(`Lỗi khi ghi file: ${error.message}`);
            return false;
        }
    }

    /**
     * Nhóm các điều kiện theo loại toán tử cho một file cụ thể
     */
    groupByOperatorForFile(fileResult) {
        const grouped = {
            '===': [],
            '==': [],
            '!==': [],
            '!=': []
        };

        fileResult.comparisons.forEach(comp => {
            grouped[comp.operator].push(comp);
        });

        return grouped;
    }

    /**
     * Nhóm các điều kiện theo loại toán tử (legacy method - không còn sử dụng)
     */
    groupByOperator() {
        const grouped = {
            '===': [],
            '==': [],
            '!==': [],
            '!=': []
        };

        // Method này không còn được sử dụng trong version mới
        return grouped;
    }

    /**
     * Chạy quá trình trích xuất
     */
    run() {
        console.log('Bắt đầu trích xuất điều kiện so sánh...\n');

        if (this.extractComparisons()) {
            if (this.writeToLog()) {
                console.log('\n✅ Hoàn thành thành công!');
                console.log(`📊 Thống kê tổng quan:`);
                console.log(`   - Tổng số file xử lý: ${this.globalStats.totalFiles}`);
                console.log(`   - Tổng cộng: ${this.globalStats.total} điều kiện duy nhất`);
                console.log(`   - Strict equality (===): ${this.globalStats['===']} lần`);
                console.log(`   - Loose equality (==): ${this.globalStats['==']} lần`);
                console.log(`   - Strict inequality (!==): ${this.globalStats['!==']} lần`);
                console.log(`   - Loose inequality (!=): ${this.globalStats['!=']} lần`);
                console.log(`📄 Kết quả đã được lưu vào: ${this.outputFile}`);
            } else {
                console.log('❌ Lỗi khi ghi file log');
            }
        } else {
            console.log('❌ Lỗi khi xử lý file/thư mục nguồn');
        }
    }
}

/**
 * Trích xuất tên project từ đường dẫn
 * Ví dụ: "/root/projects/onepay/paygate-promotion/src/" → "paygate-promotion"
 */
function extractProjectName(inputPath) {
    try {
        // Normalize path và loại bỏ trailing slash
        const normalizedPath = path.resolve(inputPath).replace(/\/$/, '');

        // Tách thành các phần
        const pathParts = normalizedPath.split(path.sep);

        // Tìm index của "src"
        const srcIndex = pathParts.findIndex(part => part === 'src');

        if (srcIndex > 0) {
            // Lấy phần trước "src"
            return pathParts[srcIndex - 1];
        }

        // Nếu không tìm thấy "src", lấy phần cuối của path
        return pathParts[pathParts.length - 1] || 'unknown';

    } catch (error) {
        console.warn(`Không thể trích xuất tên project từ: ${inputPath}`);
        return 'unknown';
    }
}

/**
 * Tạo tên file log với timestamp và tên project trong thư mục analyze-log/all-logs
 */
function generateLogFileName(inputPath) {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

    const projectName = extractProjectName(inputPath);
    const fileName = `log-${projectName}-${year}${month}${day}-${hours}${minutes}${seconds}.txt`;

    // Tạo đường dẫn đến thư mục analyze-log/all-logs
    const analyzeLogDir = '/root/projects/onepay/tool-project/analyze-log/all-logs';

    // Kiểm tra xem thư mục có tồn tại không, nếu không thì tạo
    if (!fs.existsSync(analyzeLogDir)) {
        console.log(`⚠️  Thư mục ${analyzeLogDir} không tồn tại`);
        try {
            fs.mkdirSync(analyzeLogDir, { recursive: true });
            console.log(`📁 Đã tạo thư mục: ${analyzeLogDir}`);
        } catch (error) {
            console.log(`❌ Không thể tạo thư mục ${analyzeLogDir}: ${error.message}`);
            console.log(`📁 Tạo file trong thư mục hiện tại thay thế`);
            return fileName;
        }
    }

    return path.join(analyzeLogDir, fileName);
}

// ==================== CẤU HÌNH IGNORE FILES ====================
// Danh sách các file cần bỏ qua (có thể sử dụng wildcard *)
const IGNORE_FILES = [
    'apple-pay-sdk.js',
    'google-pay-sdk.*.js',  // Sử dụng wildcard để match google-pay-sdk.1726636373844.js
    'samsung-pay-sdk.js',
    'bootstrap.*.js',
    '*.min.js',
    // Thêm các file khác cần ignore ở đây
    // 'jquery.min.js',
    // 'bootstrap.*.js',
    // '*.min.js'  // Ignore tất cả file .min.js
];

// Sử dụng script
// Lấy đường dẫn từ command line argument hoặc sử dụng default
const inputPath = process.argv[2] || './src/'; // Thay đổi default thành ./src/
const outputFile = generateLogFileName(inputPath); // Tự động tạo tên file với timestamp và project name

console.log(`🎯 Đường dẫn cần xử lý: ${inputPath}`);
console.log(`📄 File log sẽ được tạo: ${outputFile}`);
if (IGNORE_FILES.length > 0) {
    console.log(`🚫 File patterns sẽ bị bỏ qua: ${IGNORE_FILES.join(', ')}`);
}

// Kiểm tra đường dẫn nguồn có tồn tại không
if (!fs.existsSync(inputPath)) {
    console.error(`❌ Đường dẫn nguồn không tồn tại: ${inputPath}`);
    console.log('\n💡 Cách sử dụng:');
    console.log('  node extract-comparisons.js [đường_dẫn]');
    console.log('\n📝 Ví dụ:');
    console.log('  - Xử lý thư mục: node extract-comparisons.js ./src/');
    console.log('  - Xử lý file đơn: node extract-comparisons.js ./menu.ts');
    console.log('  - Sử dụng default: node extract-comparisons.js');
    console.log(`    (sẽ xử lý: ${process.argv[2] || './src/'})`);
    console.log('\n💡 Để xử lý nhiều repo paygate-* cùng lúc:');
    console.log('  node extract-comparisons-batch.js /path/to/parent/folder/');
    process.exit(1);
}

// Chạy script
const extractor = new ComparisonExtractor(inputPath, outputFile, IGNORE_FILES);
extractor.run();
``