// Test script để debug điều kiện cụ thể
const fs = require('fs');
const path = require('path');

// Copy class từ analyze-log-conditions.js
class ConditionAnalyzer {
    cleanOperand(operand) {
        if (!operand) return operand;
        
        // Loại bỏ các ký tự không cần thiết ở đầu và cuối
        return operand
            .replace(/^[^\w'"$._-]+/, '')  // Loại bỏ ký tự đặc biệt ở đầu
            .replace(/[^\w'"$._-]+$/, '')  // Loại bỏ ký tự đặc biệt ở cuối
            .trim();
    }

    isValidOperand(operand) {
        if (!operand || operand.length === 0) return false;

        // Loại bỏ các operand rõ ràng không hợp lệ
        const invalidPatterns = [
            /^[{}();,\s]*$/,           // Chỉ chứa ký tự đặc biệt
            /^[&|]{2,}$/,              // Chỉ chứa && hoặc ||
            /^\s*$$/,                  // Chỉ chứa khoảng trắng
            /^[=!<>]+$/,               // Chỉ chứa operators
            /^[\[\]]+$/                // Chỉ chứa dấu ngoặc vuông
        ];

        // Nếu match với invalid patterns thì reject
        if (invalidPatterns.some(pattern => pattern.test(operand))) {
            return false;
        }

        // Chấp nhận hầu hết các expressions khác
        // Chỉ cần có ít nhất một ký tự alphanumeric hoặc dấu ngoặc string
        return /[a-zA-Z0-9_$'"]/.test(operand);
    }

    extractConditions(text) {
        const conditions = [];

        // Test với fallback regex
        const fallbackRegex = /(\S+)\s*(===|!==|==|!=)\s*(\S+)/g;
        let fallbackMatch;
        while ((fallbackMatch = fallbackRegex.exec(text)) !== null) {
            const leftOperand = fallbackMatch[1];
            const operator = fallbackMatch[2];
            const rightOperand = fallbackMatch[3];

            console.log(`\nFound match:`);
            console.log(`  Left: "${leftOperand}"`);
            console.log(`  Operator: "${operator}"`);
            console.log(`  Right: "${rightOperand}"`);

            const cleanLeft = this.cleanOperand(leftOperand);
            const cleanRight = this.cleanOperand(rightOperand);

            console.log(`  Clean Left: "${cleanLeft}"`);
            console.log(`  Clean Right: "${cleanRight}"`);
            console.log(`  Left Valid: ${this.isValidOperand(cleanLeft)}`);
            console.log(`  Right Valid: ${this.isValidOperand(cleanRight)}`);

            if (this.isValidOperand(cleanLeft) && this.isValidOperand(cleanRight)) {
                const condition = `${cleanLeft} ${operator} ${cleanRight}`;
                conditions.push(condition);
                console.log(`  ✅ Added: "${condition}"`);
            } else {
                console.log(`  ❌ Rejected`);
            }
        }

        return conditions;
    }
}

const analyzer = new ConditionAnalyzer();

// Test với điều kiện cụ thể
const testText = `  23. [Dòng 712] this.flag.charAt(this.flag.length - 1) === '/') {`;

console.log('Testing specific condition:');
console.log('='.repeat(60));
console.log(`Input: ${testText}`);

const conditions = analyzer.extractConditions(testText);

console.log('\nFinal results:');
conditions.forEach((condition, index) => {
    console.log(`${index + 1}. ${condition}`);
});

if (conditions.length === 0) {
    console.log('❌ No conditions extracted!');
}
