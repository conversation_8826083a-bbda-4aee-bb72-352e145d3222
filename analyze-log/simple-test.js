const fs = require('fs');

// Test với file log thực
const logFile = 'all-logs/log-paygate-token-20250818-181230.txt';

if (fs.existsSync(logFile)) {
    console.log('Reading log file...');
    const content = fs.readFileSync(logFile, 'utf8');
    
    // Tìm tất cả dòng có countryCode
    const allLines = content.split('\n');
    const countryCodeLines = allLines.filter((line, index) => {
        if (line.includes('countryCode') && line.includes('US')) {
            return true;
        }
        return false;
    });
    
    console.log(`Found ${countryCodeLines.length} lines with countryCode:`);
    countryCodeLines.forEach((line, index) => {
        console.log(`${index + 1}. "${line.trim()}"`);
    });
    
    // Tìm phần tổng hợp
    const summaryMarkers = ['=== (', '== (', '!== (', '!= ('];
    let summaryStart = -1;
    
    for (const marker of summaryMarkers) {
        const index = content.lastIndexOf(marker);
        if (index > summaryStart) {
            summaryStart = index;
        }
    }
    
    console.log(`\nSummary start index: ${summaryStart}`);
    
    if (summaryStart > 0) {
        const summaryContent = content.substring(summaryStart);
        console.log(`Summary content length: ${summaryContent.length}`);
        
        // Tìm countryCode trong phần tổng hợp
        const summaryLines = summaryContent.split('\n');
        const summaryCountryLines = summaryLines.filter(line => 
            line.includes('countryCode') && line.includes('US')
        );
        
        console.log(`\nFound ${summaryCountryLines.length} countryCode lines in summary:`);
        summaryCountryLines.forEach((line, index) => {
            console.log(`${index + 1}. "${line.trim()}"`);
        });
        
        // Test regex trên dòng cụ thể
        if (summaryCountryLines.length > 0) {
            const testLine = summaryCountryLines[0];
            console.log(`\nTesting regex on: "${testLine}"`);
            
            const conditionMatch = testLine.match(/^\s*\d+\.\s*(.+)$/);
            if (conditionMatch) {
                console.log(`✅ Condition match: "${conditionMatch[1]}"`);
                
                const operatorMatch = conditionMatch[1].match(/(.+?)\s*(===|!==|==|!=)\s*(.+)/);
                if (operatorMatch) {
                    console.log(`✅ Operator match:`);
                    console.log(`  Left: "${operatorMatch[1]}"`);
                    console.log(`  Op: "${operatorMatch[2]}"`);
                    console.log(`  Right: "${operatorMatch[3]}"`);
                } else {
                    console.log(`❌ No operator match`);
                }
            } else {
                console.log(`❌ No condition match`);
            }
        }
    }
} else {
    console.log(`File not found: ${logFile}`);
}
