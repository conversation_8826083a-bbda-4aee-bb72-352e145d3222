====================================================================================================
TRÍCH XUẤT ĐIỀU KIỆN SO SÁNH TỪ CÁC FILE TYPESCRIPT/JAVASCRIPT/HTML
====================================================================================================
Thư mục/File nguồn: /root/projects/onepay/paygate-token/src
Thời gian: 18:12:30 18/8/2025
Tổng số file xử lý: 151
Tổng số file bị bỏ qua: 3
Tổng số điều kiện tìm thấy: 1466

THỐNG KÊ TỔNG QUAN THEO LOẠI TOÁN TỬ:
--------------------------------------------------
Strict equality (===): 279 lần
Loose equality (==): 828 lần
Strict inequality (!==): 61 lần
Loose inequality (!=): 298 lần
--------------------------------------------------

DANH SÁCH FILE ĐÃ XỬ LÝ:
--------------------------------------------------
1. 4en.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/4en.html
2. 4vn.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/4vn.html
3. app-routing.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/app-routing.module.ts
4. app.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/app.component.html
5. app.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/app.component.spec.ts
6. app.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/app.component.ts
7. app.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/app.module.ts
8. PaymentType.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/constants/PaymentType.ts
9. counter.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/counter.directive.spec.ts
10. counter.directive.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/counter.directive.ts
11. format-carno-input.derective.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/directives/format-carno-input.derective.ts
12. uppercase-input.directive.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/directives/uppercase-input.directive.ts
13. error.component.html (13 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/error/error.component.html
14. error.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/error/error.component.spec.ts
15. error.component.ts (40 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/error/error.component.ts
16. support-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/error/support-dialog/support-dialog.html
17. support-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/error/support-dialog/support-dialog.ts
18. format-date.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/format-date.directive.spec.ts
19. format-date.directive.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/format-date.directive.ts
20. main.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/main.component.html
21. main.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/main.component.spec.ts
22. main.component.ts (10 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/main.component.ts
23. bank-amount-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
24. bank-amount-dialog.component.ts (34 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
25. cancel-dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/cancel-dialog-guide-dialog.html
26. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/confirm-dialog/dialog-guide-dialog.html
27. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/dialog-guide-dialog.html
28. bankaccount.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
29. bankaccount.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
30. bankaccount.component.ts (156 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
31. bank.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/model/bank.ts
32. onepay-napas.component.html (13 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.html
33. onepay-napas.component.ts (102 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.ts
34. otp-auth.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
35. otp-auth.component.ts (18 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
36. shb.component.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/shb/shb.component.html
37. shb.component.ts (66 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/shb/shb.component.ts
38. techcombank.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
39. techcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
40. techcombank.component.ts (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
41. vibbank.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.html
42. vibbank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.spec.ts
43. vibbank.component.ts (98 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.ts
44. vietcombank.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
45. vietcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
46. vietcombank.component.ts (95 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
47. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
48. off-bank-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
49. off-bank-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
50. policy-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/dialog/policy-dialog/policy-dialog.component.html
51. policy-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/dialog/policy-dialog/policy-dialog.component.ts
52. domescard-main.component.html (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/domescard-main.component.html
53. domescard-main.component.ts (133 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/domescard-main.component.ts
54. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/intercard-main/dialog-guide-dialog.html
55. intercard-main.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/intercard-main/intercard-main.component.html
56. intercard-main.component.ts (81 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/intercard-main/intercard-main.component.ts
57. menu.component.html (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/menu.component.html
58. menu.component.ts (161 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/menu.component.ts
59. applepay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/applepay/applepay.component.html
60. applepay.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/applepay/applepay.component.ts
61. dialog-network-not-supported.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/applepay/dialog-network-not-supported/dialog-network-not-supported.component.html
62. dialog-network-not-supported.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/applepay/dialog-network-not-supported/dialog-network-not-supported.component.ts
63. google-pay-button-op.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/googlepay/google-pay-button-op/google-pay-button-op.component.html
64. google-pay-button-op.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/googlepay/google-pay-button-op/google-pay-button-op.component.ts
65. types-google-pay.d.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/googlepay/google-pay-button-op/types-google-pay.d.ts
66. googlepay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/googlepay/googlepay.component.html
67. googlepay.component.ts (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/googlepay/googlepay.component.ts
68. mobile-wallet-main.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/mobile-wallet-main.component.html
69. mobile-wallet-main.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/mobile-wallet-main.component.ts
70. samsung-pay-button-op.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.html
71. samsung-pay-button-op.component.ts (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.ts
72. samsungpay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/samsungpay/samsungpay.component.html
73. samsungpay.component.ts (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/samsungpay/samsungpay.component.ts
74. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
75. qr-dialog.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
76. qr-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
77. qr-main.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main/qr-main.component.html
78. qr-main.component.ts (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main/qr-main.component.ts
79. safe-html.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main/safe-html.pipe.ts
80. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/dialog/dialog-guide-dialog.html
81. qr-desktop.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.html
82. qr-desktop.component.ts (19 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.ts
83. qr-dialog.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.html
84. qr-dialog.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.ts
85. qr-guide-dialog.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.html
86. qr-guide-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.ts
87. list-bank-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.html
88. list-bank-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.ts
89. qr-main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-main.component.html
90. qr-main.component.ts (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-main.component.ts
91. qr-mobile.component.html (7 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.html
92. qr-mobile.component.ts (30 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.ts
93. safe-html.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/safe-html.pipe.ts
94. queuing.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/queuing/queuing.component.html
95. queuing.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/queuing/queuing.component.ts
96. domescard-form.component.html (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.html
97. domescard-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.ts
98. intercard-form.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.html
99. intercard-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.ts
100. mobile-wallet-form.component.html (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/sorting-option/mobile-wallet-form/mobile-wallet-form.component.html
101. mobile-wallet-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/sorting-option/mobile-wallet-form/mobile-wallet-form.component.ts
102. paypal-form.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.html
103. paypal-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.ts
104. qr-form.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/sorting-option/qr-form/qr-form.component.html
105. qr-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/sorting-option/qr-form/qr-form.component.ts
106. token-expired-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/token-expired-dialog/token-expired-dialog.component.html
107. token-expired-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/token-expired-dialog/token-expired-dialog.component.ts
108. remove-token-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/token-main/remove-token-dialog/remove-token-dialog.html
109. dialog-guide-dialog.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/token-main/token-dialog/dialog-guide-dialog.html
110. token-main.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/token-main/token-main.component.html
111. token-main.component.ts (65 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/token-main/token-main.component.ts
112. overlay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/overlay/overlay.component.html
113. overlay.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/overlay/overlay.component.spec.ts
114. overlay.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/overlay/overlay.component.ts
115. bank-amount.pipe.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/pipe/bank-amount.pipe.ts
116. close-dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/services/close-dialog.service.ts
117. data.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/services/data.service.ts
118. deep_link.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/services/deep_link.service.ts
119. dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/services/dialog.service.ts
120. digital-wallet.service.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/services/digital-wallet.service.ts
121. fee.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/services/fee.service.ts
122. multiple_method.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/services/multiple_method.service.ts
123. payment.service.ts (13 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/services/payment.service.ts
124. qr.service.ts (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/services/qr.service.ts
125. time-stop.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/services/time-stop.service.ts
126. token-main.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/services/token-main.service.ts
127. index.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/translate/index.ts
128. lang-en.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/translate/lang-en.ts
129. lang-vi.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/translate/lang-vi.ts
130. translate.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/translate/translate.pipe.ts
131. translate.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/translate/translate.service.ts
132. translations.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/translate/translations.ts
133. apps-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/util/apps-info.ts
134. apps-information.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/util/apps-information.ts
135. banks-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/util/banks-info.ts
136. error-handler.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/util/error-handler.ts
137. iso-ca-states.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/util/iso-ca-states.ts
138. iso.us-states.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/util/iso.us-states.ts
139. util.ts (41 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/app/util/util.ts
140. apple.js (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/assets/script/apple.js
141. google-pay-intergrate.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/assets/script/google-pay-intergrate.js
142. qrcode.js (67 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/assets/script/qrcode.js
143. environment.development.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/environments/environment.development.ts
144. environment.mtf.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/environments/environment.mtf.ts
145. environment.prod.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/environments/environment.prod.ts
146. environment.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/environments/environment.ts
147. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/index.html
148. karma.conf.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/karma.conf.js
149. main.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/main.ts
150. polyfills.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/polyfills.ts
151. test.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-token/src/test.ts

DANH SÁCH FILE BỊ BỎ QUA:
--------------------------------------------------
1. apple-pay-sdk.js
   Đường dẫn: /root/projects/onepay/paygate-token/src/assets/script/apple-pay-sdk.js
2. google-pay-sdk.1726636373844.js
   Đường dẫn: /root/projects/onepay/paygate-token/src/assets/script/google-pay-sdk.1726636373844.js
3. samsung-pay-sdk.js
   Đường dẫn: /root/projects/onepay/paygate-token/src/assets/script/samsung-pay-sdk.js

====================================================================================================
CHI TIẾT TỪNG FILE:
====================================================================================================

📁 FILE 1: 4en.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/4en.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 2: 4vn.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/4vn.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 3: app-routing.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/app-routing.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 4: app.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/app.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 5: app.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/app.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 6: app.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/app.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 84] return lang === this._translate.currentLang

== (2 điều kiện):
  1. [Dòng 63] 'vi' == params['locale']) {
  2. [Dòng 65] 'en' == params['locale']) {

!= (3 điều kiện):
  1. [Dòng 63] if (params['locale'] != null
  2. [Dòng 65] } else if (params['locale'] != null
  3. [Dòng 72] if (userLang != null

================================================================================

📁 FILE 7: app.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/app.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 8: PaymentType.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/constants/PaymentType.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 9: counter.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/counter.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 10: counter.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/counter.directive.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 11: format-carno-input.derective.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/directives/format-carno-input.derective.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 44] this.lastValue !== $event.target.value)) {

!= (1 điều kiện):
  1. [Dòng 23] if(value!=null){

================================================================================

📁 FILE 12: uppercase-input.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/directives/uppercase-input.directive.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 0 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 23] this.lastValue !== $event.target.value)) {

================================================================================

📁 FILE 13: error.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/error/error.component.html
📊 Thống kê: 13 điều kiện duy nhất
   - === : 5 lần
   - == : 8 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 52] checkDupTran === false"
  2. [Dòng 90] isPopupSupport === 'True') || (rePayment
  3. [Dòng 91] isPopupSupport === 'True'"
  4. [Dòng 105] isPopupSupport === 'True')">
  5. [Dòng 128] checkDupTran === false

== (8 điều kiện):
  1. [Dòng 16] errorCode == '11'"
  2. [Dòng 90] *ngIf="(isSent == false
  3. [Dòng 91] isSent == false
  4. [Dòng 105] [class.select_only]="!(isSent == false
  5. [Dòng 123] <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
  6. [Dòng 123] (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
  7. [Dòng 128] *ngIf="!(errorCode == 'overtime'
  8. [Dòng 128] errorCode == '253'

================================================================================

📁 FILE 14: error.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/error/error.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 15: error.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/error/error.component.ts
📊 Thống kê: 40 điều kiện duy nhất
   - === : 9 lần
   - == : 25 lần
   - !== : 2 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (9 điều kiện):
  1. [Dòng 177] params.timeout === 'true') {
  2. [Dòng 200] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  3. [Dòng 200] _re.body.state === 'unpaid');
  4. [Dòng 288] if (this.errorCode === 'overtime'
  5. [Dòng 288] this.errorCode === '253') {
  6. [Dòng 379] params.name === 'CUSTOMER_INTIME'
  7. [Dòng 379] params.code === '09') {
  8. [Dòng 450] if (this.timeLeft === 0) {
  9. [Dòng 571] if (param === key) {

== (25 điều kiện):
  1. [Dòng 208] if (this.res.currencies[0] == 'USD') {
  2. [Dòng 234] if (this.paymentInformation.type == "applepay_napas") {
  3. [Dòng 244] if (this.paymentInformation.type == "applepay") {
  4. [Dòng 249] } else if (this.paymentInformation.type == "googlepay") {
  5. [Dòng 258] this.res.themes.theme == 'token') {
  6. [Dòng 264] params.response_code == 'overtime') {
  7. [Dòng 313] if (_re.status == '200'
  8. [Dòng 313] _re.status == '201') {
  9. [Dòng 325] if (_re2.status == '200'
  10. [Dòng 325] _re2.status == '201') {
  11. [Dòng 338] if (this.errorCode == 'overtime'
  12. [Dòng 338] this.errorCode == '253') {
  13. [Dòng 341] } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
  14. [Dòng 346] this.res.state == 'canceled') {
  15. [Dòng 348] if (this.errorCode == '24'
  16. [Dòng 348] this.errorName == 'ONECOMM_INVALID_TOKEN_INFO') {
  17. [Dòng 373] if (lastPayment?.state == 'pending') {
  18. [Dòng 448] if (this.isTimePause == false) {
  19. [Dòng 543] if(this.locale == 'vi')
  20. [Dòng 589] if (response.body.state == 'not_paid') {
  21. [Dòng 591] if (response.body.payments[response.body.payments.length - 1].state == "failed") {
  22. [Dòng 619] } else if (response.body.state == 'paid') {
  23. [Dòng 625] } else if (response.body.state == 'canceled'
  24. [Dòng 625] response.body.state == 'closed'
  25. [Dòng 625] response.body.state == 'expired') {

!== (2 điều kiện):
  1. [Dòng 566] queryString = (sourceURL.indexOf("?") !== -1) ? sourceURL.split("?")[1] : "";
  2. [Dòng 567] if (queryString !== "") {

!= (4 điều kiện):
  1. [Dòng 134] if (message != ''
  2. [Dòng 134] message != null
  3. [Dòng 134] message != undefined) {
  4. [Dòng 642] if (this.url_new_invoice != null) {

================================================================================

📁 FILE 16: support-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/error/support-dialog/support-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 17: support-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/error/support-dialog/support-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 18: format-date.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/format-date.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 19: format-date.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/format-date.directive.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0
  2. [Dòng 123] YY % 4 === 0)) {
  3. [Dòng 138] if (YYYY % 400 === 0
  4. [Dòng 138] YYYY % 4 === 0)) {

!== (2 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0 || (YY % 100 !== 0
  2. [Dòng 138] if (YYYY % 400 === 0 || (YYYY % 100 !== 0

================================================================================

📁 FILE 20: main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/main.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 1 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 27] checkDupTran === false"

================================================================================

📁 FILE 21: main.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/main.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 22: main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/main.component.ts
📊 Thống kê: 10 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 7 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 134] params['code']==='09'){

== (2 điều kiện):
  1. [Dòng 94] if ((dataPassed.status == '200'
  2. [Dòng 94] dataPassed.status == '201') && dataPassed.body != null) {

!= (7 điều kiện):
  1. [Dòng 85] if (this._idInvoice != null
  2. [Dòng 85] this._idInvoice != 0) {
  3. [Dòng 86] if (this._paymentService.getInvoiceDetail() != null) {
  4. [Dòng 94] dataPassed.body != null) {
  5. [Dòng 120] dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
  6. [Dòng 121] dataPassed.body.themes.border_color != true ? dataPassed.body.themes.border_color : ''
  7. [Dòng 181] if (this._translate.currentLang != language) {

================================================================================

📁 FILE 23: bank-amount-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 24: bank-amount-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
📊 Thống kê: 34 điều kiện duy nhất
   - === : 0 lần
   - == : 34 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (34 điều kiện):
  1. [Dòng 41] if (this.locale == 'en') {
  2. [Dòng 55] if (name == 'MAFC')
  3. [Dòng 61] if (bankId == 3
  4. [Dòng 61] bankId == 61
  5. [Dòng 62] bankId == 8
  6. [Dòng 62] bankId == 49
  7. [Dòng 63] bankId == 48
  8. [Dòng 64] bankId == 10
  9. [Dòng 64] bankId == 53
  10. [Dòng 65] bankId == 17
  11. [Dòng 65] bankId == 65
  12. [Dòng 66] bankId == 23
  13. [Dòng 66] bankId == 52
  14. [Dòng 67] bankId == 27
  15. [Dòng 67] bankId == 66
  16. [Dòng 68] bankId == 9
  17. [Dòng 68] bankId == 54
  18. [Dòng 69] bankId == 37
  19. [Dòng 70] bankId == 38
  20. [Dòng 71] bankId == 39
  21. [Dòng 72] bankId == 40
  22. [Dòng 73] bankId == 42
  23. [Dòng 74] bankId == 44
  24. [Dòng 75] bankId == 72
  25. [Dòng 76] bankId == 59
  26. [Dòng 79] bankId == 51
  27. [Dòng 80] bankId == 64
  28. [Dòng 81] bankId == 58
  29. [Dòng 82] bankId == 56
  30. [Dòng 85] bankId == 55
  31. [Dòng 86] bankId == 60
  32. [Dòng 87] bankId == 68
  33. [Dòng 88] bankId == 74
  34. [Dòng 89] bankId == 75 //KEB HANA

================================================================================

📁 FILE 25: cancel-dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/cancel-dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 26: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/confirm-dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 27: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 28: bankaccount.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 40] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 55] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 55] valueDate.trim().length === 0)"

================================================================================

📁 FILE 29: bankaccount.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 30: bankaccount.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
📊 Thống kê: 156 điều kiện duy nhất
   - === : 47 lần
   - == : 75 lần
   - !== : 13 lần
   - != : 21 lần
--------------------------------------------------------------------------------

=== (47 điều kiện):
  1. [Dòng 129] if (isIE[0] === 'MSIE'
  2. [Dòng 129] +isIE[1] === 10) {
  3. [Dòng 217] if ((_val.value.substr(-1) === ' '
  4. [Dòng 217] _val.value.length === 24) {
  5. [Dòng 227] if (this.cardTypeBank === 'bank_card_number') {
  6. [Dòng 232] } else if (this.cardTypeBank === 'bank_account_number') {
  7. [Dòng 238] } else if (this.cardTypeBank === 'bank_username') {
  8. [Dòng 242] } else if (this.cardTypeBank === 'bank_customer_code') {
  9. [Dòng 248] this.cardTypeBank === 'bank_card_number'
  10. [Dòng 262] if (this.cardTypeOcean === 'IB') {
  11. [Dòng 266] } else if (this.cardTypeOcean === 'MB') {
  12. [Dòng 267] if (_val.value.substr(0, 2) === '84') {
  13. [Dòng 274] } else if (this.cardTypeOcean === 'ATM') {
  14. [Dòng 301] if (this.cardTypeBank === 'bank_account_number') {
  15. [Dòng 320] this.cardTypeBank === 'bank_card_number') {
  16. [Dòng 342] if (this.cardTypeBank === 'bank_card_number'
  17. [Dòng 342] if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  18. [Dòng 664] if (event.keyCode === 8
  19. [Dòng 664] event.key === "Backspace"
  20. [Dòng 704] if (v.length === 2
  21. [Dòng 704] this.flag.length === 3
  22. [Dòng 704] this.flag.charAt(this.flag.length - 1) === '/') {
  23. [Dòng 708] if (v.length === 1) {
  24. [Dòng 710] } else if (v.length === 2) {
  25. [Dòng 713] v.length === 2) {
  26. [Dòng 721] if (len === 2) {
  27. [Dòng 998] if ((this.cardTypeBank === 'bank_account_number'
  28. [Dòng 998] this.cardTypeBank === 'bank_username'
  29. [Dòng 998] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  30. [Dòng 1003] this.cardTypeOcean === 'ATM')
  31. [Dòng 1004] || (this.cardTypeOcean === 'IB'
  32. [Dòng 1063] if (valIn === this._translate.instant('bank_card_number')) {
  33. [Dòng 1088] } else if (valIn === this._translate.instant('bank_account_number')) {
  34. [Dòng 1107] } else if (valIn === this._translate.instant('bank_username')) {
  35. [Dòng 1123] } else if (valIn === this._translate.instant('bank_customer_code')) {
  36. [Dòng 1211] if (_val.value === ''
  37. [Dòng 1211] _val.value === null
  38. [Dòng 1211] _val.value === undefined) {
  39. [Dòng 1222] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  40. [Dòng 1222] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  41. [Dòng 1229] this.cardTypeOcean === 'MB') {
  42. [Dòng 1237] this.cardTypeOcean === 'IB'
  43. [Dòng 1243] if ((this.cardTypeBank === 'bank_card_number'
  44. [Dòng 1277] if (this.cardName === undefined
  45. [Dòng 1277] this.cardName === '') {
  46. [Dòng 1285] if (this.valueDate === undefined
  47. [Dòng 1285] this.valueDate === '') {

== (75 điều kiện):
  1. [Dòng 120] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 146] if (this._b == 18
  3. [Dòng 146] this._b == 19) {
  4. [Dòng 149] if (this._b == 19) {//19BIDV
  5. [Dòng 157] } else if (this._b == 3
  6. [Dòng 157] this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  7. [Dòng 162] if (this._b == 27) {
  8. [Dòng 167] } else if (this._b == 12) {// 12SHB
  9. [Dòng 172] } else if (this._b == 18) { //18Oceanbank-ocb
  10. [Dòng 226] if (this._b == 19
  11. [Dòng 226] this._b == 3
  12. [Dòng 226] this._b == 27
  13. [Dòng 226] this._b == 12) {
  14. [Dòng 261] } else if (this._b == 18) {
  15. [Dòng 292] if (this.checkBin(_val.value) && (this._b == 3
  16. [Dòng 292] this._b == 27)) {
  17. [Dòng 297] if (this._b == 3) {
  18. [Dòng 309] this.cardTypeOcean == 'ATM') {
  19. [Dòng 322] if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  20. [Dòng 342] this._b == 18)) {
  21. [Dòng 418] if (this.checkBin(v) && (this._b == 3
  22. [Dòng 664] event.inputType == 'deleteContentBackward') {
  23. [Dòng 665] if (event.target.name == 'exp_date'
  24. [Dòng 673] event.inputType == 'insertCompositionText') {
  25. [Dòng 688] if (((this.valueDate.length == 4
  26. [Dòng 688] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  27. [Dòng 688] this.valueDate.length == 5)
  28. [Dòng 768] if (temp.length == 0) {
  29. [Dòng 775] return (counter % 10 == 0);
  30. [Dòng 795] } else if (this._b == 19) {
  31. [Dòng 797] } else if (this._b == 27) {
  32. [Dòng 802] if (this._b == 12) {
  33. [Dòng 804] if (this.cardTypeBank == 'bank_customer_code') {
  34. [Dòng 806] } else if (this.cardTypeBank == 'bank_account_number') {
  35. [Dòng 823] _formCard.exp_date.length == 5
  36. [Dòng 823] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
  37. [Dòng 823] this._b == 3)) {//27-pvcombank;3-TPB ;
  38. [Dòng 828] if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
  39. [Dòng 828] this._b == 19
  40. [Dòng 828] this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
  41. [Dòng 831] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  42. [Dòng 834] if (this.cardTypeOcean == 'IB') {
  43. [Dòng 836] } else if (this.cardTypeOcean == 'MB') {
  44. [Dòng 838] } else if (this.cardTypeOcean == 'ATM') {
  45. [Dòng 868] this.token_site == 'onepay'
  46. [Dòng 887] if (_re.status == '200'
  47. [Dòng 887] _re.status == '201') {
  48. [Dòng 892] if (this._res_post.state == 'approved'
  49. [Dòng 892] this._res_post.state == 'failed') {
  50. [Dòng 899] } else if (this._res_post.state == 'authorization_required') {
  51. [Dòng 917] if (this._b == 18) {
  52. [Dòng 922] if (this._b == 27
  53. [Dòng 922] this._b == 18) {
  54. [Dòng 985] if (err.status == 400
  55. [Dòng 985] err.status == 500) {
  56. [Dòng 986] if (err.error && (err.error.code == 13
  57. [Dòng 986] err.error.code == '13')) {
  58. [Dòng 1018] if ((cardNo.length == 16
  59. [Dòng 1018] if ((cardNo.length == 16 || (cardNo.length == 19
  60. [Dòng 1019] && ((this._b == 18
  61. [Dòng 1019] cardNo.length == 19) || this._b != 18)
  62. [Dòng 1032] if (this._b == +e.id) {
  63. [Dòng 1048] if (valIn == 1) {
  64. [Dòng 1050] } else if (valIn == 2) {
  65. [Dòng 1074] this._b == 3) {
  66. [Dòng 1081] if (this._b == 19) {
  67. [Dòng 1144] if (cardType == this._translate.instant('internetbanking')
  68. [Dòng 1152] } else if (cardType == this._translate.instant('mobilebanking')
  69. [Dòng 1160] } else if (cardType == this._translate.instant('atm')
  70. [Dòng 1222] this._b == 18))) {
  71. [Dòng 1229] } else if (this._b == 18
  72. [Dòng 1255] this.c_expdate = !(((this.valueDate.length == 4
  73. [Dòng 1288] this.valueDate.length == 4
  74. [Dòng 1288] this.valueDate.search('/') == -1)
  75. [Dòng 1289] this.valueDate.length == 5))

!== (13 điều kiện):
  1. [Dòng 217] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 873] key !== '3') {
  3. [Dòng 923] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 941] codeResponse.toString() !== '0') {
  5. [Dòng 998] cardNo.length !== 0) {
  6. [Dòng 1070] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 1091] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 1112] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 1132] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 1144] this.lb_card_account !== this._translate.instant('ocb_account')) {
  11. [Dòng 1152] this.lb_card_account !== this._translate.instant('ocb_phone')) {
  12. [Dòng 1160] this.lb_card_account !== this._translate.instant('ocb_card')) {
  13. [Dòng 1243] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (21 điều kiện):
  1. [Dòng 178] } else if (this._b != 18) {
  2. [Dòng 184] if (this.htmlDesc != null
  3. [Dòng 214] if (ua.indexOf('safari') != -1
  4. [Dòng 224] if (_val.value != '') {
  5. [Dòng 310] this.auth_method != null) {
  6. [Dòng 666] if (this.valueDate.length != 3) {
  7. [Dòng 823] if (_formCard.exp_date != null
  8. [Dòng 828] if (this.cardName != null
  9. [Dòng 895] if (this._res_post.links != null
  10. [Dòng 895] this._res_post.links.merchant_return != null
  11. [Dòng 895] this._res_post.links.merchant_return.href != null) {
  12. [Dòng 903] if (this._res_post.authorization != null
  13. [Dòng 903] this._res_post.authorization.links != null
  14. [Dòng 903] this._res_post.authorization.links.approval != null) {
  15. [Dòng 910] this._res_post.links.cancel != null) {
  16. [Dòng 1018] this._b != 27
  17. [Dòng 1018] this._b != 12
  18. [Dòng 1018] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  19. [Dòng 1019] this._b != 18)
  20. [Dòng 1065] if (this._b != 18
  21. [Dòng 1065] this._b != 19) {

================================================================================

📁 FILE 31: bank.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/model/bank.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 32: onepay-napas.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.html
📊 Thống kê: 13 điều kiện duy nhất
   - === : 4 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 26] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 41] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  3. [Dòng 41] _inExpDate.trim().length === 0)"
  4. [Dòng 65] ready===1"

== (6 điều kiện):
  1. [Dòng 18] (_b != 67 && _b != 2) || ((_b == 67 || _b == 2) && !isOffTechcombankNapas && checkTwoEnabled)
  2. [Dòng 19] <mat-tab [label]="'internet_banking' | translate" *ngIf="_b != 2 || (_b == 2
  3. [Dòng 23] <div [ngStyle]="{'margin-top': !(checkTwoEnabled && !isOffTechcombankNapas && !isOffTechcombank) ? '15px' : '0px' }" *ngIf="(cardTypeBank == 'bank_card_number') &&(_b == 67
  4. [Dòng 65] (cardTypeBank == 'bank_card_number') &&(_b == 67 || checkTwoEnabled)&& ready===1
  5. [Dòng 73] token_site == 'onepay'
  6. [Dòng 97] <div [ngStyle]="{'margin-top': !(checkTwoEnabled && !isOffTechcombankNapas && !isOffTechcombank) ? '15px' : '0px' }" class="nd-bank-card-two" *ngIf="(cardTypeBank == 'internet_banking')&&(_b == 2

!= (3 điều kiện):
  1. [Dòng 18] <mat-tab [label]="'bank_card_number' | translate" *ngIf="(_b != 67
  2. [Dòng 18] _b != 2) || ((_b == 67 || _b == 2)
  3. [Dòng 19] _b != 2 || (_b == 2 && !isOffTechcombank && checkTwoEnabled)

================================================================================

📁 FILE 33: onepay-napas.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.ts
📊 Thống kê: 102 điều kiện duy nhất
   - === : 20 lần
   - == : 47 lần
   - !== : 4 lần
   - != : 31 lần
--------------------------------------------------------------------------------

=== (20 điều kiện):
  1. [Dòng 120] if (isIE[0] === 'MSIE'
  2. [Dòng 120] +isIE[1] === 10) {
  3. [Dòng 144] if (this.timeLeft === 10) {
  4. [Dòng 148] if (this.runTime === true) {
  5. [Dòng 154] if (this.timeLeft === 0) {
  6. [Dòng 156] if (this.runTime === true) this.submitCardBanking();
  7. [Dòng 336] if (event.keyCode === 8
  8. [Dòng 336] event.key === "Backspace"
  9. [Dòng 551] if (approval.method === 'REDIRECT') {
  10. [Dòng 554] } else if (approval.method === 'POST_REDIRECT') {
  11. [Dòng 628] if (valIn === this._translate.instant('bank_card_number')) {
  12. [Dòng 630] if (this.timeLeft === 1) {
  13. [Dòng 647] } else if (valIn === this._translate.instant('internet_banking')) {
  14. [Dòng 727] if (_val.value === ''
  15. [Dòng 727] _val.value === null
  16. [Dòng 727] _val.value === undefined) {
  17. [Dòng 738] if (_val.value && (this.cardTypeBank === 'bank_card_number')) {
  18. [Dòng 748] if ((this.cardTypeBank === 'bank_card_number'
  19. [Dòng 786] if (this.cardName === undefined
  20. [Dòng 786] this.cardName === '') {

== (47 điều kiện):
  1. [Dòng 111] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 136] if (this._b == 67
  3. [Dòng 136] this._b == 2) {//19BIDV
  4. [Dòng 142] if ((this._b == 2
  5. [Dòng 142] !this.checkTwoEnabled) || (this._b == 2
  6. [Dòng 165] } else if (this._b == 2
  7. [Dòng 168] if (this._b == 67) {
  8. [Dòng 204] if (_re.status == '200'
  9. [Dòng 204] _re.status == '201') {
  10. [Dòng 209] if (this._res_post.state == 'approved'
  11. [Dòng 209] this._res_post.state == 'failed') {
  12. [Dòng 213] } else if (this._res_post.state == 'authorization_required') {
  13. [Dòng 321] return this._b == 2
  14. [Dòng 321] this._b == 67
  15. [Dòng 336] event.inputType == 'deleteContentBackward') {
  16. [Dòng 337] if (event.target.name == 'exp_date'
  17. [Dòng 345] event.inputType == 'insertCompositionText') {
  18. [Dòng 411] if (temp.length == 0) {
  19. [Dòng 418] return (counter % 10 == 0);
  20. [Dòng 434] _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
  21. [Dòng 463] this.token_site == 'onepay'
  22. [Dòng 591] if ((cardNo.length == 16
  23. [Dòng 591] if ((cardNo.length == 16 || (cardNo.length == 19
  24. [Dòng 592] this.checkMod10(cardNo) == true
  25. [Dòng 605] if (this._b == +e.id) {
  26. [Dòng 681] if (this._b == 19) {
  27. [Dòng 685] if (this._b == 27
  28. [Dòng 685] this._b == 3) {
  29. [Dòng 760] if (this._b != 68 || (this._b == 68
  30. [Dòng 769] return ((value.length == 4
  31. [Dòng 769] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  32. [Dòng 769] value.length == 5) && parseInt(value.split('/')[0]
  33. [Dòng 773] || (this._util.checkValidExpireMonthYear(value) && (this._b == 2
  34. [Dòng 773] this._b == 20
  35. [Dòng 773] this._b == 33
  36. [Dòng 774] this._b == 39
  37. [Dòng 774] this._b == 43
  38. [Dòng 774] this._b == 45
  39. [Dòng 774] this._b == 64
  40. [Dòng 774] this._b == 68
  41. [Dòng 774] this._b == 72))) //sonnh them Vietbank 72
  42. [Dòng 795] this._inExpDate.length == 4
  43. [Dòng 795] this._inExpDate.search('/') == -1)
  44. [Dòng 796] this._inExpDate.length == 5))
  45. [Dòng 798] || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 20
  46. [Dòng 798] this._b == 2
  47. [Dòng 798] this._b == 72)));

!== (4 điều kiện):
  1. [Dòng 228] codeResponse.toString() !== '0') {
  2. [Dòng 589] let _b = this._b !== 67 ? 67 : this._b
  3. [Dòng 674] if (this.cardTypeBank !== 'internet_banking') {
  4. [Dòng 748] this._b !== 18)) {

!= (31 điều kiện):
  1. [Dòng 161] if (this.htmlDesc != null
  2. [Dòng 217] if (this._res_post.authorization != null
  3. [Dòng 217] this._res_post.authorization.links != null
  4. [Dòng 217] this._res_post.authorization.links.approval != null) {
  5. [Dòng 273] if (ua.indexOf('safari') != -1
  6. [Dòng 338] if (this._inExpDate.length != 3) {
  7. [Dòng 434] if (_formCard.exp_date != null
  8. [Dòng 439] if (this.cardName != null
  9. [Dòng 479] if (this._res_post.return_url != null) {
  10. [Dòng 482] if (this._res_post.links != null
  11. [Dòng 482] this._res_post.links.merchant_return != null
  12. [Dòng 482] this._res_post.links.merchant_return.href != null) {
  13. [Dòng 536] this._res_post.links.cancel != null) {
  14. [Dòng 541] let userName = _formCard.name != null ? _formCard.name : ''
  15. [Dòng 542] this._res_post.authorization.links.approval != null
  16. [Dòng 542] this._res_post.authorization.links.approval.href != null) {
  17. [Dòng 545] userName = paramUserName != null ? paramUserName : ''
  18. [Dòng 591] this._b != 27
  19. [Dòng 591] this._b != 12
  20. [Dòng 591] this._b != 3))
  21. [Dòng 760] if (this._b != 68
  22. [Dòng 771] this._b != 2
  23. [Dòng 771] this._b != 20
  24. [Dòng 771] this._b != 33
  25. [Dòng 771] this._b != 39
  26. [Dòng 772] this._b != 43
  27. [Dòng 772] this._b != 45
  28. [Dòng 772] this._b != 64
  29. [Dòng 772] this._b != 67
  30. [Dòng 772] this._b != 68
  31. [Dòng 772] this._b != 72)

================================================================================

📁 FILE 34: otp-auth.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 35: otp-auth.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
📊 Thống kê: 18 điều kiện duy nhất
   - === : 0 lần
   - == : 11 lần
   - !== : 1 lần
   - != : 6 lần
--------------------------------------------------------------------------------

== (11 điều kiện):
  1. [Dòng 98] if (this._b == 8) {//MB Bank
  2. [Dòng 102] if (this._b == 18) {//Oceanbank
  3. [Dòng 138] if (this._b == 8) {
  4. [Dòng 143] if (this._b == 18) {
  5. [Dòng 148] if (this._b == 12) { //SHB
  6. [Dòng 169] if (_re.status == '200'
  7. [Dòng 169] _re.status == '201') {
  8. [Dòng 178] } else if (this._res.state == 'authorization_required') {
  9. [Dòng 209] if (this.challengeCode == '') {
  10. [Dòng 302] if (this._b == 12) {
  11. [Dòng 356] if (this._b == 18) {//8-MB Bank;18-oceanbank

!== (1 điều kiện):
  1. [Dòng 184] codeResponse.toString() !== '0') {

!= (6 điều kiện):
  1. [Dòng 174] if (this._res.links != null
  2. [Dòng 174] this._res.links.merchant_return != null
  3. [Dòng 174] this._res.links.merchant_return.href != null) {
  4. [Dòng 351] if (!(_formCard.otp != null
  5. [Dòng 357] if (!(_formCard.password != null
  6. [Dòng 373] if (ua.indexOf('safari') != -1

================================================================================

📁 FILE 36: shb.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/shb/shb.component.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 3 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 25] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 42] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  3. [Dòng 42] _inExpDate.trim().length === 0)"

== (3 điều kiện):
  1. [Dòng 22] <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_card_number')">
  2. [Dòng 66] <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_account_number')">
  3. [Dòng 93] token_site == 'onepay'

================================================================================

📁 FILE 37: shb.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/shb/shb.component.ts
📊 Thống kê: 66 điều kiện duy nhất
   - === : 17 lần
   - == : 33 lần
   - !== : 5 lần
   - != : 11 lần
--------------------------------------------------------------------------------

=== (17 điều kiện):
  1. [Dòng 109] if (isIE[0] === 'MSIE'
  2. [Dòng 109] +isIE[1] === 10) {
  3. [Dòng 151] if (focusElement === 'card_name') {
  4. [Dòng 153] } else if (focusElement === 'exp_date'
  5. [Dòng 174] focusExpDateElement === 'card_name') {
  6. [Dòng 389] if (this.cardTypeBank === 'bank_account_number'
  7. [Dòng 434] if (valIn === this._translate.instant('bank_card_number')) {
  8. [Dòng 440] } else if (valIn === this._translate.instant('bank_account_number')) {
  9. [Dòng 482] if (_val.value === ''
  10. [Dòng 482] _val.value === null
  11. [Dòng 482] _val.value === undefined) {
  12. [Dòng 493] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  13. [Dòng 493] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  14. [Dòng 500] this.cardTypeOcean === 'MB') {
  15. [Dòng 508] this.cardTypeOcean === 'IB'
  16. [Dòng 514] if ((this.cardTypeBank === 'bank_card_number'
  17. [Dòng 537] if (this.cardTypeOcean === 'IB') {

== (33 điều kiện):
  1. [Dòng 100] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 118] if (this._b == 12) this.isShbGroup = true;
  3. [Dòng 139] return this._b == 9
  4. [Dòng 139] this._b == 11
  5. [Dòng 139] this._b == 16
  6. [Dòng 139] this._b == 17
  7. [Dòng 139] this._b == 25
  8. [Dòng 139] this._b == 44
  9. [Dòng 140] this._b == 57
  10. [Dòng 140] this._b == 59
  11. [Dòng 140] this._b == 61
  12. [Dòng 140] this._b == 63
  13. [Dòng 140] this._b == 69
  14. [Dòng 222] if (this._b == 12
  15. [Dòng 222] this.cardTypeBank == 'bank_account_number') {
  16. [Dòng 233] _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
  17. [Dòng 265] this.token_site == 'onepay'
  18. [Dòng 282] if (_re.status == '200'
  19. [Dòng 282] _re.status == '201') {
  20. [Dòng 286] if (this._res_post.state == 'approved'
  21. [Dòng 286] this._res_post.state == 'failed') {
  22. [Dòng 292] } else if (this._res_post.state == 'authorization_required') {
  23. [Dòng 392] if ((cardNo.length == 16
  24. [Dòng 392] cardNo.length == 19) && cardNo.startsWith("970443") && this._util.checkMod10(cardNo) && this.checkBin(cardNo)) {
  25. [Dòng 404] if (this._b == +e.id) {
  26. [Dòng 420] if (valIn == 1) {
  27. [Dòng 422] } else if (valIn == 2) {
  28. [Dòng 493] this._b == 18))) {
  29. [Dòng 500] } else if (this._b == 18
  30. [Dòng 514] this._b == 18)) {
  31. [Dòng 526] this.c_expdate = !(((this.valueDate.length == 4
  32. [Dòng 526] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  33. [Dòng 526] this.valueDate.length == 5)

!== (5 điều kiện):
  1. [Dòng 270] key !== '3') {
  2. [Dòng 312] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  3. [Dòng 330] codeResponse.toString() !== '0') {
  4. [Dòng 389] cardNo.length !== 0) {
  5. [Dòng 514] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (11 điều kiện):
  1. [Dòng 126] if (this.htmlDesc != null
  2. [Dòng 187] if (ua.indexOf('safari') != -1
  3. [Dòng 233] if (_formCard.exp_date != null
  4. [Dòng 238] if (this.cardName != null
  5. [Dòng 289] if (this._res_post.links != null
  6. [Dòng 289] this._res_post.links.merchant_return != null
  7. [Dòng 289] this._res_post.links.merchant_return.href != null) {
  8. [Dòng 295] if (this._res_post.authorization != null
  9. [Dòng 295] this._res_post.authorization.links != null
  10. [Dòng 295] this._res_post.authorization.links.approval != null) {
  11. [Dòng 302] this._res_post.links.cancel != null) {

================================================================================

📁 FILE 38: techcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 39: techcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 40: techcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
📊 Thống kê: 14 điều kiện duy nhất
   - === : 1 lần
   - == : 10 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 71] if (this.timeLeft === 0) {

== (10 điều kiện):
  1. [Dòng 62] if (this._b == 2
  2. [Dòng 62] this._b == 31) {
  3. [Dòng 101] if (this._b == 2) {
  4. [Dòng 103] } else if (this._b == 6) {
  5. [Dòng 105] } else if (this._b == 31) {
  6. [Dòng 135] if (_re.status == '200'
  7. [Dòng 135] _re.status == '201') {
  8. [Dòng 140] if (this._res_post.state == 'approved'
  9. [Dòng 140] this._res_post.state == 'failed') {
  10. [Dòng 144] } else if (this._res_post.state == 'authorization_required') {

!= (3 điều kiện):
  1. [Dòng 148] if (this._res_post.authorization != null
  2. [Dòng 148] this._res_post.authorization.links != null
  3. [Dòng 148] this._res_post.authorization.links.approval != null) {

================================================================================

📁 FILE 41: vibbank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 3 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 28] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 42] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 42] valueDate.trim().length === 0)"

== (1 điều kiện):
  1. [Dòng 68] token_site == 'onepay'

================================================================================

📁 FILE 42: vibbank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 43: vibbank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.ts
📊 Thống kê: 98 điều kiện duy nhất
   - === : 36 lần
   - == : 35 lần
   - !== : 10 lần
   - != : 17 lần
--------------------------------------------------------------------------------

=== (36 điều kiện):
  1. [Dòng 127] if (isIE[0] === 'MSIE'
  2. [Dòng 127] +isIE[1] === 10) {
  3. [Dòng 158] if (this.timeLeft === 0) {
  4. [Dòng 201] if ((_val.value.substr(-1) === ' '
  5. [Dòng 201] _val.value.length === 24) {
  6. [Dòng 211] if (this.cardTypeBank === 'bank_card_number') {
  7. [Dòng 216] } else if (this.cardTypeBank === 'bank_account_number') {
  8. [Dòng 222] } else if (this.cardTypeBank === 'bank_username') {
  9. [Dòng 226] } else if (this.cardTypeBank === 'bank_customer_code') {
  10. [Dòng 247] if (this.cardTypeBank === 'bank_account_number') {
  11. [Dòng 258] this.cardTypeBank === 'bank_card_number') {
  12. [Dòng 498] if (event.keyCode === 8
  13. [Dòng 498] event.key === "Backspace"
  14. [Dòng 538] if (v.length === 2
  15. [Dòng 538] this.flag.length === 3
  16. [Dòng 538] this.flag.charAt(this.flag.length - 1) === '/') {
  17. [Dòng 542] if (v.length === 1) {
  18. [Dòng 544] } else if (v.length === 2) {
  19. [Dòng 547] v.length === 2) {
  20. [Dòng 555] if (len === 2) {
  21. [Dòng 792] if ((this.cardTypeBank === 'bank_account_number'
  22. [Dòng 792] this.cardTypeBank === 'bank_username'
  23. [Dòng 792] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  24. [Dòng 842] if (valIn === this._translate.instant('bank_card_number')) {
  25. [Dòng 861] } else if (valIn === this._translate.instant('bank_account_number')) {
  26. [Dòng 873] } else if (valIn === this._translate.instant('bank_username')) {
  27. [Dòng 884] } else if (valIn === this._translate.instant('bank_customer_code')) {
  28. [Dòng 941] if (_val.value === ''
  29. [Dòng 941] _val.value === null
  30. [Dòng 941] _val.value === undefined) {
  31. [Dòng 952] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  32. [Dòng 962] if ((this.cardTypeBank === 'bank_card_number'
  33. [Dòng 994] if (this.cardName === undefined
  34. [Dòng 994] this.cardName === '') {
  35. [Dòng 1002] if (this.valueDate === undefined
  36. [Dòng 1002] this.valueDate === '') {

== (35 điều kiện):
  1. [Dòng 118] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 141] if (this._b == 5) {//5-vib;
  3. [Dòng 210] if (this._b == 5) {
  4. [Dòng 244] if (this.checkBin(_val.value) && (this._b == 5)) {
  5. [Dòng 260] if (this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  6. [Dòng 322] if (this.checkBin(v) && (this._b == 5)) {
  7. [Dòng 498] event.inputType == 'deleteContentBackward') {
  8. [Dòng 499] if (event.target.name == 'exp_date'
  9. [Dòng 507] event.inputType == 'insertCompositionText') {
  10. [Dòng 522] if (((this.valueDate.length == 4
  11. [Dòng 522] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  12. [Dòng 522] this.valueDate.length == 5)
  13. [Dòng 602] if (temp.length == 0) {
  14. [Dòng 609] return (counter % 10 == 0);
  15. [Dòng 640] _formCard.exp_date.length == 5
  16. [Dòng 640] this._b == 5) {//5 vib ;
  17. [Dòng 645] this._b == 5) {//5vib;
  18. [Dòng 673] this.token_site == 'onepay'
  19. [Dòng 692] if (_re.status == '200'
  20. [Dòng 692] _re.status == '201') {
  21. [Dòng 697] if (this._res_post.state == 'approved'
  22. [Dòng 697] this._res_post.state == 'failed') {
  23. [Dòng 704] } else if (this._res_post.state == 'authorization_required') {
  24. [Dòng 797] if ((cardNo.length == 16
  25. [Dòng 797] if ((cardNo.length == 16 || (cardNo.length == 19
  26. [Dòng 798] && ((this._b == 18
  27. [Dòng 798] cardNo.length == 19) || this._b != 18)
  28. [Dòng 811] if (this._b == +e.id) {
  29. [Dòng 827] if (valIn == 1) {
  30. [Dòng 829] } else if (valIn == 2) {
  31. [Dòng 952] this._b == 18)) {
  32. [Dòng 974] this.c_expdate = !(((this.valueDate.length == 4
  33. [Dòng 1005] this.valueDate.length == 4
  34. [Dòng 1005] this.valueDate.search('/') == -1)
  35. [Dòng 1006] this.valueDate.length == 5))

!== (10 điều kiện):
  1. [Dòng 201] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 678] key !== '3') {
  3. [Dòng 726] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 743] codeResponse.toString() !== '0') {
  5. [Dòng 792] cardNo.length !== 0) {
  6. [Dòng 849] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 864] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 878] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 891] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 962] this._b !== 18) || (this._b == 18)) {

!= (17 điều kiện):
  1. [Dòng 168] if (this.htmlDesc != null
  2. [Dòng 198] if (ua.indexOf('safari') != -1
  3. [Dòng 208] if (_val.value != '') {
  4. [Dòng 500] if (this.valueDate.length != 3) {
  5. [Dòng 640] if (_formCard.exp_date != null
  6. [Dòng 645] if (this.cardName != null
  7. [Dòng 700] if (this._res_post.links != null
  8. [Dòng 700] this._res_post.links.merchant_return != null
  9. [Dòng 700] this._res_post.links.merchant_return.href != null) {
  10. [Dòng 708] if (this._res_post.authorization != null
  11. [Dòng 708] this._res_post.authorization.links != null
  12. [Dòng 708] this._res_post.authorization.links.approval != null) {
  13. [Dòng 715] this._res_post.links.cancel != null) {
  14. [Dòng 797] this._b != 27
  15. [Dòng 797] this._b != 12
  16. [Dòng 797] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  17. [Dòng 798] this._b != 18)

================================================================================

📁 FILE 44: vietcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 28] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  2. [Dòng 28] _inExpDate.trim().length === 0)"

== (2 điều kiện):
  1. [Dòng 55] token_site == 'onepay'
  2. [Dòng 65] _b == 68"

================================================================================

📁 FILE 45: vietcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 46: vietcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
📊 Thống kê: 95 điều kiện duy nhất
   - === : 4 lần
   - == : 64 lần
   - !== : 2 lần
   - != : 25 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 215] if (event.keyCode === 8
  2. [Dòng 215] event.key === "Backspace"
  3. [Dòng 457] if (approval.method === 'REDIRECT') {
  4. [Dòng 460] } else if (approval.method === 'POST_REDIRECT') {

== (64 điều kiện):
  1. [Dòng 97] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 120] if (this._b == 1
  3. [Dòng 120] this._b == 20
  4. [Dòng 120] this._b == 36
  5. [Dòng 120] this._b == 64
  6. [Dòng 120] this._b == 55
  7. [Dòng 120] this._b == 47
  8. [Dòng 120] this._b == 48
  9. [Dòng 120] this._b == 59) {
  10. [Dòng 134] return this._b == 9
  11. [Dòng 134] this._b == 16
  12. [Dòng 134] this._b == 17
  13. [Dòng 134] this._b == 25
  14. [Dòng 134] this._b == 44
  15. [Dòng 135] this._b == 57
  16. [Dòng 135] this._b == 59
  17. [Dòng 135] this._b == 61
  18. [Dòng 135] this._b == 63
  19. [Dòng 135] this._b == 69
  20. [Dòng 139] return this._b == 11
  21. [Dòng 139] this._b == 32
  22. [Dòng 139] this._b == 33
  23. [Dòng 139] this._b == 39
  24. [Dòng 139] this._b == 43
  25. [Dòng 139] this._b == 45
  26. [Dòng 139] this._b == 67
  27. [Dòng 139] this._b == 68
  28. [Dòng 139] this._b == 72
  29. [Dòng 139] this._b == 73
  30. [Dòng 139] this._b == 74
  31. [Dòng 139] this._b == 75
  32. [Dòng 215] event.inputType == 'deleteContentBackward') {
  33. [Dòng 216] if (event.target.name == 'exp_date'
  34. [Dòng 224] event.inputType == 'insertCompositionText') {
  35. [Dòng 334] this.token_site == 'onepay'
  36. [Dòng 348] if (this._res_post.state == 'approved'
  37. [Dòng 348] this._res_post.state == 'failed') {
  38. [Dòng 397] } else if (this._res_post.state == 'authorization_required') {
  39. [Dòng 419] this._b == 14
  40. [Dòng 419] this._b == 15
  41. [Dòng 419] this._b == 24
  42. [Dòng 419] this._b == 8
  43. [Dòng 419] this._b == 10
  44. [Dòng 419] this._b == 22
  45. [Dòng 419] this._b == 23
  46. [Dòng 419] this._b == 30
  47. [Dòng 419] this._b == 11
  48. [Dòng 419] this._b == 9) {
  49. [Dòng 488] if (err.status == 400
  50. [Dòng 488] err.status == 500) {
  51. [Dòng 489] if (err.error && (err.error.code == 13
  52. [Dòng 489] err.error.code == '13')) {
  53. [Dòng 502] if ((cardNo.length == 16
  54. [Dòng 503] (cardNo.length == 19
  55. [Dòng 503] (cardNo.length == 19 && (this._b == 1
  56. [Dòng 503] this._b == 4
  57. [Dòng 503] this._b == 59))
  58. [Dòng 505] this._util.checkMod10(cardNo) == true
  59. [Dòng 541] return ((value.length == 4
  60. [Dòng 541] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  61. [Dòng 541] value.length == 5) && parseInt(value.split('/')[0]
  62. [Dòng 575] this._inExpDate.length == 4
  63. [Dòng 575] this._inExpDate.search('/') == -1)
  64. [Dòng 576] this._inExpDate.length == 5))

!== (2 điều kiện):
  1. [Dòng 361] codeResponse.toString() !== '0') {
  2. [Dòng 420] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (25 điều kiện):
  1. [Dòng 125] if (this.htmlDesc != null
  2. [Dòng 152] if (ua.indexOf('safari') != -1
  3. [Dòng 217] if (this._inExpDate.length != 3) {
  4. [Dòng 297] if (this._b != 9
  5. [Dòng 297] this._b != 16
  6. [Dòng 297] this._b != 17
  7. [Dòng 297] this._b != 25
  8. [Dòng 297] this._b != 44
  9. [Dòng 298] this._b != 57
  10. [Dòng 298] this._b != 59
  11. [Dòng 298] this._b != 61
  12. [Dòng 298] this._b != 63
  13. [Dòng 298] this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
  14. [Dòng 311] '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73'].indexOf(this._b.toString()) != -1) {
  15. [Dòng 350] if (this._res_post.return_url != null) {
  16. [Dòng 353] if (this._res_post.links != null
  17. [Dòng 353] this._res_post.links.merchant_return != null
  18. [Dòng 353] this._res_post.links.merchant_return.href != null) {
  19. [Dòng 402] if (this._res_post.authorization != null
  20. [Dòng 402] this._res_post.authorization.links != null
  21. [Dòng 407] this._res_post.links.cancel != null) {
  22. [Dòng 413] let userName = _formCard.name != null ? _formCard.name : ''
  23. [Dòng 414] this._res_post.authorization.links.approval != null
  24. [Dòng 414] this._res_post.authorization.links.approval.href != null) {
  25. [Dòng 417] userName = paramUserName != null ? paramUserName : ''

================================================================================

📁 FILE 47: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 48: off-bank-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 49: off-bank-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 50: policy-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/dialog/policy-dialog/policy-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 51: policy-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/dialog/policy-dialog/policy-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 52: domescard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/domescard-main.component.html
📊 Thống kê: 9 điều kiện duy nhất
   - === : 1 lần
   - == : 8 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 15] filteredData.length === 0"

== (8 điều kiện):
  1. [Dòng 23] ((!token && _auth==0 && vietcombankGroupSelected) || (token && bankId==16))
  2. [Dòng 23] bankId==16))">
  3. [Dòng 27] _auth==0 && techcombankGroupSelected
  4. [Dòng 32] _auth==0 && onepaynapasGroupSelected
  5. [Dòng 40] _auth==0 && shbGroupSelected
  6. [Dòng 46] _auth==0 && bankaccountGroupSelected
  7. [Dòng 50] _auth==0 && vibbankGroupSelected
  8. [Dòng 55] (token || _auth==1) && _b != 16

================================================================================

📁 FILE 53: domescard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/domescard-main/domescard-main.component.ts
📊 Thống kê: 133 điều kiện duy nhất
   - === : 21 lần
   - == : 103 lần
   - !== : 2 lần
   - != : 7 lần
--------------------------------------------------------------------------------

=== (21 điều kiện):
  1. [Dòng 287] if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
  2. [Dòng 288] filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
  3. [Dòng 333] if (valOut === 'auth') {
  4. [Dòng 495] if (this._b === '1'
  5. [Dòng 495] this._b === '20'
  6. [Dòng 495] this._b === '64') {
  7. [Dòng 498] if (this._b === '36'
  8. [Dòng 498] this._b === '18'
  9. [Dòng 498] this._b === '19'
  10. [Dòng 501] if (this._b === '19'
  11. [Dòng 501] this._b === '16'
  12. [Dòng 501] this._b === '25'
  13. [Dòng 501] this._b === '33'
  14. [Dòng 502] this._b === '39'
  15. [Dòng 502] this._b === '11'
  16. [Dòng 502] this._b === '17'
  17. [Dòng 503] this._b === '36'
  18. [Dòng 503] this._b === '44'
  19. [Dòng 504] this._b === '64'
  20. [Dòng 507] if (this._b === '20'
  21. [Dòng 510] if (this._b === '18') {

== (103 điều kiện):
  1. [Dòng 186] this._auth == 0
  2. [Dòng 186] this.tokenList.length == 0) {
  3. [Dòng 263] if (item.b.id == '2'
  4. [Dòng 263] item.b.id == '67') {
  5. [Dòng 273] this.filteredData.length == 1
  6. [Dòng 310] $event == 'true') {
  7. [Dòng 318] _b: this._b == '2' ? '67' : this._b
  8. [Dòng 339] if (bankid == 2
  9. [Dòng 339] bankid == 67) {
  10. [Dòng 342] || (off && !this.enabledTwoBankTech && ((bankid == '2'
  11. [Dòng 342] this.isOffTechcombank) || (bankid == '67'
  12. [Dòng 422] if (bankId == 1
  13. [Dòng 422] bankId == 4
  14. [Dòng 422] bankId == 7
  15. [Dòng 422] bankId == 8
  16. [Dòng 422] bankId == 9
  17. [Dòng 422] bankId == 10
  18. [Dòng 422] bankId == 11
  19. [Dòng 422] bankId == 14
  20. [Dòng 422] bankId == 15
  21. [Dòng 423] bankId == 16
  22. [Dòng 423] bankId == 17
  23. [Dòng 423] bankId == 20
  24. [Dòng 423] bankId == 22
  25. [Dòng 423] bankId == 23
  26. [Dòng 423] bankId == 24
  27. [Dòng 423] bankId == 25
  28. [Dòng 423] bankId == 30
  29. [Dòng 423] bankId == 33
  30. [Dòng 424] bankId == 34
  31. [Dòng 424] bankId == 35
  32. [Dòng 424] bankId == 36
  33. [Dòng 424] bankId == 37
  34. [Dòng 424] bankId == 38
  35. [Dòng 424] bankId == 39
  36. [Dòng 424] bankId == 40
  37. [Dòng 424] bankId == 41
  38. [Dòng 424] bankId == 42
  39. [Dòng 425] bankId == 43
  40. [Dòng 425] bankId == 44
  41. [Dòng 425] bankId == 45
  42. [Dòng 425] bankId == 46
  43. [Dòng 425] bankId == 47
  44. [Dòng 425] bankId == 48
  45. [Dòng 425] bankId == 49
  46. [Dòng 425] bankId == 50
  47. [Dòng 425] bankId == 51
  48. [Dòng 426] bankId == 52
  49. [Dòng 426] bankId == 53
  50. [Dòng 426] bankId == 54
  51. [Dòng 426] bankId == 55
  52. [Dòng 426] bankId == 56
  53. [Dòng 426] bankId == 57
  54. [Dòng 426] bankId == 58
  55. [Dòng 426] bankId == 59
  56. [Dòng 426] bankId == 60
  57. [Dòng 427] bankId == 61
  58. [Dòng 427] bankId == 62
  59. [Dòng 427] bankId == 63
  60. [Dòng 427] bankId == 64
  61. [Dòng 427] bankId == 65
  62. [Dòng 427] bankId == 66
  63. [Dòng 427] bankId == 68
  64. [Dòng 427] bankId == 69
  65. [Dòng 427] bankId == 70
  66. [Dòng 428] bankId == 71
  67. [Dòng 428] bankId == 72
  68. [Dòng 428] bankId == 73
  69. [Dòng 428] bankId == 32
  70. [Dòng 428] bankId == 74
  71. [Dòng 428] bankId == 75) {
  72. [Dòng 430] } else if (bankId == 6
  73. [Dòng 430] bankId == 31
  74. [Dòng 430] bankId == 80) {
  75. [Dòng 432] } else if (bankId == 2
  76. [Dòng 432] bankId == 67) {
  77. [Dòng 434] } else if (bankId == 3
  78. [Dòng 434] bankId == 18
  79. [Dòng 434] bankId == 19
  80. [Dòng 434] bankId == 27) {
  81. [Dòng 436] } else if (bankId == 5) {
  82. [Dòng 438] } else if (bankId == 12) {
  83. [Dòng 498] this._b == '55'
  84. [Dòng 498] this._b == '47'
  85. [Dòng 498] this._b == '48'
  86. [Dòng 498] this._b == '59'
  87. [Dòng 498] this._b == '73'
  88. [Dòng 498] this._b == '12') {
  89. [Dòng 501] this._b == '3'
  90. [Dòng 502] this._b == '43'
  91. [Dòng 502] this._b == '45'
  92. [Dòng 503] this._b == '57'
  93. [Dòng 504] this._b == '61'
  94. [Dòng 504] this._b == '63'
  95. [Dòng 504] this._b == '67'
  96. [Dòng 504] this._b == '68'
  97. [Dòng 504] this._b == '69'
  98. [Dòng 504] this._b == '72'
  99. [Dòng 504] this._b == '9'
  100. [Dòng 504] this._b == '74'
  101. [Dòng 504] this._b == '75') {
  102. [Dòng 507] this._b == '36'
  103. [Dòng 527] if (item['id'] == this._b) {

!== (2 điều kiện):
  1. [Dòng 121] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  2. [Dòng 268] this.bankList = this.bankList.filter(item => item.b.id !== '67');

!= (7 điều kiện):
  1. [Dòng 172] if (params['locale'] != null) {
  2. [Dòng 178] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 183] this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
  4. [Dòng 208] if (!(strInstrument != null
  5. [Dòng 211] if (strInstrument.substring(0, 1) != '^'
  6. [Dòng 211] strInstrument.substr(strInstrument.length - 1) != '$') {
  7. [Dòng 401] if (bankid != null) {

================================================================================

📁 FILE 54: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/intercard-main/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 55: intercard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/intercard-main/intercard-main.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 172] token_site == 'onepay'

================================================================================

📁 FILE 56: intercard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/intercard-main/intercard-main.component.ts
📊 Thống kê: 81 điều kiện duy nhất
   - === : 10 lần
   - == : 47 lần
   - !== : 11 lần
   - != : 13 lần
--------------------------------------------------------------------------------

=== (10 điều kiện):
  1. [Dòng 267] if (_formCard.country === 'default') {
  2. [Dòng 606] if (event.keyCode === 8
  3. [Dòng 606] event.key === "Backspace"
  4. [Dòng 681] if ((v.substr(-1) === ' '
  5. [Dòng 895] this._i_country_code === 'US') {
  6. [Dòng 932] const insertIndex = this._i_country_code === 'US' ? 5 : 3
  7. [Dòng 934] if (temp[i] === '-'
  8. [Dòng 934] temp[i] === ' ') {
  9. [Dòng 941] insertIndex === 3 ? ' ' : itemRemoved)
  10. [Dòng 986] this.c_country = _val.value === 'default'

== (47 điều kiện):
  1. [Dòng 147] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 305] this.token_site == 'onepay'
  3. [Dòng 330] if (this._res_post.state == 'approved'
  4. [Dòng 330] this._res_post.state == 'failed') {
  5. [Dòng 356] } else if(this._res_post.state == 'failed') {
  6. [Dòng 385] } else if (this._res_post.state == 'authorization_required') {
  7. [Dòng 386] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  8. [Dòng 398] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  9. [Dòng 424] if (err.status == 400
  10. [Dòng 424] err.status == 500) {
  11. [Dòng 425] if (err.error && (err.error.code == 8
  12. [Dòng 425] err.error.code == '8')) {
  13. [Dòng 426] if (this._type == 5
  14. [Dòng 426] this._type == 6) {
  15. [Dòng 428] } else if (this._type == 7
  16. [Dòng 428] this._type == 8) {
  17. [Dòng 431] } else if (err.error && (err.error.code == 13
  18. [Dòng 431] err.error.code == '13')) {
  19. [Dòng 498] v.length == 15) || (v.length == 16
  20. [Dòng 498] v.length == 19))
  21. [Dòng 499] this._util.checkMod10(v) == true) {
  22. [Dòng 559] cardNo.length == 15)
  23. [Dòng 561] cardNo.length == 16)
  24. [Dòng 562] cardNo.startsWith('81')) && (cardNo.length == 16
  25. [Dòng 562] cardNo.length == 19))
  26. [Dòng 606] event.inputType == 'deleteContentBackward') {
  27. [Dòng 607] if (event.target.name == 'exp_date'
  28. [Dòng 615] event.inputType == 'insertCompositionText') {
  29. [Dòng 630] if (((this.valueDate.length == 4
  30. [Dòng 630] this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  31. [Dòng 630] this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  32. [Dòng 681] v.length == 5) {
  33. [Dòng 689] v.length == 4
  34. [Dòng 693] v.length == 3)
  35. [Dòng 719] _val.value.length == 4
  36. [Dòng 723] _val.value.length == 3)
  37. [Dòng 948] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  38. [Dòng 948] this.valueDate.length == 5)
  39. [Dòng 975] this.requireAvs = this.AVS_COUNTRIES.find(e => e.code == this._i_country_code) ? true : false;
  40. [Dòng 1033] this.valueDate.length == 4
  41. [Dòng 1033] this.valueDate.search('/') == -1)
  42. [Dòng 1034] this.valueDate.length == 5))
  43. [Dòng 1047] this._i_csc.length == 4) ||
  44. [Dòng 1051] this._i_csc.length == 3)
  45. [Dòng 1130] country = this.AVS_COUNTRIES.find(e => e.code == res.bin_country);
  46. [Dòng 1164] countryCode == 'US' ? US_STATES
  47. [Dòng 1165] : countryCode == 'CA' ? CA_STATES

!== (11 điều kiện):
  1. [Dòng 313] if (this.tracking.hasOwnProperty(key) && ((key !== '8'
  2. [Dòng 313] !this._showNameOnCard) || (key !== '9'
  3. [Dòng 340] codeResponse.toString() !== '0'){
  4. [Dòng 681] event.inputType !== 'deleteContentBackward') || v.length == 5) {
  5. [Dòng 898] this._i_country_code !== 'US') {
  6. [Dòng 940] itemRemoved !== '') {
  7. [Dòng 967] if (deviceValue !== 'default') {
  8. [Dòng 971] this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
  9. [Dòng 1059] this._i_country_code !== 'default'
  10. [Dòng 1088] this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
  11. [Dòng 1095] this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {

!= (13 điều kiện):
  1. [Dòng 175] if (params['locale'] != null) {
  2. [Dòng 179] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 332] if (this._res_post.return_url != null) {
  4. [Dòng 334] } else if (this._res_post.links != null
  5. [Dòng 334] this._res_post.links.merchant_return != null
  6. [Dòng 334] this._res_post.links.merchant_return.href != null) {
  7. [Dòng 480] if (ua.indexOf('safari') != -1
  8. [Dòng 559] cardNo != null
  9. [Dòng 608] if (this.valueDate.length != 3) {
  10. [Dòng 688] v != null
  11. [Dòng 718] this.c_csc = (!(_val.value != null
  12. [Dòng 1045] this._i_csc != null
  13. [Dòng 1132] this.requireAvs = this.isAvsCountry = country != undefined

================================================================================

📁 FILE 57: menu.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/menu.component.html
📊 Thống kê: 8 điều kiện duy nhất
   - === : 1 lần
   - == : 7 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 217] d_vrbank===true"

== (7 điều kiện):
  1. [Dòng 154] method?.trim()=='International'"
  2. [Dòng 165] method.trim()=='ApplePay'"
  3. [Dòng 172] method.trim()=='GooglePay'"
  4. [Dòng 179] method.trim()=='SamsungPay'"
  5. [Dòng 185] method?.trim()=='Domestic'"
  6. [Dòng 194] method?.trim()=='QR'"
  7. [Dòng 202] method?.trim()=='Paypal'"

================================================================================

📁 FILE 58: menu.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/menu.component.ts
📊 Thống kê: 161 điều kiện duy nhất
   - === : 9 lần
   - == : 91 lần
   - !== : 3 lần
   - != : 58 lần
--------------------------------------------------------------------------------

=== (9 điều kiện):
  1. [Dòng 784] this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_mobile_wallet === 1
  2. [Dòng 861] if (this._res.state === 'unpaid'
  3. [Dòng 861] this._res.state === 'not_paid') {
  4. [Dòng 971] if ('op' === auth
  5. [Dòng 1008] } else if ('bank' === auth
  6. [Dòng 1013] if (approval.method === 'REDIRECT') {
  7. [Dòng 1016] } else if (approval.method === 'POST_REDIRECT') {
  8. [Dòng 1308] return id === 'amex' ? '1234' : '123'
  9. [Dòng 1490] if (this.timeLeftPaypal === 0) {

== (91 điều kiện):
  1. [Dòng 212] if (el == 5) {
  2. [Dòng 214] } else if (el == 6) {
  3. [Dòng 216] } else if (el == 7) {
  4. [Dòng 218] } else if (el == 8) {
  5. [Dòng 220] } else if (el == 2) {
  6. [Dòng 222] } else if (el == 4) {
  7. [Dòng 224] } else if (el == 3) {
  8. [Dòng 253] if (!isNaN(_re.status) && (_re.status == '200'
  9. [Dòng 253] _re.status == '201') && _re.body != null) {
  10. [Dòng 258] if (('closed' == this._res_polling.state
  11. [Dòng 258] 'canceled' == this._res_polling.state
  12. [Dòng 258] 'expired' == this._res_polling.state)
  13. [Dòng 278] } else if ('paid' == this._res_polling.state) {
  14. [Dòng 284] this._res_polling.payments == null
  15. [Dòng 293] if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'authorization_required') {
  16. [Dòng 297] } else if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  17. [Dòng 304] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  18. [Dòng 306] this._paymentService.getCurrentPage() == 'enter_card') {
  19. [Dòng 309] } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
  20. [Dòng 309] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
  21. [Dòng 327] } else if ('not_paid' == this._res_polling.state) {
  22. [Dòng 339] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
  23. [Dòng 477] if (message == '1') {
  24. [Dòng 484] if (this.checkInvoiceState() == 1) {
  25. [Dòng 491] if (_re.status == '200'
  26. [Dòng 491] _re.status == '201') {
  27. [Dòng 497] this.version2 = _re.body?.merchant?.qr_version == "2"
  28. [Dòng 518] if (this.type == 5
  29. [Dòng 521] } else if (this.type == 6
  30. [Dòng 524] } else if (this.type == 2
  31. [Dòng 527] } else if (this.type == 7
  32. [Dòng 530] } else if (this.type == 8
  33. [Dòng 533] } else if (this.type == 4
  34. [Dòng 536] } else if (this.type == 3
  35. [Dòng 575] if (this.themeConfig.default_method == 'International'
  36. [Dòng 577] } else if (this.themeConfig.default_method == 'Domestic'
  37. [Dòng 579] } else if (this.themeConfig.default_method == 'QR'
  38. [Dòng 581] } else if (this.themeConfig.default_method == 'Paypal'
  39. [Dòng 644] if (('closed' == this._res.state
  40. [Dòng 644] 'canceled' == this._res.state
  41. [Dòng 644] 'expired' == this._res.state
  42. [Dòng 644] 'paid' == this._res.state)
  43. [Dòng 802] if ((this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_mobile_wallet) == 0
  44. [Dòng 822] this._auth == 0) {
  45. [Dòng 836] if (count == 2
  46. [Dòng 862] if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
  47. [Dòng 862] this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
  48. [Dòng 864] if (this._res.payments[this._res.payments.length - 1].state == 'approved'
  49. [Dòng 912] if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required') {
  50. [Dòng 918] } else if (this._res.payments[this._res.payments.length - 1].state == 'pending') {
  51. [Dòng 924] } else if (this._res.payments[this._res.payments.length - 1].state == 'canceled') {
  52. [Dòng 928] } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  53. [Dòng 928] this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
  54. [Dòng 955] if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  55. [Dòng 958] } else if (idBrand == 'atm'
  56. [Dòng 1036] if ('paid' == this._res.state) {
  57. [Dòng 1037] this._res.merchant.token_site == 'onepay')) {
  58. [Dòng 1080] this._res.payments == null) {
  59. [Dòng 1082] this._res.payments[this._res.payments.length - 1].state == 'pending') {
  60. [Dòng 1092] if (this._res.currencies[0] == 'USD') {
  61. [Dòng 1100] if (this.d_inter == 1) {
  62. [Dòng 1113] } else if (this.type == 1) {
  63. [Dòng 1184] if (item.instrument.issuer.brand.id == 'atm') {
  64. [Dòng 1186] } else if (item.instrument.issuer.brand.id == 'visa'
  65. [Dòng 1186] item.instrument.issuer.brand.id == 'mastercard') {
  66. [Dòng 1187] if (item.instrument.issuer_location == 'd') {
  67. [Dòng 1192] } else if (item.instrument.issuer.brand.id == 'amex') {
  68. [Dòng 1198] } else if (item.instrument.issuer.brand.id == 'jcb') {
  69. [Dòng 1214] uniq.length == 1) {
  70. [Dòng 1281] return merchant.token_cvv == true
  71. [Dòng 1286] return (merchant.token_name == true)
  72. [Dòng 1292] return (merchant.token_email == true)
  73. [Dòng 1298] return (merchant.token_phone == true)
  74. [Dòng 1372] if (type == 'qrv1') {
  75. [Dòng 1382] if (type == 'mobile') {
  76. [Dòng 1384] e.type == 'ewallet'
  77. [Dòng 1384] e.code == 'momo')) {
  78. [Dòng 1392] } else if (type == 'desktop') {
  79. [Dòng 1393] e.type == 'vnpayqr') || (regex.test(strTest)
  80. [Dòng 1434] _val == 2) {
  81. [Dòng 1461] if (_val == 2
  82. [Dòng 1463] } else if (_val == 2
  83. [Dòng 1469] _val == 2
  84. [Dòng 1478] if (this.type == 4) {
  85. [Dòng 1548] if (this._res_post.state == 'approved'
  86. [Dòng 1548] this._res_post.state == 'failed') {
  87. [Dòng 1557] } else if (this._res_post.state == 'authorization_required') {
  88. [Dòng 1558] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  89. [Dòng 1571] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  90. [Dòng 1628] filteredData.length == 1) {
  91. [Dòng 1697] if (data._locale == 'en') {

!== (3 điều kiện):
  1. [Dòng 983] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  2. [Dòng 1425] if (_val !== 3) {
  3. [Dòng 1429] this._util.getElementParams(this.currentUrl, 't_id') !== '') {

!= (58 điều kiện):
  1. [Dòng 246] if (this._idInvoice != null
  2. [Dòng 246] this._paymentService.getState() != 'error') {
  3. [Dòng 252] if (this._paymentService.getCurrentPage() != 'otp') {
  4. [Dòng 253] _re.body != null) {
  5. [Dòng 259] this._res_polling.links != null
  6. [Dòng 259] this._res_polling.links.merchant_return != null //
  7. [Dòng 284] } else if (this._res_polling.merchant != null
  8. [Dòng 284] this._res_polling.merchant_invoice_reference != null
  9. [Dòng 286] } else if (this._res_polling.payments != null
  10. [Dòng 310] this._res_polling.links.merchant_return != null//
  11. [Dòng 330] this._res_polling.payments != null
  12. [Dòng 338] if (this._res_polling.payments != null
  13. [Dòng 342] t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
  14. [Dòng 458] this.type.toString().length != 0) {
  15. [Dòng 464] if (params['locale'] != null) {
  16. [Dòng 470] if ('otp' != this._paymentService.getCurrentPage()) {
  17. [Dòng 482] if (this._paymentService.getInvoiceDetail() != null) {
  18. [Dòng 645] this._res.links != null
  19. [Dòng 645] this._res.links.merchant_return != null
  20. [Dòng 841] if (count != 1) {
  21. [Dòng 851] if (this._res.merchant != null
  22. [Dòng 851] this._res.merchant_invoice_reference != null) {
  23. [Dòng 854] if (this._res.merchant.address_details != null) {
  24. [Dòng 862] this._res.links != null//
  25. [Dòng 907] } else if (this._res.payments != null
  26. [Dòng 929] this._res.payments[this._res.payments.length - 1].instrument != null
  27. [Dòng 929] this._res.payments[this._res.payments.length - 1].instrument.issuer != null
  28. [Dòng 930] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
  29. [Dòng 930] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
  30. [Dòng 931] this._res.payments[this._res.payments.length - 1].links != null
  31. [Dòng 931] this._res.payments[this._res.payments.length - 1].links.cancel != null
  32. [Dòng 931] this._res.payments[this._res.payments.length - 1].links.cancel.href != null
  33. [Dòng 944] this._res.payments[this._res.payments.length - 1].links.update != null
  34. [Dòng 944] this._res.payments[this._res.payments.length - 1].links.update.href != null) {
  35. [Dòng 958] this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
  36. [Dòng 959] this._res.payments[this._res.payments.length - 1].authorization != null
  37. [Dòng 959] this._res.payments[this._res.payments.length - 1].authorization.links != null) {
  38. [Dòng 971] this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
  39. [Dòng 971] this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
  40. [Dòng 974] if (this._res.payments[this._res.payments.length - 1].authorization != null
  41. [Dòng 974] this._res.payments[this._res.payments.length - 1].authorization.links != null
  42. [Dòng 980] auth = paramUserName != null ? paramUserName : ''
  43. [Dòng 1059] this._res.links.merchant_return != null //
  44. [Dòng 1080] } else if (this._res.merchant != null
  45. [Dòng 1080] this._res.merchant_invoice_reference != null
  46. [Dòng 1222] if (['mastercard', 'visa', 'amex', 'jcb', 'cup', 'card', 'np_card', 'atm'].indexOf(id) != -1) {
  47. [Dòng 1224] } else if (['techcombank_account', 'vib_account', 'dongabank_account', 'tpbank_account', 'bidv_account', 'pvcombank_account', 'shb_account', 'oceanbank_online_account'].indexOf(id) != -1) {
  48. [Dòng 1226] } else if (['oceanbank_mobile_account'].indexOf(id) != -1) {
  49. [Dòng 1228] } else if (['shb_customer_id'].indexOf(id) != -1) {
  50. [Dòng 1254] if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1) {
  51. [Dòng 1340] if (!(strInstrument != null
  52. [Dòng 1357] if (this._translate.currentLang != language) {
  53. [Dòng 1384] e.type != 'ewallet') || (regex.test(strTest)
  54. [Dòng 1431] } else if (this._res.payments != null) {
  55. [Dòng 1550] if (this._res_post.return_url != null) {
  56. [Dòng 1552] } else if (this._res_post.links != null
  57. [Dòng 1552] this._res_post.links.merchant_return != null
  58. [Dòng 1552] this._res_post.links.merchant_return.href != null) {

================================================================================

📁 FILE 59: applepay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/applepay/applepay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 60: applepay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/applepay/applepay.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 39] if (event.data?.event_type == 'applepay_network_not_supported') {

!= (1 điều kiện):
  1. [Dòng 37] if (event.origin != window.origin) return; // chỉ nhận message từ OnePay

================================================================================

📁 FILE 61: dialog-network-not-supported.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/applepay/dialog-network-not-supported/dialog-network-not-supported.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 62: dialog-network-not-supported.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/applepay/dialog-network-not-supported/dialog-network-not-supported.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 63: google-pay-button-op.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/googlepay/google-pay-button-op/google-pay-button-op.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 64: google-pay-button-op.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/googlepay/google-pay-button-op/google-pay-button-op.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 185] if (GGPaySDKScript.readyState === "loaded"
  2. [Dòng 185] GGPaySDKScript.readyState === "complete") {

================================================================================

📁 FILE 65: types-google-pay.d.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/googlepay/google-pay-button-op/types-google-pay.d.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 66: googlepay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/googlepay/googlepay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 67: googlepay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/googlepay/googlepay.component.ts
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 25] isTop = window === window.top
  2. [Dòng 59] if (approval.method === 'REDIRECT') {
  3. [Dòng 62] } else if (approval.method === 'POST_REDIRECT') {

== (2 điều kiện):
  1. [Dòng 48] if (res?.body?.state == 'approved') {
  2. [Dòng 57] } else if (res?.body?.state == 'authorization_required') {

================================================================================

📁 FILE 68: mobile-wallet-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/mobile-wallet-main.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (3 điều kiện):
  1. [Dòng 7] paymentType == PaymentType.ApplePay"
  2. [Dòng 8] paymentType == PaymentType.GooglePay"
  3. [Dòng 9] paymentType == PaymentType.SamsungPay"

================================================================================

📁 FILE 69: mobile-wallet-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/mobile-wallet-main.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 86] return currency === 'VND'

== (2 điều kiện):
  1. [Dòng 47] if (this.paymentType == PaymentType.ApplePay) {
  2. [Dòng 74] if (network == 'napas'

!= (3 điều kiện):
  1. [Dòng 57] if (this.paymentType != PaymentType.ApplePay) return;
  2. [Dòng 69] if (this.paymentType != PaymentType.ApplePay) return false;
  3. [Dòng 75] if (network != 'napas'

================================================================================

📁 FILE 70: samsung-pay-button-op.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 71: samsung-pay-button-op.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.ts
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 127] if (GGPaySDKScript.readyState === "loaded"
  2. [Dòng 127] GGPaySDKScript.readyState === "complete") {

== (1 điều kiện):
  1. [Dòng 62] serviceId: this.environment == 'PRODUCTION'? this.serviceIdProd : this.serviceIdTest

================================================================================

📁 FILE 72: samsungpay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/samsungpay/samsungpay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 73: samsungpay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/mobile-wallet-main/samsungpay/samsungpay.component.ts
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 73] if (approval.method === 'REDIRECT') {
  2. [Dòng 76] } else if (approval.method === 'POST_REDIRECT') {

== (2 điều kiện):
  1. [Dòng 61] if (res?.body?.state == 'approved') {
  2. [Dòng 71] } else if (res?.body?.state == 'authorization_required') {

================================================================================

📁 FILE 74: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 75: qr-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 2] screen=='qr'"
  2. [Dòng 112] screen=='confirm_close'"

================================================================================

📁 FILE 76: qr-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 77: qr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main/qr-main.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 6] filteredData.length === 0
  2. [Dòng 6] filteredDataOther.length === 0"
  3. [Dòng 40] filteredDataMobile.length === 0
  4. [Dòng 40] filteredDataOtherMobile.length === 0"

================================================================================

📁 FILE 78: qr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main/qr-main.component.ts
📊 Thống kê: 22 điều kiện duy nhất
   - === : 8 lần
   - == : 8 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 224] this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
  2. [Dòng 225] this.filteredDataOther = this.appList.filter(item => item.type === 'other');
  3. [Dòng 226] this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
  4. [Dòng 227] this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
  5. [Dòng 248] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  6. [Dòng 249] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  7. [Dòng 255] if (item.type === 'mobile_banking') {
  8. [Dòng 353] this.appList.length === 1

== (8 điều kiện):
  1. [Dòng 151] this.themeConfig.deeplink_status == 'Off' ? false : true
  2. [Dòng 254] if (item.available == true) {
  3. [Dòng 320] if (_re.status == '200'
  4. [Dòng 320] _re.status == '201') {
  5. [Dòng 323] if (appcode == 'grabpay'
  6. [Dòng 323] appcode == 'momo') {
  7. [Dòng 326] if (type == 2
  8. [Dòng 364] err.error.code == '04') {

!= (6 điều kiện):
  1. [Dòng 169] if (params['locale'] != null) {
  2. [Dòng 175] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 201] if (!(strInstrument != null
  4. [Dòng 284] if (appcode != null) {
  5. [Dòng 398] if (_re.status != '200'
  6. [Dòng 398] _re.status != '201')

================================================================================

📁 FILE 79: safe-html.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main/safe-html.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 80: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 81: qr-desktop.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 1 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 180] listVNPayQR.length === 0"

== (1 điều kiện):
  1. [Dòng 116] qr_error == null"

!= (2 điều kiện):
  1. [Dòng 7] this.qr_error != null"
  2. [Dòng 111] qr_error != null"

================================================================================

📁 FILE 82: qr-desktop.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.ts
📊 Thống kê: 19 điều kiện duy nhất
   - === : 4 lần
   - == : 10 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 372] this.listWalletQR.length === 1) {
  2. [Dòng 422] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  3. [Dòng 423] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  4. [Dòng 779] this.listWalletQR.length === 1

== (10 điều kiện):
  1. [Dòng 262] e.type == 'vnpayqr') {
  2. [Dòng 269] e.type == 'ewallet') {
  3. [Dòng 319] if (_re.status == '200'
  4. [Dòng 319] _re.status == '201') {
  5. [Dòng 350] e.type == 'wallet')) {
  6. [Dòng 389] if (d.b.code == s) {
  7. [Dòng 428] if (item.available == true) {
  8. [Dòng 487] if (appcode == 'grabpay'
  9. [Dòng 487] appcode == 'momo') {
  10. [Dòng 521] err.error.code == '04') {

!= (5 điều kiện):
  1. [Dòng 223] if (params['locale'] != null) {
  2. [Dòng 252] if (!(strInstrument != null
  3. [Dòng 446] if (appcode != null) {
  4. [Dòng 753] if (_re.status != '200'
  5. [Dòng 753] _re.status != '201')

================================================================================

📁 FILE 83: qr-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1] screen=='qr'"
  2. [Dòng 122] screen=='confirm_close'"

================================================================================

📁 FILE 84: qr-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 50] 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {

================================================================================

📁 FILE 85: qr-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (3 điều kiện):
  1. [Dòng 5] type == 'vnpay'"
  2. [Dòng 6] type == 'bankapp'"
  3. [Dòng 7] type == 'both'"

================================================================================

📁 FILE 86: qr-guide-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 87: list-bank-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 88: list-bank-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 89: qr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 90: qr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-main.component.ts
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 189] this.themeConfig.deeplink_status == 'Off' ? false : true

!= (2 điều kiện):
  1. [Dòng 205] if (params['locale'] != null) {
  2. [Dòng 211] if ('otp' != this._paymentService.getCurrentPage()) {

================================================================================

📁 FILE 91: qr-mobile.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.html
📊 Thống kê: 7 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 142] _locale=='vi'"
  2. [Dòng 144] _locale=='en'"
  3. [Dòng 155] _locale == 'vi'"
  4. [Dòng 157] _locale == 'en'"

!= (3 điều kiện):
  1. [Dòng 126] this.qr_error != null"
  2. [Dòng 248] qr_version2 != 'None'"
  3. [Dòng 274] qr_version2 != 'None'

================================================================================

📁 FILE 92: qr-mobile.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.ts
📊 Thống kê: 30 điều kiện duy nhất
   - === : 6 lần
   - == : 19 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (6 điều kiện):
  1. [Dòng 519] if (!this.d_vnpayqr_wallet && !this.d_vnpayqr_deeplink && (this.listWalletQR?.length === 1
  2. [Dòng 519] this.listWalletDeeplink?.length === 1)) {
  3. [Dòng 587] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  4. [Dòng 588] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  5. [Dòng 594] if (item.type === 'deeplink') {
  6. [Dòng 974] this.listWalletQR?.length === 1

== (19 điều kiện):
  1. [Dòng 280] e.type == 'deeplink') {
  2. [Dòng 291] e.type == 'ewallet'
  3. [Dòng 311] e.type == 'vnpayqr') {
  4. [Dòng 325] e.type == 'wallet')) {
  5. [Dòng 354] e.type == 'ewallet') {
  6. [Dòng 384] if (e.type == 'ewallet') {
  7. [Dòng 407] this.listWallet.length == 1
  8. [Dòng 407] this.listWallet[0].code == 'momo') {
  9. [Dòng 409] this.checkEWalletDeeplink.length == 0) {
  10. [Dòng 444] arrayWallet.length == 0) return false;
  11. [Dòng 446] if (arrayWallet[i].code == key) {
  12. [Dòng 472] if (_re.status == '200'
  13. [Dòng 472] _re.status == '201') {
  14. [Dòng 544] if (d.b.code == s) {
  15. [Dòng 593] if (item.available == true) {
  16. [Dòng 670] if (appcode == 'grabpay'
  17. [Dòng 670] appcode == 'momo') {
  18. [Dòng 673] if (type == 2
  19. [Dòng 714] err.error.code == '04') {

!= (5 điều kiện):
  1. [Dòng 232] if (params['locale'] != null) {
  2. [Dòng 261] if (!(strInstrument != null
  3. [Dòng 621] if (appcode != null) {
  4. [Dòng 944] if (_re.status != '200'
  5. [Dòng 944] _re.status != '201')

================================================================================

📁 FILE 93: safe-html.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/qr-main-version2/safe-html.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 94: queuing.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/queuing/queuing.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 95: queuing.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/queuing/queuing.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 62] if (this.locale == 'vi') {

!= (1 điều kiện):
  1. [Dòng 86] if (this.translate.currentLang != language) {

================================================================================

📁 FILE 96: domescard-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.html
📊 Thống kê: 8 điều kiện duy nhất
   - === : 4 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 1] type === 2"
  2. [Dòng 2] [ngStyle]="{'border-color': type === 2 ? this.themeConfig.border_color : border_color}"
  3. [Dòng 22] *ngIf="((type === 2
  4. [Dòng 22] type === '2'

== (2 điều kiện):
  1. [Dòng 5] uniqueTokenBank) || (type == 2
  2. [Dòng 22] type == 2)) || token">

!= (2 điều kiện):
  1. [Dòng 5] (((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token
  2. [Dòng 18] feeService['atm']['fee'] != 0"

================================================================================

📁 FILE 97: domescard-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 98: intercard-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 1] type === 1"
  2. [Dòng 2] [ngStyle]="{'border-color': type === 1 ? this.themeConfig.border_color : border_color}"

!= (1 điều kiện):
  1. [Dòng 22] feeService['visa_mastercard_d']['fee'] != 0"

================================================================================

📁 FILE 99: intercard-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 100: mobile-wallet-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/sorting-option/mobile-wallet-form/mobile-wallet-form.component.html
📊 Thống kê: 8 điều kiện duy nhất
   - === : 3 lần
   - == : 5 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 1] type === paymentType"
  2. [Dòng 2] [ngStyle]="{'border-color': (type === paymentType
  3. [Dòng 37] type === paymentType

== (5 điều kiện):
  1. [Dòng 5] *ngIf="(!token) || (type == paymentType)">
  2. [Dòng 7] paymentType == PaymentType.ApplePay"
  3. [Dòng 13] paymentType == PaymentType.GooglePay"
  4. [Dòng 19] paymentType == PaymentType.SamsungPay"
  5. [Dòng 34] type == paymentType"

================================================================================

📁 FILE 101: mobile-wallet-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/sorting-option/mobile-wallet-form/mobile-wallet-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 102: paypal-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 1] type === 3"
  2. [Dòng 2] [ngStyle]="{'border-color': type === 3 ? this.themeConfig.border_color : border_color}"

!= (1 điều kiện):
  1. [Dòng 15] feeService['pp']['fee'] != 0"

================================================================================

📁 FILE 103: paypal-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 104: qr-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/sorting-option/qr-form/qr-form.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 2] [ngStyle]="{'border-color': type === 4 ? this.themeConfig.border_color : border_color}"
  2. [Dòng 16] type === 4"
  3. [Dòng 19] type === 4

!= (1 điều kiện):
  1. [Dòng 17] feeService['qr']['fee'] != 0"

================================================================================

📁 FILE 105: qr-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/sorting-option/qr-form/qr-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 106: token-expired-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/token-expired-dialog/token-expired-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 107: token-expired-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/token-expired-dialog/token-expired-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 108: remove-token-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/token-main/remove-token-dialog/remove-token-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 109: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/token-main/token-dialog/dialog-guide-dialog.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (6 điều kiện):
  1. [Dòng 9] data['type'] == 'Visa'
  2. [Dòng 9] data['type'] == 'Master'
  3. [Dòng 9] data['type'] == 'JCB'"
  4. [Dòng 17] data['type'] == 'Visa'"
  5. [Dòng 17] data['type'] == 'Master'"
  6. [Dòng 24] data['type'] == 'Amex'"

================================================================================

📁 FILE 110: token-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/token-main/token-main.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 1 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 8] item.brand_id === 'visa' ? {'height': '14.42px', 'width': '44.67px'

== (1 điều kiện):
  1. [Dòng 24] token_main == '1'"

!= (2 điều kiện):
  1. [Dòng 17] item['feeService']['fee'] != 0
  2. [Dòng 19] item['feeService']['fee'] != 0"

================================================================================

📁 FILE 111: token-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/main/menu/token-main/token-main.component.ts
📊 Thống kê: 65 điều kiện duy nhất
   - === : 6 lần
   - == : 38 lần
   - !== : 1 lần
   - != : 20 lần
--------------------------------------------------------------------------------

=== (6 điều kiện):
  1. [Dòng 99] if (event.keyCode === 13) {
  2. [Dòng 284] && ((token.brand_id === 'amex'
  3. [Dòng 359] return id === 'amex' ? '1234' : '123'
  4. [Dòng 543] if (approval.method === 'REDIRECT') {
  5. [Dòng 546] } else if (approval.method === 'POST_REDIRECT') {
  6. [Dòng 608] return csc != null && !isNaN(+csc) && ((brand_id === 'amex'

== (38 điều kiện):
  1. [Dòng 151] if (message == '0') {
  2. [Dòng 220] item.id == element.id ? element['active'] = true : element['active'] = false
  3. [Dòng 266] if (result == 'success') {
  4. [Dòng 271] if (this.tokenList.length == 0) {
  5. [Dòng 275] } else if (result == 'error') {
  6. [Dòng 284] _val.value.trim().length == 4) || (token.brand_id != 'amex'
  7. [Dòng 284] _val.value.trim().length == 3))
  8. [Dòng 358] id == 'amex' ? this.maxLength = 4 : this.maxLength = 3
  9. [Dòng 414] if (_re.body.state == 'more_info_required') {
  10. [Dòng 436] if (this._res_post.state == 'approved'
  11. [Dòng 436] this._res_post.state == 'failed') {
  12. [Dòng 443] if (this._res_post.state == 'failed') {
  13. [Dòng 459] } else if (this._res_post.state == 'authorization_required') {
  14. [Dòng 460] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  15. [Dòng 473] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  16. [Dòng 489] } else if (_re.body.state == 'authorization_required') {
  17. [Dòng 506] if (this._b == 1
  18. [Dòng 506] this._b == 14
  19. [Dòng 506] this._b == 15
  20. [Dòng 506] this._b == 24
  21. [Dòng 506] this._b == 8
  22. [Dòng 506] this._b == 10
  23. [Dòng 506] this._b == 20
  24. [Dòng 506] this._b == 22
  25. [Dòng 506] this._b == 23
  26. [Dòng 506] this._b == 30
  27. [Dòng 506] this._b == 11
  28. [Dòng 506] this._b == 17
  29. [Dòng 506] this._b == 18
  30. [Dòng 506] this._b == 27
  31. [Dòng 506] this._b == 5
  32. [Dòng 506] this._b == 12
  33. [Dòng 506] this._b == 9) {
  34. [Dòng 564] } else if (_re.body.state == 'failed') {
  35. [Dòng 608] csc.trim().length == 4)
  36. [Dòng 609] csc.trim().length == 3));
  37. [Dòng 686] if (_re.status == '200'
  38. [Dòng 686] _re.status == '201') {

!== (1 điều kiện):
  1. [Dòng 210] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (20 điều kiện):
  1. [Dòng 100] this.themeConfig.merchant.id != 'OPTEST' ? this.themeConfig.on_off_bank : ''
  2. [Dòng 134] this._res.merchant.id != 'OPTEST' ? this._res.on_off_bank : ''
  3. [Dòng 138] if (params['locale'] != null) {
  4. [Dòng 283] if (_val.value != null
  5. [Dòng 334] if (ua.indexOf('safari') != -1
  6. [Dòng 438] if (this._res_post.return_url != null) {
  7. [Dòng 440] } else if (this._res_post.links != null
  8. [Dòng 440] this._res_post.links.merchant_return != null
  9. [Dòng 440] this._res_post.links.merchant_return.href != null) {
  10. [Dòng 494] if (_re.body.authorization != null
  11. [Dòng 494] _re.body.authorization.links != null
  12. [Dòng 501] if (_re.body.links != null
  13. [Dòng 501] _re.body.links.cancel != null) {
  14. [Dòng 566] if (_re.body.return_url != null) {
  15. [Dòng 568] } else if (_re.body.links != null
  16. [Dòng 568] _re.body.links.merchant_return != null
  17. [Dòng 568] _re.body.links.merchant_return.href != null) {
  18. [Dòng 608] return csc != null
  19. [Dòng 609] || (brand_id != 'amex'
  20. [Dòng 619] return this._i_name != ''

================================================================================

📁 FILE 112: overlay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/overlay/overlay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 113: overlay.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/overlay/overlay.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 114: overlay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/overlay/overlay.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 115: bank-amount.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/pipe/bank-amount.pipe.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 11] return ((a.id == id
  2. [Dòng 11] a.code == id) && a.type.includes(type));

================================================================================

📁 FILE 116: close-dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/services/close-dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 117: data.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/services/data.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 73] const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
  2. [Dòng 73] item.method === method) : null;

================================================================================

📁 FILE 118: deep_link.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/services/deep_link.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 9] if (isIphone == true) {
  2. [Dòng 22] } else if (isAndroid == true) {

================================================================================

📁 FILE 119: dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/services/dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 120: digital-wallet.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/services/digital-wallet.service.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 173] element.value == 'true'

================================================================================

📁 FILE 121: fee.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/services/fee.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 122: multiple_method.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/services/multiple_method.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 123: payment.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/services/payment.service.ts
📊 Thống kê: 13 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 11 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 615] return countPayment == maxPayment
  2. [Dòng 653] if (this.getLatestPayment().state == 'canceled')

!= (11 điều kiện):
  1. [Dòng 110] if (idInvoice != null
  2. [Dòng 110] idInvoice != 0)
  3. [Dòng 120] idInvoice != 0) {
  4. [Dòng 297] if (this._merchantid != null
  5. [Dòng 297] this._tranref != null
  6. [Dòng 297] this._state != null
  7. [Dòng 369] let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
  8. [Dòng 409] urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
  9. [Dòng 448] if (paymentId != null) {
  10. [Dòng 545] if (res?.status != 200
  11. [Dòng 545] res?.status != 201) return;

================================================================================

📁 FILE 124: qr.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/services/qr.service.ts
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 58] if (res?.state == 'canceled') {
  2. [Dòng 99] state == 'authorization_required'

!= (3 điều kiện):
  1. [Dòng 40] if (_re.status != '200'
  2. [Dòng 40] _re.status != '201') {
  3. [Dòng 49] latestPayment?.state != "authorization_required") {

================================================================================

📁 FILE 125: time-stop.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/services/time-stop.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 126: token-main.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/services/token-main.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 127: index.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/translate/index.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 128: lang-en.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/translate/lang-en.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 129: lang-vi.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/translate/lang-vi.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 130: translate.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/translate/translate.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 131: translate.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/translate/translate.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 37] if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
  2. [Dòng 38] else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';

================================================================================

📁 FILE 132: translations.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/translate/translations.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 133: apps-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/util/apps-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 651] if (e.name == bankSwift) { // TODO: get by swift
  2. [Dòng 659] return this.apps.find(e => e.code == appCode);

================================================================================

📁 FILE 134: apps-information.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/util/apps-information.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 135: banks-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/util/banks-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1013] if (+e.id == bankId) {
  2. [Dòng 1063] if (e.swiftCode == bankSwift) {

================================================================================

📁 FILE 136: error-handler.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/util/error-handler.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 9] err?.status === 400
  2. [Dòng 10] err?.error?.name === 'INVALID_CARD_FEE'

================================================================================

📁 FILE 137: iso-ca-states.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/util/iso-ca-states.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 138: iso.us-states.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/util/iso.us-states.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 139: util.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/app/util/util.ts
📊 Thống kê: 41 điều kiện duy nhất
   - === : 16 lần
   - == : 18 lần
   - !== : 3 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (16 điều kiện):
  1. [Dòng 69] if (v.length === 2
  2. [Dòng 69] this.flag.length === 3
  3. [Dòng 69] this.flag.charAt(this.flag.length - 1) === '/') {
  4. [Dòng 73] if (v.length === 1) {
  5. [Dòng 75] } else if (v.length === 2) {
  6. [Dòng 78] v.length === 2) {
  7. [Dòng 86] if (len === 2) {
  8. [Dòng 158] if (M[1] === 'Chrome') {
  9. [Dòng 284] if (param === key) {
  10. [Dòng 488] pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
  11. [Dòng 492] target === 0
  12. [Dòng 573] if (cardTypeBank === 'bank_card_number') {
  13. [Dòng 576] } else if (cardTypeBank === 'bank_account_number') {
  14. [Dòng 626] if (event.keyCode === 8
  15. [Dòng 626] event.key === "Backspace"
  16. [Dòng 684] if (target.tagName === 'A'

== (18 điều kiện):
  1. [Dòng 18] if (temp.length == 0) {
  2. [Dòng 25] return (counter % 10 == 0);
  3. [Dòng 39] if (currency == 'USD') {
  4. [Dòng 132] if (this.checkCount == 1) {
  5. [Dòng 144] if (results == null) {
  6. [Dòng 177] if (c.length == 3) {
  7. [Dòng 190] d = d == undefined ? '.' : d
  8. [Dòng 191] t = t == undefined ? '
  9. [Dòng 272] return results == null ? null : results[1]
  10. [Dòng 626] event.inputType == 'deleteContentBackward') {
  11. [Dòng 627] if (event.target.name == 'exp_date'
  12. [Dòng 635] event.inputType == 'insertCompositionText') {
  13. [Dòng 649] if (((_val.length == 4
  14. [Dòng 649] _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  15. [Dòng 649] _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  16. [Dòng 675] iss_date.length == 4
  17. [Dòng 675] iss_date.search('/') == -1)
  18. [Dòng 676] iss_date.length == 5))

!== (3 điều kiện):
  1. [Dòng 279] queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
  2. [Dòng 280] if (queryString !== '') {
  3. [Dòng 492] if (target !== 0

!= (4 điều kiện):
  1. [Dòng 160] if (tem != null) {
  2. [Dòng 165] if ((tem = ua.match(/version\/(\d+)/i)) != null) {
  3. [Dòng 571] if (ua.indexOf('safari') != -1
  4. [Dòng 628] if (v.length != 3) {

================================================================================

📁 FILE 140: apple.js
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/assets/script/apple.js
📊 Thống kê: 15 điều kiện duy nhất
   - === : 0 lần
   - == : 13 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (13 điều kiện):
  1. [Dòng 65] let applepayNapas = document.getElementById('applepay-napas')?.value == 'true'
  2. [Dòng 66] let applepayVisa = document.getElementById('applepay-visa')?.value == 'true'
  3. [Dòng 67] let applepayMasterCard = document.getElementById('applepay-masterCard')?.value == 'true'
  4. [Dòng 68] let applepayJCB = document.getElementById('applepay-jcb')?.value == 'true'
  5. [Dòng 72] if (applepayNapas == true) {
  6. [Dòng 76] if (applepayVisa == true) {
  7. [Dòng 80] if (applepayMasterCard == true) {
  8. [Dòng 84] if (applepayJCB == true) {
  9. [Dòng 139] if(document.getElementById('applepay-merchantAVS').value == 'true'){
  10. [Dòng 191] response.status == '400') {
  11. [Dòng 193] } else if (response.status == '500') {
  12. [Dòng 204] if (data.name == "CARD_TYPE_CAN_NOT_BE_PROCESSED") { // in case response.status == 400
  13. [Dòng 211] } else if (data.state == "approved"){ // in case response.ok

!= (2 điều kiện):
  1. [Dòng 127] if (network != "napas") return true;
  2. [Dòng 128] if (currency != "VND") return false; // napas accept VND only

================================================================================

📁 FILE 141: google-pay-intergrate.js
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/assets/script/google-pay-intergrate.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 142: qrcode.js
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/assets/script/qrcode.js
📊 Thống kê: 67 điều kiện duy nhất
   - === : 2 lần
   - == : 53 lần
   - !== : 0 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 2255] if (typeof define === 'function'
  2. [Dòng 2257] } else if (typeof exports === 'object') {

== (53 điều kiện):
  1. [Dòng 68] if (_dataCache == null) {
  2. [Dòng 85] if ( (0 <= r && r <= 6 && (c == 0
  3. [Dòng 85] c == 6) )
  4. [Dòng 86] || (0 <= c && c <= 6 && (r == 0
  5. [Dòng 86] r == 6) )
  6. [Dòng 107] if (i == 0
  7. [Dòng 122] _modules[r][6] = (r % 2 == 0);
  8. [Dòng 129] _modules[6][c] = (c % 2 == 0);
  9. [Dòng 152] if (r == -2
  10. [Dòng 152] r == 2
  11. [Dòng 152] c == -2
  12. [Dòng 152] c == 2
  13. [Dòng 153] || (r == 0
  14. [Dòng 153] c == 0) ) {
  15. [Dòng 169] ( (bits >> i) & 1) == 1);
  16. [Dòng 226] if (col == 6) col -= 1;
  17. [Dòng 232] if (_modules[row][col - c] == null) {
  18. [Dòng 237] dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
  19. [Dòng 249] if (bitIndex == -1) {
  20. [Dòng 458] margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
  21. [Dòng 498] if (typeof arguments[0] == 'object') {
  22. [Dòng 587] margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
  23. [Dòng 733] if (b == -1) throw 'eof';
  24. [Dòng 741] if (b0 == -1) break;
  25. [Dòng 767] if (typeof b == 'number') {
  26. [Dòng 768] if ( (b & 0xff) == b) {
  27. [Dòng 910] return function(i, j) { return (i + j) % 2 == 0
  28. [Dòng 912] return function(i, j) { return i % 2 == 0
  29. [Dòng 914] return function(i, j) { return j % 3 == 0
  30. [Dòng 916] return function(i, j) { return (i + j) % 3 == 0
  31. [Dòng 918] return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
  32. [Dòng 920] return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
  33. [Dòng 922] return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
  34. [Dòng 924] return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
  35. [Dòng 1011] if (r == 0
  36. [Dòng 1011] c == 0) {
  37. [Dòng 1015] if (dark == qrcode.isDark(row + r, col + c) ) {
  38. [Dòng 1036] if (count == 0
  39. [Dòng 1036] count == 4) {
  40. [Dòng 1149] if (typeof num.length == 'undefined') {
  41. [Dòng 1155] num[offset] == 0) {
  42. [Dòng 1495] if (typeof rsBlock == 'undefined') {
  43. [Dòng 1538] return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
  44. [Dòng 1543] _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
  45. [Dòng 1599] if (data.length - i == 1) {
  46. [Dòng 1601] } else if (data.length - i == 2) {
  47. [Dòng 1866] } else if (n == 62) {
  48. [Dòng 1868] } else if (n == 63) {
  49. [Dòng 1928] if (_buflen == 0) {
  50. [Dòng 1937] if (c == '=') {
  51. [Dòng 1961] } else if (c == 0x2b) {
  52. [Dòng 1963] } else if (c == 0x2f) {
  53. [Dòng 2133] if (table.size() == (1 << bitLength) ) {

!= (12 điều kiện):
  1. [Dòng 119] if (_modules[r][6] != null) {
  2. [Dòng 126] if (_modules[6][c] != null) {
  3. [Dòng 144] if (_modules[row][col] != null) {
  4. [Dòng 365] while (buffer.getLengthInBits() % 8 != 0) {
  5. [Dòng 750] if (count != numChars) {
  6. [Dòng 751] throw count + ' != ' + numChars
  7. [Dòng 878] while (data != 0) {
  8. [Dòng 1733] if (test.length != 2
  9. [Dòng 1733] ( (test[0] << 8) | test[1]) != code) {
  10. [Dòng 1894] if (_length % 3 != 0) {
  11. [Dòng 2067] if ( (data >>> length) != 0) {
  12. [Dòng 2178] return typeof _map[key] != 'undefined'

================================================================================

📁 FILE 143: environment.development.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/environments/environment.development.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 144: environment.mtf.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/environments/environment.mtf.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 145: environment.prod.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/environments/environment.prod.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 146: environment.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/environments/environment.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 147: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 148: karma.conf.js
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/karma.conf.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 149: main.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/main.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 150: polyfills.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/polyfills.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 151: test.ts
📍 Đường dẫn: /root/projects/onepay/paygate-token/src/test.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📋 TÓM TẮT TẤT CẢ ĐIỀU KIỆN DUY NHẤT:
====================================================================================================

=== (176 điều kiện duy nhất):
------------------------------------------------------------
1. return lang === this._translate.currentLang
2. checkDupTran === false"
3. isPopupSupport === 'True') || (rePayment
4. isPopupSupport === 'True'"
5. isPopupSupport === 'True')">
6. checkDupTran === false
7. params.timeout === 'true') {
8. this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
9. _re.body.state === 'unpaid');
10. if (this.errorCode === 'overtime'
11. this.errorCode === '253') {
12. params.name === 'CUSTOMER_INTIME'
13. params.code === '09') {
14. if (this.timeLeft === 0) {
15. if (param === key) {
16. if (YY % 400 === 0
17. YY % 4 === 0)) {
18. if (YYYY % 400 === 0
19. YYYY % 4 === 0)) {
20. params['code']==='09'){
21. <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
22. [class.red_border_no_background]="c_expdate && (valueDate.length === 0
23. valueDate.trim().length === 0)"
24. if (isIE[0] === 'MSIE'
25. +isIE[1] === 10) {
26. if ((_val.value.substr(-1) === ' '
27. _val.value.length === 24) {
28. if (this.cardTypeBank === 'bank_card_number') {
29. } else if (this.cardTypeBank === 'bank_account_number') {
30. } else if (this.cardTypeBank === 'bank_username') {
31. } else if (this.cardTypeBank === 'bank_customer_code') {
32. this.cardTypeBank === 'bank_card_number'
33. if (this.cardTypeOcean === 'IB') {
34. } else if (this.cardTypeOcean === 'MB') {
35. if (_val.value.substr(0, 2) === '84') {
36. } else if (this.cardTypeOcean === 'ATM') {
37. if (this.cardTypeBank === 'bank_account_number') {
38. this.cardTypeBank === 'bank_card_number') {
39. if (this.cardTypeBank === 'bank_card_number'
40. if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
41. if (event.keyCode === 8
42. event.key === "Backspace"
43. if (v.length === 2
44. this.flag.length === 3
45. this.flag.charAt(this.flag.length - 1) === '/') {
46. if (v.length === 1) {
47. } else if (v.length === 2) {
48. v.length === 2) {
49. if (len === 2) {
50. if ((this.cardTypeBank === 'bank_account_number'
51. this.cardTypeBank === 'bank_username'
52. this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
53. this.cardTypeOcean === 'ATM')
54. || (this.cardTypeOcean === 'IB'
55. if (valIn === this._translate.instant('bank_card_number')) {
56. } else if (valIn === this._translate.instant('bank_account_number')) {
57. } else if (valIn === this._translate.instant('bank_username')) {
58. } else if (valIn === this._translate.instant('bank_customer_code')) {
59. if (_val.value === ''
60. _val.value === null
61. _val.value === undefined) {
62. if (_val.value && (this.cardTypeBank === 'bank_card_number'
63. if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
64. this.cardTypeOcean === 'MB') {
65. this.cardTypeOcean === 'IB'
66. if ((this.cardTypeBank === 'bank_card_number'
67. if (this.cardName === undefined
68. this.cardName === '') {
69. if (this.valueDate === undefined
70. this.valueDate === '') {
71. c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
72. _inExpDate.trim().length === 0)"
73. ready===1"
74. if (this.timeLeft === 10) {
75. if (this.runTime === true) {
76. if (this.runTime === true) this.submitCardBanking();
77. if (approval.method === 'REDIRECT') {
78. } else if (approval.method === 'POST_REDIRECT') {
79. if (this.timeLeft === 1) {
80. } else if (valIn === this._translate.instant('internet_banking')) {
81. if (_val.value && (this.cardTypeBank === 'bank_card_number')) {
82. if (focusElement === 'card_name') {
83. } else if (focusElement === 'exp_date'
84. focusExpDateElement === 'card_name') {
85. if (this.cardTypeBank === 'bank_account_number'
86. filteredData.length === 0"
87. if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
88. filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
89. if (valOut === 'auth') {
90. if (this._b === '1'
91. this._b === '20'
92. this._b === '64') {
93. if (this._b === '36'
94. this._b === '18'
95. this._b === '19'
96. if (this._b === '19'
97. this._b === '16'
98. this._b === '25'
99. this._b === '33'
100. this._b === '39'
101. this._b === '11'
102. this._b === '17'
103. this._b === '36'
104. this._b === '44'
105. this._b === '64'
106. if (this._b === '20'
107. if (this._b === '18') {
108. if (_formCard.country === 'default') {
109. if ((v.substr(-1) === ' '
110. this._i_country_code === 'US') {
111. const insertIndex = this._i_country_code === 'US' ? 5 : 3
112. if (temp[i] === '-'
113. temp[i] === ' ') {
114. insertIndex === 3 ? ' ' : itemRemoved)
115. this.c_country = _val.value === 'default'
116. d_vrbank===true"
117. this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_mobile_wallet === 1
118. if (this._res.state === 'unpaid'
119. this._res.state === 'not_paid') {
120. if ('op' === auth
121. } else if ('bank' === auth
122. return id === 'amex' ? '1234' : '123'
123. if (this.timeLeftPaypal === 0) {
124. if (GGPaySDKScript.readyState === "loaded"
125. GGPaySDKScript.readyState === "complete") {
126. isTop = window === window.top
127. return currency === 'VND'
128. filteredData.length === 0
129. filteredDataOther.length === 0"
130. filteredDataMobile.length === 0
131. filteredDataOtherMobile.length === 0"
132. this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
133. this.filteredDataOther = this.appList.filter(item => item.type === 'other');
134. this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
135. this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
136. item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
137. filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
138. if (item.type === 'mobile_banking') {
139. this.appList.length === 1
140. listVNPayQR.length === 0"
141. this.listWalletQR.length === 1) {
142. this.listWalletQR.length === 1
143. if (!this.d_vnpayqr_wallet && !this.d_vnpayqr_deeplink && (this.listWalletQR?.length === 1
144. this.listWalletDeeplink?.length === 1)) {
145. if (item.type === 'deeplink') {
146. this.listWalletQR?.length === 1
147. type === 2"
148. [ngStyle]="{'border-color': type === 2 ? this.themeConfig.border_color : border_color}"
149. *ngIf="((type === 2
150. type === '2'
151. type === 1"
152. [ngStyle]="{'border-color': type === 1 ? this.themeConfig.border_color : border_color}"
153. type === paymentType"
154. [ngStyle]="{'border-color': (type === paymentType
155. type === paymentType
156. type === 3"
157. [ngStyle]="{'border-color': type === 3 ? this.themeConfig.border_color : border_color}"
158. [ngStyle]="{'border-color': type === 4 ? this.themeConfig.border_color : border_color}"
159. type === 4"
160. type === 4
161. item.brand_id === 'visa' ? {'height': '14.42px', 'width': '44.67px'
162. if (event.keyCode === 13) {
163. && ((token.brand_id === 'amex'
164. return csc != null && !isNaN(+csc) && ((brand_id === 'amex'
165. const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
166. item.method === method) : null;
167. err?.status === 400
168. err?.error?.name === 'INVALID_CARD_FEE'
169. if (M[1] === 'Chrome') {
170. pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
171. target === 0
172. if (cardTypeBank === 'bank_card_number') {
173. } else if (cardTypeBank === 'bank_account_number') {
174. if (target.tagName === 'A'
175. if (typeof define === 'function'
176. } else if (typeof exports === 'object') {

== (604 điều kiện duy nhất):
------------------------------------------------------------
1. 'vi' == params['locale']) {
2. 'en' == params['locale']) {
3. errorCode == '11'"
4. *ngIf="(isSent == false
5. isSent == false
6. [class.select_only]="!(isSent == false
7. <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
8. (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
9. *ngIf="!(errorCode == 'overtime'
10. errorCode == '253'
11. if (this.res.currencies[0] == 'USD') {
12. if (this.paymentInformation.type == "applepay_napas") {
13. if (this.paymentInformation.type == "applepay") {
14. } else if (this.paymentInformation.type == "googlepay") {
15. this.res.themes.theme == 'token') {
16. params.response_code == 'overtime') {
17. if (_re.status == '200'
18. _re.status == '201') {
19. if (_re2.status == '200'
20. _re2.status == '201') {
21. if (this.errorCode == 'overtime'
22. this.errorCode == '253') {
23. } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
24. this.res.state == 'canceled') {
25. if (this.errorCode == '24'
26. this.errorName == 'ONECOMM_INVALID_TOKEN_INFO') {
27. if (lastPayment?.state == 'pending') {
28. if (this.isTimePause == false) {
29. if(this.locale == 'vi')
30. if (response.body.state == 'not_paid') {
31. if (response.body.payments[response.body.payments.length - 1].state == "failed") {
32. } else if (response.body.state == 'paid') {
33. } else if (response.body.state == 'canceled'
34. response.body.state == 'closed'
35. response.body.state == 'expired') {
36. if ((dataPassed.status == '200'
37. dataPassed.status == '201') && dataPassed.body != null) {
38. if (this.locale == 'en') {
39. if (name == 'MAFC')
40. if (bankId == 3
41. bankId == 61
42. bankId == 8
43. bankId == 49
44. bankId == 48
45. bankId == 10
46. bankId == 53
47. bankId == 17
48. bankId == 65
49. bankId == 23
50. bankId == 52
51. bankId == 27
52. bankId == 66
53. bankId == 9
54. bankId == 54
55. bankId == 37
56. bankId == 38
57. bankId == 39
58. bankId == 40
59. bankId == 42
60. bankId == 44
61. bankId == 72
62. bankId == 59
63. bankId == 51
64. bankId == 64
65. bankId == 58
66. bankId == 56
67. bankId == 55
68. bankId == 60
69. bankId == 68
70. bankId == 74
71. bankId == 75 //KEB HANA
72. this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
73. if (this._b == 18
74. this._b == 19) {
75. if (this._b == 19) {//19BIDV
76. } else if (this._b == 3
77. this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
78. if (this._b == 27) {
79. } else if (this._b == 12) {// 12SHB
80. } else if (this._b == 18) { //18Oceanbank-ocb
81. if (this._b == 19
82. this._b == 3
83. this._b == 27
84. this._b == 12) {
85. } else if (this._b == 18) {
86. if (this.checkBin(_val.value) && (this._b == 3
87. this._b == 27)) {
88. if (this._b == 3) {
89. this.cardTypeOcean == 'ATM') {
90. if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
91. this._b == 18)) {
92. if (this.checkBin(v) && (this._b == 3
93. event.inputType == 'deleteContentBackward') {
94. if (event.target.name == 'exp_date'
95. event.inputType == 'insertCompositionText') {
96. if (((this.valueDate.length == 4
97. this.valueDate.search('/') == -1) || this.valueDate.length == 5)
98. this.valueDate.length == 5)
99. if (temp.length == 0) {
100. return (counter % 10 == 0);
101. } else if (this._b == 19) {
102. } else if (this._b == 27) {
103. if (this._b == 12) {
104. if (this.cardTypeBank == 'bank_customer_code') {
105. } else if (this.cardTypeBank == 'bank_account_number') {
106. _formCard.exp_date.length == 5
107. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
108. this._b == 3)) {//27-pvcombank;3-TPB ;
109. if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
110. this._b == 19
111. this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
112. if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
113. if (this.cardTypeOcean == 'IB') {
114. } else if (this.cardTypeOcean == 'MB') {
115. } else if (this.cardTypeOcean == 'ATM') {
116. this.token_site == 'onepay'
117. if (this._res_post.state == 'approved'
118. this._res_post.state == 'failed') {
119. } else if (this._res_post.state == 'authorization_required') {
120. if (this._b == 18) {
121. if (this._b == 27
122. this._b == 18) {
123. if (err.status == 400
124. err.status == 500) {
125. if (err.error && (err.error.code == 13
126. err.error.code == '13')) {
127. if ((cardNo.length == 16
128. if ((cardNo.length == 16 || (cardNo.length == 19
129. && ((this._b == 18
130. cardNo.length == 19) || this._b != 18)
131. if (this._b == +e.id) {
132. if (valIn == 1) {
133. } else if (valIn == 2) {
134. this._b == 3) {
135. if (this._b == 19) {
136. if (cardType == this._translate.instant('internetbanking')
137. } else if (cardType == this._translate.instant('mobilebanking')
138. } else if (cardType == this._translate.instant('atm')
139. this._b == 18))) {
140. } else if (this._b == 18
141. this.c_expdate = !(((this.valueDate.length == 4
142. this.valueDate.length == 4
143. this.valueDate.search('/') == -1)
144. this.valueDate.length == 5))
145. (_b != 67 && _b != 2) || ((_b == 67 || _b == 2) && !isOffTechcombankNapas && checkTwoEnabled)
146. <mat-tab [label]="'internet_banking' | translate" *ngIf="_b != 2 || (_b == 2
147. <div [ngStyle]="{'margin-top': !(checkTwoEnabled && !isOffTechcombankNapas && !isOffTechcombank) ? '15px' : '0px' }" *ngIf="(cardTypeBank == 'bank_card_number') &&(_b == 67
148. (cardTypeBank == 'bank_card_number') &&(_b == 67 || checkTwoEnabled)&& ready===1
149. token_site == 'onepay'
150. <div [ngStyle]="{'margin-top': !(checkTwoEnabled && !isOffTechcombankNapas && !isOffTechcombank) ? '15px' : '0px' }" class="nd-bank-card-two" *ngIf="(cardTypeBank == 'internet_banking')&&(_b == 2
151. if (this._b == 67
152. this._b == 2) {//19BIDV
153. if ((this._b == 2
154. !this.checkTwoEnabled) || (this._b == 2
155. } else if (this._b == 2
156. if (this._b == 67) {
157. return this._b == 2
158. this._b == 67
159. _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
160. this.checkMod10(cardNo) == true
161. if (this._b != 68 || (this._b == 68
162. return ((value.length == 4
163. value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
164. value.length == 5) && parseInt(value.split('/')[0]
165. || (this._util.checkValidExpireMonthYear(value) && (this._b == 2
166. this._b == 20
167. this._b == 33
168. this._b == 39
169. this._b == 43
170. this._b == 45
171. this._b == 64
172. this._b == 68
173. this._b == 72))) //sonnh them Vietbank 72
174. this._inExpDate.length == 4
175. this._inExpDate.search('/') == -1)
176. this._inExpDate.length == 5))
177. || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 20
178. this._b == 2
179. this._b == 72)));
180. if (this._b == 8) {//MB Bank
181. if (this._b == 18) {//Oceanbank
182. if (this._b == 8) {
183. if (this._b == 12) { //SHB
184. } else if (this._res.state == 'authorization_required') {
185. if (this.challengeCode == '') {
186. if (this._b == 18) {//8-MB Bank;18-oceanbank
187. <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_card_number')">
188. <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_account_number')">
189. if (this._b == 12) this.isShbGroup = true;
190. return this._b == 9
191. this._b == 11
192. this._b == 16
193. this._b == 17
194. this._b == 25
195. this._b == 44
196. this._b == 57
197. this._b == 59
198. this._b == 61
199. this._b == 63
200. this._b == 69
201. if (this._b == 12
202. this.cardTypeBank == 'bank_account_number') {
203. cardNo.length == 19) && cardNo.startsWith("970443") && this._util.checkMod10(cardNo) && this.checkBin(cardNo)) {
204. if (this._b == 2
205. this._b == 31) {
206. if (this._b == 2) {
207. } else if (this._b == 6) {
208. } else if (this._b == 31) {
209. if (this._b == 5) {//5-vib;
210. if (this._b == 5) {
211. if (this.checkBin(_val.value) && (this._b == 5)) {
212. if (this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
213. if (this.checkBin(v) && (this._b == 5)) {
214. this._b == 5) {//5 vib ;
215. this._b == 5) {//5vib;
216. _b == 68"
217. if (this._b == 1
218. this._b == 36
219. this._b == 55
220. this._b == 47
221. this._b == 48
222. this._b == 59) {
223. return this._b == 11
224. this._b == 32
225. this._b == 72
226. this._b == 73
227. this._b == 74
228. this._b == 75
229. this._b == 14
230. this._b == 15
231. this._b == 24
232. this._b == 8
233. this._b == 10
234. this._b == 22
235. this._b == 23
236. this._b == 30
237. this._b == 9) {
238. (cardNo.length == 19
239. (cardNo.length == 19 && (this._b == 1
240. this._b == 4
241. this._b == 59))
242. this._util.checkMod10(cardNo) == true
243. ((!token && _auth==0 && vietcombankGroupSelected) || (token && bankId==16))
244. bankId==16))">
245. _auth==0 && techcombankGroupSelected
246. _auth==0 && onepaynapasGroupSelected
247. _auth==0 && shbGroupSelected
248. _auth==0 && bankaccountGroupSelected
249. _auth==0 && vibbankGroupSelected
250. (token || _auth==1) && _b != 16
251. this._auth == 0
252. this.tokenList.length == 0) {
253. if (item.b.id == '2'
254. item.b.id == '67') {
255. this.filteredData.length == 1
256. $event == 'true') {
257. _b: this._b == '2' ? '67' : this._b
258. if (bankid == 2
259. bankid == 67) {
260. || (off && !this.enabledTwoBankTech && ((bankid == '2'
261. this.isOffTechcombank) || (bankid == '67'
262. if (bankId == 1
263. bankId == 4
264. bankId == 7
265. bankId == 11
266. bankId == 14
267. bankId == 15
268. bankId == 16
269. bankId == 20
270. bankId == 22
271. bankId == 24
272. bankId == 25
273. bankId == 30
274. bankId == 33
275. bankId == 34
276. bankId == 35
277. bankId == 36
278. bankId == 41
279. bankId == 43
280. bankId == 45
281. bankId == 46
282. bankId == 47
283. bankId == 50
284. bankId == 57
285. bankId == 62
286. bankId == 63
287. bankId == 69
288. bankId == 70
289. bankId == 71
290. bankId == 73
291. bankId == 32
292. bankId == 75) {
293. } else if (bankId == 6
294. bankId == 31
295. bankId == 80) {
296. } else if (bankId == 2
297. bankId == 67) {
298. } else if (bankId == 3
299. bankId == 18
300. bankId == 19
301. bankId == 27) {
302. } else if (bankId == 5) {
303. } else if (bankId == 12) {
304. this._b == '55'
305. this._b == '47'
306. this._b == '48'
307. this._b == '59'
308. this._b == '73'
309. this._b == '12') {
310. this._b == '3'
311. this._b == '43'
312. this._b == '45'
313. this._b == '57'
314. this._b == '61'
315. this._b == '63'
316. this._b == '67'
317. this._b == '68'
318. this._b == '69'
319. this._b == '72'
320. this._b == '9'
321. this._b == '74'
322. this._b == '75') {
323. this._b == '36'
324. if (item['id'] == this._b) {
325. } else if(this._res_post.state == 'failed') {
326. if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
327. } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
328. if (err.error && (err.error.code == 8
329. err.error.code == '8')) {
330. if (this._type == 5
331. this._type == 6) {
332. } else if (this._type == 7
333. this._type == 8) {
334. } else if (err.error && (err.error.code == 13
335. v.length == 15) || (v.length == 16
336. v.length == 19))
337. this._util.checkMod10(v) == true) {
338. cardNo.length == 15)
339. cardNo.length == 16)
340. cardNo.startsWith('81')) && (cardNo.length == 16
341. cardNo.length == 19))
342. this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
343. this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
344. v.length == 5) {
345. v.length == 4
346. v.length == 3)
347. _val.value.length == 4
348. _val.value.length == 3)
349. this.requireAvs = this.AVS_COUNTRIES.find(e => e.code == this._i_country_code) ? true : false;
350. this._i_csc.length == 4) ||
351. this._i_csc.length == 3)
352. country = this.AVS_COUNTRIES.find(e => e.code == res.bin_country);
353. countryCode == 'US' ? US_STATES
354. : countryCode == 'CA' ? CA_STATES
355. method?.trim()=='International'"
356. method.trim()=='ApplePay'"
357. method.trim()=='GooglePay'"
358. method.trim()=='SamsungPay'"
359. method?.trim()=='Domestic'"
360. method?.trim()=='QR'"
361. method?.trim()=='Paypal'"
362. if (el == 5) {
363. } else if (el == 6) {
364. } else if (el == 7) {
365. } else if (el == 8) {
366. } else if (el == 2) {
367. } else if (el == 4) {
368. } else if (el == 3) {
369. if (!isNaN(_re.status) && (_re.status == '200'
370. _re.status == '201') && _re.body != null) {
371. if (('closed' == this._res_polling.state
372. 'canceled' == this._res_polling.state
373. 'expired' == this._res_polling.state)
374. } else if ('paid' == this._res_polling.state) {
375. this._res_polling.payments == null
376. if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'authorization_required') {
377. } else if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
378. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
379. this._paymentService.getCurrentPage() == 'enter_card') {
380. } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
381. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
382. } else if ('not_paid' == this._res_polling.state) {
383. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
384. if (message == '1') {
385. if (this.checkInvoiceState() == 1) {
386. this.version2 = _re.body?.merchant?.qr_version == "2"
387. if (this.type == 5
388. } else if (this.type == 6
389. } else if (this.type == 2
390. } else if (this.type == 7
391. } else if (this.type == 8
392. } else if (this.type == 4
393. } else if (this.type == 3
394. if (this.themeConfig.default_method == 'International'
395. } else if (this.themeConfig.default_method == 'Domestic'
396. } else if (this.themeConfig.default_method == 'QR'
397. } else if (this.themeConfig.default_method == 'Paypal'
398. if (('closed' == this._res.state
399. 'canceled' == this._res.state
400. 'expired' == this._res.state
401. 'paid' == this._res.state)
402. if ((this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_mobile_wallet) == 0
403. this._auth == 0) {
404. if (count == 2
405. if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
406. this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
407. if (this._res.payments[this._res.payments.length - 1].state == 'approved'
408. if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required') {
409. } else if (this._res.payments[this._res.payments.length - 1].state == 'pending') {
410. } else if (this._res.payments[this._res.payments.length - 1].state == 'canceled') {
411. } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
412. this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
413. if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
414. } else if (idBrand == 'atm'
415. if ('paid' == this._res.state) {
416. this._res.merchant.token_site == 'onepay')) {
417. this._res.payments == null) {
418. this._res.payments[this._res.payments.length - 1].state == 'pending') {
419. if (this._res.currencies[0] == 'USD') {
420. if (this.d_inter == 1) {
421. } else if (this.type == 1) {
422. if (item.instrument.issuer.brand.id == 'atm') {
423. } else if (item.instrument.issuer.brand.id == 'visa'
424. item.instrument.issuer.brand.id == 'mastercard') {
425. if (item.instrument.issuer_location == 'd') {
426. } else if (item.instrument.issuer.brand.id == 'amex') {
427. } else if (item.instrument.issuer.brand.id == 'jcb') {
428. uniq.length == 1) {
429. return merchant.token_cvv == true
430. return (merchant.token_name == true)
431. return (merchant.token_email == true)
432. return (merchant.token_phone == true)
433. if (type == 'qrv1') {
434. if (type == 'mobile') {
435. e.type == 'ewallet'
436. e.code == 'momo')) {
437. } else if (type == 'desktop') {
438. e.type == 'vnpayqr') || (regex.test(strTest)
439. _val == 2) {
440. if (_val == 2
441. } else if (_val == 2
442. _val == 2
443. if (this.type == 4) {
444. filteredData.length == 1) {
445. if (data._locale == 'en') {
446. if (event.data?.event_type == 'applepay_network_not_supported') {
447. if (res?.body?.state == 'approved') {
448. } else if (res?.body?.state == 'authorization_required') {
449. paymentType == PaymentType.ApplePay"
450. paymentType == PaymentType.GooglePay"
451. paymentType == PaymentType.SamsungPay"
452. if (this.paymentType == PaymentType.ApplePay) {
453. if (network == 'napas'
454. serviceId: this.environment == 'PRODUCTION'? this.serviceIdProd : this.serviceIdTest
455. screen=='qr'"
456. screen=='confirm_close'"
457. this.themeConfig.deeplink_status == 'Off' ? false : true
458. if (item.available == true) {
459. if (appcode == 'grabpay'
460. appcode == 'momo') {
461. if (type == 2
462. err.error.code == '04') {
463. qr_error == null"
464. e.type == 'vnpayqr') {
465. e.type == 'ewallet') {
466. e.type == 'wallet')) {
467. if (d.b.code == s) {
468. type == 'vnpay'"
469. type == 'bankapp'"
470. type == 'both'"
471. _locale=='vi'"
472. _locale=='en'"
473. _locale == 'vi'"
474. _locale == 'en'"
475. e.type == 'deeplink') {
476. if (e.type == 'ewallet') {
477. this.listWallet.length == 1
478. this.listWallet[0].code == 'momo') {
479. this.checkEWalletDeeplink.length == 0) {
480. arrayWallet.length == 0) return false;
481. if (arrayWallet[i].code == key) {
482. if (this.locale == 'vi') {
483. uniqueTokenBank) || (type == 2
484. type == 2)) || token">
485. *ngIf="(!token) || (type == paymentType)">
486. type == paymentType"
487. data['type'] == 'Visa'
488. data['type'] == 'Master'
489. data['type'] == 'JCB'"
490. data['type'] == 'Visa'"
491. data['type'] == 'Master'"
492. data['type'] == 'Amex'"
493. token_main == '1'"
494. if (message == '0') {
495. item.id == element.id ? element['active'] = true : element['active'] = false
496. if (result == 'success') {
497. if (this.tokenList.length == 0) {
498. } else if (result == 'error') {
499. _val.value.trim().length == 4) || (token.brand_id != 'amex'
500. _val.value.trim().length == 3))
501. id == 'amex' ? this.maxLength = 4 : this.maxLength = 3
502. if (_re.body.state == 'more_info_required') {
503. if (this._res_post.state == 'failed') {
504. } else if (_re.body.state == 'authorization_required') {
505. this._b == 18
506. this._b == 5
507. this._b == 12
508. } else if (_re.body.state == 'failed') {
509. csc.trim().length == 4)
510. csc.trim().length == 3));
511. return ((a.id == id
512. a.code == id) && a.type.includes(type));
513. if (isIphone == true) {
514. } else if (isAndroid == true) {
515. element.value == 'true'
516. return countPayment == maxPayment
517. if (this.getLatestPayment().state == 'canceled')
518. if (res?.state == 'canceled') {
519. state == 'authorization_required'
520. if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
521. else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';
522. if (e.name == bankSwift) { // TODO: get by swift
523. return this.apps.find(e => e.code == appCode);
524. if (+e.id == bankId) {
525. if (e.swiftCode == bankSwift) {
526. if (currency == 'USD') {
527. if (this.checkCount == 1) {
528. if (results == null) {
529. if (c.length == 3) {
530. d = d == undefined ? '.' : d
531. t = t == undefined ? '
532. return results == null ? null : results[1]
533. if (((_val.length == 4
534. _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
535. _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
536. iss_date.length == 4
537. iss_date.search('/') == -1)
538. iss_date.length == 5))
539. let applepayNapas = document.getElementById('applepay-napas')?.value == 'true'
540. let applepayVisa = document.getElementById('applepay-visa')?.value == 'true'
541. let applepayMasterCard = document.getElementById('applepay-masterCard')?.value == 'true'
542. let applepayJCB = document.getElementById('applepay-jcb')?.value == 'true'
543. if (applepayNapas == true) {
544. if (applepayVisa == true) {
545. if (applepayMasterCard == true) {
546. if (applepayJCB == true) {
547. if(document.getElementById('applepay-merchantAVS').value == 'true'){
548. response.status == '400') {
549. } else if (response.status == '500') {
550. if (data.name == "CARD_TYPE_CAN_NOT_BE_PROCESSED") { // in case response.status == 400
551. } else if (data.state == "approved"){ // in case response.ok
552. if (_dataCache == null) {
553. if ( (0 <= r && r <= 6 && (c == 0
554. c == 6) )
555. || (0 <= c && c <= 6 && (r == 0
556. r == 6) )
557. if (i == 0
558. _modules[r][6] = (r % 2 == 0);
559. _modules[6][c] = (c % 2 == 0);
560. if (r == -2
561. r == 2
562. c == -2
563. c == 2
564. || (r == 0
565. c == 0) ) {
566. ( (bits >> i) & 1) == 1);
567. if (col == 6) col -= 1;
568. if (_modules[row][col - c] == null) {
569. dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
570. if (bitIndex == -1) {
571. margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
572. if (typeof arguments[0] == 'object') {
573. margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
574. if (b == -1) throw 'eof';
575. if (b0 == -1) break;
576. if (typeof b == 'number') {
577. if ( (b & 0xff) == b) {
578. return function(i, j) { return (i + j) % 2 == 0
579. return function(i, j) { return i % 2 == 0
580. return function(i, j) { return j % 3 == 0
581. return function(i, j) { return (i + j) % 3 == 0
582. return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
583. return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
584. return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
585. return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
586. if (r == 0
587. c == 0) {
588. if (dark == qrcode.isDark(row + r, col + c) ) {
589. if (count == 0
590. count == 4) {
591. if (typeof num.length == 'undefined') {
592. num[offset] == 0) {
593. if (typeof rsBlock == 'undefined') {
594. return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
595. _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
596. if (data.length - i == 1) {
597. } else if (data.length - i == 2) {
598. } else if (n == 62) {
599. } else if (n == 63) {
600. if (_buflen == 0) {
601. if (c == '=') {
602. } else if (c == 0x2b) {
603. } else if (c == 0x2f) {
604. if (table.size() == (1 << bitLength) ) {

!== (39 điều kiện duy nhất):
------------------------------------------------------------
1. this.lastValue !== $event.target.value)) {
2. queryString = (sourceURL.indexOf("?") !== -1) ? sourceURL.split("?")[1] : "";
3. if (queryString !== "") {
4. if (YY % 400 === 0 || (YY % 100 !== 0
5. if (YYYY % 400 === 0 || (YYYY % 100 !== 0
6. event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
7. key !== '3') {
8. this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
9. codeResponse.toString() !== '0') {
10. cardNo.length !== 0) {
11. if (this.cardTypeBank !== 'bank_card_number') {
12. if (this.cardTypeBank !== 'bank_account_number') {
13. if (this.cardTypeBank !== 'bank_username') {
14. if (this.cardTypeBank !== 'bank_customer_code') {
15. this.lb_card_account !== this._translate.instant('ocb_account')) {
16. this.lb_card_account !== this._translate.instant('ocb_phone')) {
17. this.lb_card_account !== this._translate.instant('ocb_card')) {
18. this._b !== 18) || (this.cardTypeOcean === 'ATM'
19. let _b = this._b !== 67 ? 67 : this._b
20. if (this.cardTypeBank !== 'internet_banking') {
21. this._b !== 18)) {
22. this._b !== 18) || (this._b == 18)) {
23. this.bankList = this.bankList.filter(item => item.b.id !== '67');
24. if (this.tracking.hasOwnProperty(key) && ((key !== '8'
25. !this._showNameOnCard) || (key !== '9'
26. codeResponse.toString() !== '0'){
27. event.inputType !== 'deleteContentBackward') || v.length == 5) {
28. this._i_country_code !== 'US') {
29. itemRemoved !== '') {
30. if (deviceValue !== 'default') {
31. this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
32. this._i_country_code !== 'default'
33. this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
34. this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {
35. if (_val !== 3) {
36. this._util.getElementParams(this.currentUrl, 't_id') !== '') {
37. queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
38. if (queryString !== '') {
39. if (target !== 0

!= (197 điều kiện duy nhất):
------------------------------------------------------------
1. if (params['locale'] != null
2. } else if (params['locale'] != null
3. if (userLang != null
4. if(value!=null){
5. if (message != ''
6. message != null
7. message != undefined) {
8. if (this.url_new_invoice != null) {
9. if (this._idInvoice != null
10. this._idInvoice != 0) {
11. if (this._paymentService.getInvoiceDetail() != null) {
12. dataPassed.body != null) {
13. dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
14. dataPassed.body.themes.border_color != true ? dataPassed.body.themes.border_color : ''
15. if (this._translate.currentLang != language) {
16. } else if (this._b != 18) {
17. if (this.htmlDesc != null
18. if (ua.indexOf('safari') != -1
19. if (_val.value != '') {
20. this.auth_method != null) {
21. if (this.valueDate.length != 3) {
22. if (_formCard.exp_date != null
23. if (this.cardName != null
24. if (this._res_post.links != null
25. this._res_post.links.merchant_return != null
26. this._res_post.links.merchant_return.href != null) {
27. if (this._res_post.authorization != null
28. this._res_post.authorization.links != null
29. this._res_post.authorization.links.approval != null) {
30. this._res_post.links.cancel != null) {
31. this._b != 27
32. this._b != 12
33. this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
34. this._b != 18)
35. if (this._b != 18
36. this._b != 19) {
37. <mat-tab [label]="'bank_card_number' | translate" *ngIf="(_b != 67
38. _b != 2) || ((_b == 67 || _b == 2)
39. _b != 2 || (_b == 2 && !isOffTechcombank && checkTwoEnabled)
40. if (this._inExpDate.length != 3) {
41. if (this._res_post.return_url != null) {
42. let userName = _formCard.name != null ? _formCard.name : ''
43. this._res_post.authorization.links.approval != null
44. this._res_post.authorization.links.approval.href != null) {
45. userName = paramUserName != null ? paramUserName : ''
46. this._b != 3))
47. if (this._b != 68
48. this._b != 2
49. this._b != 20
50. this._b != 33
51. this._b != 39
52. this._b != 43
53. this._b != 45
54. this._b != 64
55. this._b != 67
56. this._b != 68
57. this._b != 72)
58. if (this._res.links != null
59. this._res.links.merchant_return != null
60. this._res.links.merchant_return.href != null) {
61. if (!(_formCard.otp != null
62. if (!(_formCard.password != null
63. if (this._b != 9
64. this._b != 16
65. this._b != 17
66. this._b != 25
67. this._b != 44
68. this._b != 57
69. this._b != 59
70. this._b != 61
71. this._b != 63
72. this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
73. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73'].indexOf(this._b.toString()) != -1) {
74. if (params['locale'] != null) {
75. if ('otp' != this._paymentService.getCurrentPage()) {
76. this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
77. if (!(strInstrument != null
78. if (strInstrument.substring(0, 1) != '^'
79. strInstrument.substr(strInstrument.length - 1) != '$') {
80. if (bankid != null) {
81. } else if (this._res_post.links != null
82. cardNo != null
83. v != null
84. this.c_csc = (!(_val.value != null
85. this._i_csc != null
86. this.requireAvs = this.isAvsCountry = country != undefined
87. this._paymentService.getState() != 'error') {
88. if (this._paymentService.getCurrentPage() != 'otp') {
89. _re.body != null) {
90. this._res_polling.links != null
91. this._res_polling.links.merchant_return != null //
92. } else if (this._res_polling.merchant != null
93. this._res_polling.merchant_invoice_reference != null
94. } else if (this._res_polling.payments != null
95. this._res_polling.links.merchant_return != null//
96. this._res_polling.payments != null
97. if (this._res_polling.payments != null
98. t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
99. this.type.toString().length != 0) {
100. this._res.links != null
101. if (count != 1) {
102. if (this._res.merchant != null
103. this._res.merchant_invoice_reference != null) {
104. if (this._res.merchant.address_details != null) {
105. this._res.links != null//
106. } else if (this._res.payments != null
107. this._res.payments[this._res.payments.length - 1].instrument != null
108. this._res.payments[this._res.payments.length - 1].instrument.issuer != null
109. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
110. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
111. this._res.payments[this._res.payments.length - 1].links != null
112. this._res.payments[this._res.payments.length - 1].links.cancel != null
113. this._res.payments[this._res.payments.length - 1].links.cancel.href != null
114. this._res.payments[this._res.payments.length - 1].links.update != null
115. this._res.payments[this._res.payments.length - 1].links.update.href != null) {
116. this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
117. this._res.payments[this._res.payments.length - 1].authorization != null
118. this._res.payments[this._res.payments.length - 1].authorization.links != null) {
119. this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
120. this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
121. if (this._res.payments[this._res.payments.length - 1].authorization != null
122. this._res.payments[this._res.payments.length - 1].authorization.links != null
123. auth = paramUserName != null ? paramUserName : ''
124. this._res.links.merchant_return != null //
125. } else if (this._res.merchant != null
126. this._res.merchant_invoice_reference != null
127. if (['mastercard', 'visa', 'amex', 'jcb', 'cup', 'card', 'np_card', 'atm'].indexOf(id) != -1) {
128. } else if (['techcombank_account', 'vib_account', 'dongabank_account', 'tpbank_account', 'bidv_account', 'pvcombank_account', 'shb_account', 'oceanbank_online_account'].indexOf(id) != -1) {
129. } else if (['oceanbank_mobile_account'].indexOf(id) != -1) {
130. } else if (['shb_customer_id'].indexOf(id) != -1) {
131. if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1) {
132. e.type != 'ewallet') || (regex.test(strTest)
133. } else if (this._res.payments != null) {
134. if (event.origin != window.origin) return; // chỉ nhận message từ OnePay
135. if (this.paymentType != PaymentType.ApplePay) return;
136. if (this.paymentType != PaymentType.ApplePay) return false;
137. if (network != 'napas'
138. if (appcode != null) {
139. if (_re.status != '200'
140. _re.status != '201')
141. this.qr_error != null"
142. qr_error != null"
143. 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {
144. qr_version2 != 'None'"
145. qr_version2 != 'None'
146. if (this.translate.currentLang != language) {
147. (((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token
148. feeService['atm']['fee'] != 0"
149. feeService['visa_mastercard_d']['fee'] != 0"
150. feeService['pp']['fee'] != 0"
151. feeService['qr']['fee'] != 0"
152. item['feeService']['fee'] != 0
153. item['feeService']['fee'] != 0"
154. this.themeConfig.merchant.id != 'OPTEST' ? this.themeConfig.on_off_bank : ''
155. this._res.merchant.id != 'OPTEST' ? this._res.on_off_bank : ''
156. if (_val.value != null
157. if (_re.body.authorization != null
158. _re.body.authorization.links != null
159. if (_re.body.links != null
160. _re.body.links.cancel != null) {
161. if (_re.body.return_url != null) {
162. } else if (_re.body.links != null
163. _re.body.links.merchant_return != null
164. _re.body.links.merchant_return.href != null) {
165. return csc != null
166. || (brand_id != 'amex'
167. return this._i_name != ''
168. if (idInvoice != null
169. idInvoice != 0)
170. idInvoice != 0) {
171. if (this._merchantid != null
172. this._tranref != null
173. this._state != null
174. let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
175. urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
176. if (paymentId != null) {
177. if (res?.status != 200
178. res?.status != 201) return;
179. _re.status != '201') {
180. latestPayment?.state != "authorization_required") {
181. if (tem != null) {
182. if ((tem = ua.match(/version\/(\d+)/i)) != null) {
183. if (v.length != 3) {
184. if (network != "napas") return true;
185. if (currency != "VND") return false; // napas accept VND only
186. if (_modules[r][6] != null) {
187. if (_modules[6][c] != null) {
188. if (_modules[row][col] != null) {
189. while (buffer.getLengthInBits() % 8 != 0) {
190. if (count != numChars) {
191. throw count + ' != ' + numChars
192. while (data != 0) {
193. if (test.length != 2
194. ( (test[0] << 8) | test[1]) != code) {
195. if (_length % 3 != 0) {
196. if ( (data >>> length) != 0) {
197. return typeof _map[key] != 'undefined'

