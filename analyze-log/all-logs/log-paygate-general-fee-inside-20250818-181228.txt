====================================================================================================
TRÍCH XUẤT ĐIỀU KIỆN SO SÁNH TỪ CÁC FILE TYPESCRIPT/JAVASCRIPT/HTML
====================================================================================================
Thư mục/File nguồn: /root/projects/onepay/paygate-general-fee-inside/src
Thời gian: 18:12:28 18/8/2025
Tổng số file xử lý: 191
Tổng số file bị bỏ qua: 48
Tổng số điều kiện tìm thấy: 3698

THỐNG KÊ TỔNG QUAN THEO LOẠI TOÁN TỬ:
--------------------------------------------------
Strict equality (===): 1918 lần
Loose equality (==): 800 lần
Strict inequality (!==): 679 lần
Loose inequality (!=): 301 lần
--------------------------------------------------

DANH SÁCH FILE ĐÃ XỬ LÝ:
--------------------------------------------------
1. 4en.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/4en.html
2. 4vn.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/4vn.html
3. app-routing.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/app-routing.module.ts
4. app.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/app.component.html
5. app.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/app.component.spec.ts
6. app.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/app.component.ts
7. app.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/app.module.ts
8. counter.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/counter.directive.spec.ts
9. counter.directive.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/counter.directive.ts
10. format-carno-input.derective.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/directives/format-carno-input.derective.ts
11. uppercase-input.directive.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/directives/uppercase-input.directive.ts
12. error.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/error/error.component.html
13. error.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/error/error.component.spec.ts
14. error.component.ts (20 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/error/error.component.ts
15. format-date.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/format-date.directive.spec.ts
16. format-date.directive.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/format-date.directive.ts
17. main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/main.component.html
18. main.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/main.component.spec.ts
19. main.component.ts (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/main.component.ts
20. app-result.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/app-result/app-result.component.html
21. app-result.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/app-result/app-result.component.ts
22. bank-amount-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
23. bank-amount-dialog.component.ts (34 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
24. cancel-dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/cancel-dialog-guide-dialog.html
25. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/confirm-dialog/dialog-guide-dialog.html
26. policy-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.html
27. policy-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.ts
28. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/dialog-guide-dialog.html
29. bankaccount.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
30. bankaccount.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
31. bankaccount.component.ts (156 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
32. bank.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/model/bank.ts
33. onepay-napas.component.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.html
34. onepay-napas.component.ts (103 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.ts
35. otp-auth.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
36. otp-auth.component.ts (19 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
37. shb.component.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/shb/shb.component.html
38. shb.component.ts (66 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/shb/shb.component.ts
39. techcombank.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
40. techcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
41. techcombank.component.ts (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
42. vibbank.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.html
43. vibbank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.spec.ts
44. vibbank.component.ts (96 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.ts
45. vietcombank.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
46. vietcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
47. vietcombank.component.ts (94 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
48. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
49. off-bank-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
50. off-bank-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
51. domescard-main.component.html (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/domescard-main.component.html
52. domescard-main.component.ts (125 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/domescard-main.component.ts
53. dialog-guide-dialog.html (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/intercard-main/dialog-guide-dialog.html
54. intercard-main.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/intercard-main/intercard-main.component.html
55. intercard-main.component.ts (88 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/intercard-main/intercard-main.component.ts
56. menu.component.html (24 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/menu.component.html
57. menu.component.ts (151 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/menu.component.ts
58. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
59. qr-dialog.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
60. qr-dialog.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
61. qr-main.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main/qr-main.component.html
62. qr-main.component.ts (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main/qr-main.component.ts
63. safe-html.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main/safe-html.pipe.ts
64. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/dialog/dialog-guide-dialog.html
65. qr-desktop.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.html
66. qr-desktop.component.ts (19 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.ts
67. qr-dialog-version2.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog-version2.html
68. qr-dialog-version2.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog-version2.ts
69. qr-guide-dialog-version2.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog-version2.html
70. qr-guide-dialog-version2.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog-version2.ts
71. list-bank-dialog-version2.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog-version2.html
72. list-bank-dialog-version2.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog-version2.ts
73. qr-main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-main.component.html
74. qr-main.component.ts (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-main.component.ts
75. qr-mobile.component.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.html
76. qr-mobile.component.ts (30 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.ts
77. safe-html.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/safe-html.pipe.ts
78. queuing.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/queuing/queuing.component.html
79. queuing.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/queuing/queuing.component.ts
80. token-expired-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/token-expired-dialog/token-expired-dialog.component.html
81. token-expired-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/token-expired-dialog/token-expired-dialog.component.ts
82. remove-token-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/token-main/remove-token-dialog/remove-token-dialog.html
83. dialog-guide-dialog.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/token-main/token-dialog/dialog-guide-dialog.html
84. token-main.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/token-main/token-main.component.html
85. token-main.component.ts (68 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/token-main/token-main.component.ts
86. overlay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/overlay/overlay.component.html
87. overlay.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/overlay/overlay.component.spec.ts
88. overlay.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/overlay/overlay.component.ts
89. bank-amount.pipe.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/pipe/bank-amount.pipe.ts
90. close-dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/services/close-dialog.service.ts
91. data.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/services/data.service.ts
92. deep_link.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/services/deep_link.service.ts
93. dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/services/dialog.service.ts
94. fee.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/services/fee.service.ts
95. multiple_method.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/services/multiple_method.service.ts
96. payment.service.ts (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/services/payment.service.ts
97. qr.service.ts (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/services/qr.service.ts
98. time-stop.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/services/time-stop.service.ts
99. token-main.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/services/token-main.service.ts
100. index.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/translate/index.ts
101. lang-en.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/translate/lang-en.ts
102. lang-vi.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/translate/lang-vi.ts
103. translate.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/translate/translate.pipe.ts
104. translate.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/translate/translate.service.ts
105. translations.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/translate/translations.ts
106. apps-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/util/apps-info.ts
107. apps-information.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/util/apps-information.ts
108. banks-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/util/banks-info.ts
109. error-handler.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/util/error-handler.ts
110. iso-ca-states.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/util/iso-ca-states.ts
111. iso-us-states.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/util/iso-us-states.ts
112. util.ts (41 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/util/util.ts
113. qrcode.js (67 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/assets/script/qrcode.js
114. environment.dev.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/environments/environment.dev.ts
115. environment.mtf.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/environments/environment.mtf.ts
116. environment.prod.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/environments/environment.prod.ts
117. environment.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/environments/environment.ts
118. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/acc_bidv/index.html
119. script.js (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/acc_bidv/script.js
120. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/acc_oceanbank/index.html
121. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/acc_oceanbank/script.js
122. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/acc_pvcombank/index.html
123. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/acc_pvcombank/script.js
124. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/acc_shb/index.html
125. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/acc_shb/script.js
126. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/acc_tpbank/index.html
127. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/acc_tpbank/script.js
128. common.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/common.js
129. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/domestic_card/index.html
130. script.js (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/domestic_card/script.js
131. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/domestic_token/index.html
132. script.js (25 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/domestic_token/script.js
133. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/index.html
134. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/international_card/index.html
135. script.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/international_card/script.js
136. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/international_token/index.html
137. script.js (36 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/international_token/script.js
138. script.js (54 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/script.js
139. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.js
140. bidv.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Bidv/assets/js/bidv.js
141. bidv2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Bidv/bidv2.js
142. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Bidv/index.html
143. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.js
144. ocean.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Ocean/assets/js/ocean.js
145. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Ocean/index.html
146. ocean2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Ocean/ocean2.js
147. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
148. pvbank.js (23 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Pv_Bank/assets/js/pvbank.js
149. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Pv_Bank/index.html
150. pvbank2.js (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Pv_Bank/pvbank2.js
151. Sea2.js (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/SeaBank/Sea2.js
152. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
153. Sea.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/SeaBank/assets/js/Sea.js
154. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/SeaBank/index.html
155. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.js
156. shb.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Shb/assets/js/shb.js
157. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Shb/index.html
158. shb2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Shb/shb2.js
159. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
160. tpbank.js (23 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Tp_Bank/assets/js/tpbank.js
161. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Tp_Bank/index.html
162. tpbank2.js (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Tp_Bank/tpbank2.js
163. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.js
164. onepay.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Visa/assets/js/onepay.js
165. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Visa/index.html
166. index.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Visa/index.js
167. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
168. vpbank.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Vp_Bank/assets/js/vpbank.js
169. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Vp_Bank/index.html
170. vpbank2.js (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Vp_Bank/vpbank2.js
171. sha.js (49 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/assets/js/sha.js
172. sha256.js (28 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/assets/js/sha256.js
173. slick.js (182 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/assets/libraries/slick/slick.js
174. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.js
175. atm_b1.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_1/assets/js/atm_b1.js
176. atm_b1_2.js (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_1/atm_b1_2.js
177. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_1/index.html
178. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.js
179. atm_b2.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_2/assets/js/atm_b2.js
180. atm_b2_2.js (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_2/atm_b2_2.js
181. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_2/index.html
182. common.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/common.js
183. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.js
184. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/international_card/index.html
185. script.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/international_card/script.js
186. index.html (35 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/index.html
187. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/index.html
188. karma.conf.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/karma.conf.js
189. main.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/main.ts
190. polyfills.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/polyfills.ts
191. test.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/test.ts

DANH SÁCH FILE BỊ BỎ QUA:
--------------------------------------------------
1. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/bootstrap.min.js
2. jquery-3.0.0.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/jquery-3.0.0.min.js
3. popper.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/popper.min.js
4. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
5. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
6. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
7. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Bidv/assets/js/jquery-3.4.1.min.js
8. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
9. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
10. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
11. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Ocean/assets/js/jquery-3.4.1.min.js
12. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
13. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
14. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
15. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Pv_Bank/assets/js/jquery-3.4.1.min.js
16. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
17. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
18. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
19. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/SeaBank/assets/js/jquery-3.4.1.min.js
20. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
21. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
22. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
23. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Shb/assets/js/jquery-3.4.1.min.js
24. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
25. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
26. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
27. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Tp_Bank/assets/js/jquery-3.4.1.min.js
28. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
29. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
30. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
31. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Visa/assets/js/jquery-3.4.1.min.js
32. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
33. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
34. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
35. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Vp_Bank/assets/js/jquery-3.4.1.min.js
36. instafeed.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/assets/js/instafeed.min.js
37. slick.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/assets/libraries/slick/slick.min.js
38. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
39. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
40. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
41. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_1/assets/js/jquery-3.4.1.min.js
42. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
43. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
44. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
45. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_2/assets/js/jquery-3.4.1.min.js
46. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
47. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
48. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js

====================================================================================================
CHI TIẾT TỪNG FILE:
====================================================================================================

📁 FILE 1: 4en.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/4en.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 2: 4vn.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/4vn.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 3: app-routing.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/app-routing.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 4: app.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/app.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 5: app.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/app.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 6: app.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/app.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 76] return lang === this._translate.currentLang

== (2 điều kiện):
  1. [Dòng 55] 'vi' == params['locale']) {
  2. [Dòng 57] 'en' == params['locale']) {

!= (3 điều kiện):
  1. [Dòng 55] if (params['locale'] != null
  2. [Dòng 57] } else if (params['locale'] != null
  3. [Dòng 64] if (userLang != null

================================================================================

📁 FILE 7: app.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/app.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 8: counter.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/counter.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 9: counter.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/counter.directive.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 10: format-carno-input.derective.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/directives/format-carno-input.derective.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 44] this.lastValue !== $event.target.value)) {

!= (1 điều kiện):
  1. [Dòng 23] if(value!=null){

================================================================================

📁 FILE 11: uppercase-input.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/directives/uppercase-input.directive.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 0 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 23] this.lastValue !== $event.target.value)) {

================================================================================

📁 FILE 12: error.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/error/error.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 5 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (5 điều kiện):
  1. [Dòng 3] errorCode == '11'"
  2. [Dòng 75] <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
  3. [Dòng 75] (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
  4. [Dòng 77] <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
  5. [Dòng 77] !(errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending

================================================================================

📁 FILE 13: error.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/error/error.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 14: error.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/error/error.component.ts
📊 Thống kê: 20 điều kiện duy nhất
   - === : 5 lần
   - == : 12 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 95] params.timeout === 'true') {
  2. [Dòng 114] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  3. [Dòng 114] _re.body.state === 'unpaid');
  4. [Dòng 179] if (this.errorCode === 'overtime'
  5. [Dòng 179] this.errorCode === '253') {

== (12 điều kiện):
  1. [Dòng 115] _re.body.state == 'closed') this.rePayment = false;
  2. [Dòng 122] if (_re.body.currencies[0] == 'USD') {
  3. [Dòng 160] params.response_code == 'overtime') {
  4. [Dòng 201] if (_re.status == '200'
  5. [Dòng 201] _re.status == '201') {
  6. [Dòng 214] if (_re2.status == '200'
  7. [Dòng 214] _re2.status == '201') {
  8. [Dòng 241] if (this.errorCode == 'overtime'
  9. [Dòng 241] this.errorCode == '253') {
  10. [Dòng 244] } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
  11. [Dòng 250] bodyData.state == 'canceled') { // kiểm tra điều kiện như bên general
  12. [Dòng 286] if (lastPayment?.state == 'pending') {

!= (3 điều kiện):
  1. [Dòng 299] if (message != ''
  2. [Dòng 299] message != null
  3. [Dòng 299] message != undefined) {

================================================================================

📁 FILE 15: format-date.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/format-date.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 16: format-date.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/format-date.directive.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0
  2. [Dòng 123] YY % 4 === 0)) {
  3. [Dòng 138] if (YYYY % 400 === 0
  4. [Dòng 138] YYYY % 4 === 0)) {

!== (2 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0 || (YY % 100 !== 0
  2. [Dòng 138] if (YYYY % 400 === 0 || (YYYY % 100 !== 0

================================================================================

📁 FILE 17: main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 18: main.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/main.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 19: main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/main.component.ts
📊 Thống kê: 9 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 7 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 91] if ((dataPassed.status == '200'
  2. [Dòng 91] dataPassed.status == '201') && dataPassed.body != null) {

!= (7 điều kiện):
  1. [Dòng 82] if (this._idInvoice != null
  2. [Dòng 82] this._idInvoice != 0) {
  3. [Dòng 83] if (this._paymentService.getInvoiceDetail() != null) {
  4. [Dòng 91] dataPassed.body != null) {
  5. [Dòng 112] dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
  6. [Dòng 113] dataPassed.body.themes.border_color != true ? dataPassed.body.themes.border_color : ''
  7. [Dòng 163] if (this._translate.currentLang != language) {

================================================================================

📁 FILE 20: app-result.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/app-result/app-result.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 21: app-result.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/app-result/app-result.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 22: bank-amount-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 23: bank-amount-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
📊 Thống kê: 34 điều kiện duy nhất
   - === : 0 lần
   - == : 34 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (34 điều kiện):
  1. [Dòng 41] if (this.locale == 'en') {
  2. [Dòng 55] if (name == 'MAFC')
  3. [Dòng 61] if (bankId == 3
  4. [Dòng 61] bankId == 61
  5. [Dòng 62] bankId == 8
  6. [Dòng 62] bankId == 49
  7. [Dòng 63] bankId == 48
  8. [Dòng 64] bankId == 10
  9. [Dòng 64] bankId == 53
  10. [Dòng 65] bankId == 17
  11. [Dòng 65] bankId == 65
  12. [Dòng 66] bankId == 23
  13. [Dòng 66] bankId == 52
  14. [Dòng 67] bankId == 27
  15. [Dòng 67] bankId == 66
  16. [Dòng 68] bankId == 9
  17. [Dòng 68] bankId == 54
  18. [Dòng 69] bankId == 37
  19. [Dòng 70] bankId == 38
  20. [Dòng 71] bankId == 39
  21. [Dòng 72] bankId == 40
  22. [Dòng 73] bankId == 42
  23. [Dòng 74] bankId == 44
  24. [Dòng 75] bankId == 72
  25. [Dòng 76] bankId == 59
  26. [Dòng 79] bankId == 51
  27. [Dòng 80] bankId == 64
  28. [Dòng 81] bankId == 58
  29. [Dòng 82] bankId == 56
  30. [Dòng 85] bankId == 55
  31. [Dòng 86] bankId == 60
  32. [Dòng 87] bankId == 68
  33. [Dòng 88] bankId == 74
  34. [Dòng 89] bankId == 75

================================================================================

📁 FILE 24: cancel-dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/cancel-dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 25: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/confirm-dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 26: policy-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 27: policy-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 28: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 29: bankaccount.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 3 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 40] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 55] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 55] valueDate.trim().length === 0)"

== (1 điều kiện):
  1. [Dòng 91] token_site == 'onepay'

================================================================================

📁 FILE 30: bankaccount.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 31: bankaccount.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
📊 Thống kê: 156 điều kiện duy nhất
   - === : 47 lần
   - == : 75 lần
   - !== : 13 lần
   - != : 21 lần
--------------------------------------------------------------------------------

=== (47 điều kiện):
  1. [Dòng 128] if (isIE[0] === 'MSIE'
  2. [Dòng 128] +isIE[1] === 10) {
  3. [Dòng 212] if ((_val.value.substr(-1) === ' '
  4. [Dòng 212] _val.value.length === 24) {
  5. [Dòng 222] if (this.cardTypeBank === 'bank_card_number') {
  6. [Dòng 227] } else if (this.cardTypeBank === 'bank_account_number') {
  7. [Dòng 233] } else if (this.cardTypeBank === 'bank_username') {
  8. [Dòng 237] } else if (this.cardTypeBank === 'bank_customer_code') {
  9. [Dòng 243] this.cardTypeBank === 'bank_card_number'
  10. [Dòng 257] if (this.cardTypeOcean === 'IB') {
  11. [Dòng 261] } else if (this.cardTypeOcean === 'MB') {
  12. [Dòng 262] if (_val.value.substr(0, 2) === '84') {
  13. [Dòng 269] } else if (this.cardTypeOcean === 'ATM') {
  14. [Dòng 296] if (this.cardTypeBank === 'bank_account_number') {
  15. [Dòng 315] this.cardTypeBank === 'bank_card_number') {
  16. [Dòng 337] if (this.cardTypeBank === 'bank_card_number'
  17. [Dòng 337] if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  18. [Dòng 658] if (event.keyCode === 8
  19. [Dòng 658] event.key === "Backspace"
  20. [Dòng 698] if (v.length === 2
  21. [Dòng 698] this.flag.length === 3
  22. [Dòng 698] this.flag.charAt(this.flag.length - 1) === '/') {
  23. [Dòng 702] if (v.length === 1) {
  24. [Dòng 704] } else if (v.length === 2) {
  25. [Dòng 707] v.length === 2) {
  26. [Dòng 715] if (len === 2) {
  27. [Dòng 992] if ((this.cardTypeBank === 'bank_account_number'
  28. [Dòng 992] this.cardTypeBank === 'bank_username'
  29. [Dòng 992] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  30. [Dòng 997] this.cardTypeOcean === 'ATM')
  31. [Dòng 998] || (this.cardTypeOcean === 'IB'
  32. [Dòng 1057] if (valIn === this._translate.instant('bank_card_number')) {
  33. [Dòng 1082] } else if (valIn === this._translate.instant('bank_account_number')) {
  34. [Dòng 1101] } else if (valIn === this._translate.instant('bank_username')) {
  35. [Dòng 1117] } else if (valIn === this._translate.instant('bank_customer_code')) {
  36. [Dòng 1205] if (_val.value === ''
  37. [Dòng 1205] _val.value === null
  38. [Dòng 1205] _val.value === undefined) {
  39. [Dòng 1216] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  40. [Dòng 1216] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  41. [Dòng 1223] this.cardTypeOcean === 'MB') {
  42. [Dòng 1231] this.cardTypeOcean === 'IB'
  43. [Dòng 1237] if ((this.cardTypeBank === 'bank_card_number'
  44. [Dòng 1271] if (this.cardName === undefined
  45. [Dòng 1271] this.cardName === '') {
  46. [Dòng 1279] if (this.valueDate === undefined
  47. [Dòng 1279] this.valueDate === '') {

== (75 điều kiện):
  1. [Dòng 119] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 142] if (this._b == 18
  3. [Dòng 142] this._b == 19) {
  4. [Dòng 145] if (this._b == 19) {//19BIDV
  5. [Dòng 153] } else if (this._b == 3
  6. [Dòng 153] this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  7. [Dòng 158] if (this._b == 27) {
  8. [Dòng 163] } else if (this._b == 12) {// 12SHB
  9. [Dòng 168] } else if (this._b == 18) { //18Oceanbank-ocb
  10. [Dòng 221] if (this._b == 19
  11. [Dòng 221] this._b == 3
  12. [Dòng 221] this._b == 27
  13. [Dòng 221] this._b == 12) {
  14. [Dòng 256] } else if (this._b == 18) {
  15. [Dòng 287] if (this.checkBin(_val.value) && (this._b == 3
  16. [Dòng 287] this._b == 27)) {
  17. [Dòng 292] if (this._b == 3) {
  18. [Dòng 304] this.cardTypeOcean == 'ATM') {
  19. [Dòng 317] if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  20. [Dòng 337] this._b == 18)) {
  21. [Dòng 413] if (this.checkBin(v) && (this._b == 3
  22. [Dòng 658] event.inputType == 'deleteContentBackward') {
  23. [Dòng 659] if (event.target.name == 'exp_date'
  24. [Dòng 667] event.inputType == 'insertCompositionText') {
  25. [Dòng 682] if (((this.valueDate.length == 4
  26. [Dòng 682] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  27. [Dòng 682] this.valueDate.length == 5)
  28. [Dòng 762] if (temp.length == 0) {
  29. [Dòng 769] return (counter % 10 == 0);
  30. [Dòng 789] } else if (this._b == 19) {
  31. [Dòng 791] } else if (this._b == 27) {
  32. [Dòng 796] if (this._b == 12) {
  33. [Dòng 798] if (this.cardTypeBank == 'bank_customer_code') {
  34. [Dòng 800] } else if (this.cardTypeBank == 'bank_account_number') {
  35. [Dòng 817] _formCard.exp_date.length == 5
  36. [Dòng 817] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
  37. [Dòng 817] this._b == 3)) {//27-pvcombank;3-TPB ;
  38. [Dòng 822] if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
  39. [Dòng 822] this._b == 19
  40. [Dòng 822] this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
  41. [Dòng 825] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  42. [Dòng 828] if (this.cardTypeOcean == 'IB') {
  43. [Dòng 830] } else if (this.cardTypeOcean == 'MB') {
  44. [Dòng 832] } else if (this.cardTypeOcean == 'ATM') {
  45. [Dòng 862] this.token_site == 'onepay'
  46. [Dòng 881] if (_re.status == '200'
  47. [Dòng 881] _re.status == '201') {
  48. [Dòng 886] if (this._res_post.state == 'approved'
  49. [Dòng 886] this._res_post.state == 'failed') {
  50. [Dòng 893] } else if (this._res_post.state == 'authorization_required') {
  51. [Dòng 911] if (this._b == 18) {
  52. [Dòng 916] if (this._b == 27
  53. [Dòng 916] this._b == 18) {
  54. [Dòng 979] if (err.status == 400
  55. [Dòng 979] err.status == 500) {
  56. [Dòng 980] if (err.error && (err.error.code == 13
  57. [Dòng 980] err.error.code == '13')) {
  58. [Dòng 1012] if ((cardNo.length == 16
  59. [Dòng 1012] if ((cardNo.length == 16 || (cardNo.length == 19
  60. [Dòng 1013] && ((this._b == 18
  61. [Dòng 1013] cardNo.length == 19) || this._b != 18)
  62. [Dòng 1026] if (this._b == +e.id) {
  63. [Dòng 1042] if (valIn == 1) {
  64. [Dòng 1044] } else if (valIn == 2) {
  65. [Dòng 1068] this._b == 3) {
  66. [Dòng 1075] if (this._b == 19) {
  67. [Dòng 1138] if (cardType == this._translate.instant('internetbanking')
  68. [Dòng 1146] } else if (cardType == this._translate.instant('mobilebanking')
  69. [Dòng 1154] } else if (cardType == this._translate.instant('atm')
  70. [Dòng 1216] this._b == 18))) {
  71. [Dòng 1223] } else if (this._b == 18
  72. [Dòng 1249] this.c_expdate = !(((this.valueDate.length == 4
  73. [Dòng 1282] this.valueDate.length == 4
  74. [Dòng 1282] this.valueDate.search('/') == -1)
  75. [Dòng 1283] this.valueDate.length == 5))

!== (13 điều kiện):
  1. [Dòng 212] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 867] key !== '3') {
  3. [Dòng 917] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 935] codeResponse.toString() !== '0') {
  5. [Dòng 992] cardNo.length !== 0) {
  6. [Dòng 1064] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 1085] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 1106] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 1126] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 1138] this.lb_card_account !== this._translate.instant('ocb_account')) {
  11. [Dòng 1146] this.lb_card_account !== this._translate.instant('ocb_phone')) {
  12. [Dòng 1154] this.lb_card_account !== this._translate.instant('ocb_card')) {
  13. [Dòng 1237] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (21 điều kiện):
  1. [Dòng 173] } else if (this._b != 18) {
  2. [Dòng 179] if (this.htmlDesc != null
  3. [Dòng 209] if (ua.indexOf('safari') != -1
  4. [Dòng 219] if (_val.value != '') {
  5. [Dòng 305] this.auth_method != null) {
  6. [Dòng 660] if (this.valueDate.length != 3) {
  7. [Dòng 817] if (_formCard.exp_date != null
  8. [Dòng 822] if (this.cardName != null
  9. [Dòng 889] if (this._res_post.links != null
  10. [Dòng 889] this._res_post.links.merchant_return != null
  11. [Dòng 889] this._res_post.links.merchant_return.href != null) {
  12. [Dòng 897] if (this._res_post.authorization != null
  13. [Dòng 897] this._res_post.authorization.links != null
  14. [Dòng 897] this._res_post.authorization.links.approval != null) {
  15. [Dòng 904] this._res_post.links.cancel != null) {
  16. [Dòng 1012] this._b != 27
  17. [Dòng 1012] this._b != 12
  18. [Dòng 1012] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  19. [Dòng 1013] this._b != 18)
  20. [Dòng 1059] if (this._b != 18
  21. [Dòng 1059] this._b != 19) {

================================================================================

📁 FILE 32: bank.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/model/bank.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 33: onepay-napas.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 3 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 27] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 42] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  3. [Dòng 42] _inExpDate.trim().length === 0)"

== (3 điều kiện):
  1. [Dòng 24] (cardTypeBank == 'bank_card_number') && (_b == 67 || (checkTwoEnabled && techcombankCard)) && !isOffTechcombankNapas
  2. [Dòng 68] (cardTypeBank == 'bank_card_number') &&(_b == 67 || (checkTwoEnabled && techcombankCard))&& ready===1 && !isOffTechcombankNapas
  3. [Dòng 85] (cardTypeBank == 'internet_banking')&&(_b == 2 || (checkTwoEnabled && !techcombankCard))

================================================================================

📁 FILE 34: onepay-napas.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.ts
📊 Thống kê: 103 điều kiện duy nhất
   - === : 21 lần
   - == : 47 lần
   - !== : 4 lần
   - != : 31 lần
--------------------------------------------------------------------------------

=== (21 điều kiện):
  1. [Dòng 105] if (isIE[0] === 'MSIE'
  2. [Dòng 105] +isIE[1] === 10) {
  3. [Dòng 128] if (this.cardListTech === "op") {
  4. [Dòng 135] if (this.timeLeft === 10) {
  5. [Dòng 139] if (this.runTime === true) {
  6. [Dòng 145] if (this.timeLeft === 0) {
  7. [Dòng 147] if (this.runTime === true) this.submitCardBanking();
  8. [Dòng 335] if (event.keyCode === 8
  9. [Dòng 335] event.key === "Backspace"
  10. [Dòng 548] if (approval.method === 'REDIRECT') {
  11. [Dòng 551] } else if (approval.method === 'POST_REDIRECT') {
  12. [Dòng 630] if (valIn === this._translate.instant('bank_card_number')) {
  13. [Dòng 632] if (this.timeLeft === 1) {
  14. [Dòng 649] } else if (valIn === this._translate.instant('internet_banking')) {
  15. [Dòng 729] if (_val.value === ''
  16. [Dòng 729] _val.value === null
  17. [Dòng 729] _val.value === undefined) {
  18. [Dòng 740] if (_val.value && (this.cardTypeBank === 'bank_card_number')) {
  19. [Dòng 750] if ((this.cardTypeBank === 'bank_card_number'
  20. [Dòng 788] if (this.cardName === undefined
  21. [Dòng 788] this.cardName === '') {

== (47 điều kiện):
  1. [Dòng 121] if (this._b == 67
  2. [Dòng 121] this._b == 2) {//19BIDV
  3. [Dòng 131] if ((this._b == 2
  4. [Dòng 131] !this.checkTwoEnabled) || (this._b == 2
  5. [Dòng 132] this._b == 2
  6. [Dòng 156] } else if (this._b == 2
  7. [Dòng 160] if (this._b == 67) {
  8. [Dòng 199] if (_re.status == '200'
  9. [Dòng 199] _re.status == '201') {
  10. [Dòng 204] if (this._res_post.state == 'approved'
  11. [Dòng 204] this._res_post.state == 'failed') {
  12. [Dòng 208] } else if (this._res_post.state == 'authorization_required') {
  13. [Dòng 320] return this._b == 2
  14. [Dòng 320] this._b == 67
  15. [Dòng 335] event.inputType == 'deleteContentBackward') {
  16. [Dòng 336] if (event.target.name == 'exp_date'
  17. [Dòng 344] event.inputType == 'insertCompositionText') {
  18. [Dòng 406] if (temp.length == 0) {
  19. [Dòng 413] return (counter % 10 == 0);
  20. [Dòng 429] _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
  21. [Dòng 579] if (err.status == 400
  22. [Dòng 579] err.error['name'] == 'INVALID_INPUT_BIN') {
  23. [Dòng 593] if ((cardNo.length == 16
  24. [Dòng 593] if ((cardNo.length == 16 || (cardNo.length == 19
  25. [Dòng 594] this.checkMod10(cardNo) == true
  26. [Dòng 607] if (this._b == +e.id) {
  27. [Dòng 683] if (this._b == 19) {
  28. [Dòng 687] if (this._b == 27
  29. [Dòng 687] this._b == 3) {
  30. [Dòng 762] if (this._b != 68 || (this._b == 68
  31. [Dòng 771] return ((value.length == 4
  32. [Dòng 771] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  33. [Dòng 771] value.length == 5) && parseInt(value.split('/')[0]
  34. [Dòng 775] || (this._util.checkValidExpireMonthYear(value) && (this._b == 2
  35. [Dòng 775] this._b == 20
  36. [Dòng 775] this._b == 33
  37. [Dòng 776] this._b == 39
  38. [Dòng 776] this._b == 43
  39. [Dòng 776] this._b == 45
  40. [Dòng 776] this._b == 64
  41. [Dòng 776] this._b == 68
  42. [Dòng 776] this._b == 72))) //sonnh them Vietbank 72
  43. [Dòng 797] this._inExpDate.length == 4
  44. [Dòng 797] this._inExpDate.search('/') == -1)
  45. [Dòng 798] this._inExpDate.length == 5))
  46. [Dòng 800] || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 20
  47. [Dòng 800] this._b == 72)));

!== (4 điều kiện):
  1. [Dòng 223] codeResponse.toString() !== '0') {
  2. [Dòng 591] let _b = this._b !== 67 ? 67 : this._b
  3. [Dòng 676] if (this.cardTypeBank !== 'internet_banking') {
  4. [Dòng 750] this._b !== 18)) {

!= (31 điều kiện):
  1. [Dòng 152] if (this.htmlDesc != null
  2. [Dòng 212] if (this._res_post.authorization != null
  3. [Dòng 212] this._res_post.authorization.links != null
  4. [Dòng 212] this._res_post.authorization.links.approval != null) {
  5. [Dòng 272] if (ua.indexOf('safari') != -1
  6. [Dòng 337] if (this._inExpDate.length != 3) {
  7. [Dòng 429] if (_formCard.exp_date != null
  8. [Dòng 434] if (this.cardName != null
  9. [Dòng 472] if (this._res_post.return_url != null) {
  10. [Dòng 475] if (this._res_post.links != null
  11. [Dòng 475] this._res_post.links.merchant_return != null
  12. [Dòng 475] this._res_post.links.merchant_return.href != null) {
  13. [Dòng 533] this._res_post.links.cancel != null) {
  14. [Dòng 538] let userName = _formCard.name != null ? _formCard.name : ''
  15. [Dòng 539] this._res_post.authorization.links.approval != null
  16. [Dòng 539] this._res_post.authorization.links.approval.href != null) {
  17. [Dòng 542] userName = paramUserName != null ? paramUserName : ''
  18. [Dòng 593] this._b != 27
  19. [Dòng 593] this._b != 12
  20. [Dòng 593] this._b != 3))
  21. [Dòng 762] if (this._b != 68
  22. [Dòng 773] this._b != 2
  23. [Dòng 773] this._b != 20
  24. [Dòng 773] this._b != 33
  25. [Dòng 773] this._b != 39
  26. [Dòng 774] this._b != 43
  27. [Dòng 774] this._b != 45
  28. [Dòng 774] this._b != 64
  29. [Dòng 774] this._b != 67
  30. [Dòng 774] this._b != 68
  31. [Dòng 774] this._b != 72)

================================================================================

📁 FILE 35: otp-auth.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 36: otp-auth.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
📊 Thống kê: 19 điều kiện duy nhất
   - === : 0 lần
   - == : 11 lần
   - !== : 1 lần
   - != : 7 lần
--------------------------------------------------------------------------------

== (11 điều kiện):
  1. [Dòng 93] if (this._b == 8) {//MB Bank
  2. [Dòng 97] if (this._b == 18) {//Oceanbank
  3. [Dòng 138] if (this._b == 8) {
  4. [Dòng 143] if (this._b == 18) {
  5. [Dòng 148] if (this._b == 12) { //SHB
  6. [Dòng 169] if (_re.status == '200'
  7. [Dòng 169] _re.status == '201') {
  8. [Dòng 178] } else if (this._res.state == 'authorization_required') {
  9. [Dòng 209] if (reason.code == '25'
  10. [Dòng 295] if (this._b == 12) {
  11. [Dòng 345] if (this._b == 18) {//8-MB Bank;18-oceanbank

!== (1 điều kiện):
  1. [Dòng 184] codeResponse.toString() !== '0') {

!= (7 điều kiện):
  1. [Dòng 174] if (this._res.links != null
  2. [Dòng 174] this._res.links.merchant_return != null
  3. [Dòng 174] this._res.links.merchant_return.href != null) {
  4. [Dòng 209] this.challengeCode != '') {
  5. [Dòng 340] if (!(_formCard.otp != null
  6. [Dòng 346] if (!(_formCard.password != null
  7. [Dòng 362] if (ua.indexOf('safari') != -1

================================================================================

📁 FILE 37: shb.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/shb/shb.component.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 3 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 25] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 42] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  3. [Dòng 42] _inExpDate.trim().length === 0)"

== (3 điều kiện):
  1. [Dòng 22] <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_card_number')">
  2. [Dòng 66] <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_account_number')">
  3. [Dòng 94] token_site == 'onepay'

================================================================================

📁 FILE 38: shb.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/shb/shb.component.ts
📊 Thống kê: 66 điều kiện duy nhất
   - === : 17 lần
   - == : 33 lần
   - !== : 5 lần
   - != : 11 lần
--------------------------------------------------------------------------------

=== (17 điều kiện):
  1. [Dòng 111] if (isIE[0] === 'MSIE'
  2. [Dòng 111] +isIE[1] === 10) {
  3. [Dòng 153] if (focusElement === 'card_name') {
  4. [Dòng 155] } else if (focusElement === 'exp_date'
  5. [Dòng 176] focusExpDateElement === 'card_name') {
  6. [Dòng 392] if (this.cardTypeBank === 'bank_account_number'
  7. [Dòng 437] if (valIn === this._translate.instant('bank_card_number')) {
  8. [Dòng 443] } else if (valIn === this._translate.instant('bank_account_number')) {
  9. [Dòng 485] if (_val.value === ''
  10. [Dòng 485] _val.value === null
  11. [Dòng 485] _val.value === undefined) {
  12. [Dòng 496] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  13. [Dòng 496] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  14. [Dòng 503] this.cardTypeOcean === 'MB') {
  15. [Dòng 511] this.cardTypeOcean === 'IB'
  16. [Dòng 517] if ((this.cardTypeBank === 'bank_card_number'
  17. [Dòng 540] if (this.cardTypeOcean === 'IB') {

== (33 điều kiện):
  1. [Dòng 102] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 120] if(this._b == 12) this.isShbGroup = true;
  3. [Dòng 141] return this._b == 9
  4. [Dòng 141] this._b == 11
  5. [Dòng 141] this._b == 16
  6. [Dòng 141] this._b == 17
  7. [Dòng 141] this._b == 25
  8. [Dòng 141] this._b == 44
  9. [Dòng 142] this._b == 57
  10. [Dòng 142] this._b == 59
  11. [Dòng 142] this._b == 61
  12. [Dòng 142] this._b == 63
  13. [Dòng 142] this._b == 69
  14. [Dòng 224] if (this._b == 12
  15. [Dòng 224] this.cardTypeBank == 'bank_account_number') {
  16. [Dòng 235] _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
  17. [Dòng 268] this.token_site == 'onepay'
  18. [Dòng 285] if (_re.status == '200'
  19. [Dòng 285] _re.status == '201') {
  20. [Dòng 289] if (this._res_post.state == 'approved'
  21. [Dòng 289] this._res_post.state == 'failed') {
  22. [Dòng 295] } else if (this._res_post.state == 'authorization_required') {
  23. [Dòng 395] if ((cardNo.length == 16
  24. [Dòng 395] cardNo.length == 19) && cardNo.startsWith("970443") && this._util.checkMod10(cardNo) && this.checkBin(cardNo) ) {
  25. [Dòng 407] if (this._b == +e.id) {
  26. [Dòng 423] if (valIn == 1) {
  27. [Dòng 425] } else if (valIn == 2) {
  28. [Dòng 496] this._b == 18))) {
  29. [Dòng 503] } else if (this._b == 18
  30. [Dòng 517] this._b == 18)) {
  31. [Dòng 529] this.c_expdate = !(((this.valueDate.length == 4
  32. [Dòng 529] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  33. [Dòng 529] this.valueDate.length == 5)

!== (5 điều kiện):
  1. [Dòng 273] key !== '3') {
  2. [Dòng 315] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  3. [Dòng 333] codeResponse.toString() !== '0') {
  4. [Dòng 392] cardNo.length !== 0) {
  5. [Dòng 517] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (11 điều kiện):
  1. [Dòng 128] if (this.htmlDesc != null
  2. [Dòng 189] if (ua.indexOf('safari') != -1
  3. [Dòng 235] if (_formCard.exp_date != null
  4. [Dòng 240] if (this.cardName != null
  5. [Dòng 292] if (this._res_post.links != null
  6. [Dòng 292] this._res_post.links.merchant_return != null
  7. [Dòng 292] this._res_post.links.merchant_return.href != null) {
  8. [Dòng 298] if (this._res_post.authorization != null
  9. [Dòng 298] this._res_post.authorization.links != null
  10. [Dòng 298] this._res_post.authorization.links.approval != null) {
  11. [Dòng 305] this._res_post.links.cancel != null) {

================================================================================

📁 FILE 39: techcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 40: techcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 41: techcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
📊 Thống kê: 14 điều kiện duy nhất
   - === : 1 lần
   - == : 10 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 68] if (this.timeLeft === 0) {

== (10 điều kiện):
  1. [Dòng 59] if (this._b == 2
  2. [Dòng 59] this._b == 31) {
  3. [Dòng 98] if (this._b == 2) {
  4. [Dòng 100] } else if (this._b == 6) {
  5. [Dòng 102] } else if (this._b == 31) {
  6. [Dòng 132] if (_re.status == '200'
  7. [Dòng 132] _re.status == '201') {
  8. [Dòng 137] if (this._res_post.state == 'approved'
  9. [Dòng 137] this._res_post.state == 'failed') {
  10. [Dòng 141] } else if (this._res_post.state == 'authorization_required') {

!= (3 điều kiện):
  1. [Dòng 145] if (this._res_post.authorization != null
  2. [Dòng 145] this._res_post.authorization.links != null
  3. [Dòng 145] this._res_post.authorization.links.approval != null) {

================================================================================

📁 FILE 42: vibbank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 28] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 42] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 42] valueDate.trim().length === 0)"

================================================================================

📁 FILE 43: vibbank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 44: vibbank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.ts
📊 Thống kê: 96 điều kiện duy nhất
   - === : 36 lần
   - == : 33 lần
   - !== : 10 lần
   - != : 17 lần
--------------------------------------------------------------------------------

=== (36 điều kiện):
  1. [Dòng 118] if (isIE[0] === 'MSIE'
  2. [Dòng 118] +isIE[1] === 10) {
  3. [Dòng 149] if (this.timeLeft === 0) {
  4. [Dòng 193] if ((_val.value.substr(-1) === ' '
  5. [Dòng 193] _val.value.length === 24) {
  6. [Dòng 203] if (this.cardTypeBank === 'bank_card_number') {
  7. [Dòng 208] } else if (this.cardTypeBank === 'bank_account_number') {
  8. [Dòng 214] } else if (this.cardTypeBank === 'bank_username') {
  9. [Dòng 218] } else if (this.cardTypeBank === 'bank_customer_code') {
  10. [Dòng 239] if (this.cardTypeBank === 'bank_account_number') {
  11. [Dòng 250] this.cardTypeBank === 'bank_card_number') {
  12. [Dòng 490] if (event.keyCode === 8
  13. [Dòng 490] event.key === "Backspace"
  14. [Dòng 530] if (v.length === 2
  15. [Dòng 530] this.flag.length === 3
  16. [Dòng 530] this.flag.charAt(this.flag.length - 1) === '/') {
  17. [Dòng 534] if (v.length === 1) {
  18. [Dòng 536] } else if (v.length === 2) {
  19. [Dòng 539] v.length === 2) {
  20. [Dòng 547] if (len === 2) {
  21. [Dòng 782] if ((this.cardTypeBank === 'bank_account_number'
  22. [Dòng 782] this.cardTypeBank === 'bank_username'
  23. [Dòng 782] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  24. [Dòng 832] if (valIn === this._translate.instant('bank_card_number')) {
  25. [Dòng 851] } else if (valIn === this._translate.instant('bank_account_number')) {
  26. [Dòng 863] } else if (valIn === this._translate.instant('bank_username')) {
  27. [Dòng 874] } else if (valIn === this._translate.instant('bank_customer_code')) {
  28. [Dòng 931] if (_val.value === ''
  29. [Dòng 931] _val.value === null
  30. [Dòng 931] _val.value === undefined) {
  31. [Dòng 942] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  32. [Dòng 952] if ((this.cardTypeBank === 'bank_card_number'
  33. [Dòng 984] if (this.cardName === undefined
  34. [Dòng 984] this.cardName === '') {
  35. [Dòng 992] if (this.valueDate === undefined
  36. [Dòng 992] this.valueDate === '') {

== (33 điều kiện):
  1. [Dòng 132] if (this._b == 5) {//5-vib;
  2. [Dòng 202] if (this._b == 5) {
  3. [Dòng 236] if (this.checkBin(_val.value) && (this._b == 5)) {
  4. [Dòng 252] if (this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  5. [Dòng 314] if (this.checkBin(v) && (this._b == 5)) {
  6. [Dòng 490] event.inputType == 'deleteContentBackward') {
  7. [Dòng 491] if (event.target.name == 'exp_date'
  8. [Dòng 499] event.inputType == 'insertCompositionText') {
  9. [Dòng 514] if (((this.valueDate.length == 4
  10. [Dòng 514] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  11. [Dòng 514] this.valueDate.length == 5)
  12. [Dòng 594] if (temp.length == 0) {
  13. [Dòng 601] return (counter % 10 == 0);
  14. [Dòng 632] _formCard.exp_date.length == 5
  15. [Dòng 632] this._b == 5) {//5 vib ;
  16. [Dòng 637] this._b == 5) {//5vib;
  17. [Dòng 682] if (_re.status == '200'
  18. [Dòng 682] _re.status == '201') {
  19. [Dòng 687] if (this._res_post.state == 'approved'
  20. [Dòng 687] this._res_post.state == 'failed') {
  21. [Dòng 694] } else if (this._res_post.state == 'authorization_required') {
  22. [Dòng 787] if ((cardNo.length == 16
  23. [Dòng 787] if ((cardNo.length == 16 || (cardNo.length == 19
  24. [Dòng 788] && ((this._b == 18
  25. [Dòng 788] cardNo.length == 19) || this._b != 18)
  26. [Dòng 801] if (this._b == +e.id) {
  27. [Dòng 817] if (valIn == 1) {
  28. [Dòng 819] } else if (valIn == 2) {
  29. [Dòng 942] this._b == 18)) {
  30. [Dòng 964] this.c_expdate = !(((this.valueDate.length == 4
  31. [Dòng 995] this.valueDate.length == 4
  32. [Dòng 995] this.valueDate.search('/') == -1)
  33. [Dòng 996] this.valueDate.length == 5))

!== (10 điều kiện):
  1. [Dòng 193] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 668] key !== '3') {
  3. [Dòng 716] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 733] codeResponse.toString() !== '0') {
  5. [Dòng 782] cardNo.length !== 0) {
  6. [Dòng 839] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 854] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 868] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 881] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 952] this._b !== 18) || (this._b == 18)) {

!= (17 điều kiện):
  1. [Dòng 159] if (this.htmlDesc != null
  2. [Dòng 190] if (ua.indexOf('safari') != -1
  3. [Dòng 200] if (_val.value != '') {
  4. [Dòng 492] if (this.valueDate.length != 3) {
  5. [Dòng 632] if (_formCard.exp_date != null
  6. [Dòng 637] if (this.cardName != null
  7. [Dòng 690] if (this._res_post.links != null
  8. [Dòng 690] this._res_post.links.merchant_return != null
  9. [Dòng 690] this._res_post.links.merchant_return.href != null) {
  10. [Dòng 698] if (this._res_post.authorization != null
  11. [Dòng 698] this._res_post.authorization.links != null
  12. [Dòng 698] this._res_post.authorization.links.approval != null) {
  13. [Dòng 705] this._res_post.links.cancel != null) {
  14. [Dòng 787] this._b != 27
  15. [Dòng 787] this._b != 12
  16. [Dòng 787] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  17. [Dòng 788] this._b != 18)

================================================================================

📁 FILE 45: vietcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 28] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  2. [Dòng 28] _inExpDate.trim().length === 0)"

== (2 điều kiện):
  1. [Dòng 54] token_site == 'onepay'
  2. [Dòng 64] _b == 68"

================================================================================

📁 FILE 46: vietcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 47: vietcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
📊 Thống kê: 94 điều kiện duy nhất
   - === : 4 lần
   - == : 63 lần
   - !== : 2 lần
   - != : 25 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 208] if (event.keyCode === 8
  2. [Dòng 208] event.key === "Backspace"
  3. [Dòng 449] if (approval.method === 'REDIRECT') {
  4. [Dòng 452] } else if (approval.method === 'POST_REDIRECT') {

== (63 điều kiện):
  1. [Dòng 89] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 112] if (this._b == 1
  3. [Dòng 112] this._b == 20
  4. [Dòng 112] this._b == 36
  5. [Dòng 112] this._b == 64
  6. [Dòng 112] this._b == 55
  7. [Dòng 112] this._b == 47
  8. [Dòng 112] this._b == 48
  9. [Dòng 112] this._b == 59) {
  10. [Dòng 126] this._b == 9
  11. [Dòng 126] this._b == 16
  12. [Dòng 126] this._b == 17
  13. [Dòng 126] this._b == 25
  14. [Dòng 126] this._b == 44
  15. [Dòng 127] this._b == 57
  16. [Dòng 127] this._b == 59
  17. [Dòng 127] this._b == 61
  18. [Dòng 127] this._b == 63
  19. [Dòng 127] this._b == 69
  20. [Dòng 135] return this._b == 11
  21. [Dòng 135] this._b == 33
  22. [Dòng 135] this._b == 39
  23. [Dòng 135] this._b == 43
  24. [Dòng 135] this._b == 45
  25. [Dòng 136] this._b == 67
  26. [Dòng 136] this._b == 72
  27. [Dòng 136] this._b == 73
  28. [Dòng 136] this._b == 68
  29. [Dòng 136] this._b == 74
  30. [Dòng 136] this._b == 75
  31. [Dòng 208] event.inputType == 'deleteContentBackward') {
  32. [Dòng 209] if (event.target.name == 'exp_date'
  33. [Dòng 217] event.inputType == 'insertCompositionText') {
  34. [Dòng 326] this.token_site == 'onepay'
  35. [Dòng 340] if (this._res_post.state == 'approved'
  36. [Dòng 340] this._res_post.state == 'failed') {
  37. [Dòng 389] } else if (this._res_post.state == 'authorization_required') {
  38. [Dòng 411] this._b == 14
  39. [Dòng 411] this._b == 15
  40. [Dòng 411] this._b == 24
  41. [Dòng 411] this._b == 8
  42. [Dòng 411] this._b == 10
  43. [Dòng 411] this._b == 22
  44. [Dòng 411] this._b == 23
  45. [Dòng 411] this._b == 30
  46. [Dòng 411] this._b == 11
  47. [Dòng 411] this._b == 9) {
  48. [Dòng 480] if (err.status == 400
  49. [Dòng 480] err.status == 500) {
  50. [Dòng 481] if (err.error && (err.error.code == 13
  51. [Dòng 481] err.error.code == '13')) {
  52. [Dòng 494] if ((cardNo.length == 16
  53. [Dòng 495] (cardNo.length == 19
  54. [Dòng 495] (cardNo.length == 19 && (this._b == 1
  55. [Dòng 495] this._b == 4
  56. [Dòng 495] this._b == 59))
  57. [Dòng 497] this._util.checkMod10(cardNo) == true
  58. [Dòng 533] return ((value.length == 4
  59. [Dòng 533] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  60. [Dòng 533] value.length == 5) && parseInt(value.split('/')[0]
  61. [Dòng 567] this._inExpDate.length == 4
  62. [Dòng 567] this._inExpDate.search('/') == -1)
  63. [Dòng 568] this._inExpDate.length == 5))

!== (2 điều kiện):
  1. [Dòng 353] codeResponse.toString() !== '0') {
  2. [Dòng 412] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (25 điều kiện):
  1. [Dòng 117] if (this.htmlDesc != null
  2. [Dòng 145] if (ua.indexOf('safari') != -1
  3. [Dòng 210] if (this._inExpDate.length != 3) {
  4. [Dòng 290] if ( this._b != 9
  5. [Dòng 290] this._b != 16
  6. [Dòng 290] this._b != 17
  7. [Dòng 290] this._b != 25
  8. [Dòng 290] this._b != 44
  9. [Dòng 291] this._b != 57
  10. [Dòng 291] this._b != 59
  11. [Dòng 291] this._b != 61
  12. [Dòng 291] this._b != 63
  13. [Dòng 291] this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
  14. [Dòng 304] '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75'].indexOf(this._b.toString()) != -1) {
  15. [Dòng 342] if (this._res_post.return_url != null) {
  16. [Dòng 345] if (this._res_post.links != null
  17. [Dòng 345] this._res_post.links.merchant_return != null
  18. [Dòng 345] this._res_post.links.merchant_return.href != null) {
  19. [Dòng 394] if (this._res_post.authorization != null
  20. [Dòng 394] this._res_post.authorization.links != null
  21. [Dòng 399] this._res_post.links.cancel != null) {
  22. [Dòng 405] let userName = _formCard.name != null ? _formCard.name : ''
  23. [Dòng 406] this._res_post.authorization.links.approval != null
  24. [Dòng 406] this._res_post.authorization.links.approval.href != null) {
  25. [Dòng 409] userName = paramUserName != null ? paramUserName : ''

================================================================================

📁 FILE 48: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 49: off-bank-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 50: off-bank-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 51: domescard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/domescard-main.component.html
📊 Thống kê: 9 điều kiện duy nhất
   - === : 1 lần
   - == : 8 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 16] filteredData.length === 0"

== (8 điều kiện):
  1. [Dòng 23] ((!token && _auth==0 && vietcombankGroupSelected) || (token && bankId==16))
  2. [Dòng 23] bankId==16))">
  3. [Dòng 28] _auth==0 && techcombankGroupSelected
  4. [Dòng 33] _auth==0 && shbGroupSelected
  5. [Dòng 38] _auth==0 && onepaynapasGroupSelected
  6. [Dòng 45] _auth==0 && bankaccountGroupSelected
  7. [Dòng 50] _auth==0 && vibbankGroupSelected
  8. [Dòng 56] (token || _auth==1) && _b != 16

================================================================================

📁 FILE 52: domescard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/domescard-main/domescard-main.component.ts
📊 Thống kê: 125 điều kiện duy nhất
   - === : 21 lần
   - == : 96 lần
   - !== : 1 lần
   - != : 7 lần
--------------------------------------------------------------------------------

=== (21 điều kiện):
  1. [Dòng 272] if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
  2. [Dòng 273] filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
  3. [Dòng 314] if (valOut === 'auth') {
  4. [Dòng 457] if (this._b === '1'
  5. [Dòng 457] this._b === '20'
  6. [Dòng 457] this._b === '64') {
  7. [Dòng 460] if (this._b === '36'
  8. [Dòng 460] this._b === '18'
  9. [Dòng 460] this._b === '19'
  10. [Dòng 463] if (this._b === '19'
  11. [Dòng 463] this._b === '16'
  12. [Dòng 463] this._b === '25'
  13. [Dòng 463] this._b === '33'
  14. [Dòng 464] this._b === '39'
  15. [Dòng 464] this._b === '11'
  16. [Dòng 464] this._b === '17'
  17. [Dòng 465] this._b === '36'
  18. [Dòng 465] this._b === '44'
  19. [Dòng 466] this._b === '64'
  20. [Dòng 469] if (this._b === '20'
  21. [Dòng 472] if (this._b === '18') {

== (96 điều kiện):
  1. [Dòng 187] this._auth == 0
  2. [Dòng 187] this.tokenList.length == 0) {
  3. [Dòng 261] this.filteredData.length == 1
  4. [Dòng 295] $event == 'true') {
  5. [Dòng 386] if (bankId == 1
  6. [Dòng 386] bankId == 4
  7. [Dòng 386] bankId == 7
  8. [Dòng 386] bankId == 8
  9. [Dòng 386] bankId == 9
  10. [Dòng 386] bankId == 10
  11. [Dòng 386] bankId == 11
  12. [Dòng 386] bankId == 14
  13. [Dòng 386] bankId == 15
  14. [Dòng 387] bankId == 16
  15. [Dòng 387] bankId == 17
  16. [Dòng 387] bankId == 20
  17. [Dòng 387] bankId == 22
  18. [Dòng 387] bankId == 23
  19. [Dòng 387] bankId == 24
  20. [Dòng 387] bankId == 25
  21. [Dòng 387] bankId == 30
  22. [Dòng 387] bankId == 33
  23. [Dòng 388] bankId == 34
  24. [Dòng 388] bankId == 35
  25. [Dòng 388] bankId == 36
  26. [Dòng 388] bankId == 37
  27. [Dòng 388] bankId == 38
  28. [Dòng 388] bankId == 39
  29. [Dòng 388] bankId == 40
  30. [Dòng 388] bankId == 41
  31. [Dòng 388] bankId == 42
  32. [Dòng 389] bankId == 43
  33. [Dòng 389] bankId == 44
  34. [Dòng 389] bankId == 45
  35. [Dòng 389] bankId == 46
  36. [Dòng 389] bankId == 47
  37. [Dòng 389] bankId == 48
  38. [Dòng 389] bankId == 49
  39. [Dòng 389] bankId == 50
  40. [Dòng 389] bankId == 51
  41. [Dòng 390] bankId == 52
  42. [Dòng 390] bankId == 53
  43. [Dòng 390] bankId == 54
  44. [Dòng 390] bankId == 55
  45. [Dòng 390] bankId == 56
  46. [Dòng 390] bankId == 57
  47. [Dòng 390] bankId == 58
  48. [Dòng 390] bankId == 59
  49. [Dòng 390] bankId == 60
  50. [Dòng 391] bankId == 61
  51. [Dòng 391] bankId == 62
  52. [Dòng 391] bankId == 63
  53. [Dòng 391] bankId == 64
  54. [Dòng 391] bankId == 65
  55. [Dòng 391] bankId == 66
  56. [Dòng 391] bankId == 68
  57. [Dòng 391] bankId == 69
  58. [Dòng 391] bankId == 70
  59. [Dòng 392] bankId == 71
  60. [Dòng 392] bankId == 72
  61. [Dòng 392] bankId == 73
  62. [Dòng 392] bankId == 32
  63. [Dòng 392] bankId == 74
  64. [Dòng 392] bankId == 75) {
  65. [Dòng 394] } else if (bankId == 6
  66. [Dòng 394] bankId == 31
  67. [Dòng 394] bankId == 80) {
  68. [Dòng 396] } else if (bankId == 2
  69. [Dòng 396] bankId == 67) {
  70. [Dòng 398] } else if (bankId == 3
  71. [Dòng 398] bankId == 18
  72. [Dòng 398] bankId == 19
  73. [Dòng 398] bankId == 27) {
  74. [Dòng 400] } else if (bankId == 5) {
  75. [Dòng 402] } else if (bankId == 12) {
  76. [Dòng 460] this._b == '55'
  77. [Dòng 460] this._b == '47'
  78. [Dòng 460] this._b == '48'
  79. [Dòng 460] this._b == '59'
  80. [Dòng 460] this._b == '73'
  81. [Dòng 460] this._b == '12') {
  82. [Dòng 463] this._b == '3'
  83. [Dòng 464] this._b == '43'
  84. [Dòng 464] this._b == '45'
  85. [Dòng 465] this._b == '57'
  86. [Dòng 466] this._b == '61'
  87. [Dòng 466] this._b == '63'
  88. [Dòng 466] this._b == '67'
  89. [Dòng 466] this._b == '68'
  90. [Dòng 466] this._b == '69'
  91. [Dòng 466] this._b == '72'
  92. [Dòng 466] this._b == '9'
  93. [Dòng 466] this._b == '74'
  94. [Dòng 466] this._b == '75') {
  95. [Dòng 469] this._b == '36'
  96. [Dòng 489] if (item['id'] == this._b) {

!== (1 điều kiện):
  1. [Dòng 122] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (7 điều kiện):
  1. [Dòng 173] if (params['locale'] != null) {
  2. [Dòng 179] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 183] this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
  4. [Dòng 210] if (!(strInstrument != null
  5. [Dòng 213] if (strInstrument.substring(0, 1) != '^'
  6. [Dòng 213] strInstrument.substr(strInstrument.length - 1) != '$') {
  7. [Dòng 364] if (bankid != null) {

================================================================================

📁 FILE 53: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/intercard-main/dialog-guide-dialog.html
📊 Thống kê: 8 điều kiện duy nhất
   - === : 0 lần
   - == : 8 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (8 điều kiện):
  1. [Dòng 30] data['type'] == '5'
  2. [Dòng 30] data['type'] == '7'
  3. [Dòng 30] data['type'] == '9'
  4. [Dòng 30] data['type'] == '10'"
  5. [Dòng 39] data['type'] == '7'"
  6. [Dòng 40] data['type'] == '9'"
  7. [Dòng 49] data['type'] == '6'
  8. [Dòng 49] data['type'] == '8'"

================================================================================

📁 FILE 54: intercard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/intercard-main/intercard-main.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 140] token_site == 'onepay'

!= (1 điều kiện):
  1. [Dòng 48] _showNameOnCard!=true"

================================================================================

📁 FILE 55: intercard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/intercard-main/intercard-main.component.ts
📊 Thống kê: 88 điều kiện duy nhất
   - === : 10 lần
   - == : 54 lần
   - !== : 11 lần
   - != : 13 lần
--------------------------------------------------------------------------------

=== (10 điều kiện):
  1. [Dòng 261] if (_formCard.country === 'default') {
  2. [Dòng 582] if (event.keyCode === 8
  3. [Dòng 582] event.key === "Backspace"
  4. [Dòng 657] if ((v.substr(-1) === ' '
  5. [Dòng 836] this._i_country_code === 'US') {
  6. [Dòng 872] const insertIndex = this._i_country_code === 'US' ? 5 : 3
  7. [Dòng 874] if (temp[i] === '-'
  8. [Dòng 874] temp[i] === ' ') {
  9. [Dòng 881] insertIndex === 3 ? ' ' : itemRemoved)
  10. [Dòng 930] this.c_country = _val.value === 'default'

== (54 điều kiện):
  1. [Dòng 147] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 191] regexp.test('card;;visa;USD')) && (this._type == 5
  3. [Dòng 191] this._type == 7);
  4. [Dòng 192] regexp.test('card;;mastercard;USD')) && (this._type == 5
  5. [Dòng 193] regexp.test('card;;amex;USD')) && (this._type == 6
  6. [Dòng 193] this._type == 8);
  7. [Dòng 194] this._type == 9
  8. [Dòng 195] this._type == 10
  9. [Dòng 294] this.token_site == 'onepay'
  10. [Dòng 320] if (this._res_post.state == 'approved'
  11. [Dòng 320] this._res_post.state == 'failed') {
  12. [Dòng 347] } else if (this._res_post.state == 'failed') {  // lỗi mới kiểm tra sang trang lỗi
  13. [Dòng 377] } else if (this._res_post.state == 'authorization_required') {
  14. [Dòng 378] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  15. [Dòng 390] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  16. [Dòng 416] if (err.status == 400
  17. [Dòng 416] err.status == 500) {
  18. [Dòng 417] if (err.error && (err.error.code == 8
  19. [Dòng 417] err.error.code == '8')) {
  20. [Dòng 418] if (this._type == 5
  21. [Dòng 418] this._type == 6) {
  22. [Dòng 420] } else if (this._type == 7
  23. [Dòng 420] this._type == 8) {
  24. [Dòng 423] } else if (err.error && (err.error.code == 13
  25. [Dòng 423] err.error.code == '13')) {
  26. [Dòng 490] v.length == 15) || (v.length == 16
  27. [Dòng 490] v.length == 19))
  28. [Dòng 491] this._util.checkMod10(v) == true) {
  29. [Dòng 536] cardNo.length == 15)
  30. [Dòng 538] cardNo.length == 16)
  31. [Dòng 539] cardNo.startsWith('81')) && (cardNo.length == 16
  32. [Dòng 539] cardNo.length == 19))
  33. [Dòng 582] event.inputType == 'deleteContentBackward') {
  34. [Dòng 583] if (event.target.name == 'exp_date'
  35. [Dòng 591] event.inputType == 'insertCompositionText') {
  36. [Dòng 606] if (((this.valueDate.length == 4
  37. [Dòng 606] this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  38. [Dòng 606] this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  39. [Dòng 657] v.length == 5) {
  40. [Dòng 665] v.length == 4
  41. [Dòng 669] v.length == 3)
  42. [Dòng 695] _val.value.length == 4
  43. [Dòng 699] _val.value.length == 3)
  44. [Dòng 888] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  45. [Dòng 888] this.valueDate.length == 5)
  46. [Dòng 915] this.requireAvs = this.AVS_COUNTRIES.find(e => e.code == this._i_country_code) ? true : false;
  47. [Dòng 924] countryCode == 'US' ? US_STATES
  48. [Dòng 925] : countryCode == 'CA' ? CA_STATES
  49. [Dòng 965] this.valueDate.length == 4
  50. [Dòng 965] this.valueDate.search('/') == -1)
  51. [Dòng 966] this.valueDate.length == 5))
  52. [Dòng 979] this._i_csc.length == 4) ||
  53. [Dòng 983] this._i_csc.length == 3)
  54. [Dòng 1028] country = this.AVS_COUNTRIES.find(e => e.code == res.bin_country);

!== (11 điều kiện):
  1. [Dòng 302] if (this.tracking.hasOwnProperty(key) && ((key !== '8'
  2. [Dòng 302] !this._showNameOnCard) || (key !== '9'
  3. [Dòng 330] codeResponse.toString() !== '0'){
  4. [Dòng 657] event.inputType !== 'deleteContentBackward') || v.length == 5) {
  5. [Dòng 839] this._i_country_code !== 'US') {
  6. [Dòng 880] itemRemoved !== '') {
  7. [Dòng 907] if (deviceValue !== 'default') {
  8. [Dòng 911] this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
  9. [Dòng 991] this._i_country_code !== 'default'
  10. [Dòng 1068] this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
  11. [Dòng 1075] this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {

!= (13 điều kiện):
  1. [Dòng 175] if (params['locale'] != null) {
  2. [Dòng 181] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 322] if (this._res_post.return_url != null) {
  4. [Dòng 324] } else if (this._res_post.links != null
  5. [Dòng 324] this._res_post.links.merchant_return != null
  6. [Dòng 324] this._res_post.links.merchant_return.href != null) {
  7. [Dòng 472] if (ua.indexOf('safari') != -1
  8. [Dòng 536] cardNo != null
  9. [Dòng 584] if (this.valueDate.length != 3) {
  10. [Dòng 664] v != null
  11. [Dòng 694] this.c_csc = (!(_val.value != null
  12. [Dòng 977] this._i_csc != null
  13. [Dòng 1030] this.requireAvs = this.isAvsCountry = country != undefined

================================================================================

📁 FILE 56: menu.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/menu.component.html
📊 Thống kê: 24 điều kiện duy nhất
   - === : 17 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (17 điều kiện):
  1. [Dòng 144] [ngStyle]="{'border-color': type === 5 ? this.themeColor.border_color : border_color}"
  2. [Dòng 162] type === 5"
  3. [Dòng 181] [ngStyle]="{'border-color': type === 9 ? this.themeColor.border_color : border_color}"
  4. [Dòng 197] type === 9"
  5. [Dòng 216] [ngStyle]="{'border-color': type === 10 ? this.themeColor.border_color : border_color}"
  6. [Dòng 232] type === 10"
  7. [Dòng 250] type === 6"
  8. [Dòng 251] [ngStyle]="{'border-color': type === 6 ? this.themeColor.border_color : border_color}"
  9. [Dòng 285] type === 2"
  10. [Dòng 287] [ngStyle]="{'border-color': type === 2 ? this.themeColor.border_color : border_color}"
  11. [Dòng 305] <div *ngIf="(type === 2
  12. [Dòng 305] type === '2'
  13. [Dòng 457] type === 4"
  14. [Dòng 458] [ngStyle]="{'border-color': type === 4 ? this.themeColor.border_color : border_color}"
  15. [Dòng 489] type === 3"
  16. [Dòng 490] [ngStyle]="{'border-color': type === 3 ? this.themeColor.border_color : border_color}"
  17. [Dòng 523] d_vrbank===true"

== (6 điều kiện):
  1. [Dòng 58] merchantId == 'TESTENETVIET'"
  2. [Dòng 143] type == 5"
  3. [Dòng 180] type == 9"
  4. [Dòng 215] type == 10"
  5. [Dòng 290] tokenList.length == 1)">
  6. [Dòng 295] tokenList.length == 1"

!= (1 điều kiện):
  1. [Dòng 87] merchantId != 'TESTENETVIET'"

================================================================================

📁 FILE 57: menu.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/menu.component.ts
📊 Thống kê: 151 điều kiện duy nhất
   - === : 10 lần
   - == : 79 lần
   - !== : 3 lần
   - != : 59 lần
--------------------------------------------------------------------------------

=== (10 điều kiện):
  1. [Dòng 535] this.cardListMethod = this.d_domestic + this.d_visa_master + this.d_amex_check + this.d_jcb_check + this.d_cup_check + this.d_qr + this.d_paypal_number === 1
  2. [Dòng 817] this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number === 1
  3. [Dòng 870] if (this._res.state === 'unpaid'
  4. [Dòng 870] this._res.state === 'not_paid') {
  5. [Dòng 986] if ('op' === auth
  6. [Dòng 1023] } else if ('bank' === auth
  7. [Dòng 1028] if (approval.method === 'REDIRECT') {
  8. [Dòng 1031] } else if (approval.method === 'POST_REDIRECT') {
  9. [Dòng 1265] return id === 'amex' ? '1234' : '123'
  10. [Dòng 1425] if (this.timeLeftPaypal === 0) {

== (79 điều kiện):
  1. [Dòng 192] if (el == 5) {
  2. [Dòng 194] } else if (el == 6) {
  3. [Dòng 196] } else if (el == 7) {
  4. [Dòng 198] } else if (el == 8) {
  5. [Dòng 200] } else if (el == 2) {
  6. [Dòng 202] } else if (el == 4) {
  7. [Dòng 204] } else if (el == 3) {
  8. [Dòng 206] } else if (el == 9) {
  9. [Dòng 245] if (!isNaN(_re.status) && (_re.status == '200'
  10. [Dòng 245] _re.status == '201') && _re.body != null) {
  11. [Dòng 250] if (('closed' == this._res_polling.state
  12. [Dòng 250] 'canceled' == this._res_polling.state
  13. [Dòng 250] 'expired' == this._res_polling.state)
  14. [Dòng 270] } else if ('paid' == this._res_polling.state) {
  15. [Dòng 280] this._res_polling.payments == null) {
  16. [Dòng 289] if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'authorization_required') {
  17. [Dòng 293] } else if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  18. [Dòng 300] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  19. [Dòng 302] this._paymentService.getCurrentPage() == 'enter_card') {
  20. [Dòng 305] } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
  21. [Dòng 305] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
  22. [Dòng 322] } else if ('not_paid' == this._res_polling.state) {
  23. [Dòng 334] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
  24. [Dòng 473] if (message == '1') {
  25. [Dòng 480] if (this.checkInvoiceState() == 1) {
  26. [Dòng 489] if (_re.status == '200'
  27. [Dòng 489] _re.status == '201') {
  28. [Dòng 495] this.version2 = _re.body?.merchant?.qr_version == '2' ? true : false
  29. [Dòng 538] if (this.type == 5
  30. [Dòng 541] } else if (this.type == 6
  31. [Dòng 544] } else if (this.type == 2
  32. [Dòng 547] } else if (this.type == 7
  33. [Dòng 550] } else if (this.type == 9
  34. [Dòng 553] } else if (this.type == 10
  35. [Dòng 556] } else if (this.type == 8
  36. [Dòng 559] } else if (this.type == 4
  37. [Dòng 562] } else if (this.type == 3
  38. [Dòng 573] } else if ((this.d_domestic == 1) && this.feeService['atm'] && this.feeService['atm']['fee']) {
  39. [Dòng 673] if (('closed' == this._res.state
  40. [Dòng 673] 'canceled' == this._res.state
  41. [Dòng 673] 'expired' == this._res.state
  42. [Dòng 673] 'paid' == this._res.state)
  43. [Dòng 677] if ('paid' == this._res.state
  44. [Dòng 823] this._auth == 0) {
  45. [Dòng 848] if ((this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number) == 0
  46. [Dòng 871] if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
  47. [Dòng 871] this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
  48. [Dòng 874] if (this._res.payments[this._res.payments.length - 1].state == 'approved'
  49. [Dòng 876] } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
  50. [Dòng 923] if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required') {
  51. [Dòng 929] } else if (this._res.payments[this._res.payments.length - 1].state == 'pending') {
  52. [Dòng 935] } else if (this._res.payments[this._res.payments.length - 1].state == 'canceled') {
  53. [Dòng 939] } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  54. [Dòng 939] this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
  55. [Dòng 974] } else if (idBrand == 'atm'
  56. [Dòng 1052] if ('paid' == this._res.state) {
  57. [Dòng 1053] this._res.merchant.token_site == 'onepay')) {
  58. [Dòng 1104] this._res.payments == null) {
  59. [Dòng 1106] this._res.payments[this._res.payments.length - 1].state == 'pending') {
  60. [Dòng 1116] if (this._res.currencies[0] == 'USD') {
  61. [Dòng 1181] if (item.instrument.issuer.brand.id == 'atm') {
  62. [Dòng 1183] } else if (item.instrument.issuer.brand.id == 'visa'
  63. [Dòng 1183] item.instrument.issuer.brand.id == 'mastercard') {
  64. [Dòng 1184] if (item.instrument.issuer_location == 'd') {
  65. [Dòng 1189] } else if (item.instrument.issuer.brand.id == 'amex') {
  66. [Dòng 1195] } else if (item.instrument.issuer.brand.id == 'jcb') {
  67. [Dòng 1331] if (type == 'mobile') {
  68. [Dòng 1333] e.type == 'ewallet'
  69. [Dòng 1333] e.code == 'momo')) {
  70. [Dòng 1343] e.type == 'vnpayqr') || (regex.test(strTest)
  71. [Dòng 1400] this.tokenList.length == 1) {
  72. [Dòng 1401] if (_val == 2
  73. [Dòng 1413] if (this.type == 4) {
  74. [Dòng 1493] if (this._res_post.state == 'approved'
  75. [Dòng 1493] this._res_post.state == 'failed') {
  76. [Dòng 1502] } else if (this._res_post.state == 'authorization_required') {
  77. [Dòng 1503] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  78. [Dòng 1517] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  79. [Dòng 1610] if (data._locale == 'en') {

!== (3 điều kiện):
  1. [Dòng 998] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  2. [Dòng 1379] if (_val !== 3) {
  3. [Dòng 1383] this._util.getElementParams(this.currentUrl, 't_id') !== '') {

!= (59 điều kiện):
  1. [Dòng 238] if (this._idInvoice != null
  2. [Dòng 238] this._paymentService.getState() != 'error') {
  3. [Dòng 244] if (this._paymentService.getCurrentPage() != 'otp') {
  4. [Dòng 245] _re.body != null) {
  5. [Dòng 251] this._res_polling.links != null
  6. [Dòng 251] this._res_polling.links.merchant_return != null //
  7. [Dòng 280] } else if (this._res_polling.merchant != null
  8. [Dòng 280] this._res_polling.merchant_invoice_reference != null
  9. [Dòng 282] } else if (this._res_polling.payments != null
  10. [Dòng 306] this._res_polling.links.merchant_return != null//
  11. [Dòng 325] this._res_polling.payments != null
  12. [Dòng 333] if (this._res_polling.payments != null
  13. [Dòng 338] t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
  14. [Dòng 453] this.type.toString().length != 0) {
  15. [Dòng 459] if (params['locale'] != null) {
  16. [Dòng 466] if ('otp' != this._paymentService.getCurrentPage()) {
  17. [Dòng 478] if (this._paymentService.getInvoiceDetail() != null) {
  18. [Dòng 674] this._res.links != null
  19. [Dòng 674] this._res.links.merchant_return != null
  20. [Dòng 834] if (count != 1) {
  21. [Dòng 840] if (this._res.merchant != null
  22. [Dòng 840] this._res.merchant_invoice_reference != null) {
  23. [Dòng 843] if (this._res.merchant.address_details != null) {
  24. [Dòng 871] this._res.links != null//
  25. [Dòng 918] } else if (this._res.payments != null
  26. [Dòng 940] this._res.payments[this._res.payments.length - 1].instrument != null
  27. [Dòng 940] this._res.payments[this._res.payments.length - 1].instrument.issuer != null
  28. [Dòng 941] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
  29. [Dòng 941] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
  30. [Dòng 942] this._res.payments[this._res.payments.length - 1].links != null
  31. [Dòng 942] this._res.payments[this._res.payments.length - 1].links.cancel != null
  32. [Dòng 942] this._res.payments[this._res.payments.length - 1].links.cancel.href != null
  33. [Dòng 959] this._res.payments[this._res.payments.length - 1].links.update != null
  34. [Dòng 959] this._res.payments[this._res.payments.length - 1].links.update.href != null) {
  35. [Dòng 974] this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
  36. [Dòng 975] this._res.payments[this._res.payments.length - 1].authorization != null
  37. [Dòng 975] this._res.payments[this._res.payments.length - 1].authorization.links != null) {
  38. [Dòng 986] this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
  39. [Dòng 986] this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
  40. [Dòng 989] if (this._res.payments[this._res.payments.length - 1].authorization != null
  41. [Dòng 989] this._res.payments[this._res.payments.length - 1].authorization.links != null
  42. [Dòng 995] auth = paramUserName != null ? paramUserName : ''
  43. [Dòng 1079] this._res.links.merchant_return != null //
  44. [Dòng 1104] } else if (this._res.merchant != null
  45. [Dòng 1104] this._res.merchant_invoice_reference != null
  46. [Dòng 1209] if (['mastercard', 'visa', 'amex', 'jcb', 'cup', 'card', 'np_card', 'atm'].indexOf(id) != -1) {
  47. [Dòng 1211] } else if (['techcombank_account', 'vib_account', 'dongabank_account', 'tpbank_account', 'bidv_account', 'pvcombank_account', 'shb_account', 'oceanbank_online_account'].indexOf(id) != -1) {
  48. [Dòng 1213] } else if (['oceanbank_mobile_account'].indexOf(id) != -1) {
  49. [Dòng 1215] } else if (['shb_customer_id'].indexOf(id) != -1) {
  50. [Dòng 1232] if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1) {
  51. [Dòng 1260] return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
  52. [Dòng 1297] if (!(strInstrument != null
  53. [Dòng 1314] if (this._translate.currentLang != language) {
  54. [Dòng 1333] e.type != 'ewallet') || (regex.test(strTest)
  55. [Dòng 1385] } else if (this._res.payments != null) {
  56. [Dòng 1495] if (this._res_post.return_url != null) {
  57. [Dòng 1497] } else if (this._res_post.links != null
  58. [Dòng 1497] this._res_post.links.merchant_return != null
  59. [Dòng 1497] this._res_post.links.merchant_return.href != null) {

================================================================================

📁 FILE 58: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 59: qr-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 2] screen=='qr'"
  2. [Dòng 112] screen=='confirm_close'"

================================================================================

📁 FILE 60: qr-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 46] 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {

================================================================================

📁 FILE 61: qr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main/qr-main.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 6] filteredData.length === 0
  2. [Dòng 6] filteredDataOther.length === 0"
  3. [Dòng 40] filteredDataMobile.length === 0
  4. [Dòng 40] filteredDataOtherMobile.length === 0"

================================================================================

📁 FILE 62: qr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main/qr-main.component.ts
📊 Thống kê: 22 điều kiện duy nhất
   - === : 8 lần
   - == : 8 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 220] this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
  2. [Dòng 221] this.filteredDataOther = this.appList.filter(item => item.type === 'other');
  3. [Dòng 222] this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
  4. [Dòng 223] this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
  5. [Dòng 244] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  6. [Dòng 245] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  7. [Dòng 251] if (item.type === 'mobile_banking') {
  8. [Dòng 420] this.appList.length === 1

== (8 điều kiện):
  1. [Dòng 148] this.themeColor.deeplink_status == 'Off' ? false : true
  2. [Dòng 250] if (item.available == true) {
  3. [Dòng 318] if (_re.status == '200'
  4. [Dòng 318] _re.status == '201') {
  5. [Dòng 321] if (appcode == 'grabpay'
  6. [Dòng 321] appcode == 'momo') {
  7. [Dòng 324] if (type == 2
  8. [Dòng 361] err.error.code == '04') {

!= (6 điều kiện):
  1. [Dòng 166] if (params['locale'] != null) {
  2. [Dòng 172] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 197] if (!(strInstrument != null
  4. [Dòng 281] if (appcode != null) {
  5. [Dòng 395] if (_re.status != '200'
  6. [Dòng 395] _re.status != '201')

================================================================================

📁 FILE 63: safe-html.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main/safe-html.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 64: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 65: qr-desktop.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 1 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 168] listVNPayQR.length === 0"

================================================================================

📁 FILE 66: qr-desktop.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.ts
📊 Thống kê: 19 điều kiện duy nhất
   - === : 4 lần
   - == : 10 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 368] this.listWalletQR.length === 1) {
  2. [Dòng 418] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  3. [Dòng 419] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  4. [Dòng 773] this.listWalletQR.length === 1

== (10 điều kiện):
  1. [Dòng 266] e.type == 'vnpayqr') {
  2. [Dòng 273] e.type == 'ewallet') {
  3. [Dòng 317] if (_re.status == '200'
  4. [Dòng 317] _re.status == '201') {
  5. [Dòng 346] e.type == 'wallet')) {
  6. [Dòng 385] if (d.b.code == s) {
  7. [Dòng 424] if (item.available == true) {
  8. [Dòng 484] if (appcode == 'grabpay'
  9. [Dòng 484] appcode == 'momo') {
  10. [Dòng 517] err.error.code == '04') {

!= (5 điều kiện):
  1. [Dòng 228] if (params['locale'] != null) {
  2. [Dòng 256] if (!(strInstrument != null
  3. [Dòng 442] if (appcode != null) {
  4. [Dòng 747] if (_re.status != '200'
  5. [Dòng 747] _re.status != '201')

================================================================================

📁 FILE 67: qr-dialog-version2.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog-version2.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1] screen=='qr'"
  2. [Dòng 122] screen=='confirm_close'"

================================================================================

📁 FILE 68: qr-dialog-version2.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog-version2.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 49] 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {

================================================================================

📁 FILE 69: qr-guide-dialog-version2.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog-version2.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (3 điều kiện):
  1. [Dòng 5] type == 'vnpay'"
  2. [Dòng 6] type == 'bankapp'"
  3. [Dòng 7] type == 'both'"

================================================================================

📁 FILE 70: qr-guide-dialog-version2.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog-version2.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 71: list-bank-dialog-version2.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog-version2.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 72: list-bank-dialog-version2.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog-version2.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 73: qr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 74: qr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-main.component.ts
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 191] this.themeConfig.deeplink_status == 'Off' ? false : true

!= (2 điều kiện):
  1. [Dòng 207] if (params['locale'] != null) {
  2. [Dòng 213] if ('otp' != this._paymentService.getCurrentPage()) {

================================================================================

📁 FILE 75: qr-mobile.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 134] _locale=='vi'"
  2. [Dòng 135] _locale=='en'"
  3. [Dòng 145] _locale == 'vi'"
  4. [Dòng 147] _locale == 'en'"

!= (2 điều kiện):
  1. [Dòng 234] qr_version2 != 'None'"
  2. [Dòng 260] qr_version2 != 'None'

================================================================================

📁 FILE 76: qr-mobile.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.ts
📊 Thống kê: 30 điều kiện duy nhất
   - === : 6 lần
   - == : 19 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (6 điều kiện):
  1. [Dòng 475] if (!this.d_vnpayqr_wallet && !this.d_vnpayqr_deeplink && (this.listWalletQR?.length === 1
  2. [Dòng 475] this.listWalletDeeplink?.length === 1)) {
  3. [Dòng 594] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  4. [Dòng 595] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  5. [Dòng 601] if (item.type === 'deeplink') {
  6. [Dòng 996] this.listWalletQR?.length === 1

== (19 điều kiện):
  1. [Dòng 282] e.type == 'deeplink') {
  2. [Dòng 293] e.type == 'ewallet'
  3. [Dòng 313] e.type == 'vnpayqr') {
  4. [Dòng 327] e.type == 'wallet')) {
  5. [Dòng 356] e.type == 'ewallet') {
  6. [Dòng 386] if (e.type == 'ewallet') {
  7. [Dòng 409] this.listWallet.length == 1
  8. [Dòng 409] this.listWallet[0].code == 'momo') {
  9. [Dòng 411] this.checkEWalletDeeplink.length == 0) {
  10. [Dòng 492] arrayWallet.length == 0) return false;
  11. [Dòng 494] if (arrayWallet[i].code == key) {
  12. [Dòng 529] if (_re.status == '200'
  13. [Dòng 529] _re.status == '201') {
  14. [Dòng 551] if (d.b.code == s) {
  15. [Dòng 600] if (item.available == true) {
  16. [Dòng 678] if (appcode == 'grabpay'
  17. [Dòng 678] appcode == 'momo') {
  18. [Dòng 681] if (type == 2
  19. [Dòng 721] err.error.code == '04') {

!= (5 điều kiện):
  1. [Dòng 235] if (params['locale'] != null) {
  2. [Dòng 263] if (!(strInstrument != null
  3. [Dòng 628] if (appcode != null) {
  4. [Dòng 966] if (_re.status != '200'
  5. [Dòng 966] _re.status != '201')

================================================================================

📁 FILE 77: safe-html.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/qr-main-version2/safe-html.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 78: queuing.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/queuing/queuing.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 79: queuing.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/queuing/queuing.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 62] if (this.locale == 'vi') {

!= (1 điều kiện):
  1. [Dòng 86] if (this.translate.currentLang != language) {

================================================================================

📁 FILE 80: token-expired-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/token-expired-dialog/token-expired-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 81: token-expired-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/token-expired-dialog/token-expired-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 82: remove-token-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/token-main/remove-token-dialog/remove-token-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 83: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/token-main/token-dialog/dialog-guide-dialog.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (6 điều kiện):
  1. [Dòng 9] data['type'] == 'Visa'
  2. [Dòng 9] data['type'] == 'Master'
  3. [Dòng 9] data['type'] == 'JCB'"
  4. [Dòng 17] data['type'] == 'Visa'"
  5. [Dòng 17] data['type'] == 'Master'"
  6. [Dòng 24] data['type'] == 'Amex'"

================================================================================

📁 FILE 84: token-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/token-main/token-main.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 8] item.brand_id === 'visa' ? 'height: 14.42px

== (2 điều kiện):
  1. [Dòng 14] token_main == '1'
  2. [Dòng 23] token_main == '1'"

!= (2 điều kiện):
  1. [Dòng 16] item['feeService']['fee'] != 0
  2. [Dòng 18] item['feeService']['fee'] != 0"

================================================================================

📁 FILE 85: token-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/main/menu/token-main/token-main.component.ts
📊 Thống kê: 68 điều kiện duy nhất
   - === : 6 lần
   - == : 41 lần
   - !== : 1 lần
   - != : 20 lần
--------------------------------------------------------------------------------

=== (6 điều kiện):
  1. [Dòng 85] if (event.keyCode === 13) {
  2. [Dòng 264] && ((item.brand_id === 'amex'
  3. [Dòng 281] return id === 'amex' ? '1234' : '123'
  4. [Dòng 446] if (approval.method === 'REDIRECT') {
  5. [Dòng 449] } else if (approval.method === 'POST_REDIRECT') {
  6. [Dòng 521] return this._i_token_otp != null && !isNaN(+this._i_token_otp) && ((id === 'amex'

== (41 điều kiện):
  1. [Dòng 142] if (message == '0') {
  2. [Dòng 201] item.id == element.id ? element['active'] = true : element['active'] = false
  3. [Dòng 205] item.display_cvv == true ? this.flagToken = true : this.flagToken = false
  4. [Dòng 247] if (result == 'success') {
  5. [Dòng 252] if (this.tokenList.length == 0) {
  6. [Dòng 256] } else if (result == 'error') {
  7. [Dòng 264] _val.value.length == 4) || (item.brand_id != 'amex'
  8. [Dòng 264] _val.value.length == 3))
  9. [Dòng 274] _val.value.length == 3)));
  10. [Dòng 280] id == 'amex' ? this.maxLength = 4 : this.maxLength = 3
  11. [Dòng 325] if (_re.body.state == 'more_info_required') {
  12. [Dòng 340] if (this._res_post.state == 'approved'
  13. [Dòng 340] this._res_post.state == 'failed') {
  14. [Dòng 347] if (this._res_post.state == 'failed') {
  15. [Dòng 363] } else if (this._res_post.state == 'authorization_required') {
  16. [Dòng 364] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  17. [Dòng 376] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  18. [Dòng 392] } else if (_re.body.state == 'authorization_required') {
  19. [Dòng 409] if (this._b == 1
  20. [Dòng 409] this._b == 14
  21. [Dòng 409] this._b == 15
  22. [Dòng 409] this._b == 24
  23. [Dòng 409] this._b == 8
  24. [Dòng 409] this._b == 10
  25. [Dòng 409] this._b == 20
  26. [Dòng 409] this._b == 22
  27. [Dòng 409] this._b == 23
  28. [Dòng 409] this._b == 30
  29. [Dòng 409] this._b == 11
  30. [Dòng 409] this._b == 17
  31. [Dòng 409] this._b == 18
  32. [Dòng 409] this._b == 27
  33. [Dòng 409] this._b == 5
  34. [Dòng 409] this._b == 12
  35. [Dòng 409] this._b == 9) {
  36. [Dòng 468] } else if (_re.body.state == 'failed') {
  37. [Dòng 513] if (action == 'blur') {
  38. [Dòng 521] this._i_token_otp.length == 4)
  39. [Dòng 522] this._i_token_otp.length == 3));
  40. [Dòng 575] if (_re.status == '200'
  41. [Dòng 575] _re.status == '201') {

!== (1 điều kiện):
  1. [Dòng 191] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (20 điều kiện):
  1. [Dòng 86] this._res.merchant.id != 'OPTEST' ? this._res.on_off_bank : ''
  2. [Dòng 127] if (params['locale'] != null) {
  3. [Dòng 263] if (_val.value != null
  4. [Dòng 273] this.c_token_otp_csc = !(_val.value != null
  5. [Dòng 342] if (this._res_post.return_url != null) {
  6. [Dòng 344] } else if (this._res_post.links != null
  7. [Dòng 344] this._res_post.links.merchant_return != null
  8. [Dòng 344] this._res_post.links.merchant_return.href != null) {
  9. [Dòng 397] if (_re.body.authorization != null
  10. [Dòng 397] _re.body.authorization.links != null
  11. [Dòng 404] if (_re.body.links != null
  12. [Dòng 404] _re.body.links.cancel != null) {
  13. [Dòng 470] if (_re.body.return_url != null) {
  14. [Dòng 472] } else if (_re.body.links != null
  15. [Dòng 472] _re.body.links.merchant_return != null
  16. [Dòng 472] _re.body.links.merchant_return.href != null) {
  17. [Dòng 504] return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
  18. [Dòng 509] if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(item.brand_id) != -1
  19. [Dòng 521] return this._i_token_otp != null
  20. [Dòng 522] || (id != 'amex'

================================================================================

📁 FILE 86: overlay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/overlay/overlay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 87: overlay.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/overlay/overlay.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 88: overlay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/overlay/overlay.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 89: bank-amount.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/pipe/bank-amount.pipe.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 11] return ((a.id == id
  2. [Dòng 11] a.code == id) && a.type.includes(type));

================================================================================

📁 FILE 90: close-dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/services/close-dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 91: data.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/services/data.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 75] const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
  2. [Dòng 75] item.method === method) : null;

================================================================================

📁 FILE 92: deep_link.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/services/deep_link.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 9] if (isIphone == true) {
  2. [Dòng 22] } else if (isAndroid == true) {

================================================================================

📁 FILE 93: dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/services/dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 94: fee.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/services/fee.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 95: multiple_method.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/services/multiple_method.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 96: payment.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/services/payment.service.ts
📊 Thống kê: 14 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 12 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 416] return countPayment == maxPayment
  2. [Dòng 628] if (this.getLatestPayment().state == 'canceled')

!= (12 điều kiện):
  1. [Dòng 112] if (idInvoice != null
  2. [Dòng 112] idInvoice != 0)
  3. [Dòng 122] idInvoice != 0) {
  4. [Dòng 299] if (this._merchantid != null
  5. [Dòng 299] this._tranref != null
  6. [Dòng 299] this._state != null
  7. [Dòng 370] let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
  8. [Dòng 406] urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
  9. [Dòng 444] if (paymentId != null) {
  10. [Dòng 447] if (fee != null) {
  11. [Dòng 543] if (res?.status != 200
  12. [Dòng 543] res?.status != 201) return;

================================================================================

📁 FILE 97: qr.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/services/qr.service.ts
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 58] if (res?.state == 'canceled') {
  2. [Dòng 99] state == 'authorization_required'

!= (3 điều kiện):
  1. [Dòng 40] if (_re.status != '200'
  2. [Dòng 40] _re.status != '201') {
  3. [Dòng 49] latestPayment?.state != "authorization_required") {

================================================================================

📁 FILE 98: time-stop.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/services/time-stop.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 99: token-main.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/services/token-main.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 100: index.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/translate/index.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 101: lang-en.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/translate/lang-en.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 102: lang-vi.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/translate/lang-vi.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 103: translate.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/translate/translate.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 104: translate.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/translate/translate.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 37] if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
  2. [Dòng 38] else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';

================================================================================

📁 FILE 105: translations.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/translate/translations.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 106: apps-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/util/apps-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 486] if (e.name == bankSwift) { // TODO: get by swift
  2. [Dòng 494] return this.apps.find(e => e.code == appCode);

================================================================================

📁 FILE 107: apps-information.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/util/apps-information.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 108: banks-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/util/banks-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1014] if (+e.id == bankId) {
  2. [Dòng 1064] if (e.swiftCode == bankSwift) {

================================================================================

📁 FILE 109: error-handler.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/util/error-handler.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 9] err?.status === 400
  2. [Dòng 10] err?.error?.name === 'INVALID_CARD_FEE'

================================================================================

📁 FILE 110: iso-ca-states.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/util/iso-ca-states.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 111: iso-us-states.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/util/iso-us-states.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 112: util.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/app/util/util.ts
📊 Thống kê: 41 điều kiện duy nhất
   - === : 16 lần
   - == : 18 lần
   - !== : 3 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (16 điều kiện):
  1. [Dòng 57] if (v.length === 2
  2. [Dòng 57] this.flag.length === 3
  3. [Dòng 57] this.flag.charAt(this.flag.length - 1) === '/') {
  4. [Dòng 61] if (v.length === 1) {
  5. [Dòng 63] } else if (v.length === 2) {
  6. [Dòng 66] v.length === 2) {
  7. [Dòng 74] if (len === 2) {
  8. [Dòng 146] if (M[1] === 'Chrome') {
  9. [Dòng 271] if (param === key) {
  10. [Dòng 488] pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
  11. [Dòng 492] target === 0
  12. [Dòng 583] if (cardTypeBank === 'bank_card_number') {
  13. [Dòng 586] } else if (cardTypeBank === 'bank_account_number') {
  14. [Dòng 636] if (event.keyCode === 8
  15. [Dòng 636] event.key === "Backspace"
  16. [Dòng 694] if (target.tagName === 'A'

== (18 điều kiện):
  1. [Dòng 17] if (temp.length == 0) {
  2. [Dòng 24] return (counter % 10 == 0);
  3. [Dòng 28] if (currency == 'USD') {
  4. [Dòng 120] if (this.checkCount == 1) {
  5. [Dòng 132] if (results == null) {
  6. [Dòng 165] if (c.length == 3) {
  7. [Dòng 178] d = d == undefined ? '.' : d
  8. [Dòng 179] t = t == undefined ? '
  9. [Dòng 259] return results == null ? null : results[1]
  10. [Dòng 636] event.inputType == 'deleteContentBackward') {
  11. [Dòng 637] if (event.target.name == 'exp_date'
  12. [Dòng 645] event.inputType == 'insertCompositionText') {
  13. [Dòng 659] if (((_val.length == 4
  14. [Dòng 659] _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  15. [Dòng 659] _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  16. [Dòng 685] iss_date.length == 4
  17. [Dòng 685] iss_date.search('/') == -1)
  18. [Dòng 686] iss_date.length == 5))

!== (3 điều kiện):
  1. [Dòng 266] queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
  2. [Dòng 267] if (queryString !== '') {
  3. [Dòng 492] if (target !== 0

!= (4 điều kiện):
  1. [Dòng 148] if (tem != null) {
  2. [Dòng 153] if ((tem = ua.match(/version\/(\d+)/i)) != null) {
  3. [Dòng 581] if (ua.indexOf('safari') != -1
  4. [Dòng 638] if (v.length != 3) {

================================================================================

📁 FILE 113: qrcode.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/assets/script/qrcode.js
📊 Thống kê: 67 điều kiện duy nhất
   - === : 2 lần
   - == : 53 lần
   - !== : 0 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 2255] if (typeof define === 'function'
  2. [Dòng 2257] } else if (typeof exports === 'object') {

== (53 điều kiện):
  1. [Dòng 68] if (_dataCache == null) {
  2. [Dòng 85] if ( (0 <= r && r <= 6 && (c == 0
  3. [Dòng 85] c == 6) )
  4. [Dòng 86] || (0 <= c && c <= 6 && (r == 0
  5. [Dòng 86] r == 6) )
  6. [Dòng 107] if (i == 0
  7. [Dòng 122] _modules[r][6] = (r % 2 == 0);
  8. [Dòng 129] _modules[6][c] = (c % 2 == 0);
  9. [Dòng 152] if (r == -2
  10. [Dòng 152] r == 2
  11. [Dòng 152] c == -2
  12. [Dòng 152] c == 2
  13. [Dòng 153] || (r == 0
  14. [Dòng 153] c == 0) ) {
  15. [Dòng 169] ( (bits >> i) & 1) == 1);
  16. [Dòng 226] if (col == 6) col -= 1;
  17. [Dòng 232] if (_modules[row][col - c] == null) {
  18. [Dòng 237] dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
  19. [Dòng 249] if (bitIndex == -1) {
  20. [Dòng 458] margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
  21. [Dòng 498] if (typeof arguments[0] == 'object') {
  22. [Dòng 587] margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
  23. [Dòng 733] if (b == -1) throw 'eof';
  24. [Dòng 741] if (b0 == -1) break;
  25. [Dòng 767] if (typeof b == 'number') {
  26. [Dòng 768] if ( (b & 0xff) == b) {
  27. [Dòng 910] return function(i, j) { return (i + j) % 2 == 0
  28. [Dòng 912] return function(i, j) { return i % 2 == 0
  29. [Dòng 914] return function(i, j) { return j % 3 == 0
  30. [Dòng 916] return function(i, j) { return (i + j) % 3 == 0
  31. [Dòng 918] return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
  32. [Dòng 920] return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
  33. [Dòng 922] return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
  34. [Dòng 924] return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
  35. [Dòng 1011] if (r == 0
  36. [Dòng 1011] c == 0) {
  37. [Dòng 1015] if (dark == qrcode.isDark(row + r, col + c) ) {
  38. [Dòng 1036] if (count == 0
  39. [Dòng 1036] count == 4) {
  40. [Dòng 1149] if (typeof num.length == 'undefined') {
  41. [Dòng 1155] num[offset] == 0) {
  42. [Dòng 1495] if (typeof rsBlock == 'undefined') {
  43. [Dòng 1538] return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
  44. [Dòng 1543] _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
  45. [Dòng 1599] if (data.length - i == 1) {
  46. [Dòng 1601] } else if (data.length - i == 2) {
  47. [Dòng 1866] } else if (n == 62) {
  48. [Dòng 1868] } else if (n == 63) {
  49. [Dòng 1928] if (_buflen == 0) {
  50. [Dòng 1937] if (c == '=') {
  51. [Dòng 1961] } else if (c == 0x2b) {
  52. [Dòng 1963] } else if (c == 0x2f) {
  53. [Dòng 2133] if (table.size() == (1 << bitLength) ) {

!= (12 điều kiện):
  1. [Dòng 119] if (_modules[r][6] != null) {
  2. [Dòng 126] if (_modules[6][c] != null) {
  3. [Dòng 144] if (_modules[row][col] != null) {
  4. [Dòng 365] while (buffer.getLengthInBits() % 8 != 0) {
  5. [Dòng 750] if (count != numChars) {
  6. [Dòng 751] throw count + ' != ' + numChars
  7. [Dòng 878] while (data != 0) {
  8. [Dòng 1733] if (test.length != 2
  9. [Dòng 1733] ( (test[0] << 8) | test[1]) != code) {
  10. [Dòng 1894] if (_length % 3 != 0) {
  11. [Dòng 2067] if ( (data >>> length) != 0) {
  12. [Dòng 2178] return typeof _map[key] != 'undefined'

================================================================================

📁 FILE 114: environment.dev.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/environments/environment.dev.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 115: environment.mtf.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/environments/environment.mtf.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 116: environment.prod.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/environments/environment.prod.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 117: environment.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/environments/environment.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 118: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/acc_bidv/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 119: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/acc_bidv/script.js
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 9] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 12] if (inName.value === "") return err("MISSING_FIELD"
  3. [Dòng 21] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 23] if (e !== null) {
  2. [Dòng 55] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 120: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/acc_oceanbank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 121: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/acc_oceanbank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 10] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 19] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 21] if (e !== null) {
  2. [Dòng 53] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 122: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/acc_pvcombank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 123: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/acc_pvcombank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 7] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 16] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 18] if (e !== null) {
  2. [Dòng 50] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 124: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/acc_shb/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 125: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/acc_shb/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 10] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 19] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 21] if (e !== null) {
  2. [Dòng 53] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 126: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/acc_tpbank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 127: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/acc_tpbank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 7] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 16] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 18] if (e !== null) {
  2. [Dòng 50] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 128: common.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/common.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 89] if (i % 2 === parity) d *= 2;
  2. [Dòng 93] return (sum % 10) === 0
  3. [Dòng 163] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 166] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 252] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 258] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 274] if (xhr.status === 200
  8. [Dòng 274] xhr.status === 201) {
  9. [Dòng 277] if (invoiceState === "unpaid"
  10. [Dòng 277] invoiceState === "not_paid") {
  11. [Dòng 282] if (paymentState === "authorization_required") {
  12. [Dòng 288] if (method === "REDIRECT") {
  13. [Dòng 291] } else if (method === "POST_REDIRECT") {
  14. [Dòng 330] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 141] this.oldValue !== this.value) {
  2. [Dòng 313] if (jLinks !== undefined
  3. [Dòng 313] jLinks !== null) {
  4. [Dòng 315] if (jMerchantReturn !== undefined
  5. [Dòng 315] jMerchantReturn !== null) {
  6. [Dòng 330] if (responseCode !== undefined
  7. [Dòng 330] responseCode !== null
  8. [Dòng 358] if (parentRes !== "{

================================================================================

📁 FILE 129: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/domestic_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 130: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/domestic_card/script.js
📊 Thống kê: 14 điều kiện duy nhất
   - === : 11 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 18] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 60] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 63] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 72] year === y
  5. [Dòng 74] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 106] if (inPhone.value === "") return err("MISSING_FIELD"
  7. [Dòng 136] if ("PAY" === operation) {
  8. [Dòng 525] } else if (value === "") {
  9. [Dòng 542] if (trPhone.style.display === "") {
  10. [Dòng 544] } else if (trName.style.display === "") {
  11. [Dòng 565] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 138] if (e !== null) {
  2. [Dòng 404] if (lang !== "vi") lang = "en";
  3. [Dòng 470] if (value !== "") {

================================================================================

📁 FILE 131: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/domestic_token/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 132: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/domestic_token/script.js
📊 Thống kê: 25 điều kiện duy nhất
   - === : 23 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (23 điều kiện):
  1. [Dòng 20] if ("PAY" === operation) {
  2. [Dòng 93] if (trName.style.display === "") {
  3. [Dòng 117] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  4. [Dòng 123] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  5. [Dòng 137] if (xhr.status === 200) {
  6. [Dòng 165] if (insType === "card") {
  7. [Dòng 166] if (insBrandId === "visa"
  8. [Dòng 166] insBrandId === "mastercard"
  9. [Dòng 166] insBrandId === "amex"
  10. [Dòng 166] insBrandId === "jcb"
  11. [Dòng 166] insBrandId === "cup") {
  12. [Dòng 170] } else if (insBrandId === "atm") {
  13. [Dòng 241] } else if (insType === "dongabank_account") {
  14. [Dòng 242] } else if (insType === "techcombank_account") {
  15. [Dòng 243] } else if (insType === "vib_account") {
  16. [Dòng 244] } else if (insType === "bidv_account") {
  17. [Dòng 245] } else if (insType === "tpbank_account") {
  18. [Dòng 246] } else if (insType === "shb_account") {
  19. [Dòng 247] } else if (insType === "shb_customer_id") {
  20. [Dòng 248] } else if (insType === "vpbank_account") {
  21. [Dòng 249] } else if (insType === "oceanbank_online_account") {
  22. [Dòng 250] } else if (insType === "oceanbank_mobile_account") {
  23. [Dòng 251] } else if (insType === "pvcombank_account") {

!== (2 điều kiện):
  1. [Dòng 54] if (lang !== "vi") lang = "en";
  2. [Dòng 273] if (parentRes !== "{

================================================================================

📁 FILE 133: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 134: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/international_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 135: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/international_card/script.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 13] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 23] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 26] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 38] year === y
  5. [Dòng 40] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 71] if ("PAY" === operation) {
  7. [Dòng 161] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 73] if (e !== null) {
  2. [Dòng 118] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 136: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/international_token/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 137: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/international_token/script.js
📊 Thống kê: 36 điều kiện duy nhất
   - === : 25 lần
   - == : 0 lần
   - !== : 11 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (25 điều kiện):
  1. [Dòng 32] year === y
  2. [Dòng 34] if (inCvv.value === "") {
  3. [Dòng 67] if ("PAY" === operation) {
  4. [Dòng 129] } else if (value === "") {
  5. [Dòng 190] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 196] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 209] if (xhr.status === 200) {
  8. [Dòng 245] if (insType === "card") {
  9. [Dòng 246] if (insBrandId === "visa"
  10. [Dòng 246] insBrandId === "mastercard"
  11. [Dòng 246] insBrandId === "amex"
  12. [Dòng 246] insBrandId === "jcb"
  13. [Dòng 246] insBrandId === "cup") {
  14. [Dòng 248] } else if (insBrandId === "atm") {
  15. [Dòng 319] } else if (insType === "dongabank_account") {
  16. [Dòng 320] } else if (insType === "techcombank_account") {
  17. [Dòng 321] } else if (insType === "vib_account") {
  18. [Dòng 322] } else if (insType === "bidv_account") {
  19. [Dòng 323] } else if (insType === "tpbank_account") {
  20. [Dòng 324] } else if (insType === "shb_account") {
  21. [Dòng 325] } else if (insType === "shb_customer_id") {
  22. [Dòng 326] } else if (insType === "vpbank_account") {
  23. [Dòng 327] } else if (insType === "oceanbank_online_account") {
  24. [Dòng 328] } else if (insType === "oceanbank_mobile_account") {
  25. [Dòng 329] } else if (insType === "pvcombank_account") {

!== (11 điều kiện):
  1. [Dòng 18] if (inMonth.value !== ""
  2. [Dòng 21] if (inYear.value !== ""
  3. [Dòng 23] var month = inMonth.value !== ""
  4. [Dòng 24] var year = parseInt("20" + (inYear.value !== ""
  5. [Dòng 35] if ("2D" !== order["vpc_VerType"]) return err("MISSING_FIELD"
  6. [Dòng 71] if (e !== null) {
  7. [Dòng 79] inMonth.value !== tokenInsMonth) instrument["month"] = inMonth.value;
  8. [Dòng 80] inYear.value !== tokenInsYear) instrument["year"] = inYear.value;
  9. [Dòng 81] if (inCvv.value !== "") instrument["cvv"] = inCvv.value;
  10. [Dòng 95] if (lang !== "vi") lang = "en";
  11. [Dòng 351] if (parentRes !== "{

================================================================================

📁 FILE 138: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/amway/script.js
📊 Thống kê: 54 điều kiện duy nhất
   - === : 41 lần
   - == : 1 lần
   - !== : 12 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (41 điều kiện):
  1. [Dòng 4] if ((cardno.length === 15
  2. [Dòng 4] cardno.length === 16
  3. [Dòng 4] cardno.length === 19) && isMode10(cardno) === true) {
  4. [Dòng 4] isMode10(cardno) === true) {
  5. [Dòng 23] if (i % 2 === parity) d *= 2;
  6. [Dòng 27] return (sum % 10) === 0
  7. [Dòng 48] if ("PAY" === operation) {
  8. [Dòng 69] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  9. [Dòng 75] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  10. [Dòng 91] if (xhr.status === 200
  11. [Dòng 91] xhr.status === 201) {
  12. [Dòng 94] if (invoiceState === "unpaid"
  13. [Dòng 94] invoiceState === "not_paid") {
  14. [Dòng 99] if (paymentState === "authorization_required") {
  15. [Dòng 105] if (method === "REDIRECT") {
  16. [Dòng 110] } else if (method === "POST_REDIRECT") {
  17. [Dòng 223] if (typeof queryParams[key] === "undefined") {
  18. [Dòng 226] } else if (typeof queryParams[key] === "string") {
  19. [Dòng 256] if (params["CardList"] === undefined
  20. [Dòng 256] params["CardList"] === null) return;
  21. [Dòng 302] if (xhr.status === 200) {
  22. [Dòng 312] if (insType === "card") {
  23. [Dòng 313] if (insBrandId === "visa"
  24. [Dòng 313] insBrandId === "mastercard"
  25. [Dòng 313] insBrandId === "amex"
  26. [Dòng 313] insBrandId === "jcb"
  27. [Dòng 313] insBrandId === "cup") {
  28. [Dòng 317] } else if (insBrandId === "atm") {
  29. [Dòng 318] if (insSwiftCode === "BFTVVNVX") { //Vietcombank
  30. [Dòng 322] } else if (insSwiftCode === "BIDVVNVX") { //BIDV
  31. [Dòng 329] } else if (insType === "dongabank_account") {
  32. [Dòng 331] } else if (insType === "techcombank_account") {
  33. [Dòng 333] } else if (insType === "vib_account") {
  34. [Dòng 335] } else if (insType === "bidv_account") {
  35. [Dòng 336] } else if (insType === "tpbank_account") {
  36. [Dòng 337] } else if (insType === "shb_account") {
  37. [Dòng 338] } else if (insType === "shb_customer_id") {
  38. [Dòng 339] } else if (insType === "vpbank_account") {
  39. [Dòng 340] } else if (insType === "oceanbank_online_account") {
  40. [Dòng 341] } else if (insType === "oceanbank_mobile_account") {
  41. [Dòng 342] } else if (insType === "pvcombank_account") {

== (1 điều kiện):
  1. [Dòng 270] if (cardList.length == 1) {//TOKEN, BIDV, SHB, OCEAN, PVCOM, TP Bank

!== (12 điều kiện):
  1. [Dòng 52] if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
  2. [Dòng 53] if (inNumber.style.display !== "none") instrument.number = inNumber.value;
  3. [Dòng 54] if (inDate.style.display !== "none") instrument.date = inDate.value;
  4. [Dòng 55] if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
  5. [Dòng 56] if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
  6. [Dòng 57] if (inName.style.display !== "none") instrument.name = inName.value;
  7. [Dòng 132] if (jLinks !== undefined
  8. [Dòng 132] jLinks !== null) {
  9. [Dòng 134] if (jMerchantReturn !== undefined
  10. [Dòng 134] jMerchantReturn !== null) {
  11. [Dòng 166] if (parentRes !== "{
  12. [Dòng 255] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 139: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 140: bidv.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Bidv/assets/js/bidv.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 233] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 239] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 255] if (xhr.status === 200
  8. [Dòng 255] xhr.status === 201) {
  9. [Dòng 258] if (invoiceState === "unpaid"
  10. [Dòng 258] invoiceState === "not_paid") {
  11. [Dòng 263] if (paymentState === "authorization_required") {
  12. [Dòng 269] if (method === "REDIRECT") {
  13. [Dòng 272] } else if (method === "POST_REDIRECT") {
  14. [Dòng 311] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 294] if (jLinks !== undefined
  3. [Dòng 294] jLinks !== null) {
  4. [Dòng 296] if (jMerchantReturn !== undefined
  5. [Dòng 296] jMerchantReturn !== null) {
  6. [Dòng 311] if (responseCode !== undefined
  7. [Dòng 311] responseCode !== null
  8. [Dòng 339] if (parentRes !== "{

================================================================================

📁 FILE 141: bidv2.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Bidv/bidv2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 76] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 88] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 78] if ( $('.circle_v1').css('display') == 'block'
  2. [Dòng 112] if ($('.circle_v2').css('display') == 'block'
  3. [Dòng 112] $('.circle_v3').css('display') == 'block' ) {
  4. [Dòng 302] $('.circle_v1').css('display') == 'block'

!== (3 điều kiện):
  1. [Dòng 90] if (e !== null) {
  2. [Dòng 273] if (lang !== "vi") lang = "en";
  3. [Dòng 305] if (value !== "") {

================================================================================

📁 FILE 142: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Bidv/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 143: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 144: ocean.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Ocean/assets/js/ocean.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 232] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 237] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 251] if (xhr.status === 200
  8. [Dòng 251] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 257] if (paymentState === "authorization_required") {
  12. [Dòng 261] if (method === "REDIRECT") {
  13. [Dòng 264] } else if (method === "POST_REDIRECT") {
  14. [Dòng 303] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 286] if (jLinks !== undefined
  3. [Dòng 286] jLinks !== null) {
  4. [Dòng 288] if (jMerchantReturn !== undefined
  5. [Dòng 288] jMerchantReturn !== null) {
  6. [Dòng 303] if (responseCode !== undefined
  7. [Dòng 303] responseCode !== null
  8. [Dòng 331] if (parentRes !== "{

================================================================================

📁 FILE 145: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Ocean/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 146: ocean2.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Ocean/ocean2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 72] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 92] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 74] document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM'
  2. [Dòng 116] if ( document.getElementsByClassName("select_online")[0].value == 'Easy Online Banking') {
  3. [Dòng 119] if ( document.getElementsByClassName("select_online")[0].value == 'Easy Mobile Banking') {
  4. [Dòng 304] if ( document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM') {

!== (3 điều kiện):
  1. [Dòng 94] if (e !== null) {
  2. [Dòng 276] if (lang !== "vi") lang = "en";
  3. [Dòng 306] if (value !== "") {

================================================================================

📁 FILE 147: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 148: pvbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Pv_Bank/assets/js/pvbank.js
📊 Thống kê: 23 điều kiện duy nhất
   - === : 14 lần
   - == : 1 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 239] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 261] if (xhr.status === 200
  8. [Dòng 261] xhr.status === 201) {
  9. [Dòng 264] if (invoiceState === "unpaid"
  10. [Dòng 264] invoiceState === "not_paid") {
  11. [Dòng 269] if (paymentState === "authorization_required") {
  12. [Dòng 275] if (method === "REDIRECT") {
  13. [Dòng 278] } else if (method === "POST_REDIRECT") {
  14. [Dòng 317] responseCode === "0") {

== (1 điều kiện):
  1. [Dòng 167] if ($('.circle_v1').css('display') == 'block') {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 300] if (jLinks !== undefined
  3. [Dòng 300] jLinks !== null) {
  4. [Dòng 302] if (jMerchantReturn !== undefined
  5. [Dòng 302] jMerchantReturn !== null) {
  6. [Dòng 317] if (responseCode !== undefined
  7. [Dòng 317] responseCode !== null
  8. [Dòng 345] if (parentRes !== "{

================================================================================

📁 FILE 149: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Pv_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 150: pvbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Pv_Bank/pvbank2.js
📊 Thống kê: 15 điều kiện duy nhất
   - === : 8 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 71] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 76] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 79] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 88] year === y
  5. [Dòng 90] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 100] if ("PAY" === operation) {
  7. [Dòng 347] } else if (value === "") {
  8. [Dòng 364] if (trName.style.display === "") {

== (4 điều kiện):
  1. [Dòng 73] if ($('.circle_v1').css('display') == 'block'
  2. [Dòng 75] $('.circle_v1').css('display') == 'block'
  3. [Dòng 124] if ( $('.circle_v1').css('display') == 'block') {
  4. [Dòng 129] else if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 102] if (e !== null) {
  2. [Dòng 286] if (lang !== "vi") lang = "en";
  3. [Dòng 316] if (value !== "") {

================================================================================

📁 FILE 151: Sea2.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/SeaBank/Sea2.js
📊 Thống kê: 11 điều kiện duy nhất
   - === : 8 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 23] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 31] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 34] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 43] year === y
  5. [Dòng 45] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 55] if ("PAY" === operation) {
  7. [Dòng 287] } else if (value === "") {
  8. [Dòng 304] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 57] if (e !== null) {
  2. [Dòng 233] if (lang !== "vi") lang = "en";
  3. [Dòng 263] if (value !== "") {

================================================================================

📁 FILE 152: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 153: Sea.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/SeaBank/assets/js/Sea.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 228] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 234] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 258] if (paymentState === "authorization_required") {
  12. [Dòng 264] if (method === "REDIRECT") {
  13. [Dòng 267] } else if (method === "POST_REDIRECT") {
  14. [Dòng 306] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 289] if (jLinks !== undefined
  3. [Dòng 289] jLinks !== null) {
  4. [Dòng 291] if (jMerchantReturn !== undefined
  5. [Dòng 291] jMerchantReturn !== null) {
  6. [Dòng 306] if (responseCode !== undefined
  7. [Dòng 306] responseCode !== null
  8. [Dòng 334] if (parentRes !== "{

================================================================================

📁 FILE 154: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/SeaBank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 155: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 156: shb.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Shb/assets/js/shb.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 234] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 240] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 256] if (xhr.status === 200
  8. [Dòng 256] xhr.status === 201) {
  9. [Dòng 259] if (invoiceState === "unpaid"
  10. [Dòng 259] invoiceState === "not_paid") {
  11. [Dòng 264] if (paymentState === "authorization_required") {
  12. [Dòng 270] if (method === "REDIRECT") {
  13. [Dòng 273] } else if (method === "POST_REDIRECT") {
  14. [Dòng 312] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 295] if (jLinks !== undefined
  3. [Dòng 295] jLinks !== null) {
  4. [Dòng 297] if (jMerchantReturn !== undefined
  5. [Dòng 297] jMerchantReturn !== null) {
  6. [Dòng 312] if (responseCode !== undefined
  7. [Dòng 312] responseCode !== null
  8. [Dòng 340] if (parentRes !== "{

================================================================================

📁 FILE 157: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Shb/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 158: shb2.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Shb/shb2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 73] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 85] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 74] if ($('.circle_v2').css('display') == 'block'
  2. [Dòng 110] if ($('.circle_v1').css('display') == 'block'
  3. [Dòng 114] if ( $('.circle_v3').css('display') == 'block' ) {
  4. [Dòng 299] if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 87] if (e !== null) {
  2. [Dòng 271] if (lang !== "vi") lang = "en";
  3. [Dòng 301] if (value !== "") {

================================================================================

📁 FILE 159: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 160: tpbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Tp_Bank/assets/js/tpbank.js
📊 Thống kê: 23 điều kiện duy nhất
   - === : 14 lần
   - == : 1 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 239] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 261] if (xhr.status === 200
  8. [Dòng 261] xhr.status === 201) {
  9. [Dòng 264] if (invoiceState === "unpaid"
  10. [Dòng 264] invoiceState === "not_paid") {
  11. [Dòng 269] if (paymentState === "authorization_required") {
  12. [Dòng 275] if (method === "REDIRECT") {
  13. [Dòng 278] } else if (method === "POST_REDIRECT") {
  14. [Dòng 317] responseCode === "0") {

== (1 điều kiện):
  1. [Dòng 167] if ($('.circle_v1').css('display') == 'block') {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 300] if (jLinks !== undefined
  3. [Dòng 300] jLinks !== null) {
  4. [Dòng 302] if (jMerchantReturn !== undefined
  5. [Dòng 302] jMerchantReturn !== null) {
  6. [Dòng 317] if (responseCode !== undefined
  7. [Dòng 317] responseCode !== null
  8. [Dòng 345] if (parentRes !== "{

================================================================================

📁 FILE 161: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Tp_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 162: tpbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Tp_Bank/tpbank2.js
📊 Thống kê: 15 điều kiện duy nhất
   - === : 8 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 71] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 78] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 81] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 90] year === y
  5. [Dòng 92] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 102] if ("PAY" === operation) {
  7. [Dòng 349] } else if (value === "") {
  8. [Dòng 366] if (trName.style.display === "") {

== (4 điều kiện):
  1. [Dòng 73] if ($('.circle_v1').css('display') == 'block'
  2. [Dòng 76] $('.circle_v1').css('display') == 'block'
  3. [Dòng 126] if ( $('.circle_v1').css('display') == 'block') {
  4. [Dòng 131] else if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 104] if (e !== null) {
  2. [Dòng 288] if (lang !== "vi") lang = "en";
  3. [Dòng 318] if (value !== "") {

================================================================================

📁 FILE 163: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 164: onepay.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Visa/assets/js/onepay.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 78] if (i % 2 === parity) d *= 2;
  2. [Dòng 82] return (sum % 10) === 0
  3. [Dòng 152] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 155] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 240] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 259] if (xhr.status === 200
  8. [Dòng 259] xhr.status === 201) {
  9. [Dòng 261] if (invoiceState === "unpaid"
  10. [Dòng 261] invoiceState === "not_paid") {
  11. [Dòng 265] if (paymentState === "authorization_required") {
  12. [Dòng 269] if (method === "REDIRECT") {
  13. [Dòng 272] } else if (method === "POST_REDIRECT") {
  14. [Dòng 311] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 130] this.oldValue !== this.value) {
  2. [Dòng 294] if (jLinks !== undefined
  3. [Dòng 294] jLinks !== null) {
  4. [Dòng 296] if (jMerchantReturn !== undefined
  5. [Dòng 296] jMerchantReturn !== null) {
  6. [Dòng 311] if (responseCode !== undefined
  7. [Dòng 311] responseCode !== null
  8. [Dòng 339] if (parentRes !== "{

================================================================================

📁 FILE 165: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Visa/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 166: index.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Visa/index.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 19] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 29] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 32] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 44] year === y
  5. [Dòng 46] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 77] if ("PAY" === operation) {
  7. [Dòng 168] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 79] if (e !== null) {
  2. [Dòng 125] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 167: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 168: vpbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Vp_Bank/assets/js/vpbank.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 230] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 236] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 252] if (xhr.status === 200
  8. [Dòng 252] xhr.status === 201) {
  9. [Dòng 255] if (invoiceState === "unpaid"
  10. [Dòng 255] invoiceState === "not_paid") {
  11. [Dòng 260] if (paymentState === "authorization_required") {
  12. [Dòng 266] if (method === "REDIRECT") {
  13. [Dòng 269] } else if (method === "POST_REDIRECT") {
  14. [Dòng 308] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 291] if (jLinks !== undefined
  3. [Dòng 291] jLinks !== null) {
  4. [Dòng 293] if (jMerchantReturn !== undefined
  5. [Dòng 293] jMerchantReturn !== null) {
  6. [Dòng 308] if (responseCode !== undefined
  7. [Dòng 308] responseCode !== null
  8. [Dòng 336] if (parentRes !== "{

================================================================================

📁 FILE 169: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Vp_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 170: vpbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/Vp_Bank/vpbank2.js
📊 Thống kê: 14 điều kiện duy nhất
   - === : 11 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 32] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 35] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 44] year === y
  5. [Dòng 46] if (inPhone.value === "") return err("MISSING_FIELD"
  6. [Dòng 49] if (inName.value === "") return err("MISSING_FIELD"
  7. [Dòng 59] if ("PAY" === operation) {
  8. [Dòng 292] } else if (value === "") {
  9. [Dòng 309] if (trPhone.style.display === "") {
  10. [Dòng 311] } else if (trName.style.display === "") {
  11. [Dòng 332] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 61] if (e !== null) {
  2. [Dòng 237] if (lang !== "vi") lang = "en";
  3. [Dòng 267] if (value !== "") {

================================================================================

📁 FILE 171: sha.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/assets/js/sha.js
📊 Thống kê: 49 điều kiện duy nhất
   - === : 38 lần
   - == : 0 lần
   - !== : 11 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (38 điều kiện):
  1. [Dòng 12] 1>t)throw Error("numRounds must a integer >= 1");if("SHA-1"===c)m=512,q=K,u=Z,f=160,r=function(a){return a.slice()};else if(0===c.lastIndexOf("SHA-",0))if(q=function(a,b){return L(a,b,c)
  2. [Dòng 12] c)},u=function(a,b,h,e){var k,f;if("SHA-224"===c
  3. [Dòng 12] "SHA-256"===c)k=(b+65>>>9<<4)+15,f=16;else if("SHA-384"===c
  4. [Dòng 12] "SHA-512"===c)k=(b+129>>>10<<
  5. [Dòng 13] c);if("SHA-224"===c)a=[e[0],e[1],e[2],e[3],e[4],e[5],e[6]];else if("SHA-256"===c)a=e;else if("SHA-384"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,e[4].b,e[5].a,e[5].b];else if("SHA-512"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,
  6. [Dòng 14] "SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)
  7. [Dòng 14] 0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
  8. [Dòng 14] return a},r=function(a){return a.slice()},"SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)||0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
  9. [Dòng 15] c)m=1152,f=224;else if("SHA3-256"===c)m=1088,f=256;else if("SHA3-384"===c)m=832,f=384;else if("SHA3-512"===c)m=576,f=512;else if("SHAKE128"===c)m=1344,f=-1,F=31,z=!0;else if("SHAKE256"===c)m=1088,f=-1,F=31,z=!0;else throw Error("Chosen SHA variant is not supported");u=function(a
  10. [Dòng 16] 0===64*l%e
  11. [Dòng 16] 5][l/5|0];g.push(a.b);if(32*g.length>=h)break;g.push(a.a);l+=1;0===64*l%e&&D(null,b)}return g}}else throw Error("Chosen SHA variant is not supported");d=M(a,g,x);l=A(c);this.setHMACKey=function(a,b,h){var k;if(!0===I)throw Error("HMAC key already set");if(!0===y)throw Error("Cannot set HMAC key after calling update");if(!0===z)throw Error("SHAKE is not supported for HMAC");g=(h
  12. [Dòng 17] f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
  13. [Dòng 17] )b.push(0);b[h]&=4294967040}for(a=0;a<=h;a+=1)v[a]=b[a]^909522486,w[a]=b[a]^1549556828;l=q(v,l);e=m;I=!0};this.update=function(a){var c,b,k,f=0,g=m>>>5;c=d(a,h,n);a=c.binLen;b=c.value;c=a>>>5;for(k=0;k<c;k+=g)f+m<=a&&(l=q(b.slice(k,k+g),l),f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
  14. [Dòng 18] for(g=1;g<t;g+=1)!0===z
  15. [Dòng 19] f);return k(m)};this.getHMAC=function(a,b){var k,g,d,p;if(!1===I)throw Error("Cannot call getHMAC without first setting HMAC key");d=N(b);switch(a){case "HEX":k=function(a){return O(a,f,x,d)};break;case "B64":k=function(a){return P(a,f,x,d)};break;case "BYTES":k=function(a){return Q(a,f,x)};break;case "ARRAYBUFFER":try{k=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}k=function(a){return R(a,f,x)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
  16. [Dòng 20] d=-1===b?3:0
  17. [Dòng 20] f=-1===b?3:0
  18. [Dòng 21] g=-1===b?3:0
  19. [Dòng 22] !0===c.hasOwnProperty("b64Pad")
  20. [Dòng 22] a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");
  21. [Dòng 23] u=-1===b?3:0
  22. [Dòng 23] l+=2){p=parseInt(a.substr(l,2),16);if(isNaN(p))throw Error("String of HEX type contains invalid characters");m=(l>>>1)+q;for(f=m>>>2;c.length<=f;)c.push(0);c[f]|=p<<8*(u+m%4*b)}return{value:c,binLen:4*g+d}};break;case "TEXT":c=function(c,h,d){var g,l,p=0,f,m,q,u,r,t;h=h||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(t=-1===
  23. [Dòng 24] m+=1){r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=l[m]<<8*(t+r%4*b);p+=1}else if("UTF16BE"===a
  24. [Dòng 24] "UTF16LE"===a)for(t=-1===b?2:0
  25. [Dòng 24] UTF16LE"===a
  26. [Dòng 24] 1===b
  27. [Dòng 25] !0===l
  28. [Dòng 25] !0===l&&(m=g&255,g=m<<8|g>>>8);r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=g<<8*(t+r%4*b);p+=2}return{value:h,binLen:8*p+d}};break;case "B64":c=function(a,c,d){var g=0,l,p,f,m,q,u,r,t;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");p=a.indexOf("=");a=a.replace(/\=/g
  29. [Dòng 25] t=-1===b?3:0
  30. [Dòng 26] q=-1===b?3:0
  31. [Dòng 27] m=-1===b?3:0
  32. [Dòng 32] c.b^a.b)}function A(c){var a=[],d;if("SHA-1"===c)a=[1732584193,4023233417,2562383102,271733878,3285377520];else if(0===c.lastIndexOf("SHA-",0))switch(a=
  33. [Dòng 34] new b(d[2],4271175723),new b(d[3],1595750129),new b(d[4],2917565137),new b(d[5],725511199),new b(d[6],4215389547),new b(d[7],327033209)];break;default:throw Error("Unknown SHA variant");}else if(0===c.lastIndexOf("SHA3-",0)
  34. [Dòng 34] 0===c.lastIndexOf("SHAKE",0))for(c=0
  35. [Dòng 36] a,k){var e,h,n,g,l,p,f,m,q,u,r,t,v,w,y,A,z,x,F,B,C,D,E=[],J;if("SHA-224"===k
  36. [Dòng 36] "SHA-256"===k)u=64,t=1,D=Number,v=G,w=la,y=H,A=ha,z=ja,x=da,F=fa,C=U,B=aa,J=d;else if("SHA-384"===k
  37. [Dòng 36] "SHA-512"===k)u=80,t=2,D=b,v=ma,w=na,y=oa,A=ia,z=ka,x=ea,F=ga,C=ca,B=ba,J=V;else throw Error("Unexpected error in SHA-2 implementation");k=a[0];e=a[1];h=a[2];n=a[3];g=a[4];l=a[5];p=a[6];f=a[7];for(r=0
  38. [Dòng 45] "function"===typeof define

!== (11 điều kiện):
  1. [Dòng 12] 'use strict';(function(Y){function C(c,a,b){var e=0,h=[],n=0,g,l,d,f,m,q,u,r,I=!1,v=[],w=[],t,y=!1,z=!1,x=-1;b=b||{};g=b.encoding||"UTF8";t=b.numRounds||1;if(t!==parseInt(t,10)
  2. [Dòng 18] 0!==f%32
  3. [Dòng 22] a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8
  4. [Dòng 23] }switch(c){case "HEX":c=function(a,c,d){var g=a.length,l,p,f,m,q,u;if(0!==g%2)throw Error("String of HEX type must be in byte increments");c=c||[0];d=d||0;q=d>>>3;u=-1===b?3:0;for(l=0
  5. [Dòng 24] 1!==b
  6. [Dòng 24] "UTF16LE"!==a
  7. [Dòng 25] "");if(-1!==p
  8. [Dòng 27] function(a,c,d){var g,l,p,f,m,q;c=c||[0];d=d||0;l=d>>>3;m=-1===b?3:0;q=new Uint8Array(a);for(g=0;g<a.byteLength;g+=1)f=g+l,p=f>>>2,c.length<=p&&c.push(0),c[p]|=q[g]<<8*(m+f%4*b);return{value:c,binLen:8*a.byteLength+d}};break;default:throw Error("format must be HEX, TEXT, B64, BYTES, or ARRAYBUFFER");}return c}function y(c,a){return c<<a|c>>>32-a}function S(c,a){return 32<a?(a-=32,new b(c.b<<a|c.a>>>32-a,c.a<<a|c.b>>>32-a)):0!==a?new b(c.a<<a|c.b>>>32-a,c.b<<a|c.a>>>32-a):c}function w(c,a){return c>>>
  9. [Dòng 37] a[7]);return a}function D(c,a){var d,e,h,n,g=[],l=[];if(null!==c)for(e=0
  10. [Dòng 45] define.amd?define(function(){return C}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=C),exports=C):Y.jsSHA=C})(this);
  11. [Dòng 45] return C}):"undefined"!==typeof exports?("undefined"!==typeof module

================================================================================

📁 FILE 172: sha256.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/assets/js/sha256.js
📊 Thống kê: 28 điều kiện duy nhất
   - === : 20 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (20 điều kiện):
  1. [Dòng 12] 1>u)throw Error("numRounds must a integer >= 1");if(0===c.lastIndexOf("SHA-",0))if(q=function(b,a){return A(b,a,c)
  2. [Dòng 12] c)},y=function(b,a,l,f){var g,e;if("SHA-224"===c
  3. [Dòng 12] "SHA-256"===c)g=(a+65>>>9<<4)+15,e=16;else throw Error("Unexpected error in SHA-2 implementation");for(
  4. [Dòng 13] c);if("SHA-224"===c)b=[f[0],f[1],f[2],f[3],f[4],f[5],f[6]];else if("SHA-256"===c)b=f;else throw Error("Unexpected error in SHA-2 implementation");return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
  5. [Dòng 13] "SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a
  6. [Dòng 13] return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
  7. [Dòng 14] if(!0===z)throw Error("Cannot set HMAC key after calling update");f=(g
  8. [Dòng 15] 5);g=a%h;z=!0};this.getHash=function(a,f){var d,h,k,q;if(!0===m)throw Error("Cannot call getHash after setting HMAC key");k=C(f);switch(a){case "HEX":d=function(a){return D(a,e,k)};break;case "B64":d=function(a){return E(a,e,k)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{h=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("format must be HEX, B64, BYTES, or ARRAYBUFFER");
  9. [Dòng 16] x(c));return d(q)};this.getHMAC=function(a,f){var d,k,t,u;if(!1===m)throw Error("Cannot call getHMAC without first setting HMAC key");t=C(f);switch(a){case "HEX":d=function(a){return D(a,e,t)};break;case "B64":d=function(a){return E(a,e,t)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{d=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
  10. [Dòng 18] !0===c.hasOwnProperty("b64Pad")
  11. [Dòng 20] h=(d>>>1)+q;for(e=h>>>2;b.length<=e;)b.push(0);b[e]|=k<<8*(3+h%4*-1)}return{value:b,binLen:4*f+c}};break;case "TEXT":d=function(c,b,d){var f,n,k=0,e,h,q,m,p,r;b=b||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(r=3
  12. [Dòng 21] q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=n[h]<<8*(r+p%4*-1);k+=1}else if("UTF16BE"===a
  13. [Dòng 21] "UTF16LE"===a)for(r=2
  14. [Dòng 21] UTF16LE"===a
  15. [Dòng 21] !0===n
  16. [Dòng 21] e+=1){f=c.charCodeAt(e);!0===n&&(h=f&255,f=h<<8|f>>>8);p=k+q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=f<<8*(r+p%4*-1);k+=2}return{value:b,binLen:8*k+d}};break;case "B64":d=function(a,b,c){var f=0,d,k,e,h,q,m,p;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");k=a.indexOf("=");a=a.replace(/\=/g
  17. [Dòng 25] 16)&65535)<<16|b&65535}function R(c,a,d,l,b){var g=(c&65535)+(a&65535)+(d&65535)+(l&65535)+(b&65535);return((c>>>16)+(a>>>16)+(d>>>16)+(l>>>16)+(b>>>16)+(g>>>16)&65535)<<16|g&65535}function x(c){var a=[],d;if(0===c.lastIndexOf("SHA-",0))switch(a=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428]
  18. [Dòng 26] new m,new m,new m,new m,new m,new m];break;case "SHA-512":a=[new m,new m,new m,new m,new m,new m,new m,new m];break;default:throw Error("Unknown SHA variant");}else throw Error("No SHA variants supported");return a}function A(c,a,d){var l,b,g,f,n,k,e,h,m,r,p,w,t,x,u,z,A,B,C,D,E,F,v=[],G;if("SHA-224"===d
  19. [Dòng 26] "SHA-256"===d)r=64,w=1,F=Number,t=P,x=Q,u=R,z=N,A=O,B=L,C=M,E=K,D=J,G=H;else throw Error("Unexpected error in SHA-2 implementation");d=a[0];l=a[1];b=a[2];g=a[3];f=a[4];n=a[5];k=a[6];e=a[7];for(p=
  20. [Dòng 29] "function"===typeof define

!== (8 điều kiện):
  1. [Dòng 12] 'use strict';(function(I){function w(c,a,d){var l=0,b=[],g=0,f,n,k,e,h,q,y,p,m=!1,t=[],r=[],u,z=!1;d=d||{};f=d.encoding||"UTF8";u=d.numRounds||1;if(u!==parseInt(u,10)
  2. [Dòng 18] l+=1)g[l]=c[l>>>2]>>>8*(3+l%4*-1)&255;return b}function C(c){var a={outputUpper:!1,b64Pad:"=",shakeLen:-1};c=c||{};a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");
  3. [Dòng 19] if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0;d<f;d+=2){k=parseInt(a.substr(d,2),16);if(isNaN(k))throw Error("String of HEX type contains invalid characters");
  4. [Dòng 19] if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0
  5. [Dòng 21] "UTF16LE"!==a
  6. [Dòng 22] "");if(-1!==k
  7. [Dòng 29] define.amd?define(function(){return w}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=w),exports=w):I.jsSHA=w})(this);
  8. [Dòng 29] return w}):"undefined"!==typeof exports?("undefined"!==typeof module

================================================================================

📁 FILE 173: slick.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/assets/libraries/slick/slick.js
📊 Thống kê: 182 điều kiện duy nhất
   - === : 126 lần
   - == : 5 lần
   - !== : 47 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (126 điều kiện):
  1. [Dòng 20] if (typeof define === 'function'
  2. [Dòng 206] if (typeof(index) === 'boolean') {
  3. [Dòng 215] if (typeof(index) === 'number') {
  4. [Dòng 216] if (index === 0
  5. [Dòng 216] _.$slides.length === 0) {
  6. [Dòng 224] if (addBefore === true) {
  7. [Dòng 249] if (_.options.slidesToShow === 1
  8. [Dòng 249] _.options.adaptiveHeight === true
  9. [Dòng 249] _.options.vertical === false) {
  10. [Dòng 264] if (_.options.rtl === true
  11. [Dòng 267] if (_.transformsEnabled === false) {
  12. [Dòng 268] if (_.options.vertical === false) {
  13. [Dòng 280] if (_.cssTransitions === false) {
  14. [Dòng 281] if (_.options.rtl === true) {
  15. [Dòng 355] typeof asNavFor === 'object' ) {
  16. [Dòng 371] if (_.options.fade === false) {
  17. [Dòng 414] if ( _.options.infinite === false ) {
  18. [Dòng 416] if ( _.direction === 1
  19. [Dòng 416] ( _.currentSlide + 1 ) === ( _.slideCount - 1 )) {
  20. [Dòng 420] else if ( _.direction === 0 ) {
  21. [Dòng 424] if ( _.currentSlide - 1 === 0 ) {
  22. [Dòng 442] if (_.options.arrows === true ) {
  23. [Dòng 487] if (_.options.dots === true
  24. [Dòng 524] _.$slideTrack = (_.slideCount === 0) ?
  25. [Dòng 532] if (_.options.centerMode === true
  26. [Dòng 532] _.options.swipeToSlide === true) {
  27. [Dòng 547] _.setSlideClasses(typeof _.currentSlide === 'number' ? _.currentSlide : 0);
  28. [Dòng 549] if (_.options.draggable === true) {
  29. [Dòng 602] if (_.respondTo === 'window') {
  30. [Dòng 604] } else if (_.respondTo === 'slider') {
  31. [Dòng 606] } else if (_.respondTo === 'min') {
  32. [Dòng 618] if (_.originalSettings.mobileFirst === false) {
  33. [Dòng 635] if (_.breakpointSettings[targetBreakpoint] === 'unslick') {
  34. [Dòng 641] if (initial === true) {
  35. [Dòng 705] slideOffset = indexOffset === 0 ? _.options.slidesToScroll : _.options.slidesToShow - indexOffset
  36. [Dòng 712] slideOffset = indexOffset === 0 ? _.options.slidesToScroll : indexOffset
  37. [Dòng 719] var index = event.data.index === 0 ? 0 :
  38. [Dòng 765] if (_.options.accessibility === true) {
  39. [Dòng 772] if (_.options.arrows === true
  40. [Dòng 797] if (_.options.focusOnSelect === true) {
  41. [Dòng 836] if (_.shouldClick === false) {
  42. [Dòng 1051] if (_.options.infinite === true) {
  43. [Dòng 1061] } else if (_.options.centerMode === true) {
  44. [Dòng 1094] if (_.options.vertical === true
  45. [Dòng 1094] _.options.centerMode === true) {
  46. [Dòng 1095] if (_.options.slidesToShow === 2) {
  47. [Dòng 1097] } else if (_.options.slidesToShow === 1) {
  48. [Dòng 1128] } else if (_.options.centerMode === true
  49. [Dòng 1128] _.options.infinite === true) {
  50. [Dòng 1141] if (_.options.variableWidth === true) {
  51. [Dòng 1143] _.options.infinite === false) {
  52. [Dòng 1159] if (_.options.centerMode === true) {
  53. [Dòng 1200] if (_.options.infinite === false) {
  54. [Dòng 1229] centerOffset = _.options.centerMode === true ? _.slideWidth * Math.floor(_.options.slidesToShow / 2) : 0
  55. [Dòng 1231] if (_.options.swipeToSlide === true) {
  56. [Dòng 1406] _.options.pauseOnDotsHover === true
  57. [Dòng 1498] if (event.keyCode === 37
  58. [Dòng 1498] _.options.accessibility === true) {
  59. [Dòng 1501] message: _.options.rtl === true ? 'next' :
  60. [Dòng 1504] } else if (event.keyCode === 39
  61. [Dòng 1507] message: _.options.rtl === true ? 'previous' : 'next'
  62. [Dòng 1585] if (_.options.fade === true) {
  63. [Dòng 1593] if (_.options.lazyLoad === 'anticipated') {
  64. [Dòng 1616] } else if (_.currentSlide === 0) {
  65. [Dòng 1637] if (_.options.lazyLoad === 'progressive') {
  66. [Dòng 1773] if ( _.options.adaptiveHeight === true ) {
  67. [Dòng 1864] if ( $.type(responsiveSettings) === 'array'
  68. [Dòng 1878] _.breakpoints[l] === currentBreakpoint ) {
  69. [Dòng 1969] index = removeBefore === true ? 0 : _.slideCount - 1
  70. [Dòng 1971] index = removeBefore === true ? --index : index
  71. [Dòng 1980] if (removeAll === true) {
  72. [Dòng 2050] if (_.options.vertical === false
  73. [Dòng 2050] _.options.variableWidth === false) {
  74. [Dòng 2054] } else if (_.options.variableWidth === true) {
  75. [Dòng 2062] if (_.options.variableWidth === false) _.$slideTrack.children('.slick-slide').width(_.slideWidth - offset);
  76. [Dòng 2128] if( $.type( arguments[0] ) === 'object' ) {
  77. [Dòng 2134] } else if ( $.type( arguments[0] ) === 'string' ) {
  78. [Dòng 2140] if ( arguments[0] === 'responsive'
  79. [Dòng 2140] $.type( arguments[1] ) === 'array' ) {
  80. [Dòng 2152] if ( type === 'single' ) {
  81. [Dòng 2157] } else if ( type === 'multiple' ) {
  82. [Dòng 2166] } else if ( type === 'responsive' ) {
  83. [Dòng 2181] if( _.options.responsive[l].breakpoint === value[item].breakpoint ) {
  84. [Dòng 2231] _.positionProp = _.options.vertical === true ? 'top' : 'left'
  85. [Dòng 2233] if (_.positionProp === 'top') {
  86. [Dòng 2242] if (_.options.useCSS === true) {
  87. [Dòng 2248] if ( typeof _.options.zIndex === 'number' ) {
  88. [Dòng 2261] if (bodyStyle.perspectiveProperty === undefined
  89. [Dòng 2261] bodyStyle.webkitPerspective === undefined) _.animType = false;
  90. [Dòng 2267] bodyStyle.MozPerspective === undefined) _.animType = false;
  91. [Dòng 2279] if (bodyStyle.msTransform === undefined) _.animType = false;
  92. [Dòng 2306] var evenCoef = _.options.slidesToShow % 2 === 0 ? 1 : 0
  93. [Dòng 2328] if (index === 0) {
  94. [Dòng 2334] } else if (index === _.slideCount - 1) {
  95. [Dòng 2366] indexOffset = _.options.infinite === true ? _.options.slidesToShow + index : index
  96. [Dòng 2388] if (_.options.lazyLoad === 'ondemand'
  97. [Dòng 2388] _.options.lazyLoad === 'anticipated') {
  98. [Dòng 2402] if (_.options.infinite === true
  99. [Dòng 2402] _.options.fade === false) {
  100. [Dòng 2479] if (_.animating === true
  101. [Dòng 2479] _.options.waitForAnimate === true) {
  102. [Dòng 2483] if (_.options.fade === true
  103. [Dòng 2483] _.currentSlide === index) {
  104. [Dòng 2487] if (sync === false) {
  105. [Dòng 2495] _.currentLeft = _.swipeLeft === null ? slideLeft : _.swipeLeft
  106. [Dòng 2497] if (_.options.infinite === false
  107. [Dòng 2497] _.options.centerMode === false
  108. [Dòng 2509] } else if (_.options.infinite === false
  109. [Dòng 2509] _.options.centerMode === true
  110. [Dòng 2627] return (_.options.rtl === false ? 'left' : 'right');
  111. [Dòng 2633] return (_.options.rtl === false ? 'right' : 'left');
  112. [Dòng 2635] if (_.options.verticalSwiping === true) {
  113. [Dòng 2664] if ( _.touchObject.curX === undefined ) {
  114. [Dòng 2668] if ( _.touchObject.edgeHit === true ) {
  115. [Dòng 2732] if ((_.options.swipe === false) || ('ontouchend' in document
  116. [Dòng 2732] _.options.swipe === false)) {
  117. [Dòng 2734] } else if (_.options.draggable === false
  118. [Dòng 2806] positionOffset = (_.options.rtl === false ? 1 : -1) * (_.touchObject.curX > _.touchObject.startX ? 1 : -1);
  119. [Dòng 2817] if ((_.currentSlide === 0
  120. [Dòng 2817] swipeDirection === 'right') || (_.currentSlide >= _.getDotCount()
  121. [Dòng 2817] swipeDirection === 'left')) {
  122. [Dòng 2832] _.options.touchMove === false) {
  123. [Dòng 2836] if (_.animating === true) {
  124. [Dòng 2926] if ( _.options.arrows === true
  125. [Dòng 2933] if (_.currentSlide === 0) {
  126. [Dòng 2938] _.options.centerMode === false) {

== (5 điều kiện):
  1. [Dòng 2007] x = _.positionProp == 'left' ? Math.ceil(position) + 'px' : '0px'
  2. [Dòng 2008] y = _.positionProp == 'top' ? Math.ceil(position) + 'px' : '0px'
  3. [Dòng 2368] if (_.options.slidesToShow == _.options.slidesToScroll
  4. [Dòng 3002] if (typeof opt == 'object'
  5. [Dòng 3002] typeof opt == 'undefined')

!== (47 điều kiện):
  1. [Dòng 22] } else if (typeof exports !== 'undefined') {
  2. [Dòng 155] if (typeof document.mozHidden !== 'undefined') {
  3. [Dòng 158] } else if (typeof document.webkitHidden !== 'undefined') {
  4. [Dòng 342] asNavFor !== null ) {
  5. [Dòng 355] if ( asNavFor !== null
  6. [Dòng 460] if (_.options.infinite !== true) {
  7. [Dòng 612] _.options.responsive !== null) {
  8. [Dòng 630] if (targetBreakpoint !== null) {
  9. [Dòng 631] if (_.activeBreakpoint !== null) {
  10. [Dòng 632] if (targetBreakpoint !== _.activeBreakpoint
  11. [Dòng 676] triggerBreakpoint !== false ) {
  12. [Dòng 699] unevenOffset = (_.slideCount % _.options.slidesToScroll !== 0);
  13. [Dòng 758] _.$dots !== null) {
  14. [Dòng 997] if (filter !== null) {
  15. [Dòng 1103] if (_.slideCount % _.options.slidesToScroll !== 0) {
  16. [Dòng 1314] if (_.$dots !== null) {
  17. [Dòng 1324] if (slideControlIndex !== -1) {
  18. [Dòng 1910] _.currentSlide !== 0) {
  19. [Dòng 1953] if ($(window).width() !== _.windowWidth) {
  20. [Dòng 2144] } else if ( typeof arguments[1] !== 'undefined' ) {
  21. [Dòng 2170] if( $.type( _.options.responsive ) !== 'array' ) {
  22. [Dòng 2239] if (bodyStyle.WebkitTransition !== undefined
  23. [Dòng 2240] bodyStyle.MozTransition !== undefined
  24. [Dòng 2241] bodyStyle.msTransition !== undefined) {
  25. [Dòng 2257] if (bodyStyle.OTransform !== undefined) {
  26. [Dòng 2263] if (bodyStyle.MozTransform !== undefined) {
  27. [Dòng 2269] if (bodyStyle.webkitTransform !== undefined) {
  28. [Dòng 2275] if (bodyStyle.msTransform !== undefined) {
  29. [Dòng 2281] if (bodyStyle.transform !== undefined
  30. [Dòng 2281] _.animType !== false) {
  31. [Dòng 2286] _.transformsEnabled = _.options.useTransform && (_.animType !== null
  32. [Dòng 2286] _.animType !== false);
  33. [Dòng 2500] if (dontAnimate !== true
  34. [Dòng 2567] if (dontAnimate !== true) {
  35. [Dòng 2717] if ( _.touchObject.startX !== _.touchObject.curX ) {
  36. [Dòng 2734] event.type.indexOf('mouse') !== -1) {
  37. [Dòng 2738] event.originalEvent.touches !== undefined ?
  38. [Dòng 2773] touches = event.originalEvent !== undefined ? event.originalEvent.touches : null
  39. [Dòng 2775] touches.length !== 1) {
  40. [Dòng 2781] _.touchObject.curX = touches !== undefined ? touches[0].pageX : event.clientX
  41. [Dòng 2782] _.touchObject.curY = touches !== undefined ? touches[0].pageY : event.clientY
  42. [Dòng 2801] if (event.originalEvent !== undefined
  43. [Dòng 2852] if (_.touchObject.fingerCount !== 1
  44. [Dòng 2857] event.originalEvent.touches !== undefined) {
  45. [Dòng 2861] _.touchObject.startX = _.touchObject.curX = touches !== undefined ? touches.pageX : event.clientX
  46. [Dòng 2862] _.touchObject.startY = _.touchObject.curY = touches !== undefined ? touches.pageY : event.clientY
  47. [Dòng 2872] if (_.$slidesCache !== null) {

!= (4 điều kiện):
  1. [Dòng 805] $('[draggable!=true]', _.$slideTrack).off('dragstart', _.preventDefault);
  2. [Dòng 1467] $('[draggable!=true]', _.$slideTrack).on('dragstart', _.preventDefault);
  3. [Dòng 2707] if( direction != 'vertical' ) {
  4. [Dòng 3006] if (typeof ret != 'undefined') return ret;

================================================================================

📁 FILE 174: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 175: atm_b1.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_1/assets/js/atm_b1.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 228] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 234] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 258] if (paymentState === "authorization_required") {
  12. [Dòng 264] if (method === "REDIRECT") {
  13. [Dòng 267] } else if (method === "POST_REDIRECT") {
  14. [Dòng 306] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 289] if (jLinks !== undefined
  3. [Dòng 289] jLinks !== null) {
  4. [Dòng 291] if (jMerchantReturn !== undefined
  5. [Dòng 291] jMerchantReturn !== null) {
  6. [Dòng 306] if (responseCode !== undefined
  7. [Dòng 306] responseCode !== null
  8. [Dòng 334] if (parentRes !== "{

================================================================================

📁 FILE 176: atm_b1_2.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_1/atm_b1_2.js
📊 Thống kê: 11 điều kiện duy nhất
   - === : 8 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 52] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 55] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 64] year === y
  5. [Dòng 66] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 75] if ("PAY" === operation) {
  7. [Dòng 319] } else if (value === "") {
  8. [Dòng 336] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 77] if (e !== null) {
  2. [Dòng 253] if (lang !== "vi") lang = "en";
  3. [Dòng 283] if (value !== "") {

================================================================================

📁 FILE 177: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_1/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 178: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 179: atm_b2.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_2/assets/js/atm_b2.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 231] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 236] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 252] if (invoiceState === "unpaid"
  10. [Dòng 252] invoiceState === "not_paid") {
  11. [Dòng 256] if (paymentState === "authorization_required") {
  12. [Dòng 260] if (method === "REDIRECT") {
  13. [Dòng 263] } else if (method === "POST_REDIRECT") {
  14. [Dòng 302] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 285] if (jLinks !== undefined
  3. [Dòng 285] jLinks !== null) {
  4. [Dòng 287] if (jMerchantReturn !== undefined
  5. [Dòng 287] jMerchantReturn !== null) {
  6. [Dòng 302] if (responseCode !== undefined
  7. [Dòng 302] responseCode !== null
  8. [Dòng 330] if (parentRes !== "{

================================================================================

📁 FILE 180: atm_b2_2.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_2/atm_b2_2.js
📊 Thống kê: 6 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 41] if (inName.value === "") return err("MISSING_FIELD"
  3. [Dòng 51] if ("PAY" === operation) {

!== (3 điều kiện):
  1. [Dòng 53] if (e !== null) {
  2. [Dòng 229] if (lang !== "vi") lang = "en";
  3. [Dòng 259] if (value !== "") {

================================================================================

📁 FILE 181: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/atm_bank_2/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 182: common.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/common.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 42] if (i % 2 === parity) d *= 2;
  2. [Dòng 46] return (sum % 10) === 0
  3. [Dòng 116] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 119] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 205] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 211] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 227] if (xhr.status === 200
  8. [Dòng 227] xhr.status === 201) {
  9. [Dòng 230] if (invoiceState === "unpaid"
  10. [Dòng 230] invoiceState === "not_paid") {
  11. [Dòng 235] if (paymentState === "authorization_required") {
  12. [Dòng 241] if (method === "REDIRECT") {
  13. [Dòng 244] } else if (method === "POST_REDIRECT") {
  14. [Dòng 283] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 94] this.oldValue !== this.value) {
  2. [Dòng 266] if (jLinks !== undefined
  3. [Dòng 266] jLinks !== null) {
  4. [Dòng 268] if (jMerchantReturn !== undefined
  5. [Dòng 268] jMerchantReturn !== null) {
  6. [Dòng 283] if (responseCode !== undefined
  7. [Dòng 283] responseCode !== null
  8. [Dòng 311] if (parentRes !== "{

================================================================================

📁 FILE 183: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 184: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/international_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 185: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/begodi/international_card/script.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 12] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 22] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 25] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 37] year === y
  5. [Dòng 39] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 70] if ("PAY" === operation) {
  7. [Dòng 144] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 72] if (e !== null) {
  2. [Dòng 117] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 186: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/iframe/index.html
📊 Thống kê: 35 điều kiện duy nhất
   - === : 21 lần
   - == : 0 lần
   - !== : 14 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (21 điều kiện):
  1. [Dòng 10] if ((cardno.length === 15
  2. [Dòng 10] cardno.length === 16
  3. [Dòng 10] cardno.length === 19) && isMode10(cardno) === true) {
  4. [Dòng 10] isMode10(cardno) === true) {
  5. [Dòng 29] if (i % 2 === parity) d *= 2;
  6. [Dòng 33] return (sum % 10) === 0
  7. [Dòng 54] if ("PAY" === operation) {
  8. [Dòng 75] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  9. [Dòng 81] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  10. [Dòng 97] if (xhr.status === 200
  11. [Dòng 97] xhr.status === 201) {
  12. [Dòng 100] if (invoiceState === "unpaid"
  13. [Dòng 100] invoiceState === "not_paid") {
  14. [Dòng 105] if (paymentState === "authorization_required") {
  15. [Dòng 111] if (method === "REDIRECT") {
  16. [Dòng 116] } else if (method === "POST_REDIRECT") {
  17. [Dòng 183] //Customizable =================================================================================================
  18. [Dòng 229] if (typeof queryParams[key] === "undefined") {
  19. [Dòng 232] } else if (typeof queryParams[key] === "string") {
  20. [Dòng 246] if (params["CardList"] === undefined
  21. [Dòng 246] params["CardList"] === null) return;

!== (14 điều kiện):
  1. [Dòng 40] //if (event.origin !== "http://example.com:8080") return;
  2. [Dòng 58] if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
  3. [Dòng 59] if (inNumber.style.display !== "none") instrument.number = inNumber.value;
  4. [Dòng 60] if (inDate.style.display !== "none") instrument.date = inDate.value;
  5. [Dòng 61] if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
  6. [Dòng 62] if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
  7. [Dòng 63] if (inName.style.display !== "none") instrument.name = inName.value;
  8. [Dòng 78] /*if (contentType !== my_expected_type) {
  9. [Dòng 138] if (jLinks !== undefined
  10. [Dòng 138] jLinks !== null) {
  11. [Dòng 140] if (jMerchantReturn !== undefined
  12. [Dòng 140] jMerchantReturn !== null) {
  13. [Dòng 172] if (parentRes !== "{
  14. [Dòng 245] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 187: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 188: karma.conf.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/karma.conf.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 189: main.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/main.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 190: polyfills.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/polyfills.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 191: test.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-inside/src/test.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📋 TÓM TẮT TẤT CẢ ĐIỀU KIỆN DUY NHẤT:
====================================================================================================

=== (502 điều kiện duy nhất):
------------------------------------------------------------
1. return lang === this._translate.currentLang
2. params.timeout === 'true') {
3. this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
4. _re.body.state === 'unpaid');
5. if (this.errorCode === 'overtime'
6. this.errorCode === '253') {
7. if (YY % 400 === 0
8. YY % 4 === 0)) {
9. if (YYYY % 400 === 0
10. YYYY % 4 === 0)) {
11. <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
12. [class.red_border_no_background]="c_expdate && (valueDate.length === 0
13. valueDate.trim().length === 0)"
14. if (isIE[0] === 'MSIE'
15. +isIE[1] === 10) {
16. if ((_val.value.substr(-1) === ' '
17. _val.value.length === 24) {
18. if (this.cardTypeBank === 'bank_card_number') {
19. } else if (this.cardTypeBank === 'bank_account_number') {
20. } else if (this.cardTypeBank === 'bank_username') {
21. } else if (this.cardTypeBank === 'bank_customer_code') {
22. this.cardTypeBank === 'bank_card_number'
23. if (this.cardTypeOcean === 'IB') {
24. } else if (this.cardTypeOcean === 'MB') {
25. if (_val.value.substr(0, 2) === '84') {
26. } else if (this.cardTypeOcean === 'ATM') {
27. if (this.cardTypeBank === 'bank_account_number') {
28. this.cardTypeBank === 'bank_card_number') {
29. if (this.cardTypeBank === 'bank_card_number'
30. if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
31. if (event.keyCode === 8
32. event.key === "Backspace"
33. if (v.length === 2
34. this.flag.length === 3
35. this.flag.charAt(this.flag.length - 1) === '/') {
36. if (v.length === 1) {
37. } else if (v.length === 2) {
38. v.length === 2) {
39. if (len === 2) {
40. if ((this.cardTypeBank === 'bank_account_number'
41. this.cardTypeBank === 'bank_username'
42. this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
43. this.cardTypeOcean === 'ATM')
44. || (this.cardTypeOcean === 'IB'
45. if (valIn === this._translate.instant('bank_card_number')) {
46. } else if (valIn === this._translate.instant('bank_account_number')) {
47. } else if (valIn === this._translate.instant('bank_username')) {
48. } else if (valIn === this._translate.instant('bank_customer_code')) {
49. if (_val.value === ''
50. _val.value === null
51. _val.value === undefined) {
52. if (_val.value && (this.cardTypeBank === 'bank_card_number'
53. if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
54. this.cardTypeOcean === 'MB') {
55. this.cardTypeOcean === 'IB'
56. if ((this.cardTypeBank === 'bank_card_number'
57. if (this.cardName === undefined
58. this.cardName === '') {
59. if (this.valueDate === undefined
60. this.valueDate === '') {
61. c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
62. _inExpDate.trim().length === 0)"
63. if (this.cardListTech === "op") {
64. if (this.timeLeft === 10) {
65. if (this.runTime === true) {
66. if (this.timeLeft === 0) {
67. if (this.runTime === true) this.submitCardBanking();
68. if (approval.method === 'REDIRECT') {
69. } else if (approval.method === 'POST_REDIRECT') {
70. if (this.timeLeft === 1) {
71. } else if (valIn === this._translate.instant('internet_banking')) {
72. if (_val.value && (this.cardTypeBank === 'bank_card_number')) {
73. if (focusElement === 'card_name') {
74. } else if (focusElement === 'exp_date'
75. focusExpDateElement === 'card_name') {
76. if (this.cardTypeBank === 'bank_account_number'
77. filteredData.length === 0"
78. if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
79. filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
80. if (valOut === 'auth') {
81. if (this._b === '1'
82. this._b === '20'
83. this._b === '64') {
84. if (this._b === '36'
85. this._b === '18'
86. this._b === '19'
87. if (this._b === '19'
88. this._b === '16'
89. this._b === '25'
90. this._b === '33'
91. this._b === '39'
92. this._b === '11'
93. this._b === '17'
94. this._b === '36'
95. this._b === '44'
96. this._b === '64'
97. if (this._b === '20'
98. if (this._b === '18') {
99. if (_formCard.country === 'default') {
100. if ((v.substr(-1) === ' '
101. this._i_country_code === 'US') {
102. const insertIndex = this._i_country_code === 'US' ? 5 : 3
103. if (temp[i] === '-'
104. temp[i] === ' ') {
105. insertIndex === 3 ? ' ' : itemRemoved)
106. this.c_country = _val.value === 'default'
107. [ngStyle]="{'border-color': type === 5 ? this.themeColor.border_color : border_color}"
108. type === 5"
109. [ngStyle]="{'border-color': type === 9 ? this.themeColor.border_color : border_color}"
110. type === 9"
111. [ngStyle]="{'border-color': type === 10 ? this.themeColor.border_color : border_color}"
112. type === 10"
113. type === 6"
114. [ngStyle]="{'border-color': type === 6 ? this.themeColor.border_color : border_color}"
115. type === 2"
116. [ngStyle]="{'border-color': type === 2 ? this.themeColor.border_color : border_color}"
117. <div *ngIf="(type === 2
118. type === '2'
119. type === 4"
120. [ngStyle]="{'border-color': type === 4 ? this.themeColor.border_color : border_color}"
121. type === 3"
122. [ngStyle]="{'border-color': type === 3 ? this.themeColor.border_color : border_color}"
123. d_vrbank===true"
124. this.cardListMethod = this.d_domestic + this.d_visa_master + this.d_amex_check + this.d_jcb_check + this.d_cup_check + this.d_qr + this.d_paypal_number === 1
125. this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number === 1
126. if (this._res.state === 'unpaid'
127. this._res.state === 'not_paid') {
128. if ('op' === auth
129. } else if ('bank' === auth
130. return id === 'amex' ? '1234' : '123'
131. if (this.timeLeftPaypal === 0) {
132. filteredData.length === 0
133. filteredDataOther.length === 0"
134. filteredDataMobile.length === 0
135. filteredDataOtherMobile.length === 0"
136. this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
137. this.filteredDataOther = this.appList.filter(item => item.type === 'other');
138. this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
139. this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
140. item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
141. filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
142. if (item.type === 'mobile_banking') {
143. this.appList.length === 1
144. listVNPayQR.length === 0"
145. this.listWalletQR.length === 1) {
146. this.listWalletQR.length === 1
147. if (!this.d_vnpayqr_wallet && !this.d_vnpayqr_deeplink && (this.listWalletQR?.length === 1
148. this.listWalletDeeplink?.length === 1)) {
149. if (item.type === 'deeplink') {
150. this.listWalletQR?.length === 1
151. item.brand_id === 'visa' ? 'height: 14.42px
152. if (event.keyCode === 13) {
153. && ((item.brand_id === 'amex'
154. return this._i_token_otp != null && !isNaN(+this._i_token_otp) && ((id === 'amex'
155. const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
156. item.method === method) : null;
157. err?.status === 400
158. err?.error?.name === 'INVALID_CARD_FEE'
159. if (M[1] === 'Chrome') {
160. if (param === key) {
161. pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
162. target === 0
163. if (cardTypeBank === 'bank_card_number') {
164. } else if (cardTypeBank === 'bank_account_number') {
165. if (target.tagName === 'A'
166. if (typeof define === 'function'
167. } else if (typeof exports === 'object') {
168. if (number === "") return err("MISSING_FIELD"
169. if (inName.value === "") return err("MISSING_FIELD"
170. if ("PAY" === operation) {
171. if (i % 2 === parity) d *= 2;
172. return (sum % 10) === 0
173. if (typeof queryParams[key] === "undefined") {
174. } else if (typeof queryParams[key] === "string") {
175. if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
176. } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
177. if (xhr.status === 200
178. xhr.status === 201) {
179. if (invoiceState === "unpaid"
180. invoiceState === "not_paid") {
181. if (paymentState === "authorization_required") {
182. if (method === "REDIRECT") {
183. } else if (method === "POST_REDIRECT") {
184. responseCode === "0") {
185. if (inMonth.value === "") return err("MISSING_FIELD"
186. if (inYear.value === "") return err("MISSING_FIELD"
187. year === y
188. if (inPhone.value === "") return err("MISSING_FIELD"
189. } else if (value === "") {
190. if (trPhone.style.display === "") {
191. } else if (trName.style.display === "") {
192. if (trName.style.display === "") {
193. if (xhr.status === 200) {
194. if (insType === "card") {
195. if (insBrandId === "visa"
196. insBrandId === "mastercard"
197. insBrandId === "amex"
198. insBrandId === "jcb"
199. insBrandId === "cup") {
200. } else if (insBrandId === "atm") {
201. } else if (insType === "dongabank_account") {
202. } else if (insType === "techcombank_account") {
203. } else if (insType === "vib_account") {
204. } else if (insType === "bidv_account") {
205. } else if (insType === "tpbank_account") {
206. } else if (insType === "shb_account") {
207. } else if (insType === "shb_customer_id") {
208. } else if (insType === "vpbank_account") {
209. } else if (insType === "oceanbank_online_account") {
210. } else if (insType === "oceanbank_mobile_account") {
211. } else if (insType === "pvcombank_account") {
212. if (inCvv.value === "") return err("MISSING_FIELD"
213. if (inCvv.value === "") {
214. if ((cardno.length === 15
215. cardno.length === 16
216. cardno.length === 19) && isMode10(cardno) === true) {
217. isMode10(cardno) === true) {
218. if (params["CardList"] === undefined
219. params["CardList"] === null) return;
220. if (insSwiftCode === "BFTVVNVX") { //Vietcombank
221. } else if (insSwiftCode === "BIDVVNVX") { //BIDV
222. typeof exports === 'object'
223. typeof define === 'function'
224. selector === '#') {
225. if (typeof element.getRootNode === 'function') {
226. if (typeof $ === 'undefined') {
227. version[0] === minMajor
228. version[1] === minMinor
229. if (config === 'close') {
230. if (input.type === 'radio') {
231. } else if (input.type === 'checkbox') {
232. if (this._element.tagName === 'LABEL'
233. input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
234. if (config === 'toggle') {
235. if (_button.getAttribute('aria-pressed') === 'true') {
236. if (activeIndex === index) {
237. if (this._config.pause === 'hover') {
238. if (_this3._config.pause === 'hover') {
239. var isNextDirection = direction === Direction.NEXT
240. var isPrevDirection = direction === Direction.PREV
241. activeIndex === 0
242. activeIndex === lastItemIndex
243. var delta = direction === Direction.PREV ? -1 : 1
244. return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
245. if (direction === Direction.NEXT) {
246. if (typeof config === 'object') {
247. var action = typeof config === 'string' ? config : _config.slide
248. if (typeof config === 'number') {
249. } else if (typeof action === 'string') {
250. if (typeof data[action] === 'undefined') {
251. return foundElem === element
252. if (typeof _this._config.parent === 'string') {
253. return elem.getAttribute('data-parent') === _this._config.parent
254. if (actives.length === 0) {
255. typeof config === 'object'
256. if (typeof config === 'string') {
257. if (typeof data[config] === 'undefined') {
258. if (event.currentTarget.tagName === 'A') {
259. if (usePopper === void 0) {
260. if (typeof Popper === 'undefined') {
261. if (this._config.reference === 'parent') {
262. $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
263. if (typeof this._config.offset === 'function') {
264. if (this._config.display === 'static') {
265. var _config = typeof config === 'object' ? config : null
266. if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
267. event.type === 'keyup'
268. event.type === 'click') {
269. if (event && (event.type === 'click'
270. event.which === TAB_KEYCODE) && $.contains(parent
271. if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
272. event.which === ESCAPE_KEYCODE) {
273. if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
274. event.which === SPACE_KEYCODE)) {
275. if (event.which === ESCAPE_KEYCODE) {
276. if (items.length === 0) {
277. if (event.which === ARROW_UP_KEYCODE
278. if (event.which === ARROW_DOWN_KEYCODE
279. if (this._config.backdrop === 'static') {
280. $(_this5._element).has(event.target).length === 0) {
281. if (event.which === ESCAPE_KEYCODE$1) {
282. if (this.tagName === 'A'
283. this.tagName === 'AREA') {
284. if (unsafeHtml.length === 0) {
285. typeof sanitizeFn === 'function') {
286. if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
287. if (_ret === "continue") continue;
288. if ($(this.element).css('display') === 'none') {
289. var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
290. if (prevHoverState === HoverState.OUT) {
291. if (typeof content === 'object'
292. title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
293. if (typeof this.config.offset === 'function') {
294. if (this.config.container === false) {
295. if (trigger === 'click') {
296. var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
297. var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
298. context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
299. context._hoverState === HoverState.SHOW) {
300. if (context._hoverState === HoverState.SHOW) {
301. context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
302. if (context._hoverState === HoverState.OUT) {
303. if (typeof config.delay === 'number') {
304. if (typeof config.title === 'number') {
305. if (typeof config.content === 'number') {
306. var _config = typeof config === 'object'
307. if (typeof content === 'function') {
308. this._scrollElement = element.tagName === 'BODY' ? window : element
309. var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
310. var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
311. var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
312. return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
313. return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
314. var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
315. this._element.parentNode.nodeType === Node.ELEMENT_NODE
316. var itemSelector = listElement.nodeName === 'UL'
317. listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
318. var activeElements = container && (container.nodeName === 'UL'
319. container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
320. if (active.getAttribute('role') === 'tab') {
321. if (element.getAttribute('role') === 'tab') {
322. 1>t)throw Error("numRounds must a integer >= 1");if("SHA-1"===c)m=512,q=K,u=Z,f=160,r=function(a){return a.slice()};else if(0===c.lastIndexOf("SHA-",0))if(q=function(a,b){return L(a,b,c)
323. c)},u=function(a,b,h,e){var k,f;if("SHA-224"===c
324. "SHA-256"===c)k=(b+65>>>9<<4)+15,f=16;else if("SHA-384"===c
325. "SHA-512"===c)k=(b+129>>>10<<
326. c);if("SHA-224"===c)a=[e[0],e[1],e[2],e[3],e[4],e[5],e[6]];else if("SHA-256"===c)a=e;else if("SHA-384"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,e[4].b,e[5].a,e[5].b];else if("SHA-512"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,
327. "SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)
328. 0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
329. return a},r=function(a){return a.slice()},"SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)||0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
330. c)m=1152,f=224;else if("SHA3-256"===c)m=1088,f=256;else if("SHA3-384"===c)m=832,f=384;else if("SHA3-512"===c)m=576,f=512;else if("SHAKE128"===c)m=1344,f=-1,F=31,z=!0;else if("SHAKE256"===c)m=1088,f=-1,F=31,z=!0;else throw Error("Chosen SHA variant is not supported");u=function(a
331. 0===64*l%e
332. 5][l/5|0];g.push(a.b);if(32*g.length>=h)break;g.push(a.a);l+=1;0===64*l%e&&D(null,b)}return g}}else throw Error("Chosen SHA variant is not supported");d=M(a,g,x);l=A(c);this.setHMACKey=function(a,b,h){var k;if(!0===I)throw Error("HMAC key already set");if(!0===y)throw Error("Cannot set HMAC key after calling update");if(!0===z)throw Error("SHAKE is not supported for HMAC");g=(h
333. f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
334. )b.push(0);b[h]&=4294967040}for(a=0;a<=h;a+=1)v[a]=b[a]^909522486,w[a]=b[a]^1549556828;l=q(v,l);e=m;I=!0};this.update=function(a){var c,b,k,f=0,g=m>>>5;c=d(a,h,n);a=c.binLen;b=c.value;c=a>>>5;for(k=0;k<c;k+=g)f+m<=a&&(l=q(b.slice(k,k+g),l),f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
335. for(g=1;g<t;g+=1)!0===z
336. f);return k(m)};this.getHMAC=function(a,b){var k,g,d,p;if(!1===I)throw Error("Cannot call getHMAC without first setting HMAC key");d=N(b);switch(a){case "HEX":k=function(a){return O(a,f,x,d)};break;case "B64":k=function(a){return P(a,f,x,d)};break;case "BYTES":k=function(a){return Q(a,f,x)};break;case "ARRAYBUFFER":try{k=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}k=function(a){return R(a,f,x)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
337. d=-1===b?3:0
338. f=-1===b?3:0
339. g=-1===b?3:0
340. !0===c.hasOwnProperty("b64Pad")
341. a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");
342. u=-1===b?3:0
343. l+=2){p=parseInt(a.substr(l,2),16);if(isNaN(p))throw Error("String of HEX type contains invalid characters");m=(l>>>1)+q;for(f=m>>>2;c.length<=f;)c.push(0);c[f]|=p<<8*(u+m%4*b)}return{value:c,binLen:4*g+d}};break;case "TEXT":c=function(c,h,d){var g,l,p=0,f,m,q,u,r,t;h=h||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(t=-1===
344. m+=1){r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=l[m]<<8*(t+r%4*b);p+=1}else if("UTF16BE"===a
345. "UTF16LE"===a)for(t=-1===b?2:0
346. UTF16LE"===a
347. 1===b
348. !0===l
349. !0===l&&(m=g&255,g=m<<8|g>>>8);r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=g<<8*(t+r%4*b);p+=2}return{value:h,binLen:8*p+d}};break;case "B64":c=function(a,c,d){var g=0,l,p,f,m,q,u,r,t;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");p=a.indexOf("=");a=a.replace(/\=/g
350. t=-1===b?3:0
351. q=-1===b?3:0
352. m=-1===b?3:0
353. c.b^a.b)}function A(c){var a=[],d;if("SHA-1"===c)a=[1732584193,4023233417,2562383102,271733878,3285377520];else if(0===c.lastIndexOf("SHA-",0))switch(a=
354. new b(d[2],4271175723),new b(d[3],1595750129),new b(d[4],2917565137),new b(d[5],725511199),new b(d[6],4215389547),new b(d[7],327033209)];break;default:throw Error("Unknown SHA variant");}else if(0===c.lastIndexOf("SHA3-",0)
355. 0===c.lastIndexOf("SHAKE",0))for(c=0
356. a,k){var e,h,n,g,l,p,f,m,q,u,r,t,v,w,y,A,z,x,F,B,C,D,E=[],J;if("SHA-224"===k
357. "SHA-256"===k)u=64,t=1,D=Number,v=G,w=la,y=H,A=ha,z=ja,x=da,F=fa,C=U,B=aa,J=d;else if("SHA-384"===k
358. "SHA-512"===k)u=80,t=2,D=b,v=ma,w=na,y=oa,A=ia,z=ka,x=ea,F=ga,C=ca,B=ba,J=V;else throw Error("Unexpected error in SHA-2 implementation");k=a[0];e=a[1];h=a[2];n=a[3];g=a[4];l=a[5];p=a[6];f=a[7];for(r=0
359. "function"===typeof define
360. 1>u)throw Error("numRounds must a integer >= 1");if(0===c.lastIndexOf("SHA-",0))if(q=function(b,a){return A(b,a,c)
361. c)},y=function(b,a,l,f){var g,e;if("SHA-224"===c
362. "SHA-256"===c)g=(a+65>>>9<<4)+15,e=16;else throw Error("Unexpected error in SHA-2 implementation");for(
363. c);if("SHA-224"===c)b=[f[0],f[1],f[2],f[3],f[4],f[5],f[6]];else if("SHA-256"===c)b=f;else throw Error("Unexpected error in SHA-2 implementation");return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
364. "SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a
365. return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
366. if(!0===z)throw Error("Cannot set HMAC key after calling update");f=(g
367. 5);g=a%h;z=!0};this.getHash=function(a,f){var d,h,k,q;if(!0===m)throw Error("Cannot call getHash after setting HMAC key");k=C(f);switch(a){case "HEX":d=function(a){return D(a,e,k)};break;case "B64":d=function(a){return E(a,e,k)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{h=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("format must be HEX, B64, BYTES, or ARRAYBUFFER");
368. x(c));return d(q)};this.getHMAC=function(a,f){var d,k,t,u;if(!1===m)throw Error("Cannot call getHMAC without first setting HMAC key");t=C(f);switch(a){case "HEX":d=function(a){return D(a,e,t)};break;case "B64":d=function(a){return E(a,e,t)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{d=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
369. h=(d>>>1)+q;for(e=h>>>2;b.length<=e;)b.push(0);b[e]|=k<<8*(3+h%4*-1)}return{value:b,binLen:4*f+c}};break;case "TEXT":d=function(c,b,d){var f,n,k=0,e,h,q,m,p,r;b=b||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(r=3
370. q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=n[h]<<8*(r+p%4*-1);k+=1}else if("UTF16BE"===a
371. "UTF16LE"===a)for(r=2
372. !0===n
373. e+=1){f=c.charCodeAt(e);!0===n&&(h=f&255,f=h<<8|f>>>8);p=k+q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=f<<8*(r+p%4*-1);k+=2}return{value:b,binLen:8*k+d}};break;case "B64":d=function(a,b,c){var f=0,d,k,e,h,q,m,p;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");k=a.indexOf("=");a=a.replace(/\=/g
374. 16)&65535)<<16|b&65535}function R(c,a,d,l,b){var g=(c&65535)+(a&65535)+(d&65535)+(l&65535)+(b&65535);return((c>>>16)+(a>>>16)+(d>>>16)+(l>>>16)+(b>>>16)+(g>>>16)&65535)<<16|g&65535}function x(c){var a=[],d;if(0===c.lastIndexOf("SHA-",0))switch(a=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428]
375. new m,new m,new m,new m,new m,new m];break;case "SHA-512":a=[new m,new m,new m,new m,new m,new m,new m,new m];break;default:throw Error("Unknown SHA variant");}else throw Error("No SHA variants supported");return a}function A(c,a,d){var l,b,g,f,n,k,e,h,m,r,p,w,t,x,u,z,A,B,C,D,E,F,v=[],G;if("SHA-224"===d
376. "SHA-256"===d)r=64,w=1,F=Number,t=P,x=Q,u=R,z=N,A=O,B=L,C=M,E=K,D=J,G=H;else throw Error("Unexpected error in SHA-2 implementation");d=a[0];l=a[1];b=a[2];g=a[3];f=a[4];n=a[5];k=a[6];e=a[7];for(p=
377. if (typeof(index) === 'boolean') {
378. if (typeof(index) === 'number') {
379. if (index === 0
380. _.$slides.length === 0) {
381. if (addBefore === true) {
382. if (_.options.slidesToShow === 1
383. _.options.adaptiveHeight === true
384. _.options.vertical === false) {
385. if (_.options.rtl === true
386. if (_.transformsEnabled === false) {
387. if (_.options.vertical === false) {
388. if (_.cssTransitions === false) {
389. if (_.options.rtl === true) {
390. typeof asNavFor === 'object' ) {
391. if (_.options.fade === false) {
392. if ( _.options.infinite === false ) {
393. if ( _.direction === 1
394. ( _.currentSlide + 1 ) === ( _.slideCount - 1 )) {
395. else if ( _.direction === 0 ) {
396. if ( _.currentSlide - 1 === 0 ) {
397. if (_.options.arrows === true ) {
398. if (_.options.dots === true
399. _.$slideTrack = (_.slideCount === 0) ?
400. if (_.options.centerMode === true
401. _.options.swipeToSlide === true) {
402. _.setSlideClasses(typeof _.currentSlide === 'number' ? _.currentSlide : 0);
403. if (_.options.draggable === true) {
404. if (_.respondTo === 'window') {
405. } else if (_.respondTo === 'slider') {
406. } else if (_.respondTo === 'min') {
407. if (_.originalSettings.mobileFirst === false) {
408. if (_.breakpointSettings[targetBreakpoint] === 'unslick') {
409. if (initial === true) {
410. slideOffset = indexOffset === 0 ? _.options.slidesToScroll : _.options.slidesToShow - indexOffset
411. slideOffset = indexOffset === 0 ? _.options.slidesToScroll : indexOffset
412. var index = event.data.index === 0 ? 0 :
413. if (_.options.accessibility === true) {
414. if (_.options.arrows === true
415. if (_.options.focusOnSelect === true) {
416. if (_.shouldClick === false) {
417. if (_.options.infinite === true) {
418. } else if (_.options.centerMode === true) {
419. if (_.options.vertical === true
420. _.options.centerMode === true) {
421. if (_.options.slidesToShow === 2) {
422. } else if (_.options.slidesToShow === 1) {
423. } else if (_.options.centerMode === true
424. _.options.infinite === true) {
425. if (_.options.variableWidth === true) {
426. _.options.infinite === false) {
427. if (_.options.centerMode === true) {
428. if (_.options.infinite === false) {
429. centerOffset = _.options.centerMode === true ? _.slideWidth * Math.floor(_.options.slidesToShow / 2) : 0
430. if (_.options.swipeToSlide === true) {
431. _.options.pauseOnDotsHover === true
432. if (event.keyCode === 37
433. _.options.accessibility === true) {
434. message: _.options.rtl === true ? 'next' :
435. } else if (event.keyCode === 39
436. message: _.options.rtl === true ? 'previous' : 'next'
437. if (_.options.fade === true) {
438. if (_.options.lazyLoad === 'anticipated') {
439. } else if (_.currentSlide === 0) {
440. if (_.options.lazyLoad === 'progressive') {
441. if ( _.options.adaptiveHeight === true ) {
442. if ( $.type(responsiveSettings) === 'array'
443. _.breakpoints[l] === currentBreakpoint ) {
444. index = removeBefore === true ? 0 : _.slideCount - 1
445. index = removeBefore === true ? --index : index
446. if (removeAll === true) {
447. if (_.options.vertical === false
448. _.options.variableWidth === false) {
449. } else if (_.options.variableWidth === true) {
450. if (_.options.variableWidth === false) _.$slideTrack.children('.slick-slide').width(_.slideWidth - offset);
451. if( $.type( arguments[0] ) === 'object' ) {
452. } else if ( $.type( arguments[0] ) === 'string' ) {
453. if ( arguments[0] === 'responsive'
454. $.type( arguments[1] ) === 'array' ) {
455. if ( type === 'single' ) {
456. } else if ( type === 'multiple' ) {
457. } else if ( type === 'responsive' ) {
458. if( _.options.responsive[l].breakpoint === value[item].breakpoint ) {
459. _.positionProp = _.options.vertical === true ? 'top' : 'left'
460. if (_.positionProp === 'top') {
461. if (_.options.useCSS === true) {
462. if ( typeof _.options.zIndex === 'number' ) {
463. if (bodyStyle.perspectiveProperty === undefined
464. bodyStyle.webkitPerspective === undefined) _.animType = false;
465. bodyStyle.MozPerspective === undefined) _.animType = false;
466. if (bodyStyle.msTransform === undefined) _.animType = false;
467. var evenCoef = _.options.slidesToShow % 2 === 0 ? 1 : 0
468. if (index === 0) {
469. } else if (index === _.slideCount - 1) {
470. indexOffset = _.options.infinite === true ? _.options.slidesToShow + index : index
471. if (_.options.lazyLoad === 'ondemand'
472. _.options.lazyLoad === 'anticipated') {
473. if (_.options.infinite === true
474. _.options.fade === false) {
475. if (_.animating === true
476. _.options.waitForAnimate === true) {
477. if (_.options.fade === true
478. _.currentSlide === index) {
479. if (sync === false) {
480. _.currentLeft = _.swipeLeft === null ? slideLeft : _.swipeLeft
481. if (_.options.infinite === false
482. _.options.centerMode === false
483. } else if (_.options.infinite === false
484. _.options.centerMode === true
485. return (_.options.rtl === false ? 'left' : 'right');
486. return (_.options.rtl === false ? 'right' : 'left');
487. if (_.options.verticalSwiping === true) {
488. if ( _.touchObject.curX === undefined ) {
489. if ( _.touchObject.edgeHit === true ) {
490. if ((_.options.swipe === false) || ('ontouchend' in document
491. _.options.swipe === false)) {
492. } else if (_.options.draggable === false
493. positionOffset = (_.options.rtl === false ? 1 : -1) * (_.touchObject.curX > _.touchObject.startX ? 1 : -1);
494. if ((_.currentSlide === 0
495. swipeDirection === 'right') || (_.currentSlide >= _.getDotCount()
496. swipeDirection === 'left')) {
497. _.options.touchMove === false) {
498. if (_.animating === true) {
499. if ( _.options.arrows === true
500. if (_.currentSlide === 0) {
501. _.options.centerMode === false) {
502. //Customizable =================================================================================================

== (579 điều kiện duy nhất):
------------------------------------------------------------
1. 'vi' == params['locale']) {
2. 'en' == params['locale']) {
3. errorCode == '11'"
4. <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
5. (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
6. <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
7. !(errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
8. _re.body.state == 'closed') this.rePayment = false;
9. if (_re.body.currencies[0] == 'USD') {
10. params.response_code == 'overtime') {
11. if (_re.status == '200'
12. _re.status == '201') {
13. if (_re2.status == '200'
14. _re2.status == '201') {
15. if (this.errorCode == 'overtime'
16. this.errorCode == '253') {
17. } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
18. bodyData.state == 'canceled') { // kiểm tra điều kiện như bên general
19. if (lastPayment?.state == 'pending') {
20. if ((dataPassed.status == '200'
21. dataPassed.status == '201') && dataPassed.body != null) {
22. if (this.locale == 'en') {
23. if (name == 'MAFC')
24. if (bankId == 3
25. bankId == 61
26. bankId == 8
27. bankId == 49
28. bankId == 48
29. bankId == 10
30. bankId == 53
31. bankId == 17
32. bankId == 65
33. bankId == 23
34. bankId == 52
35. bankId == 27
36. bankId == 66
37. bankId == 9
38. bankId == 54
39. bankId == 37
40. bankId == 38
41. bankId == 39
42. bankId == 40
43. bankId == 42
44. bankId == 44
45. bankId == 72
46. bankId == 59
47. bankId == 51
48. bankId == 64
49. bankId == 58
50. bankId == 56
51. bankId == 55
52. bankId == 60
53. bankId == 68
54. bankId == 74
55. bankId == 75
56. token_site == 'onepay'
57. this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
58. if (this._b == 18
59. this._b == 19) {
60. if (this._b == 19) {//19BIDV
61. } else if (this._b == 3
62. this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
63. if (this._b == 27) {
64. } else if (this._b == 12) {// 12SHB
65. } else if (this._b == 18) { //18Oceanbank-ocb
66. if (this._b == 19
67. this._b == 3
68. this._b == 27
69. this._b == 12) {
70. } else if (this._b == 18) {
71. if (this.checkBin(_val.value) && (this._b == 3
72. this._b == 27)) {
73. if (this._b == 3) {
74. this.cardTypeOcean == 'ATM') {
75. if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
76. this._b == 18)) {
77. if (this.checkBin(v) && (this._b == 3
78. event.inputType == 'deleteContentBackward') {
79. if (event.target.name == 'exp_date'
80. event.inputType == 'insertCompositionText') {
81. if (((this.valueDate.length == 4
82. this.valueDate.search('/') == -1) || this.valueDate.length == 5)
83. this.valueDate.length == 5)
84. if (temp.length == 0) {
85. return (counter % 10 == 0);
86. } else if (this._b == 19) {
87. } else if (this._b == 27) {
88. if (this._b == 12) {
89. if (this.cardTypeBank == 'bank_customer_code') {
90. } else if (this.cardTypeBank == 'bank_account_number') {
91. _formCard.exp_date.length == 5
92. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
93. this._b == 3)) {//27-pvcombank;3-TPB ;
94. if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
95. this._b == 19
96. this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
97. if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
98. if (this.cardTypeOcean == 'IB') {
99. } else if (this.cardTypeOcean == 'MB') {
100. } else if (this.cardTypeOcean == 'ATM') {
101. this.token_site == 'onepay'
102. if (this._res_post.state == 'approved'
103. this._res_post.state == 'failed') {
104. } else if (this._res_post.state == 'authorization_required') {
105. if (this._b == 18) {
106. if (this._b == 27
107. this._b == 18) {
108. if (err.status == 400
109. err.status == 500) {
110. if (err.error && (err.error.code == 13
111. err.error.code == '13')) {
112. if ((cardNo.length == 16
113. if ((cardNo.length == 16 || (cardNo.length == 19
114. && ((this._b == 18
115. cardNo.length == 19) || this._b != 18)
116. if (this._b == +e.id) {
117. if (valIn == 1) {
118. } else if (valIn == 2) {
119. this._b == 3) {
120. if (this._b == 19) {
121. if (cardType == this._translate.instant('internetbanking')
122. } else if (cardType == this._translate.instant('mobilebanking')
123. } else if (cardType == this._translate.instant('atm')
124. this._b == 18))) {
125. } else if (this._b == 18
126. this.c_expdate = !(((this.valueDate.length == 4
127. this.valueDate.length == 4
128. this.valueDate.search('/') == -1)
129. this.valueDate.length == 5))
130. (cardTypeBank == 'bank_card_number') && (_b == 67 || (checkTwoEnabled && techcombankCard)) && !isOffTechcombankNapas
131. (cardTypeBank == 'bank_card_number') &&(_b == 67 || (checkTwoEnabled && techcombankCard))&& ready===1 && !isOffTechcombankNapas
132. (cardTypeBank == 'internet_banking')&&(_b == 2 || (checkTwoEnabled && !techcombankCard))
133. if (this._b == 67
134. this._b == 2) {//19BIDV
135. if ((this._b == 2
136. !this.checkTwoEnabled) || (this._b == 2
137. this._b == 2
138. } else if (this._b == 2
139. if (this._b == 67) {
140. return this._b == 2
141. this._b == 67
142. _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
143. err.error['name'] == 'INVALID_INPUT_BIN') {
144. this.checkMod10(cardNo) == true
145. if (this._b != 68 || (this._b == 68
146. return ((value.length == 4
147. value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
148. value.length == 5) && parseInt(value.split('/')[0]
149. || (this._util.checkValidExpireMonthYear(value) && (this._b == 2
150. this._b == 20
151. this._b == 33
152. this._b == 39
153. this._b == 43
154. this._b == 45
155. this._b == 64
156. this._b == 68
157. this._b == 72))) //sonnh them Vietbank 72
158. this._inExpDate.length == 4
159. this._inExpDate.search('/') == -1)
160. this._inExpDate.length == 5))
161. || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 20
162. this._b == 72)));
163. if (this._b == 8) {//MB Bank
164. if (this._b == 18) {//Oceanbank
165. if (this._b == 8) {
166. if (this._b == 12) { //SHB
167. } else if (this._res.state == 'authorization_required') {
168. if (reason.code == '25'
169. if (this._b == 18) {//8-MB Bank;18-oceanbank
170. <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_card_number')">
171. <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_account_number')">
172. if(this._b == 12) this.isShbGroup = true;
173. return this._b == 9
174. this._b == 11
175. this._b == 16
176. this._b == 17
177. this._b == 25
178. this._b == 44
179. this._b == 57
180. this._b == 59
181. this._b == 61
182. this._b == 63
183. this._b == 69
184. if (this._b == 12
185. this.cardTypeBank == 'bank_account_number') {
186. cardNo.length == 19) && cardNo.startsWith("970443") && this._util.checkMod10(cardNo) && this.checkBin(cardNo) ) {
187. if (this._b == 2
188. this._b == 31) {
189. if (this._b == 2) {
190. } else if (this._b == 6) {
191. } else if (this._b == 31) {
192. if (this._b == 5) {//5-vib;
193. if (this._b == 5) {
194. if (this.checkBin(_val.value) && (this._b == 5)) {
195. if (this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
196. if (this.checkBin(v) && (this._b == 5)) {
197. this._b == 5) {//5 vib ;
198. this._b == 5) {//5vib;
199. _b == 68"
200. if (this._b == 1
201. this._b == 36
202. this._b == 55
203. this._b == 47
204. this._b == 48
205. this._b == 59) {
206. this._b == 9
207. return this._b == 11
208. this._b == 72
209. this._b == 73
210. this._b == 74
211. this._b == 75
212. this._b == 14
213. this._b == 15
214. this._b == 24
215. this._b == 8
216. this._b == 10
217. this._b == 22
218. this._b == 23
219. this._b == 30
220. this._b == 9) {
221. (cardNo.length == 19
222. (cardNo.length == 19 && (this._b == 1
223. this._b == 4
224. this._b == 59))
225. this._util.checkMod10(cardNo) == true
226. ((!token && _auth==0 && vietcombankGroupSelected) || (token && bankId==16))
227. bankId==16))">
228. _auth==0 && techcombankGroupSelected
229. _auth==0 && shbGroupSelected
230. _auth==0 && onepaynapasGroupSelected
231. _auth==0 && bankaccountGroupSelected
232. _auth==0 && vibbankGroupSelected
233. (token || _auth==1) && _b != 16
234. this._auth == 0
235. this.tokenList.length == 0) {
236. this.filteredData.length == 1
237. $event == 'true') {
238. if (bankId == 1
239. bankId == 4
240. bankId == 7
241. bankId == 11
242. bankId == 14
243. bankId == 15
244. bankId == 16
245. bankId == 20
246. bankId == 22
247. bankId == 24
248. bankId == 25
249. bankId == 30
250. bankId == 33
251. bankId == 34
252. bankId == 35
253. bankId == 36
254. bankId == 41
255. bankId == 43
256. bankId == 45
257. bankId == 46
258. bankId == 47
259. bankId == 50
260. bankId == 57
261. bankId == 62
262. bankId == 63
263. bankId == 69
264. bankId == 70
265. bankId == 71
266. bankId == 73
267. bankId == 32
268. bankId == 75) {
269. } else if (bankId == 6
270. bankId == 31
271. bankId == 80) {
272. } else if (bankId == 2
273. bankId == 67) {
274. } else if (bankId == 3
275. bankId == 18
276. bankId == 19
277. bankId == 27) {
278. } else if (bankId == 5) {
279. } else if (bankId == 12) {
280. this._b == '55'
281. this._b == '47'
282. this._b == '48'
283. this._b == '59'
284. this._b == '73'
285. this._b == '12') {
286. this._b == '3'
287. this._b == '43'
288. this._b == '45'
289. this._b == '57'
290. this._b == '61'
291. this._b == '63'
292. this._b == '67'
293. this._b == '68'
294. this._b == '69'
295. this._b == '72'
296. this._b == '9'
297. this._b == '74'
298. this._b == '75') {
299. this._b == '36'
300. if (item['id'] == this._b) {
301. data['type'] == '5'
302. data['type'] == '7'
303. data['type'] == '9'
304. data['type'] == '10'"
305. data['type'] == '7'"
306. data['type'] == '9'"
307. data['type'] == '6'
308. data['type'] == '8'"
309. regexp.test('card;;visa;USD')) && (this._type == 5
310. this._type == 7);
311. regexp.test('card;;mastercard;USD')) && (this._type == 5
312. regexp.test('card;;amex;USD')) && (this._type == 6
313. this._type == 8);
314. this._type == 9
315. this._type == 10
316. } else if (this._res_post.state == 'failed') {  // lỗi mới kiểm tra sang trang lỗi
317. if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
318. } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
319. if (err.error && (err.error.code == 8
320. err.error.code == '8')) {
321. if (this._type == 5
322. this._type == 6) {
323. } else if (this._type == 7
324. this._type == 8) {
325. } else if (err.error && (err.error.code == 13
326. v.length == 15) || (v.length == 16
327. v.length == 19))
328. this._util.checkMod10(v) == true) {
329. cardNo.length == 15)
330. cardNo.length == 16)
331. cardNo.startsWith('81')) && (cardNo.length == 16
332. cardNo.length == 19))
333. this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
334. this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
335. v.length == 5) {
336. v.length == 4
337. v.length == 3)
338. _val.value.length == 4
339. _val.value.length == 3)
340. this.requireAvs = this.AVS_COUNTRIES.find(e => e.code == this._i_country_code) ? true : false;
341. countryCode == 'US' ? US_STATES
342. : countryCode == 'CA' ? CA_STATES
343. this._i_csc.length == 4) ||
344. this._i_csc.length == 3)
345. country = this.AVS_COUNTRIES.find(e => e.code == res.bin_country);
346. merchantId == 'TESTENETVIET'"
347. type == 5"
348. type == 9"
349. type == 10"
350. tokenList.length == 1)">
351. tokenList.length == 1"
352. if (el == 5) {
353. } else if (el == 6) {
354. } else if (el == 7) {
355. } else if (el == 8) {
356. } else if (el == 2) {
357. } else if (el == 4) {
358. } else if (el == 3) {
359. } else if (el == 9) {
360. if (!isNaN(_re.status) && (_re.status == '200'
361. _re.status == '201') && _re.body != null) {
362. if (('closed' == this._res_polling.state
363. 'canceled' == this._res_polling.state
364. 'expired' == this._res_polling.state)
365. } else if ('paid' == this._res_polling.state) {
366. this._res_polling.payments == null) {
367. if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'authorization_required') {
368. } else if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
369. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
370. this._paymentService.getCurrentPage() == 'enter_card') {
371. } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
372. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
373. } else if ('not_paid' == this._res_polling.state) {
374. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
375. if (message == '1') {
376. if (this.checkInvoiceState() == 1) {
377. this.version2 = _re.body?.merchant?.qr_version == '2' ? true : false
378. if (this.type == 5
379. } else if (this.type == 6
380. } else if (this.type == 2
381. } else if (this.type == 7
382. } else if (this.type == 9
383. } else if (this.type == 10
384. } else if (this.type == 8
385. } else if (this.type == 4
386. } else if (this.type == 3
387. } else if ((this.d_domestic == 1) && this.feeService['atm'] && this.feeService['atm']['fee']) {
388. if (('closed' == this._res.state
389. 'canceled' == this._res.state
390. 'expired' == this._res.state
391. 'paid' == this._res.state)
392. if ('paid' == this._res.state
393. this._auth == 0) {
394. if ((this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number) == 0
395. if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
396. this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
397. if (this._res.payments[this._res.payments.length - 1].state == 'approved'
398. } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
399. if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required') {
400. } else if (this._res.payments[this._res.payments.length - 1].state == 'pending') {
401. } else if (this._res.payments[this._res.payments.length - 1].state == 'canceled') {
402. } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
403. this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
404. } else if (idBrand == 'atm'
405. if ('paid' == this._res.state) {
406. this._res.merchant.token_site == 'onepay')) {
407. this._res.payments == null) {
408. this._res.payments[this._res.payments.length - 1].state == 'pending') {
409. if (this._res.currencies[0] == 'USD') {
410. if (item.instrument.issuer.brand.id == 'atm') {
411. } else if (item.instrument.issuer.brand.id == 'visa'
412. item.instrument.issuer.brand.id == 'mastercard') {
413. if (item.instrument.issuer_location == 'd') {
414. } else if (item.instrument.issuer.brand.id == 'amex') {
415. } else if (item.instrument.issuer.brand.id == 'jcb') {
416. if (type == 'mobile') {
417. e.type == 'ewallet'
418. e.code == 'momo')) {
419. e.type == 'vnpayqr') || (regex.test(strTest)
420. this.tokenList.length == 1) {
421. if (_val == 2
422. if (this.type == 4) {
423. if (data._locale == 'en') {
424. screen=='qr'"
425. screen=='confirm_close'"
426. this.themeColor.deeplink_status == 'Off' ? false : true
427. if (item.available == true) {
428. if (appcode == 'grabpay'
429. appcode == 'momo') {
430. if (type == 2
431. err.error.code == '04') {
432. e.type == 'vnpayqr') {
433. e.type == 'ewallet') {
434. e.type == 'wallet')) {
435. if (d.b.code == s) {
436. type == 'vnpay'"
437. type == 'bankapp'"
438. type == 'both'"
439. this.themeConfig.deeplink_status == 'Off' ? false : true
440. _locale=='vi'"
441. _locale=='en'"
442. _locale == 'vi'"
443. _locale == 'en'"
444. e.type == 'deeplink') {
445. if (e.type == 'ewallet') {
446. this.listWallet.length == 1
447. this.listWallet[0].code == 'momo') {
448. this.checkEWalletDeeplink.length == 0) {
449. arrayWallet.length == 0) return false;
450. if (arrayWallet[i].code == key) {
451. if (this.locale == 'vi') {
452. data['type'] == 'Visa'
453. data['type'] == 'Master'
454. data['type'] == 'JCB'"
455. data['type'] == 'Visa'"
456. data['type'] == 'Master'"
457. data['type'] == 'Amex'"
458. token_main == '1'
459. token_main == '1'"
460. if (message == '0') {
461. item.id == element.id ? element['active'] = true : element['active'] = false
462. item.display_cvv == true ? this.flagToken = true : this.flagToken = false
463. if (result == 'success') {
464. if (this.tokenList.length == 0) {
465. } else if (result == 'error') {
466. _val.value.length == 4) || (item.brand_id != 'amex'
467. _val.value.length == 3))
468. _val.value.length == 3)));
469. id == 'amex' ? this.maxLength = 4 : this.maxLength = 3
470. if (_re.body.state == 'more_info_required') {
471. if (this._res_post.state == 'failed') {
472. } else if (_re.body.state == 'authorization_required') {
473. this._b == 18
474. this._b == 5
475. this._b == 12
476. } else if (_re.body.state == 'failed') {
477. if (action == 'blur') {
478. this._i_token_otp.length == 4)
479. this._i_token_otp.length == 3));
480. return ((a.id == id
481. a.code == id) && a.type.includes(type));
482. if (isIphone == true) {
483. } else if (isAndroid == true) {
484. return countPayment == maxPayment
485. if (this.getLatestPayment().state == 'canceled')
486. if (res?.state == 'canceled') {
487. state == 'authorization_required'
488. if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
489. else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';
490. if (e.name == bankSwift) { // TODO: get by swift
491. return this.apps.find(e => e.code == appCode);
492. if (+e.id == bankId) {
493. if (e.swiftCode == bankSwift) {
494. if (currency == 'USD') {
495. if (this.checkCount == 1) {
496. if (results == null) {
497. if (c.length == 3) {
498. d = d == undefined ? '.' : d
499. t = t == undefined ? '
500. return results == null ? null : results[1]
501. if (((_val.length == 4
502. _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
503. _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
504. iss_date.length == 4
505. iss_date.search('/') == -1)
506. iss_date.length == 5))
507. if (_dataCache == null) {
508. if ( (0 <= r && r <= 6 && (c == 0
509. c == 6) )
510. || (0 <= c && c <= 6 && (r == 0
511. r == 6) )
512. if (i == 0
513. _modules[r][6] = (r % 2 == 0);
514. _modules[6][c] = (c % 2 == 0);
515. if (r == -2
516. r == 2
517. c == -2
518. c == 2
519. || (r == 0
520. c == 0) ) {
521. ( (bits >> i) & 1) == 1);
522. if (col == 6) col -= 1;
523. if (_modules[row][col - c] == null) {
524. dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
525. if (bitIndex == -1) {
526. margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
527. if (typeof arguments[0] == 'object') {
528. margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
529. if (b == -1) throw 'eof';
530. if (b0 == -1) break;
531. if (typeof b == 'number') {
532. if ( (b & 0xff) == b) {
533. return function(i, j) { return (i + j) % 2 == 0
534. return function(i, j) { return i % 2 == 0
535. return function(i, j) { return j % 3 == 0
536. return function(i, j) { return (i + j) % 3 == 0
537. return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
538. return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
539. return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
540. return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
541. if (r == 0
542. c == 0) {
543. if (dark == qrcode.isDark(row + r, col + c) ) {
544. if (count == 0
545. count == 4) {
546. if (typeof num.length == 'undefined') {
547. num[offset] == 0) {
548. if (typeof rsBlock == 'undefined') {
549. return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
550. _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
551. if (data.length - i == 1) {
552. } else if (data.length - i == 2) {
553. } else if (n == 62) {
554. } else if (n == 63) {
555. if (_buflen == 0) {
556. if (c == '=') {
557. } else if (c == 0x2b) {
558. } else if (c == 0x2f) {
559. if (table.size() == (1 << bitLength) ) {
560. if (cardList.length == 1) {//TOKEN, BIDV, SHB, OCEAN, PVCOM, TP Bank
561. if ( $('.circle_v1').css('display') == 'block'
562. if ($('.circle_v2').css('display') == 'block'
563. $('.circle_v3').css('display') == 'block' ) {
564. $('.circle_v1').css('display') == 'block'
565. document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM'
566. if ( document.getElementsByClassName("select_online")[0].value == 'Easy Online Banking') {
567. if ( document.getElementsByClassName("select_online")[0].value == 'Easy Mobile Banking') {
568. if ( document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM') {
569. if ($('.circle_v1').css('display') == 'block') {
570. if ($('.circle_v1').css('display') == 'block'
571. if ( $('.circle_v1').css('display') == 'block') {
572. else if ($('.circle_v2').css('display') == 'block') {
573. if ( $('.circle_v3').css('display') == 'block' ) {
574. if ($('.circle_v2').css('display') == 'block') {
575. x = _.positionProp == 'left' ? Math.ceil(position) + 'px' : '0px'
576. y = _.positionProp == 'top' ? Math.ceil(position) + 'px' : '0px'
577. if (_.options.slidesToShow == _.options.slidesToScroll
578. if (typeof opt == 'object'
579. typeof opt == 'undefined')

!== (162 điều kiện duy nhất):
------------------------------------------------------------
1. this.lastValue !== $event.target.value)) {
2. if (YY % 400 === 0 || (YY % 100 !== 0
3. if (YYYY % 400 === 0 || (YYYY % 100 !== 0
4. event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
5. key !== '3') {
6. this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
7. codeResponse.toString() !== '0') {
8. cardNo.length !== 0) {
9. if (this.cardTypeBank !== 'bank_card_number') {
10. if (this.cardTypeBank !== 'bank_account_number') {
11. if (this.cardTypeBank !== 'bank_username') {
12. if (this.cardTypeBank !== 'bank_customer_code') {
13. this.lb_card_account !== this._translate.instant('ocb_account')) {
14. this.lb_card_account !== this._translate.instant('ocb_phone')) {
15. this.lb_card_account !== this._translate.instant('ocb_card')) {
16. this._b !== 18) || (this.cardTypeOcean === 'ATM'
17. let _b = this._b !== 67 ? 67 : this._b
18. if (this.cardTypeBank !== 'internet_banking') {
19. this._b !== 18)) {
20. this._b !== 18) || (this._b == 18)) {
21. if (this.tracking.hasOwnProperty(key) && ((key !== '8'
22. !this._showNameOnCard) || (key !== '9'
23. codeResponse.toString() !== '0'){
24. event.inputType !== 'deleteContentBackward') || v.length == 5) {
25. this._i_country_code !== 'US') {
26. itemRemoved !== '') {
27. if (deviceValue !== 'default') {
28. this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
29. this._i_country_code !== 'default'
30. this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
31. this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {
32. if (_val !== 3) {
33. this._util.getElementParams(this.currentUrl, 't_id') !== '') {
34. queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
35. if (queryString !== '') {
36. if (target !== 0
37. if (e !== null) {
38. if (lang !== "vi") lang = "en";
39. this.oldValue !== this.value) {
40. if (jLinks !== undefined
41. jLinks !== null) {
42. if (jMerchantReturn !== undefined
43. jMerchantReturn !== null) {
44. if (responseCode !== undefined
45. responseCode !== null
46. if (parentRes !== "{
47. if (value !== "") {
48. if (inMonth.value !== ""
49. if (inYear.value !== ""
50. var month = inMonth.value !== ""
51. var year = parseInt("20" + (inYear.value !== ""
52. if ("2D" !== order["vpc_VerType"]) return err("MISSING_FIELD"
53. inMonth.value !== tokenInsMonth) instrument["month"] = inMonth.value;
54. inYear.value !== tokenInsYear) instrument["year"] = inYear.value;
55. if (inCvv.value !== "") instrument["cvv"] = inCvv.value;
56. if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
57. if (inNumber.style.display !== "none") instrument.number = inNumber.value;
58. if (inDate.style.display !== "none") instrument.date = inDate.value;
59. if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
60. if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
61. if (inName.style.display !== "none") instrument.name = inName.value;
62. typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
63. hrefAttr !== '#' ? hrefAttr.trim() : ''
64. $(this._element).css('visibility') !== 'hidden') {
65. if (selector !== null
66. if (selector !== null) {
67. if (typeof this._config.parent.jquery !== 'undefined') {
68. if (typeof this._config.reference.jquery !== 'undefined') {
69. if (this._config.boundary !== 'scrollParent') {
70. if (this._popper !== null) {
71. event.which !== TAB_KEYCODE)) {
72. event.which !== ESCAPE_KEYCODE
73. if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
74. event.which !== ARROW_UP_KEYCODE
75. this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
76. if (document !== event.target
77. _this5._element !== event.target
78. if (event.target !== event.currentTarget) {
79. if (typeof margin !== 'undefined') {
80. if (allowedAttributeList.indexOf(attrName) !== -1) {
81. if (uriAttrs.indexOf(attrName) !== -1) {
82. var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
83. if (_this2._hoverState !== HoverState.SHOW
84. if (_this2._popper !== null) {
85. if (data.originalPlacement !== data.placement) {
86. } else if (trigger !== Trigger.MANUAL) {
87. titleType !== 'string') {
88. if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
89. if (this.constructor.Default[key] !== this.config[key]) {
90. if (tabClass !== null
91. if (tip.getAttribute('x-placement') !== null) {
92. if (typeof config.target !== 'string') {
93. if (this._scrollHeight !== scrollHeight) {
94. if (this._activeTarget !== target) {
95. var isActiveTarget = this._activeTarget !== this._targets[i]
96. 'use strict';(function(Y){function C(c,a,b){var e=0,h=[],n=0,g,l,d,f,m,q,u,r,I=!1,v=[],w=[],t,y=!1,z=!1,x=-1;b=b||{};g=b.encoding||"UTF8";t=b.numRounds||1;if(t!==parseInt(t,10)
97. 0!==f%32
98. a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8
99. }switch(c){case "HEX":c=function(a,c,d){var g=a.length,l,p,f,m,q,u;if(0!==g%2)throw Error("String of HEX type must be in byte increments");c=c||[0];d=d||0;q=d>>>3;u=-1===b?3:0;for(l=0
100. 1!==b
101. "UTF16LE"!==a
102. "");if(-1!==p
103. function(a,c,d){var g,l,p,f,m,q;c=c||[0];d=d||0;l=d>>>3;m=-1===b?3:0;q=new Uint8Array(a);for(g=0;g<a.byteLength;g+=1)f=g+l,p=f>>>2,c.length<=p&&c.push(0),c[p]|=q[g]<<8*(m+f%4*b);return{value:c,binLen:8*a.byteLength+d}};break;default:throw Error("format must be HEX, TEXT, B64, BYTES, or ARRAYBUFFER");}return c}function y(c,a){return c<<a|c>>>32-a}function S(c,a){return 32<a?(a-=32,new b(c.b<<a|c.a>>>32-a,c.a<<a|c.b>>>32-a)):0!==a?new b(c.a<<a|c.b>>>32-a,c.b<<a|c.a>>>32-a):c}function w(c,a){return c>>>
104. a[7]);return a}function D(c,a){var d,e,h,n,g=[],l=[];if(null!==c)for(e=0
105. define.amd?define(function(){return C}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=C),exports=C):Y.jsSHA=C})(this);
106. return C}):"undefined"!==typeof exports?("undefined"!==typeof module
107. 'use strict';(function(I){function w(c,a,d){var l=0,b=[],g=0,f,n,k,e,h,q,y,p,m=!1,t=[],r=[],u,z=!1;d=d||{};f=d.encoding||"UTF8";u=d.numRounds||1;if(u!==parseInt(u,10)
108. l+=1)g[l]=c[l>>>2]>>>8*(3+l%4*-1)&255;return b}function C(c){var a={outputUpper:!1,b64Pad:"=",shakeLen:-1};c=c||{};a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");
109. if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0;d<f;d+=2){k=parseInt(a.substr(d,2),16);if(isNaN(k))throw Error("String of HEX type contains invalid characters");
110. if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0
111. "");if(-1!==k
112. define.amd?define(function(){return w}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=w),exports=w):I.jsSHA=w})(this);
113. return w}):"undefined"!==typeof exports?("undefined"!==typeof module
114. } else if (typeof exports !== 'undefined') {
115. if (typeof document.mozHidden !== 'undefined') {
116. } else if (typeof document.webkitHidden !== 'undefined') {
117. asNavFor !== null ) {
118. if ( asNavFor !== null
119. if (_.options.infinite !== true) {
120. _.options.responsive !== null) {
121. if (targetBreakpoint !== null) {
122. if (_.activeBreakpoint !== null) {
123. if (targetBreakpoint !== _.activeBreakpoint
124. triggerBreakpoint !== false ) {
125. unevenOffset = (_.slideCount % _.options.slidesToScroll !== 0);
126. _.$dots !== null) {
127. if (filter !== null) {
128. if (_.slideCount % _.options.slidesToScroll !== 0) {
129. if (_.$dots !== null) {
130. if (slideControlIndex !== -1) {
131. _.currentSlide !== 0) {
132. if ($(window).width() !== _.windowWidth) {
133. } else if ( typeof arguments[1] !== 'undefined' ) {
134. if( $.type( _.options.responsive ) !== 'array' ) {
135. if (bodyStyle.WebkitTransition !== undefined
136. bodyStyle.MozTransition !== undefined
137. bodyStyle.msTransition !== undefined) {
138. if (bodyStyle.OTransform !== undefined) {
139. if (bodyStyle.MozTransform !== undefined) {
140. if (bodyStyle.webkitTransform !== undefined) {
141. if (bodyStyle.msTransform !== undefined) {
142. if (bodyStyle.transform !== undefined
143. _.animType !== false) {
144. _.transformsEnabled = _.options.useTransform && (_.animType !== null
145. _.animType !== false);
146. if (dontAnimate !== true
147. if (dontAnimate !== true) {
148. if ( _.touchObject.startX !== _.touchObject.curX ) {
149. event.type.indexOf('mouse') !== -1) {
150. event.originalEvent.touches !== undefined ?
151. touches = event.originalEvent !== undefined ? event.originalEvent.touches : null
152. touches.length !== 1) {
153. _.touchObject.curX = touches !== undefined ? touches[0].pageX : event.clientX
154. _.touchObject.curY = touches !== undefined ? touches[0].pageY : event.clientY
155. if (event.originalEvent !== undefined
156. if (_.touchObject.fingerCount !== 1
157. event.originalEvent.touches !== undefined) {
158. _.touchObject.startX = _.touchObject.curX = touches !== undefined ? touches.pageX : event.clientX
159. _.touchObject.startY = _.touchObject.curY = touches !== undefined ? touches.pageY : event.clientY
160. if (_.$slidesCache !== null) {
161. //if (event.origin !== "http://example.com:8080") return;
162. /*if (contentType !== my_expected_type) {

!= (190 điều kiện duy nhất):
------------------------------------------------------------
1. if (params['locale'] != null
2. } else if (params['locale'] != null
3. if (userLang != null
4. if(value!=null){
5. if (message != ''
6. message != null
7. message != undefined) {
8. if (this._idInvoice != null
9. this._idInvoice != 0) {
10. if (this._paymentService.getInvoiceDetail() != null) {
11. dataPassed.body != null) {
12. dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
13. dataPassed.body.themes.border_color != true ? dataPassed.body.themes.border_color : ''
14. if (this._translate.currentLang != language) {
15. } else if (this._b != 18) {
16. if (this.htmlDesc != null
17. if (ua.indexOf('safari') != -1
18. if (_val.value != '') {
19. this.auth_method != null) {
20. if (this.valueDate.length != 3) {
21. if (_formCard.exp_date != null
22. if (this.cardName != null
23. if (this._res_post.links != null
24. this._res_post.links.merchant_return != null
25. this._res_post.links.merchant_return.href != null) {
26. if (this._res_post.authorization != null
27. this._res_post.authorization.links != null
28. this._res_post.authorization.links.approval != null) {
29. this._res_post.links.cancel != null) {
30. this._b != 27
31. this._b != 12
32. this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
33. this._b != 18)
34. if (this._b != 18
35. this._b != 19) {
36. if (this._inExpDate.length != 3) {
37. if (this._res_post.return_url != null) {
38. let userName = _formCard.name != null ? _formCard.name : ''
39. this._res_post.authorization.links.approval != null
40. this._res_post.authorization.links.approval.href != null) {
41. userName = paramUserName != null ? paramUserName : ''
42. this._b != 3))
43. if (this._b != 68
44. this._b != 2
45. this._b != 20
46. this._b != 33
47. this._b != 39
48. this._b != 43
49. this._b != 45
50. this._b != 64
51. this._b != 67
52. this._b != 68
53. this._b != 72)
54. if (this._res.links != null
55. this._res.links.merchant_return != null
56. this._res.links.merchant_return.href != null) {
57. this.challengeCode != '') {
58. if (!(_formCard.otp != null
59. if (!(_formCard.password != null
60. if ( this._b != 9
61. this._b != 16
62. this._b != 17
63. this._b != 25
64. this._b != 44
65. this._b != 57
66. this._b != 59
67. this._b != 61
68. this._b != 63
69. this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
70. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75'].indexOf(this._b.toString()) != -1) {
71. if (params['locale'] != null) {
72. if ('otp' != this._paymentService.getCurrentPage()) {
73. this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
74. if (!(strInstrument != null
75. if (strInstrument.substring(0, 1) != '^'
76. strInstrument.substr(strInstrument.length - 1) != '$') {
77. if (bankid != null) {
78. _showNameOnCard!=true"
79. } else if (this._res_post.links != null
80. cardNo != null
81. v != null
82. this.c_csc = (!(_val.value != null
83. this._i_csc != null
84. this.requireAvs = this.isAvsCountry = country != undefined
85. merchantId != 'TESTENETVIET'"
86. this._paymentService.getState() != 'error') {
87. if (this._paymentService.getCurrentPage() != 'otp') {
88. _re.body != null) {
89. this._res_polling.links != null
90. this._res_polling.links.merchant_return != null //
91. } else if (this._res_polling.merchant != null
92. this._res_polling.merchant_invoice_reference != null
93. } else if (this._res_polling.payments != null
94. this._res_polling.links.merchant_return != null//
95. this._res_polling.payments != null
96. if (this._res_polling.payments != null
97. t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
98. this.type.toString().length != 0) {
99. this._res.links != null
100. if (count != 1) {
101. if (this._res.merchant != null
102. this._res.merchant_invoice_reference != null) {
103. if (this._res.merchant.address_details != null) {
104. this._res.links != null//
105. } else if (this._res.payments != null
106. this._res.payments[this._res.payments.length - 1].instrument != null
107. this._res.payments[this._res.payments.length - 1].instrument.issuer != null
108. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
109. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
110. this._res.payments[this._res.payments.length - 1].links != null
111. this._res.payments[this._res.payments.length - 1].links.cancel != null
112. this._res.payments[this._res.payments.length - 1].links.cancel.href != null
113. this._res.payments[this._res.payments.length - 1].links.update != null
114. this._res.payments[this._res.payments.length - 1].links.update.href != null) {
115. this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
116. this._res.payments[this._res.payments.length - 1].authorization != null
117. this._res.payments[this._res.payments.length - 1].authorization.links != null) {
118. this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
119. this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
120. if (this._res.payments[this._res.payments.length - 1].authorization != null
121. this._res.payments[this._res.payments.length - 1].authorization.links != null
122. auth = paramUserName != null ? paramUserName : ''
123. this._res.links.merchant_return != null //
124. } else if (this._res.merchant != null
125. this._res.merchant_invoice_reference != null
126. if (['mastercard', 'visa', 'amex', 'jcb', 'cup', 'card', 'np_card', 'atm'].indexOf(id) != -1) {
127. } else if (['techcombank_account', 'vib_account', 'dongabank_account', 'tpbank_account', 'bidv_account', 'pvcombank_account', 'shb_account', 'oceanbank_online_account'].indexOf(id) != -1) {
128. } else if (['oceanbank_mobile_account'].indexOf(id) != -1) {
129. } else if (['shb_customer_id'].indexOf(id) != -1) {
130. if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1) {
131. return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
132. e.type != 'ewallet') || (regex.test(strTest)
133. } else if (this._res.payments != null) {
134. 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {
135. if (appcode != null) {
136. if (_re.status != '200'
137. _re.status != '201')
138. qr_version2 != 'None'"
139. qr_version2 != 'None'
140. if (this.translate.currentLang != language) {
141. item['feeService']['fee'] != 0
142. item['feeService']['fee'] != 0"
143. this._res.merchant.id != 'OPTEST' ? this._res.on_off_bank : ''
144. if (_val.value != null
145. this.c_token_otp_csc = !(_val.value != null
146. if (_re.body.authorization != null
147. _re.body.authorization.links != null
148. if (_re.body.links != null
149. _re.body.links.cancel != null) {
150. if (_re.body.return_url != null) {
151. } else if (_re.body.links != null
152. _re.body.links.merchant_return != null
153. _re.body.links.merchant_return.href != null) {
154. if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(item.brand_id) != -1
155. return this._i_token_otp != null
156. || (id != 'amex'
157. if (idInvoice != null
158. idInvoice != 0)
159. idInvoice != 0) {
160. if (this._merchantid != null
161. this._tranref != null
162. this._state != null
163. let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
164. urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
165. if (paymentId != null) {
166. if (fee != null) {
167. if (res?.status != 200
168. res?.status != 201) return;
169. _re.status != '201') {
170. latestPayment?.state != "authorization_required") {
171. if (tem != null) {
172. if ((tem = ua.match(/version\/(\d+)/i)) != null) {
173. if (v.length != 3) {
174. if (_modules[r][6] != null) {
175. if (_modules[6][c] != null) {
176. if (_modules[row][col] != null) {
177. while (buffer.getLengthInBits() % 8 != 0) {
178. if (count != numChars) {
179. throw count + ' != ' + numChars
180. while (data != 0) {
181. if (test.length != 2
182. ( (test[0] << 8) | test[1]) != code) {
183. if (_length % 3 != 0) {
184. if ( (data >>> length) != 0) {
185. return typeof _map[key] != 'undefined'
186. var source = arguments[i] != null ? arguments[i] : {
187. $('[draggable!=true]', _.$slideTrack).off('dragstart', _.preventDefault);
188. $('[draggable!=true]', _.$slideTrack).on('dragstart', _.preventDefault);
189. if( direction != 'vertical' ) {
190. if (typeof ret != 'undefined') return ret;

