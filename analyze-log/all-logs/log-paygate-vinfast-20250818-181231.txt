====================================================================================================
TRÍCH XUẤT ĐIỀU KIỆN SO SÁNH TỪ CÁC FILE TYPESCRIPT/JAVASCRIPT/HTML
====================================================================================================
Thư mục/File nguồn: /root/projects/onepay/paygate-vinfast/src
Thời gian: 18:12:31 18/8/2025
Tổng số file xử lý: 145
Tổng số file bị bỏ qua: 48
Tổng số điều kiện tìm thấy: 2887

THỐNG KÊ TỔNG QUAN THEO LOẠI TOÁN TỬ:
--------------------------------------------------
Strict equality (===): 1720 lần
Loose equality (==): 320 lần
Strict inequality (!==): 647 lần
Loose inequality (!=): 200 lần
--------------------------------------------------

DANH SÁCH FILE ĐÃ XỬ LÝ:
--------------------------------------------------
1. 4en.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/4en.html
2. 4vn.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/4vn.html
3. app-routing.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/app-routing.module.ts
4. app.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/app.component.html
5. app.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/app.component.spec.ts
6. app.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/app.component.ts
7. app.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/app.module.ts
8. counter.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/counter.directive.spec.ts
9. counter.directive.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/counter.directive.ts
10. dialog-policy.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/dialog/dialog-policy/dialog-policy.component.html
11. dialog-policy.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/dialog/dialog-policy/dialog-policy.component.ts
12. format-carno-input.derective.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/directives/format-carno-input.derective.ts
13. sdk-scanner.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/directives/sdk-scanner/sdk-scanner.component.html
14. sdk-scanner.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/directives/sdk-scanner/sdk-scanner.component.ts
15. uppercase-input.directive.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/directives/uppercase-input.directive.ts
16. error.component.html (7 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/error/error.component.html
17. error.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/error/error.component.spec.ts
18. error.component.ts (19 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/error/error.component.ts
19. support-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/error/support-dialog/support-dialog.html
20. support-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/error/support-dialog/support-dialog.ts
21. format-date.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/format-date.directive.spec.ts
22. format-date.directive.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/format-date.directive.ts
23. main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/main.component.html
24. main.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/main.component.spec.ts
25. main.component.ts (10 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/main.component.ts
26. app.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/app.module.ts
27. cancel-dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/cancel-dialog-guide-dialog.html
28. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/dialog-guide-dialog.html
29. bottom-guide.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/domescard/bottom-guide/bottom-guide.html
30. bottom-guide.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/domescard/bottom-guide/bottom-guide.ts
31. confirm-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/domescard/confirm-dialog.html
32. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/domescard/dialog/dialog-guide-dialog.html
33. domescard.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/domescard/domescard.component.html
34. domescard.component.ts (132 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/domescard/domescard.component.ts
35. bottom-guide.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/intercard/bottom-guide/bottom-guide.html
36. bottom-guide.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/intercard/bottom-guide/bottom-guide.ts
37. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/intercard/dialog/dialog-guide-dialog.html
38. intercard-form.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/intercard/intercard-form/intercard-form.component.html
39. intercard-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/intercard/intercard-form/intercard-form.component.ts
40. international.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/intercard/international.component.html
41. international.component.ts (68 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/intercard/international.component.ts
42. menu.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/menu.component.html
43. menu.component.ts (101 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/menu.component.ts
44. otp-card.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/otp-card/otp-card.component.html
45. otp-card.component.ts (45 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/otp-card/otp-card.component.ts
46. otp-transaction.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/otp-transaction/otp-transaction.component.html
47. otp-transaction.component.ts (40 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/otp-transaction/otp-transaction.component.ts
48. overlay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/overlay/overlay.component.html
49. overlay.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/overlay/overlay.component.spec.ts
50. overlay.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/overlay/overlay.component.ts
51. bank-amount.pipe.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/pipe/bank-amount.pipe.ts
52. close-dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/services/close-dialog.service.ts
53. data.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/services/data.service.ts
54. dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/services/dialog.service.ts
55. fee.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/services/fee.service.ts
56. focus-input.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/services/focus-input.service.ts
57. multiple_method.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/services/multiple_method.service.ts
58. payment.service.ts (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/services/payment.service.ts
59. token-main.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/services/token-main.service.ts
60. index.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/translate/index.ts
61. lang-en.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/translate/lang-en.ts
62. lang-vi.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/translate/lang-vi.ts
63. translate.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/translate/translate.pipe.ts
64. translate.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/translate/translate.service.ts
65. translations.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/translate/translations.ts
66. apps-info.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/util/apps-info.ts
67. banks-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/util/banks-info.ts
68. util.ts (24 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/util/util.ts
69. qrcode.js (67 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/assets/script/qrcode.js
70. environment.prod.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/environments/environment.prod.ts
71. environment.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/environments/environment.ts
72. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/acc_bidv/index.html
73. script.js (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/acc_bidv/script.js
74. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/acc_oceanbank/index.html
75. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/acc_oceanbank/script.js
76. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/acc_pvcombank/index.html
77. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/acc_pvcombank/script.js
78. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/acc_shb/index.html
79. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/acc_shb/script.js
80. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/acc_tpbank/index.html
81. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/acc_tpbank/script.js
82. common.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/common.js
83. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/domestic_card/index.html
84. script.js (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/domestic_card/script.js
85. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/domestic_token/index.html
86. script.js (25 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/domestic_token/script.js
87. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/index.html
88. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/international_card/index.html
89. script.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/international_card/script.js
90. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/international_token/index.html
91. script.js (36 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/international_token/script.js
92. script.js (54 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/script.js
93. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.js
94. bidv.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Bidv/assets/js/bidv.js
95. bidv2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Bidv/bidv2.js
96. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Bidv/index.html
97. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.js
98. ocean.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Ocean/assets/js/ocean.js
99. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Ocean/index.html
100. ocean2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Ocean/ocean2.js
101. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
102. pvbank.js (23 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Pv_Bank/assets/js/pvbank.js
103. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Pv_Bank/index.html
104. pvbank2.js (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Pv_Bank/pvbank2.js
105. Sea2.js (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/SeaBank/Sea2.js
106. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
107. Sea.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/SeaBank/assets/js/Sea.js
108. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/SeaBank/index.html
109. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.js
110. shb.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Shb/assets/js/shb.js
111. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Shb/index.html
112. shb2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Shb/shb2.js
113. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
114. tpbank.js (23 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Tp_Bank/assets/js/tpbank.js
115. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Tp_Bank/index.html
116. tpbank2.js (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Tp_Bank/tpbank2.js
117. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.js
118. onepay.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Visa/assets/js/onepay.js
119. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Visa/index.html
120. index.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Visa/index.js
121. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
122. vpbank.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Vp_Bank/assets/js/vpbank.js
123. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Vp_Bank/index.html
124. vpbank2.js (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Vp_Bank/vpbank2.js
125. sha.js (49 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/assets/js/sha.js
126. sha256.js (28 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/assets/js/sha256.js
127. slick.js (182 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/assets/libraries/slick/slick.js
128. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.js
129. atm_b1.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_1/assets/js/atm_b1.js
130. atm_b1_2.js (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_1/atm_b1_2.js
131. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_1/index.html
132. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.js
133. atm_b2.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_2/assets/js/atm_b2.js
134. atm_b2_2.js (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_2/atm_b2_2.js
135. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_2/index.html
136. common.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/common.js
137. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.js
138. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/international_card/index.html
139. script.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/international_card/script.js
140. index.html (35 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/index.html
141. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/index.html
142. karma.conf.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/karma.conf.js
143. main.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/main.ts
144. polyfills.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/polyfills.ts
145. test.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/test.ts

DANH SÁCH FILE BỊ BỎ QUA:
--------------------------------------------------
1. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/bootstrap.min.js
2. jquery-3.0.0.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/jquery-3.0.0.min.js
3. popper.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/popper.min.js
4. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
5. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
6. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
7. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Bidv/assets/js/jquery-3.4.1.min.js
8. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
9. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
10. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
11. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Ocean/assets/js/jquery-3.4.1.min.js
12. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
13. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
14. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
15. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Pv_Bank/assets/js/jquery-3.4.1.min.js
16. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
17. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
18. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
19. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/SeaBank/assets/js/jquery-3.4.1.min.js
20. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
21. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
22. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
23. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Shb/assets/js/jquery-3.4.1.min.js
24. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
25. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
26. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
27. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Tp_Bank/assets/js/jquery-3.4.1.min.js
28. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
29. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
30. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
31. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Visa/assets/js/jquery-3.4.1.min.js
32. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
33. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
34. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
35. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Vp_Bank/assets/js/jquery-3.4.1.min.js
36. instafeed.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/assets/js/instafeed.min.js
37. slick.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/assets/libraries/slick/slick.min.js
38. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
39. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
40. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
41. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_1/assets/js/jquery-3.4.1.min.js
42. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
43. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
44. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
45. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_2/assets/js/jquery-3.4.1.min.js
46. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
47. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
48. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js

====================================================================================================
CHI TIẾT TỪNG FILE:
====================================================================================================

📁 FILE 1: 4en.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/4en.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 2: 4vn.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/4vn.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 3: app-routing.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/app-routing.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 4: app.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/app.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 5: app.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/app.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 6: app.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/app.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 84] return lang === this._translate.currentLang

== (2 điều kiện):
  1. [Dòng 63] 'vi' == params['locale']) {
  2. [Dòng 65] 'en' == params['locale']) {

!= (3 điều kiện):
  1. [Dòng 63] if (params['locale'] != null
  2. [Dòng 65] } else if (params['locale'] != null
  3. [Dòng 72] if (userLang != null

================================================================================

📁 FILE 7: app.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/app.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 8: counter.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/counter.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 9: counter.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/counter.directive.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 10: dialog-policy.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/dialog/dialog-policy/dialog-policy.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 11: dialog-policy.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/dialog/dialog-policy/dialog-policy.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 12: format-carno-input.derective.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/directives/format-carno-input.derective.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 44] this.lastValue !== $event.target.value)) {

!= (1 điều kiện):
  1. [Dòng 23] if(value!=null){

================================================================================

📁 FILE 13: sdk-scanner.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/directives/sdk-scanner/sdk-scanner.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 14: sdk-scanner.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/directives/sdk-scanner/sdk-scanner.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 15: uppercase-input.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/directives/uppercase-input.directive.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 0 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 23] this.lastValue !== $event.target.value)) {

================================================================================

📁 FILE 16: error.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/error/error.component.html
📊 Thống kê: 7 điều kiện duy nhất
   - === : 1 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 18] isPopupSupport === 'True'"

== (6 điều kiện):
  1. [Dòng 15] errorCode == '11'"
  2. [Dòng 18] isSent == false
  3. [Dòng 40] <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
  4. [Dòng 40] (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
  5. [Dòng 42] <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
  6. [Dòng 42] !(errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending

================================================================================

📁 FILE 17: error.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/error/error.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 18: error.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/error/error.component.ts
📊 Thống kê: 19 điều kiện duy nhất
   - === : 8 lần
   - == : 11 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 104] params.timeout === 'true') {
  2. [Dòng 123] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  3. [Dòng 123] _re.body.state === 'unpaid');
  4. [Dòng 172] if (this.errorCode === 'overtime'
  5. [Dòng 172] this.errorCode === '253') {
  6. [Dòng 243] params.name === 'CUSTOMER_INTIME'
  7. [Dòng 243] params.code === '09') {
  8. [Dòng 291] if (this.timeLeft === 0) {

== (11 điều kiện):
  1. [Dòng 144] _re.body.themes.theme == 'token') {
  2. [Dòng 150] params.response_code == 'overtime') {
  3. [Dòng 193] if (_re.status == '200'
  4. [Dòng 193] _re.status == '201') {
  5. [Dòng 206] if (_re2.status == '200'
  6. [Dòng 206] _re2.status == '201') {
  7. [Dòng 218] if (this.errorCode == 'overtime'
  8. [Dòng 218] this.errorCode == '253') {
  9. [Dòng 220] } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
  10. [Dòng 237] if (lastPayment?.state == 'pending') {
  11. [Dòng 289] if(this.isTimePause == false) {

================================================================================

📁 FILE 19: support-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/error/support-dialog/support-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 20: support-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/error/support-dialog/support-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 21: format-date.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/format-date.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 22: format-date.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/format-date.directive.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0
  2. [Dòng 123] YY % 4 === 0)) {
  3. [Dòng 138] if (YYYY % 400 === 0
  4. [Dòng 138] YYYY % 4 === 0)) {

!== (2 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0 || (YY % 100 !== 0
  2. [Dòng 138] if (YYYY % 400 === 0 || (YYYY % 100 !== 0

================================================================================

📁 FILE 23: main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 24: main.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/main.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 25: main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/main.component.ts
📊 Thống kê: 10 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 7 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 123] params['code']==='09'){

== (2 điều kiện):
  1. [Dòng 89] if ((dataPassed.status == '200'
  2. [Dòng 89] dataPassed.status == '201') && dataPassed.body != null) {

!= (7 điều kiện):
  1. [Dòng 80] if (this._idInvoice != null
  2. [Dòng 80] this._idInvoice != 0) {
  3. [Dòng 81] if (this._paymentService.getInvoiceDetail() != null) {
  4. [Dòng 89] dataPassed.body != null) {
  5. [Dòng 109] dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
  6. [Dòng 110] dataPassed.body.themes.border_color != true ? dataPassed.body.themes.border_color : ''
  7. [Dòng 164] if (this._translate.currentLang != language) {

================================================================================

📁 FILE 26: app.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/app.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 27: cancel-dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/cancel-dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 28: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 29: bottom-guide.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/domescard/bottom-guide/bottom-guide.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 2] guide == 'card_number'"
  2. [Dòng 10] guide == 'card_name'"
  3. [Dòng 18] guide == 'expire_date'"
  4. [Dòng 28] guide == 'issue_date'"

================================================================================

📁 FILE 30: bottom-guide.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/domescard/bottom-guide/bottom-guide.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 31: confirm-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/domescard/confirm-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 32: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/domescard/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 33: domescard.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/domescard/domescard.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 37] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  2. [Dòng 37] _inExpDate.trim().length === 0)"

================================================================================

📁 FILE 34: domescard.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/domescard/domescard.component.ts
📊 Thống kê: 132 điều kiện duy nhất
   - === : 5 lần
   - == : 84 lần
   - !== : 2 lần
   - != : 41 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 168] if (target.tagName === 'A'
  2. [Dòng 305] if (event.keyCode === 8
  3. [Dòng 305] event.key === "Backspace"
  4. [Dòng 566] if (approval.method === 'REDIRECT') {
  5. [Dòng 569] } else if (approval.method === 'POST_REDIRECT') {

== (84 điều kiện):
  1. [Dòng 114] if (params['try_again'] == '1'
  2. [Dòng 186] return this._b == 3
  3. [Dòng 186] this._b == 9
  4. [Dòng 186] this._b == 16
  5. [Dòng 186] this._b == 17
  6. [Dòng 186] this._b == 19
  7. [Dòng 186] this._b == 25
  8. [Dòng 186] this._b == 44
  9. [Dòng 187] this._b == 57
  10. [Dòng 187] this._b == 59
  11. [Dòng 187] this._b == 61
  12. [Dòng 187] this._b == 63
  13. [Dòng 187] this._b == 69
  14. [Dòng 191] return this._b == 18
  15. [Dòng 223] if (parseInt(b) == parseInt(v.substring(0, 6))) {
  16. [Dòng 235] if (this._b == 11
  17. [Dòng 235] this._b == 20
  18. [Dòng 235] this._b == 33
  19. [Dòng 235] this._b == 39
  20. [Dòng 235] this._b == 43
  21. [Dòng 235] this._b == 45
  22. [Dòng 235] this._b == 67
  23. [Dòng 235] this._b == 64
  24. [Dòng 235] this._b == 72
  25. [Dòng 235] this._b == 36
  26. [Dòng 235] this._b == 73
  27. [Dòng 235] this._b == 74
  28. [Dòng 235] this._b == 75
  29. [Dòng 235] this._b == 68) {//seabank
  30. [Dòng 305] event.inputType == 'deleteContentBackward') {
  31. [Dòng 306] if (event.target.name == 'exp_date'
  32. [Dòng 314] event.inputType == 'insertCompositionText') {
  33. [Dòng 397] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  34. [Dòng 408] } else if (this._b == 2) {
  35. [Dòng 410] } else if (this._b == 5) {
  36. [Dòng 412] } else if (this._b == 6) {
  37. [Dòng 414] } else if (this._b == 12) {
  38. [Dòng 416] } else if (this._b == 31) {
  39. [Dòng 441] if (this._b == 12) {
  40. [Dòng 455] if (this._res_post.state == 'approved'
  41. [Dòng 455] this._res_post.state == 'failed') {
  42. [Dòng 506] } else if (this._res_post.state == 'authorization_required') {
  43. [Dòng 528] if (this._b == 1
  44. [Dòng 528] this._b == 14
  45. [Dòng 528] this._b == 15
  46. [Dòng 528] this._b == 24
  47. [Dòng 528] this._b == 8
  48. [Dòng 528] this._b == 10
  49. [Dòng 528] this._b == 18
  50. [Dòng 528] this._b == 22
  51. [Dòng 528] this._b == 23
  52. [Dòng 528] this._b == 27
  53. [Dòng 528] this._b == 30
  54. [Dòng 528] this._b == 11
  55. [Dòng 528] this._b == 5
  56. [Dòng 528] this._b == 12
  57. [Dòng 528] this._b == 9) {
  58. [Dòng 597] if (err.status == 400
  59. [Dòng 597] err.status == 500) {
  60. [Dòng 598] if (err.error && (err.error.code == 13
  61. [Dòng 598] err.error.code == '13')) {
  62. [Dòng 613] if ((cardNo.length == 16
  63. [Dòng 614] (cardNo.length == 19
  64. [Dòng 614] (cardNo.length == 19 && (this._b == 1
  65. [Dòng 614] this._b == 4
  66. [Dòng 614] this._b == 55
  67. [Dòng 614] this._b == 47
  68. [Dòng 614] this._b == 48
  69. [Dòng 614] this._b == 5))
  70. [Dòng 616] this._util.checkMod10(cardNo) == true
  71. [Dòng 718] return ((value.length == 4
  72. [Dòng 718] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  73. [Dòng 718] value.length == 5) && parseInt(value.split('/')[0]
  74. [Dòng 722] || (this._util.checkValidExpireMonthYear(value) && (this._b == 11
  75. [Dòng 723] this._b == 68
  76. [Dòng 723] this._b == 75)))
  77. [Dòng 762] this._inExpDate.length == 4
  78. [Dòng 762] this._inExpDate.search('/') == -1)
  79. [Dòng 763] this._inExpDate.length == 5))
  80. [Dòng 766] || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 11
  81. [Dòng 767] this._b == 75)));
  82. [Dòng 783] if (text == 'card_date') {
  83. [Dòng 784] if (this._lb_card_date == '01/19') {
  84. [Dòng 879] if (data._locale == 'en') {

!== (2 điều kiện):
  1. [Dòng 468] codeResponse.toString() !== '0') {
  2. [Dòng 529] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (41 điều kiện):
  1. [Dòng 122] if (!(strInstrument != null
  2. [Dòng 125] if (strInstrument.substring(0, 1) != '^'
  3. [Dòng 125] strInstrument.substr(strInstrument.length - 1) != '$') {
  4. [Dòng 200] if (ua.indexOf('safari') != -1
  5. [Dòng 307] if (this._inExpDate.length != 3) {
  6. [Dòng 389] if (this._b != 9
  7. [Dòng 389] this._b != 16
  8. [Dòng 389] this._b != 17
  9. [Dòng 389] this._b != 25
  10. [Dòng 389] this._b != 44
  11. [Dòng 390] this._b != 57
  12. [Dòng 390] this._b != 59
  13. [Dòng 390] this._b != 61
  14. [Dòng 390] this._b != 63
  15. [Dòng 390] this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
  16. [Dòng 406] '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72','73','74','75'].indexOf(this._b.toString()) != -1) {
  17. [Dòng 457] if (this._res_post.return_url != null) {
  18. [Dòng 460] if (this._res_post.links != null
  19. [Dòng 460] this._res_post.links.merchant_return != null
  20. [Dòng 460] this._res_post.links.merchant_return.href != null) {
  21. [Dòng 511] if (this._res_post.authorization != null
  22. [Dòng 511] this._res_post.authorization.links != null
  23. [Dòng 516] this._res_post.links.cancel != null) {
  24. [Dòng 522] let userName = _formCard.name != null ? _formCard.name : ''
  25. [Dòng 523] this._res_post.authorization.links.approval != null
  26. [Dòng 523] this._res_post.authorization.links.approval.href != null) {
  27. [Dòng 526] userName = paramUserName != null ? paramUserName : ''
  28. [Dòng 720] this._b != 11
  29. [Dòng 720] this._b != 20
  30. [Dòng 720] this._b != 33
  31. [Dòng 720] this._b != 39
  32. [Dòng 721] this._b != 43
  33. [Dòng 721] this._b != 45
  34. [Dòng 721] this._b != 64
  35. [Dòng 721] this._b != 67
  36. [Dòng 721] this._b != 68
  37. [Dòng 721] this._b != 72
  38. [Dòng 721] this._b != 36
  39. [Dòng 721] this._b != 73
  40. [Dòng 721] this._b != 74
  41. [Dòng 721] this._b != 75)

================================================================================

📁 FILE 35: bottom-guide.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/intercard/bottom-guide/bottom-guide.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

!== (3 điều kiện):
  1. [Dòng 2] guide !== 'card_number'"
  2. [Dòng 11] guide !==  'expire_date'
  3. [Dòng 22] guide !== 'cvv_code'"

================================================================================

📁 FILE 36: bottom-guide.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/intercard/bottom-guide/bottom-guide.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 37: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/intercard/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 38: intercard-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/intercard/intercard-form/intercard-form.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 3 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 2] [ngStyle]="{'border-color': (type === 1
  2. [Dòng 15] type === 1"
  3. [Dòng 19] type === 1

== (1 điều kiện):
  1. [Dòng 4] !token) || (type == 1

================================================================================

📁 FILE 39: intercard-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/intercard/intercard-form/intercard-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 40: international.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/intercard/international.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 222] _b == 6 || _b == 57

!= (1 điều kiện):
  1. [Dòng 14] _showAVS!=true"

================================================================================

📁 FILE 41: international.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/intercard/international.component.ts
📊 Thống kê: 68 điều kiện duy nhất
   - === : 8 lần
   - == : 40 lần
   - !== : 8 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 230] if (target.tagName === 'A'
  2. [Dòng 298] if (_formCard.country === 'default') {
  3. [Dòng 607] if (event.keyCode === 8
  4. [Dòng 607] event.key === "Backspace"
  5. [Dòng 681] if ((v.substr(-1) === ' '
  6. [Dòng 913] if (deviceValue === 'CA'
  7. [Dòng 913] deviceValue === 'US') {
  8. [Dòng 934] this.c_country = _val.value === 'default'

== (40 điều kiện):
  1. [Dòng 169] if (params['try_again'] == '1'
  2. [Dòng 359] if (this._res_post.state == 'approved'
  3. [Dòng 359] this._res_post.state == 'failed') {
  4. [Dòng 385] } else if(this._res_post.state == 'failed') {
  5. [Dòng 414] } else if (this._res_post.state == 'authorization_required') {
  6. [Dòng 415] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  7. [Dòng 427] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  8. [Dòng 444] if (err.status == 400
  9. [Dòng 444] err.status == 500) {
  10. [Dòng 445] if (err.error && (err.error.code == 13
  11. [Dòng 445] err.error.code == '13')) {
  12. [Dòng 502] v.length == 15) || (v.length == 16
  13. [Dòng 502] v.length == 19))
  14. [Dòng 503] this._util.checkMod10(v) == true) {
  15. [Dòng 541] cardNo.length == 15)
  16. [Dòng 543] cardNo.length == 16)
  17. [Dòng 544] cardNo.startsWith('81')) && (cardNo.length == 16
  18. [Dòng 544] cardNo.length == 19))
  19. [Dòng 607] event.inputType == 'deleteContentBackward') {
  20. [Dòng 608] if (event.target.name == 'exp_date'
  21. [Dòng 616] event.inputType == 'insertCompositionText') {
  22. [Dòng 631] if (((this.valueDate.length == 4
  23. [Dòng 631] this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  24. [Dòng 631] this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  25. [Dòng 681] v.length == 5) {
  26. [Dòng 689] v.length == 4
  27. [Dòng 693] v.length == 3)
  28. [Dòng 729] _val.value.length == 4
  29. [Dòng 733] _val.value.length == 3)
  30. [Dòng 891] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  31. [Dòng 891] this.valueDate.length == 5)
  32. [Dòng 984] this.valueDate.length == 4
  33. [Dòng 984] this.valueDate.search('/') == -1)
  34. [Dòng 985] this.valueDate.length == 5))
  35. [Dòng 998] this._i_csc.length == 4) ||
  36. [Dòng 1002] this._i_csc.length == 3)
  37. [Dòng 1016] return this._i_email == ''
  38. [Dòng 1021] return this._i_phone == ''
  39. [Dòng 1085] if (text == 'card_date') {
  40. [Dòng 1086] if (this._lb_card_date == '01/19') {

!== (8 điều kiện):
  1. [Dòng 341] key !== '8') {
  2. [Dòng 369] codeResponse.toString() !== '0'){
  3. [Dòng 681] event.inputType !== 'deleteContentBackward') || v.length == 5) {
  4. [Dòng 910] if (deviceValue !== 'default') {
  5. [Dòng 927] this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
  6. [Dòng 1025] return this._i_country_code !== 'default'
  7. [Dòng 1054] this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
  8. [Dòng 1061] this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {

!= (12 điều kiện):
  1. [Dòng 164] if (params['locale'] != null) {
  2. [Dòng 173] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 361] if (this._res_post.return_url != null) {
  4. [Dòng 363] } else if (this._res_post.links != null
  5. [Dòng 363] this._res_post.links.merchant_return != null
  6. [Dòng 363] this._res_post.links.merchant_return.href != null) {
  7. [Dòng 466] if (ua.indexOf('safari') != -1
  8. [Dòng 541] cardNo != null
  9. [Dòng 609] if (this.valueDate.length != 3) {
  10. [Dòng 688] v != null
  11. [Dòng 728] this.c_csc = (!(_val.value != null
  12. [Dòng 996] this._i_csc != null

================================================================================

📁 FILE 42: menu.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/menu.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 5 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (5 điều kiện):
  1. [Dòng 1] screen == 1"
  2. [Dòng 2] screen == 2"
  3. [Dòng 3] screen == 3"
  4. [Dòng 5] screen == 5"
  5. [Dòng 10] screen == 99"

================================================================================

📁 FILE 43: menu.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/menu.component.ts
📊 Thống kê: 101 điều kiện duy nhất
   - === : 8 lần
   - == : 40 lần
   - !== : 3 lần
   - != : 50 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 472] this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number === 1
  2. [Dòng 505] if (this._res.state === 'unpaid'
  3. [Dòng 505] this._res.state === 'not_paid') {
  4. [Dòng 599] if ('op' === auth
  5. [Dòng 642] } else if ('bank' === auth
  6. [Dòng 647] if (approval.method === 'REDIRECT') {
  7. [Dòng 650] } else if (approval.method === 'POST_REDIRECT') {
  8. [Dòng 876] if (this.timeLeftPaypal === 0) {

== (40 điều kiện):
  1. [Dòng 146] if (!isNaN(_re.status) && (_re.status == '200'
  2. [Dòng 146] _re.status == '201') && _re.body != null) {
  3. [Dòng 152] if (('closed' == this._res_polling.state
  4. [Dòng 152] 'canceled' == this._res_polling.state
  5. [Dòng 152] 'expired' == this._res_polling.state)
  6. [Dòng 172] } else if ('paid' == this._res_polling.state) {
  7. [Dòng 178] this._res_polling.payments == null
  8. [Dòng 180] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  9. [Dòng 182] this._paymentService.getCurrentPage() == 'enter_card') {
  10. [Dòng 185] } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
  11. [Dòng 185] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
  12. [Dòng 202] } else if ('not_paid' == this._res_polling.state) {
  13. [Dòng 213] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
  14. [Dòng 351] if (_re.status == '200'
  15. [Dòng 351] _re.status == '201') {
  16. [Dòng 478] this._auth == 0) {
  17. [Dòng 506] if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
  18. [Dòng 506] this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
  19. [Dòng 508] if (this._res.payments[this._res.payments.length - 1].state == 'approved'
  20. [Dòng 510] } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
  21. [Dòng 549] } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  22. [Dòng 549] this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
  23. [Dòng 583] if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  24. [Dòng 587] } else if (idBrand == 'atm'
  25. [Dòng 611] detail.merchant.id == 'AMWAY') {
  26. [Dòng 671] if ('paid' == this._res.state) {
  27. [Dòng 691] if (('closed' == this._res.state
  28. [Dòng 691] 'canceled' == this._res.state
  29. [Dòng 691] 'expired' == this._res.state
  30. [Dòng 691] 'paid' == this._res.state)
  31. [Dòng 694] if ('paid' == this._res.state
  32. [Dòng 694] 'canceled' == this._res.state) {
  33. [Dòng 713] this._res.payments == null) {
  34. [Dòng 715] this._res.payments[this._res.payments.length - 1].state == 'pending') {
  35. [Dòng 725] if (this._res.currencies[0] == 'USD') {
  36. [Dòng 928] if (this._res_post.state == 'approved'
  37. [Dòng 928] this._res_post.state == 'failed') {
  38. [Dòng 937] } else if (this._res_post.state == 'authorization_required') {
  39. [Dòng 938] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  40. [Dòng 952] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {

!== (3 điều kiện):
  1. [Dòng 615] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  2. [Dòng 842] if (_val !== 3) {
  3. [Dòng 846] this._util.getElementParams(this.currentUrl, 't_id') !== '') {

!= (50 điều kiện):
  1. [Dòng 140] if (this._idInvoice != null
  2. [Dòng 146] _re.body != null) {
  3. [Dòng 153] this._res_polling.links != null
  4. [Dòng 153] this._res_polling.links.merchant_return != null //
  5. [Dòng 156] this._util.getElementParams(url_redirect, 'vpc_TxnResponseCode') != '99') {
  6. [Dòng 178] } else if (this._res_polling.merchant != null
  7. [Dòng 178] this._res_polling.merchant_invoice_reference != null
  8. [Dòng 178] this._paymentService.getState() != 'error') {
  9. [Dòng 180] } else if (this._res_polling.payments != null
  10. [Dòng 186] this._res_polling.links.merchant_return != null//
  11. [Dòng 205] this._res_polling.payments != null
  12. [Dòng 212] if (this._res_polling.payments != null
  13. [Dòng 216] t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
  14. [Dòng 331] if (params['locale'] != null) {
  15. [Dòng 339] if ('otp' != this._paymentService.getCurrentPage()) {
  16. [Dòng 343] if (this._paymentService.getInvoiceDetail() != null) {
  17. [Dòng 489] if (count != 1) {
  18. [Dòng 495] if (this._res.merchant != null
  19. [Dòng 495] this._res.merchant_invoice_reference != null) {
  20. [Dòng 498] if (this._res.merchant.address_details != null) {
  21. [Dòng 506] this._res.links != null//
  22. [Dòng 549] } else if (this._res.payments != null
  23. [Dòng 550] this._res.payments[this._res.payments.length - 1].instrument != null
  24. [Dòng 550] this._res.payments[this._res.payments.length - 1].instrument.issuer != null
  25. [Dòng 551] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
  26. [Dòng 551] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
  27. [Dòng 552] this._res.payments[this._res.payments.length - 1].links != null
  28. [Dòng 552] this._res.payments[this._res.payments.length - 1].links.cancel != null
  29. [Dòng 552] this._res.payments[this._res.payments.length - 1].links.cancel.href != null
  30. [Dòng 568] this._res.payments[this._res.payments.length - 1].links.update != null
  31. [Dòng 568] this._res.payments[this._res.payments.length - 1].links.update.href != null) {
  32. [Dòng 587] this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
  33. [Dòng 588] this._res.payments[this._res.payments.length - 1].authorization != null
  34. [Dòng 588] this._res.payments[this._res.payments.length - 1].authorization.links != null) {
  35. [Dòng 599] this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
  36. [Dòng 599] this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
  37. [Dòng 602] if (this._res.payments[this._res.payments.length - 1].authorization != null
  38. [Dòng 602] this._res.payments[this._res.payments.length - 1].authorization.links != null
  39. [Dòng 608] auth = paramUserName != null ? paramUserName : ''
  40. [Dòng 692] this._res.links != null
  41. [Dòng 692] this._res.links.merchant_return != null //
  42. [Dòng 713] } else if (this._res.merchant != null
  43. [Dòng 713] this._res.merchant_invoice_reference != null
  44. [Dòng 786] if (!(strInstrument != null
  45. [Dòng 803] if (this._translate.currentLang != language) {
  46. [Dòng 848] } else if (this._res.payments != null) {
  47. [Dòng 930] if (this._res_post.return_url != null) {
  48. [Dòng 932] } else if (this._res_post.links != null
  49. [Dòng 932] this._res_post.links.merchant_return != null
  50. [Dòng 932] this._res_post.links.merchant_return.href != null) {

================================================================================

📁 FILE 44: otp-card.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/otp-card/otp-card.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 45: otp-card.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/otp-card/otp-card.component.ts
📊 Thống kê: 45 điều kiện duy nhất
   - === : 4 lần
   - == : 16 lần
   - !== : 2 lần
   - != : 23 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 173] if (this._res.state === "unpaid"
  2. [Dòng 173] this._res.state === "not_paid") {
  3. [Dòng 211] "op" === auth
  4. [Dòng 681] return this.d_pass_word ? this._inPassword.length === 0 : false

== (16 điều kiện):
  1. [Dòng 129] this.token = params["token"] == 'true' ? true : false
  2. [Dòng 131] if (this._b == 8) {
  3. [Dòng 136] if (this._b == 18) {
  4. [Dòng 145] if (_re.status == "200"
  5. [Dòng 145] _re.status == "201") {
  6. [Dòng 176] (this._res.payments[this._res.payments.length - 1].state ==
  7. [Dòng 178] this._res.payments[this._res.payments.length - 1].state ==
  8. [Dòng 198] idBrand == "atm"
  9. [Dòng 310] $event.key == "Backspace"
  10. [Dòng 311] $event.keyCode == 8
  11. [Dòng 312] $event.charCode == 8
  12. [Dòng 351] if (this._b == 12) { //SHB
  13. [Dòng 385] } else if (this._res.state == "authorization_required") {
  14. [Dòng 429] if(this._b == 12){
  15. [Dòng 430] if (this.challengeCode == '') {
  16. [Dòng 538] if (this._b == 12) {

!== (2 điều kiện):
  1. [Dòng 397] codeResponse.toString() !== "0") {
  2. [Dòng 673] return this._otp.trim().length !== 0 ? false : true

!= (23 điều kiện):
  1. [Dòng 171] if (this._res != null) {
  2. [Dòng 175] this._res.payments != null
  3. [Dòng 180] this._res.payments[this._res.payments.length - 1].instrument !=
  4. [Dòng 183] .issuer != null
  5. [Dòng 185] .brand != null
  6. [Dòng 187] .brand.id != null
  7. [Dòng 188] this._res.payments[this._res.payments.length - 1].links != null
  8. [Dòng 189] this._res.payments[this._res.payments.length - 1].links.cancel !=
  9. [Dòng 192] .href != null
  10. [Dòng 200] .issuer.swift_code != null
  11. [Dòng 201] this._res.payments[this._res.payments.length - 1].authorization !=
  12. [Dòng 204] .links != null
  13. [Dòng 213] .links.approval != null
  14. [Dòng 215] .links.approval.href != null
  15. [Dòng 378] this._res.links != null
  16. [Dòng 379] this._res.links.merchant_return != null
  17. [Dòng 380] this._res.links.merchant_return.href != null
  18. [Dòng 579] if (this._res_post != null
  19. [Dòng 579] this._res_post.links != null
  20. [Dòng 579] this._res_post.links.merchant_return != null
  21. [Dòng 579] this._res_post.links.merchant_return.href != null) {
  22. [Dòng 607] if (!(this._otp != null
  23. [Dòng 614] if (!(_formCard.password != null

================================================================================

📁 FILE 46: otp-transaction.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/otp-transaction/otp-transaction.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 47: otp-transaction.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/main/menu/otp-transaction/otp-transaction.component.ts
📊 Thống kê: 40 điều kiện duy nhất
   - === : 3 lần
   - == : 11 lần
   - !== : 1 lần
   - != : 25 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 162] if (this._res.state === "unpaid"
  2. [Dòng 162] this._res.state === "not_paid") {
  3. [Dòng 200] "op" === auth

== (11 điều kiện):
  1. [Dòng 123] if (this._b == 8) {
  2. [Dòng 128] if (this._b == 18) {
  3. [Dòng 137] if (_re.status == "200"
  4. [Dòng 137] _re.status == "201") {
  5. [Dòng 165] (this._res.payments[this._res.payments.length - 1].state ==
  6. [Dòng 167] this._res.payments[this._res.payments.length - 1].state ==
  7. [Dòng 187] idBrand == "atm"
  8. [Dòng 285] if ($event.key == 'Backspace'
  9. [Dòng 285] $event.keyCode == 8
  10. [Dòng 285] $event.charCode == 8) {
  11. [Dòng 379] } else if (this._res.state == "authorization_required") {

!== (1 điều kiện):
  1. [Dòng 391] codeResponse.toString() !== "0") {

!= (25 điều kiện):
  1. [Dòng 160] if (this._res != null) {
  2. [Dòng 164] this._res.payments != null
  3. [Dòng 169] this._res.payments[this._res.payments.length - 1].instrument !=
  4. [Dòng 172] .issuer != null
  5. [Dòng 174] .brand != null
  6. [Dòng 176] .brand.id != null
  7. [Dòng 177] this._res.payments[this._res.payments.length - 1].links != null
  8. [Dòng 178] this._res.payments[this._res.payments.length - 1].links.cancel !=
  9. [Dòng 181] .href != null
  10. [Dòng 189] .issuer.swift_code != null
  11. [Dòng 190] this._res.payments[this._res.payments.length - 1].authorization !=
  12. [Dòng 193] .links != null
  13. [Dòng 202] .links.approval != null
  14. [Dòng 204] .links.approval.href != null
  15. [Dòng 306] this._inOtp_9.length !=
  16. [Dòng 372] this._res.links != null
  17. [Dòng 373] this._res.links.merchant_return != null
  18. [Dòng 374] this._res.links.merchant_return.href != null
  19. [Dòng 521] this._res_post != null
  20. [Dòng 522] this._res_post.links != null
  21. [Dòng 523] this._res_post.links.merchant_return != null
  22. [Dòng 524] this._res_post.links.merchant_return.href != null
  23. [Dòng 564] if (!(this._otp != null
  24. [Dòng 571] if (!(_formCard.password != null
  25. [Dòng 586] if (ua.indexOf("safari") != -1

================================================================================

📁 FILE 48: overlay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/overlay/overlay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 49: overlay.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/overlay/overlay.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 50: overlay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/overlay/overlay.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 51: bank-amount.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/pipe/bank-amount.pipe.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 11] return ((a.id == id
  2. [Dòng 11] a.code == id) && a.type.includes(type));

================================================================================

📁 FILE 52: close-dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/services/close-dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 53: data.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/services/data.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 54: dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/services/dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 55: fee.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/services/fee.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 56: focus-input.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/services/focus-input.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 57: multiple_method.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/services/multiple_method.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 58: payment.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/services/payment.service.ts
📊 Thống kê: 11 điều kiện duy nhất
   - === : 2 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 8 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 513] const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
  2. [Dòng 513] item.method === method) : null;

== (1 điều kiện):
  1. [Dòng 545] return countPayment == maxPayment

!= (8 điều kiện):
  1. [Dòng 107] if (idInvoice != null
  2. [Dòng 107] idInvoice != 0)
  3. [Dòng 117] idInvoice != 0) {
  4. [Dòng 254] if (this._merchantid != null
  5. [Dòng 254] this._tranref != null
  6. [Dòng 254] this._state != null
  7. [Dòng 320] let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
  8. [Dòng 353] urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');

================================================================================

📁 FILE 59: token-main.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/services/token-main.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 60: index.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/translate/index.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 61: lang-en.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/translate/lang-en.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 62: lang-vi.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/translate/lang-vi.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 63: translate.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/translate/translate.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 64: translate.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/translate/translate.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 37] if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
  2. [Dòng 38] else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';

================================================================================

📁 FILE 65: translations.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/translate/translations.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 66: apps-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/util/apps-info.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 155] if (e.name == bankSwift) { // TODO: get by swift

================================================================================

📁 FILE 67: banks-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/util/banks-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1077] if (+e.id == bankId) {
  2. [Dòng 1127] if (e.swiftCode == bankSwift) {

================================================================================

📁 FILE 68: util.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/app/util/util.ts
📊 Thống kê: 24 điều kiện duy nhất
   - === : 11 lần
   - == : 8 lần
   - !== : 3 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 63] if (v.length === 2
  2. [Dòng 63] this.flag.length === 3
  3. [Dòng 63] this.flag.charAt(this.flag.length - 1) === '/') {
  4. [Dòng 67] if (v.length === 1) {
  5. [Dòng 69] } else if (v.length === 2) {
  6. [Dòng 72] v.length === 2) {
  7. [Dòng 80] if (len === 2) {
  8. [Dòng 152] if (M[1] === 'Chrome') {
  9. [Dòng 277] if (param === key) {
  10. [Dòng 439] pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
  11. [Dòng 443] target === 0

== (8 điều kiện):
  1. [Dòng 13] if (temp.length == 0) {
  2. [Dòng 20] return (counter % 10 == 0);
  3. [Dòng 126] if (this.checkCount == 1) {
  4. [Dòng 138] if (results == null) {
  5. [Dòng 171] if (c.length == 3) {
  6. [Dòng 184] d = d == undefined ? '.' : d
  7. [Dòng 185] t = t == undefined ? '
  8. [Dòng 265] return results == null ? null : results[1]

!== (3 điều kiện):
  1. [Dòng 272] queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
  2. [Dòng 273] if (queryString !== '') {
  3. [Dòng 443] if (target !== 0

!= (2 điều kiện):
  1. [Dòng 154] if (tem != null) {
  2. [Dòng 159] if ((tem = ua.match(/version\/(\d+)/i)) != null) {

================================================================================

📁 FILE 69: qrcode.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/assets/script/qrcode.js
📊 Thống kê: 67 điều kiện duy nhất
   - === : 2 lần
   - == : 53 lần
   - !== : 0 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 2255] if (typeof define === 'function'
  2. [Dòng 2257] } else if (typeof exports === 'object') {

== (53 điều kiện):
  1. [Dòng 68] if (_dataCache == null) {
  2. [Dòng 85] if ( (0 <= r && r <= 6 && (c == 0
  3. [Dòng 85] c == 6) )
  4. [Dòng 86] || (0 <= c && c <= 6 && (r == 0
  5. [Dòng 86] r == 6) )
  6. [Dòng 107] if (i == 0
  7. [Dòng 122] _modules[r][6] = (r % 2 == 0);
  8. [Dòng 129] _modules[6][c] = (c % 2 == 0);
  9. [Dòng 152] if (r == -2
  10. [Dòng 152] r == 2
  11. [Dòng 152] c == -2
  12. [Dòng 152] c == 2
  13. [Dòng 153] || (r == 0
  14. [Dòng 153] c == 0) ) {
  15. [Dòng 169] ( (bits >> i) & 1) == 1);
  16. [Dòng 226] if (col == 6) col -= 1;
  17. [Dòng 232] if (_modules[row][col - c] == null) {
  18. [Dòng 237] dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
  19. [Dòng 249] if (bitIndex == -1) {
  20. [Dòng 458] margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
  21. [Dòng 498] if (typeof arguments[0] == 'object') {
  22. [Dòng 587] margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
  23. [Dòng 733] if (b == -1) throw 'eof';
  24. [Dòng 741] if (b0 == -1) break;
  25. [Dòng 767] if (typeof b == 'number') {
  26. [Dòng 768] if ( (b & 0xff) == b) {
  27. [Dòng 910] return function(i, j) { return (i + j) % 2 == 0
  28. [Dòng 912] return function(i, j) { return i % 2 == 0
  29. [Dòng 914] return function(i, j) { return j % 3 == 0
  30. [Dòng 916] return function(i, j) { return (i + j) % 3 == 0
  31. [Dòng 918] return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
  32. [Dòng 920] return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
  33. [Dòng 922] return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
  34. [Dòng 924] return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
  35. [Dòng 1011] if (r == 0
  36. [Dòng 1011] c == 0) {
  37. [Dòng 1015] if (dark == qrcode.isDark(row + r, col + c) ) {
  38. [Dòng 1036] if (count == 0
  39. [Dòng 1036] count == 4) {
  40. [Dòng 1149] if (typeof num.length == 'undefined') {
  41. [Dòng 1155] num[offset] == 0) {
  42. [Dòng 1495] if (typeof rsBlock == 'undefined') {
  43. [Dòng 1538] return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
  44. [Dòng 1543] _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
  45. [Dòng 1599] if (data.length - i == 1) {
  46. [Dòng 1601] } else if (data.length - i == 2) {
  47. [Dòng 1866] } else if (n == 62) {
  48. [Dòng 1868] } else if (n == 63) {
  49. [Dòng 1928] if (_buflen == 0) {
  50. [Dòng 1937] if (c == '=') {
  51. [Dòng 1961] } else if (c == 0x2b) {
  52. [Dòng 1963] } else if (c == 0x2f) {
  53. [Dòng 2133] if (table.size() == (1 << bitLength) ) {

!= (12 điều kiện):
  1. [Dòng 119] if (_modules[r][6] != null) {
  2. [Dòng 126] if (_modules[6][c] != null) {
  3. [Dòng 144] if (_modules[row][col] != null) {
  4. [Dòng 365] while (buffer.getLengthInBits() % 8 != 0) {
  5. [Dòng 750] if (count != numChars) {
  6. [Dòng 751] throw count + ' != ' + numChars
  7. [Dòng 878] while (data != 0) {
  8. [Dòng 1733] if (test.length != 2
  9. [Dòng 1733] ( (test[0] << 8) | test[1]) != code) {
  10. [Dòng 1894] if (_length % 3 != 0) {
  11. [Dòng 2067] if ( (data >>> length) != 0) {
  12. [Dòng 2178] return typeof _map[key] != 'undefined'

================================================================================

📁 FILE 70: environment.prod.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/environments/environment.prod.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 71: environment.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/environments/environment.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 72: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/acc_bidv/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 73: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/acc_bidv/script.js
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 9] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 12] if (inName.value === "") return err("MISSING_FIELD"
  3. [Dòng 21] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 23] if (e !== null) {
  2. [Dòng 55] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 74: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/acc_oceanbank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 75: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/acc_oceanbank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 10] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 19] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 21] if (e !== null) {
  2. [Dòng 53] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 76: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/acc_pvcombank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 77: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/acc_pvcombank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 7] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 16] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 18] if (e !== null) {
  2. [Dòng 50] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 78: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/acc_shb/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 79: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/acc_shb/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 10] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 19] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 21] if (e !== null) {
  2. [Dòng 53] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 80: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/acc_tpbank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 81: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/acc_tpbank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 7] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 16] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 18] if (e !== null) {
  2. [Dòng 50] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 82: common.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/common.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 89] if (i % 2 === parity) d *= 2;
  2. [Dòng 93] return (sum % 10) === 0
  3. [Dòng 163] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 166] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 252] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 258] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 274] if (xhr.status === 200
  8. [Dòng 274] xhr.status === 201) {
  9. [Dòng 277] if (invoiceState === "unpaid"
  10. [Dòng 277] invoiceState === "not_paid") {
  11. [Dòng 282] if (paymentState === "authorization_required") {
  12. [Dòng 288] if (method === "REDIRECT") {
  13. [Dòng 291] } else if (method === "POST_REDIRECT") {
  14. [Dòng 330] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 141] this.oldValue !== this.value) {
  2. [Dòng 313] if (jLinks !== undefined
  3. [Dòng 313] jLinks !== null) {
  4. [Dòng 315] if (jMerchantReturn !== undefined
  5. [Dòng 315] jMerchantReturn !== null) {
  6. [Dòng 330] if (responseCode !== undefined
  7. [Dòng 330] responseCode !== null
  8. [Dòng 358] if (parentRes !== "{

================================================================================

📁 FILE 83: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/domestic_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 84: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/domestic_card/script.js
📊 Thống kê: 14 điều kiện duy nhất
   - === : 11 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 18] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 60] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 63] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 72] year === y
  5. [Dòng 74] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 106] if (inPhone.value === "") return err("MISSING_FIELD"
  7. [Dòng 136] if ("PAY" === operation) {
  8. [Dòng 525] } else if (value === "") {
  9. [Dòng 542] if (trPhone.style.display === "") {
  10. [Dòng 544] } else if (trName.style.display === "") {
  11. [Dòng 565] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 138] if (e !== null) {
  2. [Dòng 404] if (lang !== "vi") lang = "en";
  3. [Dòng 470] if (value !== "") {

================================================================================

📁 FILE 85: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/domestic_token/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 86: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/domestic_token/script.js
📊 Thống kê: 25 điều kiện duy nhất
   - === : 23 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (23 điều kiện):
  1. [Dòng 20] if ("PAY" === operation) {
  2. [Dòng 93] if (trName.style.display === "") {
  3. [Dòng 117] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  4. [Dòng 123] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  5. [Dòng 137] if (xhr.status === 200) {
  6. [Dòng 165] if (insType === "card") {
  7. [Dòng 166] if (insBrandId === "visa"
  8. [Dòng 166] insBrandId === "mastercard"
  9. [Dòng 166] insBrandId === "amex"
  10. [Dòng 166] insBrandId === "jcb"
  11. [Dòng 166] insBrandId === "cup") {
  12. [Dòng 170] } else if (insBrandId === "atm") {
  13. [Dòng 241] } else if (insType === "dongabank_account") {
  14. [Dòng 242] } else if (insType === "techcombank_account") {
  15. [Dòng 243] } else if (insType === "vib_account") {
  16. [Dòng 244] } else if (insType === "bidv_account") {
  17. [Dòng 245] } else if (insType === "tpbank_account") {
  18. [Dòng 246] } else if (insType === "shb_account") {
  19. [Dòng 247] } else if (insType === "shb_customer_id") {
  20. [Dòng 248] } else if (insType === "vpbank_account") {
  21. [Dòng 249] } else if (insType === "oceanbank_online_account") {
  22. [Dòng 250] } else if (insType === "oceanbank_mobile_account") {
  23. [Dòng 251] } else if (insType === "pvcombank_account") {

!== (2 điều kiện):
  1. [Dòng 54] if (lang !== "vi") lang = "en";
  2. [Dòng 273] if (parentRes !== "{

================================================================================

📁 FILE 87: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 88: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/international_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 89: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/international_card/script.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 13] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 23] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 26] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 38] year === y
  5. [Dòng 40] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 71] if ("PAY" === operation) {
  7. [Dòng 161] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 73] if (e !== null) {
  2. [Dòng 118] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 90: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/international_token/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 91: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/international_token/script.js
📊 Thống kê: 36 điều kiện duy nhất
   - === : 25 lần
   - == : 0 lần
   - !== : 11 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (25 điều kiện):
  1. [Dòng 32] year === y
  2. [Dòng 34] if (inCvv.value === "") {
  3. [Dòng 67] if ("PAY" === operation) {
  4. [Dòng 129] } else if (value === "") {
  5. [Dòng 190] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 196] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 209] if (xhr.status === 200) {
  8. [Dòng 245] if (insType === "card") {
  9. [Dòng 246] if (insBrandId === "visa"
  10. [Dòng 246] insBrandId === "mastercard"
  11. [Dòng 246] insBrandId === "amex"
  12. [Dòng 246] insBrandId === "jcb"
  13. [Dòng 246] insBrandId === "cup") {
  14. [Dòng 248] } else if (insBrandId === "atm") {
  15. [Dòng 319] } else if (insType === "dongabank_account") {
  16. [Dòng 320] } else if (insType === "techcombank_account") {
  17. [Dòng 321] } else if (insType === "vib_account") {
  18. [Dòng 322] } else if (insType === "bidv_account") {
  19. [Dòng 323] } else if (insType === "tpbank_account") {
  20. [Dòng 324] } else if (insType === "shb_account") {
  21. [Dòng 325] } else if (insType === "shb_customer_id") {
  22. [Dòng 326] } else if (insType === "vpbank_account") {
  23. [Dòng 327] } else if (insType === "oceanbank_online_account") {
  24. [Dòng 328] } else if (insType === "oceanbank_mobile_account") {
  25. [Dòng 329] } else if (insType === "pvcombank_account") {

!== (11 điều kiện):
  1. [Dòng 18] if (inMonth.value !== ""
  2. [Dòng 21] if (inYear.value !== ""
  3. [Dòng 23] var month = inMonth.value !== ""
  4. [Dòng 24] var year = parseInt("20" + (inYear.value !== ""
  5. [Dòng 35] if ("2D" !== order["vpc_VerType"]) return err("MISSING_FIELD"
  6. [Dòng 71] if (e !== null) {
  7. [Dòng 79] inMonth.value !== tokenInsMonth) instrument["month"] = inMonth.value;
  8. [Dòng 80] inYear.value !== tokenInsYear) instrument["year"] = inYear.value;
  9. [Dòng 81] if (inCvv.value !== "") instrument["cvv"] = inCvv.value;
  10. [Dòng 95] if (lang !== "vi") lang = "en";
  11. [Dòng 351] if (parentRes !== "{

================================================================================

📁 FILE 92: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/amway/script.js
📊 Thống kê: 54 điều kiện duy nhất
   - === : 41 lần
   - == : 1 lần
   - !== : 12 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (41 điều kiện):
  1. [Dòng 4] if ((cardno.length === 15
  2. [Dòng 4] cardno.length === 16
  3. [Dòng 4] cardno.length === 19) && isMode10(cardno) === true) {
  4. [Dòng 4] isMode10(cardno) === true) {
  5. [Dòng 23] if (i % 2 === parity) d *= 2;
  6. [Dòng 27] return (sum % 10) === 0
  7. [Dòng 48] if ("PAY" === operation) {
  8. [Dòng 69] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  9. [Dòng 75] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  10. [Dòng 91] if (xhr.status === 200
  11. [Dòng 91] xhr.status === 201) {
  12. [Dòng 94] if (invoiceState === "unpaid"
  13. [Dòng 94] invoiceState === "not_paid") {
  14. [Dòng 99] if (paymentState === "authorization_required") {
  15. [Dòng 105] if (method === "REDIRECT") {
  16. [Dòng 110] } else if (method === "POST_REDIRECT") {
  17. [Dòng 223] if (typeof queryParams[key] === "undefined") {
  18. [Dòng 226] } else if (typeof queryParams[key] === "string") {
  19. [Dòng 256] if (params["CardList"] === undefined
  20. [Dòng 256] params["CardList"] === null) return;
  21. [Dòng 302] if (xhr.status === 200) {
  22. [Dòng 312] if (insType === "card") {
  23. [Dòng 313] if (insBrandId === "visa"
  24. [Dòng 313] insBrandId === "mastercard"
  25. [Dòng 313] insBrandId === "amex"
  26. [Dòng 313] insBrandId === "jcb"
  27. [Dòng 313] insBrandId === "cup") {
  28. [Dòng 317] } else if (insBrandId === "atm") {
  29. [Dòng 318] if (insSwiftCode === "BFTVVNVX") { //Vietcombank
  30. [Dòng 322] } else if (insSwiftCode === "BIDVVNVX") { //BIDV
  31. [Dòng 329] } else if (insType === "dongabank_account") {
  32. [Dòng 331] } else if (insType === "techcombank_account") {
  33. [Dòng 333] } else if (insType === "vib_account") {
  34. [Dòng 335] } else if (insType === "bidv_account") {
  35. [Dòng 336] } else if (insType === "tpbank_account") {
  36. [Dòng 337] } else if (insType === "shb_account") {
  37. [Dòng 338] } else if (insType === "shb_customer_id") {
  38. [Dòng 339] } else if (insType === "vpbank_account") {
  39. [Dòng 340] } else if (insType === "oceanbank_online_account") {
  40. [Dòng 341] } else if (insType === "oceanbank_mobile_account") {
  41. [Dòng 342] } else if (insType === "pvcombank_account") {

== (1 điều kiện):
  1. [Dòng 270] if (cardList.length == 1) {//TOKEN, BIDV, SHB, OCEAN, PVCOM, TP Bank

!== (12 điều kiện):
  1. [Dòng 52] if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
  2. [Dòng 53] if (inNumber.style.display !== "none") instrument.number = inNumber.value;
  3. [Dòng 54] if (inDate.style.display !== "none") instrument.date = inDate.value;
  4. [Dòng 55] if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
  5. [Dòng 56] if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
  6. [Dòng 57] if (inName.style.display !== "none") instrument.name = inName.value;
  7. [Dòng 132] if (jLinks !== undefined
  8. [Dòng 132] jLinks !== null) {
  9. [Dòng 134] if (jMerchantReturn !== undefined
  10. [Dòng 134] jMerchantReturn !== null) {
  11. [Dòng 166] if (parentRes !== "{
  12. [Dòng 255] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 93: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 94: bidv.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Bidv/assets/js/bidv.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 233] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 239] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 255] if (xhr.status === 200
  8. [Dòng 255] xhr.status === 201) {
  9. [Dòng 258] if (invoiceState === "unpaid"
  10. [Dòng 258] invoiceState === "not_paid") {
  11. [Dòng 263] if (paymentState === "authorization_required") {
  12. [Dòng 269] if (method === "REDIRECT") {
  13. [Dòng 272] } else if (method === "POST_REDIRECT") {
  14. [Dòng 311] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 294] if (jLinks !== undefined
  3. [Dòng 294] jLinks !== null) {
  4. [Dòng 296] if (jMerchantReturn !== undefined
  5. [Dòng 296] jMerchantReturn !== null) {
  6. [Dòng 311] if (responseCode !== undefined
  7. [Dòng 311] responseCode !== null
  8. [Dòng 339] if (parentRes !== "{

================================================================================

📁 FILE 95: bidv2.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Bidv/bidv2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 76] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 88] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 78] if ( $('.circle_v1').css('display') == 'block'
  2. [Dòng 112] if ($('.circle_v2').css('display') == 'block'
  3. [Dòng 112] $('.circle_v3').css('display') == 'block' ) {
  4. [Dòng 302] $('.circle_v1').css('display') == 'block'

!== (3 điều kiện):
  1. [Dòng 90] if (e !== null) {
  2. [Dòng 273] if (lang !== "vi") lang = "en";
  3. [Dòng 305] if (value !== "") {

================================================================================

📁 FILE 96: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Bidv/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 97: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 98: ocean.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Ocean/assets/js/ocean.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 232] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 237] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 251] if (xhr.status === 200
  8. [Dòng 251] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 257] if (paymentState === "authorization_required") {
  12. [Dòng 261] if (method === "REDIRECT") {
  13. [Dòng 264] } else if (method === "POST_REDIRECT") {
  14. [Dòng 303] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 286] if (jLinks !== undefined
  3. [Dòng 286] jLinks !== null) {
  4. [Dòng 288] if (jMerchantReturn !== undefined
  5. [Dòng 288] jMerchantReturn !== null) {
  6. [Dòng 303] if (responseCode !== undefined
  7. [Dòng 303] responseCode !== null
  8. [Dòng 331] if (parentRes !== "{

================================================================================

📁 FILE 99: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Ocean/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 100: ocean2.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Ocean/ocean2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 72] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 92] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 74] document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM'
  2. [Dòng 116] if ( document.getElementsByClassName("select_online")[0].value == 'Easy Online Banking') {
  3. [Dòng 119] if ( document.getElementsByClassName("select_online")[0].value == 'Easy Mobile Banking') {
  4. [Dòng 304] if ( document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM') {

!== (3 điều kiện):
  1. [Dòng 94] if (e !== null) {
  2. [Dòng 276] if (lang !== "vi") lang = "en";
  3. [Dòng 306] if (value !== "") {

================================================================================

📁 FILE 101: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 102: pvbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Pv_Bank/assets/js/pvbank.js
📊 Thống kê: 23 điều kiện duy nhất
   - === : 14 lần
   - == : 1 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 239] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 261] if (xhr.status === 200
  8. [Dòng 261] xhr.status === 201) {
  9. [Dòng 264] if (invoiceState === "unpaid"
  10. [Dòng 264] invoiceState === "not_paid") {
  11. [Dòng 269] if (paymentState === "authorization_required") {
  12. [Dòng 275] if (method === "REDIRECT") {
  13. [Dòng 278] } else if (method === "POST_REDIRECT") {
  14. [Dòng 317] responseCode === "0") {

== (1 điều kiện):
  1. [Dòng 167] if ($('.circle_v1').css('display') == 'block') {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 300] if (jLinks !== undefined
  3. [Dòng 300] jLinks !== null) {
  4. [Dòng 302] if (jMerchantReturn !== undefined
  5. [Dòng 302] jMerchantReturn !== null) {
  6. [Dòng 317] if (responseCode !== undefined
  7. [Dòng 317] responseCode !== null
  8. [Dòng 345] if (parentRes !== "{

================================================================================

📁 FILE 103: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Pv_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 104: pvbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Pv_Bank/pvbank2.js
📊 Thống kê: 15 điều kiện duy nhất
   - === : 8 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 71] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 76] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 79] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 88] year === y
  5. [Dòng 90] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 100] if ("PAY" === operation) {
  7. [Dòng 347] } else if (value === "") {
  8. [Dòng 364] if (trName.style.display === "") {

== (4 điều kiện):
  1. [Dòng 73] if ($('.circle_v1').css('display') == 'block'
  2. [Dòng 75] $('.circle_v1').css('display') == 'block'
  3. [Dòng 124] if ( $('.circle_v1').css('display') == 'block') {
  4. [Dòng 129] else if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 102] if (e !== null) {
  2. [Dòng 286] if (lang !== "vi") lang = "en";
  3. [Dòng 316] if (value !== "") {

================================================================================

📁 FILE 105: Sea2.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/SeaBank/Sea2.js
📊 Thống kê: 11 điều kiện duy nhất
   - === : 8 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 23] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 31] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 34] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 43] year === y
  5. [Dòng 45] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 55] if ("PAY" === operation) {
  7. [Dòng 287] } else if (value === "") {
  8. [Dòng 304] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 57] if (e !== null) {
  2. [Dòng 233] if (lang !== "vi") lang = "en";
  3. [Dòng 263] if (value !== "") {

================================================================================

📁 FILE 106: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 107: Sea.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/SeaBank/assets/js/Sea.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 228] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 234] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 258] if (paymentState === "authorization_required") {
  12. [Dòng 264] if (method === "REDIRECT") {
  13. [Dòng 267] } else if (method === "POST_REDIRECT") {
  14. [Dòng 306] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 289] if (jLinks !== undefined
  3. [Dòng 289] jLinks !== null) {
  4. [Dòng 291] if (jMerchantReturn !== undefined
  5. [Dòng 291] jMerchantReturn !== null) {
  6. [Dòng 306] if (responseCode !== undefined
  7. [Dòng 306] responseCode !== null
  8. [Dòng 334] if (parentRes !== "{

================================================================================

📁 FILE 108: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/SeaBank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 109: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 110: shb.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Shb/assets/js/shb.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 234] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 240] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 256] if (xhr.status === 200
  8. [Dòng 256] xhr.status === 201) {
  9. [Dòng 259] if (invoiceState === "unpaid"
  10. [Dòng 259] invoiceState === "not_paid") {
  11. [Dòng 264] if (paymentState === "authorization_required") {
  12. [Dòng 270] if (method === "REDIRECT") {
  13. [Dòng 273] } else if (method === "POST_REDIRECT") {
  14. [Dòng 312] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 295] if (jLinks !== undefined
  3. [Dòng 295] jLinks !== null) {
  4. [Dòng 297] if (jMerchantReturn !== undefined
  5. [Dòng 297] jMerchantReturn !== null) {
  6. [Dòng 312] if (responseCode !== undefined
  7. [Dòng 312] responseCode !== null
  8. [Dòng 340] if (parentRes !== "{

================================================================================

📁 FILE 111: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Shb/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 112: shb2.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Shb/shb2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 73] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 85] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 74] if ($('.circle_v2').css('display') == 'block'
  2. [Dòng 110] if ($('.circle_v1').css('display') == 'block'
  3. [Dòng 114] if ( $('.circle_v3').css('display') == 'block' ) {
  4. [Dòng 299] if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 87] if (e !== null) {
  2. [Dòng 271] if (lang !== "vi") lang = "en";
  3. [Dòng 301] if (value !== "") {

================================================================================

📁 FILE 113: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 114: tpbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Tp_Bank/assets/js/tpbank.js
📊 Thống kê: 23 điều kiện duy nhất
   - === : 14 lần
   - == : 1 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 239] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 261] if (xhr.status === 200
  8. [Dòng 261] xhr.status === 201) {
  9. [Dòng 264] if (invoiceState === "unpaid"
  10. [Dòng 264] invoiceState === "not_paid") {
  11. [Dòng 269] if (paymentState === "authorization_required") {
  12. [Dòng 275] if (method === "REDIRECT") {
  13. [Dòng 278] } else if (method === "POST_REDIRECT") {
  14. [Dòng 317] responseCode === "0") {

== (1 điều kiện):
  1. [Dòng 167] if ($('.circle_v1').css('display') == 'block') {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 300] if (jLinks !== undefined
  3. [Dòng 300] jLinks !== null) {
  4. [Dòng 302] if (jMerchantReturn !== undefined
  5. [Dòng 302] jMerchantReturn !== null) {
  6. [Dòng 317] if (responseCode !== undefined
  7. [Dòng 317] responseCode !== null
  8. [Dòng 345] if (parentRes !== "{

================================================================================

📁 FILE 115: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Tp_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 116: tpbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Tp_Bank/tpbank2.js
📊 Thống kê: 15 điều kiện duy nhất
   - === : 8 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 71] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 78] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 81] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 90] year === y
  5. [Dòng 92] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 102] if ("PAY" === operation) {
  7. [Dòng 349] } else if (value === "") {
  8. [Dòng 366] if (trName.style.display === "") {

== (4 điều kiện):
  1. [Dòng 73] if ($('.circle_v1').css('display') == 'block'
  2. [Dòng 76] $('.circle_v1').css('display') == 'block'
  3. [Dòng 126] if ( $('.circle_v1').css('display') == 'block') {
  4. [Dòng 131] else if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 104] if (e !== null) {
  2. [Dòng 288] if (lang !== "vi") lang = "en";
  3. [Dòng 318] if (value !== "") {

================================================================================

📁 FILE 117: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 118: onepay.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Visa/assets/js/onepay.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 78] if (i % 2 === parity) d *= 2;
  2. [Dòng 82] return (sum % 10) === 0
  3. [Dòng 152] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 155] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 240] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 259] if (xhr.status === 200
  8. [Dòng 259] xhr.status === 201) {
  9. [Dòng 261] if (invoiceState === "unpaid"
  10. [Dòng 261] invoiceState === "not_paid") {
  11. [Dòng 265] if (paymentState === "authorization_required") {
  12. [Dòng 269] if (method === "REDIRECT") {
  13. [Dòng 272] } else if (method === "POST_REDIRECT") {
  14. [Dòng 311] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 130] this.oldValue !== this.value) {
  2. [Dòng 294] if (jLinks !== undefined
  3. [Dòng 294] jLinks !== null) {
  4. [Dòng 296] if (jMerchantReturn !== undefined
  5. [Dòng 296] jMerchantReturn !== null) {
  6. [Dòng 311] if (responseCode !== undefined
  7. [Dòng 311] responseCode !== null
  8. [Dòng 339] if (parentRes !== "{

================================================================================

📁 FILE 119: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Visa/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 120: index.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Visa/index.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 19] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 29] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 32] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 44] year === y
  5. [Dòng 46] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 77] if ("PAY" === operation) {
  7. [Dòng 168] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 79] if (e !== null) {
  2. [Dòng 125] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 121: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 122: vpbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Vp_Bank/assets/js/vpbank.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 230] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 236] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 252] if (xhr.status === 200
  8. [Dòng 252] xhr.status === 201) {
  9. [Dòng 255] if (invoiceState === "unpaid"
  10. [Dòng 255] invoiceState === "not_paid") {
  11. [Dòng 260] if (paymentState === "authorization_required") {
  12. [Dòng 266] if (method === "REDIRECT") {
  13. [Dòng 269] } else if (method === "POST_REDIRECT") {
  14. [Dòng 308] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 291] if (jLinks !== undefined
  3. [Dòng 291] jLinks !== null) {
  4. [Dòng 293] if (jMerchantReturn !== undefined
  5. [Dòng 293] jMerchantReturn !== null) {
  6. [Dòng 308] if (responseCode !== undefined
  7. [Dòng 308] responseCode !== null
  8. [Dòng 336] if (parentRes !== "{

================================================================================

📁 FILE 123: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Vp_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 124: vpbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/Vp_Bank/vpbank2.js
📊 Thống kê: 14 điều kiện duy nhất
   - === : 11 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 32] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 35] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 44] year === y
  5. [Dòng 46] if (inPhone.value === "") return err("MISSING_FIELD"
  6. [Dòng 49] if (inName.value === "") return err("MISSING_FIELD"
  7. [Dòng 59] if ("PAY" === operation) {
  8. [Dòng 292] } else if (value === "") {
  9. [Dòng 309] if (trPhone.style.display === "") {
  10. [Dòng 311] } else if (trName.style.display === "") {
  11. [Dòng 332] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 61] if (e !== null) {
  2. [Dòng 237] if (lang !== "vi") lang = "en";
  3. [Dòng 267] if (value !== "") {

================================================================================

📁 FILE 125: sha.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/assets/js/sha.js
📊 Thống kê: 49 điều kiện duy nhất
   - === : 38 lần
   - == : 0 lần
   - !== : 11 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (38 điều kiện):
  1. [Dòng 12] 1>t)throw Error("numRounds must a integer >= 1");if("SHA-1"===c)m=512,q=K,u=Z,f=160,r=function(a){return a.slice()};else if(0===c.lastIndexOf("SHA-",0))if(q=function(a,b){return L(a,b,c)
  2. [Dòng 12] c)},u=function(a,b,h,e){var k,f;if("SHA-224"===c
  3. [Dòng 12] "SHA-256"===c)k=(b+65>>>9<<4)+15,f=16;else if("SHA-384"===c
  4. [Dòng 12] "SHA-512"===c)k=(b+129>>>10<<
  5. [Dòng 13] c);if("SHA-224"===c)a=[e[0],e[1],e[2],e[3],e[4],e[5],e[6]];else if("SHA-256"===c)a=e;else if("SHA-384"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,e[4].b,e[5].a,e[5].b];else if("SHA-512"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,
  6. [Dòng 14] "SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)
  7. [Dòng 14] 0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
  8. [Dòng 14] return a},r=function(a){return a.slice()},"SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)||0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
  9. [Dòng 15] c)m=1152,f=224;else if("SHA3-256"===c)m=1088,f=256;else if("SHA3-384"===c)m=832,f=384;else if("SHA3-512"===c)m=576,f=512;else if("SHAKE128"===c)m=1344,f=-1,F=31,z=!0;else if("SHAKE256"===c)m=1088,f=-1,F=31,z=!0;else throw Error("Chosen SHA variant is not supported");u=function(a
  10. [Dòng 16] 0===64*l%e
  11. [Dòng 16] 5][l/5|0];g.push(a.b);if(32*g.length>=h)break;g.push(a.a);l+=1;0===64*l%e&&D(null,b)}return g}}else throw Error("Chosen SHA variant is not supported");d=M(a,g,x);l=A(c);this.setHMACKey=function(a,b,h){var k;if(!0===I)throw Error("HMAC key already set");if(!0===y)throw Error("Cannot set HMAC key after calling update");if(!0===z)throw Error("SHAKE is not supported for HMAC");g=(h
  12. [Dòng 17] f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
  13. [Dòng 17] )b.push(0);b[h]&=4294967040}for(a=0;a<=h;a+=1)v[a]=b[a]^909522486,w[a]=b[a]^1549556828;l=q(v,l);e=m;I=!0};this.update=function(a){var c,b,k,f=0,g=m>>>5;c=d(a,h,n);a=c.binLen;b=c.value;c=a>>>5;for(k=0;k<c;k+=g)f+m<=a&&(l=q(b.slice(k,k+g),l),f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
  14. [Dòng 18] for(g=1;g<t;g+=1)!0===z
  15. [Dòng 19] f);return k(m)};this.getHMAC=function(a,b){var k,g,d,p;if(!1===I)throw Error("Cannot call getHMAC without first setting HMAC key");d=N(b);switch(a){case "HEX":k=function(a){return O(a,f,x,d)};break;case "B64":k=function(a){return P(a,f,x,d)};break;case "BYTES":k=function(a){return Q(a,f,x)};break;case "ARRAYBUFFER":try{k=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}k=function(a){return R(a,f,x)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
  16. [Dòng 20] d=-1===b?3:0
  17. [Dòng 20] f=-1===b?3:0
  18. [Dòng 21] g=-1===b?3:0
  19. [Dòng 22] !0===c.hasOwnProperty("b64Pad")
  20. [Dòng 22] a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");
  21. [Dòng 23] u=-1===b?3:0
  22. [Dòng 23] l+=2){p=parseInt(a.substr(l,2),16);if(isNaN(p))throw Error("String of HEX type contains invalid characters");m=(l>>>1)+q;for(f=m>>>2;c.length<=f;)c.push(0);c[f]|=p<<8*(u+m%4*b)}return{value:c,binLen:4*g+d}};break;case "TEXT":c=function(c,h,d){var g,l,p=0,f,m,q,u,r,t;h=h||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(t=-1===
  23. [Dòng 24] m+=1){r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=l[m]<<8*(t+r%4*b);p+=1}else if("UTF16BE"===a
  24. [Dòng 24] "UTF16LE"===a)for(t=-1===b?2:0
  25. [Dòng 24] UTF16LE"===a
  26. [Dòng 24] 1===b
  27. [Dòng 25] !0===l
  28. [Dòng 25] !0===l&&(m=g&255,g=m<<8|g>>>8);r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=g<<8*(t+r%4*b);p+=2}return{value:h,binLen:8*p+d}};break;case "B64":c=function(a,c,d){var g=0,l,p,f,m,q,u,r,t;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");p=a.indexOf("=");a=a.replace(/\=/g
  29. [Dòng 25] t=-1===b?3:0
  30. [Dòng 26] q=-1===b?3:0
  31. [Dòng 27] m=-1===b?3:0
  32. [Dòng 32] c.b^a.b)}function A(c){var a=[],d;if("SHA-1"===c)a=[1732584193,4023233417,2562383102,271733878,3285377520];else if(0===c.lastIndexOf("SHA-",0))switch(a=
  33. [Dòng 34] new b(d[2],4271175723),new b(d[3],1595750129),new b(d[4],2917565137),new b(d[5],725511199),new b(d[6],4215389547),new b(d[7],327033209)];break;default:throw Error("Unknown SHA variant");}else if(0===c.lastIndexOf("SHA3-",0)
  34. [Dòng 34] 0===c.lastIndexOf("SHAKE",0))for(c=0
  35. [Dòng 36] a,k){var e,h,n,g,l,p,f,m,q,u,r,t,v,w,y,A,z,x,F,B,C,D,E=[],J;if("SHA-224"===k
  36. [Dòng 36] "SHA-256"===k)u=64,t=1,D=Number,v=G,w=la,y=H,A=ha,z=ja,x=da,F=fa,C=U,B=aa,J=d;else if("SHA-384"===k
  37. [Dòng 36] "SHA-512"===k)u=80,t=2,D=b,v=ma,w=na,y=oa,A=ia,z=ka,x=ea,F=ga,C=ca,B=ba,J=V;else throw Error("Unexpected error in SHA-2 implementation");k=a[0];e=a[1];h=a[2];n=a[3];g=a[4];l=a[5];p=a[6];f=a[7];for(r=0
  38. [Dòng 45] "function"===typeof define

!== (11 điều kiện):
  1. [Dòng 12] 'use strict';(function(Y){function C(c,a,b){var e=0,h=[],n=0,g,l,d,f,m,q,u,r,I=!1,v=[],w=[],t,y=!1,z=!1,x=-1;b=b||{};g=b.encoding||"UTF8";t=b.numRounds||1;if(t!==parseInt(t,10)
  2. [Dòng 18] 0!==f%32
  3. [Dòng 22] a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8
  4. [Dòng 23] }switch(c){case "HEX":c=function(a,c,d){var g=a.length,l,p,f,m,q,u;if(0!==g%2)throw Error("String of HEX type must be in byte increments");c=c||[0];d=d||0;q=d>>>3;u=-1===b?3:0;for(l=0
  5. [Dòng 24] 1!==b
  6. [Dòng 24] "UTF16LE"!==a
  7. [Dòng 25] "");if(-1!==p
  8. [Dòng 27] function(a,c,d){var g,l,p,f,m,q;c=c||[0];d=d||0;l=d>>>3;m=-1===b?3:0;q=new Uint8Array(a);for(g=0;g<a.byteLength;g+=1)f=g+l,p=f>>>2,c.length<=p&&c.push(0),c[p]|=q[g]<<8*(m+f%4*b);return{value:c,binLen:8*a.byteLength+d}};break;default:throw Error("format must be HEX, TEXT, B64, BYTES, or ARRAYBUFFER");}return c}function y(c,a){return c<<a|c>>>32-a}function S(c,a){return 32<a?(a-=32,new b(c.b<<a|c.a>>>32-a,c.a<<a|c.b>>>32-a)):0!==a?new b(c.a<<a|c.b>>>32-a,c.b<<a|c.a>>>32-a):c}function w(c,a){return c>>>
  9. [Dòng 37] a[7]);return a}function D(c,a){var d,e,h,n,g=[],l=[];if(null!==c)for(e=0
  10. [Dòng 45] define.amd?define(function(){return C}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=C),exports=C):Y.jsSHA=C})(this);
  11. [Dòng 45] return C}):"undefined"!==typeof exports?("undefined"!==typeof module

================================================================================

📁 FILE 126: sha256.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/assets/js/sha256.js
📊 Thống kê: 28 điều kiện duy nhất
   - === : 20 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (20 điều kiện):
  1. [Dòng 12] 1>u)throw Error("numRounds must a integer >= 1");if(0===c.lastIndexOf("SHA-",0))if(q=function(b,a){return A(b,a,c)
  2. [Dòng 12] c)},y=function(b,a,l,f){var g,e;if("SHA-224"===c
  3. [Dòng 12] "SHA-256"===c)g=(a+65>>>9<<4)+15,e=16;else throw Error("Unexpected error in SHA-2 implementation");for(
  4. [Dòng 13] c);if("SHA-224"===c)b=[f[0],f[1],f[2],f[3],f[4],f[5],f[6]];else if("SHA-256"===c)b=f;else throw Error("Unexpected error in SHA-2 implementation");return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
  5. [Dòng 13] "SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a
  6. [Dòng 13] return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
  7. [Dòng 14] if(!0===z)throw Error("Cannot set HMAC key after calling update");f=(g
  8. [Dòng 15] 5);g=a%h;z=!0};this.getHash=function(a,f){var d,h,k,q;if(!0===m)throw Error("Cannot call getHash after setting HMAC key");k=C(f);switch(a){case "HEX":d=function(a){return D(a,e,k)};break;case "B64":d=function(a){return E(a,e,k)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{h=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("format must be HEX, B64, BYTES, or ARRAYBUFFER");
  9. [Dòng 16] x(c));return d(q)};this.getHMAC=function(a,f){var d,k,t,u;if(!1===m)throw Error("Cannot call getHMAC without first setting HMAC key");t=C(f);switch(a){case "HEX":d=function(a){return D(a,e,t)};break;case "B64":d=function(a){return E(a,e,t)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{d=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
  10. [Dòng 18] !0===c.hasOwnProperty("b64Pad")
  11. [Dòng 20] h=(d>>>1)+q;for(e=h>>>2;b.length<=e;)b.push(0);b[e]|=k<<8*(3+h%4*-1)}return{value:b,binLen:4*f+c}};break;case "TEXT":d=function(c,b,d){var f,n,k=0,e,h,q,m,p,r;b=b||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(r=3
  12. [Dòng 21] q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=n[h]<<8*(r+p%4*-1);k+=1}else if("UTF16BE"===a
  13. [Dòng 21] "UTF16LE"===a)for(r=2
  14. [Dòng 21] UTF16LE"===a
  15. [Dòng 21] !0===n
  16. [Dòng 21] e+=1){f=c.charCodeAt(e);!0===n&&(h=f&255,f=h<<8|f>>>8);p=k+q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=f<<8*(r+p%4*-1);k+=2}return{value:b,binLen:8*k+d}};break;case "B64":d=function(a,b,c){var f=0,d,k,e,h,q,m,p;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");k=a.indexOf("=");a=a.replace(/\=/g
  17. [Dòng 25] 16)&65535)<<16|b&65535}function R(c,a,d,l,b){var g=(c&65535)+(a&65535)+(d&65535)+(l&65535)+(b&65535);return((c>>>16)+(a>>>16)+(d>>>16)+(l>>>16)+(b>>>16)+(g>>>16)&65535)<<16|g&65535}function x(c){var a=[],d;if(0===c.lastIndexOf("SHA-",0))switch(a=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428]
  18. [Dòng 26] new m,new m,new m,new m,new m,new m];break;case "SHA-512":a=[new m,new m,new m,new m,new m,new m,new m,new m];break;default:throw Error("Unknown SHA variant");}else throw Error("No SHA variants supported");return a}function A(c,a,d){var l,b,g,f,n,k,e,h,m,r,p,w,t,x,u,z,A,B,C,D,E,F,v=[],G;if("SHA-224"===d
  19. [Dòng 26] "SHA-256"===d)r=64,w=1,F=Number,t=P,x=Q,u=R,z=N,A=O,B=L,C=M,E=K,D=J,G=H;else throw Error("Unexpected error in SHA-2 implementation");d=a[0];l=a[1];b=a[2];g=a[3];f=a[4];n=a[5];k=a[6];e=a[7];for(p=
  20. [Dòng 29] "function"===typeof define

!== (8 điều kiện):
  1. [Dòng 12] 'use strict';(function(I){function w(c,a,d){var l=0,b=[],g=0,f,n,k,e,h,q,y,p,m=!1,t=[],r=[],u,z=!1;d=d||{};f=d.encoding||"UTF8";u=d.numRounds||1;if(u!==parseInt(u,10)
  2. [Dòng 18] l+=1)g[l]=c[l>>>2]>>>8*(3+l%4*-1)&255;return b}function C(c){var a={outputUpper:!1,b64Pad:"=",shakeLen:-1};c=c||{};a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");
  3. [Dòng 19] if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0;d<f;d+=2){k=parseInt(a.substr(d,2),16);if(isNaN(k))throw Error("String of HEX type contains invalid characters");
  4. [Dòng 19] if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0
  5. [Dòng 21] "UTF16LE"!==a
  6. [Dòng 22] "");if(-1!==k
  7. [Dòng 29] define.amd?define(function(){return w}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=w),exports=w):I.jsSHA=w})(this);
  8. [Dòng 29] return w}):"undefined"!==typeof exports?("undefined"!==typeof module

================================================================================

📁 FILE 127: slick.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/assets/libraries/slick/slick.js
📊 Thống kê: 182 điều kiện duy nhất
   - === : 126 lần
   - == : 5 lần
   - !== : 47 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (126 điều kiện):
  1. [Dòng 20] if (typeof define === 'function'
  2. [Dòng 206] if (typeof(index) === 'boolean') {
  3. [Dòng 215] if (typeof(index) === 'number') {
  4. [Dòng 216] if (index === 0
  5. [Dòng 216] _.$slides.length === 0) {
  6. [Dòng 224] if (addBefore === true) {
  7. [Dòng 249] if (_.options.slidesToShow === 1
  8. [Dòng 249] _.options.adaptiveHeight === true
  9. [Dòng 249] _.options.vertical === false) {
  10. [Dòng 264] if (_.options.rtl === true
  11. [Dòng 267] if (_.transformsEnabled === false) {
  12. [Dòng 268] if (_.options.vertical === false) {
  13. [Dòng 280] if (_.cssTransitions === false) {
  14. [Dòng 281] if (_.options.rtl === true) {
  15. [Dòng 355] typeof asNavFor === 'object' ) {
  16. [Dòng 371] if (_.options.fade === false) {
  17. [Dòng 414] if ( _.options.infinite === false ) {
  18. [Dòng 416] if ( _.direction === 1
  19. [Dòng 416] ( _.currentSlide + 1 ) === ( _.slideCount - 1 )) {
  20. [Dòng 420] else if ( _.direction === 0 ) {
  21. [Dòng 424] if ( _.currentSlide - 1 === 0 ) {
  22. [Dòng 442] if (_.options.arrows === true ) {
  23. [Dòng 487] if (_.options.dots === true
  24. [Dòng 524] _.$slideTrack = (_.slideCount === 0) ?
  25. [Dòng 532] if (_.options.centerMode === true
  26. [Dòng 532] _.options.swipeToSlide === true) {
  27. [Dòng 547] _.setSlideClasses(typeof _.currentSlide === 'number' ? _.currentSlide : 0);
  28. [Dòng 549] if (_.options.draggable === true) {
  29. [Dòng 602] if (_.respondTo === 'window') {
  30. [Dòng 604] } else if (_.respondTo === 'slider') {
  31. [Dòng 606] } else if (_.respondTo === 'min') {
  32. [Dòng 618] if (_.originalSettings.mobileFirst === false) {
  33. [Dòng 635] if (_.breakpointSettings[targetBreakpoint] === 'unslick') {
  34. [Dòng 641] if (initial === true) {
  35. [Dòng 705] slideOffset = indexOffset === 0 ? _.options.slidesToScroll : _.options.slidesToShow - indexOffset
  36. [Dòng 712] slideOffset = indexOffset === 0 ? _.options.slidesToScroll : indexOffset
  37. [Dòng 719] var index = event.data.index === 0 ? 0 :
  38. [Dòng 765] if (_.options.accessibility === true) {
  39. [Dòng 772] if (_.options.arrows === true
  40. [Dòng 797] if (_.options.focusOnSelect === true) {
  41. [Dòng 836] if (_.shouldClick === false) {
  42. [Dòng 1051] if (_.options.infinite === true) {
  43. [Dòng 1061] } else if (_.options.centerMode === true) {
  44. [Dòng 1094] if (_.options.vertical === true
  45. [Dòng 1094] _.options.centerMode === true) {
  46. [Dòng 1095] if (_.options.slidesToShow === 2) {
  47. [Dòng 1097] } else if (_.options.slidesToShow === 1) {
  48. [Dòng 1128] } else if (_.options.centerMode === true
  49. [Dòng 1128] _.options.infinite === true) {
  50. [Dòng 1141] if (_.options.variableWidth === true) {
  51. [Dòng 1143] _.options.infinite === false) {
  52. [Dòng 1159] if (_.options.centerMode === true) {
  53. [Dòng 1200] if (_.options.infinite === false) {
  54. [Dòng 1229] centerOffset = _.options.centerMode === true ? _.slideWidth * Math.floor(_.options.slidesToShow / 2) : 0
  55. [Dòng 1231] if (_.options.swipeToSlide === true) {
  56. [Dòng 1406] _.options.pauseOnDotsHover === true
  57. [Dòng 1498] if (event.keyCode === 37
  58. [Dòng 1498] _.options.accessibility === true) {
  59. [Dòng 1501] message: _.options.rtl === true ? 'next' :
  60. [Dòng 1504] } else if (event.keyCode === 39
  61. [Dòng 1507] message: _.options.rtl === true ? 'previous' : 'next'
  62. [Dòng 1585] if (_.options.fade === true) {
  63. [Dòng 1593] if (_.options.lazyLoad === 'anticipated') {
  64. [Dòng 1616] } else if (_.currentSlide === 0) {
  65. [Dòng 1637] if (_.options.lazyLoad === 'progressive') {
  66. [Dòng 1773] if ( _.options.adaptiveHeight === true ) {
  67. [Dòng 1864] if ( $.type(responsiveSettings) === 'array'
  68. [Dòng 1878] _.breakpoints[l] === currentBreakpoint ) {
  69. [Dòng 1969] index = removeBefore === true ? 0 : _.slideCount - 1
  70. [Dòng 1971] index = removeBefore === true ? --index : index
  71. [Dòng 1980] if (removeAll === true) {
  72. [Dòng 2050] if (_.options.vertical === false
  73. [Dòng 2050] _.options.variableWidth === false) {
  74. [Dòng 2054] } else if (_.options.variableWidth === true) {
  75. [Dòng 2062] if (_.options.variableWidth === false) _.$slideTrack.children('.slick-slide').width(_.slideWidth - offset);
  76. [Dòng 2128] if( $.type( arguments[0] ) === 'object' ) {
  77. [Dòng 2134] } else if ( $.type( arguments[0] ) === 'string' ) {
  78. [Dòng 2140] if ( arguments[0] === 'responsive'
  79. [Dòng 2140] $.type( arguments[1] ) === 'array' ) {
  80. [Dòng 2152] if ( type === 'single' ) {
  81. [Dòng 2157] } else if ( type === 'multiple' ) {
  82. [Dòng 2166] } else if ( type === 'responsive' ) {
  83. [Dòng 2181] if( _.options.responsive[l].breakpoint === value[item].breakpoint ) {
  84. [Dòng 2231] _.positionProp = _.options.vertical === true ? 'top' : 'left'
  85. [Dòng 2233] if (_.positionProp === 'top') {
  86. [Dòng 2242] if (_.options.useCSS === true) {
  87. [Dòng 2248] if ( typeof _.options.zIndex === 'number' ) {
  88. [Dòng 2261] if (bodyStyle.perspectiveProperty === undefined
  89. [Dòng 2261] bodyStyle.webkitPerspective === undefined) _.animType = false;
  90. [Dòng 2267] bodyStyle.MozPerspective === undefined) _.animType = false;
  91. [Dòng 2279] if (bodyStyle.msTransform === undefined) _.animType = false;
  92. [Dòng 2306] var evenCoef = _.options.slidesToShow % 2 === 0 ? 1 : 0
  93. [Dòng 2328] if (index === 0) {
  94. [Dòng 2334] } else if (index === _.slideCount - 1) {
  95. [Dòng 2366] indexOffset = _.options.infinite === true ? _.options.slidesToShow + index : index
  96. [Dòng 2388] if (_.options.lazyLoad === 'ondemand'
  97. [Dòng 2388] _.options.lazyLoad === 'anticipated') {
  98. [Dòng 2402] if (_.options.infinite === true
  99. [Dòng 2402] _.options.fade === false) {
  100. [Dòng 2479] if (_.animating === true
  101. [Dòng 2479] _.options.waitForAnimate === true) {
  102. [Dòng 2483] if (_.options.fade === true
  103. [Dòng 2483] _.currentSlide === index) {
  104. [Dòng 2487] if (sync === false) {
  105. [Dòng 2495] _.currentLeft = _.swipeLeft === null ? slideLeft : _.swipeLeft
  106. [Dòng 2497] if (_.options.infinite === false
  107. [Dòng 2497] _.options.centerMode === false
  108. [Dòng 2509] } else if (_.options.infinite === false
  109. [Dòng 2509] _.options.centerMode === true
  110. [Dòng 2627] return (_.options.rtl === false ? 'left' : 'right');
  111. [Dòng 2633] return (_.options.rtl === false ? 'right' : 'left');
  112. [Dòng 2635] if (_.options.verticalSwiping === true) {
  113. [Dòng 2664] if ( _.touchObject.curX === undefined ) {
  114. [Dòng 2668] if ( _.touchObject.edgeHit === true ) {
  115. [Dòng 2732] if ((_.options.swipe === false) || ('ontouchend' in document
  116. [Dòng 2732] _.options.swipe === false)) {
  117. [Dòng 2734] } else if (_.options.draggable === false
  118. [Dòng 2806] positionOffset = (_.options.rtl === false ? 1 : -1) * (_.touchObject.curX > _.touchObject.startX ? 1 : -1);
  119. [Dòng 2817] if ((_.currentSlide === 0
  120. [Dòng 2817] swipeDirection === 'right') || (_.currentSlide >= _.getDotCount()
  121. [Dòng 2817] swipeDirection === 'left')) {
  122. [Dòng 2832] _.options.touchMove === false) {
  123. [Dòng 2836] if (_.animating === true) {
  124. [Dòng 2926] if ( _.options.arrows === true
  125. [Dòng 2933] if (_.currentSlide === 0) {
  126. [Dòng 2938] _.options.centerMode === false) {

== (5 điều kiện):
  1. [Dòng 2007] x = _.positionProp == 'left' ? Math.ceil(position) + 'px' : '0px'
  2. [Dòng 2008] y = _.positionProp == 'top' ? Math.ceil(position) + 'px' : '0px'
  3. [Dòng 2368] if (_.options.slidesToShow == _.options.slidesToScroll
  4. [Dòng 3002] if (typeof opt == 'object'
  5. [Dòng 3002] typeof opt == 'undefined')

!== (47 điều kiện):
  1. [Dòng 22] } else if (typeof exports !== 'undefined') {
  2. [Dòng 155] if (typeof document.mozHidden !== 'undefined') {
  3. [Dòng 158] } else if (typeof document.webkitHidden !== 'undefined') {
  4. [Dòng 342] asNavFor !== null ) {
  5. [Dòng 355] if ( asNavFor !== null
  6. [Dòng 460] if (_.options.infinite !== true) {
  7. [Dòng 612] _.options.responsive !== null) {
  8. [Dòng 630] if (targetBreakpoint !== null) {
  9. [Dòng 631] if (_.activeBreakpoint !== null) {
  10. [Dòng 632] if (targetBreakpoint !== _.activeBreakpoint
  11. [Dòng 676] triggerBreakpoint !== false ) {
  12. [Dòng 699] unevenOffset = (_.slideCount % _.options.slidesToScroll !== 0);
  13. [Dòng 758] _.$dots !== null) {
  14. [Dòng 997] if (filter !== null) {
  15. [Dòng 1103] if (_.slideCount % _.options.slidesToScroll !== 0) {
  16. [Dòng 1314] if (_.$dots !== null) {
  17. [Dòng 1324] if (slideControlIndex !== -1) {
  18. [Dòng 1910] _.currentSlide !== 0) {
  19. [Dòng 1953] if ($(window).width() !== _.windowWidth) {
  20. [Dòng 2144] } else if ( typeof arguments[1] !== 'undefined' ) {
  21. [Dòng 2170] if( $.type( _.options.responsive ) !== 'array' ) {
  22. [Dòng 2239] if (bodyStyle.WebkitTransition !== undefined
  23. [Dòng 2240] bodyStyle.MozTransition !== undefined
  24. [Dòng 2241] bodyStyle.msTransition !== undefined) {
  25. [Dòng 2257] if (bodyStyle.OTransform !== undefined) {
  26. [Dòng 2263] if (bodyStyle.MozTransform !== undefined) {
  27. [Dòng 2269] if (bodyStyle.webkitTransform !== undefined) {
  28. [Dòng 2275] if (bodyStyle.msTransform !== undefined) {
  29. [Dòng 2281] if (bodyStyle.transform !== undefined
  30. [Dòng 2281] _.animType !== false) {
  31. [Dòng 2286] _.transformsEnabled = _.options.useTransform && (_.animType !== null
  32. [Dòng 2286] _.animType !== false);
  33. [Dòng 2500] if (dontAnimate !== true
  34. [Dòng 2567] if (dontAnimate !== true) {
  35. [Dòng 2717] if ( _.touchObject.startX !== _.touchObject.curX ) {
  36. [Dòng 2734] event.type.indexOf('mouse') !== -1) {
  37. [Dòng 2738] event.originalEvent.touches !== undefined ?
  38. [Dòng 2773] touches = event.originalEvent !== undefined ? event.originalEvent.touches : null
  39. [Dòng 2775] touches.length !== 1) {
  40. [Dòng 2781] _.touchObject.curX = touches !== undefined ? touches[0].pageX : event.clientX
  41. [Dòng 2782] _.touchObject.curY = touches !== undefined ? touches[0].pageY : event.clientY
  42. [Dòng 2801] if (event.originalEvent !== undefined
  43. [Dòng 2852] if (_.touchObject.fingerCount !== 1
  44. [Dòng 2857] event.originalEvent.touches !== undefined) {
  45. [Dòng 2861] _.touchObject.startX = _.touchObject.curX = touches !== undefined ? touches.pageX : event.clientX
  46. [Dòng 2862] _.touchObject.startY = _.touchObject.curY = touches !== undefined ? touches.pageY : event.clientY
  47. [Dòng 2872] if (_.$slidesCache !== null) {

!= (4 điều kiện):
  1. [Dòng 805] $('[draggable!=true]', _.$slideTrack).off('dragstart', _.preventDefault);
  2. [Dòng 1467] $('[draggable!=true]', _.$slideTrack).on('dragstart', _.preventDefault);
  3. [Dòng 2707] if( direction != 'vertical' ) {
  4. [Dòng 3006] if (typeof ret != 'undefined') return ret;

================================================================================

📁 FILE 128: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 129: atm_b1.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_1/assets/js/atm_b1.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 228] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 234] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 258] if (paymentState === "authorization_required") {
  12. [Dòng 264] if (method === "REDIRECT") {
  13. [Dòng 267] } else if (method === "POST_REDIRECT") {
  14. [Dòng 306] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 289] if (jLinks !== undefined
  3. [Dòng 289] jLinks !== null) {
  4. [Dòng 291] if (jMerchantReturn !== undefined
  5. [Dòng 291] jMerchantReturn !== null) {
  6. [Dòng 306] if (responseCode !== undefined
  7. [Dòng 306] responseCode !== null
  8. [Dòng 334] if (parentRes !== "{

================================================================================

📁 FILE 130: atm_b1_2.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_1/atm_b1_2.js
📊 Thống kê: 11 điều kiện duy nhất
   - === : 8 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 52] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 55] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 64] year === y
  5. [Dòng 66] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 75] if ("PAY" === operation) {
  7. [Dòng 319] } else if (value === "") {
  8. [Dòng 336] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 77] if (e !== null) {
  2. [Dòng 253] if (lang !== "vi") lang = "en";
  3. [Dòng 283] if (value !== "") {

================================================================================

📁 FILE 131: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_1/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 132: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 133: atm_b2.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_2/assets/js/atm_b2.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 231] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 236] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 252] if (invoiceState === "unpaid"
  10. [Dòng 252] invoiceState === "not_paid") {
  11. [Dòng 256] if (paymentState === "authorization_required") {
  12. [Dòng 260] if (method === "REDIRECT") {
  13. [Dòng 263] } else if (method === "POST_REDIRECT") {
  14. [Dòng 302] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 285] if (jLinks !== undefined
  3. [Dòng 285] jLinks !== null) {
  4. [Dòng 287] if (jMerchantReturn !== undefined
  5. [Dòng 287] jMerchantReturn !== null) {
  6. [Dòng 302] if (responseCode !== undefined
  7. [Dòng 302] responseCode !== null
  8. [Dòng 330] if (parentRes !== "{

================================================================================

📁 FILE 134: atm_b2_2.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_2/atm_b2_2.js
📊 Thống kê: 6 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 41] if (inName.value === "") return err("MISSING_FIELD"
  3. [Dòng 51] if ("PAY" === operation) {

!== (3 điều kiện):
  1. [Dòng 53] if (e !== null) {
  2. [Dòng 229] if (lang !== "vi") lang = "en";
  3. [Dòng 259] if (value !== "") {

================================================================================

📁 FILE 135: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/atm_bank_2/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 136: common.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/common.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 42] if (i % 2 === parity) d *= 2;
  2. [Dòng 46] return (sum % 10) === 0
  3. [Dòng 116] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 119] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 205] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 211] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 227] if (xhr.status === 200
  8. [Dòng 227] xhr.status === 201) {
  9. [Dòng 230] if (invoiceState === "unpaid"
  10. [Dòng 230] invoiceState === "not_paid") {
  11. [Dòng 235] if (paymentState === "authorization_required") {
  12. [Dòng 241] if (method === "REDIRECT") {
  13. [Dòng 244] } else if (method === "POST_REDIRECT") {
  14. [Dòng 283] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 94] this.oldValue !== this.value) {
  2. [Dòng 266] if (jLinks !== undefined
  3. [Dòng 266] jLinks !== null) {
  4. [Dòng 268] if (jMerchantReturn !== undefined
  5. [Dòng 268] jMerchantReturn !== null) {
  6. [Dòng 283] if (responseCode !== undefined
  7. [Dòng 283] responseCode !== null
  8. [Dòng 311] if (parentRes !== "{

================================================================================

📁 FILE 137: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 138: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/international_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 139: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/begodi/international_card/script.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 12] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 22] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 25] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 37] year === y
  5. [Dòng 39] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 70] if ("PAY" === operation) {
  7. [Dòng 144] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 72] if (e !== null) {
  2. [Dòng 117] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 140: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/iframe/index.html
📊 Thống kê: 35 điều kiện duy nhất
   - === : 21 lần
   - == : 0 lần
   - !== : 14 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (21 điều kiện):
  1. [Dòng 10] if ((cardno.length === 15
  2. [Dòng 10] cardno.length === 16
  3. [Dòng 10] cardno.length === 19) && isMode10(cardno) === true) {
  4. [Dòng 10] isMode10(cardno) === true) {
  5. [Dòng 29] if (i % 2 === parity) d *= 2;
  6. [Dòng 33] return (sum % 10) === 0
  7. [Dòng 54] if ("PAY" === operation) {
  8. [Dòng 75] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  9. [Dòng 81] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  10. [Dòng 97] if (xhr.status === 200
  11. [Dòng 97] xhr.status === 201) {
  12. [Dòng 100] if (invoiceState === "unpaid"
  13. [Dòng 100] invoiceState === "not_paid") {
  14. [Dòng 105] if (paymentState === "authorization_required") {
  15. [Dòng 111] if (method === "REDIRECT") {
  16. [Dòng 116] } else if (method === "POST_REDIRECT") {
  17. [Dòng 183] //Customizable =================================================================================================
  18. [Dòng 229] if (typeof queryParams[key] === "undefined") {
  19. [Dòng 232] } else if (typeof queryParams[key] === "string") {
  20. [Dòng 246] if (params["CardList"] === undefined
  21. [Dòng 246] params["CardList"] === null) return;

!== (14 điều kiện):
  1. [Dòng 40] //if (event.origin !== "http://example.com:8080") return;
  2. [Dòng 58] if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
  3. [Dòng 59] if (inNumber.style.display !== "none") instrument.number = inNumber.value;
  4. [Dòng 60] if (inDate.style.display !== "none") instrument.date = inDate.value;
  5. [Dòng 61] if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
  6. [Dòng 62] if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
  7. [Dòng 63] if (inName.style.display !== "none") instrument.name = inName.value;
  8. [Dòng 78] /*if (contentType !== my_expected_type) {
  9. [Dòng 138] if (jLinks !== undefined
  10. [Dòng 138] jLinks !== null) {
  11. [Dòng 140] if (jMerchantReturn !== undefined
  12. [Dòng 140] jMerchantReturn !== null) {
  13. [Dòng 172] if (parentRes !== "{
  14. [Dòng 245] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 141: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 142: karma.conf.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/karma.conf.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 143: main.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/main.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 144: polyfills.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/polyfills.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 145: test.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast/src/test.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📋 TÓM TẮT TẤT CẢ ĐIỀU KIỆN DUY NHẤT:
====================================================================================================

=== (390 điều kiện duy nhất):
------------------------------------------------------------
1. return lang === this._translate.currentLang
2. isPopupSupport === 'True'"
3. params.timeout === 'true') {
4. this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
5. _re.body.state === 'unpaid');
6. if (this.errorCode === 'overtime'
7. this.errorCode === '253') {
8. params.name === 'CUSTOMER_INTIME'
9. params.code === '09') {
10. if (this.timeLeft === 0) {
11. if (YY % 400 === 0
12. YY % 4 === 0)) {
13. if (YYYY % 400 === 0
14. YYYY % 4 === 0)) {
15. params['code']==='09'){
16. c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
17. _inExpDate.trim().length === 0)"
18. if (target.tagName === 'A'
19. if (event.keyCode === 8
20. event.key === "Backspace"
21. if (approval.method === 'REDIRECT') {
22. } else if (approval.method === 'POST_REDIRECT') {
23. [ngStyle]="{'border-color': (type === 1
24. type === 1"
25. type === 1
26. if (_formCard.country === 'default') {
27. if ((v.substr(-1) === ' '
28. if (deviceValue === 'CA'
29. deviceValue === 'US') {
30. this.c_country = _val.value === 'default'
31. this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number === 1
32. if (this._res.state === 'unpaid'
33. this._res.state === 'not_paid') {
34. if ('op' === auth
35. } else if ('bank' === auth
36. if (this.timeLeftPaypal === 0) {
37. if (this._res.state === "unpaid"
38. this._res.state === "not_paid") {
39. "op" === auth
40. return this.d_pass_word ? this._inPassword.length === 0 : false
41. const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
42. item.method === method) : null;
43. if (v.length === 2
44. this.flag.length === 3
45. this.flag.charAt(this.flag.length - 1) === '/') {
46. if (v.length === 1) {
47. } else if (v.length === 2) {
48. v.length === 2) {
49. if (len === 2) {
50. if (M[1] === 'Chrome') {
51. if (param === key) {
52. pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
53. target === 0
54. if (typeof define === 'function'
55. } else if (typeof exports === 'object') {
56. if (number === "") return err("MISSING_FIELD"
57. if (inName.value === "") return err("MISSING_FIELD"
58. if ("PAY" === operation) {
59. if (i % 2 === parity) d *= 2;
60. return (sum % 10) === 0
61. if (typeof queryParams[key] === "undefined") {
62. } else if (typeof queryParams[key] === "string") {
63. if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
64. } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
65. if (xhr.status === 200
66. xhr.status === 201) {
67. if (invoiceState === "unpaid"
68. invoiceState === "not_paid") {
69. if (paymentState === "authorization_required") {
70. if (method === "REDIRECT") {
71. } else if (method === "POST_REDIRECT") {
72. responseCode === "0") {
73. if (inMonth.value === "") return err("MISSING_FIELD"
74. if (inYear.value === "") return err("MISSING_FIELD"
75. year === y
76. if (inPhone.value === "") return err("MISSING_FIELD"
77. } else if (value === "") {
78. if (trPhone.style.display === "") {
79. } else if (trName.style.display === "") {
80. if (trName.style.display === "") {
81. if (xhr.status === 200) {
82. if (insType === "card") {
83. if (insBrandId === "visa"
84. insBrandId === "mastercard"
85. insBrandId === "amex"
86. insBrandId === "jcb"
87. insBrandId === "cup") {
88. } else if (insBrandId === "atm") {
89. } else if (insType === "dongabank_account") {
90. } else if (insType === "techcombank_account") {
91. } else if (insType === "vib_account") {
92. } else if (insType === "bidv_account") {
93. } else if (insType === "tpbank_account") {
94. } else if (insType === "shb_account") {
95. } else if (insType === "shb_customer_id") {
96. } else if (insType === "vpbank_account") {
97. } else if (insType === "oceanbank_online_account") {
98. } else if (insType === "oceanbank_mobile_account") {
99. } else if (insType === "pvcombank_account") {
100. if (inCvv.value === "") return err("MISSING_FIELD"
101. if (inCvv.value === "") {
102. if ((cardno.length === 15
103. cardno.length === 16
104. cardno.length === 19) && isMode10(cardno) === true) {
105. isMode10(cardno) === true) {
106. if (params["CardList"] === undefined
107. params["CardList"] === null) return;
108. if (insSwiftCode === "BFTVVNVX") { //Vietcombank
109. } else if (insSwiftCode === "BIDVVNVX") { //BIDV
110. typeof exports === 'object'
111. typeof define === 'function'
112. selector === '#') {
113. if (typeof element.getRootNode === 'function') {
114. if (typeof $ === 'undefined') {
115. version[0] === minMajor
116. version[1] === minMinor
117. if (config === 'close') {
118. if (input.type === 'radio') {
119. } else if (input.type === 'checkbox') {
120. if (this._element.tagName === 'LABEL'
121. input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
122. if (config === 'toggle') {
123. if (_button.getAttribute('aria-pressed') === 'true') {
124. if (activeIndex === index) {
125. if (this._config.pause === 'hover') {
126. if (_this3._config.pause === 'hover') {
127. var isNextDirection = direction === Direction.NEXT
128. var isPrevDirection = direction === Direction.PREV
129. activeIndex === 0
130. activeIndex === lastItemIndex
131. var delta = direction === Direction.PREV ? -1 : 1
132. return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
133. if (direction === Direction.NEXT) {
134. if (typeof config === 'object') {
135. var action = typeof config === 'string' ? config : _config.slide
136. if (typeof config === 'number') {
137. } else if (typeof action === 'string') {
138. if (typeof data[action] === 'undefined') {
139. return foundElem === element
140. if (typeof _this._config.parent === 'string') {
141. return elem.getAttribute('data-parent') === _this._config.parent
142. if (actives.length === 0) {
143. typeof config === 'object'
144. if (typeof config === 'string') {
145. if (typeof data[config] === 'undefined') {
146. if (event.currentTarget.tagName === 'A') {
147. if (usePopper === void 0) {
148. if (typeof Popper === 'undefined') {
149. if (this._config.reference === 'parent') {
150. $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
151. if (typeof this._config.offset === 'function') {
152. if (this._config.display === 'static') {
153. var _config = typeof config === 'object' ? config : null
154. if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
155. event.type === 'keyup'
156. event.type === 'click') {
157. if (event && (event.type === 'click'
158. event.which === TAB_KEYCODE) && $.contains(parent
159. if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
160. event.which === ESCAPE_KEYCODE) {
161. if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
162. event.which === SPACE_KEYCODE)) {
163. if (event.which === ESCAPE_KEYCODE) {
164. if (items.length === 0) {
165. if (event.which === ARROW_UP_KEYCODE
166. if (event.which === ARROW_DOWN_KEYCODE
167. if (this._config.backdrop === 'static') {
168. $(_this5._element).has(event.target).length === 0) {
169. if (event.which === ESCAPE_KEYCODE$1) {
170. if (this.tagName === 'A'
171. this.tagName === 'AREA') {
172. if (unsafeHtml.length === 0) {
173. typeof sanitizeFn === 'function') {
174. if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
175. if (_ret === "continue") continue;
176. if ($(this.element).css('display') === 'none') {
177. var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
178. if (prevHoverState === HoverState.OUT) {
179. if (typeof content === 'object'
180. title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
181. if (typeof this.config.offset === 'function') {
182. if (this.config.container === false) {
183. if (trigger === 'click') {
184. var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
185. var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
186. context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
187. context._hoverState === HoverState.SHOW) {
188. if (context._hoverState === HoverState.SHOW) {
189. context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
190. if (context._hoverState === HoverState.OUT) {
191. if (typeof config.delay === 'number') {
192. if (typeof config.title === 'number') {
193. if (typeof config.content === 'number') {
194. var _config = typeof config === 'object'
195. if (typeof content === 'function') {
196. this._scrollElement = element.tagName === 'BODY' ? window : element
197. var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
198. var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
199. var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
200. return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
201. return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
202. var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
203. this._element.parentNode.nodeType === Node.ELEMENT_NODE
204. var itemSelector = listElement.nodeName === 'UL'
205. listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
206. var activeElements = container && (container.nodeName === 'UL'
207. container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
208. if (active.getAttribute('role') === 'tab') {
209. if (element.getAttribute('role') === 'tab') {
210. 1>t)throw Error("numRounds must a integer >= 1");if("SHA-1"===c)m=512,q=K,u=Z,f=160,r=function(a){return a.slice()};else if(0===c.lastIndexOf("SHA-",0))if(q=function(a,b){return L(a,b,c)
211. c)},u=function(a,b,h,e){var k,f;if("SHA-224"===c
212. "SHA-256"===c)k=(b+65>>>9<<4)+15,f=16;else if("SHA-384"===c
213. "SHA-512"===c)k=(b+129>>>10<<
214. c);if("SHA-224"===c)a=[e[0],e[1],e[2],e[3],e[4],e[5],e[6]];else if("SHA-256"===c)a=e;else if("SHA-384"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,e[4].b,e[5].a,e[5].b];else if("SHA-512"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,
215. "SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)
216. 0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
217. return a},r=function(a){return a.slice()},"SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)||0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
218. c)m=1152,f=224;else if("SHA3-256"===c)m=1088,f=256;else if("SHA3-384"===c)m=832,f=384;else if("SHA3-512"===c)m=576,f=512;else if("SHAKE128"===c)m=1344,f=-1,F=31,z=!0;else if("SHAKE256"===c)m=1088,f=-1,F=31,z=!0;else throw Error("Chosen SHA variant is not supported");u=function(a
219. 0===64*l%e
220. 5][l/5|0];g.push(a.b);if(32*g.length>=h)break;g.push(a.a);l+=1;0===64*l%e&&D(null,b)}return g}}else throw Error("Chosen SHA variant is not supported");d=M(a,g,x);l=A(c);this.setHMACKey=function(a,b,h){var k;if(!0===I)throw Error("HMAC key already set");if(!0===y)throw Error("Cannot set HMAC key after calling update");if(!0===z)throw Error("SHAKE is not supported for HMAC");g=(h
221. f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
222. )b.push(0);b[h]&=4294967040}for(a=0;a<=h;a+=1)v[a]=b[a]^909522486,w[a]=b[a]^1549556828;l=q(v,l);e=m;I=!0};this.update=function(a){var c,b,k,f=0,g=m>>>5;c=d(a,h,n);a=c.binLen;b=c.value;c=a>>>5;for(k=0;k<c;k+=g)f+m<=a&&(l=q(b.slice(k,k+g),l),f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
223. for(g=1;g<t;g+=1)!0===z
224. f);return k(m)};this.getHMAC=function(a,b){var k,g,d,p;if(!1===I)throw Error("Cannot call getHMAC without first setting HMAC key");d=N(b);switch(a){case "HEX":k=function(a){return O(a,f,x,d)};break;case "B64":k=function(a){return P(a,f,x,d)};break;case "BYTES":k=function(a){return Q(a,f,x)};break;case "ARRAYBUFFER":try{k=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}k=function(a){return R(a,f,x)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
225. d=-1===b?3:0
226. f=-1===b?3:0
227. g=-1===b?3:0
228. !0===c.hasOwnProperty("b64Pad")
229. a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");
230. u=-1===b?3:0
231. l+=2){p=parseInt(a.substr(l,2),16);if(isNaN(p))throw Error("String of HEX type contains invalid characters");m=(l>>>1)+q;for(f=m>>>2;c.length<=f;)c.push(0);c[f]|=p<<8*(u+m%4*b)}return{value:c,binLen:4*g+d}};break;case "TEXT":c=function(c,h,d){var g,l,p=0,f,m,q,u,r,t;h=h||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(t=-1===
232. m+=1){r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=l[m]<<8*(t+r%4*b);p+=1}else if("UTF16BE"===a
233. "UTF16LE"===a)for(t=-1===b?2:0
234. UTF16LE"===a
235. 1===b
236. !0===l
237. !0===l&&(m=g&255,g=m<<8|g>>>8);r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=g<<8*(t+r%4*b);p+=2}return{value:h,binLen:8*p+d}};break;case "B64":c=function(a,c,d){var g=0,l,p,f,m,q,u,r,t;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");p=a.indexOf("=");a=a.replace(/\=/g
238. t=-1===b?3:0
239. q=-1===b?3:0
240. m=-1===b?3:0
241. c.b^a.b)}function A(c){var a=[],d;if("SHA-1"===c)a=[1732584193,4023233417,2562383102,271733878,3285377520];else if(0===c.lastIndexOf("SHA-",0))switch(a=
242. new b(d[2],4271175723),new b(d[3],1595750129),new b(d[4],2917565137),new b(d[5],725511199),new b(d[6],4215389547),new b(d[7],327033209)];break;default:throw Error("Unknown SHA variant");}else if(0===c.lastIndexOf("SHA3-",0)
243. 0===c.lastIndexOf("SHAKE",0))for(c=0
244. a,k){var e,h,n,g,l,p,f,m,q,u,r,t,v,w,y,A,z,x,F,B,C,D,E=[],J;if("SHA-224"===k
245. "SHA-256"===k)u=64,t=1,D=Number,v=G,w=la,y=H,A=ha,z=ja,x=da,F=fa,C=U,B=aa,J=d;else if("SHA-384"===k
246. "SHA-512"===k)u=80,t=2,D=b,v=ma,w=na,y=oa,A=ia,z=ka,x=ea,F=ga,C=ca,B=ba,J=V;else throw Error("Unexpected error in SHA-2 implementation");k=a[0];e=a[1];h=a[2];n=a[3];g=a[4];l=a[5];p=a[6];f=a[7];for(r=0
247. "function"===typeof define
248. 1>u)throw Error("numRounds must a integer >= 1");if(0===c.lastIndexOf("SHA-",0))if(q=function(b,a){return A(b,a,c)
249. c)},y=function(b,a,l,f){var g,e;if("SHA-224"===c
250. "SHA-256"===c)g=(a+65>>>9<<4)+15,e=16;else throw Error("Unexpected error in SHA-2 implementation");for(
251. c);if("SHA-224"===c)b=[f[0],f[1],f[2],f[3],f[4],f[5],f[6]];else if("SHA-256"===c)b=f;else throw Error("Unexpected error in SHA-2 implementation");return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
252. "SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a
253. return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
254. if(!0===z)throw Error("Cannot set HMAC key after calling update");f=(g
255. 5);g=a%h;z=!0};this.getHash=function(a,f){var d,h,k,q;if(!0===m)throw Error("Cannot call getHash after setting HMAC key");k=C(f);switch(a){case "HEX":d=function(a){return D(a,e,k)};break;case "B64":d=function(a){return E(a,e,k)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{h=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("format must be HEX, B64, BYTES, or ARRAYBUFFER");
256. x(c));return d(q)};this.getHMAC=function(a,f){var d,k,t,u;if(!1===m)throw Error("Cannot call getHMAC without first setting HMAC key");t=C(f);switch(a){case "HEX":d=function(a){return D(a,e,t)};break;case "B64":d=function(a){return E(a,e,t)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{d=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
257. h=(d>>>1)+q;for(e=h>>>2;b.length<=e;)b.push(0);b[e]|=k<<8*(3+h%4*-1)}return{value:b,binLen:4*f+c}};break;case "TEXT":d=function(c,b,d){var f,n,k=0,e,h,q,m,p,r;b=b||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(r=3
258. q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=n[h]<<8*(r+p%4*-1);k+=1}else if("UTF16BE"===a
259. "UTF16LE"===a)for(r=2
260. !0===n
261. e+=1){f=c.charCodeAt(e);!0===n&&(h=f&255,f=h<<8|f>>>8);p=k+q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=f<<8*(r+p%4*-1);k+=2}return{value:b,binLen:8*k+d}};break;case "B64":d=function(a,b,c){var f=0,d,k,e,h,q,m,p;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");k=a.indexOf("=");a=a.replace(/\=/g
262. 16)&65535)<<16|b&65535}function R(c,a,d,l,b){var g=(c&65535)+(a&65535)+(d&65535)+(l&65535)+(b&65535);return((c>>>16)+(a>>>16)+(d>>>16)+(l>>>16)+(b>>>16)+(g>>>16)&65535)<<16|g&65535}function x(c){var a=[],d;if(0===c.lastIndexOf("SHA-",0))switch(a=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428]
263. new m,new m,new m,new m,new m,new m];break;case "SHA-512":a=[new m,new m,new m,new m,new m,new m,new m,new m];break;default:throw Error("Unknown SHA variant");}else throw Error("No SHA variants supported");return a}function A(c,a,d){var l,b,g,f,n,k,e,h,m,r,p,w,t,x,u,z,A,B,C,D,E,F,v=[],G;if("SHA-224"===d
264. "SHA-256"===d)r=64,w=1,F=Number,t=P,x=Q,u=R,z=N,A=O,B=L,C=M,E=K,D=J,G=H;else throw Error("Unexpected error in SHA-2 implementation");d=a[0];l=a[1];b=a[2];g=a[3];f=a[4];n=a[5];k=a[6];e=a[7];for(p=
265. if (typeof(index) === 'boolean') {
266. if (typeof(index) === 'number') {
267. if (index === 0
268. _.$slides.length === 0) {
269. if (addBefore === true) {
270. if (_.options.slidesToShow === 1
271. _.options.adaptiveHeight === true
272. _.options.vertical === false) {
273. if (_.options.rtl === true
274. if (_.transformsEnabled === false) {
275. if (_.options.vertical === false) {
276. if (_.cssTransitions === false) {
277. if (_.options.rtl === true) {
278. typeof asNavFor === 'object' ) {
279. if (_.options.fade === false) {
280. if ( _.options.infinite === false ) {
281. if ( _.direction === 1
282. ( _.currentSlide + 1 ) === ( _.slideCount - 1 )) {
283. else if ( _.direction === 0 ) {
284. if ( _.currentSlide - 1 === 0 ) {
285. if (_.options.arrows === true ) {
286. if (_.options.dots === true
287. _.$slideTrack = (_.slideCount === 0) ?
288. if (_.options.centerMode === true
289. _.options.swipeToSlide === true) {
290. _.setSlideClasses(typeof _.currentSlide === 'number' ? _.currentSlide : 0);
291. if (_.options.draggable === true) {
292. if (_.respondTo === 'window') {
293. } else if (_.respondTo === 'slider') {
294. } else if (_.respondTo === 'min') {
295. if (_.originalSettings.mobileFirst === false) {
296. if (_.breakpointSettings[targetBreakpoint] === 'unslick') {
297. if (initial === true) {
298. slideOffset = indexOffset === 0 ? _.options.slidesToScroll : _.options.slidesToShow - indexOffset
299. slideOffset = indexOffset === 0 ? _.options.slidesToScroll : indexOffset
300. var index = event.data.index === 0 ? 0 :
301. if (_.options.accessibility === true) {
302. if (_.options.arrows === true
303. if (_.options.focusOnSelect === true) {
304. if (_.shouldClick === false) {
305. if (_.options.infinite === true) {
306. } else if (_.options.centerMode === true) {
307. if (_.options.vertical === true
308. _.options.centerMode === true) {
309. if (_.options.slidesToShow === 2) {
310. } else if (_.options.slidesToShow === 1) {
311. } else if (_.options.centerMode === true
312. _.options.infinite === true) {
313. if (_.options.variableWidth === true) {
314. _.options.infinite === false) {
315. if (_.options.centerMode === true) {
316. if (_.options.infinite === false) {
317. centerOffset = _.options.centerMode === true ? _.slideWidth * Math.floor(_.options.slidesToShow / 2) : 0
318. if (_.options.swipeToSlide === true) {
319. _.options.pauseOnDotsHover === true
320. if (event.keyCode === 37
321. _.options.accessibility === true) {
322. message: _.options.rtl === true ? 'next' :
323. } else if (event.keyCode === 39
324. message: _.options.rtl === true ? 'previous' : 'next'
325. if (_.options.fade === true) {
326. if (_.options.lazyLoad === 'anticipated') {
327. } else if (_.currentSlide === 0) {
328. if (_.options.lazyLoad === 'progressive') {
329. if ( _.options.adaptiveHeight === true ) {
330. if ( $.type(responsiveSettings) === 'array'
331. _.breakpoints[l] === currentBreakpoint ) {
332. index = removeBefore === true ? 0 : _.slideCount - 1
333. index = removeBefore === true ? --index : index
334. if (removeAll === true) {
335. if (_.options.vertical === false
336. _.options.variableWidth === false) {
337. } else if (_.options.variableWidth === true) {
338. if (_.options.variableWidth === false) _.$slideTrack.children('.slick-slide').width(_.slideWidth - offset);
339. if( $.type( arguments[0] ) === 'object' ) {
340. } else if ( $.type( arguments[0] ) === 'string' ) {
341. if ( arguments[0] === 'responsive'
342. $.type( arguments[1] ) === 'array' ) {
343. if ( type === 'single' ) {
344. } else if ( type === 'multiple' ) {
345. } else if ( type === 'responsive' ) {
346. if( _.options.responsive[l].breakpoint === value[item].breakpoint ) {
347. _.positionProp = _.options.vertical === true ? 'top' : 'left'
348. if (_.positionProp === 'top') {
349. if (_.options.useCSS === true) {
350. if ( typeof _.options.zIndex === 'number' ) {
351. if (bodyStyle.perspectiveProperty === undefined
352. bodyStyle.webkitPerspective === undefined) _.animType = false;
353. bodyStyle.MozPerspective === undefined) _.animType = false;
354. if (bodyStyle.msTransform === undefined) _.animType = false;
355. var evenCoef = _.options.slidesToShow % 2 === 0 ? 1 : 0
356. if (index === 0) {
357. } else if (index === _.slideCount - 1) {
358. indexOffset = _.options.infinite === true ? _.options.slidesToShow + index : index
359. if (_.options.lazyLoad === 'ondemand'
360. _.options.lazyLoad === 'anticipated') {
361. if (_.options.infinite === true
362. _.options.fade === false) {
363. if (_.animating === true
364. _.options.waitForAnimate === true) {
365. if (_.options.fade === true
366. _.currentSlide === index) {
367. if (sync === false) {
368. _.currentLeft = _.swipeLeft === null ? slideLeft : _.swipeLeft
369. if (_.options.infinite === false
370. _.options.centerMode === false
371. } else if (_.options.infinite === false
372. _.options.centerMode === true
373. return (_.options.rtl === false ? 'left' : 'right');
374. return (_.options.rtl === false ? 'right' : 'left');
375. if (_.options.verticalSwiping === true) {
376. if ( _.touchObject.curX === undefined ) {
377. if ( _.touchObject.edgeHit === true ) {
378. if ((_.options.swipe === false) || ('ontouchend' in document
379. _.options.swipe === false)) {
380. } else if (_.options.draggable === false
381. positionOffset = (_.options.rtl === false ? 1 : -1) * (_.touchObject.curX > _.touchObject.startX ? 1 : -1);
382. if ((_.currentSlide === 0
383. swipeDirection === 'right') || (_.currentSlide >= _.getDotCount()
384. swipeDirection === 'left')) {
385. _.options.touchMove === false) {
386. if (_.animating === true) {
387. if ( _.options.arrows === true
388. if (_.currentSlide === 0) {
389. _.options.centerMode === false) {
390. //Customizable =================================================================================================

== (282 điều kiện duy nhất):
------------------------------------------------------------
1. 'vi' == params['locale']) {
2. 'en' == params['locale']) {
3. errorCode == '11'"
4. isSent == false
5. <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
6. (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
7. <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
8. !(errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
9. _re.body.themes.theme == 'token') {
10. params.response_code == 'overtime') {
11. if (_re.status == '200'
12. _re.status == '201') {
13. if (_re2.status == '200'
14. _re2.status == '201') {
15. if (this.errorCode == 'overtime'
16. this.errorCode == '253') {
17. } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
18. if (lastPayment?.state == 'pending') {
19. if(this.isTimePause == false) {
20. if ((dataPassed.status == '200'
21. dataPassed.status == '201') && dataPassed.body != null) {
22. guide == 'card_number'"
23. guide == 'card_name'"
24. guide == 'expire_date'"
25. guide == 'issue_date'"
26. if (params['try_again'] == '1'
27. return this._b == 3
28. this._b == 9
29. this._b == 16
30. this._b == 17
31. this._b == 19
32. this._b == 25
33. this._b == 44
34. this._b == 57
35. this._b == 59
36. this._b == 61
37. this._b == 63
38. this._b == 69
39. return this._b == 18
40. if (parseInt(b) == parseInt(v.substring(0, 6))) {
41. if (this._b == 11
42. this._b == 20
43. this._b == 33
44. this._b == 39
45. this._b == 43
46. this._b == 45
47. this._b == 67
48. this._b == 64
49. this._b == 72
50. this._b == 36
51. this._b == 73
52. this._b == 74
53. this._b == 75
54. this._b == 68) {//seabank
55. event.inputType == 'deleteContentBackward') {
56. if (event.target.name == 'exp_date'
57. event.inputType == 'insertCompositionText') {
58. if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
59. } else if (this._b == 2) {
60. } else if (this._b == 5) {
61. } else if (this._b == 6) {
62. } else if (this._b == 12) {
63. } else if (this._b == 31) {
64. if (this._b == 12) {
65. if (this._res_post.state == 'approved'
66. this._res_post.state == 'failed') {
67. } else if (this._res_post.state == 'authorization_required') {
68. if (this._b == 1
69. this._b == 14
70. this._b == 15
71. this._b == 24
72. this._b == 8
73. this._b == 10
74. this._b == 18
75. this._b == 22
76. this._b == 23
77. this._b == 27
78. this._b == 30
79. this._b == 11
80. this._b == 5
81. this._b == 12
82. this._b == 9) {
83. if (err.status == 400
84. err.status == 500) {
85. if (err.error && (err.error.code == 13
86. err.error.code == '13')) {
87. if ((cardNo.length == 16
88. (cardNo.length == 19
89. (cardNo.length == 19 && (this._b == 1
90. this._b == 4
91. this._b == 55
92. this._b == 47
93. this._b == 48
94. this._b == 5))
95. this._util.checkMod10(cardNo) == true
96. return ((value.length == 4
97. value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
98. value.length == 5) && parseInt(value.split('/')[0]
99. || (this._util.checkValidExpireMonthYear(value) && (this._b == 11
100. this._b == 68
101. this._b == 75)))
102. this._inExpDate.length == 4
103. this._inExpDate.search('/') == -1)
104. this._inExpDate.length == 5))
105. || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 11
106. this._b == 75)));
107. if (text == 'card_date') {
108. if (this._lb_card_date == '01/19') {
109. if (data._locale == 'en') {
110. !token) || (type == 1
111. _b == 6 || _b == 57
112. } else if(this._res_post.state == 'failed') {
113. if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
114. } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
115. v.length == 15) || (v.length == 16
116. v.length == 19))
117. this._util.checkMod10(v) == true) {
118. cardNo.length == 15)
119. cardNo.length == 16)
120. cardNo.startsWith('81')) && (cardNo.length == 16
121. cardNo.length == 19))
122. if (((this.valueDate.length == 4
123. this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
124. this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
125. v.length == 5) {
126. v.length == 4
127. v.length == 3)
128. _val.value.length == 4
129. _val.value.length == 3)
130. this.valueDate.search('/') == -1) || this.valueDate.length == 5)
131. this.valueDate.length == 5)
132. this.valueDate.length == 4
133. this.valueDate.search('/') == -1)
134. this.valueDate.length == 5))
135. this._i_csc.length == 4) ||
136. this._i_csc.length == 3)
137. return this._i_email == ''
138. return this._i_phone == ''
139. screen == 1"
140. screen == 2"
141. screen == 3"
142. screen == 5"
143. screen == 99"
144. if (!isNaN(_re.status) && (_re.status == '200'
145. _re.status == '201') && _re.body != null) {
146. if (('closed' == this._res_polling.state
147. 'canceled' == this._res_polling.state
148. 'expired' == this._res_polling.state)
149. } else if ('paid' == this._res_polling.state) {
150. this._res_polling.payments == null
151. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
152. this._paymentService.getCurrentPage() == 'enter_card') {
153. } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
154. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
155. } else if ('not_paid' == this._res_polling.state) {
156. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
157. this._auth == 0) {
158. if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
159. this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
160. if (this._res.payments[this._res.payments.length - 1].state == 'approved'
161. } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
162. } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
163. this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
164. if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
165. } else if (idBrand == 'atm'
166. detail.merchant.id == 'AMWAY') {
167. if ('paid' == this._res.state) {
168. if (('closed' == this._res.state
169. 'canceled' == this._res.state
170. 'expired' == this._res.state
171. 'paid' == this._res.state)
172. if ('paid' == this._res.state
173. 'canceled' == this._res.state) {
174. this._res.payments == null) {
175. this._res.payments[this._res.payments.length - 1].state == 'pending') {
176. if (this._res.currencies[0] == 'USD') {
177. this.token = params["token"] == 'true' ? true : false
178. if (this._b == 8) {
179. if (this._b == 18) {
180. if (_re.status == "200"
181. _re.status == "201") {
182. (this._res.payments[this._res.payments.length - 1].state ==
183. this._res.payments[this._res.payments.length - 1].state ==
184. idBrand == "atm"
185. $event.key == "Backspace"
186. $event.keyCode == 8
187. $event.charCode == 8
188. if (this._b == 12) { //SHB
189. } else if (this._res.state == "authorization_required") {
190. if(this._b == 12){
191. if (this.challengeCode == '') {
192. if ($event.key == 'Backspace'
193. $event.charCode == 8) {
194. return ((a.id == id
195. a.code == id) && a.type.includes(type));
196. return countPayment == maxPayment
197. if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
198. else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';
199. if (e.name == bankSwift) { // TODO: get by swift
200. if (+e.id == bankId) {
201. if (e.swiftCode == bankSwift) {
202. if (temp.length == 0) {
203. return (counter % 10 == 0);
204. if (this.checkCount == 1) {
205. if (results == null) {
206. if (c.length == 3) {
207. d = d == undefined ? '.' : d
208. t = t == undefined ? '
209. return results == null ? null : results[1]
210. if (_dataCache == null) {
211. if ( (0 <= r && r <= 6 && (c == 0
212. c == 6) )
213. || (0 <= c && c <= 6 && (r == 0
214. r == 6) )
215. if (i == 0
216. _modules[r][6] = (r % 2 == 0);
217. _modules[6][c] = (c % 2 == 0);
218. if (r == -2
219. r == 2
220. c == -2
221. c == 2
222. || (r == 0
223. c == 0) ) {
224. ( (bits >> i) & 1) == 1);
225. if (col == 6) col -= 1;
226. if (_modules[row][col - c] == null) {
227. dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
228. if (bitIndex == -1) {
229. margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
230. if (typeof arguments[0] == 'object') {
231. margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
232. if (b == -1) throw 'eof';
233. if (b0 == -1) break;
234. if (typeof b == 'number') {
235. if ( (b & 0xff) == b) {
236. return function(i, j) { return (i + j) % 2 == 0
237. return function(i, j) { return i % 2 == 0
238. return function(i, j) { return j % 3 == 0
239. return function(i, j) { return (i + j) % 3 == 0
240. return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
241. return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
242. return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
243. return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
244. if (r == 0
245. c == 0) {
246. if (dark == qrcode.isDark(row + r, col + c) ) {
247. if (count == 0
248. count == 4) {
249. if (typeof num.length == 'undefined') {
250. num[offset] == 0) {
251. if (typeof rsBlock == 'undefined') {
252. return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
253. _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
254. if (data.length - i == 1) {
255. } else if (data.length - i == 2) {
256. } else if (n == 62) {
257. } else if (n == 63) {
258. if (_buflen == 0) {
259. if (c == '=') {
260. } else if (c == 0x2b) {
261. } else if (c == 0x2f) {
262. if (table.size() == (1 << bitLength) ) {
263. if (cardList.length == 1) {//TOKEN, BIDV, SHB, OCEAN, PVCOM, TP Bank
264. if ( $('.circle_v1').css('display') == 'block'
265. if ($('.circle_v2').css('display') == 'block'
266. $('.circle_v3').css('display') == 'block' ) {
267. $('.circle_v1').css('display') == 'block'
268. document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM'
269. if ( document.getElementsByClassName("select_online")[0].value == 'Easy Online Banking') {
270. if ( document.getElementsByClassName("select_online")[0].value == 'Easy Mobile Banking') {
271. if ( document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM') {
272. if ($('.circle_v1').css('display') == 'block') {
273. if ($('.circle_v1').css('display') == 'block'
274. if ( $('.circle_v1').css('display') == 'block') {
275. else if ($('.circle_v2').css('display') == 'block') {
276. if ( $('.circle_v3').css('display') == 'block' ) {
277. if ($('.circle_v2').css('display') == 'block') {
278. x = _.positionProp == 'left' ? Math.ceil(position) + 'px' : '0px'
279. y = _.positionProp == 'top' ? Math.ceil(position) + 'px' : '0px'
280. if (_.options.slidesToShow == _.options.slidesToScroll
281. if (typeof opt == 'object'
282. typeof opt == 'undefined')

!== (149 điều kiện duy nhất):
------------------------------------------------------------
1. this.lastValue !== $event.target.value)) {
2. if (YY % 400 === 0 || (YY % 100 !== 0
3. if (YYYY % 400 === 0 || (YYYY % 100 !== 0
4. codeResponse.toString() !== '0') {
5. this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
6. guide !== 'card_number'"
7. guide !==  'expire_date'
8. guide !== 'cvv_code'"
9. key !== '8') {
10. codeResponse.toString() !== '0'){
11. event.inputType !== 'deleteContentBackward') || v.length == 5) {
12. if (deviceValue !== 'default') {
13. this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
14. return this._i_country_code !== 'default'
15. this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
16. this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {
17. if (_val !== 3) {
18. this._util.getElementParams(this.currentUrl, 't_id') !== '') {
19. codeResponse.toString() !== "0") {
20. return this._otp.trim().length !== 0 ? false : true
21. queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
22. if (queryString !== '') {
23. if (target !== 0
24. if (e !== null) {
25. if (lang !== "vi") lang = "en";
26. this.oldValue !== this.value) {
27. if (jLinks !== undefined
28. jLinks !== null) {
29. if (jMerchantReturn !== undefined
30. jMerchantReturn !== null) {
31. if (responseCode !== undefined
32. responseCode !== null
33. if (parentRes !== "{
34. if (value !== "") {
35. if (inMonth.value !== ""
36. if (inYear.value !== ""
37. var month = inMonth.value !== ""
38. var year = parseInt("20" + (inYear.value !== ""
39. if ("2D" !== order["vpc_VerType"]) return err("MISSING_FIELD"
40. inMonth.value !== tokenInsMonth) instrument["month"] = inMonth.value;
41. inYear.value !== tokenInsYear) instrument["year"] = inYear.value;
42. if (inCvv.value !== "") instrument["cvv"] = inCvv.value;
43. if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
44. if (inNumber.style.display !== "none") instrument.number = inNumber.value;
45. if (inDate.style.display !== "none") instrument.date = inDate.value;
46. if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
47. if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
48. if (inName.style.display !== "none") instrument.name = inName.value;
49. typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
50. hrefAttr !== '#' ? hrefAttr.trim() : ''
51. $(this._element).css('visibility') !== 'hidden') {
52. if (selector !== null
53. if (selector !== null) {
54. if (typeof this._config.parent.jquery !== 'undefined') {
55. if (typeof this._config.reference.jquery !== 'undefined') {
56. if (this._config.boundary !== 'scrollParent') {
57. if (this._popper !== null) {
58. event.which !== TAB_KEYCODE)) {
59. event.which !== ESCAPE_KEYCODE
60. if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
61. event.which !== ARROW_UP_KEYCODE
62. this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
63. if (document !== event.target
64. _this5._element !== event.target
65. if (event.target !== event.currentTarget) {
66. if (typeof margin !== 'undefined') {
67. if (allowedAttributeList.indexOf(attrName) !== -1) {
68. if (uriAttrs.indexOf(attrName) !== -1) {
69. var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
70. if (_this2._hoverState !== HoverState.SHOW
71. if (_this2._popper !== null) {
72. if (data.originalPlacement !== data.placement) {
73. } else if (trigger !== Trigger.MANUAL) {
74. titleType !== 'string') {
75. if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
76. if (this.constructor.Default[key] !== this.config[key]) {
77. if (tabClass !== null
78. if (tip.getAttribute('x-placement') !== null) {
79. if (typeof config.target !== 'string') {
80. if (this._scrollHeight !== scrollHeight) {
81. if (this._activeTarget !== target) {
82. var isActiveTarget = this._activeTarget !== this._targets[i]
83. 'use strict';(function(Y){function C(c,a,b){var e=0,h=[],n=0,g,l,d,f,m,q,u,r,I=!1,v=[],w=[],t,y=!1,z=!1,x=-1;b=b||{};g=b.encoding||"UTF8";t=b.numRounds||1;if(t!==parseInt(t,10)
84. 0!==f%32
85. a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8
86. }switch(c){case "HEX":c=function(a,c,d){var g=a.length,l,p,f,m,q,u;if(0!==g%2)throw Error("String of HEX type must be in byte increments");c=c||[0];d=d||0;q=d>>>3;u=-1===b?3:0;for(l=0
87. 1!==b
88. "UTF16LE"!==a
89. "");if(-1!==p
90. function(a,c,d){var g,l,p,f,m,q;c=c||[0];d=d||0;l=d>>>3;m=-1===b?3:0;q=new Uint8Array(a);for(g=0;g<a.byteLength;g+=1)f=g+l,p=f>>>2,c.length<=p&&c.push(0),c[p]|=q[g]<<8*(m+f%4*b);return{value:c,binLen:8*a.byteLength+d}};break;default:throw Error("format must be HEX, TEXT, B64, BYTES, or ARRAYBUFFER");}return c}function y(c,a){return c<<a|c>>>32-a}function S(c,a){return 32<a?(a-=32,new b(c.b<<a|c.a>>>32-a,c.a<<a|c.b>>>32-a)):0!==a?new b(c.a<<a|c.b>>>32-a,c.b<<a|c.a>>>32-a):c}function w(c,a){return c>>>
91. a[7]);return a}function D(c,a){var d,e,h,n,g=[],l=[];if(null!==c)for(e=0
92. define.amd?define(function(){return C}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=C),exports=C):Y.jsSHA=C})(this);
93. return C}):"undefined"!==typeof exports?("undefined"!==typeof module
94. 'use strict';(function(I){function w(c,a,d){var l=0,b=[],g=0,f,n,k,e,h,q,y,p,m=!1,t=[],r=[],u,z=!1;d=d||{};f=d.encoding||"UTF8";u=d.numRounds||1;if(u!==parseInt(u,10)
95. l+=1)g[l]=c[l>>>2]>>>8*(3+l%4*-1)&255;return b}function C(c){var a={outputUpper:!1,b64Pad:"=",shakeLen:-1};c=c||{};a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");
96. if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0;d<f;d+=2){k=parseInt(a.substr(d,2),16);if(isNaN(k))throw Error("String of HEX type contains invalid characters");
97. if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0
98. "");if(-1!==k
99. define.amd?define(function(){return w}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=w),exports=w):I.jsSHA=w})(this);
100. return w}):"undefined"!==typeof exports?("undefined"!==typeof module
101. } else if (typeof exports !== 'undefined') {
102. if (typeof document.mozHidden !== 'undefined') {
103. } else if (typeof document.webkitHidden !== 'undefined') {
104. asNavFor !== null ) {
105. if ( asNavFor !== null
106. if (_.options.infinite !== true) {
107. _.options.responsive !== null) {
108. if (targetBreakpoint !== null) {
109. if (_.activeBreakpoint !== null) {
110. if (targetBreakpoint !== _.activeBreakpoint
111. triggerBreakpoint !== false ) {
112. unevenOffset = (_.slideCount % _.options.slidesToScroll !== 0);
113. _.$dots !== null) {
114. if (filter !== null) {
115. if (_.slideCount % _.options.slidesToScroll !== 0) {
116. if (_.$dots !== null) {
117. if (slideControlIndex !== -1) {
118. _.currentSlide !== 0) {
119. if ($(window).width() !== _.windowWidth) {
120. } else if ( typeof arguments[1] !== 'undefined' ) {
121. if( $.type( _.options.responsive ) !== 'array' ) {
122. if (bodyStyle.WebkitTransition !== undefined
123. bodyStyle.MozTransition !== undefined
124. bodyStyle.msTransition !== undefined) {
125. if (bodyStyle.OTransform !== undefined) {
126. if (bodyStyle.MozTransform !== undefined) {
127. if (bodyStyle.webkitTransform !== undefined) {
128. if (bodyStyle.msTransform !== undefined) {
129. if (bodyStyle.transform !== undefined
130. _.animType !== false) {
131. _.transformsEnabled = _.options.useTransform && (_.animType !== null
132. _.animType !== false);
133. if (dontAnimate !== true
134. if (dontAnimate !== true) {
135. if ( _.touchObject.startX !== _.touchObject.curX ) {
136. event.type.indexOf('mouse') !== -1) {
137. event.originalEvent.touches !== undefined ?
138. touches = event.originalEvent !== undefined ? event.originalEvent.touches : null
139. touches.length !== 1) {
140. _.touchObject.curX = touches !== undefined ? touches[0].pageX : event.clientX
141. _.touchObject.curY = touches !== undefined ? touches[0].pageY : event.clientY
142. if (event.originalEvent !== undefined
143. if (_.touchObject.fingerCount !== 1
144. event.originalEvent.touches !== undefined) {
145. _.touchObject.startX = _.touchObject.curX = touches !== undefined ? touches.pageX : event.clientX
146. _.touchObject.startY = _.touchObject.curY = touches !== undefined ? touches.pageY : event.clientY
147. if (_.$slidesCache !== null) {
148. //if (event.origin !== "http://example.com:8080") return;
149. /*if (contentType !== my_expected_type) {

!= (151 điều kiện duy nhất):
------------------------------------------------------------
1. if (params['locale'] != null
2. } else if (params['locale'] != null
3. if (userLang != null
4. if(value!=null){
5. if (this._idInvoice != null
6. this._idInvoice != 0) {
7. if (this._paymentService.getInvoiceDetail() != null) {
8. dataPassed.body != null) {
9. dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
10. dataPassed.body.themes.border_color != true ? dataPassed.body.themes.border_color : ''
11. if (this._translate.currentLang != language) {
12. if (!(strInstrument != null
13. if (strInstrument.substring(0, 1) != '^'
14. strInstrument.substr(strInstrument.length - 1) != '$') {
15. if (ua.indexOf('safari') != -1
16. if (this._inExpDate.length != 3) {
17. if (this._b != 9
18. this._b != 16
19. this._b != 17
20. this._b != 25
21. this._b != 44
22. this._b != 57
23. this._b != 59
24. this._b != 61
25. this._b != 63
26. this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
27. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72','73','74','75'].indexOf(this._b.toString()) != -1) {
28. if (this._res_post.return_url != null) {
29. if (this._res_post.links != null
30. this._res_post.links.merchant_return != null
31. this._res_post.links.merchant_return.href != null) {
32. if (this._res_post.authorization != null
33. this._res_post.authorization.links != null
34. this._res_post.links.cancel != null) {
35. let userName = _formCard.name != null ? _formCard.name : ''
36. this._res_post.authorization.links.approval != null
37. this._res_post.authorization.links.approval.href != null) {
38. userName = paramUserName != null ? paramUserName : ''
39. this._b != 11
40. this._b != 20
41. this._b != 33
42. this._b != 39
43. this._b != 43
44. this._b != 45
45. this._b != 64
46. this._b != 67
47. this._b != 68
48. this._b != 72
49. this._b != 36
50. this._b != 73
51. this._b != 74
52. this._b != 75)
53. _showAVS!=true"
54. if (params['locale'] != null) {
55. if ('otp' != this._paymentService.getCurrentPage()) {
56. } else if (this._res_post.links != null
57. cardNo != null
58. if (this.valueDate.length != 3) {
59. v != null
60. this.c_csc = (!(_val.value != null
61. this._i_csc != null
62. _re.body != null) {
63. this._res_polling.links != null
64. this._res_polling.links.merchant_return != null //
65. this._util.getElementParams(url_redirect, 'vpc_TxnResponseCode') != '99') {
66. } else if (this._res_polling.merchant != null
67. this._res_polling.merchant_invoice_reference != null
68. this._paymentService.getState() != 'error') {
69. } else if (this._res_polling.payments != null
70. this._res_polling.links.merchant_return != null//
71. this._res_polling.payments != null
72. if (this._res_polling.payments != null
73. t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
74. if (count != 1) {
75. if (this._res.merchant != null
76. this._res.merchant_invoice_reference != null) {
77. if (this._res.merchant.address_details != null) {
78. this._res.links != null//
79. } else if (this._res.payments != null
80. this._res.payments[this._res.payments.length - 1].instrument != null
81. this._res.payments[this._res.payments.length - 1].instrument.issuer != null
82. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
83. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
84. this._res.payments[this._res.payments.length - 1].links != null
85. this._res.payments[this._res.payments.length - 1].links.cancel != null
86. this._res.payments[this._res.payments.length - 1].links.cancel.href != null
87. this._res.payments[this._res.payments.length - 1].links.update != null
88. this._res.payments[this._res.payments.length - 1].links.update.href != null) {
89. this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
90. this._res.payments[this._res.payments.length - 1].authorization != null
91. this._res.payments[this._res.payments.length - 1].authorization.links != null) {
92. this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
93. this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
94. if (this._res.payments[this._res.payments.length - 1].authorization != null
95. this._res.payments[this._res.payments.length - 1].authorization.links != null
96. auth = paramUserName != null ? paramUserName : ''
97. this._res.links != null
98. this._res.links.merchant_return != null //
99. } else if (this._res.merchant != null
100. this._res.merchant_invoice_reference != null
101. } else if (this._res.payments != null) {
102. if (this._res != null) {
103. this._res.payments != null
104. this._res.payments[this._res.payments.length - 1].instrument !=
105. .issuer != null
106. .brand != null
107. .brand.id != null
108. this._res.payments[this._res.payments.length - 1].links.cancel !=
109. .href != null
110. .issuer.swift_code != null
111. this._res.payments[this._res.payments.length - 1].authorization !=
112. .links != null
113. .links.approval != null
114. .links.approval.href != null
115. this._res.links.merchant_return != null
116. this._res.links.merchant_return.href != null
117. if (this._res_post != null
118. this._res_post.links != null
119. if (!(this._otp != null
120. if (!(_formCard.password != null
121. this._inOtp_9.length !=
122. this._res_post != null
123. this._res_post.links.merchant_return.href != null
124. if (ua.indexOf("safari") != -1
125. if (idInvoice != null
126. idInvoice != 0)
127. idInvoice != 0) {
128. if (this._merchantid != null
129. this._tranref != null
130. this._state != null
131. let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
132. urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
133. if (tem != null) {
134. if ((tem = ua.match(/version\/(\d+)/i)) != null) {
135. if (_modules[r][6] != null) {
136. if (_modules[6][c] != null) {
137. if (_modules[row][col] != null) {
138. while (buffer.getLengthInBits() % 8 != 0) {
139. if (count != numChars) {
140. throw count + ' != ' + numChars
141. while (data != 0) {
142. if (test.length != 2
143. ( (test[0] << 8) | test[1]) != code) {
144. if (_length % 3 != 0) {
145. if ( (data >>> length) != 0) {
146. return typeof _map[key] != 'undefined'
147. var source = arguments[i] != null ? arguments[i] : {
148. $('[draggable!=true]', _.$slideTrack).off('dragstart', _.preventDefault);
149. $('[draggable!=true]', _.$slideTrack).on('dragstart', _.preventDefault);
150. if( direction != 'vertical' ) {
151. if (typeof ret != 'undefined') return ret;

