====================================================================================================
TRÍCH XUẤT ĐIỀU KIỆN SO SÁNH TỪ CÁC FILE TYPESCRIPT/JAVASCRIPT/HTML
====================================================================================================
Thư mục/File nguồn: /root/projects/onepay/paygate-vinfast-web/src
Thời gian: 18:12:31 18/8/2025
Tổng số file xử lý: 154
Tổng số file bị bỏ qua: 48
Tổng số điều kiện tìm thấy: 3152

THỐNG KÊ TỔNG QUAN THEO LOẠI TOÁN TỬ:
--------------------------------------------------
Strict equality (===): 1799 lần
Loose equality (==): 476 lần
Strict inequality (!==): 658 lần
Loose inequality (!=): 219 lần
--------------------------------------------------

DANH SÁCH FILE ĐÃ XỬ LÝ:
--------------------------------------------------
1. 4en.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/4en.html
2. 4vn.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/4vn.html
3. app-routing.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/app-routing.module.ts
4. app.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/app.component.html
5. app.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/app.component.spec.ts
6. app.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/app.component.ts
7. app.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/app.module.ts
8. counter.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/counter.directive.spec.ts
9. counter.directive.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/counter.directive.ts
10. format-carno-input.derective.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/directives/format-carno-input.derective.ts
11. uppercase-input.directive.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/directives/uppercase-input.directive.ts
12. error.component.html (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/error/error.component.html
13. error.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/error/error.component.spec.ts
14. error.component.ts (30 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/error/error.component.ts
15. support-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/error/support-dialog/support-dialog.html
16. support-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/error/support-dialog/support-dialog.ts
17. format-date.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/format-date.directive.spec.ts
18. format-date.directive.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/format-date.directive.ts
19. main.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/main.component.html
20. main.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/main.component.spec.ts
21. main.component.ts (12 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/main.component.ts
22. app-result.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/app-result/app-result.component.html
23. app-result.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/app-result/app-result.component.ts
24. bank-amount-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
25. bank-amount-dialog.component.ts (34 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
26. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/dialog-guide-dialog.html
27. bankaccount.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
28. bankaccount.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
29. bankaccount.component.ts (154 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
30. bank.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/model/bank.ts
31. otp-auth.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
32. otp-auth.component.ts (21 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
33. techcombank.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
34. techcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
35. techcombank.component.ts (16 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
36. vietcombank.component.html (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
37. vietcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
38. vietcombank.component.ts (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
39. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
40. off-bank-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
41. off-bank-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
42. policy-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/dialog/policy-dialog/policy-dialog.component.html
43. policy-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/dialog/policy-dialog/policy-dialog.component.ts
44. domescard-main.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/domescard-main.component.html
45. domescard-main.component.ts (53 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/domescard-main.component.ts
46. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/intercard-main/dialog-guide-dialog.html
47. intercard-main.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/intercard-main/intercard-main.component.html
48. intercard-main.component.ts (64 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/intercard-main/intercard-main.component.ts
49. menu.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/menu.component.html
50. menu.component.ts (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/menu.component.ts
51. bnpl-management.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/model/bnpl-management.ts
52. homecredit-management.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/model/homecredit-management.ts
53. overlay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/overlay/overlay.component.html
54. overlay.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/overlay/overlay.component.spec.ts
55. overlay.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/overlay/overlay.component.ts
56. bank-amount.pipe.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/pipe/bank-amount.pipe.ts
57. auth.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/services/auth.service.ts
58. close-dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/services/close-dialog.service.ts
59. data.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/services/data.service.ts
60. deep_link.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/services/deep_link.service.ts
61. dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/services/dialog.service.ts
62. fee.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/services/fee.service.ts
63. focus-input.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/services/focus-input.service.ts
64. multiple_method.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/services/multiple_method.service.ts
65. payment.service.ts (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/services/payment.service.ts
66. token-main.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/services/token-main.service.ts
67. success.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/success/success.component.html
68. success.component.ts (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/success/success.component.ts
69. index.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/translate/index.ts
70. lang-en.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/translate/lang-en.ts
71. lang-vi.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/translate/lang-vi.ts
72. translate.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/translate/translate.pipe.ts
73. translate.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/translate/translate.service.ts
74. translations.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/translate/translations.ts
75. apps-info.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/util/apps-info.ts
76. banks-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/util/banks-info.ts
77. util.ts (25 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/util/util.ts
78. qrcode.js (67 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/assets/script/qrcode.js
79. environment.prod.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/environments/environment.prod.ts
80. environment.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/environments/environment.ts
81. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/acc_bidv/index.html
82. script.js (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/acc_bidv/script.js
83. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/acc_oceanbank/index.html
84. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/acc_oceanbank/script.js
85. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/acc_pvcombank/index.html
86. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/acc_pvcombank/script.js
87. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/acc_shb/index.html
88. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/acc_shb/script.js
89. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/acc_tpbank/index.html
90. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/acc_tpbank/script.js
91. common.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/common.js
92. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/domestic_card/index.html
93. script.js (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/domestic_card/script.js
94. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/domestic_token/index.html
95. script.js (25 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/domestic_token/script.js
96. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/index.html
97. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/international_card/index.html
98. script.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/international_card/script.js
99. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/international_token/index.html
100. script.js (36 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/international_token/script.js
101. script.js (54 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/script.js
102. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.js
103. bidv.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Bidv/assets/js/bidv.js
104. bidv2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Bidv/bidv2.js
105. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Bidv/index.html
106. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.js
107. ocean.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Ocean/assets/js/ocean.js
108. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Ocean/index.html
109. ocean2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Ocean/ocean2.js
110. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
111. pvbank.js (23 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Pv_Bank/assets/js/pvbank.js
112. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Pv_Bank/index.html
113. pvbank2.js (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Pv_Bank/pvbank2.js
114. Sea2.js (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/SeaBank/Sea2.js
115. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
116. Sea.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/SeaBank/assets/js/Sea.js
117. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/SeaBank/index.html
118. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.js
119. shb.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Shb/assets/js/shb.js
120. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Shb/index.html
121. shb2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Shb/shb2.js
122. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
123. tpbank.js (23 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Tp_Bank/assets/js/tpbank.js
124. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Tp_Bank/index.html
125. tpbank2.js (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Tp_Bank/tpbank2.js
126. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.js
127. onepay.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Visa/assets/js/onepay.js
128. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Visa/index.html
129. index.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Visa/index.js
130. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
131. vpbank.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Vp_Bank/assets/js/vpbank.js
132. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Vp_Bank/index.html
133. vpbank2.js (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Vp_Bank/vpbank2.js
134. sha.js (49 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/assets/js/sha.js
135. sha256.js (28 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/assets/js/sha256.js
136. slick.js (182 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/assets/libraries/slick/slick.js
137. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.js
138. atm_b1.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_1/assets/js/atm_b1.js
139. atm_b1_2.js (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_1/atm_b1_2.js
140. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_1/index.html
141. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.js
142. atm_b2.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_2/assets/js/atm_b2.js
143. atm_b2_2.js (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_2/atm_b2_2.js
144. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_2/index.html
145. common.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/common.js
146. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.js
147. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/international_card/index.html
148. script.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/international_card/script.js
149. index.html (35 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/index.html
150. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/index.html
151. karma.conf.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/karma.conf.js
152. main.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/main.ts
153. polyfills.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/polyfills.ts
154. test.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/test.ts

DANH SÁCH FILE BỊ BỎ QUA:
--------------------------------------------------
1. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/bootstrap.min.js
2. jquery-3.0.0.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/jquery-3.0.0.min.js
3. popper.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/popper.min.js
4. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
5. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
6. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
7. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Bidv/assets/js/jquery-3.4.1.min.js
8. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
9. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
10. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
11. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Ocean/assets/js/jquery-3.4.1.min.js
12. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
13. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
14. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
15. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Pv_Bank/assets/js/jquery-3.4.1.min.js
16. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
17. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
18. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
19. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/SeaBank/assets/js/jquery-3.4.1.min.js
20. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
21. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
22. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
23. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Shb/assets/js/jquery-3.4.1.min.js
24. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
25. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
26. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
27. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Tp_Bank/assets/js/jquery-3.4.1.min.js
28. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
29. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
30. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
31. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Visa/assets/js/jquery-3.4.1.min.js
32. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
33. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
34. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
35. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Vp_Bank/assets/js/jquery-3.4.1.min.js
36. instafeed.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/assets/js/instafeed.min.js
37. slick.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/assets/libraries/slick/slick.min.js
38. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
39. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
40. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
41. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_1/assets/js/jquery-3.4.1.min.js
42. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
43. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
44. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
45. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_2/assets/js/jquery-3.4.1.min.js
46. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
47. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
48. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js

====================================================================================================
CHI TIẾT TỪNG FILE:
====================================================================================================

📁 FILE 1: 4en.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/4en.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 2: 4vn.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/4vn.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 3: app-routing.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/app-routing.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 4: app.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/app.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 5: app.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/app.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 6: app.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/app.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 84] return lang === this._translate.currentLang

== (2 điều kiện):
  1. [Dòng 63] 'vi' == params['locale']) {
  2. [Dòng 65] 'en' == params['locale']) {

!= (3 điều kiện):
  1. [Dòng 63] if (params['locale'] != null
  2. [Dòng 65] } else if (params['locale'] != null
  3. [Dòng 72] if (userLang != null

================================================================================

📁 FILE 7: app.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/app.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 8: counter.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/counter.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 9: counter.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/counter.directive.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 10: format-carno-input.derective.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/directives/format-carno-input.derective.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 44] this.lastValue !== $event.target.value)) {

!= (1 điều kiện):
  1. [Dòng 23] if(value!=null){

================================================================================

📁 FILE 11: uppercase-input.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/directives/uppercase-input.directive.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 0 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 23] this.lastValue !== $event.target.value)) {

================================================================================

📁 FILE 12: error.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/error/error.component.html
📊 Thống kê: 11 điều kiện duy nhất
   - === : 3 lần
   - == : 8 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 94] isPopupSupport === 'True') || (rePayment
  2. [Dòng 95] isPopupSupport === 'True')" class="left" [class.select_only]="!(rePayment
  3. [Dòng 101] isPopupSupport === 'True')">

== (8 điều kiện):
  1. [Dòng 3] errorCode == '11'"
  2. [Dòng 94] <div class="footer-button" *ngIf="(isSent == false
  3. [Dòng 95] <div *ngIf="(isSent == false
  4. [Dòng 101] <div class="payment_again" *ngIf="rePayment && !timeOut" [class.select_only]="!(isSent == false
  5. [Dòng 118] <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
  6. [Dòng 118] (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
  7. [Dòng 120] <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
  8. [Dòng 120] !(errorCode == 'overtime' || errorCode == '253') && isBack && !invoiceNearExpire && !paymentPending

================================================================================

📁 FILE 13: error.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/error/error.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 14: error.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/error/error.component.ts
📊 Thống kê: 30 điều kiện duy nhất
   - === : 8 lần
   - == : 16 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 150] params.timeout === 'true') {
  2. [Dòng 168] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  3. [Dòng 168] _re.body.state === 'unpaid');
  4. [Dòng 244] if (this.errorCode === 'overtime'
  5. [Dòng 244] this.errorCode === '253') {
  6. [Dòng 323] params.name === 'CUSTOMER_INTIME'
  7. [Dòng 323] params.code === '09') {
  8. [Dòng 402] if (this.timeLeft === 0) {

== (16 điều kiện):
  1. [Dòng 134] if (params && (params['bnpl'] == 'true')) {
  2. [Dòng 138] if (params && (params['bnpl']== 'false')) {
  3. [Dòng 176] if (this.res.currencies[0] == 'USD') {
  4. [Dòng 206] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'amigo') {
  5. [Dòng 218] this.res.themes.theme == 'general') {
  6. [Dòng 224] params.response_code == 'overtime') {
  7. [Dòng 265] if (_re.status == '200'
  8. [Dòng 265] _re.status == '201') {
  9. [Dòng 278] if (_re2.status == '200'
  10. [Dòng 278] _re2.status == '201') {
  11. [Dòng 291] if (this.errorCode == 'overtime'
  12. [Dòng 291] this.errorCode == '253') {
  13. [Dòng 294] } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
  14. [Dòng 299] this.res.state == 'canceled') {
  15. [Dòng 317] if (lastPayment?.state == 'pending') {
  16. [Dòng 400] if (this.isTimePause == false) {

!= (6 điều kiện):
  1. [Dòng 204] } else if (this.res.payments[this.res.payments.length - 1].instrument.issuer.brand != null
  2. [Dòng 205] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id != null
  3. [Dòng 462] if (this.res != null
  4. [Dòng 462] this.res.links != null
  5. [Dòng 462] this.res.links.merchant_return != null
  6. [Dòng 463] this.res.links.merchant_return.href != null) {

================================================================================

📁 FILE 15: support-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/error/support-dialog/support-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 16: support-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/error/support-dialog/support-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 17: format-date.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/format-date.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 18: format-date.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/format-date.directive.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0
  2. [Dòng 123] YY % 4 === 0)) {
  3. [Dòng 138] if (YYYY % 400 === 0
  4. [Dòng 138] YYYY % 4 === 0)) {

!== (2 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0 || (YY % 100 !== 0
  2. [Dòng 138] if (YYYY % 400 === 0 || (YYYY % 100 !== 0

================================================================================

📁 FILE 19: main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/main.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 80] screen==1"
  2. [Dòng 81] screen==2"

================================================================================

📁 FILE 20: main.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/main.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 21: main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/main.component.ts
📊 Thống kê: 12 điều kiện duy nhất
   - === : 1 lần
   - == : 5 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 136] params.name === 'CUSTOMER_INTIME')) {

== (5 điều kiện):
  1. [Dòng 94] if ((dataPassed.status == '200'
  2. [Dòng 94] dataPassed.status == '201') && dataPassed.body != null) {
  3. [Dòng 98] dataPassed.body.themes.logo_full == 'True') {
  4. [Dòng 118] payments[payments.length - 1].state == 'pending'
  5. [Dòng 120] payments[payments.length - 1].instrument.issuer.brand.id == 'amigo') {

!= (6 điều kiện):
  1. [Dòng 85] if (this._idInvoice != null
  2. [Dòng 85] this._idInvoice != 0) {
  3. [Dòng 86] if (this._paymentService.getInvoiceDetail() != null) {
  4. [Dòng 94] dataPassed.body != null) {
  5. [Dòng 112] dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
  6. [Dòng 173] if (this._translate.currentLang != language) {

================================================================================

📁 FILE 22: app-result.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/app-result/app-result.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 23: app-result.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/app-result/app-result.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 24: bank-amount-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 25: bank-amount-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
📊 Thống kê: 34 điều kiện duy nhất
   - === : 0 lần
   - == : 34 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (34 điều kiện):
  1. [Dòng 40] if (this.locale == 'en') {
  2. [Dòng 54] if (bankId == 3
  3. [Dòng 54] bankId == 61
  4. [Dòng 55] bankId == 8
  5. [Dòng 55] bankId == 49
  6. [Dòng 56] bankId == 48
  7. [Dòng 57] bankId == 10
  8. [Dòng 57] bankId == 53
  9. [Dòng 58] bankId == 17
  10. [Dòng 58] bankId == 65
  11. [Dòng 59] bankId == 23
  12. [Dòng 59] bankId == 52
  13. [Dòng 60] bankId == 27
  14. [Dòng 60] bankId == 66
  15. [Dòng 61] bankId == 9
  16. [Dòng 61] bankId == 54
  17. [Dòng 62] bankId == 37
  18. [Dòng 63] bankId == 38
  19. [Dòng 64] bankId == 39
  20. [Dòng 65] bankId == 40
  21. [Dòng 66] bankId == 42
  22. [Dòng 67] bankId == 44
  23. [Dòng 68] bankId == 72
  24. [Dòng 69] bankId == 59
  25. [Dòng 72] bankId == 51
  26. [Dòng 73] bankId == 64
  27. [Dòng 74] bankId == 58
  28. [Dòng 75] bankId == 56
  29. [Dòng 78] bankId == 55
  30. [Dòng 79] bankId == 60
  31. [Dòng 80] bankId == 68
  32. [Dòng 81] bankId == 73
  33. [Dòng 82] bankId == 74
  34. [Dòng 83] bankId == 75 //keb hana

================================================================================

📁 FILE 26: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 27: bankaccount.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 40] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 55] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 55] valueDate.trim().length === 0)"

================================================================================

📁 FILE 28: bankaccount.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 29: bankaccount.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
📊 Thống kê: 154 điều kiện duy nhất
   - === : 47 lần
   - == : 73 lần
   - !== : 13 lần
   - != : 21 lần
--------------------------------------------------------------------------------

=== (47 điều kiện):
  1. [Dòng 122] if (isIE[0] === 'MSIE'
  2. [Dòng 122] +isIE[1] === 10) {
  3. [Dòng 210] if ((_val.value.substr(-1) === ' '
  4. [Dòng 210] _val.value.length === 24) {
  5. [Dòng 220] if (this.cardTypeBank === 'bank_card_number') {
  6. [Dòng 225] } else if (this.cardTypeBank === 'bank_account_number') {
  7. [Dòng 231] } else if (this.cardTypeBank === 'bank_username') {
  8. [Dòng 235] } else if (this.cardTypeBank === 'bank_customer_code') {
  9. [Dòng 241] this.cardTypeBank === 'bank_card_number'
  10. [Dòng 255] if (this.cardTypeOcean === 'IB') {
  11. [Dòng 259] } else if (this.cardTypeOcean === 'MB') {
  12. [Dòng 260] if (_val.value.substr(0, 2) === '84') {
  13. [Dòng 267] } else if (this.cardTypeOcean === 'ATM') {
  14. [Dòng 294] if (this.cardTypeBank === 'bank_account_number') {
  15. [Dòng 313] this.cardTypeBank === 'bank_card_number') {
  16. [Dòng 335] if (this.cardTypeBank === 'bank_card_number'
  17. [Dòng 335] if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  18. [Dòng 657] if (event.keyCode === 8
  19. [Dòng 657] event.key === "Backspace"
  20. [Dòng 697] if (v.length === 2
  21. [Dòng 697] this.flag.length === 3
  22. [Dòng 697] this.flag.charAt(this.flag.length - 1) === '/') {
  23. [Dòng 701] if (v.length === 1) {
  24. [Dòng 703] } else if (v.length === 2) {
  25. [Dòng 706] v.length === 2) {
  26. [Dòng 714] if (len === 2) {
  27. [Dòng 1004] if ((this.cardTypeBank === 'bank_account_number'
  28. [Dòng 1004] this.cardTypeBank === 'bank_username'
  29. [Dòng 1004] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  30. [Dòng 1009] this.cardTypeOcean === 'ATM')
  31. [Dòng 1010] || (this.cardTypeOcean === 'IB'
  32. [Dòng 1069] if (valIn === this._translate.instant('bank_card_number')) {
  33. [Dòng 1094] } else if (valIn === this._translate.instant('bank_account_number')) {
  34. [Dòng 1113] } else if (valIn === this._translate.instant('bank_username')) {
  35. [Dòng 1129] } else if (valIn === this._translate.instant('bank_customer_code')) {
  36. [Dòng 1217] if (_val.value === ''
  37. [Dòng 1217] _val.value === null
  38. [Dòng 1217] _val.value === undefined) {
  39. [Dòng 1226] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  40. [Dòng 1226] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  41. [Dòng 1233] this.cardTypeOcean === 'MB') {
  42. [Dòng 1241] this.cardTypeOcean === 'IB'
  43. [Dòng 1246] if ((this.cardTypeBank === 'bank_card_number'
  44. [Dòng 1278] if (this.cardName === undefined
  45. [Dòng 1278] this.cardName === '') {
  46. [Dòng 1286] if (this.valueDate === undefined
  47. [Dòng 1286] this.valueDate === '') {

== (73 điều kiện):
  1. [Dòng 139] if (this._b == 18
  2. [Dòng 139] this._b == 19) {
  3. [Dòng 142] if (this._b == 19) {//19BIDV
  4. [Dòng 150] } else if (this._b == 3
  5. [Dòng 150] this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  6. [Dòng 155] if (this._b == 27) {
  7. [Dòng 160] } else if (this._b == 12) {// 12SHB
  8. [Dòng 165] } else if (this._b == 18) { //18Oceanbank-ocb
  9. [Dòng 219] if (this._b == 19
  10. [Dòng 219] this._b == 3
  11. [Dòng 219] this._b == 27
  12. [Dòng 219] this._b == 12) {
  13. [Dòng 254] } else if (this._b == 18) {
  14. [Dòng 285] if (this.checkBin(_val.value) && (this._b == 3
  15. [Dòng 285] this._b == 27)) {
  16. [Dòng 290] if (this._b == 3) {
  17. [Dòng 302] this.cardTypeOcean == 'ATM') {
  18. [Dòng 315] if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  19. [Dòng 335] this._b == 18)) {
  20. [Dòng 411] if (this.checkBin(v) && (this._b == 3
  21. [Dòng 657] event.inputType == 'deleteContentBackward') {
  22. [Dòng 658] if (event.target.name == 'exp_date'
  23. [Dòng 666] event.inputType == 'insertCompositionText') {
  24. [Dòng 681] if (((this.valueDate.length == 4
  25. [Dòng 681] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  26. [Dòng 681] this.valueDate.length == 5)
  27. [Dòng 761] if (temp.length == 0) {
  28. [Dòng 768] return (counter % 10 == 0);
  29. [Dòng 788] } else if (this._b == 19) {
  30. [Dòng 790] } else if (this._b == 27) {
  31. [Dòng 795] if (this._b == 12) {
  32. [Dòng 797] if (this.cardTypeBank == 'bank_customer_code') {
  33. [Dòng 799] } else if (this.cardTypeBank == 'bank_account_number') {
  34. [Dòng 816] _formCard.exp_date.length == 5
  35. [Dòng 816] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
  36. [Dòng 816] this._b == 3)) {//27-pvcombank;3-TPB ;
  37. [Dòng 821] if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
  38. [Dòng 821] this._b == 19
  39. [Dòng 821] this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
  40. [Dòng 824] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  41. [Dòng 827] if (this.cardTypeOcean == 'IB') {
  42. [Dòng 829] } else if (this.cardTypeOcean == 'MB') {
  43. [Dòng 831] } else if (this.cardTypeOcean == 'ATM') {
  44. [Dòng 878] if (_re.status == '200'
  45. [Dòng 878] _re.status == '201') {
  46. [Dòng 883] if (this._res_post.state == 'approved'
  47. [Dòng 883] this._res_post.state == 'failed') {
  48. [Dòng 890] } else if (this._res_post.state == 'authorization_required') {
  49. [Dòng 908] if (this._b == 18) {
  50. [Dòng 913] if (this._b == 27
  51. [Dòng 913] this._b == 18) {
  52. [Dòng 977] if (err.status == 400
  53. [Dòng 977] err.status == 500) {
  54. [Dòng 978] if (err.error && (err.error.code == 13
  55. [Dòng 978] err.error.code == '13')) {
  56. [Dòng 1024] if ((cardNo.length == 16
  57. [Dòng 1024] if ((cardNo.length == 16 || (cardNo.length == 19
  58. [Dòng 1025] && ((this._b == 18
  59. [Dòng 1025] cardNo.length == 19) || this._b != 18)
  60. [Dòng 1038] if (this._b == +e.id) {
  61. [Dòng 1054] if (valIn == 1) {
  62. [Dòng 1056] } else if (valIn == 2) {
  63. [Dòng 1080] this._b == 3) {
  64. [Dòng 1087] if (this._b == 19) {
  65. [Dòng 1150] if (cardType == this._translate.instant('internetbanking')
  66. [Dòng 1158] } else if (cardType == this._translate.instant('mobilebanking')
  67. [Dòng 1166] } else if (cardType == this._translate.instant('atm')
  68. [Dòng 1226] this._b == 18))) {
  69. [Dòng 1233] } else if (this._b == 18
  70. [Dòng 1257] this.c_expdate = !(((this.valueDate.length == 4
  71. [Dòng 1289] this.valueDate.length == 4
  72. [Dòng 1289] this.valueDate.search('/') == -1)
  73. [Dòng 1290] this.valueDate.length == 5))

!== (13 điều kiện):
  1. [Dòng 210] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 864] key !== '3') {
  3. [Dòng 914] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 932] codeResponse.toString() !== '0') {
  5. [Dòng 1004] cardNo.length !== 0) {
  6. [Dòng 1076] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 1097] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 1118] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 1138] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 1150] this.lb_card_account !== this._translate.instant('ocb_account')) {
  11. [Dòng 1158] this.lb_card_account !== this._translate.instant('ocb_phone')) {
  12. [Dòng 1166] this.lb_card_account !== this._translate.instant('ocb_card')) {
  13. [Dòng 1246] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (21 điều kiện):
  1. [Dòng 171] } else if (this._b != 18) {
  2. [Dòng 177] if (this.htmlDesc != null
  3. [Dòng 207] if (ua.indexOf('safari') != -1
  4. [Dòng 217] if (_val.value != '') {
  5. [Dòng 303] this.auth_method != null) {
  6. [Dòng 659] if (this.valueDate.length != 3) {
  7. [Dòng 816] if (_formCard.exp_date != null
  8. [Dòng 821] if (this.cardName != null
  9. [Dòng 886] if (this._res_post.links != null
  10. [Dòng 886] this._res_post.links.merchant_return != null
  11. [Dòng 886] this._res_post.links.merchant_return.href != null) {
  12. [Dòng 894] if (this._res_post.authorization != null
  13. [Dòng 894] this._res_post.authorization.links != null
  14. [Dòng 894] this._res_post.authorization.links.approval != null) {
  15. [Dòng 901] this._res_post.links.cancel != null) {
  16. [Dòng 1024] this._b != 27
  17. [Dòng 1024] this._b != 12
  18. [Dòng 1024] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  19. [Dòng 1025] this._b != 18)
  20. [Dòng 1071] if (this._b != 18
  21. [Dòng 1071] this._b != 19) {

================================================================================

📁 FILE 30: bank.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/model/bank.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 31: otp-auth.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 32: otp-auth.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
📊 Thống kê: 21 điều kiện duy nhất
   - === : 0 lần
   - == : 11 lần
   - !== : 1 lần
   - != : 9 lần
--------------------------------------------------------------------------------

== (11 điều kiện):
  1. [Dòng 98] if (this._b == 8) {//MB Bank
  2. [Dòng 102] if (this._b == 18) {//Oceanbank
  3. [Dòng 138] if (this._b == 8) {
  4. [Dòng 143] if (this._b == 18) {
  5. [Dòng 148] if (this._b == 12) { //SHB
  6. [Dòng 169] if (_re.status == '200'
  7. [Dòng 169] _re.status == '201') {
  8. [Dòng 178] } else if (this._res.state == 'authorization_required') {
  9. [Dòng 213] if (this.challengeCode == '') {
  10. [Dòng 319] if (this._b == 12) {
  11. [Dòng 371] if (this._b == 18) {//8-MB Bank;18-oceanbank

!== (1 điều kiện):
  1. [Dòng 184] codeResponse.toString() !== '0') {

!= (9 điều kiện):
  1. [Dòng 174] if (this._res.links != null
  2. [Dòng 174] this._res.links.merchant_return != null
  3. [Dòng 174] this._res.links.merchant_return.href != null) {
  4. [Dòng 344] if (this._res_post != null
  5. [Dòng 344] this._res_post.links != null
  6. [Dòng 344] this._res_post.links.merchant_return != null
  7. [Dòng 344] this._res_post.links.merchant_return.href != null) {
  8. [Dòng 366] if (!(_formCard.otp != null
  9. [Dòng 372] if (!(_formCard.password != null

================================================================================

📁 FILE 33: techcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 34: techcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 35: techcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
📊 Thống kê: 16 điều kiện duy nhất
   - === : 1 lần
   - == : 12 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 75] if (this.timeLeft === 0) {

== (12 điều kiện):
  1. [Dòng 66] if (this._b == 2
  2. [Dòng 66] this._b == 5
  3. [Dòng 66] this._b == 31) {
  4. [Dòng 105] if (this._b == 2) {
  5. [Dòng 107] } else if (this._b == 5) {
  6. [Dòng 109] } else if (this._b == 6) {
  7. [Dòng 111] } else if (this._b == 31) {
  8. [Dòng 141] if (_re.status == '200'
  9. [Dòng 141] _re.status == '201') {
  10. [Dòng 146] if (this._res_post.state == 'approved'
  11. [Dòng 146] this._res_post.state == 'failed') {
  12. [Dòng 150] } else if (this._res_post.state == 'authorization_required') {

!= (3 điều kiện):
  1. [Dòng 154] if (this._res_post.authorization != null
  2. [Dòng 154] this._res_post.authorization.links != null
  3. [Dòng 154] this._res_post.authorization.links.approval != null) {

================================================================================

📁 FILE 36: vietcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
📊 Thống kê: 8 điều kiện duy nhất
   - === : 2 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 26] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  2. [Dòng 26] _inExpDate.trim().length === 0)"

== (1 điều kiện):
  1. [Dòng 60] _b == 6 || _b == 57

!= (5 điều kiện):
  1. [Dòng 22] d_card_date && (_b != 3 && _b != 19 && _b != 18)
  2. [Dòng 22] _b != 18)">
  3. [Dòng 38] *ngIf="(_b != 18
  4. [Dòng 38] (_b != 18 && _b != 6 && _b != 2 && _b != 57)
  5. [Dòng 38] _b != 57)">

================================================================================

📁 FILE 37: vietcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 38: vietcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
📊 Thống kê: 135 điều kiện duy nhất
   - === : 4 lần
   - == : 83 lần
   - !== : 3 lần
   - != : 45 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 368] if (event.keyCode === 8
  2. [Dòng 368] event.key === "Backspace"
  3. [Dòng 641] if (approval.method === 'REDIRECT') {
  4. [Dòng 644] } else if (approval.method === 'POST_REDIRECT') {

== (83 điều kiện):
  1. [Dòng 141] if (this._b == 11
  2. [Dòng 141] this._b == 20
  3. [Dòng 141] this._b == 33
  4. [Dòng 141] this._b == 39
  5. [Dòng 141] this._b == 43
  6. [Dòng 141] this._b == 45
  7. [Dòng 141] this._b == 67
  8. [Dòng 141] this._b == 64
  9. [Dòng 141] this._b == 73
  10. [Dòng 141] this._b == 74
  11. [Dòng 141] this._b == 75
  12. [Dòng 141] this._b == 68) {//seabank
  13. [Dòng 151] if (this._b == 1
  14. [Dòng 151] this._b == 36
  15. [Dòng 151] this._b == 55
  16. [Dòng 151] this._b == 47
  17. [Dòng 151] this._b == 48
  18. [Dòng 151] this._b == 59
  19. [Dòng 151] this._b == 67) {
  20. [Dòng 162] if (message == '1') {
  21. [Dòng 173] return this._b == 3
  22. [Dòng 173] this._b == 9
  23. [Dòng 173] this._b == 16
  24. [Dòng 173] this._b == 17
  25. [Dòng 173] this._b == 19
  26. [Dòng 173] this._b == 25
  27. [Dòng 173] this._b == 44
  28. [Dòng 174] this._b == 57
  29. [Dòng 174] this._b == 61
  30. [Dòng 174] this._b == 63
  31. [Dòng 174] this._b == 69
  32. [Dòng 178] return this._b == 6
  33. [Dòng 178] this._b == 2
  34. [Dòng 182] return this._b == 18
  35. [Dòng 214] if (parseInt(b) == parseInt(v.substring(0, 6))) {
  36. [Dòng 277] this._b == 72
  37. [Dòng 368] event.inputType == 'deleteContentBackward') {
  38. [Dòng 369] if (event.target.name == 'exp_date'
  39. [Dòng 377] event.inputType == 'insertCompositionText') {
  40. [Dòng 458] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  41. [Dòng 469] } else if (this._b == 2) {
  42. [Dòng 471] } else if (this._b == 6) {
  43. [Dòng 473] } else if (this._b == 31) {
  44. [Dòng 498] if(this._b == 12){
  45. [Dòng 502] if(this._b == 5){
  46. [Dòng 531] if (this._res_post.state == 'approved'
  47. [Dòng 531] this._res_post.state == 'failed') {
  48. [Dòng 581] } else if (this._res_post.state == 'authorization_required') {
  49. [Dòng 603] this._b == 5
  50. [Dòng 603] this._b == 14
  51. [Dòng 603] this._b == 15
  52. [Dòng 603] this._b == 24
  53. [Dòng 603] this._b == 8
  54. [Dòng 603] this._b == 10
  55. [Dòng 603] this._b == 18
  56. [Dòng 603] this._b == 22
  57. [Dòng 603] this._b == 23
  58. [Dòng 603] this._b == 27
  59. [Dòng 603] this._b == 30
  60. [Dòng 603] this._b == 11
  61. [Dòng 603] this._b == 12
  62. [Dòng 603] this._b == 9) {
  63. [Dòng 671] if (err.status == 400
  64. [Dòng 671] err.status == 500) {
  65. [Dòng 672] if (err.error && (err.error.code == 13
  66. [Dòng 672] err.error.code == '13')) {
  67. [Dòng 699] if ((cardNo.length == 16
  68. [Dòng 700] (cardNo.length == 19
  69. [Dòng 700] (cardNo.length == 19 && (this._b == 1
  70. [Dòng 700] this._b == 4
  71. [Dòng 700] this._b == 67))
  72. [Dòng 702] this._util.checkMod10(cardNo) == true
  73. [Dòng 789] return ((value.length == 4
  74. [Dòng 789] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  75. [Dòng 789] value.length == 5) && parseInt(value.split('/')[0]
  76. [Dòng 793] || (this._util.checkValidExpireMonthYear(value) && (this._b == 11
  77. [Dòng 794] this._b == 68
  78. [Dòng 794] this._b == 75)))
  79. [Dòng 824] this._inExpDate.length == 4
  80. [Dòng 824] this._inExpDate.search('/') == -1)
  81. [Dòng 825] this._inExpDate.length == 5))
  82. [Dòng 827] || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 11
  83. [Dòng 827] this._b == 75)));

!== (3 điều kiện):
  1. [Dòng 511] key !== '3') {
  2. [Dòng 544] codeResponse.toString() !== '0') {
  3. [Dòng 604] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (45 điều kiện):
  1. [Dòng 125] this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
  2. [Dòng 156] if (this.htmlDesc != null
  3. [Dòng 191] if (ua.indexOf('safari') != -1
  4. [Dòng 370] if (this._inExpDate.length != 3) {
  5. [Dòng 452] if (this._b != 3
  6. [Dòng 452] this._b != 9
  7. [Dòng 452] this._b != 16
  8. [Dòng 452] this._b != 17
  9. [Dòng 452] this._b != 18
  10. [Dòng 452] this._b != 19
  11. [Dòng 452] this._b != 25
  12. [Dòng 452] this._b != 36
  13. [Dòng 452] this._b != 44
  14. [Dòng 453] this._b != 57
  15. [Dòng 453] this._b != 59
  16. [Dòng 453] this._b != 61
  17. [Dòng 453] this._b != 63
  18. [Dòng 453] this._b != 69
  19. [Dòng 453] this._b != 6
  20. [Dòng 453] this._b != 2) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
  21. [Dòng 467] '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72' , '73', '74', '75'].indexOf(this._b.toString()) != -1) {
  22. [Dòng 533] if (this._res_post.return_url != null) {
  23. [Dòng 536] if (this._res_post.links != null
  24. [Dòng 536] this._res_post.links.merchant_return != null
  25. [Dòng 536] this._res_post.links.merchant_return.href != null) {
  26. [Dòng 586] if (this._res_post.authorization != null
  27. [Dòng 586] this._res_post.authorization.links != null
  28. [Dòng 591] this._res_post.links.cancel != null) {
  29. [Dòng 597] let userName = _formCard.name != null ? _formCard.name : ''
  30. [Dòng 598] this._res_post.authorization.links.approval != null
  31. [Dòng 598] this._res_post.authorization.links.approval.href != null) {
  32. [Dòng 601] userName = paramUserName != null ? paramUserName : ''
  33. [Dòng 791] this._b != 11
  34. [Dòng 791] this._b != 20
  35. [Dòng 791] this._b != 33
  36. [Dòng 791] this._b != 39
  37. [Dòng 792] this._b != 43
  38. [Dòng 792] this._b != 45
  39. [Dòng 792] this._b != 64
  40. [Dòng 792] this._b != 67
  41. [Dòng 792] this._b != 68
  42. [Dòng 792] this._b != 72
  43. [Dòng 792] this._b != 73
  44. [Dòng 792] this._b != 74
  45. [Dòng 792] this._b != 75)

================================================================================

📁 FILE 39: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 40: off-bank-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 41: off-bank-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 42: policy-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/dialog/policy-dialog/policy-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 43: policy-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/dialog/policy-dialog/policy-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 44: domescard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/domescard-main.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 1 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 22] filteredData.length === 0"

== (4 điều kiện):
  1. [Dòng 5] _auth==1) || (token
  2. [Dòng 5] _b == 16)"
  3. [Dòng 8] (token || _auth==1) && _b != 16
  4. [Dòng 11] _auth==1)">

================================================================================

📁 FILE 45: domescard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/domescard-main/domescard-main.component.ts
📊 Thống kê: 53 điều kiện duy nhất
   - === : 24 lần
   - == : 23 lần
   - !== : 1 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (24 điều kiện):
  1. [Dòng 230] if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
  2. [Dòng 231] filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
  3. [Dòng 275] if (valOut === 'auth') {
  4. [Dòng 346] if (this._b === '1'
  5. [Dòng 346] this._b === '20'
  6. [Dòng 346] this._b === '64') {
  7. [Dòng 349] if (this._b === '36'
  8. [Dòng 349] this._b === '18'
  9. [Dòng 349] this._b === '19'
  10. [Dòng 352] if (this._b === '19'
  11. [Dòng 352] this._b === '16'
  12. [Dòng 352] this._b === '25'
  13. [Dòng 352] this._b === '33'
  14. [Dòng 353] this._b === '39'
  15. [Dòng 353] this._b === '9'
  16. [Dòng 353] this._b === '11'
  17. [Dòng 353] this._b === '17'
  18. [Dòng 354] this._b === '36'
  19. [Dòng 354] this._b === '44'
  20. [Dòng 354] this._b === '12'
  21. [Dòng 355] this._b === '64'
  22. [Dòng 358] if (this._b === '20'
  23. [Dòng 361] if (this._b === '12'
  24. [Dòng 361] this._b === '18') {

== (23 điều kiện):
  1. [Dòng 159] this._auth == 0
  2. [Dòng 159] this.tokenList.length == 0) {
  3. [Dòng 217] this.filteredData.length == 1
  4. [Dòng 257] if ($event && ($event == 'true'
  5. [Dòng 349] this._b == '55'
  6. [Dòng 349] this._b == '47'
  7. [Dòng 349] this._b == '48'
  8. [Dòng 349] this._b == '59'
  9. [Dòng 349] this._b == '73'
  10. [Dòng 349] this._b == '67') {
  11. [Dòng 352] this._b == '3'
  12. [Dòng 353] this._b == '43'
  13. [Dòng 353] this._b == '45'
  14. [Dòng 354] this._b == '57'
  15. [Dòng 355] this._b == '61'
  16. [Dòng 355] this._b == '63'
  17. [Dòng 355] this._b == '67'
  18. [Dòng 355] this._b == '68'
  19. [Dòng 355] this._b == '69'
  20. [Dòng 355] this._b == '72'
  21. [Dòng 355] this._b == '74'
  22. [Dòng 355] this._b == '75') {
  23. [Dòng 378] if (item['id'] == this._b) {

!== (1 điều kiện):
  1. [Dòng 108] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (5 điều kiện):
  1. [Dòng 138] if (params['locale'] != null) {
  2. [Dòng 144] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 180] if (!(strInstrument != null
  4. [Dòng 183] if (strInstrument.substring(0, 1) != '^'
  5. [Dòng 183] strInstrument.substr(strInstrument.length - 1) != '$') {

================================================================================

📁 FILE 46: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/intercard-main/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 47: intercard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/intercard-main/intercard-main.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 19] _showAVS!=true"

================================================================================

📁 FILE 48: intercard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/intercard-main/intercard-main.component.ts
📊 Thống kê: 64 điều kiện duy nhất
   - === : 12 lần
   - == : 31 lần
   - !== : 9 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (12 điều kiện):
  1. [Dòng 292] if (_formCard.country === 'default') {
  2. [Dòng 574] if (event.keyCode === 8
  3. [Dòng 574] event.key === "Backspace"
  4. [Dòng 650] if ((v.substr(-1) === ' '
  5. [Dòng 901] if (deviceValue === 'CA'
  6. [Dòng 901] deviceValue === 'US') {
  7. [Dòng 922] this.c_country = _val.value === 'default'
  8. [Dòng 1063] this.selectedCardType = this.listCardTypes.find(({ name }) => name === 'AMEX');
  9. [Dòng 1067] this.selectedCardType = this.listCardTypes.find(({ name }) => name === 'Mastercard');
  10. [Dòng 1071] this.selectedCardType = this.listCardTypes.find(({ name }) => name === 'Visa');
  11. [Dòng 1075] this.selectedCardType = this.listCardTypes.find(({ name }) => name === 'JCB');
  12. [Dòng 1079] this.selectedCardType = this.listCardTypes.find(({ name }) => name === 'UnionPay');

== (31 điều kiện):
  1. [Dòng 352] if (this._res_post.state == 'approved'
  2. [Dòng 352] this._res_post.state == 'failed') {
  3. [Dòng 378] } else if(this._res_post.state == 'failed') { // lỗi mới kiểm tra sang trang lỗi
  4. [Dòng 408] } else if (this._res_post.state == 'authorization_required') {
  5. [Dòng 409] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  6. [Dòng 421] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  7. [Dòng 493] v.length == 15) || (v.length == 16
  8. [Dòng 493] v.length == 19))
  9. [Dòng 494] this._util.checkMod10(v) == true) {
  10. [Dòng 528] cardNo.length == 15)
  11. [Dòng 530] cardNo.length == 16)
  12. [Dòng 531] cardNo.startsWith('81')) && (cardNo.length == 16
  13. [Dòng 531] cardNo.length == 19))
  14. [Dòng 574] event.inputType == 'deleteContentBackward') {
  15. [Dòng 575] if (event.target.name == 'exp_date'
  16. [Dòng 583] event.inputType == 'insertCompositionText') {
  17. [Dòng 598] if (((this.valueDate.length == 4
  18. [Dòng 598] this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  19. [Dòng 598] this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  20. [Dòng 650] v.length == 5) {
  21. [Dòng 658] v.length == 4
  22. [Dòng 662] v.length == 3)
  23. [Dòng 699] _val.value.length == 4
  24. [Dòng 703] _val.value.length == 3)
  25. [Dòng 879] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  26. [Dòng 879] this.valueDate.length == 5)
  27. [Dòng 969] this.valueDate.length == 4
  28. [Dòng 969] this.valueDate.search('/') == -1)
  29. [Dòng 970] this.valueDate.length == 5))
  30. [Dòng 984] this._i_csc.length == 4) ||
  31. [Dòng 988] this._i_csc.length == 3)

!== (9 điều kiện):
  1. [Dòng 334] if (this.tracking.hasOwnProperty(key) && ((key !== '8'
  2. [Dòng 334] !this._showCardName) || (key !== '9'
  3. [Dòng 362] codeResponse.toString() !== '0'){
  4. [Dòng 650] event.inputType !== 'deleteContentBackward') || v.length == 5) {
  5. [Dòng 898] if (deviceValue !== 'default') {
  6. [Dòng 915] this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
  7. [Dòng 995] return this._i_country_code !== 'default'
  8. [Dòng 1024] this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
  9. [Dòng 1031] this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {

!= (12 điều kiện):
  1. [Dòng 158] if (params['locale'] != null) {
  2. [Dòng 164] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 354] if (this._res_post.return_url != null) {
  4. [Dòng 356] } else if (this._res_post.links != null
  5. [Dòng 356] this._res_post.links.merchant_return != null
  6. [Dòng 356] this._res_post.links.merchant_return.href != null) {
  7. [Dòng 475] if (ua.indexOf('safari') != -1
  8. [Dòng 528] cardNo != null
  9. [Dòng 576] if (this.valueDate.length != 3) {
  10. [Dòng 657] v != null
  11. [Dòng 698] this.c_csc = (!(_val.value != null
  12. [Dòng 982] this._i_csc != null

================================================================================

📁 FILE 49: menu.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/menu.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 1 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 74] [ngStyle]="{'border-color': type === 2 ? this.themeConfig.border_color : border_color}"

== (4 điều kiện):
  1. [Dòng 44] screen==1"
  2. [Dòng 55] screen==2"
  3. [Dòng 76] token || _auth==1
  4. [Dòng 81] _auth==1)">

================================================================================

📁 FILE 50: menu.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/main/menu/menu.component.ts
📊 Thống kê: 135 điều kiện duy nhất
   - === : 9 lần
   - == : 64 lần
   - !== : 3 lần
   - != : 59 lần
--------------------------------------------------------------------------------

=== (9 điều kiện):
  1. [Dòng 678] this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_bnpl === 1
  2. [Dòng 711] if (this._res.state === 'unpaid'
  3. [Dòng 711] this._res.state === 'not_paid') {
  4. [Dòng 810] if ('op' === auth
  5. [Dòng 851] } else if ('bank' === auth
  6. [Dòng 856] if (approval.method === 'REDIRECT') {
  7. [Dòng 859] } else if (approval.method === 'POST_REDIRECT') {
  8. [Dòng 1117] if (this.timeLeftPaypal === 0) {
  9. [Dòng 1385] return id === 'amex' ? '1234' : '123'

== (64 điều kiện):
  1. [Dòng 199] if (el == 1) {
  2. [Dòng 201] } else if (el == 2) {
  3. [Dòng 203] } else if (el == 4) {
  4. [Dòng 205] } else if (el == 3) {
  5. [Dòng 234] if (!isNaN(_re.status) && (_re.status == '200'
  6. [Dòng 234] _re.status == '201') && _re.body != null) {
  7. [Dòng 239] if (('closed' == this._res_polling.state
  8. [Dòng 239] 'canceled' == this._res_polling.state
  9. [Dòng 239] 'expired' == this._res_polling.state)
  10. [Dòng 259] } else if ('paid' == this._res_polling.state) {
  11. [Dòng 269] this._res_polling.payments == null
  12. [Dòng 271] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  13. [Dòng 273] this._paymentService.getCurrentPage() == 'enter_card'
  14. [Dòng 276] } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
  15. [Dòng 276] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
  16. [Dòng 293] } else if ('not_paid' == this._res_polling.state) {
  17. [Dòng 305] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
  18. [Dòng 425] if (auth == 'auth') {
  19. [Dòng 427] detail.merchant.id == 'AMWAY') {
  20. [Dòng 451] if (message == '1') {
  21. [Dòng 453] } else if (message == '0') {
  22. [Dòng 477] if (_re.status == '200'
  23. [Dòng 477] _re.status == '201') {
  24. [Dòng 505] if (this.type == 5
  25. [Dòng 508] } else if (this.type == 6
  26. [Dòng 511] } else if (this.type == 2
  27. [Dòng 514] } else if (this.type == 7
  28. [Dòng 517] } else if (this.type == 8
  29. [Dòng 520] } else if (this.type == 4
  30. [Dòng 523] } else if (this.type == 3
  31. [Dòng 684] this._auth == 0) {
  32. [Dòng 712] if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
  33. [Dòng 712] this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
  34. [Dòng 714] if (this._res.payments[this._res.payments.length - 1].state == 'approved'
  35. [Dòng 720] } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
  36. [Dòng 759] } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  37. [Dòng 759] this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
  38. [Dòng 794] if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  39. [Dòng 798] } else if (idBrand == 'atm'
  40. [Dòng 878] this._res.payments[this._res.payments.length - 1].state == 'pending'
  41. [Dòng 880] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
  42. [Dòng 899] if ('paid' == this._res.state) {
  43. [Dòng 923] if (('closed' == this._res.state
  44. [Dòng 923] 'canceled' == this._res.state
  45. [Dòng 923] 'expired' == this._res.state
  46. [Dòng 923] 'paid' == this._res.state)
  47. [Dòng 926] if ('paid' == this._res.state
  48. [Dòng 926] 'canceled' == this._res.state) {
  49. [Dòng 945] this._res.payments == null) {
  50. [Dòng 947] this._res.payments[this._res.payments.length - 1].state == 'pending') {
  51. [Dòng 957] if (this._res.currencies[0] == 'USD') {
  52. [Dòng 1169] if (this._res_post.state == 'approved'
  53. [Dòng 1169] this._res_post.state == 'failed') {
  54. [Dòng 1178] } else if (this._res_post.state == 'authorization_required') {
  55. [Dòng 1179] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  56. [Dòng 1193] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  57. [Dòng 1282] if (item.instrument.issuer.brand.id == 'atm') {
  58. [Dòng 1284] } else if (item.instrument.issuer.brand.id == 'visa'
  59. [Dòng 1284] item.instrument.issuer.brand.id == 'mastercard') {
  60. [Dòng 1285] if (item.instrument.issuer_location == 'd') {
  61. [Dòng 1290] } else if (item.instrument.issuer.brand.id == 'amex') {
  62. [Dòng 1296] } else if (item.instrument.issuer.brand.id == 'jcb') {
  63. [Dòng 1312] uniq.length == 1) {
  64. [Dòng 1406] if (data._locale == 'en') {

!== (3 điều kiện):
  1. [Dòng 826] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  2. [Dòng 1083] if (_val !== 3) {
  3. [Dòng 1087] this._util.getElementParams(this.currentUrl, 't_id') !== '') {

!= (59 điều kiện):
  1. [Dòng 227] if (this._idInvoice != null
  2. [Dòng 227] this._paymentService.getState() != 'error') {
  3. [Dòng 233] if (this._paymentService.getCurrentPage() != 'otp') {
  4. [Dòng 234] _re.body != null) {
  5. [Dòng 240] this._res_polling.links != null
  6. [Dòng 240] this._res_polling.links.merchant_return != null //
  7. [Dòng 243] this._util.getElementParams(url_redirect, 'vpc_TxnResponseCode') != '99') {
  8. [Dòng 269] } else if (this._res_polling.merchant != null
  9. [Dòng 269] this._res_polling.merchant_invoice_reference != null
  10. [Dòng 271] } else if (this._res_polling.payments != null
  11. [Dòng 273] this._res_polling.payments[this._res_polling.payments.length - 1].instrument.brand != 'amigo') {
  12. [Dòng 277] this._res_polling.links.merchant_return != null//
  13. [Dòng 296] this._res_polling.payments != null
  14. [Dòng 304] if (this._res_polling.payments != null
  15. [Dòng 308] t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
  16. [Dòng 434] this.type.toString().length != 0) {
  17. [Dòng 440] if (params['locale'] != null) {
  18. [Dòng 447] if ('otp' != this._paymentService.getCurrentPage()) {
  19. [Dòng 465] if (this._paymentService.getInvoiceDetail() != null) {
  20. [Dòng 695] if (count != 1) {
  21. [Dòng 701] if (this._res.merchant != null
  22. [Dòng 701] this._res.merchant_invoice_reference != null) {
  23. [Dòng 704] if (this._res.merchant.address_details != null) {
  24. [Dòng 712] this._res.links != null//
  25. [Dòng 759] } else if (this._res.payments != null
  26. [Dòng 760] this._res.payments[this._res.payments.length - 1].instrument != null
  27. [Dòng 760] this._res.payments[this._res.payments.length - 1].instrument.issuer != null
  28. [Dòng 761] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
  29. [Dòng 761] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
  30. [Dòng 762] this._res.payments[this._res.payments.length - 1].links != null
  31. [Dòng 762] this._res.payments[this._res.payments.length - 1].links.cancel != null
  32. [Dòng 762] this._res.payments[this._res.payments.length - 1].links.cancel.href != null
  33. [Dòng 779] this._res.payments[this._res.payments.length - 1].links.update != null
  34. [Dòng 779] this._res.payments[this._res.payments.length - 1].links.update.href != null) {
  35. [Dòng 798] this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
  36. [Dòng 799] this._res.payments[this._res.payments.length - 1].authorization != null ) {
  37. [Dòng 810] this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
  38. [Dòng 810] this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
  39. [Dòng 813] if (this._res.payments[this._res.payments.length - 1].authorization != null
  40. [Dòng 813] this._res.payments[this._res.payments.length - 1].authorization.links != null
  41. [Dòng 819] auth = paramUserName != null ? paramUserName : ''
  42. [Dòng 924] this._res.links != null
  43. [Dòng 924] this._res.links.merchant_return != null //
  44. [Dòng 945] } else if (this._res.merchant != null
  45. [Dòng 945] this._res.merchant_invoice_reference != null
  46. [Dòng 949] this._res.payments[this._res.payments.length - 1].instrument.brand != 'amigo') {
  47. [Dòng 1017] if (!(strInstrument != null
  48. [Dòng 1034] if (this._translate.currentLang != language) {
  49. [Dòng 1089] } else if (this._res.payments != null) {
  50. [Dòng 1171] if (this._res_post.return_url != null) {
  51. [Dòng 1173] } else if (this._res_post.links != null
  52. [Dòng 1173] this._res_post.links.merchant_return != null
  53. [Dòng 1173] this._res_post.links.merchant_return.href != null) {
  54. [Dòng 1320] if (['mastercard', 'visa', 'amex', 'jcb', 'cup', 'card', 'np_card', 'atm'].indexOf(id) != -1) {
  55. [Dòng 1322] } else if (['techcombank_account', 'vib_account', 'dongabank_account', 'tpbank_account', 'bidv_account', 'pvcombank_account', 'shb_account', 'oceanbank_online_account'].indexOf(id) != -1) {
  56. [Dòng 1324] } else if (['oceanbank_mobile_account'].indexOf(id) != -1) {
  57. [Dòng 1326] } else if (['shb_customer_id'].indexOf(id) != -1) {
  58. [Dòng 1352] if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1) {
  59. [Dòng 1380] return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1

================================================================================

📁 FILE 51: bnpl-management.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/model/bnpl-management.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 52: homecredit-management.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/model/homecredit-management.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 53: overlay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/overlay/overlay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 54: overlay.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/overlay/overlay.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 55: overlay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/overlay/overlay.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 56: bank-amount.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/pipe/bank-amount.pipe.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 11] return ((a.id == id
  2. [Dòng 11] a.code == id) && a.type.includes(type));

================================================================================

📁 FILE 57: auth.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/services/auth.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 58: close-dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/services/close-dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 59: data.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/services/data.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 74] const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
  2. [Dòng 74] item.method === method) : null;

================================================================================

📁 FILE 60: deep_link.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/services/deep_link.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 9] if (isIphone == true) {
  2. [Dòng 22] } else if (isAndroid == true) {

================================================================================

📁 FILE 61: dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/services/dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 62: fee.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/services/fee.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 63: focus-input.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/services/focus-input.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 64: multiple_method.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/services/multiple_method.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 65: payment.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/services/payment.service.ts
📊 Thống kê: 8 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 8 lần
--------------------------------------------------------------------------------

!= (8 điều kiện):
  1. [Dòng 119] if (idInvoice != null
  2. [Dòng 119] idInvoice != 0)
  3. [Dòng 129] idInvoice != 0) {
  4. [Dòng 270] let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
  5. [Dòng 284] if (this._merchantid != null
  6. [Dòng 284] this._tranref != null
  7. [Dòng 284] this._state != null
  8. [Dòng 359] urlCancel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');

================================================================================

📁 FILE 66: token-main.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/services/token-main.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 67: success.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/success/success.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 52] amigo_type == 'SP'"
  2. [Dòng 96] amigo_type == 'PL'"

================================================================================

📁 FILE 68: success.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/success/success.component.ts
📊 Thống kê: 14 điều kiện duy nhất
   - === : 5 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 137] params.timeout === 'true') {
  2. [Dòng 156] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  3. [Dòng 156] _re.body.state === 'unpaid');
  4. [Dòng 219] params.name === 'CUSTOMER_INTIME'
  5. [Dòng 219] params.code === '09') {

== (3 điều kiện):
  1. [Dòng 167] if (this.res.currencies[0] == 'USD') {
  2. [Dòng 195] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
  3. [Dòng 206] this.res.themes.theme == 'general') {

!= (6 điều kiện):
  1. [Dòng 193] } else if (this.res.payments[this.res.payments.length - 1].instrument.issuer.brand != null
  2. [Dòng 194] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id != null
  3. [Dòng 312] if (this.res != null
  4. [Dòng 312] this.res.links != null
  5. [Dòng 312] this.res.links.merchant_return != null
  6. [Dòng 313] this.res.links.merchant_return.href != null) {

================================================================================

📁 FILE 69: index.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/translate/index.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 70: lang-en.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/translate/lang-en.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 71: lang-vi.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/translate/lang-vi.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 72: translate.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/translate/translate.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 73: translate.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/translate/translate.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 37] if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
  2. [Dòng 38] else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';

================================================================================

📁 FILE 74: translations.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/translate/translations.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 75: apps-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/util/apps-info.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 459] if (e.name == bankSwift) { // TODO: get by swift

================================================================================

📁 FILE 76: banks-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/util/banks-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1079] if (+e.id == bankId) {
  2. [Dòng 1129] if (e.swiftCode == bankSwift) {

================================================================================

📁 FILE 77: util.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/app/util/util.ts
📊 Thống kê: 25 điều kiện duy nhất
   - === : 12 lần
   - == : 8 lần
   - !== : 3 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (12 điều kiện):
  1. [Dòng 66] if (v.length === 2
  2. [Dòng 66] this.flag.length === 3
  3. [Dòng 66] this.flag.charAt(this.flag.length - 1) === '/') {
  4. [Dòng 70] if (v.length === 1) {
  5. [Dòng 72] } else if (v.length === 2) {
  6. [Dòng 75] v.length === 2) {
  7. [Dòng 83] if (len === 2) {
  8. [Dòng 183] if (M[1] === 'Chrome') {
  9. [Dòng 308] if (param === key) {
  10. [Dòng 430] pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
  11. [Dòng 434] target === 0
  12. [Dòng 482] if (target.tagName === 'A'

== (8 điều kiện):
  1. [Dòng 19] if (temp.length == 0) {
  2. [Dòng 26] return (counter % 10 == 0);
  3. [Dòng 157] if (this.checkCount == 1) {
  4. [Dòng 169] if (results == null) {
  5. [Dòng 202] if (c.length == 3) {
  6. [Dòng 215] d = d == undefined ? '.' : d
  7. [Dòng 216] t = t == undefined ? '
  8. [Dòng 296] return results == null ? null : results[1]

!== (3 điều kiện):
  1. [Dòng 303] queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
  2. [Dòng 304] if (queryString !== '') {
  3. [Dòng 434] if (target !== 0

!= (2 điều kiện):
  1. [Dòng 185] if (tem != null) {
  2. [Dòng 190] if ((tem = ua.match(/version\/(\d+)/i)) != null) {

================================================================================

📁 FILE 78: qrcode.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/assets/script/qrcode.js
📊 Thống kê: 67 điều kiện duy nhất
   - === : 2 lần
   - == : 53 lần
   - !== : 0 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 2255] if (typeof define === 'function'
  2. [Dòng 2257] } else if (typeof exports === 'object') {

== (53 điều kiện):
  1. [Dòng 68] if (_dataCache == null) {
  2. [Dòng 85] if ( (0 <= r && r <= 6 && (c == 0
  3. [Dòng 85] c == 6) )
  4. [Dòng 86] || (0 <= c && c <= 6 && (r == 0
  5. [Dòng 86] r == 6) )
  6. [Dòng 107] if (i == 0
  7. [Dòng 122] _modules[r][6] = (r % 2 == 0);
  8. [Dòng 129] _modules[6][c] = (c % 2 == 0);
  9. [Dòng 152] if (r == -2
  10. [Dòng 152] r == 2
  11. [Dòng 152] c == -2
  12. [Dòng 152] c == 2
  13. [Dòng 153] || (r == 0
  14. [Dòng 153] c == 0) ) {
  15. [Dòng 169] ( (bits >> i) & 1) == 1);
  16. [Dòng 226] if (col == 6) col -= 1;
  17. [Dòng 232] if (_modules[row][col - c] == null) {
  18. [Dòng 237] dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
  19. [Dòng 249] if (bitIndex == -1) {
  20. [Dòng 458] margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
  21. [Dòng 498] if (typeof arguments[0] == 'object') {
  22. [Dòng 587] margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
  23. [Dòng 733] if (b == -1) throw 'eof';
  24. [Dòng 741] if (b0 == -1) break;
  25. [Dòng 767] if (typeof b == 'number') {
  26. [Dòng 768] if ( (b & 0xff) == b) {
  27. [Dòng 910] return function(i, j) { return (i + j) % 2 == 0
  28. [Dòng 912] return function(i, j) { return i % 2 == 0
  29. [Dòng 914] return function(i, j) { return j % 3 == 0
  30. [Dòng 916] return function(i, j) { return (i + j) % 3 == 0
  31. [Dòng 918] return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
  32. [Dòng 920] return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
  33. [Dòng 922] return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
  34. [Dòng 924] return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
  35. [Dòng 1011] if (r == 0
  36. [Dòng 1011] c == 0) {
  37. [Dòng 1015] if (dark == qrcode.isDark(row + r, col + c) ) {
  38. [Dòng 1036] if (count == 0
  39. [Dòng 1036] count == 4) {
  40. [Dòng 1149] if (typeof num.length == 'undefined') {
  41. [Dòng 1155] num[offset] == 0) {
  42. [Dòng 1495] if (typeof rsBlock == 'undefined') {
  43. [Dòng 1538] return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
  44. [Dòng 1543] _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
  45. [Dòng 1599] if (data.length - i == 1) {
  46. [Dòng 1601] } else if (data.length - i == 2) {
  47. [Dòng 1866] } else if (n == 62) {
  48. [Dòng 1868] } else if (n == 63) {
  49. [Dòng 1928] if (_buflen == 0) {
  50. [Dòng 1937] if (c == '=') {
  51. [Dòng 1961] } else if (c == 0x2b) {
  52. [Dòng 1963] } else if (c == 0x2f) {
  53. [Dòng 2133] if (table.size() == (1 << bitLength) ) {

!= (12 điều kiện):
  1. [Dòng 119] if (_modules[r][6] != null) {
  2. [Dòng 126] if (_modules[6][c] != null) {
  3. [Dòng 144] if (_modules[row][col] != null) {
  4. [Dòng 365] while (buffer.getLengthInBits() % 8 != 0) {
  5. [Dòng 750] if (count != numChars) {
  6. [Dòng 751] throw count + ' != ' + numChars
  7. [Dòng 878] while (data != 0) {
  8. [Dòng 1733] if (test.length != 2
  9. [Dòng 1733] ( (test[0] << 8) | test[1]) != code) {
  10. [Dòng 1894] if (_length % 3 != 0) {
  11. [Dòng 2067] if ( (data >>> length) != 0) {
  12. [Dòng 2178] return typeof _map[key] != 'undefined'

================================================================================

📁 FILE 79: environment.prod.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/environments/environment.prod.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 80: environment.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/environments/environment.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 81: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/acc_bidv/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 82: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/acc_bidv/script.js
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 9] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 12] if (inName.value === "") return err("MISSING_FIELD"
  3. [Dòng 21] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 23] if (e !== null) {
  2. [Dòng 55] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 83: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/acc_oceanbank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 84: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/acc_oceanbank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 10] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 19] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 21] if (e !== null) {
  2. [Dòng 53] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 85: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/acc_pvcombank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 86: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/acc_pvcombank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 7] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 16] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 18] if (e !== null) {
  2. [Dòng 50] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 87: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/acc_shb/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 88: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/acc_shb/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 10] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 19] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 21] if (e !== null) {
  2. [Dòng 53] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 89: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/acc_tpbank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 90: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/acc_tpbank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 7] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 16] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 18] if (e !== null) {
  2. [Dòng 50] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 91: common.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/common.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 89] if (i % 2 === parity) d *= 2;
  2. [Dòng 93] return (sum % 10) === 0
  3. [Dòng 163] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 166] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 252] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 258] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 274] if (xhr.status === 200
  8. [Dòng 274] xhr.status === 201) {
  9. [Dòng 277] if (invoiceState === "unpaid"
  10. [Dòng 277] invoiceState === "not_paid") {
  11. [Dòng 282] if (paymentState === "authorization_required") {
  12. [Dòng 288] if (method === "REDIRECT") {
  13. [Dòng 291] } else if (method === "POST_REDIRECT") {
  14. [Dòng 330] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 141] this.oldValue !== this.value) {
  2. [Dòng 313] if (jLinks !== undefined
  3. [Dòng 313] jLinks !== null) {
  4. [Dòng 315] if (jMerchantReturn !== undefined
  5. [Dòng 315] jMerchantReturn !== null) {
  6. [Dòng 330] if (responseCode !== undefined
  7. [Dòng 330] responseCode !== null
  8. [Dòng 358] if (parentRes !== "{

================================================================================

📁 FILE 92: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/domestic_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 93: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/domestic_card/script.js
📊 Thống kê: 14 điều kiện duy nhất
   - === : 11 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 18] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 60] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 63] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 72] year === y
  5. [Dòng 74] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 106] if (inPhone.value === "") return err("MISSING_FIELD"
  7. [Dòng 136] if ("PAY" === operation) {
  8. [Dòng 525] } else if (value === "") {
  9. [Dòng 542] if (trPhone.style.display === "") {
  10. [Dòng 544] } else if (trName.style.display === "") {
  11. [Dòng 565] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 138] if (e !== null) {
  2. [Dòng 404] if (lang !== "vi") lang = "en";
  3. [Dòng 470] if (value !== "") {

================================================================================

📁 FILE 94: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/domestic_token/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 95: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/domestic_token/script.js
📊 Thống kê: 25 điều kiện duy nhất
   - === : 23 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (23 điều kiện):
  1. [Dòng 20] if ("PAY" === operation) {
  2. [Dòng 93] if (trName.style.display === "") {
  3. [Dòng 117] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  4. [Dòng 123] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  5. [Dòng 137] if (xhr.status === 200) {
  6. [Dòng 165] if (insType === "card") {
  7. [Dòng 166] if (insBrandId === "visa"
  8. [Dòng 166] insBrandId === "mastercard"
  9. [Dòng 166] insBrandId === "amex"
  10. [Dòng 166] insBrandId === "jcb"
  11. [Dòng 166] insBrandId === "cup") {
  12. [Dòng 170] } else if (insBrandId === "atm") {
  13. [Dòng 241] } else if (insType === "dongabank_account") {
  14. [Dòng 242] } else if (insType === "techcombank_account") {
  15. [Dòng 243] } else if (insType === "vib_account") {
  16. [Dòng 244] } else if (insType === "bidv_account") {
  17. [Dòng 245] } else if (insType === "tpbank_account") {
  18. [Dòng 246] } else if (insType === "shb_account") {
  19. [Dòng 247] } else if (insType === "shb_customer_id") {
  20. [Dòng 248] } else if (insType === "vpbank_account") {
  21. [Dòng 249] } else if (insType === "oceanbank_online_account") {
  22. [Dòng 250] } else if (insType === "oceanbank_mobile_account") {
  23. [Dòng 251] } else if (insType === "pvcombank_account") {

!== (2 điều kiện):
  1. [Dòng 54] if (lang !== "vi") lang = "en";
  2. [Dòng 273] if (parentRes !== "{

================================================================================

📁 FILE 96: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 97: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/international_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 98: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/international_card/script.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 13] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 23] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 26] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 38] year === y
  5. [Dòng 40] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 71] if ("PAY" === operation) {
  7. [Dòng 161] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 73] if (e !== null) {
  2. [Dòng 118] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 99: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/international_token/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 100: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/international_token/script.js
📊 Thống kê: 36 điều kiện duy nhất
   - === : 25 lần
   - == : 0 lần
   - !== : 11 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (25 điều kiện):
  1. [Dòng 32] year === y
  2. [Dòng 34] if (inCvv.value === "") {
  3. [Dòng 67] if ("PAY" === operation) {
  4. [Dòng 129] } else if (value === "") {
  5. [Dòng 190] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 196] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 209] if (xhr.status === 200) {
  8. [Dòng 245] if (insType === "card") {
  9. [Dòng 246] if (insBrandId === "visa"
  10. [Dòng 246] insBrandId === "mastercard"
  11. [Dòng 246] insBrandId === "amex"
  12. [Dòng 246] insBrandId === "jcb"
  13. [Dòng 246] insBrandId === "cup") {
  14. [Dòng 248] } else if (insBrandId === "atm") {
  15. [Dòng 319] } else if (insType === "dongabank_account") {
  16. [Dòng 320] } else if (insType === "techcombank_account") {
  17. [Dòng 321] } else if (insType === "vib_account") {
  18. [Dòng 322] } else if (insType === "bidv_account") {
  19. [Dòng 323] } else if (insType === "tpbank_account") {
  20. [Dòng 324] } else if (insType === "shb_account") {
  21. [Dòng 325] } else if (insType === "shb_customer_id") {
  22. [Dòng 326] } else if (insType === "vpbank_account") {
  23. [Dòng 327] } else if (insType === "oceanbank_online_account") {
  24. [Dòng 328] } else if (insType === "oceanbank_mobile_account") {
  25. [Dòng 329] } else if (insType === "pvcombank_account") {

!== (11 điều kiện):
  1. [Dòng 18] if (inMonth.value !== ""
  2. [Dòng 21] if (inYear.value !== ""
  3. [Dòng 23] var month = inMonth.value !== ""
  4. [Dòng 24] var year = parseInt("20" + (inYear.value !== ""
  5. [Dòng 35] if ("2D" !== order["vpc_VerType"]) return err("MISSING_FIELD"
  6. [Dòng 71] if (e !== null) {
  7. [Dòng 79] inMonth.value !== tokenInsMonth) instrument["month"] = inMonth.value;
  8. [Dòng 80] inYear.value !== tokenInsYear) instrument["year"] = inYear.value;
  9. [Dòng 81] if (inCvv.value !== "") instrument["cvv"] = inCvv.value;
  10. [Dòng 95] if (lang !== "vi") lang = "en";
  11. [Dòng 351] if (parentRes !== "{

================================================================================

📁 FILE 101: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/amway/script.js
📊 Thống kê: 54 điều kiện duy nhất
   - === : 41 lần
   - == : 1 lần
   - !== : 12 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (41 điều kiện):
  1. [Dòng 4] if ((cardno.length === 15
  2. [Dòng 4] cardno.length === 16
  3. [Dòng 4] cardno.length === 19) && isMode10(cardno) === true) {
  4. [Dòng 4] isMode10(cardno) === true) {
  5. [Dòng 23] if (i % 2 === parity) d *= 2;
  6. [Dòng 27] return (sum % 10) === 0
  7. [Dòng 48] if ("PAY" === operation) {
  8. [Dòng 69] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  9. [Dòng 75] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  10. [Dòng 91] if (xhr.status === 200
  11. [Dòng 91] xhr.status === 201) {
  12. [Dòng 94] if (invoiceState === "unpaid"
  13. [Dòng 94] invoiceState === "not_paid") {
  14. [Dòng 99] if (paymentState === "authorization_required") {
  15. [Dòng 105] if (method === "REDIRECT") {
  16. [Dòng 110] } else if (method === "POST_REDIRECT") {
  17. [Dòng 223] if (typeof queryParams[key] === "undefined") {
  18. [Dòng 226] } else if (typeof queryParams[key] === "string") {
  19. [Dòng 256] if (params["CardList"] === undefined
  20. [Dòng 256] params["CardList"] === null) return;
  21. [Dòng 302] if (xhr.status === 200) {
  22. [Dòng 312] if (insType === "card") {
  23. [Dòng 313] if (insBrandId === "visa"
  24. [Dòng 313] insBrandId === "mastercard"
  25. [Dòng 313] insBrandId === "amex"
  26. [Dòng 313] insBrandId === "jcb"
  27. [Dòng 313] insBrandId === "cup") {
  28. [Dòng 317] } else if (insBrandId === "atm") {
  29. [Dòng 318] if (insSwiftCode === "BFTVVNVX") { //Vietcombank
  30. [Dòng 322] } else if (insSwiftCode === "BIDVVNVX") { //BIDV
  31. [Dòng 329] } else if (insType === "dongabank_account") {
  32. [Dòng 331] } else if (insType === "techcombank_account") {
  33. [Dòng 333] } else if (insType === "vib_account") {
  34. [Dòng 335] } else if (insType === "bidv_account") {
  35. [Dòng 336] } else if (insType === "tpbank_account") {
  36. [Dòng 337] } else if (insType === "shb_account") {
  37. [Dòng 338] } else if (insType === "shb_customer_id") {
  38. [Dòng 339] } else if (insType === "vpbank_account") {
  39. [Dòng 340] } else if (insType === "oceanbank_online_account") {
  40. [Dòng 341] } else if (insType === "oceanbank_mobile_account") {
  41. [Dòng 342] } else if (insType === "pvcombank_account") {

== (1 điều kiện):
  1. [Dòng 270] if (cardList.length == 1) {//TOKEN, BIDV, SHB, OCEAN, PVCOM, TP Bank

!== (12 điều kiện):
  1. [Dòng 52] if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
  2. [Dòng 53] if (inNumber.style.display !== "none") instrument.number = inNumber.value;
  3. [Dòng 54] if (inDate.style.display !== "none") instrument.date = inDate.value;
  4. [Dòng 55] if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
  5. [Dòng 56] if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
  6. [Dòng 57] if (inName.style.display !== "none") instrument.name = inName.value;
  7. [Dòng 132] if (jLinks !== undefined
  8. [Dòng 132] jLinks !== null) {
  9. [Dòng 134] if (jMerchantReturn !== undefined
  10. [Dòng 134] jMerchantReturn !== null) {
  11. [Dòng 166] if (parentRes !== "{
  12. [Dòng 255] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 102: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 103: bidv.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Bidv/assets/js/bidv.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 233] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 239] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 255] if (xhr.status === 200
  8. [Dòng 255] xhr.status === 201) {
  9. [Dòng 258] if (invoiceState === "unpaid"
  10. [Dòng 258] invoiceState === "not_paid") {
  11. [Dòng 263] if (paymentState === "authorization_required") {
  12. [Dòng 269] if (method === "REDIRECT") {
  13. [Dòng 272] } else if (method === "POST_REDIRECT") {
  14. [Dòng 311] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 294] if (jLinks !== undefined
  3. [Dòng 294] jLinks !== null) {
  4. [Dòng 296] if (jMerchantReturn !== undefined
  5. [Dòng 296] jMerchantReturn !== null) {
  6. [Dòng 311] if (responseCode !== undefined
  7. [Dòng 311] responseCode !== null
  8. [Dòng 339] if (parentRes !== "{

================================================================================

📁 FILE 104: bidv2.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Bidv/bidv2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 76] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 88] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 78] if ( $('.circle_v1').css('display') == 'block'
  2. [Dòng 112] if ($('.circle_v2').css('display') == 'block'
  3. [Dòng 112] $('.circle_v3').css('display') == 'block' ) {
  4. [Dòng 302] $('.circle_v1').css('display') == 'block'

!== (3 điều kiện):
  1. [Dòng 90] if (e !== null) {
  2. [Dòng 273] if (lang !== "vi") lang = "en";
  3. [Dòng 305] if (value !== "") {

================================================================================

📁 FILE 105: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Bidv/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 106: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 107: ocean.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Ocean/assets/js/ocean.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 232] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 237] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 251] if (xhr.status === 200
  8. [Dòng 251] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 257] if (paymentState === "authorization_required") {
  12. [Dòng 261] if (method === "REDIRECT") {
  13. [Dòng 264] } else if (method === "POST_REDIRECT") {
  14. [Dòng 303] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 286] if (jLinks !== undefined
  3. [Dòng 286] jLinks !== null) {
  4. [Dòng 288] if (jMerchantReturn !== undefined
  5. [Dòng 288] jMerchantReturn !== null) {
  6. [Dòng 303] if (responseCode !== undefined
  7. [Dòng 303] responseCode !== null
  8. [Dòng 331] if (parentRes !== "{

================================================================================

📁 FILE 108: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Ocean/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 109: ocean2.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Ocean/ocean2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 72] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 92] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 74] document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM'
  2. [Dòng 116] if ( document.getElementsByClassName("select_online")[0].value == 'Easy Online Banking') {
  3. [Dòng 119] if ( document.getElementsByClassName("select_online")[0].value == 'Easy Mobile Banking') {
  4. [Dòng 304] if ( document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM') {

!== (3 điều kiện):
  1. [Dòng 94] if (e !== null) {
  2. [Dòng 276] if (lang !== "vi") lang = "en";
  3. [Dòng 306] if (value !== "") {

================================================================================

📁 FILE 110: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 111: pvbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Pv_Bank/assets/js/pvbank.js
📊 Thống kê: 23 điều kiện duy nhất
   - === : 14 lần
   - == : 1 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 239] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 261] if (xhr.status === 200
  8. [Dòng 261] xhr.status === 201) {
  9. [Dòng 264] if (invoiceState === "unpaid"
  10. [Dòng 264] invoiceState === "not_paid") {
  11. [Dòng 269] if (paymentState === "authorization_required") {
  12. [Dòng 275] if (method === "REDIRECT") {
  13. [Dòng 278] } else if (method === "POST_REDIRECT") {
  14. [Dòng 317] responseCode === "0") {

== (1 điều kiện):
  1. [Dòng 167] if ($('.circle_v1').css('display') == 'block') {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 300] if (jLinks !== undefined
  3. [Dòng 300] jLinks !== null) {
  4. [Dòng 302] if (jMerchantReturn !== undefined
  5. [Dòng 302] jMerchantReturn !== null) {
  6. [Dòng 317] if (responseCode !== undefined
  7. [Dòng 317] responseCode !== null
  8. [Dòng 345] if (parentRes !== "{

================================================================================

📁 FILE 112: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Pv_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 113: pvbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Pv_Bank/pvbank2.js
📊 Thống kê: 15 điều kiện duy nhất
   - === : 8 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 71] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 76] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 79] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 88] year === y
  5. [Dòng 90] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 100] if ("PAY" === operation) {
  7. [Dòng 347] } else if (value === "") {
  8. [Dòng 364] if (trName.style.display === "") {

== (4 điều kiện):
  1. [Dòng 73] if ($('.circle_v1').css('display') == 'block'
  2. [Dòng 75] $('.circle_v1').css('display') == 'block'
  3. [Dòng 124] if ( $('.circle_v1').css('display') == 'block') {
  4. [Dòng 129] else if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 102] if (e !== null) {
  2. [Dòng 286] if (lang !== "vi") lang = "en";
  3. [Dòng 316] if (value !== "") {

================================================================================

📁 FILE 114: Sea2.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/SeaBank/Sea2.js
📊 Thống kê: 11 điều kiện duy nhất
   - === : 8 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 23] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 31] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 34] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 43] year === y
  5. [Dòng 45] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 55] if ("PAY" === operation) {
  7. [Dòng 287] } else if (value === "") {
  8. [Dòng 304] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 57] if (e !== null) {
  2. [Dòng 233] if (lang !== "vi") lang = "en";
  3. [Dòng 263] if (value !== "") {

================================================================================

📁 FILE 115: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 116: Sea.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/SeaBank/assets/js/Sea.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 228] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 234] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 258] if (paymentState === "authorization_required") {
  12. [Dòng 264] if (method === "REDIRECT") {
  13. [Dòng 267] } else if (method === "POST_REDIRECT") {
  14. [Dòng 306] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 289] if (jLinks !== undefined
  3. [Dòng 289] jLinks !== null) {
  4. [Dòng 291] if (jMerchantReturn !== undefined
  5. [Dòng 291] jMerchantReturn !== null) {
  6. [Dòng 306] if (responseCode !== undefined
  7. [Dòng 306] responseCode !== null
  8. [Dòng 334] if (parentRes !== "{

================================================================================

📁 FILE 117: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/SeaBank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 118: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 119: shb.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Shb/assets/js/shb.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 234] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 240] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 256] if (xhr.status === 200
  8. [Dòng 256] xhr.status === 201) {
  9. [Dòng 259] if (invoiceState === "unpaid"
  10. [Dòng 259] invoiceState === "not_paid") {
  11. [Dòng 264] if (paymentState === "authorization_required") {
  12. [Dòng 270] if (method === "REDIRECT") {
  13. [Dòng 273] } else if (method === "POST_REDIRECT") {
  14. [Dòng 312] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 295] if (jLinks !== undefined
  3. [Dòng 295] jLinks !== null) {
  4. [Dòng 297] if (jMerchantReturn !== undefined
  5. [Dòng 297] jMerchantReturn !== null) {
  6. [Dòng 312] if (responseCode !== undefined
  7. [Dòng 312] responseCode !== null
  8. [Dòng 340] if (parentRes !== "{

================================================================================

📁 FILE 120: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Shb/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 121: shb2.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Shb/shb2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 73] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 85] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 74] if ($('.circle_v2').css('display') == 'block'
  2. [Dòng 110] if ($('.circle_v1').css('display') == 'block'
  3. [Dòng 114] if ( $('.circle_v3').css('display') == 'block' ) {
  4. [Dòng 299] if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 87] if (e !== null) {
  2. [Dòng 271] if (lang !== "vi") lang = "en";
  3. [Dòng 301] if (value !== "") {

================================================================================

📁 FILE 122: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 123: tpbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Tp_Bank/assets/js/tpbank.js
📊 Thống kê: 23 điều kiện duy nhất
   - === : 14 lần
   - == : 1 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 239] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 261] if (xhr.status === 200
  8. [Dòng 261] xhr.status === 201) {
  9. [Dòng 264] if (invoiceState === "unpaid"
  10. [Dòng 264] invoiceState === "not_paid") {
  11. [Dòng 269] if (paymentState === "authorization_required") {
  12. [Dòng 275] if (method === "REDIRECT") {
  13. [Dòng 278] } else if (method === "POST_REDIRECT") {
  14. [Dòng 317] responseCode === "0") {

== (1 điều kiện):
  1. [Dòng 167] if ($('.circle_v1').css('display') == 'block') {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 300] if (jLinks !== undefined
  3. [Dòng 300] jLinks !== null) {
  4. [Dòng 302] if (jMerchantReturn !== undefined
  5. [Dòng 302] jMerchantReturn !== null) {
  6. [Dòng 317] if (responseCode !== undefined
  7. [Dòng 317] responseCode !== null
  8. [Dòng 345] if (parentRes !== "{

================================================================================

📁 FILE 124: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Tp_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 125: tpbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Tp_Bank/tpbank2.js
📊 Thống kê: 15 điều kiện duy nhất
   - === : 8 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 71] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 78] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 81] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 90] year === y
  5. [Dòng 92] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 102] if ("PAY" === operation) {
  7. [Dòng 349] } else if (value === "") {
  8. [Dòng 366] if (trName.style.display === "") {

== (4 điều kiện):
  1. [Dòng 73] if ($('.circle_v1').css('display') == 'block'
  2. [Dòng 76] $('.circle_v1').css('display') == 'block'
  3. [Dòng 126] if ( $('.circle_v1').css('display') == 'block') {
  4. [Dòng 131] else if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 104] if (e !== null) {
  2. [Dòng 288] if (lang !== "vi") lang = "en";
  3. [Dòng 318] if (value !== "") {

================================================================================

📁 FILE 126: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 127: onepay.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Visa/assets/js/onepay.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 78] if (i % 2 === parity) d *= 2;
  2. [Dòng 82] return (sum % 10) === 0
  3. [Dòng 152] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 155] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 240] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 259] if (xhr.status === 200
  8. [Dòng 259] xhr.status === 201) {
  9. [Dòng 261] if (invoiceState === "unpaid"
  10. [Dòng 261] invoiceState === "not_paid") {
  11. [Dòng 265] if (paymentState === "authorization_required") {
  12. [Dòng 269] if (method === "REDIRECT") {
  13. [Dòng 272] } else if (method === "POST_REDIRECT") {
  14. [Dòng 311] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 130] this.oldValue !== this.value) {
  2. [Dòng 294] if (jLinks !== undefined
  3. [Dòng 294] jLinks !== null) {
  4. [Dòng 296] if (jMerchantReturn !== undefined
  5. [Dòng 296] jMerchantReturn !== null) {
  6. [Dòng 311] if (responseCode !== undefined
  7. [Dòng 311] responseCode !== null
  8. [Dòng 339] if (parentRes !== "{

================================================================================

📁 FILE 128: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Visa/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 129: index.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Visa/index.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 19] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 29] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 32] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 44] year === y
  5. [Dòng 46] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 77] if ("PAY" === operation) {
  7. [Dòng 168] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 79] if (e !== null) {
  2. [Dòng 125] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 130: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 131: vpbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Vp_Bank/assets/js/vpbank.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 230] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 236] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 252] if (xhr.status === 200
  8. [Dòng 252] xhr.status === 201) {
  9. [Dòng 255] if (invoiceState === "unpaid"
  10. [Dòng 255] invoiceState === "not_paid") {
  11. [Dòng 260] if (paymentState === "authorization_required") {
  12. [Dòng 266] if (method === "REDIRECT") {
  13. [Dòng 269] } else if (method === "POST_REDIRECT") {
  14. [Dòng 308] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 291] if (jLinks !== undefined
  3. [Dòng 291] jLinks !== null) {
  4. [Dòng 293] if (jMerchantReturn !== undefined
  5. [Dòng 293] jMerchantReturn !== null) {
  6. [Dòng 308] if (responseCode !== undefined
  7. [Dòng 308] responseCode !== null
  8. [Dòng 336] if (parentRes !== "{

================================================================================

📁 FILE 132: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Vp_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 133: vpbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/Vp_Bank/vpbank2.js
📊 Thống kê: 14 điều kiện duy nhất
   - === : 11 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 32] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 35] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 44] year === y
  5. [Dòng 46] if (inPhone.value === "") return err("MISSING_FIELD"
  6. [Dòng 49] if (inName.value === "") return err("MISSING_FIELD"
  7. [Dòng 59] if ("PAY" === operation) {
  8. [Dòng 292] } else if (value === "") {
  9. [Dòng 309] if (trPhone.style.display === "") {
  10. [Dòng 311] } else if (trName.style.display === "") {
  11. [Dòng 332] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 61] if (e !== null) {
  2. [Dòng 237] if (lang !== "vi") lang = "en";
  3. [Dòng 267] if (value !== "") {

================================================================================

📁 FILE 134: sha.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/assets/js/sha.js
📊 Thống kê: 49 điều kiện duy nhất
   - === : 38 lần
   - == : 0 lần
   - !== : 11 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (38 điều kiện):
  1. [Dòng 12] 1>t)throw Error("numRounds must a integer >= 1");if("SHA-1"===c)m=512,q=K,u=Z,f=160,r=function(a){return a.slice()};else if(0===c.lastIndexOf("SHA-",0))if(q=function(a,b){return L(a,b,c)
  2. [Dòng 12] c)},u=function(a,b,h,e){var k,f;if("SHA-224"===c
  3. [Dòng 12] "SHA-256"===c)k=(b+65>>>9<<4)+15,f=16;else if("SHA-384"===c
  4. [Dòng 12] "SHA-512"===c)k=(b+129>>>10<<
  5. [Dòng 13] c);if("SHA-224"===c)a=[e[0],e[1],e[2],e[3],e[4],e[5],e[6]];else if("SHA-256"===c)a=e;else if("SHA-384"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,e[4].b,e[5].a,e[5].b];else if("SHA-512"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,
  6. [Dòng 14] "SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)
  7. [Dòng 14] 0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
  8. [Dòng 14] return a},r=function(a){return a.slice()},"SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)||0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
  9. [Dòng 15] c)m=1152,f=224;else if("SHA3-256"===c)m=1088,f=256;else if("SHA3-384"===c)m=832,f=384;else if("SHA3-512"===c)m=576,f=512;else if("SHAKE128"===c)m=1344,f=-1,F=31,z=!0;else if("SHAKE256"===c)m=1088,f=-1,F=31,z=!0;else throw Error("Chosen SHA variant is not supported");u=function(a
  10. [Dòng 16] 0===64*l%e
  11. [Dòng 16] 5][l/5|0];g.push(a.b);if(32*g.length>=h)break;g.push(a.a);l+=1;0===64*l%e&&D(null,b)}return g}}else throw Error("Chosen SHA variant is not supported");d=M(a,g,x);l=A(c);this.setHMACKey=function(a,b,h){var k;if(!0===I)throw Error("HMAC key already set");if(!0===y)throw Error("Cannot set HMAC key after calling update");if(!0===z)throw Error("SHAKE is not supported for HMAC");g=(h
  12. [Dòng 17] f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
  13. [Dòng 17] )b.push(0);b[h]&=4294967040}for(a=0;a<=h;a+=1)v[a]=b[a]^909522486,w[a]=b[a]^1549556828;l=q(v,l);e=m;I=!0};this.update=function(a){var c,b,k,f=0,g=m>>>5;c=d(a,h,n);a=c.binLen;b=c.value;c=a>>>5;for(k=0;k<c;k+=g)f+m<=a&&(l=q(b.slice(k,k+g),l),f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
  14. [Dòng 18] for(g=1;g<t;g+=1)!0===z
  15. [Dòng 19] f);return k(m)};this.getHMAC=function(a,b){var k,g,d,p;if(!1===I)throw Error("Cannot call getHMAC without first setting HMAC key");d=N(b);switch(a){case "HEX":k=function(a){return O(a,f,x,d)};break;case "B64":k=function(a){return P(a,f,x,d)};break;case "BYTES":k=function(a){return Q(a,f,x)};break;case "ARRAYBUFFER":try{k=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}k=function(a){return R(a,f,x)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
  16. [Dòng 20] d=-1===b?3:0
  17. [Dòng 20] f=-1===b?3:0
  18. [Dòng 21] g=-1===b?3:0
  19. [Dòng 22] !0===c.hasOwnProperty("b64Pad")
  20. [Dòng 22] a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");
  21. [Dòng 23] u=-1===b?3:0
  22. [Dòng 23] l+=2){p=parseInt(a.substr(l,2),16);if(isNaN(p))throw Error("String of HEX type contains invalid characters");m=(l>>>1)+q;for(f=m>>>2;c.length<=f;)c.push(0);c[f]|=p<<8*(u+m%4*b)}return{value:c,binLen:4*g+d}};break;case "TEXT":c=function(c,h,d){var g,l,p=0,f,m,q,u,r,t;h=h||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(t=-1===
  23. [Dòng 24] m+=1){r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=l[m]<<8*(t+r%4*b);p+=1}else if("UTF16BE"===a
  24. [Dòng 24] "UTF16LE"===a)for(t=-1===b?2:0
  25. [Dòng 24] UTF16LE"===a
  26. [Dòng 24] 1===b
  27. [Dòng 25] !0===l
  28. [Dòng 25] !0===l&&(m=g&255,g=m<<8|g>>>8);r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=g<<8*(t+r%4*b);p+=2}return{value:h,binLen:8*p+d}};break;case "B64":c=function(a,c,d){var g=0,l,p,f,m,q,u,r,t;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");p=a.indexOf("=");a=a.replace(/\=/g
  29. [Dòng 25] t=-1===b?3:0
  30. [Dòng 26] q=-1===b?3:0
  31. [Dòng 27] m=-1===b?3:0
  32. [Dòng 32] c.b^a.b)}function A(c){var a=[],d;if("SHA-1"===c)a=[1732584193,4023233417,2562383102,271733878,3285377520];else if(0===c.lastIndexOf("SHA-",0))switch(a=
  33. [Dòng 34] new b(d[2],4271175723),new b(d[3],1595750129),new b(d[4],2917565137),new b(d[5],725511199),new b(d[6],4215389547),new b(d[7],327033209)];break;default:throw Error("Unknown SHA variant");}else if(0===c.lastIndexOf("SHA3-",0)
  34. [Dòng 34] 0===c.lastIndexOf("SHAKE",0))for(c=0
  35. [Dòng 36] a,k){var e,h,n,g,l,p,f,m,q,u,r,t,v,w,y,A,z,x,F,B,C,D,E=[],J;if("SHA-224"===k
  36. [Dòng 36] "SHA-256"===k)u=64,t=1,D=Number,v=G,w=la,y=H,A=ha,z=ja,x=da,F=fa,C=U,B=aa,J=d;else if("SHA-384"===k
  37. [Dòng 36] "SHA-512"===k)u=80,t=2,D=b,v=ma,w=na,y=oa,A=ia,z=ka,x=ea,F=ga,C=ca,B=ba,J=V;else throw Error("Unexpected error in SHA-2 implementation");k=a[0];e=a[1];h=a[2];n=a[3];g=a[4];l=a[5];p=a[6];f=a[7];for(r=0
  38. [Dòng 45] "function"===typeof define

!== (11 điều kiện):
  1. [Dòng 12] 'use strict';(function(Y){function C(c,a,b){var e=0,h=[],n=0,g,l,d,f,m,q,u,r,I=!1,v=[],w=[],t,y=!1,z=!1,x=-1;b=b||{};g=b.encoding||"UTF8";t=b.numRounds||1;if(t!==parseInt(t,10)
  2. [Dòng 18] 0!==f%32
  3. [Dòng 22] a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8
  4. [Dòng 23] }switch(c){case "HEX":c=function(a,c,d){var g=a.length,l,p,f,m,q,u;if(0!==g%2)throw Error("String of HEX type must be in byte increments");c=c||[0];d=d||0;q=d>>>3;u=-1===b?3:0;for(l=0
  5. [Dòng 24] 1!==b
  6. [Dòng 24] "UTF16LE"!==a
  7. [Dòng 25] "");if(-1!==p
  8. [Dòng 27] function(a,c,d){var g,l,p,f,m,q;c=c||[0];d=d||0;l=d>>>3;m=-1===b?3:0;q=new Uint8Array(a);for(g=0;g<a.byteLength;g+=1)f=g+l,p=f>>>2,c.length<=p&&c.push(0),c[p]|=q[g]<<8*(m+f%4*b);return{value:c,binLen:8*a.byteLength+d}};break;default:throw Error("format must be HEX, TEXT, B64, BYTES, or ARRAYBUFFER");}return c}function y(c,a){return c<<a|c>>>32-a}function S(c,a){return 32<a?(a-=32,new b(c.b<<a|c.a>>>32-a,c.a<<a|c.b>>>32-a)):0!==a?new b(c.a<<a|c.b>>>32-a,c.b<<a|c.a>>>32-a):c}function w(c,a){return c>>>
  9. [Dòng 37] a[7]);return a}function D(c,a){var d,e,h,n,g=[],l=[];if(null!==c)for(e=0
  10. [Dòng 45] define.amd?define(function(){return C}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=C),exports=C):Y.jsSHA=C})(this);
  11. [Dòng 45] return C}):"undefined"!==typeof exports?("undefined"!==typeof module

================================================================================

📁 FILE 135: sha256.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/assets/js/sha256.js
📊 Thống kê: 28 điều kiện duy nhất
   - === : 20 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (20 điều kiện):
  1. [Dòng 12] 1>u)throw Error("numRounds must a integer >= 1");if(0===c.lastIndexOf("SHA-",0))if(q=function(b,a){return A(b,a,c)
  2. [Dòng 12] c)},y=function(b,a,l,f){var g,e;if("SHA-224"===c
  3. [Dòng 12] "SHA-256"===c)g=(a+65>>>9<<4)+15,e=16;else throw Error("Unexpected error in SHA-2 implementation");for(
  4. [Dòng 13] c);if("SHA-224"===c)b=[f[0],f[1],f[2],f[3],f[4],f[5],f[6]];else if("SHA-256"===c)b=f;else throw Error("Unexpected error in SHA-2 implementation");return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
  5. [Dòng 13] "SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a
  6. [Dòng 13] return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
  7. [Dòng 14] if(!0===z)throw Error("Cannot set HMAC key after calling update");f=(g
  8. [Dòng 15] 5);g=a%h;z=!0};this.getHash=function(a,f){var d,h,k,q;if(!0===m)throw Error("Cannot call getHash after setting HMAC key");k=C(f);switch(a){case "HEX":d=function(a){return D(a,e,k)};break;case "B64":d=function(a){return E(a,e,k)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{h=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("format must be HEX, B64, BYTES, or ARRAYBUFFER");
  9. [Dòng 16] x(c));return d(q)};this.getHMAC=function(a,f){var d,k,t,u;if(!1===m)throw Error("Cannot call getHMAC without first setting HMAC key");t=C(f);switch(a){case "HEX":d=function(a){return D(a,e,t)};break;case "B64":d=function(a){return E(a,e,t)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{d=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
  10. [Dòng 18] !0===c.hasOwnProperty("b64Pad")
  11. [Dòng 20] h=(d>>>1)+q;for(e=h>>>2;b.length<=e;)b.push(0);b[e]|=k<<8*(3+h%4*-1)}return{value:b,binLen:4*f+c}};break;case "TEXT":d=function(c,b,d){var f,n,k=0,e,h,q,m,p,r;b=b||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(r=3
  12. [Dòng 21] q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=n[h]<<8*(r+p%4*-1);k+=1}else if("UTF16BE"===a
  13. [Dòng 21] "UTF16LE"===a)for(r=2
  14. [Dòng 21] UTF16LE"===a
  15. [Dòng 21] !0===n
  16. [Dòng 21] e+=1){f=c.charCodeAt(e);!0===n&&(h=f&255,f=h<<8|f>>>8);p=k+q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=f<<8*(r+p%4*-1);k+=2}return{value:b,binLen:8*k+d}};break;case "B64":d=function(a,b,c){var f=0,d,k,e,h,q,m,p;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");k=a.indexOf("=");a=a.replace(/\=/g
  17. [Dòng 25] 16)&65535)<<16|b&65535}function R(c,a,d,l,b){var g=(c&65535)+(a&65535)+(d&65535)+(l&65535)+(b&65535);return((c>>>16)+(a>>>16)+(d>>>16)+(l>>>16)+(b>>>16)+(g>>>16)&65535)<<16|g&65535}function x(c){var a=[],d;if(0===c.lastIndexOf("SHA-",0))switch(a=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428]
  18. [Dòng 26] new m,new m,new m,new m,new m,new m];break;case "SHA-512":a=[new m,new m,new m,new m,new m,new m,new m,new m];break;default:throw Error("Unknown SHA variant");}else throw Error("No SHA variants supported");return a}function A(c,a,d){var l,b,g,f,n,k,e,h,m,r,p,w,t,x,u,z,A,B,C,D,E,F,v=[],G;if("SHA-224"===d
  19. [Dòng 26] "SHA-256"===d)r=64,w=1,F=Number,t=P,x=Q,u=R,z=N,A=O,B=L,C=M,E=K,D=J,G=H;else throw Error("Unexpected error in SHA-2 implementation");d=a[0];l=a[1];b=a[2];g=a[3];f=a[4];n=a[5];k=a[6];e=a[7];for(p=
  20. [Dòng 29] "function"===typeof define

!== (8 điều kiện):
  1. [Dòng 12] 'use strict';(function(I){function w(c,a,d){var l=0,b=[],g=0,f,n,k,e,h,q,y,p,m=!1,t=[],r=[],u,z=!1;d=d||{};f=d.encoding||"UTF8";u=d.numRounds||1;if(u!==parseInt(u,10)
  2. [Dòng 18] l+=1)g[l]=c[l>>>2]>>>8*(3+l%4*-1)&255;return b}function C(c){var a={outputUpper:!1,b64Pad:"=",shakeLen:-1};c=c||{};a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");
  3. [Dòng 19] if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0;d<f;d+=2){k=parseInt(a.substr(d,2),16);if(isNaN(k))throw Error("String of HEX type contains invalid characters");
  4. [Dòng 19] if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0
  5. [Dòng 21] "UTF16LE"!==a
  6. [Dòng 22] "");if(-1!==k
  7. [Dòng 29] define.amd?define(function(){return w}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=w),exports=w):I.jsSHA=w})(this);
  8. [Dòng 29] return w}):"undefined"!==typeof exports?("undefined"!==typeof module

================================================================================

📁 FILE 136: slick.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/assets/libraries/slick/slick.js
📊 Thống kê: 182 điều kiện duy nhất
   - === : 126 lần
   - == : 5 lần
   - !== : 47 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (126 điều kiện):
  1. [Dòng 20] if (typeof define === 'function'
  2. [Dòng 206] if (typeof(index) === 'boolean') {
  3. [Dòng 215] if (typeof(index) === 'number') {
  4. [Dòng 216] if (index === 0
  5. [Dòng 216] _.$slides.length === 0) {
  6. [Dòng 224] if (addBefore === true) {
  7. [Dòng 249] if (_.options.slidesToShow === 1
  8. [Dòng 249] _.options.adaptiveHeight === true
  9. [Dòng 249] _.options.vertical === false) {
  10. [Dòng 264] if (_.options.rtl === true
  11. [Dòng 267] if (_.transformsEnabled === false) {
  12. [Dòng 268] if (_.options.vertical === false) {
  13. [Dòng 280] if (_.cssTransitions === false) {
  14. [Dòng 281] if (_.options.rtl === true) {
  15. [Dòng 355] typeof asNavFor === 'object' ) {
  16. [Dòng 371] if (_.options.fade === false) {
  17. [Dòng 414] if ( _.options.infinite === false ) {
  18. [Dòng 416] if ( _.direction === 1
  19. [Dòng 416] ( _.currentSlide + 1 ) === ( _.slideCount - 1 )) {
  20. [Dòng 420] else if ( _.direction === 0 ) {
  21. [Dòng 424] if ( _.currentSlide - 1 === 0 ) {
  22. [Dòng 442] if (_.options.arrows === true ) {
  23. [Dòng 487] if (_.options.dots === true
  24. [Dòng 524] _.$slideTrack = (_.slideCount === 0) ?
  25. [Dòng 532] if (_.options.centerMode === true
  26. [Dòng 532] _.options.swipeToSlide === true) {
  27. [Dòng 547] _.setSlideClasses(typeof _.currentSlide === 'number' ? _.currentSlide : 0);
  28. [Dòng 549] if (_.options.draggable === true) {
  29. [Dòng 602] if (_.respondTo === 'window') {
  30. [Dòng 604] } else if (_.respondTo === 'slider') {
  31. [Dòng 606] } else if (_.respondTo === 'min') {
  32. [Dòng 618] if (_.originalSettings.mobileFirst === false) {
  33. [Dòng 635] if (_.breakpointSettings[targetBreakpoint] === 'unslick') {
  34. [Dòng 641] if (initial === true) {
  35. [Dòng 705] slideOffset = indexOffset === 0 ? _.options.slidesToScroll : _.options.slidesToShow - indexOffset
  36. [Dòng 712] slideOffset = indexOffset === 0 ? _.options.slidesToScroll : indexOffset
  37. [Dòng 719] var index = event.data.index === 0 ? 0 :
  38. [Dòng 765] if (_.options.accessibility === true) {
  39. [Dòng 772] if (_.options.arrows === true
  40. [Dòng 797] if (_.options.focusOnSelect === true) {
  41. [Dòng 836] if (_.shouldClick === false) {
  42. [Dòng 1051] if (_.options.infinite === true) {
  43. [Dòng 1061] } else if (_.options.centerMode === true) {
  44. [Dòng 1094] if (_.options.vertical === true
  45. [Dòng 1094] _.options.centerMode === true) {
  46. [Dòng 1095] if (_.options.slidesToShow === 2) {
  47. [Dòng 1097] } else if (_.options.slidesToShow === 1) {
  48. [Dòng 1128] } else if (_.options.centerMode === true
  49. [Dòng 1128] _.options.infinite === true) {
  50. [Dòng 1141] if (_.options.variableWidth === true) {
  51. [Dòng 1143] _.options.infinite === false) {
  52. [Dòng 1159] if (_.options.centerMode === true) {
  53. [Dòng 1200] if (_.options.infinite === false) {
  54. [Dòng 1229] centerOffset = _.options.centerMode === true ? _.slideWidth * Math.floor(_.options.slidesToShow / 2) : 0
  55. [Dòng 1231] if (_.options.swipeToSlide === true) {
  56. [Dòng 1406] _.options.pauseOnDotsHover === true
  57. [Dòng 1498] if (event.keyCode === 37
  58. [Dòng 1498] _.options.accessibility === true) {
  59. [Dòng 1501] message: _.options.rtl === true ? 'next' :
  60. [Dòng 1504] } else if (event.keyCode === 39
  61. [Dòng 1507] message: _.options.rtl === true ? 'previous' : 'next'
  62. [Dòng 1585] if (_.options.fade === true) {
  63. [Dòng 1593] if (_.options.lazyLoad === 'anticipated') {
  64. [Dòng 1616] } else if (_.currentSlide === 0) {
  65. [Dòng 1637] if (_.options.lazyLoad === 'progressive') {
  66. [Dòng 1773] if ( _.options.adaptiveHeight === true ) {
  67. [Dòng 1864] if ( $.type(responsiveSettings) === 'array'
  68. [Dòng 1878] _.breakpoints[l] === currentBreakpoint ) {
  69. [Dòng 1969] index = removeBefore === true ? 0 : _.slideCount - 1
  70. [Dòng 1971] index = removeBefore === true ? --index : index
  71. [Dòng 1980] if (removeAll === true) {
  72. [Dòng 2050] if (_.options.vertical === false
  73. [Dòng 2050] _.options.variableWidth === false) {
  74. [Dòng 2054] } else if (_.options.variableWidth === true) {
  75. [Dòng 2062] if (_.options.variableWidth === false) _.$slideTrack.children('.slick-slide').width(_.slideWidth - offset);
  76. [Dòng 2128] if( $.type( arguments[0] ) === 'object' ) {
  77. [Dòng 2134] } else if ( $.type( arguments[0] ) === 'string' ) {
  78. [Dòng 2140] if ( arguments[0] === 'responsive'
  79. [Dòng 2140] $.type( arguments[1] ) === 'array' ) {
  80. [Dòng 2152] if ( type === 'single' ) {
  81. [Dòng 2157] } else if ( type === 'multiple' ) {
  82. [Dòng 2166] } else if ( type === 'responsive' ) {
  83. [Dòng 2181] if( _.options.responsive[l].breakpoint === value[item].breakpoint ) {
  84. [Dòng 2231] _.positionProp = _.options.vertical === true ? 'top' : 'left'
  85. [Dòng 2233] if (_.positionProp === 'top') {
  86. [Dòng 2242] if (_.options.useCSS === true) {
  87. [Dòng 2248] if ( typeof _.options.zIndex === 'number' ) {
  88. [Dòng 2261] if (bodyStyle.perspectiveProperty === undefined
  89. [Dòng 2261] bodyStyle.webkitPerspective === undefined) _.animType = false;
  90. [Dòng 2267] bodyStyle.MozPerspective === undefined) _.animType = false;
  91. [Dòng 2279] if (bodyStyle.msTransform === undefined) _.animType = false;
  92. [Dòng 2306] var evenCoef = _.options.slidesToShow % 2 === 0 ? 1 : 0
  93. [Dòng 2328] if (index === 0) {
  94. [Dòng 2334] } else if (index === _.slideCount - 1) {
  95. [Dòng 2366] indexOffset = _.options.infinite === true ? _.options.slidesToShow + index : index
  96. [Dòng 2388] if (_.options.lazyLoad === 'ondemand'
  97. [Dòng 2388] _.options.lazyLoad === 'anticipated') {
  98. [Dòng 2402] if (_.options.infinite === true
  99. [Dòng 2402] _.options.fade === false) {
  100. [Dòng 2479] if (_.animating === true
  101. [Dòng 2479] _.options.waitForAnimate === true) {
  102. [Dòng 2483] if (_.options.fade === true
  103. [Dòng 2483] _.currentSlide === index) {
  104. [Dòng 2487] if (sync === false) {
  105. [Dòng 2495] _.currentLeft = _.swipeLeft === null ? slideLeft : _.swipeLeft
  106. [Dòng 2497] if (_.options.infinite === false
  107. [Dòng 2497] _.options.centerMode === false
  108. [Dòng 2509] } else if (_.options.infinite === false
  109. [Dòng 2509] _.options.centerMode === true
  110. [Dòng 2627] return (_.options.rtl === false ? 'left' : 'right');
  111. [Dòng 2633] return (_.options.rtl === false ? 'right' : 'left');
  112. [Dòng 2635] if (_.options.verticalSwiping === true) {
  113. [Dòng 2664] if ( _.touchObject.curX === undefined ) {
  114. [Dòng 2668] if ( _.touchObject.edgeHit === true ) {
  115. [Dòng 2732] if ((_.options.swipe === false) || ('ontouchend' in document
  116. [Dòng 2732] _.options.swipe === false)) {
  117. [Dòng 2734] } else if (_.options.draggable === false
  118. [Dòng 2806] positionOffset = (_.options.rtl === false ? 1 : -1) * (_.touchObject.curX > _.touchObject.startX ? 1 : -1);
  119. [Dòng 2817] if ((_.currentSlide === 0
  120. [Dòng 2817] swipeDirection === 'right') || (_.currentSlide >= _.getDotCount()
  121. [Dòng 2817] swipeDirection === 'left')) {
  122. [Dòng 2832] _.options.touchMove === false) {
  123. [Dòng 2836] if (_.animating === true) {
  124. [Dòng 2926] if ( _.options.arrows === true
  125. [Dòng 2933] if (_.currentSlide === 0) {
  126. [Dòng 2938] _.options.centerMode === false) {

== (5 điều kiện):
  1. [Dòng 2007] x = _.positionProp == 'left' ? Math.ceil(position) + 'px' : '0px'
  2. [Dòng 2008] y = _.positionProp == 'top' ? Math.ceil(position) + 'px' : '0px'
  3. [Dòng 2368] if (_.options.slidesToShow == _.options.slidesToScroll
  4. [Dòng 3002] if (typeof opt == 'object'
  5. [Dòng 3002] typeof opt == 'undefined')

!== (47 điều kiện):
  1. [Dòng 22] } else if (typeof exports !== 'undefined') {
  2. [Dòng 155] if (typeof document.mozHidden !== 'undefined') {
  3. [Dòng 158] } else if (typeof document.webkitHidden !== 'undefined') {
  4. [Dòng 342] asNavFor !== null ) {
  5. [Dòng 355] if ( asNavFor !== null
  6. [Dòng 460] if (_.options.infinite !== true) {
  7. [Dòng 612] _.options.responsive !== null) {
  8. [Dòng 630] if (targetBreakpoint !== null) {
  9. [Dòng 631] if (_.activeBreakpoint !== null) {
  10. [Dòng 632] if (targetBreakpoint !== _.activeBreakpoint
  11. [Dòng 676] triggerBreakpoint !== false ) {
  12. [Dòng 699] unevenOffset = (_.slideCount % _.options.slidesToScroll !== 0);
  13. [Dòng 758] _.$dots !== null) {
  14. [Dòng 997] if (filter !== null) {
  15. [Dòng 1103] if (_.slideCount % _.options.slidesToScroll !== 0) {
  16. [Dòng 1314] if (_.$dots !== null) {
  17. [Dòng 1324] if (slideControlIndex !== -1) {
  18. [Dòng 1910] _.currentSlide !== 0) {
  19. [Dòng 1953] if ($(window).width() !== _.windowWidth) {
  20. [Dòng 2144] } else if ( typeof arguments[1] !== 'undefined' ) {
  21. [Dòng 2170] if( $.type( _.options.responsive ) !== 'array' ) {
  22. [Dòng 2239] if (bodyStyle.WebkitTransition !== undefined
  23. [Dòng 2240] bodyStyle.MozTransition !== undefined
  24. [Dòng 2241] bodyStyle.msTransition !== undefined) {
  25. [Dòng 2257] if (bodyStyle.OTransform !== undefined) {
  26. [Dòng 2263] if (bodyStyle.MozTransform !== undefined) {
  27. [Dòng 2269] if (bodyStyle.webkitTransform !== undefined) {
  28. [Dòng 2275] if (bodyStyle.msTransform !== undefined) {
  29. [Dòng 2281] if (bodyStyle.transform !== undefined
  30. [Dòng 2281] _.animType !== false) {
  31. [Dòng 2286] _.transformsEnabled = _.options.useTransform && (_.animType !== null
  32. [Dòng 2286] _.animType !== false);
  33. [Dòng 2500] if (dontAnimate !== true
  34. [Dòng 2567] if (dontAnimate !== true) {
  35. [Dòng 2717] if ( _.touchObject.startX !== _.touchObject.curX ) {
  36. [Dòng 2734] event.type.indexOf('mouse') !== -1) {
  37. [Dòng 2738] event.originalEvent.touches !== undefined ?
  38. [Dòng 2773] touches = event.originalEvent !== undefined ? event.originalEvent.touches : null
  39. [Dòng 2775] touches.length !== 1) {
  40. [Dòng 2781] _.touchObject.curX = touches !== undefined ? touches[0].pageX : event.clientX
  41. [Dòng 2782] _.touchObject.curY = touches !== undefined ? touches[0].pageY : event.clientY
  42. [Dòng 2801] if (event.originalEvent !== undefined
  43. [Dòng 2852] if (_.touchObject.fingerCount !== 1
  44. [Dòng 2857] event.originalEvent.touches !== undefined) {
  45. [Dòng 2861] _.touchObject.startX = _.touchObject.curX = touches !== undefined ? touches.pageX : event.clientX
  46. [Dòng 2862] _.touchObject.startY = _.touchObject.curY = touches !== undefined ? touches.pageY : event.clientY
  47. [Dòng 2872] if (_.$slidesCache !== null) {

!= (4 điều kiện):
  1. [Dòng 805] $('[draggable!=true]', _.$slideTrack).off('dragstart', _.preventDefault);
  2. [Dòng 1467] $('[draggable!=true]', _.$slideTrack).on('dragstart', _.preventDefault);
  3. [Dòng 2707] if( direction != 'vertical' ) {
  4. [Dòng 3006] if (typeof ret != 'undefined') return ret;

================================================================================

📁 FILE 137: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 138: atm_b1.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_1/assets/js/atm_b1.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 228] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 234] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 258] if (paymentState === "authorization_required") {
  12. [Dòng 264] if (method === "REDIRECT") {
  13. [Dòng 267] } else if (method === "POST_REDIRECT") {
  14. [Dòng 306] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 289] if (jLinks !== undefined
  3. [Dòng 289] jLinks !== null) {
  4. [Dòng 291] if (jMerchantReturn !== undefined
  5. [Dòng 291] jMerchantReturn !== null) {
  6. [Dòng 306] if (responseCode !== undefined
  7. [Dòng 306] responseCode !== null
  8. [Dòng 334] if (parentRes !== "{

================================================================================

📁 FILE 139: atm_b1_2.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_1/atm_b1_2.js
📊 Thống kê: 11 điều kiện duy nhất
   - === : 8 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 52] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 55] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 64] year === y
  5. [Dòng 66] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 75] if ("PAY" === operation) {
  7. [Dòng 319] } else if (value === "") {
  8. [Dòng 336] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 77] if (e !== null) {
  2. [Dòng 253] if (lang !== "vi") lang = "en";
  3. [Dòng 283] if (value !== "") {

================================================================================

📁 FILE 140: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_1/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 141: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 142: atm_b2.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_2/assets/js/atm_b2.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 231] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 236] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 252] if (invoiceState === "unpaid"
  10. [Dòng 252] invoiceState === "not_paid") {
  11. [Dòng 256] if (paymentState === "authorization_required") {
  12. [Dòng 260] if (method === "REDIRECT") {
  13. [Dòng 263] } else if (method === "POST_REDIRECT") {
  14. [Dòng 302] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 285] if (jLinks !== undefined
  3. [Dòng 285] jLinks !== null) {
  4. [Dòng 287] if (jMerchantReturn !== undefined
  5. [Dòng 287] jMerchantReturn !== null) {
  6. [Dòng 302] if (responseCode !== undefined
  7. [Dòng 302] responseCode !== null
  8. [Dòng 330] if (parentRes !== "{

================================================================================

📁 FILE 143: atm_b2_2.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_2/atm_b2_2.js
📊 Thống kê: 6 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 41] if (inName.value === "") return err("MISSING_FIELD"
  3. [Dòng 51] if ("PAY" === operation) {

!== (3 điều kiện):
  1. [Dòng 53] if (e !== null) {
  2. [Dòng 229] if (lang !== "vi") lang = "en";
  3. [Dòng 259] if (value !== "") {

================================================================================

📁 FILE 144: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/atm_bank_2/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 145: common.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/common.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 42] if (i % 2 === parity) d *= 2;
  2. [Dòng 46] return (sum % 10) === 0
  3. [Dòng 116] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 119] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 205] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 211] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 227] if (xhr.status === 200
  8. [Dòng 227] xhr.status === 201) {
  9. [Dòng 230] if (invoiceState === "unpaid"
  10. [Dòng 230] invoiceState === "not_paid") {
  11. [Dòng 235] if (paymentState === "authorization_required") {
  12. [Dòng 241] if (method === "REDIRECT") {
  13. [Dòng 244] } else if (method === "POST_REDIRECT") {
  14. [Dòng 283] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 94] this.oldValue !== this.value) {
  2. [Dòng 266] if (jLinks !== undefined
  3. [Dòng 266] jLinks !== null) {
  4. [Dòng 268] if (jMerchantReturn !== undefined
  5. [Dòng 268] jMerchantReturn !== null) {
  6. [Dòng 283] if (responseCode !== undefined
  7. [Dòng 283] responseCode !== null
  8. [Dòng 311] if (parentRes !== "{

================================================================================

📁 FILE 146: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 147: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/international_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 148: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/begodi/international_card/script.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 12] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 22] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 25] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 37] year === y
  5. [Dòng 39] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 70] if ("PAY" === operation) {
  7. [Dòng 144] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 72] if (e !== null) {
  2. [Dòng 117] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 149: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/iframe/index.html
📊 Thống kê: 35 điều kiện duy nhất
   - === : 21 lần
   - == : 0 lần
   - !== : 14 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (21 điều kiện):
  1. [Dòng 10] if ((cardno.length === 15
  2. [Dòng 10] cardno.length === 16
  3. [Dòng 10] cardno.length === 19) && isMode10(cardno) === true) {
  4. [Dòng 10] isMode10(cardno) === true) {
  5. [Dòng 29] if (i % 2 === parity) d *= 2;
  6. [Dòng 33] return (sum % 10) === 0
  7. [Dòng 54] if ("PAY" === operation) {
  8. [Dòng 75] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  9. [Dòng 81] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  10. [Dòng 97] if (xhr.status === 200
  11. [Dòng 97] xhr.status === 201) {
  12. [Dòng 100] if (invoiceState === "unpaid"
  13. [Dòng 100] invoiceState === "not_paid") {
  14. [Dòng 105] if (paymentState === "authorization_required") {
  15. [Dòng 111] if (method === "REDIRECT") {
  16. [Dòng 116] } else if (method === "POST_REDIRECT") {
  17. [Dòng 183] //Customizable =================================================================================================
  18. [Dòng 229] if (typeof queryParams[key] === "undefined") {
  19. [Dòng 232] } else if (typeof queryParams[key] === "string") {
  20. [Dòng 246] if (params["CardList"] === undefined
  21. [Dòng 246] params["CardList"] === null) return;

!== (14 điều kiện):
  1. [Dòng 40] //if (event.origin !== "http://example.com:8080") return;
  2. [Dòng 58] if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
  3. [Dòng 59] if (inNumber.style.display !== "none") instrument.number = inNumber.value;
  4. [Dòng 60] if (inDate.style.display !== "none") instrument.date = inDate.value;
  5. [Dòng 61] if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
  6. [Dòng 62] if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
  7. [Dòng 63] if (inName.style.display !== "none") instrument.name = inName.value;
  8. [Dòng 78] /*if (contentType !== my_expected_type) {
  9. [Dòng 138] if (jLinks !== undefined
  10. [Dòng 138] jLinks !== null) {
  11. [Dòng 140] if (jMerchantReturn !== undefined
  12. [Dòng 140] jMerchantReturn !== null) {
  13. [Dòng 172] if (parentRes !== "{
  14. [Dòng 245] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 150: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 151: karma.conf.js
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/karma.conf.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 152: main.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/main.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 153: polyfills.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/polyfills.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 154: test.ts
📍 Đường dẫn: /root/projects/onepay/paygate-vinfast-web/src/test.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📋 TÓM TẮT TẤT CẢ ĐIỀU KIỆN DUY NHẤT:
====================================================================================================

=== (458 điều kiện duy nhất):
------------------------------------------------------------
1. return lang === this._translate.currentLang
2. isPopupSupport === 'True') || (rePayment
3. isPopupSupport === 'True')" class="left" [class.select_only]="!(rePayment
4. isPopupSupport === 'True')">
5. params.timeout === 'true') {
6. this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
7. _re.body.state === 'unpaid');
8. if (this.errorCode === 'overtime'
9. this.errorCode === '253') {
10. params.name === 'CUSTOMER_INTIME'
11. params.code === '09') {
12. if (this.timeLeft === 0) {
13. if (YY % 400 === 0
14. YY % 4 === 0)) {
15. if (YYYY % 400 === 0
16. YYYY % 4 === 0)) {
17. params.name === 'CUSTOMER_INTIME')) {
18. <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
19. [class.red_border_no_background]="c_expdate && (valueDate.length === 0
20. valueDate.trim().length === 0)"
21. if (isIE[0] === 'MSIE'
22. +isIE[1] === 10) {
23. if ((_val.value.substr(-1) === ' '
24. _val.value.length === 24) {
25. if (this.cardTypeBank === 'bank_card_number') {
26. } else if (this.cardTypeBank === 'bank_account_number') {
27. } else if (this.cardTypeBank === 'bank_username') {
28. } else if (this.cardTypeBank === 'bank_customer_code') {
29. this.cardTypeBank === 'bank_card_number'
30. if (this.cardTypeOcean === 'IB') {
31. } else if (this.cardTypeOcean === 'MB') {
32. if (_val.value.substr(0, 2) === '84') {
33. } else if (this.cardTypeOcean === 'ATM') {
34. if (this.cardTypeBank === 'bank_account_number') {
35. this.cardTypeBank === 'bank_card_number') {
36. if (this.cardTypeBank === 'bank_card_number'
37. if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
38. if (event.keyCode === 8
39. event.key === "Backspace"
40. if (v.length === 2
41. this.flag.length === 3
42. this.flag.charAt(this.flag.length - 1) === '/') {
43. if (v.length === 1) {
44. } else if (v.length === 2) {
45. v.length === 2) {
46. if (len === 2) {
47. if ((this.cardTypeBank === 'bank_account_number'
48. this.cardTypeBank === 'bank_username'
49. this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
50. this.cardTypeOcean === 'ATM')
51. || (this.cardTypeOcean === 'IB'
52. if (valIn === this._translate.instant('bank_card_number')) {
53. } else if (valIn === this._translate.instant('bank_account_number')) {
54. } else if (valIn === this._translate.instant('bank_username')) {
55. } else if (valIn === this._translate.instant('bank_customer_code')) {
56. if (_val.value === ''
57. _val.value === null
58. _val.value === undefined) {
59. if (_val.value && (this.cardTypeBank === 'bank_card_number'
60. if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
61. this.cardTypeOcean === 'MB') {
62. this.cardTypeOcean === 'IB'
63. if ((this.cardTypeBank === 'bank_card_number'
64. if (this.cardName === undefined
65. this.cardName === '') {
66. if (this.valueDate === undefined
67. this.valueDate === '') {
68. c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
69. _inExpDate.trim().length === 0)"
70. if (approval.method === 'REDIRECT') {
71. } else if (approval.method === 'POST_REDIRECT') {
72. filteredData.length === 0"
73. if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
74. filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
75. if (valOut === 'auth') {
76. if (this._b === '1'
77. this._b === '20'
78. this._b === '64') {
79. if (this._b === '36'
80. this._b === '18'
81. this._b === '19'
82. if (this._b === '19'
83. this._b === '16'
84. this._b === '25'
85. this._b === '33'
86. this._b === '39'
87. this._b === '9'
88. this._b === '11'
89. this._b === '17'
90. this._b === '36'
91. this._b === '44'
92. this._b === '12'
93. this._b === '64'
94. if (this._b === '20'
95. if (this._b === '12'
96. this._b === '18') {
97. if (_formCard.country === 'default') {
98. if ((v.substr(-1) === ' '
99. if (deviceValue === 'CA'
100. deviceValue === 'US') {
101. this.c_country = _val.value === 'default'
102. this.selectedCardType = this.listCardTypes.find(({ name }) => name === 'AMEX');
103. this.selectedCardType = this.listCardTypes.find(({ name }) => name === 'Mastercard');
104. this.selectedCardType = this.listCardTypes.find(({ name }) => name === 'Visa');
105. this.selectedCardType = this.listCardTypes.find(({ name }) => name === 'JCB');
106. this.selectedCardType = this.listCardTypes.find(({ name }) => name === 'UnionPay');
107. [ngStyle]="{'border-color': type === 2 ? this.themeConfig.border_color : border_color}"
108. this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_bnpl === 1
109. if (this._res.state === 'unpaid'
110. this._res.state === 'not_paid') {
111. if ('op' === auth
112. } else if ('bank' === auth
113. if (this.timeLeftPaypal === 0) {
114. return id === 'amex' ? '1234' : '123'
115. const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
116. item.method === method) : null;
117. if (M[1] === 'Chrome') {
118. if (param === key) {
119. pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
120. target === 0
121. if (target.tagName === 'A'
122. if (typeof define === 'function'
123. } else if (typeof exports === 'object') {
124. if (number === "") return err("MISSING_FIELD"
125. if (inName.value === "") return err("MISSING_FIELD"
126. if ("PAY" === operation) {
127. if (i % 2 === parity) d *= 2;
128. return (sum % 10) === 0
129. if (typeof queryParams[key] === "undefined") {
130. } else if (typeof queryParams[key] === "string") {
131. if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
132. } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
133. if (xhr.status === 200
134. xhr.status === 201) {
135. if (invoiceState === "unpaid"
136. invoiceState === "not_paid") {
137. if (paymentState === "authorization_required") {
138. if (method === "REDIRECT") {
139. } else if (method === "POST_REDIRECT") {
140. responseCode === "0") {
141. if (inMonth.value === "") return err("MISSING_FIELD"
142. if (inYear.value === "") return err("MISSING_FIELD"
143. year === y
144. if (inPhone.value === "") return err("MISSING_FIELD"
145. } else if (value === "") {
146. if (trPhone.style.display === "") {
147. } else if (trName.style.display === "") {
148. if (trName.style.display === "") {
149. if (xhr.status === 200) {
150. if (insType === "card") {
151. if (insBrandId === "visa"
152. insBrandId === "mastercard"
153. insBrandId === "amex"
154. insBrandId === "jcb"
155. insBrandId === "cup") {
156. } else if (insBrandId === "atm") {
157. } else if (insType === "dongabank_account") {
158. } else if (insType === "techcombank_account") {
159. } else if (insType === "vib_account") {
160. } else if (insType === "bidv_account") {
161. } else if (insType === "tpbank_account") {
162. } else if (insType === "shb_account") {
163. } else if (insType === "shb_customer_id") {
164. } else if (insType === "vpbank_account") {
165. } else if (insType === "oceanbank_online_account") {
166. } else if (insType === "oceanbank_mobile_account") {
167. } else if (insType === "pvcombank_account") {
168. if (inCvv.value === "") return err("MISSING_FIELD"
169. if (inCvv.value === "") {
170. if ((cardno.length === 15
171. cardno.length === 16
172. cardno.length === 19) && isMode10(cardno) === true) {
173. isMode10(cardno) === true) {
174. if (params["CardList"] === undefined
175. params["CardList"] === null) return;
176. if (insSwiftCode === "BFTVVNVX") { //Vietcombank
177. } else if (insSwiftCode === "BIDVVNVX") { //BIDV
178. typeof exports === 'object'
179. typeof define === 'function'
180. selector === '#') {
181. if (typeof element.getRootNode === 'function') {
182. if (typeof $ === 'undefined') {
183. version[0] === minMajor
184. version[1] === minMinor
185. if (config === 'close') {
186. if (input.type === 'radio') {
187. } else if (input.type === 'checkbox') {
188. if (this._element.tagName === 'LABEL'
189. input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
190. if (config === 'toggle') {
191. if (_button.getAttribute('aria-pressed') === 'true') {
192. if (activeIndex === index) {
193. if (this._config.pause === 'hover') {
194. if (_this3._config.pause === 'hover') {
195. var isNextDirection = direction === Direction.NEXT
196. var isPrevDirection = direction === Direction.PREV
197. activeIndex === 0
198. activeIndex === lastItemIndex
199. var delta = direction === Direction.PREV ? -1 : 1
200. return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
201. if (direction === Direction.NEXT) {
202. if (typeof config === 'object') {
203. var action = typeof config === 'string' ? config : _config.slide
204. if (typeof config === 'number') {
205. } else if (typeof action === 'string') {
206. if (typeof data[action] === 'undefined') {
207. return foundElem === element
208. if (typeof _this._config.parent === 'string') {
209. return elem.getAttribute('data-parent') === _this._config.parent
210. if (actives.length === 0) {
211. typeof config === 'object'
212. if (typeof config === 'string') {
213. if (typeof data[config] === 'undefined') {
214. if (event.currentTarget.tagName === 'A') {
215. if (usePopper === void 0) {
216. if (typeof Popper === 'undefined') {
217. if (this._config.reference === 'parent') {
218. $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
219. if (typeof this._config.offset === 'function') {
220. if (this._config.display === 'static') {
221. var _config = typeof config === 'object' ? config : null
222. if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
223. event.type === 'keyup'
224. event.type === 'click') {
225. if (event && (event.type === 'click'
226. event.which === TAB_KEYCODE) && $.contains(parent
227. if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
228. event.which === ESCAPE_KEYCODE) {
229. if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
230. event.which === SPACE_KEYCODE)) {
231. if (event.which === ESCAPE_KEYCODE) {
232. if (items.length === 0) {
233. if (event.which === ARROW_UP_KEYCODE
234. if (event.which === ARROW_DOWN_KEYCODE
235. if (this._config.backdrop === 'static') {
236. $(_this5._element).has(event.target).length === 0) {
237. if (event.which === ESCAPE_KEYCODE$1) {
238. if (this.tagName === 'A'
239. this.tagName === 'AREA') {
240. if (unsafeHtml.length === 0) {
241. typeof sanitizeFn === 'function') {
242. if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
243. if (_ret === "continue") continue;
244. if ($(this.element).css('display') === 'none') {
245. var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
246. if (prevHoverState === HoverState.OUT) {
247. if (typeof content === 'object'
248. title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
249. if (typeof this.config.offset === 'function') {
250. if (this.config.container === false) {
251. if (trigger === 'click') {
252. var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
253. var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
254. context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
255. context._hoverState === HoverState.SHOW) {
256. if (context._hoverState === HoverState.SHOW) {
257. context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
258. if (context._hoverState === HoverState.OUT) {
259. if (typeof config.delay === 'number') {
260. if (typeof config.title === 'number') {
261. if (typeof config.content === 'number') {
262. var _config = typeof config === 'object'
263. if (typeof content === 'function') {
264. this._scrollElement = element.tagName === 'BODY' ? window : element
265. var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
266. var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
267. var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
268. return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
269. return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
270. var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
271. this._element.parentNode.nodeType === Node.ELEMENT_NODE
272. var itemSelector = listElement.nodeName === 'UL'
273. listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
274. var activeElements = container && (container.nodeName === 'UL'
275. container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
276. if (active.getAttribute('role') === 'tab') {
277. if (element.getAttribute('role') === 'tab') {
278. 1>t)throw Error("numRounds must a integer >= 1");if("SHA-1"===c)m=512,q=K,u=Z,f=160,r=function(a){return a.slice()};else if(0===c.lastIndexOf("SHA-",0))if(q=function(a,b){return L(a,b,c)
279. c)},u=function(a,b,h,e){var k,f;if("SHA-224"===c
280. "SHA-256"===c)k=(b+65>>>9<<4)+15,f=16;else if("SHA-384"===c
281. "SHA-512"===c)k=(b+129>>>10<<
282. c);if("SHA-224"===c)a=[e[0],e[1],e[2],e[3],e[4],e[5],e[6]];else if("SHA-256"===c)a=e;else if("SHA-384"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,e[4].b,e[5].a,e[5].b];else if("SHA-512"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,
283. "SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)
284. 0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
285. return a},r=function(a){return a.slice()},"SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)||0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
286. c)m=1152,f=224;else if("SHA3-256"===c)m=1088,f=256;else if("SHA3-384"===c)m=832,f=384;else if("SHA3-512"===c)m=576,f=512;else if("SHAKE128"===c)m=1344,f=-1,F=31,z=!0;else if("SHAKE256"===c)m=1088,f=-1,F=31,z=!0;else throw Error("Chosen SHA variant is not supported");u=function(a
287. 0===64*l%e
288. 5][l/5|0];g.push(a.b);if(32*g.length>=h)break;g.push(a.a);l+=1;0===64*l%e&&D(null,b)}return g}}else throw Error("Chosen SHA variant is not supported");d=M(a,g,x);l=A(c);this.setHMACKey=function(a,b,h){var k;if(!0===I)throw Error("HMAC key already set");if(!0===y)throw Error("Cannot set HMAC key after calling update");if(!0===z)throw Error("SHAKE is not supported for HMAC");g=(h
289. f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
290. )b.push(0);b[h]&=4294967040}for(a=0;a<=h;a+=1)v[a]=b[a]^909522486,w[a]=b[a]^1549556828;l=q(v,l);e=m;I=!0};this.update=function(a){var c,b,k,f=0,g=m>>>5;c=d(a,h,n);a=c.binLen;b=c.value;c=a>>>5;for(k=0;k<c;k+=g)f+m<=a&&(l=q(b.slice(k,k+g),l),f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
291. for(g=1;g<t;g+=1)!0===z
292. f);return k(m)};this.getHMAC=function(a,b){var k,g,d,p;if(!1===I)throw Error("Cannot call getHMAC without first setting HMAC key");d=N(b);switch(a){case "HEX":k=function(a){return O(a,f,x,d)};break;case "B64":k=function(a){return P(a,f,x,d)};break;case "BYTES":k=function(a){return Q(a,f,x)};break;case "ARRAYBUFFER":try{k=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}k=function(a){return R(a,f,x)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
293. d=-1===b?3:0
294. f=-1===b?3:0
295. g=-1===b?3:0
296. !0===c.hasOwnProperty("b64Pad")
297. a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");
298. u=-1===b?3:0
299. l+=2){p=parseInt(a.substr(l,2),16);if(isNaN(p))throw Error("String of HEX type contains invalid characters");m=(l>>>1)+q;for(f=m>>>2;c.length<=f;)c.push(0);c[f]|=p<<8*(u+m%4*b)}return{value:c,binLen:4*g+d}};break;case "TEXT":c=function(c,h,d){var g,l,p=0,f,m,q,u,r,t;h=h||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(t=-1===
300. m+=1){r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=l[m]<<8*(t+r%4*b);p+=1}else if("UTF16BE"===a
301. "UTF16LE"===a)for(t=-1===b?2:0
302. UTF16LE"===a
303. 1===b
304. !0===l
305. !0===l&&(m=g&255,g=m<<8|g>>>8);r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=g<<8*(t+r%4*b);p+=2}return{value:h,binLen:8*p+d}};break;case "B64":c=function(a,c,d){var g=0,l,p,f,m,q,u,r,t;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");p=a.indexOf("=");a=a.replace(/\=/g
306. t=-1===b?3:0
307. q=-1===b?3:0
308. m=-1===b?3:0
309. c.b^a.b)}function A(c){var a=[],d;if("SHA-1"===c)a=[1732584193,4023233417,2562383102,271733878,3285377520];else if(0===c.lastIndexOf("SHA-",0))switch(a=
310. new b(d[2],4271175723),new b(d[3],1595750129),new b(d[4],2917565137),new b(d[5],725511199),new b(d[6],4215389547),new b(d[7],327033209)];break;default:throw Error("Unknown SHA variant");}else if(0===c.lastIndexOf("SHA3-",0)
311. 0===c.lastIndexOf("SHAKE",0))for(c=0
312. a,k){var e,h,n,g,l,p,f,m,q,u,r,t,v,w,y,A,z,x,F,B,C,D,E=[],J;if("SHA-224"===k
313. "SHA-256"===k)u=64,t=1,D=Number,v=G,w=la,y=H,A=ha,z=ja,x=da,F=fa,C=U,B=aa,J=d;else if("SHA-384"===k
314. "SHA-512"===k)u=80,t=2,D=b,v=ma,w=na,y=oa,A=ia,z=ka,x=ea,F=ga,C=ca,B=ba,J=V;else throw Error("Unexpected error in SHA-2 implementation");k=a[0];e=a[1];h=a[2];n=a[3];g=a[4];l=a[5];p=a[6];f=a[7];for(r=0
315. "function"===typeof define
316. 1>u)throw Error("numRounds must a integer >= 1");if(0===c.lastIndexOf("SHA-",0))if(q=function(b,a){return A(b,a,c)
317. c)},y=function(b,a,l,f){var g,e;if("SHA-224"===c
318. "SHA-256"===c)g=(a+65>>>9<<4)+15,e=16;else throw Error("Unexpected error in SHA-2 implementation");for(
319. c);if("SHA-224"===c)b=[f[0],f[1],f[2],f[3],f[4],f[5],f[6]];else if("SHA-256"===c)b=f;else throw Error("Unexpected error in SHA-2 implementation");return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
320. "SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a
321. return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
322. if(!0===z)throw Error("Cannot set HMAC key after calling update");f=(g
323. 5);g=a%h;z=!0};this.getHash=function(a,f){var d,h,k,q;if(!0===m)throw Error("Cannot call getHash after setting HMAC key");k=C(f);switch(a){case "HEX":d=function(a){return D(a,e,k)};break;case "B64":d=function(a){return E(a,e,k)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{h=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("format must be HEX, B64, BYTES, or ARRAYBUFFER");
324. x(c));return d(q)};this.getHMAC=function(a,f){var d,k,t,u;if(!1===m)throw Error("Cannot call getHMAC without first setting HMAC key");t=C(f);switch(a){case "HEX":d=function(a){return D(a,e,t)};break;case "B64":d=function(a){return E(a,e,t)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{d=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
325. h=(d>>>1)+q;for(e=h>>>2;b.length<=e;)b.push(0);b[e]|=k<<8*(3+h%4*-1)}return{value:b,binLen:4*f+c}};break;case "TEXT":d=function(c,b,d){var f,n,k=0,e,h,q,m,p,r;b=b||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(r=3
326. q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=n[h]<<8*(r+p%4*-1);k+=1}else if("UTF16BE"===a
327. "UTF16LE"===a)for(r=2
328. !0===n
329. e+=1){f=c.charCodeAt(e);!0===n&&(h=f&255,f=h<<8|f>>>8);p=k+q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=f<<8*(r+p%4*-1);k+=2}return{value:b,binLen:8*k+d}};break;case "B64":d=function(a,b,c){var f=0,d,k,e,h,q,m,p;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");k=a.indexOf("=");a=a.replace(/\=/g
330. 16)&65535)<<16|b&65535}function R(c,a,d,l,b){var g=(c&65535)+(a&65535)+(d&65535)+(l&65535)+(b&65535);return((c>>>16)+(a>>>16)+(d>>>16)+(l>>>16)+(b>>>16)+(g>>>16)&65535)<<16|g&65535}function x(c){var a=[],d;if(0===c.lastIndexOf("SHA-",0))switch(a=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428]
331. new m,new m,new m,new m,new m,new m];break;case "SHA-512":a=[new m,new m,new m,new m,new m,new m,new m,new m];break;default:throw Error("Unknown SHA variant");}else throw Error("No SHA variants supported");return a}function A(c,a,d){var l,b,g,f,n,k,e,h,m,r,p,w,t,x,u,z,A,B,C,D,E,F,v=[],G;if("SHA-224"===d
332. "SHA-256"===d)r=64,w=1,F=Number,t=P,x=Q,u=R,z=N,A=O,B=L,C=M,E=K,D=J,G=H;else throw Error("Unexpected error in SHA-2 implementation");d=a[0];l=a[1];b=a[2];g=a[3];f=a[4];n=a[5];k=a[6];e=a[7];for(p=
333. if (typeof(index) === 'boolean') {
334. if (typeof(index) === 'number') {
335. if (index === 0
336. _.$slides.length === 0) {
337. if (addBefore === true) {
338. if (_.options.slidesToShow === 1
339. _.options.adaptiveHeight === true
340. _.options.vertical === false) {
341. if (_.options.rtl === true
342. if (_.transformsEnabled === false) {
343. if (_.options.vertical === false) {
344. if (_.cssTransitions === false) {
345. if (_.options.rtl === true) {
346. typeof asNavFor === 'object' ) {
347. if (_.options.fade === false) {
348. if ( _.options.infinite === false ) {
349. if ( _.direction === 1
350. ( _.currentSlide + 1 ) === ( _.slideCount - 1 )) {
351. else if ( _.direction === 0 ) {
352. if ( _.currentSlide - 1 === 0 ) {
353. if (_.options.arrows === true ) {
354. if (_.options.dots === true
355. _.$slideTrack = (_.slideCount === 0) ?
356. if (_.options.centerMode === true
357. _.options.swipeToSlide === true) {
358. _.setSlideClasses(typeof _.currentSlide === 'number' ? _.currentSlide : 0);
359. if (_.options.draggable === true) {
360. if (_.respondTo === 'window') {
361. } else if (_.respondTo === 'slider') {
362. } else if (_.respondTo === 'min') {
363. if (_.originalSettings.mobileFirst === false) {
364. if (_.breakpointSettings[targetBreakpoint] === 'unslick') {
365. if (initial === true) {
366. slideOffset = indexOffset === 0 ? _.options.slidesToScroll : _.options.slidesToShow - indexOffset
367. slideOffset = indexOffset === 0 ? _.options.slidesToScroll : indexOffset
368. var index = event.data.index === 0 ? 0 :
369. if (_.options.accessibility === true) {
370. if (_.options.arrows === true
371. if (_.options.focusOnSelect === true) {
372. if (_.shouldClick === false) {
373. if (_.options.infinite === true) {
374. } else if (_.options.centerMode === true) {
375. if (_.options.vertical === true
376. _.options.centerMode === true) {
377. if (_.options.slidesToShow === 2) {
378. } else if (_.options.slidesToShow === 1) {
379. } else if (_.options.centerMode === true
380. _.options.infinite === true) {
381. if (_.options.variableWidth === true) {
382. _.options.infinite === false) {
383. if (_.options.centerMode === true) {
384. if (_.options.infinite === false) {
385. centerOffset = _.options.centerMode === true ? _.slideWidth * Math.floor(_.options.slidesToShow / 2) : 0
386. if (_.options.swipeToSlide === true) {
387. _.options.pauseOnDotsHover === true
388. if (event.keyCode === 37
389. _.options.accessibility === true) {
390. message: _.options.rtl === true ? 'next' :
391. } else if (event.keyCode === 39
392. message: _.options.rtl === true ? 'previous' : 'next'
393. if (_.options.fade === true) {
394. if (_.options.lazyLoad === 'anticipated') {
395. } else if (_.currentSlide === 0) {
396. if (_.options.lazyLoad === 'progressive') {
397. if ( _.options.adaptiveHeight === true ) {
398. if ( $.type(responsiveSettings) === 'array'
399. _.breakpoints[l] === currentBreakpoint ) {
400. index = removeBefore === true ? 0 : _.slideCount - 1
401. index = removeBefore === true ? --index : index
402. if (removeAll === true) {
403. if (_.options.vertical === false
404. _.options.variableWidth === false) {
405. } else if (_.options.variableWidth === true) {
406. if (_.options.variableWidth === false) _.$slideTrack.children('.slick-slide').width(_.slideWidth - offset);
407. if( $.type( arguments[0] ) === 'object' ) {
408. } else if ( $.type( arguments[0] ) === 'string' ) {
409. if ( arguments[0] === 'responsive'
410. $.type( arguments[1] ) === 'array' ) {
411. if ( type === 'single' ) {
412. } else if ( type === 'multiple' ) {
413. } else if ( type === 'responsive' ) {
414. if( _.options.responsive[l].breakpoint === value[item].breakpoint ) {
415. _.positionProp = _.options.vertical === true ? 'top' : 'left'
416. if (_.positionProp === 'top') {
417. if (_.options.useCSS === true) {
418. if ( typeof _.options.zIndex === 'number' ) {
419. if (bodyStyle.perspectiveProperty === undefined
420. bodyStyle.webkitPerspective === undefined) _.animType = false;
421. bodyStyle.MozPerspective === undefined) _.animType = false;
422. if (bodyStyle.msTransform === undefined) _.animType = false;
423. var evenCoef = _.options.slidesToShow % 2 === 0 ? 1 : 0
424. if (index === 0) {
425. } else if (index === _.slideCount - 1) {
426. indexOffset = _.options.infinite === true ? _.options.slidesToShow + index : index
427. if (_.options.lazyLoad === 'ondemand'
428. _.options.lazyLoad === 'anticipated') {
429. if (_.options.infinite === true
430. _.options.fade === false) {
431. if (_.animating === true
432. _.options.waitForAnimate === true) {
433. if (_.options.fade === true
434. _.currentSlide === index) {
435. if (sync === false) {
436. _.currentLeft = _.swipeLeft === null ? slideLeft : _.swipeLeft
437. if (_.options.infinite === false
438. _.options.centerMode === false
439. } else if (_.options.infinite === false
440. _.options.centerMode === true
441. return (_.options.rtl === false ? 'left' : 'right');
442. return (_.options.rtl === false ? 'right' : 'left');
443. if (_.options.verticalSwiping === true) {
444. if ( _.touchObject.curX === undefined ) {
445. if ( _.touchObject.edgeHit === true ) {
446. if ((_.options.swipe === false) || ('ontouchend' in document
447. _.options.swipe === false)) {
448. } else if (_.options.draggable === false
449. positionOffset = (_.options.rtl === false ? 1 : -1) * (_.touchObject.curX > _.touchObject.startX ? 1 : -1);
450. if ((_.currentSlide === 0
451. swipeDirection === 'right') || (_.currentSlide >= _.getDotCount()
452. swipeDirection === 'left')) {
453. _.options.touchMove === false) {
454. if (_.animating === true) {
455. if ( _.options.arrows === true
456. if (_.currentSlide === 0) {
457. _.options.centerMode === false) {
458. //Customizable =================================================================================================

== (413 điều kiện duy nhất):
------------------------------------------------------------
1. 'vi' == params['locale']) {
2. 'en' == params['locale']) {
3. errorCode == '11'"
4. <div class="footer-button" *ngIf="(isSent == false
5. <div *ngIf="(isSent == false
6. <div class="payment_again" *ngIf="rePayment && !timeOut" [class.select_only]="!(isSent == false
7. <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
8. (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
9. <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
10. !(errorCode == 'overtime' || errorCode == '253') && isBack && !invoiceNearExpire && !paymentPending
11. if (params && (params['bnpl'] == 'true')) {
12. if (params && (params['bnpl']== 'false')) {
13. if (this.res.currencies[0] == 'USD') {
14. this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'amigo') {
15. this.res.themes.theme == 'general') {
16. params.response_code == 'overtime') {
17. if (_re.status == '200'
18. _re.status == '201') {
19. if (_re2.status == '200'
20. _re2.status == '201') {
21. if (this.errorCode == 'overtime'
22. this.errorCode == '253') {
23. } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
24. this.res.state == 'canceled') {
25. if (lastPayment?.state == 'pending') {
26. if (this.isTimePause == false) {
27. screen==1"
28. screen==2"
29. if ((dataPassed.status == '200'
30. dataPassed.status == '201') && dataPassed.body != null) {
31. dataPassed.body.themes.logo_full == 'True') {
32. payments[payments.length - 1].state == 'pending'
33. payments[payments.length - 1].instrument.issuer.brand.id == 'amigo') {
34. if (this.locale == 'en') {
35. if (bankId == 3
36. bankId == 61
37. bankId == 8
38. bankId == 49
39. bankId == 48
40. bankId == 10
41. bankId == 53
42. bankId == 17
43. bankId == 65
44. bankId == 23
45. bankId == 52
46. bankId == 27
47. bankId == 66
48. bankId == 9
49. bankId == 54
50. bankId == 37
51. bankId == 38
52. bankId == 39
53. bankId == 40
54. bankId == 42
55. bankId == 44
56. bankId == 72
57. bankId == 59
58. bankId == 51
59. bankId == 64
60. bankId == 58
61. bankId == 56
62. bankId == 55
63. bankId == 60
64. bankId == 68
65. bankId == 73
66. bankId == 74
67. bankId == 75 //keb hana
68. if (this._b == 18
69. this._b == 19) {
70. if (this._b == 19) {//19BIDV
71. } else if (this._b == 3
72. this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
73. if (this._b == 27) {
74. } else if (this._b == 12) {// 12SHB
75. } else if (this._b == 18) { //18Oceanbank-ocb
76. if (this._b == 19
77. this._b == 3
78. this._b == 27
79. this._b == 12) {
80. } else if (this._b == 18) {
81. if (this.checkBin(_val.value) && (this._b == 3
82. this._b == 27)) {
83. if (this._b == 3) {
84. this.cardTypeOcean == 'ATM') {
85. if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
86. this._b == 18)) {
87. if (this.checkBin(v) && (this._b == 3
88. event.inputType == 'deleteContentBackward') {
89. if (event.target.name == 'exp_date'
90. event.inputType == 'insertCompositionText') {
91. if (((this.valueDate.length == 4
92. this.valueDate.search('/') == -1) || this.valueDate.length == 5)
93. this.valueDate.length == 5)
94. if (temp.length == 0) {
95. return (counter % 10 == 0);
96. } else if (this._b == 19) {
97. } else if (this._b == 27) {
98. if (this._b == 12) {
99. if (this.cardTypeBank == 'bank_customer_code') {
100. } else if (this.cardTypeBank == 'bank_account_number') {
101. _formCard.exp_date.length == 5
102. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
103. this._b == 3)) {//27-pvcombank;3-TPB ;
104. if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
105. this._b == 19
106. this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
107. if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
108. if (this.cardTypeOcean == 'IB') {
109. } else if (this.cardTypeOcean == 'MB') {
110. } else if (this.cardTypeOcean == 'ATM') {
111. if (this._res_post.state == 'approved'
112. this._res_post.state == 'failed') {
113. } else if (this._res_post.state == 'authorization_required') {
114. if (this._b == 18) {
115. if (this._b == 27
116. this._b == 18) {
117. if (err.status == 400
118. err.status == 500) {
119. if (err.error && (err.error.code == 13
120. err.error.code == '13')) {
121. if ((cardNo.length == 16
122. if ((cardNo.length == 16 || (cardNo.length == 19
123. && ((this._b == 18
124. cardNo.length == 19) || this._b != 18)
125. if (this._b == +e.id) {
126. if (valIn == 1) {
127. } else if (valIn == 2) {
128. this._b == 3) {
129. if (this._b == 19) {
130. if (cardType == this._translate.instant('internetbanking')
131. } else if (cardType == this._translate.instant('mobilebanking')
132. } else if (cardType == this._translate.instant('atm')
133. this._b == 18))) {
134. } else if (this._b == 18
135. this.c_expdate = !(((this.valueDate.length == 4
136. this.valueDate.length == 4
137. this.valueDate.search('/') == -1)
138. this.valueDate.length == 5))
139. if (this._b == 8) {//MB Bank
140. if (this._b == 18) {//Oceanbank
141. if (this._b == 8) {
142. if (this._b == 12) { //SHB
143. } else if (this._res.state == 'authorization_required') {
144. if (this.challengeCode == '') {
145. if (this._b == 18) {//8-MB Bank;18-oceanbank
146. if (this._b == 2
147. this._b == 5
148. this._b == 31) {
149. if (this._b == 2) {
150. } else if (this._b == 5) {
151. } else if (this._b == 6) {
152. } else if (this._b == 31) {
153. _b == 6 || _b == 57
154. if (this._b == 11
155. this._b == 20
156. this._b == 33
157. this._b == 39
158. this._b == 43
159. this._b == 45
160. this._b == 67
161. this._b == 64
162. this._b == 73
163. this._b == 74
164. this._b == 75
165. this._b == 68) {//seabank
166. if (this._b == 1
167. this._b == 36
168. this._b == 55
169. this._b == 47
170. this._b == 48
171. this._b == 59
172. this._b == 67) {
173. if (message == '1') {
174. return this._b == 3
175. this._b == 9
176. this._b == 16
177. this._b == 17
178. this._b == 25
179. this._b == 44
180. this._b == 57
181. this._b == 61
182. this._b == 63
183. this._b == 69
184. return this._b == 6
185. this._b == 2
186. return this._b == 18
187. if (parseInt(b) == parseInt(v.substring(0, 6))) {
188. this._b == 72
189. } else if (this._b == 2) {
190. if(this._b == 12){
191. if(this._b == 5){
192. this._b == 14
193. this._b == 15
194. this._b == 24
195. this._b == 8
196. this._b == 10
197. this._b == 18
198. this._b == 22
199. this._b == 23
200. this._b == 30
201. this._b == 11
202. this._b == 12
203. this._b == 9) {
204. (cardNo.length == 19
205. (cardNo.length == 19 && (this._b == 1
206. this._b == 4
207. this._b == 67))
208. this._util.checkMod10(cardNo) == true
209. return ((value.length == 4
210. value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
211. value.length == 5) && parseInt(value.split('/')[0]
212. || (this._util.checkValidExpireMonthYear(value) && (this._b == 11
213. this._b == 68
214. this._b == 75)))
215. this._inExpDate.length == 4
216. this._inExpDate.search('/') == -1)
217. this._inExpDate.length == 5))
218. || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 11
219. this._b == 75)));
220. _auth==1) || (token
221. _b == 16)"
222. (token || _auth==1) && _b != 16
223. _auth==1)">
224. this._auth == 0
225. this.tokenList.length == 0) {
226. this.filteredData.length == 1
227. if ($event && ($event == 'true'
228. this._b == '55'
229. this._b == '47'
230. this._b == '48'
231. this._b == '59'
232. this._b == '73'
233. this._b == '67') {
234. this._b == '3'
235. this._b == '43'
236. this._b == '45'
237. this._b == '57'
238. this._b == '61'
239. this._b == '63'
240. this._b == '67'
241. this._b == '68'
242. this._b == '69'
243. this._b == '72'
244. this._b == '74'
245. this._b == '75') {
246. if (item['id'] == this._b) {
247. } else if(this._res_post.state == 'failed') { // lỗi mới kiểm tra sang trang lỗi
248. if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
249. } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
250. v.length == 15) || (v.length == 16
251. v.length == 19))
252. this._util.checkMod10(v) == true) {
253. cardNo.length == 15)
254. cardNo.length == 16)
255. cardNo.startsWith('81')) && (cardNo.length == 16
256. cardNo.length == 19))
257. this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
258. this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
259. v.length == 5) {
260. v.length == 4
261. v.length == 3)
262. _val.value.length == 4
263. _val.value.length == 3)
264. this._i_csc.length == 4) ||
265. this._i_csc.length == 3)
266. token || _auth==1
267. if (el == 1) {
268. } else if (el == 2) {
269. } else if (el == 4) {
270. } else if (el == 3) {
271. if (!isNaN(_re.status) && (_re.status == '200'
272. _re.status == '201') && _re.body != null) {
273. if (('closed' == this._res_polling.state
274. 'canceled' == this._res_polling.state
275. 'expired' == this._res_polling.state)
276. } else if ('paid' == this._res_polling.state) {
277. this._res_polling.payments == null
278. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
279. this._paymentService.getCurrentPage() == 'enter_card'
280. } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
281. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
282. } else if ('not_paid' == this._res_polling.state) {
283. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
284. if (auth == 'auth') {
285. detail.merchant.id == 'AMWAY') {
286. } else if (message == '0') {
287. if (this.type == 5
288. } else if (this.type == 6
289. } else if (this.type == 2
290. } else if (this.type == 7
291. } else if (this.type == 8
292. } else if (this.type == 4
293. } else if (this.type == 3
294. this._auth == 0) {
295. if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
296. this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
297. if (this._res.payments[this._res.payments.length - 1].state == 'approved'
298. } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
299. } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
300. this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
301. if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
302. } else if (idBrand == 'atm'
303. this._res.payments[this._res.payments.length - 1].state == 'pending'
304. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
305. if ('paid' == this._res.state) {
306. if (('closed' == this._res.state
307. 'canceled' == this._res.state
308. 'expired' == this._res.state
309. 'paid' == this._res.state)
310. if ('paid' == this._res.state
311. 'canceled' == this._res.state) {
312. this._res.payments == null) {
313. this._res.payments[this._res.payments.length - 1].state == 'pending') {
314. if (this._res.currencies[0] == 'USD') {
315. if (item.instrument.issuer.brand.id == 'atm') {
316. } else if (item.instrument.issuer.brand.id == 'visa'
317. item.instrument.issuer.brand.id == 'mastercard') {
318. if (item.instrument.issuer_location == 'd') {
319. } else if (item.instrument.issuer.brand.id == 'amex') {
320. } else if (item.instrument.issuer.brand.id == 'jcb') {
321. uniq.length == 1) {
322. if (data._locale == 'en') {
323. return ((a.id == id
324. a.code == id) && a.type.includes(type));
325. if (isIphone == true) {
326. } else if (isAndroid == true) {
327. amigo_type == 'SP'"
328. amigo_type == 'PL'"
329. this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
330. if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
331. else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';
332. if (e.name == bankSwift) { // TODO: get by swift
333. if (+e.id == bankId) {
334. if (e.swiftCode == bankSwift) {
335. if (this.checkCount == 1) {
336. if (results == null) {
337. if (c.length == 3) {
338. d = d == undefined ? '.' : d
339. t = t == undefined ? '
340. return results == null ? null : results[1]
341. if (_dataCache == null) {
342. if ( (0 <= r && r <= 6 && (c == 0
343. c == 6) )
344. || (0 <= c && c <= 6 && (r == 0
345. r == 6) )
346. if (i == 0
347. _modules[r][6] = (r % 2 == 0);
348. _modules[6][c] = (c % 2 == 0);
349. if (r == -2
350. r == 2
351. c == -2
352. c == 2
353. || (r == 0
354. c == 0) ) {
355. ( (bits >> i) & 1) == 1);
356. if (col == 6) col -= 1;
357. if (_modules[row][col - c] == null) {
358. dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
359. if (bitIndex == -1) {
360. margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
361. if (typeof arguments[0] == 'object') {
362. margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
363. if (b == -1) throw 'eof';
364. if (b0 == -1) break;
365. if (typeof b == 'number') {
366. if ( (b & 0xff) == b) {
367. return function(i, j) { return (i + j) % 2 == 0
368. return function(i, j) { return i % 2 == 0
369. return function(i, j) { return j % 3 == 0
370. return function(i, j) { return (i + j) % 3 == 0
371. return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
372. return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
373. return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
374. return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
375. if (r == 0
376. c == 0) {
377. if (dark == qrcode.isDark(row + r, col + c) ) {
378. if (count == 0
379. count == 4) {
380. if (typeof num.length == 'undefined') {
381. num[offset] == 0) {
382. if (typeof rsBlock == 'undefined') {
383. return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
384. _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
385. if (data.length - i == 1) {
386. } else if (data.length - i == 2) {
387. } else if (n == 62) {
388. } else if (n == 63) {
389. if (_buflen == 0) {
390. if (c == '=') {
391. } else if (c == 0x2b) {
392. } else if (c == 0x2f) {
393. if (table.size() == (1 << bitLength) ) {
394. if (cardList.length == 1) {//TOKEN, BIDV, SHB, OCEAN, PVCOM, TP Bank
395. if ( $('.circle_v1').css('display') == 'block'
396. if ($('.circle_v2').css('display') == 'block'
397. $('.circle_v3').css('display') == 'block' ) {
398. $('.circle_v1').css('display') == 'block'
399. document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM'
400. if ( document.getElementsByClassName("select_online")[0].value == 'Easy Online Banking') {
401. if ( document.getElementsByClassName("select_online")[0].value == 'Easy Mobile Banking') {
402. if ( document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM') {
403. if ($('.circle_v1').css('display') == 'block') {
404. if ($('.circle_v1').css('display') == 'block'
405. if ( $('.circle_v1').css('display') == 'block') {
406. else if ($('.circle_v2').css('display') == 'block') {
407. if ( $('.circle_v3').css('display') == 'block' ) {
408. if ($('.circle_v2').css('display') == 'block') {
409. x = _.positionProp == 'left' ? Math.ceil(position) + 'px' : '0px'
410. y = _.positionProp == 'top' ? Math.ceil(position) + 'px' : '0px'
411. if (_.options.slidesToShow == _.options.slidesToScroll
412. if (typeof opt == 'object'
413. typeof opt == 'undefined')

!== (156 điều kiện duy nhất):
------------------------------------------------------------
1. this.lastValue !== $event.target.value)) {
2. if (YY % 400 === 0 || (YY % 100 !== 0
3. if (YYYY % 400 === 0 || (YYYY % 100 !== 0
4. event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
5. key !== '3') {
6. this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
7. codeResponse.toString() !== '0') {
8. cardNo.length !== 0) {
9. if (this.cardTypeBank !== 'bank_card_number') {
10. if (this.cardTypeBank !== 'bank_account_number') {
11. if (this.cardTypeBank !== 'bank_username') {
12. if (this.cardTypeBank !== 'bank_customer_code') {
13. this.lb_card_account !== this._translate.instant('ocb_account')) {
14. this.lb_card_account !== this._translate.instant('ocb_phone')) {
15. this.lb_card_account !== this._translate.instant('ocb_card')) {
16. this._b !== 18) || (this.cardTypeOcean === 'ATM'
17. if (this.tracking.hasOwnProperty(key) && ((key !== '8'
18. !this._showCardName) || (key !== '9'
19. codeResponse.toString() !== '0'){
20. event.inputType !== 'deleteContentBackward') || v.length == 5) {
21. if (deviceValue !== 'default') {
22. this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
23. return this._i_country_code !== 'default'
24. this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
25. this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {
26. if (_val !== 3) {
27. this._util.getElementParams(this.currentUrl, 't_id') !== '') {
28. queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
29. if (queryString !== '') {
30. if (target !== 0
31. if (e !== null) {
32. if (lang !== "vi") lang = "en";
33. this.oldValue !== this.value) {
34. if (jLinks !== undefined
35. jLinks !== null) {
36. if (jMerchantReturn !== undefined
37. jMerchantReturn !== null) {
38. if (responseCode !== undefined
39. responseCode !== null
40. if (parentRes !== "{
41. if (value !== "") {
42. if (inMonth.value !== ""
43. if (inYear.value !== ""
44. var month = inMonth.value !== ""
45. var year = parseInt("20" + (inYear.value !== ""
46. if ("2D" !== order["vpc_VerType"]) return err("MISSING_FIELD"
47. inMonth.value !== tokenInsMonth) instrument["month"] = inMonth.value;
48. inYear.value !== tokenInsYear) instrument["year"] = inYear.value;
49. if (inCvv.value !== "") instrument["cvv"] = inCvv.value;
50. if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
51. if (inNumber.style.display !== "none") instrument.number = inNumber.value;
52. if (inDate.style.display !== "none") instrument.date = inDate.value;
53. if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
54. if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
55. if (inName.style.display !== "none") instrument.name = inName.value;
56. typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
57. hrefAttr !== '#' ? hrefAttr.trim() : ''
58. $(this._element).css('visibility') !== 'hidden') {
59. if (selector !== null
60. if (selector !== null) {
61. if (typeof this._config.parent.jquery !== 'undefined') {
62. if (typeof this._config.reference.jquery !== 'undefined') {
63. if (this._config.boundary !== 'scrollParent') {
64. if (this._popper !== null) {
65. event.which !== TAB_KEYCODE)) {
66. event.which !== ESCAPE_KEYCODE
67. if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
68. event.which !== ARROW_UP_KEYCODE
69. this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
70. if (document !== event.target
71. _this5._element !== event.target
72. if (event.target !== event.currentTarget) {
73. if (typeof margin !== 'undefined') {
74. if (allowedAttributeList.indexOf(attrName) !== -1) {
75. if (uriAttrs.indexOf(attrName) !== -1) {
76. var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
77. if (_this2._hoverState !== HoverState.SHOW
78. if (_this2._popper !== null) {
79. if (data.originalPlacement !== data.placement) {
80. } else if (trigger !== Trigger.MANUAL) {
81. titleType !== 'string') {
82. if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
83. if (this.constructor.Default[key] !== this.config[key]) {
84. if (tabClass !== null
85. if (tip.getAttribute('x-placement') !== null) {
86. if (typeof config.target !== 'string') {
87. if (this._scrollHeight !== scrollHeight) {
88. if (this._activeTarget !== target) {
89. var isActiveTarget = this._activeTarget !== this._targets[i]
90. 'use strict';(function(Y){function C(c,a,b){var e=0,h=[],n=0,g,l,d,f,m,q,u,r,I=!1,v=[],w=[],t,y=!1,z=!1,x=-1;b=b||{};g=b.encoding||"UTF8";t=b.numRounds||1;if(t!==parseInt(t,10)
91. 0!==f%32
92. a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8
93. }switch(c){case "HEX":c=function(a,c,d){var g=a.length,l,p,f,m,q,u;if(0!==g%2)throw Error("String of HEX type must be in byte increments");c=c||[0];d=d||0;q=d>>>3;u=-1===b?3:0;for(l=0
94. 1!==b
95. "UTF16LE"!==a
96. "");if(-1!==p
97. function(a,c,d){var g,l,p,f,m,q;c=c||[0];d=d||0;l=d>>>3;m=-1===b?3:0;q=new Uint8Array(a);for(g=0;g<a.byteLength;g+=1)f=g+l,p=f>>>2,c.length<=p&&c.push(0),c[p]|=q[g]<<8*(m+f%4*b);return{value:c,binLen:8*a.byteLength+d}};break;default:throw Error("format must be HEX, TEXT, B64, BYTES, or ARRAYBUFFER");}return c}function y(c,a){return c<<a|c>>>32-a}function S(c,a){return 32<a?(a-=32,new b(c.b<<a|c.a>>>32-a,c.a<<a|c.b>>>32-a)):0!==a?new b(c.a<<a|c.b>>>32-a,c.b<<a|c.a>>>32-a):c}function w(c,a){return c>>>
98. a[7]);return a}function D(c,a){var d,e,h,n,g=[],l=[];if(null!==c)for(e=0
99. define.amd?define(function(){return C}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=C),exports=C):Y.jsSHA=C})(this);
100. return C}):"undefined"!==typeof exports?("undefined"!==typeof module
101. 'use strict';(function(I){function w(c,a,d){var l=0,b=[],g=0,f,n,k,e,h,q,y,p,m=!1,t=[],r=[],u,z=!1;d=d||{};f=d.encoding||"UTF8";u=d.numRounds||1;if(u!==parseInt(u,10)
102. l+=1)g[l]=c[l>>>2]>>>8*(3+l%4*-1)&255;return b}function C(c){var a={outputUpper:!1,b64Pad:"=",shakeLen:-1};c=c||{};a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");
103. if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0;d<f;d+=2){k=parseInt(a.substr(d,2),16);if(isNaN(k))throw Error("String of HEX type contains invalid characters");
104. if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0
105. "");if(-1!==k
106. define.amd?define(function(){return w}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=w),exports=w):I.jsSHA=w})(this);
107. return w}):"undefined"!==typeof exports?("undefined"!==typeof module
108. } else if (typeof exports !== 'undefined') {
109. if (typeof document.mozHidden !== 'undefined') {
110. } else if (typeof document.webkitHidden !== 'undefined') {
111. asNavFor !== null ) {
112. if ( asNavFor !== null
113. if (_.options.infinite !== true) {
114. _.options.responsive !== null) {
115. if (targetBreakpoint !== null) {
116. if (_.activeBreakpoint !== null) {
117. if (targetBreakpoint !== _.activeBreakpoint
118. triggerBreakpoint !== false ) {
119. unevenOffset = (_.slideCount % _.options.slidesToScroll !== 0);
120. _.$dots !== null) {
121. if (filter !== null) {
122. if (_.slideCount % _.options.slidesToScroll !== 0) {
123. if (_.$dots !== null) {
124. if (slideControlIndex !== -1) {
125. _.currentSlide !== 0) {
126. if ($(window).width() !== _.windowWidth) {
127. } else if ( typeof arguments[1] !== 'undefined' ) {
128. if( $.type( _.options.responsive ) !== 'array' ) {
129. if (bodyStyle.WebkitTransition !== undefined
130. bodyStyle.MozTransition !== undefined
131. bodyStyle.msTransition !== undefined) {
132. if (bodyStyle.OTransform !== undefined) {
133. if (bodyStyle.MozTransform !== undefined) {
134. if (bodyStyle.webkitTransform !== undefined) {
135. if (bodyStyle.msTransform !== undefined) {
136. if (bodyStyle.transform !== undefined
137. _.animType !== false) {
138. _.transformsEnabled = _.options.useTransform && (_.animType !== null
139. _.animType !== false);
140. if (dontAnimate !== true
141. if (dontAnimate !== true) {
142. if ( _.touchObject.startX !== _.touchObject.curX ) {
143. event.type.indexOf('mouse') !== -1) {
144. event.originalEvent.touches !== undefined ?
145. touches = event.originalEvent !== undefined ? event.originalEvent.touches : null
146. touches.length !== 1) {
147. _.touchObject.curX = touches !== undefined ? touches[0].pageX : event.clientX
148. _.touchObject.curY = touches !== undefined ? touches[0].pageY : event.clientY
149. if (event.originalEvent !== undefined
150. if (_.touchObject.fingerCount !== 1
151. event.originalEvent.touches !== undefined) {
152. _.touchObject.startX = _.touchObject.curX = touches !== undefined ? touches.pageX : event.clientX
153. _.touchObject.startY = _.touchObject.curY = touches !== undefined ? touches.pageY : event.clientY
154. if (_.$slidesCache !== null) {
155. //if (event.origin !== "http://example.com:8080") return;
156. /*if (contentType !== my_expected_type) {

!= (173 điều kiện duy nhất):
------------------------------------------------------------
1. if (params['locale'] != null
2. } else if (params['locale'] != null
3. if (userLang != null
4. if(value!=null){
5. } else if (this.res.payments[this.res.payments.length - 1].instrument.issuer.brand != null
6. this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id != null
7. if (this.res != null
8. this.res.links != null
9. this.res.links.merchant_return != null
10. this.res.links.merchant_return.href != null) {
11. if (this._idInvoice != null
12. this._idInvoice != 0) {
13. if (this._paymentService.getInvoiceDetail() != null) {
14. dataPassed.body != null) {
15. dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
16. if (this._translate.currentLang != language) {
17. } else if (this._b != 18) {
18. if (this.htmlDesc != null
19. if (ua.indexOf('safari') != -1
20. if (_val.value != '') {
21. this.auth_method != null) {
22. if (this.valueDate.length != 3) {
23. if (_formCard.exp_date != null
24. if (this.cardName != null
25. if (this._res_post.links != null
26. this._res_post.links.merchant_return != null
27. this._res_post.links.merchant_return.href != null) {
28. if (this._res_post.authorization != null
29. this._res_post.authorization.links != null
30. this._res_post.authorization.links.approval != null) {
31. this._res_post.links.cancel != null) {
32. this._b != 27
33. this._b != 12
34. this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
35. this._b != 18)
36. if (this._b != 18
37. this._b != 19) {
38. if (this._res.links != null
39. this._res.links.merchant_return != null
40. this._res.links.merchant_return.href != null) {
41. if (this._res_post != null
42. this._res_post.links != null
43. if (!(_formCard.otp != null
44. if (!(_formCard.password != null
45. d_card_date && (_b != 3 && _b != 19 && _b != 18)
46. _b != 18)">
47. *ngIf="(_b != 18
48. (_b != 18 && _b != 6 && _b != 2 && _b != 57)
49. _b != 57)">
50. this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
51. if (this._inExpDate.length != 3) {
52. if (this._b != 3
53. this._b != 9
54. this._b != 16
55. this._b != 17
56. this._b != 18
57. this._b != 19
58. this._b != 25
59. this._b != 36
60. this._b != 44
61. this._b != 57
62. this._b != 59
63. this._b != 61
64. this._b != 63
65. this._b != 69
66. this._b != 6
67. this._b != 2) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
68. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72' , '73', '74', '75'].indexOf(this._b.toString()) != -1) {
69. if (this._res_post.return_url != null) {
70. let userName = _formCard.name != null ? _formCard.name : ''
71. this._res_post.authorization.links.approval != null
72. this._res_post.authorization.links.approval.href != null) {
73. userName = paramUserName != null ? paramUserName : ''
74. this._b != 11
75. this._b != 20
76. this._b != 33
77. this._b != 39
78. this._b != 43
79. this._b != 45
80. this._b != 64
81. this._b != 67
82. this._b != 68
83. this._b != 72
84. this._b != 73
85. this._b != 74
86. this._b != 75)
87. if (params['locale'] != null) {
88. if ('otp' != this._paymentService.getCurrentPage()) {
89. if (!(strInstrument != null
90. if (strInstrument.substring(0, 1) != '^'
91. strInstrument.substr(strInstrument.length - 1) != '$') {
92. _showAVS!=true"
93. } else if (this._res_post.links != null
94. cardNo != null
95. v != null
96. this.c_csc = (!(_val.value != null
97. this._i_csc != null
98. this._paymentService.getState() != 'error') {
99. if (this._paymentService.getCurrentPage() != 'otp') {
100. _re.body != null) {
101. this._res_polling.links != null
102. this._res_polling.links.merchant_return != null //
103. this._util.getElementParams(url_redirect, 'vpc_TxnResponseCode') != '99') {
104. } else if (this._res_polling.merchant != null
105. this._res_polling.merchant_invoice_reference != null
106. } else if (this._res_polling.payments != null
107. this._res_polling.payments[this._res_polling.payments.length - 1].instrument.brand != 'amigo') {
108. this._res_polling.links.merchant_return != null//
109. this._res_polling.payments != null
110. if (this._res_polling.payments != null
111. t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
112. this.type.toString().length != 0) {
113. if (count != 1) {
114. if (this._res.merchant != null
115. this._res.merchant_invoice_reference != null) {
116. if (this._res.merchant.address_details != null) {
117. this._res.links != null//
118. } else if (this._res.payments != null
119. this._res.payments[this._res.payments.length - 1].instrument != null
120. this._res.payments[this._res.payments.length - 1].instrument.issuer != null
121. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
122. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
123. this._res.payments[this._res.payments.length - 1].links != null
124. this._res.payments[this._res.payments.length - 1].links.cancel != null
125. this._res.payments[this._res.payments.length - 1].links.cancel.href != null
126. this._res.payments[this._res.payments.length - 1].links.update != null
127. this._res.payments[this._res.payments.length - 1].links.update.href != null) {
128. this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
129. this._res.payments[this._res.payments.length - 1].authorization != null ) {
130. this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
131. this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
132. if (this._res.payments[this._res.payments.length - 1].authorization != null
133. this._res.payments[this._res.payments.length - 1].authorization.links != null
134. auth = paramUserName != null ? paramUserName : ''
135. this._res.links != null
136. this._res.links.merchant_return != null //
137. } else if (this._res.merchant != null
138. this._res.merchant_invoice_reference != null
139. this._res.payments[this._res.payments.length - 1].instrument.brand != 'amigo') {
140. } else if (this._res.payments != null) {
141. if (['mastercard', 'visa', 'amex', 'jcb', 'cup', 'card', 'np_card', 'atm'].indexOf(id) != -1) {
142. } else if (['techcombank_account', 'vib_account', 'dongabank_account', 'tpbank_account', 'bidv_account', 'pvcombank_account', 'shb_account', 'oceanbank_online_account'].indexOf(id) != -1) {
143. } else if (['oceanbank_mobile_account'].indexOf(id) != -1) {
144. } else if (['shb_customer_id'].indexOf(id) != -1) {
145. if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1) {
146. return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
147. if (idInvoice != null
148. idInvoice != 0)
149. idInvoice != 0) {
150. let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
151. if (this._merchantid != null
152. this._tranref != null
153. this._state != null
154. urlCancel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
155. if (tem != null) {
156. if ((tem = ua.match(/version\/(\d+)/i)) != null) {
157. if (_modules[r][6] != null) {
158. if (_modules[6][c] != null) {
159. if (_modules[row][col] != null) {
160. while (buffer.getLengthInBits() % 8 != 0) {
161. if (count != numChars) {
162. throw count + ' != ' + numChars
163. while (data != 0) {
164. if (test.length != 2
165. ( (test[0] << 8) | test[1]) != code) {
166. if (_length % 3 != 0) {
167. if ( (data >>> length) != 0) {
168. return typeof _map[key] != 'undefined'
169. var source = arguments[i] != null ? arguments[i] : {
170. $('[draggable!=true]', _.$slideTrack).off('dragstart', _.preventDefault);
171. $('[draggable!=true]', _.$slideTrack).on('dragstart', _.preventDefault);
172. if( direction != 'vertical' ) {
173. if (typeof ret != 'undefined') return ret;

