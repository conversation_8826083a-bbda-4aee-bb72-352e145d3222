====================================================================================================
TRÍCH XUẤT ĐIỀU KIỆN SO SÁNH TỪ CÁC FILE TYPESCRIPT/JAVASCRIPT/HTML
====================================================================================================
Thư mục/File nguồn: /root/projects/onepay/paygate-domestic/src
Thời gian: 18:12:28 18/8/2025
Tổng số file xử lý: 157
Tổng số file bị bỏ qua: 48
Tổng số điều kiện tìm thấy: 3219

THỐNG KÊ TỔNG QUAN THEO LOẠI TOÁN TỬ:
--------------------------------------------------
Strict equality (===): 1809 lần
Loose equality (==): 519 lần
Strict inequality (!==): 658 lần
Loose inequality (!=): 233 lần
--------------------------------------------------

DANH SÁCH FILE ĐÃ XỬ LÝ:
--------------------------------------------------
1. 4en.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/4en.html
2. 4vn.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/4vn.html
3. app-routing.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/app-routing.module.ts
4. app.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/app.component.html
5. app.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/app.component.spec.ts
6. app.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/app.component.ts
7. app.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/app.module.ts
8. counter.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/counter.directive.spec.ts
9. counter.directive.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/counter.directive.ts
10. format-carno-input.derective.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/directives/format-carno-input.derective.ts
11. uppercase-input.directive.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/directives/uppercase-input.directive.ts
12. error.component.html (10 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/error/error.component.html
13. error.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/error/error.component.spec.ts
14. error.component.ts (27 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/error/error.component.ts
15. support-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/error/support-dialog/support-dialog.html
16. support-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/error/support-dialog/support-dialog.ts
17. format-date.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/format-date.directive.spec.ts
18. format-date.directive.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/format-date.directive.ts
19. main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/main.component.html
20. main.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/main.component.spec.ts
21. main.component.ts (12 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/main.component.ts
22. bank-amount-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/bank-amount-dialog/bank-amount-dialog.component.html
23. bank-amount-dialog.component.ts (34 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/bank-amount-dialog/bank-amount-dialog.component.ts
24. cancel-dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/cancel-dialog-guide-dialog.html
25. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/confirm-dialog/dialog-guide-dialog.html
26. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/dialog-guide-dialog.html
27. bankaccount.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
28. bankaccount.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
29. bankaccount.component.ts (153 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
30. bank.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/model/bank.ts
31. otp-auth.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
32. otp-auth.component.ts (21 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
33. techcombank.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
34. techcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
35. techcombank.component.ts (16 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
36. vietcombank.component.html (12 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
37. vietcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
38. vietcombank.component.ts (116 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
39. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
40. off-bank-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
41. off-bank-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
42. domescard-main.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/domescard-main.component.html
43. domescard-main.component.ts (54 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/domescard-main.component.ts
44. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/intercard-main/dialog-guide-dialog.html
45. intercard-main.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/intercard-main/intercard-main.component.html
46. intercard-main.component.ts (60 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/intercard-main/intercard-main.component.ts
47. menu.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/menu.component.html
48. menu.component.ts (136 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/menu.component.ts
49. policy-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/policy-dialog/policy-dialog/policy-dialog.component.html
50. policy-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/policy-dialog/policy-dialog/policy-dialog.component.ts
51. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
52. qr-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
53. qr-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
54. qr-main.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/qr-main/qr-main.component.html
55. qr-main.component.ts (12 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/qr-main/qr-main.component.ts
56. remove-token-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/token-main/remove-token-dialog/remove-token-dialog.html
57. dialog-guide-dialog.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/token-main/token-dialog/dialog-guide-dialog.html
58. token-main.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/token-main/token-main.component.html
59. token-main.component.ts (69 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/token-main/token-main.component.ts
60. overlay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/overlay/overlay.component.html
61. overlay.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/overlay/overlay.component.spec.ts
62. overlay.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/overlay/overlay.component.ts
63. bank-amount.pipe.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/pipe/bank-amount.pipe.ts
64. close-dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/services/close-dialog.service.ts
65. data.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/services/data.service.ts
66. dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/services/dialog.service.ts
67. fee.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/services/fee.service.ts
68. focus-input.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/services/focus-input.service.ts
69. multiple_method.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/services/multiple_method.service.ts
70. payment.service.ts (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/services/payment.service.ts
71. token-main.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/services/token-main.service.ts
72. index.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/translate/index.ts
73. lang-en.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/translate/lang-en.ts
74. lang-vi.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/translate/lang-vi.ts
75. translate.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/translate/translate.pipe.ts
76. translate.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/translate/translate.service.ts
77. translations.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/translate/translations.ts
78. apps-info.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/util/apps-info.ts
79. banks-info.ts (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/util/banks-info.ts
80. util.ts (39 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/util/util.ts
81. qrcode.js (67 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/assets/script/qrcode.js
82. environment.prod.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/environments/environment.prod.ts
83. environment.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/environments/environment.ts
84. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/acc_bidv/index.html
85. script.js (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/acc_bidv/script.js
86. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/acc_oceanbank/index.html
87. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/acc_oceanbank/script.js
88. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/acc_pvcombank/index.html
89. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/acc_pvcombank/script.js
90. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/acc_shb/index.html
91. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/acc_shb/script.js
92. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/acc_tpbank/index.html
93. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/acc_tpbank/script.js
94. common.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/common.js
95. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/domestic_card/index.html
96. script.js (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/domestic_card/script.js
97. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/domestic_token/index.html
98. script.js (25 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/domestic_token/script.js
99. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/index.html
100. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/international_card/index.html
101. script.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/international_card/script.js
102. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/international_token/index.html
103. script.js (36 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/international_token/script.js
104. script.js (54 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/script.js
105. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.js
106. bidv.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Bidv/assets/js/bidv.js
107. bidv2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Bidv/bidv2.js
108. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Bidv/index.html
109. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.js
110. ocean.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Ocean/assets/js/ocean.js
111. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Ocean/index.html
112. ocean2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Ocean/ocean2.js
113. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
114. pvbank.js (23 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Pv_Bank/assets/js/pvbank.js
115. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Pv_Bank/index.html
116. pvbank2.js (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Pv_Bank/pvbank2.js
117. Sea2.js (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/SeaBank/Sea2.js
118. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
119. Sea.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/SeaBank/assets/js/Sea.js
120. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/SeaBank/index.html
121. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.js
122. shb.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Shb/assets/js/shb.js
123. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Shb/index.html
124. shb2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Shb/shb2.js
125. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
126. tpbank.js (23 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Tp_Bank/assets/js/tpbank.js
127. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Tp_Bank/index.html
128. tpbank2.js (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Tp_Bank/tpbank2.js
129. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.js
130. onepay.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Visa/assets/js/onepay.js
131. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Visa/index.html
132. index.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Visa/index.js
133. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
134. vpbank.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Vp_Bank/assets/js/vpbank.js
135. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Vp_Bank/index.html
136. vpbank2.js (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Vp_Bank/vpbank2.js
137. sha.js (49 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/assets/js/sha.js
138. sha256.js (28 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/assets/js/sha256.js
139. slick.js (182 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/assets/libraries/slick/slick.js
140. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.js
141. atm_b1.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_1/assets/js/atm_b1.js
142. atm_b1_2.js (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_1/atm_b1_2.js
143. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_1/index.html
144. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.js
145. atm_b2.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_2/assets/js/atm_b2.js
146. atm_b2_2.js (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_2/atm_b2_2.js
147. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_2/index.html
148. common.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/common.js
149. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.js
150. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/international_card/index.html
151. script.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/international_card/script.js
152. index.html (35 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/index.html
153. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/index.html
154. karma.conf.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/karma.conf.js
155. main.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/main.ts
156. polyfills.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/polyfills.ts
157. test.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/test.ts

DANH SÁCH FILE BỊ BỎ QUA:
--------------------------------------------------
1. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/bootstrap.min.js
2. jquery-3.0.0.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/jquery-3.0.0.min.js
3. popper.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/popper.min.js
4. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
5. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
6. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
7. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Bidv/assets/js/jquery-3.4.1.min.js
8. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
9. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
10. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
11. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Ocean/assets/js/jquery-3.4.1.min.js
12. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
13. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
14. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
15. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Pv_Bank/assets/js/jquery-3.4.1.min.js
16. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
17. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
18. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
19. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/SeaBank/assets/js/jquery-3.4.1.min.js
20. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
21. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
22. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
23. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Shb/assets/js/jquery-3.4.1.min.js
24. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
25. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
26. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
27. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Tp_Bank/assets/js/jquery-3.4.1.min.js
28. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
29. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
30. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
31. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Visa/assets/js/jquery-3.4.1.min.js
32. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
33. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
34. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
35. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Vp_Bank/assets/js/jquery-3.4.1.min.js
36. instafeed.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/assets/js/instafeed.min.js
37. slick.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/assets/libraries/slick/slick.min.js
38. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
39. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
40. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
41. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_1/assets/js/jquery-3.4.1.min.js
42. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
43. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
44. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
45. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_2/assets/js/jquery-3.4.1.min.js
46. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
47. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
48. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js

====================================================================================================
CHI TIẾT TỪNG FILE:
====================================================================================================

📁 FILE 1: 4en.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/4en.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 2: 4vn.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/4vn.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 3: app-routing.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/app-routing.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 4: app.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/app.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 5: app.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/app.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 6: app.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/app.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 76] return lang === this._translate.currentLang

== (2 điều kiện):
  1. [Dòng 55] 'vi' == params['locale']) {
  2. [Dòng 57] 'en' == params['locale']) {

!= (3 điều kiện):
  1. [Dòng 55] if (params['locale'] != null
  2. [Dòng 57] } else if (params['locale'] != null
  3. [Dòng 64] if (userLang != null

================================================================================

📁 FILE 7: app.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/app.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 8: counter.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/counter.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 9: counter.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/counter.directive.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 10: format-carno-input.derective.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/directives/format-carno-input.derective.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 44] this.lastValue !== $event.target.value)) {

!= (1 điều kiện):
  1. [Dòng 23] if(value!=null){

================================================================================

📁 FILE 11: uppercase-input.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/directives/uppercase-input.directive.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 0 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 23] this.lastValue !== $event.target.value)) {

================================================================================

📁 FILE 12: error.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/error/error.component.html
📊 Thống kê: 10 điều kiện duy nhất
   - === : 3 lần
   - == : 7 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 72] isPopupSupport === 'True') || (errorInitPage
  2. [Dòng 73] isPopupSupport === 'True'"
  3. [Dòng 79] isPopupSupport === 'True')">

== (7 điều kiện):
  1. [Dòng 3] errorCode == '11'"
  2. [Dòng 72] isSent == false
  3. [Dòng 79] <div class="payment_again" *ngIf="rePayment && !timeOut" [class.select_only]="!(isSent == false
  4. [Dòng 92] <div class="col-md-12 d_btn_cancel_select" *ngIf="errorInitPage && (errorCode == 'overtime'
  5. [Dòng 92] errorCode == '253')"><a
  6. [Dòng 94] <a (click)="leaveNow()" class="leave_now" *ngIf="errorInitPage && !(errorCode == 'overtime'
  7. [Dòng 94] errorInitPage && !(errorCode == 'overtime' || errorCode == '253') && isBack

================================================================================

📁 FILE 13: error.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/error/error.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 14: error.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/error/error.component.ts
📊 Thống kê: 27 điều kiện duy nhất
   - === : 8 lần
   - == : 11 lần
   - !== : 0 lần
   - != : 8 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 126] params.timeout === 'true') {
  2. [Dòng 145] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  3. [Dòng 145] _re.body.state === 'unpaid');
  4. [Dòng 212] if (this.errorCode === 'overtime'
  5. [Dòng 212] this.errorCode === '253') {
  6. [Dòng 276] params.name === 'CUSTOMER_INTIME'
  7. [Dòng 276] params.code === '09') {
  8. [Dòng 323] if (this.timeLeft === 0) {

== (11 điều kiện):
  1. [Dòng 153] if (this.res.currencies[0] == 'USD') {
  2. [Dòng 186] if(_re.body.themes.theme == 'mafc'){
  3. [Dòng 192] params.response_code == 'overtime') {
  4. [Dòng 231] if (_re.status == '200'
  5. [Dòng 231] _re.status == '201') {
  6. [Dòng 244] if (_re2.status == '200'
  7. [Dòng 244] _re2.status == '201') {
  8. [Dòng 257] if (this.errorCode == 'overtime'
  9. [Dòng 257] this.errorCode == '253') {
  10. [Dòng 262] this.res.state == 'canceled') {
  11. [Dòng 321] if (this.isTimePause == false) {

!= (8 điều kiện):
  1. [Dòng 382] if (_re != null
  2. [Dòng 382] _re.links != null
  3. [Dòng 382] _re.links.merchant_return != null
  4. [Dòng 383] _re.links.merchant_return.href != null) {
  5. [Dòng 389] if (_re.body != null
  6. [Dòng 389] _re.body.links != null
  7. [Dòng 389] _re.body.links.merchant_return != null
  8. [Dòng 390] _re.body.links.merchant_return.href != null) {

================================================================================

📁 FILE 15: support-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/error/support-dialog/support-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 16: support-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/error/support-dialog/support-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 17: format-date.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/format-date.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 18: format-date.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/format-date.directive.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0
  2. [Dòng 123] YY % 4 === 0)) {
  3. [Dòng 138] if (YYYY % 400 === 0
  4. [Dòng 138] YYYY % 4 === 0)) {

!== (2 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0 || (YY % 100 !== 0
  2. [Dòng 138] if (YYYY % 400 === 0 || (YYYY % 100 !== 0

================================================================================

📁 FILE 19: main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 20: main.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/main.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 21: main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/main.component.ts
📊 Thống kê: 12 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 8 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 94] if ((dataPassed.status == '200'
  2. [Dòng 94] dataPassed.status == '201') && dataPassed.body != null) {
  3. [Dòng 136] if (message == '1') {
  4. [Dòng 138] } else if (message == '0') {

!= (8 điều kiện):
  1. [Dòng 85] if (this._idInvoice != null
  2. [Dòng 85] this._idInvoice != 0) {
  3. [Dòng 86] if (this._paymentService.getInvoiceDetail() != null) {
  4. [Dòng 94] dataPassed.body != null) {
  5. [Dòng 114] dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
  6. [Dòng 115] dataPassed.body.themes.allow_save_token != true ? dataPassed.body.themes.allow_save_token : true
  7. [Dòng 116] dataPassed.body.themes.border_color != true ? dataPassed.body.themes.border_color : ''
  8. [Dòng 173] if (this._translate.currentLang != language) {

================================================================================

📁 FILE 22: bank-amount-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/bank-amount-dialog/bank-amount-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 23: bank-amount-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/bank-amount-dialog/bank-amount-dialog.component.ts
📊 Thống kê: 34 điều kiện duy nhất
   - === : 0 lần
   - == : 34 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (34 điều kiện):
  1. [Dòng 41] if (this.locale == 'en') {
  2. [Dòng 55] if (name == 'MAFC')
  3. [Dòng 61] if (bankId == 3
  4. [Dòng 61] bankId == 61
  5. [Dòng 62] bankId == 8
  6. [Dòng 62] bankId == 49
  7. [Dòng 63] bankId == 48
  8. [Dòng 64] bankId == 10
  9. [Dòng 64] bankId == 53
  10. [Dòng 65] bankId == 17
  11. [Dòng 65] bankId == 65
  12. [Dòng 66] bankId == 23
  13. [Dòng 66] bankId == 52
  14. [Dòng 67] bankId == 27
  15. [Dòng 67] bankId == 66
  16. [Dòng 68] bankId == 9
  17. [Dòng 68] bankId == 54
  18. [Dòng 69] bankId == 37
  19. [Dòng 70] bankId == 38
  20. [Dòng 71] bankId == 39
  21. [Dòng 72] bankId == 40
  22. [Dòng 73] bankId == 42
  23. [Dòng 74] bankId == 44
  24. [Dòng 75] bankId == 72
  25. [Dòng 76] bankId == 59
  26. [Dòng 79] bankId == 51
  27. [Dòng 80] bankId == 64
  28. [Dòng 81] bankId == 58
  29. [Dòng 82] bankId == 56
  30. [Dòng 85] bankId == 55
  31. [Dòng 86] bankId == 60
  32. [Dòng 87] bankId == 68
  33. [Dòng 88] bankId == 74
  34. [Dòng 89] bankId == 75

================================================================================

📁 FILE 24: cancel-dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/cancel-dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 25: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/confirm-dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 26: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 27: bankaccount.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 3 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 40] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 55] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 55] valueDate.trim().length === 0)"

== (1 điều kiện):
  1. [Dòng 91] token_site == 'onepay'

================================================================================

📁 FILE 28: bankaccount.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 29: bankaccount.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
📊 Thống kê: 153 điều kiện duy nhất
   - === : 49 lần
   - == : 70 lần
   - !== : 13 lần
   - != : 21 lần
--------------------------------------------------------------------------------

=== (49 điều kiện):
  1. [Dòng 119] if (target.tagName === 'A'
  2. [Dòng 148] if (isIE[0] === 'MSIE'
  3. [Dòng 148] +isIE[1] === 10) {
  4. [Dòng 236] if ((_val.value.substr(-1) === ' '
  5. [Dòng 236] _val.value.length === 24) {
  6. [Dòng 246] if (this.cardTypeBank === 'bank_card_number') {
  7. [Dòng 251] } else if (this.cardTypeBank === 'bank_account_number') {
  8. [Dòng 257] } else if (this.cardTypeBank === 'bank_username') {
  9. [Dòng 261] } else if (this.cardTypeBank === 'bank_customer_code') {
  10. [Dòng 267] this.cardTypeBank === 'bank_card_number'
  11. [Dòng 281] if (this.cardTypeOcean === 'IB') {
  12. [Dòng 285] } else if (this.cardTypeOcean === 'MB') {
  13. [Dòng 286] if (_val.value.substr(0, 2) === '84') {
  14. [Dòng 293] } else if (this.cardTypeOcean === 'ATM') {
  15. [Dòng 320] if (this.cardTypeBank === 'bank_account_number') {
  16. [Dòng 339] this.cardTypeBank === 'bank_card_number') {
  17. [Dòng 361] if (this.cardTypeBank === 'bank_card_number'
  18. [Dòng 361] if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  19. [Dòng 683] if (event.keyCode === 8
  20. [Dòng 683] event.key === "Backspace"
  21. [Dòng 723] if (v.length === 2
  22. [Dòng 723] this.flag.length === 3
  23. [Dòng 723] this.flag.charAt(this.flag.length - 1) === '/') {
  24. [Dòng 727] if (v.length === 1) {
  25. [Dòng 729] } else if (v.length === 2) {
  26. [Dòng 732] v.length === 2) {
  27. [Dòng 740] if (len === 2) {
  28. [Dòng 818] this.cardTypeBank === 'bank_account_number') {
  29. [Dòng 1002] if ((this.cardTypeBank === 'bank_account_number'
  30. [Dòng 1002] this.cardTypeBank === 'bank_username'
  31. [Dòng 1002] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  32. [Dòng 1007] this.cardTypeOcean === 'ATM')
  33. [Dòng 1008] || (this.cardTypeOcean === 'IB'
  34. [Dòng 1067] if (valIn === this._translate.instant('bank_card_number')) {
  35. [Dòng 1092] } else if (valIn === this._translate.instant('bank_account_number')) {
  36. [Dòng 1111] } else if (valIn === this._translate.instant('bank_username')) {
  37. [Dòng 1127] } else if (valIn === this._translate.instant('bank_customer_code')) {
  38. [Dòng 1215] if (_val.value === ''
  39. [Dòng 1215] _val.value === null
  40. [Dòng 1215] _val.value === undefined) {
  41. [Dòng 1224] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  42. [Dòng 1224] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  43. [Dòng 1231] this.cardTypeOcean === 'MB') {
  44. [Dòng 1239] this.cardTypeOcean === 'IB'
  45. [Dòng 1244] if ((this.cardTypeBank === 'bank_card_number'
  46. [Dòng 1276] if (this.cardName === undefined
  47. [Dòng 1276] this.cardName === '') {
  48. [Dòng 1284] if (this.valueDate === undefined
  49. [Dòng 1284] this.valueDate === '') {

== (70 điều kiện):
  1. [Dòng 139] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 165] if (this._b == 18
  3. [Dòng 165] this._b == 19) {
  4. [Dòng 168] if (this._b == 19) {//19BIDV
  5. [Dòng 176] } else if (this._b == 3
  6. [Dòng 176] this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  7. [Dòng 181] if (this._b == 27) {
  8. [Dòng 186] } else if (this._b == 12) {// 12SHB
  9. [Dòng 191] } else if (this._b == 18) { //18Oceanbank-ocb
  10. [Dòng 245] if (this._b == 19
  11. [Dòng 245] this._b == 3
  12. [Dòng 245] this._b == 27
  13. [Dòng 245] this._b == 12) {
  14. [Dòng 280] } else if (this._b == 18) {
  15. [Dòng 311] if (this.checkBin(_val.value) && (this._b == 3
  16. [Dòng 311] this._b == 27)) {
  17. [Dòng 316] if (this._b == 3) {
  18. [Dòng 328] this.cardTypeOcean == 'ATM') {
  19. [Dòng 341] if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  20. [Dòng 361] this._b == 18)) {
  21. [Dòng 437] if (this.checkBin(v) && (this._b == 3
  22. [Dòng 683] event.inputType == 'deleteContentBackward') {
  23. [Dòng 684] if (event.target.name == 'exp_date'
  24. [Dòng 692] event.inputType == 'insertCompositionText') {
  25. [Dòng 707] if (((this.valueDate.length == 4
  26. [Dòng 707] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  27. [Dòng 707] this.valueDate.length == 5)
  28. [Dòng 787] if (temp.length == 0) {
  29. [Dòng 794] return (counter % 10 == 0);
  30. [Dòng 814] } else if (this._b == 19) {
  31. [Dòng 816] } else if (this._b == 27) {
  32. [Dòng 818] } else if (this._b == 12
  33. [Dòng 832] _formCard.exp_date.length == 5
  34. [Dòng 832] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
  35. [Dòng 832] this._b == 3)) {//27-pvcombank;3-TPB ;
  36. [Dòng 837] if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
  37. [Dòng 837] this._b == 19
  38. [Dòng 837] this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
  39. [Dòng 840] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  40. [Dòng 843] if (this.cardTypeOcean == 'IB') {
  41. [Dòng 845] } else if (this.cardTypeOcean == 'MB') {
  42. [Dòng 847] } else if (this.cardTypeOcean == 'ATM') {
  43. [Dòng 877] this.token_site == 'onepay'
  44. [Dòng 896] if (_re.status == '200'
  45. [Dòng 896] _re.status == '201') {
  46. [Dòng 901] if (this._res_post.state == 'approved'
  47. [Dòng 901] this._res_post.state == 'failed') {
  48. [Dòng 908] } else if (this._res_post.state == 'authorization_required') {
  49. [Dòng 926] if (this._b == 18) {
  50. [Dòng 931] if (this._b == 27
  51. [Dòng 931] this._b == 18) {
  52. [Dòng 1022] if ((cardNo.length == 16
  53. [Dòng 1022] if ((cardNo.length == 16 || (cardNo.length == 19
  54. [Dòng 1023] && ((this._b == 18
  55. [Dòng 1023] cardNo.length == 19) || this._b != 18)
  56. [Dòng 1036] if (this._b == +e.id) {
  57. [Dòng 1052] if (valIn == 1) {
  58. [Dòng 1054] } else if (valIn == 2) {
  59. [Dòng 1078] this._b == 3) {
  60. [Dòng 1085] if (this._b == 19) {
  61. [Dòng 1131] if (this._b == 12) {
  62. [Dòng 1148] if (cardType == this._translate.instant('internetbanking')
  63. [Dòng 1156] } else if (cardType == this._translate.instant('mobilebanking')
  64. [Dòng 1164] } else if (cardType == this._translate.instant('atm')
  65. [Dòng 1224] this._b == 18))) {
  66. [Dòng 1231] } else if (this._b == 18
  67. [Dòng 1255] this.c_expdate = !(((this.valueDate.length == 4
  68. [Dòng 1287] this.valueDate.length == 4
  69. [Dòng 1287] this.valueDate.search('/') == -1)
  70. [Dòng 1288] this.valueDate.length == 5))

!== (13 điều kiện):
  1. [Dòng 236] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 882] key !== '3') {
  3. [Dòng 932] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 950] codeResponse.toString() !== '0') {
  5. [Dòng 1002] cardNo.length !== 0) {
  6. [Dòng 1074] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 1095] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 1116] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 1136] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 1148] this.lb_card_account !== this._translate.instant('ocb_account')) {
  11. [Dòng 1156] this.lb_card_account !== this._translate.instant('ocb_phone')) {
  12. [Dòng 1164] this.lb_card_account !== this._translate.instant('ocb_card')) {
  13. [Dòng 1244] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (21 điều kiện):
  1. [Dòng 197] } else if (this._b != 18) {
  2. [Dòng 203] if (this.htmlDesc != null
  3. [Dòng 233] if (ua.indexOf('safari') != -1
  4. [Dòng 243] if (_val.value != '') {
  5. [Dòng 329] this.auth_method != null) {
  6. [Dòng 685] if (this.valueDate.length != 3) {
  7. [Dòng 832] if (_formCard.exp_date != null
  8. [Dòng 837] if (this.cardName != null
  9. [Dòng 904] if (this._res_post.links != null
  10. [Dòng 904] this._res_post.links.merchant_return != null
  11. [Dòng 904] this._res_post.links.merchant_return.href != null) {
  12. [Dòng 912] if (this._res_post.authorization != null
  13. [Dòng 912] this._res_post.authorization.links != null
  14. [Dòng 912] this._res_post.authorization.links.approval != null) {
  15. [Dòng 919] this._res_post.links.cancel != null) {
  16. [Dòng 1022] this._b != 27
  17. [Dòng 1022] this._b != 12
  18. [Dòng 1022] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  19. [Dòng 1023] this._b != 18)
  20. [Dòng 1069] if (this._b != 18
  21. [Dòng 1069] this._b != 19) {

================================================================================

📁 FILE 30: bank.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/model/bank.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 31: otp-auth.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 32: otp-auth.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
📊 Thống kê: 21 điều kiện duy nhất
   - === : 0 lần
   - == : 11 lần
   - !== : 1 lần
   - != : 9 lần
--------------------------------------------------------------------------------

== (11 điều kiện):
  1. [Dòng 99] if (this._b == 8) {//MB Bank
  2. [Dòng 103] if (this._b == 18) {//Oceanbank
  3. [Dòng 140] if (this._b == 8) {
  4. [Dòng 145] if (this._b == 18) {
  5. [Dòng 150] if (this._b == 12) { //SHB
  6. [Dòng 172] if (_re.status == '200'
  7. [Dòng 172] _re.status == '201') {
  8. [Dòng 181] } else if (this._res.state == 'authorization_required') {
  9. [Dòng 212] if (this.challengeCode == '') {
  10. [Dòng 306] if (this._b == 12) {
  11. [Dòng 363] if (this._b == 18) {//8-MB Bank;18-oceanbank

!== (1 điều kiện):
  1. [Dòng 187] codeResponse.toString() !== '0') {

!= (9 điều kiện):
  1. [Dòng 177] if (this._res.links != null
  2. [Dòng 177] this._res.links.merchant_return != null
  3. [Dòng 177] this._res.links.merchant_return.href != null) {
  4. [Dòng 333] if (this._res_post != null
  5. [Dòng 333] this._res_post.links != null
  6. [Dòng 333] this._res_post.links.merchant_return != null
  7. [Dòng 333] this._res_post.links.merchant_return.href != null) {
  8. [Dòng 358] if (!(_formCard.otp != null
  9. [Dòng 364] if (!(_formCard.password != null

================================================================================

📁 FILE 33: techcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 34: techcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 35: techcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
📊 Thống kê: 16 điều kiện duy nhất
   - === : 1 lần
   - == : 12 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 75] if (this.timeLeft === 0) {

== (12 điều kiện):
  1. [Dòng 66] if (this._b == 2
  2. [Dòng 66] this._b == 5
  3. [Dòng 66] this._b == 31) {
  4. [Dòng 105] if (this._b == 2) {
  5. [Dòng 107] } else if (this._b == 5) {
  6. [Dòng 109] } else if (this._b == 6) {
  7. [Dòng 111] } else if (this._b == 31) {
  8. [Dòng 141] if (_re.status == '200'
  9. [Dòng 141] _re.status == '201') {
  10. [Dòng 146] if (this._res_post.state == 'approved'
  11. [Dòng 146] this._res_post.state == 'failed') {
  12. [Dòng 150] } else if (this._res_post.state == 'authorization_required') {

!= (3 điều kiện):
  1. [Dòng 154] if (this._res_post.authorization != null
  2. [Dòng 154] this._res_post.authorization.links != null
  3. [Dòng 154] this._res_post.authorization.links.approval != null) {

================================================================================

📁 FILE 36: vietcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
📊 Thống kê: 12 điều kiện duy nhất
   - === : 2 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 7 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 23] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  2. [Dòng 23] _inExpDate.trim().length === 0)"

== (3 điều kiện):
  1. [Dòng 53] _b == 6"
  2. [Dòng 54] token_site == 'onepay'
  3. [Dòng 64] _b == 68"

!= (7 điều kiện):
  1. [Dòng 19] <div class="form-field form-field-small margin-right-2" [class.visible_hidden]="!valid_card" *ngIf="d_card_date && (_b != 3
  2. [Dòng 19] d_card_date && (_b != 3 && _b != 19 && _b != 18)
  3. [Dòng 19] _b != 18)">
  4. [Dòng 32] *ngIf="(_b != 18
  5. [Dòng 32] (_b != 18 && _b != 6 && _b != 2)
  6. [Dòng 32] _b != 2)">
  7. [Dòng 76] _b != 6 && _b != 2

================================================================================

📁 FILE 37: vietcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 38: vietcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
📊 Thống kê: 116 điều kiện duy nhất
   - === : 5 lần
   - == : 76 lần
   - !== : 3 lần
   - != : 32 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 119] if (target.tagName === 'A'
  2. [Dòng 401] if (event.keyCode === 8
  3. [Dòng 401] event.key === "Backspace"
  4. [Dòng 673] if (approval.method === 'REDIRECT') {
  5. [Dòng 676] } else if (approval.method === 'POST_REDIRECT') {

== (76 điều kiện):
  1. [Dòng 146] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 171] if (this._b == 1
  3. [Dòng 171] this._b == 20
  4. [Dòng 171] this._b == 36
  5. [Dòng 171] this._b == 64
  6. [Dòng 171] this._b == 55
  7. [Dòng 171] this._b == 47
  8. [Dòng 171] this._b == 48
  9. [Dòng 171] this._b == 59
  10. [Dòng 171] this._b == 67) {
  11. [Dòng 182] if (message == '1') {
  12. [Dòng 199] return this._b == 3
  13. [Dòng 199] this._b == 9
  14. [Dòng 199] this._b == 16
  15. [Dòng 199] this._b == 17
  16. [Dòng 199] this._b == 19
  17. [Dòng 199] this._b == 25
  18. [Dòng 199] this._b == 44
  19. [Dòng 200] this._b == 57
  20. [Dòng 200] this._b == 61
  21. [Dòng 200] this._b == 63
  22. [Dòng 200] this._b == 69
  23. [Dòng 204] return this._b == 6
  24. [Dòng 204] this._b == 2
  25. [Dòng 208] return this._b == 18
  26. [Dòng 216] return this._b == 11
  27. [Dòng 216] this._b == 33
  28. [Dòng 216] this._b == 39
  29. [Dòng 216] this._b == 43
  30. [Dòng 216] this._b == 45
  31. [Dòng 217] this._b == 67
  32. [Dòng 217] this._b == 72
  33. [Dòng 217] this._b == 73
  34. [Dòng 217] this._b == 68
  35. [Dòng 217] this._b == 74
  36. [Dòng 217] this._b == 75
  37. [Dòng 249] if (parseInt(b) == parseInt(v.substring(0, 6))) {
  38. [Dòng 401] event.inputType == 'deleteContentBackward') {
  39. [Dòng 402] if (event.target.name == 'exp_date'
  40. [Dòng 410] event.inputType == 'insertCompositionText') {
  41. [Dòng 491] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  42. [Dòng 502] } else if (this._b == 2) {
  43. [Dòng 504] } else if (this._b == 6) {
  44. [Dòng 506] } else if (this._b == 31) {
  45. [Dòng 530] this.token_site == 'onepay'
  46. [Dòng 534] if (this._b == 5){
  47. [Dòng 547] if(this._b == 12){
  48. [Dòng 562] if (this._res_post.state == 'approved'
  49. [Dòng 562] this._res_post.state == 'failed') {
  50. [Dòng 611] } else if (this._res_post.state == 'authorization_required') {
  51. [Dòng 633] this._b == 14
  52. [Dòng 633] this._b == 15
  53. [Dòng 633] this._b == 24
  54. [Dòng 633] this._b == 8
  55. [Dòng 633] this._b == 10
  56. [Dòng 633] this._b == 18
  57. [Dòng 633] this._b == 22
  58. [Dòng 633] this._b == 23
  59. [Dòng 633] this._b == 27
  60. [Dòng 633] this._b == 30
  61. [Dòng 633] this._b == 11
  62. [Dòng 633] this._b == 12
  63. [Dòng 633] this._b == 5
  64. [Dòng 633] this._b == 9) {
  65. [Dòng 713] if ((cardNo.length == 16
  66. [Dòng 714] (cardNo.length == 19
  67. [Dòng 714] (cardNo.length == 19 && (this._b == 1
  68. [Dòng 714] this._b == 4
  69. [Dòng 714] this._b == 67))
  70. [Dòng 716] this._util.checkMod10(cardNo) == true
  71. [Dòng 803] return ((value.length == 4
  72. [Dòng 803] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  73. [Dòng 803] value.length == 5) && parseInt(value.split('/')[0]
  74. [Dòng 836] this._inExpDate.length == 4
  75. [Dòng 836] this._inExpDate.search('/') == -1)
  76. [Dòng 837] this._inExpDate.length == 5))

!== (3 điều kiện):
  1. [Dòng 537] key !== '3') {
  2. [Dòng 575] codeResponse.toString() !== '0') {
  3. [Dòng 635] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (32 điều kiện):
  1. [Dòng 145] this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
  2. [Dòng 176] if (this.htmlDesc != null
  3. [Dòng 226] if (ua.indexOf('safari') != -1
  4. [Dòng 403] if (this._inExpDate.length != 3) {
  5. [Dòng 485] if (this._b != 3
  6. [Dòng 485] this._b != 9
  7. [Dòng 485] this._b != 16
  8. [Dòng 485] this._b != 17
  9. [Dòng 485] this._b != 18
  10. [Dòng 485] this._b != 19
  11. [Dòng 485] this._b != 25
  12. [Dòng 485] this._b != 44
  13. [Dòng 486] this._b != 57
  14. [Dòng 486] this._b != 59
  15. [Dòng 486] this._b != 61
  16. [Dòng 486] this._b != 63
  17. [Dòng 486] this._b != 69
  18. [Dòng 486] this._b != 6
  19. [Dòng 486] this._b != 2
  20. [Dòng 486] this._b != 57) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
  21. [Dòng 500] '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72' , '73', '74', '75'].indexOf(this._b.toString()) != -1) {
  22. [Dòng 564] if (this._res_post.return_url != null) {
  23. [Dòng 567] if (this._res_post.links != null
  24. [Dòng 567] this._res_post.links.merchant_return != null
  25. [Dòng 567] this._res_post.links.merchant_return.href != null) {
  26. [Dòng 616] if (this._res_post.authorization != null
  27. [Dòng 616] this._res_post.authorization.links != null
  28. [Dòng 621] this._res_post.links.cancel != null) {
  29. [Dòng 627] let userName = _formCard.name != null ? _formCard.name : ''
  30. [Dòng 628] this._res_post.authorization.links.approval != null
  31. [Dòng 628] this._res_post.authorization.links.approval.href != null) {
  32. [Dòng 631] userName = paramUserName != null ? paramUserName : ''

================================================================================

📁 FILE 39: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 40: off-bank-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 41: off-bank-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 42: domescard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/domescard-main.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 1 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 22] filteredData.length === 0"

== (4 điều kiện):
  1. [Dòng 5] _auth==1) || (token
  2. [Dòng 5] _b == 16)"
  3. [Dòng 8] (token || _auth==1) && _b != 16
  4. [Dòng 11] _auth==1)">

================================================================================

📁 FILE 43: domescard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/domescard-main/domescard-main.component.ts
📊 Thống kê: 54 điều kiện duy nhất
   - === : 23 lần
   - == : 25 lần
   - !== : 1 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (23 điều kiện):
  1. [Dòng 228] if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
  2. [Dòng 229] filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
  3. [Dòng 277] if (valOut === 'auth') {
  4. [Dòng 349] if (this._b === '1'
  5. [Dòng 349] this._b === '20'
  6. [Dòng 349] this._b === '64') {
  7. [Dòng 352] if (this._b === '36'
  8. [Dòng 352] this._b === '18'
  9. [Dòng 352] this._b === '19'
  10. [Dòng 355] if (this._b === '19'
  11. [Dòng 355] this._b === '16'
  12. [Dòng 355] this._b === '25'
  13. [Dòng 355] this._b === '33'
  14. [Dòng 356] this._b === '39'
  15. [Dòng 356] this._b === '11'
  16. [Dòng 356] this._b === '17'
  17. [Dòng 357] this._b === '36'
  18. [Dòng 357] this._b === '44'
  19. [Dòng 357] this._b === '12'
  20. [Dòng 358] this._b === '64'
  21. [Dòng 362] if (this._b === '20'
  22. [Dòng 365] if (this._b === '12'
  23. [Dòng 365] this._b === '18') {

== (25 điều kiện):
  1. [Dòng 156] this._auth == 0
  2. [Dòng 156] this.tokenList.length == 0) {
  3. [Dòng 215] this.filteredData.length == 1
  4. [Dòng 255] if ($event && ($event == 'true'
  5. [Dòng 352] this._b == '55'
  6. [Dòng 352] this._b == '47'
  7. [Dòng 352] this._b == '48'
  8. [Dòng 352] this._b == '59'
  9. [Dòng 352] this._b == '73'
  10. [Dòng 352] this._b == '67') {
  11. [Dòng 355] this._b == '3'
  12. [Dòng 356] this._b == '43'
  13. [Dòng 356] this._b == '45'
  14. [Dòng 357] this._b == '57'
  15. [Dòng 358] this._b == '61'
  16. [Dòng 358] this._b == '63'
  17. [Dòng 358] this._b == '67'
  18. [Dòng 358] this._b == '68'
  19. [Dòng 359] this._b == '69'
  20. [Dòng 359] this._b == '72'
  21. [Dòng 359] this._b == '9'
  22. [Dòng 359] this._b == '74'
  23. [Dòng 359] this._b == '75') {
  24. [Dòng 362] this._b == '36'
  25. [Dòng 382] if (item['id'] == this._b) {

!== (1 điều kiện):
  1. [Dòng 106] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (5 điều kiện):
  1. [Dòng 136] if (params['locale'] != null) {
  2. [Dòng 142] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 178] if (!(strInstrument != null
  4. [Dòng 181] if (strInstrument.substring(0, 1) != '^'
  5. [Dòng 181] strInstrument.substr(strInstrument.length - 1) != '$') {

================================================================================

📁 FILE 44: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/intercard-main/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 45: intercard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/intercard-main/intercard-main.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 6] _showAVS!=true"

================================================================================

📁 FILE 46: intercard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/intercard-main/intercard-main.component.ts
📊 Thống kê: 60 điều kiện duy nhất
   - === : 7 lần
   - == : 33 lần
   - !== : 8 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 241] if (_formCard.country === 'default') {
  2. [Dòng 504] if (event.keyCode === 8
  3. [Dòng 504] event.key === "Backspace"
  4. [Dòng 579] if ((v.substr(-1) === ' '
  5. [Dòng 736] if (deviceValue === 'CA'
  6. [Dòng 736] deviceValue === 'US') {
  7. [Dòng 757] this.c_country = _val.value === 'default'

== (33 điều kiện):
  1. [Dòng 127] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 272] this.token_site == 'onepay'
  3. [Dòng 298] if (this._res_post.state == 'approved'
  4. [Dòng 298] this._res_post.state == 'failed') {
  5. [Dòng 324] } else if (this._res_post.state == 'failed') { // lỗi mới kiểm tra sang trang lỗi
  6. [Dòng 351] } else if (this._res_post.state == 'authorization_required') {
  7. [Dòng 352] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  8. [Dòng 364] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  9. [Dòng 423] v.length == 15) || (v.length == 16
  10. [Dòng 423] v.length == 19))
  11. [Dòng 424] this._util.checkMod10(v) == true) {
  12. [Dòng 458] cardNo.length == 15)
  13. [Dòng 460] cardNo.length == 16)
  14. [Dòng 461] || (cardNo.startsWith('6') && (cardNo.length == 16
  15. [Dòng 461] cardNo.length == 19))
  16. [Dòng 504] event.inputType == 'deleteContentBackward') {
  17. [Dòng 505] if (event.target.name == 'exp_date'
  18. [Dòng 513] event.inputType == 'insertCompositionText') {
  19. [Dòng 528] if (((this.valueDate.length == 4
  20. [Dòng 528] this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  21. [Dòng 528] this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  22. [Dòng 579] v.length == 5) {
  23. [Dòng 587] v.length == 4
  24. [Dòng 591] v.length == 3)
  25. [Dòng 614] _val.value.length == 4
  26. [Dòng 618] _val.value.length == 3)
  27. [Dòng 714] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  28. [Dòng 714] this.valueDate.length == 5)
  29. [Dòng 787] this.valueDate.length == 4
  30. [Dòng 787] this.valueDate.search('/') == -1)
  31. [Dòng 788] this.valueDate.length == 5))
  32. [Dòng 801] this._i_csc.length == 4) ||
  33. [Dòng 805] this._i_csc.length == 3)

!== (8 điều kiện):
  1. [Dòng 280] key !== '8') {
  2. [Dòng 308] codeResponse.toString() !== '0'){
  3. [Dòng 579] event.inputType !== 'deleteContentBackward') || v.length == 5) {
  4. [Dòng 733] if (deviceValue !== 'default') {
  5. [Dòng 750] this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
  6. [Dòng 812] return this._i_country_code !== 'default'
  7. [Dòng 845] this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
  8. [Dòng 852] this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {

!= (12 điều kiện):
  1. [Dòng 154] if (params['locale'] != null) {
  2. [Dòng 160] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 300] if (this._res_post.return_url != null) {
  4. [Dòng 302] } else if (this._res_post.links != null
  5. [Dòng 302] this._res_post.links.merchant_return != null
  6. [Dòng 302] this._res_post.links.merchant_return.href != null) {
  7. [Dòng 405] if (ua.indexOf('safari') != -1
  8. [Dòng 458] cardNo != null
  9. [Dòng 506] if (this.valueDate.length != 3) {
  10. [Dòng 586] v != null
  11. [Dòng 613] this.c_csc = (!(_val.value != null
  12. [Dòng 799] this._i_csc != null

================================================================================

📁 FILE 47: menu.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/menu.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 74] [ngStyle]="{'border-color': type === 2 ? this.themeColor.border_color : border_color}"
  2. [Dòng 91] d_vrbank===true"

== (2 điều kiện):
  1. [Dòng 39] _auth==1)">
  2. [Dòng 76] token || _auth==1

================================================================================

📁 FILE 48: menu.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/menu.component.ts
📊 Thống kê: 136 điều kiện duy nhất
   - === : 9 lần
   - == : 66 lần
   - !== : 3 lần
   - != : 58 lần
--------------------------------------------------------------------------------

=== (9 điều kiện):
  1. [Dòng 662] this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number === 1
  2. [Dòng 702] if (this._res.state === 'unpaid'
  3. [Dòng 702] this._res.state === 'not_paid') {
  4. [Dòng 796] if ('op' === auth
  5. [Dòng 833] } else if ('bank' === auth
  6. [Dòng 838] if (approval.method === 'REDIRECT') {
  7. [Dòng 841] } else if (approval.method === 'POST_REDIRECT') {
  8. [Dòng 1084] return id === 'amex' ? '1234' : '123'
  9. [Dòng 1247] if (this.timeLeftPaypal === 0) {

== (66 điều kiện):
  1. [Dòng 191] if (!isNaN(_re.status) && (_re.status == '200'
  2. [Dòng 191] _re.status == '201') && _re.body != null) {
  3. [Dòng 196] if (('closed' == this._res_polling.state
  4. [Dòng 196] 'canceled' == this._res_polling.state
  5. [Dòng 196] 'expired' == this._res_polling.state)
  6. [Dòng 216] } else if ('paid' == this._res_polling.state) {
  7. [Dòng 222] this._res_polling.payments == null
  8. [Dòng 224] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  9. [Dòng 226] this._paymentService.getCurrentPage() == 'enter_card') {
  10. [Dòng 229] } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
  11. [Dòng 229] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
  12. [Dòng 247] } else if ('not_paid' == this._res_polling.state) {
  13. [Dòng 259] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
  14. [Dòng 266] if (!((this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '8'
  15. [Dòng 267] this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '9'
  16. [Dòng 268] this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '12'
  17. [Dòng 269] this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '13'
  18. [Dòng 270] this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '24'
  19. [Dòng 271] this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '25'))) {
  20. [Dòng 386] if (message == '1') {
  21. [Dòng 388] } else if (message == '0') {
  22. [Dòng 408] if (_re.status == '200'
  23. [Dòng 408] _re.status == '201') {
  24. [Dòng 434] if (this.type == 5
  25. [Dòng 437] } else if (this.type == 6
  26. [Dòng 440] } else if (this.type == 2
  27. [Dòng 443] } else if (this.type == 7
  28. [Dòng 446] } else if (this.type == 8
  29. [Dòng 449] } else if (this.type == 4
  30. [Dòng 452] } else if (this.type == 3
  31. [Dòng 668] this._auth == 0) {
  32. [Dòng 703] if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
  33. [Dòng 703] this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
  34. [Dòng 705] if (this._res.payments[this._res.payments.length - 1].state == 'approved'
  35. [Dòng 707] } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
  36. [Dòng 745] } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  37. [Dòng 745] this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
  38. [Dòng 782] } else if (idBrand == 'atm'
  39. [Dòng 861] if ('paid' == this._res.state) {
  40. [Dòng 862] this._res.merchant.token_site == 'onepay')) {
  41. [Dòng 883] if (('closed' == this._res.state
  42. [Dòng 883] 'canceled' == this._res.state
  43. [Dòng 883] 'expired' == this._res.state
  44. [Dòng 883] 'paid' == this._res.state)
  45. [Dòng 905] this._res.payments == null) {
  46. [Dòng 907] this._res.payments[this._res.payments.length - 1].state == 'pending') {
  47. [Dòng 917] if (this._res.currencies[0] == 'USD') {
  48. [Dòng 981] if (item.instrument.issuer.brand.id == 'atm') {
  49. [Dòng 983] } else if (item.instrument.issuer.brand.id == 'visa'
  50. [Dòng 983] item.instrument.issuer.brand.id == 'mastercard') {
  51. [Dòng 984] if (item.instrument.issuer_location == 'd') {
  52. [Dòng 989] } else if (item.instrument.issuer.brand.id == 'amex') {
  53. [Dòng 995] } else if (item.instrument.issuer.brand.id == 'jcb') {
  54. [Dòng 1011] uniq.length == 1) {
  55. [Dòng 1191] _val == 2) {
  56. [Dòng 1218] if (_val == 2
  57. [Dòng 1220] } else if (_val == 2
  58. [Dòng 1226] _val == 2
  59. [Dòng 1235] if (this.type == 4) {
  60. [Dòng 1315] if (this._res_post.state == 'approved'
  61. [Dòng 1315] this._res_post.state == 'failed') {
  62. [Dòng 1324] } else if (this._res_post.state == 'authorization_required') {
  63. [Dòng 1325] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  64. [Dòng 1339] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  65. [Dòng 1413] filteredData.length == 1) {
  66. [Dòng 1490] if (data._locale == 'en') {

!== (3 điều kiện):
  1. [Dòng 808] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  2. [Dòng 1182] if (_val !== 3) {
  3. [Dòng 1186] this._util.getElementParams(this.currentUrl, 't_id') !== '') {

!= (58 điều kiện):
  1. [Dòng 184] if (this._idInvoice != null
  2. [Dòng 184] this._paymentService.getState() != 'error') {
  3. [Dòng 190] if (this._paymentService.getCurrentPage() != 'otp') {
  4. [Dòng 191] _re.body != null) {
  5. [Dòng 197] this._res_polling.links != null
  6. [Dòng 197] this._res_polling.links.merchant_return != null //
  7. [Dòng 222] } else if (this._res_polling.merchant != null
  8. [Dòng 222] this._res_polling.merchant_invoice_reference != null
  9. [Dòng 224] } else if (this._res_polling.payments != null
  10. [Dòng 230] this._res_polling.links.merchant_return != null//
  11. [Dòng 250] this._res_polling.payments != null
  12. [Dòng 258] if (this._res_polling.payments != null
  13. [Dòng 262] t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
  14. [Dòng 371] if (params['locale'] != null) {
  15. [Dòng 379] if ('otp' != this._paymentService.getCurrentPage()) {
  16. [Dòng 400] if (this._paymentService.getInvoiceDetail() != null) {
  17. [Dòng 682] if (count != 1) {
  18. [Dòng 692] if (this._res.merchant != null
  19. [Dòng 692] this._res.merchant_invoice_reference != null) {
  20. [Dòng 695] if (this._res.merchant.address_details != null) {
  21. [Dòng 703] this._res.links != null//
  22. [Dòng 745] } else if (this._res.payments != null
  23. [Dòng 746] this._res.payments[this._res.payments.length - 1].instrument != null
  24. [Dòng 746] this._res.payments[this._res.payments.length - 1].instrument.issuer != null
  25. [Dòng 747] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
  26. [Dòng 747] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
  27. [Dòng 748] this._res.payments[this._res.payments.length - 1].links != null
  28. [Dòng 748] this._res.payments[this._res.payments.length - 1].links.cancel != null
  29. [Dòng 748] this._res.payments[this._res.payments.length - 1].links.cancel.href != null
  30. [Dòng 767] this._res.payments[this._res.payments.length - 1].links.update != null
  31. [Dòng 767] this._res.payments[this._res.payments.length - 1].links.update.href != null) {
  32. [Dòng 782] this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
  33. [Dòng 783] this._res.payments[this._res.payments.length - 1].authorization != null
  34. [Dòng 783] this._res.payments[this._res.payments.length - 1].authorization.links != null) {
  35. [Dòng 796] this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
  36. [Dòng 796] this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
  37. [Dòng 799] if (this._res.payments[this._res.payments.length - 1].authorization != null
  38. [Dòng 799] this._res.payments[this._res.payments.length - 1].authorization.links != null
  39. [Dòng 805] auth = paramUserName != null ? paramUserName : ''
  40. [Dòng 884] this._res.links != null
  41. [Dòng 884] this._res.links.merchant_return != null //
  42. [Dòng 905] } else if (this._res.merchant != null
  43. [Dòng 905] this._res.merchant_invoice_reference != null
  44. [Dòng 1019] if (['mastercard', 'visa', 'amex', 'jcb', 'cup', 'card', 'np_card', 'atm'].indexOf(id) != -1) {
  45. [Dòng 1021] } else if (['techcombank_account', 'vib_account', 'dongabank_account', 'tpbank_account', 'bidv_account', 'pvcombank_account', 'shb_account', 'oceanbank_online_account'].indexOf(id) != -1) {
  46. [Dòng 1023] } else if (['oceanbank_mobile_account'].indexOf(id) != -1) {
  47. [Dòng 1025] } else if (['shb_customer_id'].indexOf(id) != -1) {
  48. [Dòng 1051] if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1) {
  49. [Dòng 1079] return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
  50. [Dòng 1116] if (!(strInstrument != null
  51. [Dòng 1133] if (this._translate.currentLang != language) {
  52. [Dòng 1188] } else if (this._res.payments != null) {
  53. [Dòng 1317] if (this._res_post.return_url != null) {
  54. [Dòng 1319] } else if (this._res_post.links != null
  55. [Dòng 1319] this._res_post.links.merchant_return != null
  56. [Dòng 1319] this._res_post.links.merchant_return.href != null) {
  57. [Dòng 1438] if (this._res_post != null
  58. [Dòng 1438] this._res_post.links != null

================================================================================

📁 FILE 49: policy-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/policy-dialog/policy-dialog/policy-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 50: policy-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/policy-dialog/policy-dialog/policy-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 51: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 52: qr-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 53: qr-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 54: qr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/qr-main/qr-main.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 5] filteredData.length === 0
  2. [Dòng 5] filteredDataOther.length === 0"

================================================================================

📁 FILE 55: qr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/qr-main/qr-main.component.ts
📊 Thống kê: 12 điều kiện duy nhất
   - === : 5 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 176] this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
  2. [Dòng 177] this.filteredDataOther = this.appList.filter(item => item.type === 'other');
  3. [Dòng 195] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  4. [Dòng 196] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  5. [Dòng 202] if (item.type === 'mobile_banking') {

== (3 điều kiện):
  1. [Dòng 201] if (item.available == true) {
  2. [Dòng 244] if (_re.status == '200'
  3. [Dòng 244] _re.status == '201') {

!= (4 điều kiện):
  1. [Dòng 136] if (params['locale'] != null) {
  2. [Dòng 142] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 156] if (!(strInstrument != null
  4. [Dòng 225] if (appcode != null) {

================================================================================

📁 FILE 56: remove-token-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/token-main/remove-token-dialog/remove-token-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 57: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/token-main/token-dialog/dialog-guide-dialog.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (6 điều kiện):
  1. [Dòng 9] data['type'] == 'Visa'
  2. [Dòng 9] data['type'] == 'Master'
  3. [Dòng 9] data['type'] == 'JCB'"
  4. [Dòng 17] data['type'] == 'Visa'"
  5. [Dòng 17] data['type'] == 'Master'"
  6. [Dòng 24] data['type'] == 'Amex'"

================================================================================

📁 FILE 58: token-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/token-main/token-main.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 9] item.brand_id === 'visa' ? 'height: 14.42px

== (2 điều kiện):
  1. [Dòng 3] i == tokenList.length - 1"
  2. [Dòng 30] token_main == '1'"

!= (2 điều kiện):
  1. [Dòng 23] item['feeService']['fee'] != 0
  2. [Dòng 25] item['feeService']['fee'] != 0"

================================================================================

📁 FILE 59: token-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/main/menu/token-main/token-main.component.ts
📊 Thống kê: 69 điều kiện duy nhất
   - === : 7 lần
   - == : 41 lần
   - !== : 1 lần
   - != : 20 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 87] if (event.keyCode === 13) {
  2. [Dòng 107] if (target.tagName === 'A'
  3. [Dòng 255] && ((item.brand_id === 'amex'
  4. [Dòng 271] return id === 'amex' ? '1234' : '123'
  5. [Dòng 437] if (approval.method === 'REDIRECT') {
  6. [Dòng 440] } else if (approval.method === 'POST_REDIRECT') {
  7. [Dòng 512] return this._i_token_otp != null && !isNaN(+this._i_token_otp) && ((id === 'amex'

== (41 điều kiện):
  1. [Dòng 140] if (message == '0') {
  2. [Dòng 193] item.id == element.id ? element['active'] = true : element['active'] = false
  3. [Dòng 197] item.display_cvv == true ? this.flagToken = true : this.flagToken = false
  4. [Dòng 237] if (result == 'success') {
  5. [Dòng 242] if (this.tokenList.length == 0) {
  6. [Dòng 246] } else if (result == 'error') {
  7. [Dòng 255] _val.value.trim().length == 4) || (item.brand_id != 'amex'
  8. [Dòng 255] _val.value.trim().length == 3))
  9. [Dòng 264] _val.value.length == 4) || (item.brand_id != 'amex'
  10. [Dòng 264] _val.value.length == 3)));
  11. [Dòng 270] id == 'amex' ? this.maxLength = 4 : this.maxLength = 3
  12. [Dòng 316] if (_re.body.state == 'more_info_required') {
  13. [Dòng 331] if (this._res_post.state == 'approved'
  14. [Dòng 331] this._res_post.state == 'failed') {
  15. [Dòng 338] if (this._res_post.state == 'failed') {
  16. [Dòng 354] } else if (this._res_post.state == 'authorization_required') {
  17. [Dòng 355] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  18. [Dòng 367] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  19. [Dòng 383] } else if (_re.body.state == 'authorization_required') {
  20. [Dòng 400] if (this._b == 1
  21. [Dòng 400] this._b == 14
  22. [Dòng 400] this._b == 15
  23. [Dòng 400] this._b == 24
  24. [Dòng 400] this._b == 8
  25. [Dòng 400] this._b == 10
  26. [Dòng 400] this._b == 20
  27. [Dòng 400] this._b == 22
  28. [Dòng 400] this._b == 23
  29. [Dòng 400] this._b == 30
  30. [Dòng 400] this._b == 11
  31. [Dòng 400] this._b == 17
  32. [Dòng 400] this._b == 18
  33. [Dòng 400] this._b == 27
  34. [Dòng 400] this._b == 9
  35. [Dòng 400] this._b == 12) {
  36. [Dòng 459] } else if (_re.body.state == 'failed') {
  37. [Dòng 504] if (action == 'blur') {
  38. [Dòng 512] this._i_token_otp.trim().length == 4)
  39. [Dòng 513] this._i_token_otp.trim().length == 3));
  40. [Dòng 566] if (_re.status == '200'
  41. [Dòng 566] _re.status == '201') {

!== (1 điều kiện):
  1. [Dòng 183] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (20 điều kiện):
  1. [Dòng 88] this._res.merchant.id != 'OPTEST' ? this._res.on_off_bank : ''
  2. [Dòng 133] if (params['locale'] != null) {
  3. [Dòng 254] if (_val.value != null
  4. [Dòng 263] this.c_token_otp_csc = !(_val.value != null
  5. [Dòng 333] if (this._res_post.return_url != null) {
  6. [Dòng 335] } else if (this._res_post.links != null
  7. [Dòng 335] this._res_post.links.merchant_return != null
  8. [Dòng 335] this._res_post.links.merchant_return.href != null) {
  9. [Dòng 388] if (_re.body.authorization != null
  10. [Dòng 388] _re.body.authorization.links != null
  11. [Dòng 395] if (_re.body.links != null
  12. [Dòng 395] _re.body.links.cancel != null) {
  13. [Dòng 461] if (_re.body.return_url != null) {
  14. [Dòng 463] } else if (_re.body.links != null
  15. [Dòng 463] _re.body.links.merchant_return != null
  16. [Dòng 463] _re.body.links.merchant_return.href != null) {
  17. [Dòng 495] return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
  18. [Dòng 500] if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(item.brand_id) != -1
  19. [Dòng 512] return this._i_token_otp != null
  20. [Dòng 513] || (id != 'amex'

================================================================================

📁 FILE 60: overlay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/overlay/overlay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 61: overlay.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/overlay/overlay.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 62: overlay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/overlay/overlay.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 63: bank-amount.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/pipe/bank-amount.pipe.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 11] return ((a.id == id
  2. [Dòng 11] a.code == id) && a.type.includes(type));

================================================================================

📁 FILE 64: close-dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/services/close-dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 65: data.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/services/data.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 82] const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
  2. [Dòng 82] item.method === method) : null;

================================================================================

📁 FILE 66: dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/services/dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 67: fee.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/services/fee.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 68: focus-input.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/services/focus-input.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 69: multiple_method.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/services/multiple_method.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 70: payment.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/services/payment.service.ts
📊 Thống kê: 8 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 8 lần
--------------------------------------------------------------------------------

!= (8 điều kiện):
  1. [Dòng 106] if (idInvoice != null
  2. [Dòng 106] idInvoice != 0)
  3. [Dòng 116] idInvoice != 0) {
  4. [Dòng 253] if (this._merchantid != null
  5. [Dòng 253] this._tranref != null
  6. [Dòng 253] this._state != null
  7. [Dòng 324] let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
  8. [Dòng 357] urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');

================================================================================

📁 FILE 71: token-main.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/services/token-main.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 72: index.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/translate/index.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 73: lang-en.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/translate/lang-en.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 74: lang-vi.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/translate/lang-vi.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 75: translate.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/translate/translate.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 76: translate.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/translate/translate.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 37] if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
  2. [Dòng 38] else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';

================================================================================

📁 FILE 77: translations.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/translate/translations.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 78: apps-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/util/apps-info.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 149] if (e.name == bankSwift) { // TODO: get by swift

================================================================================

📁 FILE 79: banks-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/util/banks-info.ts
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (3 điều kiện):
  1. [Dòng 1015] if (+e.id == bankId) {
  2. [Dòng 1057] if (parseInt(b) == number) {
  3. [Dòng 1079] if (e.swiftCode == bankSwift) {

================================================================================

📁 FILE 80: util.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/app/util/util.ts
📊 Thống kê: 39 điều kiện duy nhất
   - === : 15 lần
   - == : 17 lần
   - !== : 3 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (15 điều kiện):
  1. [Dòng 65] if (v.length === 2
  2. [Dòng 65] this.flag.length === 3
  3. [Dòng 65] this.flag.charAt(this.flag.length - 1) === '/') {
  4. [Dòng 69] if (v.length === 1) {
  5. [Dòng 71] } else if (v.length === 2) {
  6. [Dòng 74] v.length === 2) {
  7. [Dòng 82] if (len === 2) {
  8. [Dòng 154] if (M[1] === 'Chrome') {
  9. [Dòng 279] if (param === key) {
  10. [Dòng 402] pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
  11. [Dòng 406] target === 0
  12. [Dòng 479] if (cardTypeBank === 'bank_card_number') {
  13. [Dòng 482] } else if (cardTypeBank === 'bank_account_number') {
  14. [Dòng 532] if (event.keyCode === 8
  15. [Dòng 532] event.key === "Backspace"

== (17 điều kiện):
  1. [Dòng 15] if (temp.length == 0) {
  2. [Dòng 22] return (counter % 10 == 0);
  3. [Dòng 128] if (this.checkCount == 1) {
  4. [Dòng 140] if (results == null) {
  5. [Dòng 173] if (c.length == 3) {
  6. [Dòng 186] d = d == undefined ? '.' : d
  7. [Dòng 187] t = t == undefined ? '
  8. [Dòng 267] return results == null ? null : results[1]
  9. [Dòng 532] event.inputType == 'deleteContentBackward') {
  10. [Dòng 533] if (event.target.name == 'exp_date'
  11. [Dòng 541] event.inputType == 'insertCompositionText') {
  12. [Dòng 555] if (((_val.length == 4
  13. [Dòng 555] _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  14. [Dòng 555] _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  15. [Dòng 581] iss_date.length == 4
  16. [Dòng 581] iss_date.search('/') == -1)
  17. [Dòng 582] iss_date.length == 5))

!== (3 điều kiện):
  1. [Dòng 274] queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
  2. [Dòng 275] if (queryString !== '') {
  3. [Dòng 406] if (target !== 0

!= (4 điều kiện):
  1. [Dòng 156] if (tem != null) {
  2. [Dòng 161] if ((tem = ua.match(/version\/(\d+)/i)) != null) {
  3. [Dòng 477] if (ua.indexOf('safari') != -1
  4. [Dòng 534] if (v.length != 3) {

================================================================================

📁 FILE 81: qrcode.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/assets/script/qrcode.js
📊 Thống kê: 67 điều kiện duy nhất
   - === : 2 lần
   - == : 53 lần
   - !== : 0 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 2255] if (typeof define === 'function'
  2. [Dòng 2257] } else if (typeof exports === 'object') {

== (53 điều kiện):
  1. [Dòng 68] if (_dataCache == null) {
  2. [Dòng 85] if ( (0 <= r && r <= 6 && (c == 0
  3. [Dòng 85] c == 6) )
  4. [Dòng 86] || (0 <= c && c <= 6 && (r == 0
  5. [Dòng 86] r == 6) )
  6. [Dòng 107] if (i == 0
  7. [Dòng 122] _modules[r][6] = (r % 2 == 0);
  8. [Dòng 129] _modules[6][c] = (c % 2 == 0);
  9. [Dòng 152] if (r == -2
  10. [Dòng 152] r == 2
  11. [Dòng 152] c == -2
  12. [Dòng 152] c == 2
  13. [Dòng 153] || (r == 0
  14. [Dòng 153] c == 0) ) {
  15. [Dòng 169] ( (bits >> i) & 1) == 1);
  16. [Dòng 226] if (col == 6) col -= 1;
  17. [Dòng 232] if (_modules[row][col - c] == null) {
  18. [Dòng 237] dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
  19. [Dòng 249] if (bitIndex == -1) {
  20. [Dòng 458] margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
  21. [Dòng 498] if (typeof arguments[0] == 'object') {
  22. [Dòng 587] margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
  23. [Dòng 733] if (b == -1) throw 'eof';
  24. [Dòng 741] if (b0 == -1) break;
  25. [Dòng 767] if (typeof b == 'number') {
  26. [Dòng 768] if ( (b & 0xff) == b) {
  27. [Dòng 910] return function(i, j) { return (i + j) % 2 == 0
  28. [Dòng 912] return function(i, j) { return i % 2 == 0
  29. [Dòng 914] return function(i, j) { return j % 3 == 0
  30. [Dòng 916] return function(i, j) { return (i + j) % 3 == 0
  31. [Dòng 918] return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
  32. [Dòng 920] return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
  33. [Dòng 922] return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
  34. [Dòng 924] return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
  35. [Dòng 1011] if (r == 0
  36. [Dòng 1011] c == 0) {
  37. [Dòng 1015] if (dark == qrcode.isDark(row + r, col + c) ) {
  38. [Dòng 1036] if (count == 0
  39. [Dòng 1036] count == 4) {
  40. [Dòng 1149] if (typeof num.length == 'undefined') {
  41. [Dòng 1155] num[offset] == 0) {
  42. [Dòng 1495] if (typeof rsBlock == 'undefined') {
  43. [Dòng 1538] return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
  44. [Dòng 1543] _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
  45. [Dòng 1599] if (data.length - i == 1) {
  46. [Dòng 1601] } else if (data.length - i == 2) {
  47. [Dòng 1866] } else if (n == 62) {
  48. [Dòng 1868] } else if (n == 63) {
  49. [Dòng 1928] if (_buflen == 0) {
  50. [Dòng 1937] if (c == '=') {
  51. [Dòng 1961] } else if (c == 0x2b) {
  52. [Dòng 1963] } else if (c == 0x2f) {
  53. [Dòng 2133] if (table.size() == (1 << bitLength) ) {

!= (12 điều kiện):
  1. [Dòng 119] if (_modules[r][6] != null) {
  2. [Dòng 126] if (_modules[6][c] != null) {
  3. [Dòng 144] if (_modules[row][col] != null) {
  4. [Dòng 365] while (buffer.getLengthInBits() % 8 != 0) {
  5. [Dòng 750] if (count != numChars) {
  6. [Dòng 751] throw count + ' != ' + numChars
  7. [Dòng 878] while (data != 0) {
  8. [Dòng 1733] if (test.length != 2
  9. [Dòng 1733] ( (test[0] << 8) | test[1]) != code) {
  10. [Dòng 1894] if (_length % 3 != 0) {
  11. [Dòng 2067] if ( (data >>> length) != 0) {
  12. [Dòng 2178] return typeof _map[key] != 'undefined'

================================================================================

📁 FILE 82: environment.prod.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/environments/environment.prod.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 83: environment.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/environments/environment.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 84: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/acc_bidv/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 85: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/acc_bidv/script.js
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 9] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 12] if (inName.value === "") return err("MISSING_FIELD"
  3. [Dòng 21] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 23] if (e !== null) {
  2. [Dòng 55] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 86: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/acc_oceanbank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 87: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/acc_oceanbank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 10] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 19] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 21] if (e !== null) {
  2. [Dòng 53] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 88: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/acc_pvcombank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 89: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/acc_pvcombank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 7] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 16] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 18] if (e !== null) {
  2. [Dòng 50] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 90: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/acc_shb/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 91: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/acc_shb/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 10] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 19] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 21] if (e !== null) {
  2. [Dòng 53] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 92: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/acc_tpbank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 93: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/acc_tpbank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 7] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 16] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 18] if (e !== null) {
  2. [Dòng 50] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 94: common.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/common.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 89] if (i % 2 === parity) d *= 2;
  2. [Dòng 93] return (sum % 10) === 0
  3. [Dòng 163] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 166] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 252] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 258] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 274] if (xhr.status === 200
  8. [Dòng 274] xhr.status === 201) {
  9. [Dòng 277] if (invoiceState === "unpaid"
  10. [Dòng 277] invoiceState === "not_paid") {
  11. [Dòng 282] if (paymentState === "authorization_required") {
  12. [Dòng 288] if (method === "REDIRECT") {
  13. [Dòng 291] } else if (method === "POST_REDIRECT") {
  14. [Dòng 330] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 141] this.oldValue !== this.value) {
  2. [Dòng 313] if (jLinks !== undefined
  3. [Dòng 313] jLinks !== null) {
  4. [Dòng 315] if (jMerchantReturn !== undefined
  5. [Dòng 315] jMerchantReturn !== null) {
  6. [Dòng 330] if (responseCode !== undefined
  7. [Dòng 330] responseCode !== null
  8. [Dòng 358] if (parentRes !== "{

================================================================================

📁 FILE 95: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/domestic_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 96: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/domestic_card/script.js
📊 Thống kê: 14 điều kiện duy nhất
   - === : 11 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 18] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 60] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 63] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 72] year === y
  5. [Dòng 74] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 106] if (inPhone.value === "") return err("MISSING_FIELD"
  7. [Dòng 136] if ("PAY" === operation) {
  8. [Dòng 525] } else if (value === "") {
  9. [Dòng 542] if (trPhone.style.display === "") {
  10. [Dòng 544] } else if (trName.style.display === "") {
  11. [Dòng 565] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 138] if (e !== null) {
  2. [Dòng 404] if (lang !== "vi") lang = "en";
  3. [Dòng 470] if (value !== "") {

================================================================================

📁 FILE 97: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/domestic_token/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 98: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/domestic_token/script.js
📊 Thống kê: 25 điều kiện duy nhất
   - === : 23 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (23 điều kiện):
  1. [Dòng 20] if ("PAY" === operation) {
  2. [Dòng 93] if (trName.style.display === "") {
  3. [Dòng 117] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  4. [Dòng 123] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  5. [Dòng 137] if (xhr.status === 200) {
  6. [Dòng 165] if (insType === "card") {
  7. [Dòng 166] if (insBrandId === "visa"
  8. [Dòng 166] insBrandId === "mastercard"
  9. [Dòng 166] insBrandId === "amex"
  10. [Dòng 166] insBrandId === "jcb"
  11. [Dòng 166] insBrandId === "cup") {
  12. [Dòng 170] } else if (insBrandId === "atm") {
  13. [Dòng 241] } else if (insType === "dongabank_account") {
  14. [Dòng 242] } else if (insType === "techcombank_account") {
  15. [Dòng 243] } else if (insType === "vib_account") {
  16. [Dòng 244] } else if (insType === "bidv_account") {
  17. [Dòng 245] } else if (insType === "tpbank_account") {
  18. [Dòng 246] } else if (insType === "shb_account") {
  19. [Dòng 247] } else if (insType === "shb_customer_id") {
  20. [Dòng 248] } else if (insType === "vpbank_account") {
  21. [Dòng 249] } else if (insType === "oceanbank_online_account") {
  22. [Dòng 250] } else if (insType === "oceanbank_mobile_account") {
  23. [Dòng 251] } else if (insType === "pvcombank_account") {

!== (2 điều kiện):
  1. [Dòng 54] if (lang !== "vi") lang = "en";
  2. [Dòng 273] if (parentRes !== "{

================================================================================

📁 FILE 99: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 100: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/international_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 101: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/international_card/script.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 13] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 23] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 26] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 38] year === y
  5. [Dòng 40] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 71] if ("PAY" === operation) {
  7. [Dòng 161] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 73] if (e !== null) {
  2. [Dòng 118] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 102: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/international_token/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 103: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/international_token/script.js
📊 Thống kê: 36 điều kiện duy nhất
   - === : 25 lần
   - == : 0 lần
   - !== : 11 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (25 điều kiện):
  1. [Dòng 32] year === y
  2. [Dòng 34] if (inCvv.value === "") {
  3. [Dòng 67] if ("PAY" === operation) {
  4. [Dòng 129] } else if (value === "") {
  5. [Dòng 190] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 196] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 209] if (xhr.status === 200) {
  8. [Dòng 245] if (insType === "card") {
  9. [Dòng 246] if (insBrandId === "visa"
  10. [Dòng 246] insBrandId === "mastercard"
  11. [Dòng 246] insBrandId === "amex"
  12. [Dòng 246] insBrandId === "jcb"
  13. [Dòng 246] insBrandId === "cup") {
  14. [Dòng 248] } else if (insBrandId === "atm") {
  15. [Dòng 319] } else if (insType === "dongabank_account") {
  16. [Dòng 320] } else if (insType === "techcombank_account") {
  17. [Dòng 321] } else if (insType === "vib_account") {
  18. [Dòng 322] } else if (insType === "bidv_account") {
  19. [Dòng 323] } else if (insType === "tpbank_account") {
  20. [Dòng 324] } else if (insType === "shb_account") {
  21. [Dòng 325] } else if (insType === "shb_customer_id") {
  22. [Dòng 326] } else if (insType === "vpbank_account") {
  23. [Dòng 327] } else if (insType === "oceanbank_online_account") {
  24. [Dòng 328] } else if (insType === "oceanbank_mobile_account") {
  25. [Dòng 329] } else if (insType === "pvcombank_account") {

!== (11 điều kiện):
  1. [Dòng 18] if (inMonth.value !== ""
  2. [Dòng 21] if (inYear.value !== ""
  3. [Dòng 23] var month = inMonth.value !== ""
  4. [Dòng 24] var year = parseInt("20" + (inYear.value !== ""
  5. [Dòng 35] if ("2D" !== order["vpc_VerType"]) return err("MISSING_FIELD"
  6. [Dòng 71] if (e !== null) {
  7. [Dòng 79] inMonth.value !== tokenInsMonth) instrument["month"] = inMonth.value;
  8. [Dòng 80] inYear.value !== tokenInsYear) instrument["year"] = inYear.value;
  9. [Dòng 81] if (inCvv.value !== "") instrument["cvv"] = inCvv.value;
  10. [Dòng 95] if (lang !== "vi") lang = "en";
  11. [Dòng 351] if (parentRes !== "{

================================================================================

📁 FILE 104: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/amway/script.js
📊 Thống kê: 54 điều kiện duy nhất
   - === : 41 lần
   - == : 1 lần
   - !== : 12 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (41 điều kiện):
  1. [Dòng 4] if ((cardno.length === 15
  2. [Dòng 4] cardno.length === 16
  3. [Dòng 4] cardno.length === 19) && isMode10(cardno) === true) {
  4. [Dòng 4] isMode10(cardno) === true) {
  5. [Dòng 23] if (i % 2 === parity) d *= 2;
  6. [Dòng 27] return (sum % 10) === 0
  7. [Dòng 48] if ("PAY" === operation) {
  8. [Dòng 69] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  9. [Dòng 75] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  10. [Dòng 91] if (xhr.status === 200
  11. [Dòng 91] xhr.status === 201) {
  12. [Dòng 94] if (invoiceState === "unpaid"
  13. [Dòng 94] invoiceState === "not_paid") {
  14. [Dòng 99] if (paymentState === "authorization_required") {
  15. [Dòng 105] if (method === "REDIRECT") {
  16. [Dòng 110] } else if (method === "POST_REDIRECT") {
  17. [Dòng 223] if (typeof queryParams[key] === "undefined") {
  18. [Dòng 226] } else if (typeof queryParams[key] === "string") {
  19. [Dòng 256] if (params["CardList"] === undefined
  20. [Dòng 256] params["CardList"] === null) return;
  21. [Dòng 302] if (xhr.status === 200) {
  22. [Dòng 312] if (insType === "card") {
  23. [Dòng 313] if (insBrandId === "visa"
  24. [Dòng 313] insBrandId === "mastercard"
  25. [Dòng 313] insBrandId === "amex"
  26. [Dòng 313] insBrandId === "jcb"
  27. [Dòng 313] insBrandId === "cup") {
  28. [Dòng 317] } else if (insBrandId === "atm") {
  29. [Dòng 318] if (insSwiftCode === "BFTVVNVX") { //Vietcombank
  30. [Dòng 322] } else if (insSwiftCode === "BIDVVNVX") { //BIDV
  31. [Dòng 329] } else if (insType === "dongabank_account") {
  32. [Dòng 331] } else if (insType === "techcombank_account") {
  33. [Dòng 333] } else if (insType === "vib_account") {
  34. [Dòng 335] } else if (insType === "bidv_account") {
  35. [Dòng 336] } else if (insType === "tpbank_account") {
  36. [Dòng 337] } else if (insType === "shb_account") {
  37. [Dòng 338] } else if (insType === "shb_customer_id") {
  38. [Dòng 339] } else if (insType === "vpbank_account") {
  39. [Dòng 340] } else if (insType === "oceanbank_online_account") {
  40. [Dòng 341] } else if (insType === "oceanbank_mobile_account") {
  41. [Dòng 342] } else if (insType === "pvcombank_account") {

== (1 điều kiện):
  1. [Dòng 270] if (cardList.length == 1) {//TOKEN, BIDV, SHB, OCEAN, PVCOM, TP Bank

!== (12 điều kiện):
  1. [Dòng 52] if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
  2. [Dòng 53] if (inNumber.style.display !== "none") instrument.number = inNumber.value;
  3. [Dòng 54] if (inDate.style.display !== "none") instrument.date = inDate.value;
  4. [Dòng 55] if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
  5. [Dòng 56] if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
  6. [Dòng 57] if (inName.style.display !== "none") instrument.name = inName.value;
  7. [Dòng 132] if (jLinks !== undefined
  8. [Dòng 132] jLinks !== null) {
  9. [Dòng 134] if (jMerchantReturn !== undefined
  10. [Dòng 134] jMerchantReturn !== null) {
  11. [Dòng 166] if (parentRes !== "{
  12. [Dòng 255] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 105: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 106: bidv.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Bidv/assets/js/bidv.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 233] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 239] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 255] if (xhr.status === 200
  8. [Dòng 255] xhr.status === 201) {
  9. [Dòng 258] if (invoiceState === "unpaid"
  10. [Dòng 258] invoiceState === "not_paid") {
  11. [Dòng 263] if (paymentState === "authorization_required") {
  12. [Dòng 269] if (method === "REDIRECT") {
  13. [Dòng 272] } else if (method === "POST_REDIRECT") {
  14. [Dòng 311] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 294] if (jLinks !== undefined
  3. [Dòng 294] jLinks !== null) {
  4. [Dòng 296] if (jMerchantReturn !== undefined
  5. [Dòng 296] jMerchantReturn !== null) {
  6. [Dòng 311] if (responseCode !== undefined
  7. [Dòng 311] responseCode !== null
  8. [Dòng 339] if (parentRes !== "{

================================================================================

📁 FILE 107: bidv2.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Bidv/bidv2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 76] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 88] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 78] if ( $('.circle_v1').css('display') == 'block'
  2. [Dòng 112] if ($('.circle_v2').css('display') == 'block'
  3. [Dòng 112] $('.circle_v3').css('display') == 'block' ) {
  4. [Dòng 302] $('.circle_v1').css('display') == 'block'

!== (3 điều kiện):
  1. [Dòng 90] if (e !== null) {
  2. [Dòng 273] if (lang !== "vi") lang = "en";
  3. [Dòng 305] if (value !== "") {

================================================================================

📁 FILE 108: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Bidv/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 109: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 110: ocean.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Ocean/assets/js/ocean.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 232] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 237] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 251] if (xhr.status === 200
  8. [Dòng 251] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 257] if (paymentState === "authorization_required") {
  12. [Dòng 261] if (method === "REDIRECT") {
  13. [Dòng 264] } else if (method === "POST_REDIRECT") {
  14. [Dòng 303] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 286] if (jLinks !== undefined
  3. [Dòng 286] jLinks !== null) {
  4. [Dòng 288] if (jMerchantReturn !== undefined
  5. [Dòng 288] jMerchantReturn !== null) {
  6. [Dòng 303] if (responseCode !== undefined
  7. [Dòng 303] responseCode !== null
  8. [Dòng 331] if (parentRes !== "{

================================================================================

📁 FILE 111: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Ocean/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 112: ocean2.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Ocean/ocean2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 72] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 92] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 74] document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM'
  2. [Dòng 116] if ( document.getElementsByClassName("select_online")[0].value == 'Easy Online Banking') {
  3. [Dòng 119] if ( document.getElementsByClassName("select_online")[0].value == 'Easy Mobile Banking') {
  4. [Dòng 304] if ( document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM') {

!== (3 điều kiện):
  1. [Dòng 94] if (e !== null) {
  2. [Dòng 276] if (lang !== "vi") lang = "en";
  3. [Dòng 306] if (value !== "") {

================================================================================

📁 FILE 113: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 114: pvbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Pv_Bank/assets/js/pvbank.js
📊 Thống kê: 23 điều kiện duy nhất
   - === : 14 lần
   - == : 1 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 239] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 261] if (xhr.status === 200
  8. [Dòng 261] xhr.status === 201) {
  9. [Dòng 264] if (invoiceState === "unpaid"
  10. [Dòng 264] invoiceState === "not_paid") {
  11. [Dòng 269] if (paymentState === "authorization_required") {
  12. [Dòng 275] if (method === "REDIRECT") {
  13. [Dòng 278] } else if (method === "POST_REDIRECT") {
  14. [Dòng 317] responseCode === "0") {

== (1 điều kiện):
  1. [Dòng 167] if ($('.circle_v1').css('display') == 'block') {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 300] if (jLinks !== undefined
  3. [Dòng 300] jLinks !== null) {
  4. [Dòng 302] if (jMerchantReturn !== undefined
  5. [Dòng 302] jMerchantReturn !== null) {
  6. [Dòng 317] if (responseCode !== undefined
  7. [Dòng 317] responseCode !== null
  8. [Dòng 345] if (parentRes !== "{

================================================================================

📁 FILE 115: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Pv_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 116: pvbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Pv_Bank/pvbank2.js
📊 Thống kê: 15 điều kiện duy nhất
   - === : 8 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 71] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 76] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 79] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 88] year === y
  5. [Dòng 90] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 100] if ("PAY" === operation) {
  7. [Dòng 347] } else if (value === "") {
  8. [Dòng 364] if (trName.style.display === "") {

== (4 điều kiện):
  1. [Dòng 73] if ($('.circle_v1').css('display') == 'block'
  2. [Dòng 75] $('.circle_v1').css('display') == 'block'
  3. [Dòng 124] if ( $('.circle_v1').css('display') == 'block') {
  4. [Dòng 129] else if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 102] if (e !== null) {
  2. [Dòng 286] if (lang !== "vi") lang = "en";
  3. [Dòng 316] if (value !== "") {

================================================================================

📁 FILE 117: Sea2.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/SeaBank/Sea2.js
📊 Thống kê: 11 điều kiện duy nhất
   - === : 8 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 23] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 31] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 34] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 43] year === y
  5. [Dòng 45] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 55] if ("PAY" === operation) {
  7. [Dòng 287] } else if (value === "") {
  8. [Dòng 304] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 57] if (e !== null) {
  2. [Dòng 233] if (lang !== "vi") lang = "en";
  3. [Dòng 263] if (value !== "") {

================================================================================

📁 FILE 118: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 119: Sea.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/SeaBank/assets/js/Sea.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 228] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 234] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 258] if (paymentState === "authorization_required") {
  12. [Dòng 264] if (method === "REDIRECT") {
  13. [Dòng 267] } else if (method === "POST_REDIRECT") {
  14. [Dòng 306] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 289] if (jLinks !== undefined
  3. [Dòng 289] jLinks !== null) {
  4. [Dòng 291] if (jMerchantReturn !== undefined
  5. [Dòng 291] jMerchantReturn !== null) {
  6. [Dòng 306] if (responseCode !== undefined
  7. [Dòng 306] responseCode !== null
  8. [Dòng 334] if (parentRes !== "{

================================================================================

📁 FILE 120: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/SeaBank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 121: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 122: shb.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Shb/assets/js/shb.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 234] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 240] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 256] if (xhr.status === 200
  8. [Dòng 256] xhr.status === 201) {
  9. [Dòng 259] if (invoiceState === "unpaid"
  10. [Dòng 259] invoiceState === "not_paid") {
  11. [Dòng 264] if (paymentState === "authorization_required") {
  12. [Dòng 270] if (method === "REDIRECT") {
  13. [Dòng 273] } else if (method === "POST_REDIRECT") {
  14. [Dòng 312] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 295] if (jLinks !== undefined
  3. [Dòng 295] jLinks !== null) {
  4. [Dòng 297] if (jMerchantReturn !== undefined
  5. [Dòng 297] jMerchantReturn !== null) {
  6. [Dòng 312] if (responseCode !== undefined
  7. [Dòng 312] responseCode !== null
  8. [Dòng 340] if (parentRes !== "{

================================================================================

📁 FILE 123: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Shb/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 124: shb2.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Shb/shb2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 73] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 85] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 74] if ($('.circle_v2').css('display') == 'block'
  2. [Dòng 110] if ($('.circle_v1').css('display') == 'block'
  3. [Dòng 114] if ( $('.circle_v3').css('display') == 'block' ) {
  4. [Dòng 299] if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 87] if (e !== null) {
  2. [Dòng 271] if (lang !== "vi") lang = "en";
  3. [Dòng 301] if (value !== "") {

================================================================================

📁 FILE 125: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 126: tpbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Tp_Bank/assets/js/tpbank.js
📊 Thống kê: 23 điều kiện duy nhất
   - === : 14 lần
   - == : 1 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 239] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 261] if (xhr.status === 200
  8. [Dòng 261] xhr.status === 201) {
  9. [Dòng 264] if (invoiceState === "unpaid"
  10. [Dòng 264] invoiceState === "not_paid") {
  11. [Dòng 269] if (paymentState === "authorization_required") {
  12. [Dòng 275] if (method === "REDIRECT") {
  13. [Dòng 278] } else if (method === "POST_REDIRECT") {
  14. [Dòng 317] responseCode === "0") {

== (1 điều kiện):
  1. [Dòng 167] if ($('.circle_v1').css('display') == 'block') {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 300] if (jLinks !== undefined
  3. [Dòng 300] jLinks !== null) {
  4. [Dòng 302] if (jMerchantReturn !== undefined
  5. [Dòng 302] jMerchantReturn !== null) {
  6. [Dòng 317] if (responseCode !== undefined
  7. [Dòng 317] responseCode !== null
  8. [Dòng 345] if (parentRes !== "{

================================================================================

📁 FILE 127: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Tp_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 128: tpbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Tp_Bank/tpbank2.js
📊 Thống kê: 15 điều kiện duy nhất
   - === : 8 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 71] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 78] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 81] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 90] year === y
  5. [Dòng 92] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 102] if ("PAY" === operation) {
  7. [Dòng 349] } else if (value === "") {
  8. [Dòng 366] if (trName.style.display === "") {

== (4 điều kiện):
  1. [Dòng 73] if ($('.circle_v1').css('display') == 'block'
  2. [Dòng 76] $('.circle_v1').css('display') == 'block'
  3. [Dòng 126] if ( $('.circle_v1').css('display') == 'block') {
  4. [Dòng 131] else if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 104] if (e !== null) {
  2. [Dòng 288] if (lang !== "vi") lang = "en";
  3. [Dòng 318] if (value !== "") {

================================================================================

📁 FILE 129: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 130: onepay.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Visa/assets/js/onepay.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 78] if (i % 2 === parity) d *= 2;
  2. [Dòng 82] return (sum % 10) === 0
  3. [Dòng 152] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 155] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 240] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 259] if (xhr.status === 200
  8. [Dòng 259] xhr.status === 201) {
  9. [Dòng 261] if (invoiceState === "unpaid"
  10. [Dòng 261] invoiceState === "not_paid") {
  11. [Dòng 265] if (paymentState === "authorization_required") {
  12. [Dòng 269] if (method === "REDIRECT") {
  13. [Dòng 272] } else if (method === "POST_REDIRECT") {
  14. [Dòng 311] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 130] this.oldValue !== this.value) {
  2. [Dòng 294] if (jLinks !== undefined
  3. [Dòng 294] jLinks !== null) {
  4. [Dòng 296] if (jMerchantReturn !== undefined
  5. [Dòng 296] jMerchantReturn !== null) {
  6. [Dòng 311] if (responseCode !== undefined
  7. [Dòng 311] responseCode !== null
  8. [Dòng 339] if (parentRes !== "{

================================================================================

📁 FILE 131: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Visa/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 132: index.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Visa/index.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 19] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 29] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 32] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 44] year === y
  5. [Dòng 46] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 77] if ("PAY" === operation) {
  7. [Dòng 168] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 79] if (e !== null) {
  2. [Dòng 125] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 133: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 134: vpbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Vp_Bank/assets/js/vpbank.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 230] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 236] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 252] if (xhr.status === 200
  8. [Dòng 252] xhr.status === 201) {
  9. [Dòng 255] if (invoiceState === "unpaid"
  10. [Dòng 255] invoiceState === "not_paid") {
  11. [Dòng 260] if (paymentState === "authorization_required") {
  12. [Dòng 266] if (method === "REDIRECT") {
  13. [Dòng 269] } else if (method === "POST_REDIRECT") {
  14. [Dòng 308] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 291] if (jLinks !== undefined
  3. [Dòng 291] jLinks !== null) {
  4. [Dòng 293] if (jMerchantReturn !== undefined
  5. [Dòng 293] jMerchantReturn !== null) {
  6. [Dòng 308] if (responseCode !== undefined
  7. [Dòng 308] responseCode !== null
  8. [Dòng 336] if (parentRes !== "{

================================================================================

📁 FILE 135: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Vp_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 136: vpbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/Vp_Bank/vpbank2.js
📊 Thống kê: 14 điều kiện duy nhất
   - === : 11 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 32] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 35] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 44] year === y
  5. [Dòng 46] if (inPhone.value === "") return err("MISSING_FIELD"
  6. [Dòng 49] if (inName.value === "") return err("MISSING_FIELD"
  7. [Dòng 59] if ("PAY" === operation) {
  8. [Dòng 292] } else if (value === "") {
  9. [Dòng 309] if (trPhone.style.display === "") {
  10. [Dòng 311] } else if (trName.style.display === "") {
  11. [Dòng 332] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 61] if (e !== null) {
  2. [Dòng 237] if (lang !== "vi") lang = "en";
  3. [Dòng 267] if (value !== "") {

================================================================================

📁 FILE 137: sha.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/assets/js/sha.js
📊 Thống kê: 49 điều kiện duy nhất
   - === : 38 lần
   - == : 0 lần
   - !== : 11 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (38 điều kiện):
  1. [Dòng 12] 1>t)throw Error("numRounds must a integer >= 1");if("SHA-1"===c)m=512,q=K,u=Z,f=160,r=function(a){return a.slice()};else if(0===c.lastIndexOf("SHA-",0))if(q=function(a,b){return L(a,b,c)
  2. [Dòng 12] c)},u=function(a,b,h,e){var k,f;if("SHA-224"===c
  3. [Dòng 12] "SHA-256"===c)k=(b+65>>>9<<4)+15,f=16;else if("SHA-384"===c
  4. [Dòng 12] "SHA-512"===c)k=(b+129>>>10<<
  5. [Dòng 13] c);if("SHA-224"===c)a=[e[0],e[1],e[2],e[3],e[4],e[5],e[6]];else if("SHA-256"===c)a=e;else if("SHA-384"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,e[4].b,e[5].a,e[5].b];else if("SHA-512"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,
  6. [Dòng 14] "SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)
  7. [Dòng 14] 0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
  8. [Dòng 14] return a},r=function(a){return a.slice()},"SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)||0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
  9. [Dòng 15] c)m=1152,f=224;else if("SHA3-256"===c)m=1088,f=256;else if("SHA3-384"===c)m=832,f=384;else if("SHA3-512"===c)m=576,f=512;else if("SHAKE128"===c)m=1344,f=-1,F=31,z=!0;else if("SHAKE256"===c)m=1088,f=-1,F=31,z=!0;else throw Error("Chosen SHA variant is not supported");u=function(a
  10. [Dòng 16] 0===64*l%e
  11. [Dòng 16] 5][l/5|0];g.push(a.b);if(32*g.length>=h)break;g.push(a.a);l+=1;0===64*l%e&&D(null,b)}return g}}else throw Error("Chosen SHA variant is not supported");d=M(a,g,x);l=A(c);this.setHMACKey=function(a,b,h){var k;if(!0===I)throw Error("HMAC key already set");if(!0===y)throw Error("Cannot set HMAC key after calling update");if(!0===z)throw Error("SHAKE is not supported for HMAC");g=(h
  12. [Dòng 17] f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
  13. [Dòng 17] )b.push(0);b[h]&=4294967040}for(a=0;a<=h;a+=1)v[a]=b[a]^909522486,w[a]=b[a]^1549556828;l=q(v,l);e=m;I=!0};this.update=function(a){var c,b,k,f=0,g=m>>>5;c=d(a,h,n);a=c.binLen;b=c.value;c=a>>>5;for(k=0;k<c;k+=g)f+m<=a&&(l=q(b.slice(k,k+g),l),f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
  14. [Dòng 18] for(g=1;g<t;g+=1)!0===z
  15. [Dòng 19] f);return k(m)};this.getHMAC=function(a,b){var k,g,d,p;if(!1===I)throw Error("Cannot call getHMAC without first setting HMAC key");d=N(b);switch(a){case "HEX":k=function(a){return O(a,f,x,d)};break;case "B64":k=function(a){return P(a,f,x,d)};break;case "BYTES":k=function(a){return Q(a,f,x)};break;case "ARRAYBUFFER":try{k=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}k=function(a){return R(a,f,x)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
  16. [Dòng 20] d=-1===b?3:0
  17. [Dòng 20] f=-1===b?3:0
  18. [Dòng 21] g=-1===b?3:0
  19. [Dòng 22] !0===c.hasOwnProperty("b64Pad")
  20. [Dòng 22] a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");
  21. [Dòng 23] u=-1===b?3:0
  22. [Dòng 23] l+=2){p=parseInt(a.substr(l,2),16);if(isNaN(p))throw Error("String of HEX type contains invalid characters");m=(l>>>1)+q;for(f=m>>>2;c.length<=f;)c.push(0);c[f]|=p<<8*(u+m%4*b)}return{value:c,binLen:4*g+d}};break;case "TEXT":c=function(c,h,d){var g,l,p=0,f,m,q,u,r,t;h=h||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(t=-1===
  23. [Dòng 24] m+=1){r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=l[m]<<8*(t+r%4*b);p+=1}else if("UTF16BE"===a
  24. [Dòng 24] "UTF16LE"===a)for(t=-1===b?2:0
  25. [Dòng 24] UTF16LE"===a
  26. [Dòng 24] 1===b
  27. [Dòng 25] !0===l
  28. [Dòng 25] !0===l&&(m=g&255,g=m<<8|g>>>8);r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=g<<8*(t+r%4*b);p+=2}return{value:h,binLen:8*p+d}};break;case "B64":c=function(a,c,d){var g=0,l,p,f,m,q,u,r,t;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");p=a.indexOf("=");a=a.replace(/\=/g
  29. [Dòng 25] t=-1===b?3:0
  30. [Dòng 26] q=-1===b?3:0
  31. [Dòng 27] m=-1===b?3:0
  32. [Dòng 32] c.b^a.b)}function A(c){var a=[],d;if("SHA-1"===c)a=[1732584193,4023233417,2562383102,271733878,3285377520];else if(0===c.lastIndexOf("SHA-",0))switch(a=
  33. [Dòng 34] new b(d[2],4271175723),new b(d[3],1595750129),new b(d[4],2917565137),new b(d[5],725511199),new b(d[6],4215389547),new b(d[7],327033209)];break;default:throw Error("Unknown SHA variant");}else if(0===c.lastIndexOf("SHA3-",0)
  34. [Dòng 34] 0===c.lastIndexOf("SHAKE",0))for(c=0
  35. [Dòng 36] a,k){var e,h,n,g,l,p,f,m,q,u,r,t,v,w,y,A,z,x,F,B,C,D,E=[],J;if("SHA-224"===k
  36. [Dòng 36] "SHA-256"===k)u=64,t=1,D=Number,v=G,w=la,y=H,A=ha,z=ja,x=da,F=fa,C=U,B=aa,J=d;else if("SHA-384"===k
  37. [Dòng 36] "SHA-512"===k)u=80,t=2,D=b,v=ma,w=na,y=oa,A=ia,z=ka,x=ea,F=ga,C=ca,B=ba,J=V;else throw Error("Unexpected error in SHA-2 implementation");k=a[0];e=a[1];h=a[2];n=a[3];g=a[4];l=a[5];p=a[6];f=a[7];for(r=0
  38. [Dòng 45] "function"===typeof define

!== (11 điều kiện):
  1. [Dòng 12] 'use strict';(function(Y){function C(c,a,b){var e=0,h=[],n=0,g,l,d,f,m,q,u,r,I=!1,v=[],w=[],t,y=!1,z=!1,x=-1;b=b||{};g=b.encoding||"UTF8";t=b.numRounds||1;if(t!==parseInt(t,10)
  2. [Dòng 18] 0!==f%32
  3. [Dòng 22] a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8
  4. [Dòng 23] }switch(c){case "HEX":c=function(a,c,d){var g=a.length,l,p,f,m,q,u;if(0!==g%2)throw Error("String of HEX type must be in byte increments");c=c||[0];d=d||0;q=d>>>3;u=-1===b?3:0;for(l=0
  5. [Dòng 24] 1!==b
  6. [Dòng 24] "UTF16LE"!==a
  7. [Dòng 25] "");if(-1!==p
  8. [Dòng 27] function(a,c,d){var g,l,p,f,m,q;c=c||[0];d=d||0;l=d>>>3;m=-1===b?3:0;q=new Uint8Array(a);for(g=0;g<a.byteLength;g+=1)f=g+l,p=f>>>2,c.length<=p&&c.push(0),c[p]|=q[g]<<8*(m+f%4*b);return{value:c,binLen:8*a.byteLength+d}};break;default:throw Error("format must be HEX, TEXT, B64, BYTES, or ARRAYBUFFER");}return c}function y(c,a){return c<<a|c>>>32-a}function S(c,a){return 32<a?(a-=32,new b(c.b<<a|c.a>>>32-a,c.a<<a|c.b>>>32-a)):0!==a?new b(c.a<<a|c.b>>>32-a,c.b<<a|c.a>>>32-a):c}function w(c,a){return c>>>
  9. [Dòng 37] a[7]);return a}function D(c,a){var d,e,h,n,g=[],l=[];if(null!==c)for(e=0
  10. [Dòng 45] define.amd?define(function(){return C}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=C),exports=C):Y.jsSHA=C})(this);
  11. [Dòng 45] return C}):"undefined"!==typeof exports?("undefined"!==typeof module

================================================================================

📁 FILE 138: sha256.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/assets/js/sha256.js
📊 Thống kê: 28 điều kiện duy nhất
   - === : 20 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (20 điều kiện):
  1. [Dòng 12] 1>u)throw Error("numRounds must a integer >= 1");if(0===c.lastIndexOf("SHA-",0))if(q=function(b,a){return A(b,a,c)
  2. [Dòng 12] c)},y=function(b,a,l,f){var g,e;if("SHA-224"===c
  3. [Dòng 12] "SHA-256"===c)g=(a+65>>>9<<4)+15,e=16;else throw Error("Unexpected error in SHA-2 implementation");for(
  4. [Dòng 13] c);if("SHA-224"===c)b=[f[0],f[1],f[2],f[3],f[4],f[5],f[6]];else if("SHA-256"===c)b=f;else throw Error("Unexpected error in SHA-2 implementation");return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
  5. [Dòng 13] "SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a
  6. [Dòng 13] return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
  7. [Dòng 14] if(!0===z)throw Error("Cannot set HMAC key after calling update");f=(g
  8. [Dòng 15] 5);g=a%h;z=!0};this.getHash=function(a,f){var d,h,k,q;if(!0===m)throw Error("Cannot call getHash after setting HMAC key");k=C(f);switch(a){case "HEX":d=function(a){return D(a,e,k)};break;case "B64":d=function(a){return E(a,e,k)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{h=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("format must be HEX, B64, BYTES, or ARRAYBUFFER");
  9. [Dòng 16] x(c));return d(q)};this.getHMAC=function(a,f){var d,k,t,u;if(!1===m)throw Error("Cannot call getHMAC without first setting HMAC key");t=C(f);switch(a){case "HEX":d=function(a){return D(a,e,t)};break;case "B64":d=function(a){return E(a,e,t)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{d=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
  10. [Dòng 18] !0===c.hasOwnProperty("b64Pad")
  11. [Dòng 20] h=(d>>>1)+q;for(e=h>>>2;b.length<=e;)b.push(0);b[e]|=k<<8*(3+h%4*-1)}return{value:b,binLen:4*f+c}};break;case "TEXT":d=function(c,b,d){var f,n,k=0,e,h,q,m,p,r;b=b||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(r=3
  12. [Dòng 21] q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=n[h]<<8*(r+p%4*-1);k+=1}else if("UTF16BE"===a
  13. [Dòng 21] "UTF16LE"===a)for(r=2
  14. [Dòng 21] UTF16LE"===a
  15. [Dòng 21] !0===n
  16. [Dòng 21] e+=1){f=c.charCodeAt(e);!0===n&&(h=f&255,f=h<<8|f>>>8);p=k+q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=f<<8*(r+p%4*-1);k+=2}return{value:b,binLen:8*k+d}};break;case "B64":d=function(a,b,c){var f=0,d,k,e,h,q,m,p;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");k=a.indexOf("=");a=a.replace(/\=/g
  17. [Dòng 25] 16)&65535)<<16|b&65535}function R(c,a,d,l,b){var g=(c&65535)+(a&65535)+(d&65535)+(l&65535)+(b&65535);return((c>>>16)+(a>>>16)+(d>>>16)+(l>>>16)+(b>>>16)+(g>>>16)&65535)<<16|g&65535}function x(c){var a=[],d;if(0===c.lastIndexOf("SHA-",0))switch(a=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428]
  18. [Dòng 26] new m,new m,new m,new m,new m,new m];break;case "SHA-512":a=[new m,new m,new m,new m,new m,new m,new m,new m];break;default:throw Error("Unknown SHA variant");}else throw Error("No SHA variants supported");return a}function A(c,a,d){var l,b,g,f,n,k,e,h,m,r,p,w,t,x,u,z,A,B,C,D,E,F,v=[],G;if("SHA-224"===d
  19. [Dòng 26] "SHA-256"===d)r=64,w=1,F=Number,t=P,x=Q,u=R,z=N,A=O,B=L,C=M,E=K,D=J,G=H;else throw Error("Unexpected error in SHA-2 implementation");d=a[0];l=a[1];b=a[2];g=a[3];f=a[4];n=a[5];k=a[6];e=a[7];for(p=
  20. [Dòng 29] "function"===typeof define

!== (8 điều kiện):
  1. [Dòng 12] 'use strict';(function(I){function w(c,a,d){var l=0,b=[],g=0,f,n,k,e,h,q,y,p,m=!1,t=[],r=[],u,z=!1;d=d||{};f=d.encoding||"UTF8";u=d.numRounds||1;if(u!==parseInt(u,10)
  2. [Dòng 18] l+=1)g[l]=c[l>>>2]>>>8*(3+l%4*-1)&255;return b}function C(c){var a={outputUpper:!1,b64Pad:"=",shakeLen:-1};c=c||{};a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");
  3. [Dòng 19] if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0;d<f;d+=2){k=parseInt(a.substr(d,2),16);if(isNaN(k))throw Error("String of HEX type contains invalid characters");
  4. [Dòng 19] if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0
  5. [Dòng 21] "UTF16LE"!==a
  6. [Dòng 22] "");if(-1!==k
  7. [Dòng 29] define.amd?define(function(){return w}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=w),exports=w):I.jsSHA=w})(this);
  8. [Dòng 29] return w}):"undefined"!==typeof exports?("undefined"!==typeof module

================================================================================

📁 FILE 139: slick.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/assets/libraries/slick/slick.js
📊 Thống kê: 182 điều kiện duy nhất
   - === : 126 lần
   - == : 5 lần
   - !== : 47 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (126 điều kiện):
  1. [Dòng 20] if (typeof define === 'function'
  2. [Dòng 206] if (typeof(index) === 'boolean') {
  3. [Dòng 215] if (typeof(index) === 'number') {
  4. [Dòng 216] if (index === 0
  5. [Dòng 216] _.$slides.length === 0) {
  6. [Dòng 224] if (addBefore === true) {
  7. [Dòng 249] if (_.options.slidesToShow === 1
  8. [Dòng 249] _.options.adaptiveHeight === true
  9. [Dòng 249] _.options.vertical === false) {
  10. [Dòng 264] if (_.options.rtl === true
  11. [Dòng 267] if (_.transformsEnabled === false) {
  12. [Dòng 268] if (_.options.vertical === false) {
  13. [Dòng 280] if (_.cssTransitions === false) {
  14. [Dòng 281] if (_.options.rtl === true) {
  15. [Dòng 355] typeof asNavFor === 'object' ) {
  16. [Dòng 371] if (_.options.fade === false) {
  17. [Dòng 414] if ( _.options.infinite === false ) {
  18. [Dòng 416] if ( _.direction === 1
  19. [Dòng 416] ( _.currentSlide + 1 ) === ( _.slideCount - 1 )) {
  20. [Dòng 420] else if ( _.direction === 0 ) {
  21. [Dòng 424] if ( _.currentSlide - 1 === 0 ) {
  22. [Dòng 442] if (_.options.arrows === true ) {
  23. [Dòng 487] if (_.options.dots === true
  24. [Dòng 524] _.$slideTrack = (_.slideCount === 0) ?
  25. [Dòng 532] if (_.options.centerMode === true
  26. [Dòng 532] _.options.swipeToSlide === true) {
  27. [Dòng 547] _.setSlideClasses(typeof _.currentSlide === 'number' ? _.currentSlide : 0);
  28. [Dòng 549] if (_.options.draggable === true) {
  29. [Dòng 602] if (_.respondTo === 'window') {
  30. [Dòng 604] } else if (_.respondTo === 'slider') {
  31. [Dòng 606] } else if (_.respondTo === 'min') {
  32. [Dòng 618] if (_.originalSettings.mobileFirst === false) {
  33. [Dòng 635] if (_.breakpointSettings[targetBreakpoint] === 'unslick') {
  34. [Dòng 641] if (initial === true) {
  35. [Dòng 705] slideOffset = indexOffset === 0 ? _.options.slidesToScroll : _.options.slidesToShow - indexOffset
  36. [Dòng 712] slideOffset = indexOffset === 0 ? _.options.slidesToScroll : indexOffset
  37. [Dòng 719] var index = event.data.index === 0 ? 0 :
  38. [Dòng 765] if (_.options.accessibility === true) {
  39. [Dòng 772] if (_.options.arrows === true
  40. [Dòng 797] if (_.options.focusOnSelect === true) {
  41. [Dòng 836] if (_.shouldClick === false) {
  42. [Dòng 1051] if (_.options.infinite === true) {
  43. [Dòng 1061] } else if (_.options.centerMode === true) {
  44. [Dòng 1094] if (_.options.vertical === true
  45. [Dòng 1094] _.options.centerMode === true) {
  46. [Dòng 1095] if (_.options.slidesToShow === 2) {
  47. [Dòng 1097] } else if (_.options.slidesToShow === 1) {
  48. [Dòng 1128] } else if (_.options.centerMode === true
  49. [Dòng 1128] _.options.infinite === true) {
  50. [Dòng 1141] if (_.options.variableWidth === true) {
  51. [Dòng 1143] _.options.infinite === false) {
  52. [Dòng 1159] if (_.options.centerMode === true) {
  53. [Dòng 1200] if (_.options.infinite === false) {
  54. [Dòng 1229] centerOffset = _.options.centerMode === true ? _.slideWidth * Math.floor(_.options.slidesToShow / 2) : 0
  55. [Dòng 1231] if (_.options.swipeToSlide === true) {
  56. [Dòng 1406] _.options.pauseOnDotsHover === true
  57. [Dòng 1498] if (event.keyCode === 37
  58. [Dòng 1498] _.options.accessibility === true) {
  59. [Dòng 1501] message: _.options.rtl === true ? 'next' :
  60. [Dòng 1504] } else if (event.keyCode === 39
  61. [Dòng 1507] message: _.options.rtl === true ? 'previous' : 'next'
  62. [Dòng 1585] if (_.options.fade === true) {
  63. [Dòng 1593] if (_.options.lazyLoad === 'anticipated') {
  64. [Dòng 1616] } else if (_.currentSlide === 0) {
  65. [Dòng 1637] if (_.options.lazyLoad === 'progressive') {
  66. [Dòng 1773] if ( _.options.adaptiveHeight === true ) {
  67. [Dòng 1864] if ( $.type(responsiveSettings) === 'array'
  68. [Dòng 1878] _.breakpoints[l] === currentBreakpoint ) {
  69. [Dòng 1969] index = removeBefore === true ? 0 : _.slideCount - 1
  70. [Dòng 1971] index = removeBefore === true ? --index : index
  71. [Dòng 1980] if (removeAll === true) {
  72. [Dòng 2050] if (_.options.vertical === false
  73. [Dòng 2050] _.options.variableWidth === false) {
  74. [Dòng 2054] } else if (_.options.variableWidth === true) {
  75. [Dòng 2062] if (_.options.variableWidth === false) _.$slideTrack.children('.slick-slide').width(_.slideWidth - offset);
  76. [Dòng 2128] if( $.type( arguments[0] ) === 'object' ) {
  77. [Dòng 2134] } else if ( $.type( arguments[0] ) === 'string' ) {
  78. [Dòng 2140] if ( arguments[0] === 'responsive'
  79. [Dòng 2140] $.type( arguments[1] ) === 'array' ) {
  80. [Dòng 2152] if ( type === 'single' ) {
  81. [Dòng 2157] } else if ( type === 'multiple' ) {
  82. [Dòng 2166] } else if ( type === 'responsive' ) {
  83. [Dòng 2181] if( _.options.responsive[l].breakpoint === value[item].breakpoint ) {
  84. [Dòng 2231] _.positionProp = _.options.vertical === true ? 'top' : 'left'
  85. [Dòng 2233] if (_.positionProp === 'top') {
  86. [Dòng 2242] if (_.options.useCSS === true) {
  87. [Dòng 2248] if ( typeof _.options.zIndex === 'number' ) {
  88. [Dòng 2261] if (bodyStyle.perspectiveProperty === undefined
  89. [Dòng 2261] bodyStyle.webkitPerspective === undefined) _.animType = false;
  90. [Dòng 2267] bodyStyle.MozPerspective === undefined) _.animType = false;
  91. [Dòng 2279] if (bodyStyle.msTransform === undefined) _.animType = false;
  92. [Dòng 2306] var evenCoef = _.options.slidesToShow % 2 === 0 ? 1 : 0
  93. [Dòng 2328] if (index === 0) {
  94. [Dòng 2334] } else if (index === _.slideCount - 1) {
  95. [Dòng 2366] indexOffset = _.options.infinite === true ? _.options.slidesToShow + index : index
  96. [Dòng 2388] if (_.options.lazyLoad === 'ondemand'
  97. [Dòng 2388] _.options.lazyLoad === 'anticipated') {
  98. [Dòng 2402] if (_.options.infinite === true
  99. [Dòng 2402] _.options.fade === false) {
  100. [Dòng 2479] if (_.animating === true
  101. [Dòng 2479] _.options.waitForAnimate === true) {
  102. [Dòng 2483] if (_.options.fade === true
  103. [Dòng 2483] _.currentSlide === index) {
  104. [Dòng 2487] if (sync === false) {
  105. [Dòng 2495] _.currentLeft = _.swipeLeft === null ? slideLeft : _.swipeLeft
  106. [Dòng 2497] if (_.options.infinite === false
  107. [Dòng 2497] _.options.centerMode === false
  108. [Dòng 2509] } else if (_.options.infinite === false
  109. [Dòng 2509] _.options.centerMode === true
  110. [Dòng 2627] return (_.options.rtl === false ? 'left' : 'right');
  111. [Dòng 2633] return (_.options.rtl === false ? 'right' : 'left');
  112. [Dòng 2635] if (_.options.verticalSwiping === true) {
  113. [Dòng 2664] if ( _.touchObject.curX === undefined ) {
  114. [Dòng 2668] if ( _.touchObject.edgeHit === true ) {
  115. [Dòng 2732] if ((_.options.swipe === false) || ('ontouchend' in document
  116. [Dòng 2732] _.options.swipe === false)) {
  117. [Dòng 2734] } else if (_.options.draggable === false
  118. [Dòng 2806] positionOffset = (_.options.rtl === false ? 1 : -1) * (_.touchObject.curX > _.touchObject.startX ? 1 : -1);
  119. [Dòng 2817] if ((_.currentSlide === 0
  120. [Dòng 2817] swipeDirection === 'right') || (_.currentSlide >= _.getDotCount()
  121. [Dòng 2817] swipeDirection === 'left')) {
  122. [Dòng 2832] _.options.touchMove === false) {
  123. [Dòng 2836] if (_.animating === true) {
  124. [Dòng 2926] if ( _.options.arrows === true
  125. [Dòng 2933] if (_.currentSlide === 0) {
  126. [Dòng 2938] _.options.centerMode === false) {

== (5 điều kiện):
  1. [Dòng 2007] x = _.positionProp == 'left' ? Math.ceil(position) + 'px' : '0px'
  2. [Dòng 2008] y = _.positionProp == 'top' ? Math.ceil(position) + 'px' : '0px'
  3. [Dòng 2368] if (_.options.slidesToShow == _.options.slidesToScroll
  4. [Dòng 3002] if (typeof opt == 'object'
  5. [Dòng 3002] typeof opt == 'undefined')

!== (47 điều kiện):
  1. [Dòng 22] } else if (typeof exports !== 'undefined') {
  2. [Dòng 155] if (typeof document.mozHidden !== 'undefined') {
  3. [Dòng 158] } else if (typeof document.webkitHidden !== 'undefined') {
  4. [Dòng 342] asNavFor !== null ) {
  5. [Dòng 355] if ( asNavFor !== null
  6. [Dòng 460] if (_.options.infinite !== true) {
  7. [Dòng 612] _.options.responsive !== null) {
  8. [Dòng 630] if (targetBreakpoint !== null) {
  9. [Dòng 631] if (_.activeBreakpoint !== null) {
  10. [Dòng 632] if (targetBreakpoint !== _.activeBreakpoint
  11. [Dòng 676] triggerBreakpoint !== false ) {
  12. [Dòng 699] unevenOffset = (_.slideCount % _.options.slidesToScroll !== 0);
  13. [Dòng 758] _.$dots !== null) {
  14. [Dòng 997] if (filter !== null) {
  15. [Dòng 1103] if (_.slideCount % _.options.slidesToScroll !== 0) {
  16. [Dòng 1314] if (_.$dots !== null) {
  17. [Dòng 1324] if (slideControlIndex !== -1) {
  18. [Dòng 1910] _.currentSlide !== 0) {
  19. [Dòng 1953] if ($(window).width() !== _.windowWidth) {
  20. [Dòng 2144] } else if ( typeof arguments[1] !== 'undefined' ) {
  21. [Dòng 2170] if( $.type( _.options.responsive ) !== 'array' ) {
  22. [Dòng 2239] if (bodyStyle.WebkitTransition !== undefined
  23. [Dòng 2240] bodyStyle.MozTransition !== undefined
  24. [Dòng 2241] bodyStyle.msTransition !== undefined) {
  25. [Dòng 2257] if (bodyStyle.OTransform !== undefined) {
  26. [Dòng 2263] if (bodyStyle.MozTransform !== undefined) {
  27. [Dòng 2269] if (bodyStyle.webkitTransform !== undefined) {
  28. [Dòng 2275] if (bodyStyle.msTransform !== undefined) {
  29. [Dòng 2281] if (bodyStyle.transform !== undefined
  30. [Dòng 2281] _.animType !== false) {
  31. [Dòng 2286] _.transformsEnabled = _.options.useTransform && (_.animType !== null
  32. [Dòng 2286] _.animType !== false);
  33. [Dòng 2500] if (dontAnimate !== true
  34. [Dòng 2567] if (dontAnimate !== true) {
  35. [Dòng 2717] if ( _.touchObject.startX !== _.touchObject.curX ) {
  36. [Dòng 2734] event.type.indexOf('mouse') !== -1) {
  37. [Dòng 2738] event.originalEvent.touches !== undefined ?
  38. [Dòng 2773] touches = event.originalEvent !== undefined ? event.originalEvent.touches : null
  39. [Dòng 2775] touches.length !== 1) {
  40. [Dòng 2781] _.touchObject.curX = touches !== undefined ? touches[0].pageX : event.clientX
  41. [Dòng 2782] _.touchObject.curY = touches !== undefined ? touches[0].pageY : event.clientY
  42. [Dòng 2801] if (event.originalEvent !== undefined
  43. [Dòng 2852] if (_.touchObject.fingerCount !== 1
  44. [Dòng 2857] event.originalEvent.touches !== undefined) {
  45. [Dòng 2861] _.touchObject.startX = _.touchObject.curX = touches !== undefined ? touches.pageX : event.clientX
  46. [Dòng 2862] _.touchObject.startY = _.touchObject.curY = touches !== undefined ? touches.pageY : event.clientY
  47. [Dòng 2872] if (_.$slidesCache !== null) {

!= (4 điều kiện):
  1. [Dòng 805] $('[draggable!=true]', _.$slideTrack).off('dragstart', _.preventDefault);
  2. [Dòng 1467] $('[draggable!=true]', _.$slideTrack).on('dragstart', _.preventDefault);
  3. [Dòng 2707] if( direction != 'vertical' ) {
  4. [Dòng 3006] if (typeof ret != 'undefined') return ret;

================================================================================

📁 FILE 140: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 141: atm_b1.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_1/assets/js/atm_b1.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 228] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 234] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 258] if (paymentState === "authorization_required") {
  12. [Dòng 264] if (method === "REDIRECT") {
  13. [Dòng 267] } else if (method === "POST_REDIRECT") {
  14. [Dòng 306] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 289] if (jLinks !== undefined
  3. [Dòng 289] jLinks !== null) {
  4. [Dòng 291] if (jMerchantReturn !== undefined
  5. [Dòng 291] jMerchantReturn !== null) {
  6. [Dòng 306] if (responseCode !== undefined
  7. [Dòng 306] responseCode !== null
  8. [Dòng 334] if (parentRes !== "{

================================================================================

📁 FILE 142: atm_b1_2.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_1/atm_b1_2.js
📊 Thống kê: 11 điều kiện duy nhất
   - === : 8 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 52] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 55] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 64] year === y
  5. [Dòng 66] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 75] if ("PAY" === operation) {
  7. [Dòng 319] } else if (value === "") {
  8. [Dòng 336] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 77] if (e !== null) {
  2. [Dòng 253] if (lang !== "vi") lang = "en";
  3. [Dòng 283] if (value !== "") {

================================================================================

📁 FILE 143: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_1/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 144: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 145: atm_b2.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_2/assets/js/atm_b2.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 231] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 236] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 252] if (invoiceState === "unpaid"
  10. [Dòng 252] invoiceState === "not_paid") {
  11. [Dòng 256] if (paymentState === "authorization_required") {
  12. [Dòng 260] if (method === "REDIRECT") {
  13. [Dòng 263] } else if (method === "POST_REDIRECT") {
  14. [Dòng 302] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 285] if (jLinks !== undefined
  3. [Dòng 285] jLinks !== null) {
  4. [Dòng 287] if (jMerchantReturn !== undefined
  5. [Dòng 287] jMerchantReturn !== null) {
  6. [Dòng 302] if (responseCode !== undefined
  7. [Dòng 302] responseCode !== null
  8. [Dòng 330] if (parentRes !== "{

================================================================================

📁 FILE 146: atm_b2_2.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_2/atm_b2_2.js
📊 Thống kê: 6 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 41] if (inName.value === "") return err("MISSING_FIELD"
  3. [Dòng 51] if ("PAY" === operation) {

!== (3 điều kiện):
  1. [Dòng 53] if (e !== null) {
  2. [Dòng 229] if (lang !== "vi") lang = "en";
  3. [Dòng 259] if (value !== "") {

================================================================================

📁 FILE 147: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/atm_bank_2/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 148: common.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/common.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 42] if (i % 2 === parity) d *= 2;
  2. [Dòng 46] return (sum % 10) === 0
  3. [Dòng 116] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 119] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 205] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 211] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 227] if (xhr.status === 200
  8. [Dòng 227] xhr.status === 201) {
  9. [Dòng 230] if (invoiceState === "unpaid"
  10. [Dòng 230] invoiceState === "not_paid") {
  11. [Dòng 235] if (paymentState === "authorization_required") {
  12. [Dòng 241] if (method === "REDIRECT") {
  13. [Dòng 244] } else if (method === "POST_REDIRECT") {
  14. [Dòng 283] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 94] this.oldValue !== this.value) {
  2. [Dòng 266] if (jLinks !== undefined
  3. [Dòng 266] jLinks !== null) {
  4. [Dòng 268] if (jMerchantReturn !== undefined
  5. [Dòng 268] jMerchantReturn !== null) {
  6. [Dòng 283] if (responseCode !== undefined
  7. [Dòng 283] responseCode !== null
  8. [Dòng 311] if (parentRes !== "{

================================================================================

📁 FILE 149: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 150: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/international_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 151: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/begodi/international_card/script.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 12] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 22] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 25] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 37] year === y
  5. [Dòng 39] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 70] if ("PAY" === operation) {
  7. [Dòng 144] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 72] if (e !== null) {
  2. [Dòng 117] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 152: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/iframe/index.html
📊 Thống kê: 35 điều kiện duy nhất
   - === : 21 lần
   - == : 0 lần
   - !== : 14 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (21 điều kiện):
  1. [Dòng 10] if ((cardno.length === 15
  2. [Dòng 10] cardno.length === 16
  3. [Dòng 10] cardno.length === 19) && isMode10(cardno) === true) {
  4. [Dòng 10] isMode10(cardno) === true) {
  5. [Dòng 29] if (i % 2 === parity) d *= 2;
  6. [Dòng 33] return (sum % 10) === 0
  7. [Dòng 54] if ("PAY" === operation) {
  8. [Dòng 75] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  9. [Dòng 81] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  10. [Dòng 97] if (xhr.status === 200
  11. [Dòng 97] xhr.status === 201) {
  12. [Dòng 100] if (invoiceState === "unpaid"
  13. [Dòng 100] invoiceState === "not_paid") {
  14. [Dòng 105] if (paymentState === "authorization_required") {
  15. [Dòng 111] if (method === "REDIRECT") {
  16. [Dòng 116] } else if (method === "POST_REDIRECT") {
  17. [Dòng 183] //Customizable =================================================================================================
  18. [Dòng 229] if (typeof queryParams[key] === "undefined") {
  19. [Dòng 232] } else if (typeof queryParams[key] === "string") {
  20. [Dòng 246] if (params["CardList"] === undefined
  21. [Dòng 246] params["CardList"] === null) return;

!== (14 điều kiện):
  1. [Dòng 40] //if (event.origin !== "http://example.com:8080") return;
  2. [Dòng 58] if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
  3. [Dòng 59] if (inNumber.style.display !== "none") instrument.number = inNumber.value;
  4. [Dòng 60] if (inDate.style.display !== "none") instrument.date = inDate.value;
  5. [Dòng 61] if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
  6. [Dòng 62] if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
  7. [Dòng 63] if (inName.style.display !== "none") instrument.name = inName.value;
  8. [Dòng 78] /*if (contentType !== my_expected_type) {
  9. [Dòng 138] if (jLinks !== undefined
  10. [Dòng 138] jLinks !== null) {
  11. [Dòng 140] if (jMerchantReturn !== undefined
  12. [Dòng 140] jMerchantReturn !== null) {
  13. [Dòng 172] if (parentRes !== "{
  14. [Dòng 245] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 153: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 154: karma.conf.js
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/karma.conf.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 155: main.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/main.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 156: polyfills.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/polyfills.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 157: test.ts
📍 Đường dẫn: /root/projects/onepay/paygate-domestic/src/test.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📋 TÓM TẮT TẤT CẢ ĐIỀU KIỆN DUY NHẤT:
====================================================================================================

=== (466 điều kiện duy nhất):
------------------------------------------------------------
1. return lang === this._translate.currentLang
2. isPopupSupport === 'True') || (errorInitPage
3. isPopupSupport === 'True'"
4. isPopupSupport === 'True')">
5. params.timeout === 'true') {
6. this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
7. _re.body.state === 'unpaid');
8. if (this.errorCode === 'overtime'
9. this.errorCode === '253') {
10. params.name === 'CUSTOMER_INTIME'
11. params.code === '09') {
12. if (this.timeLeft === 0) {
13. if (YY % 400 === 0
14. YY % 4 === 0)) {
15. if (YYYY % 400 === 0
16. YYYY % 4 === 0)) {
17. <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
18. [class.red_border_no_background]="c_expdate && (valueDate.length === 0
19. valueDate.trim().length === 0)"
20. if (target.tagName === 'A'
21. if (isIE[0] === 'MSIE'
22. +isIE[1] === 10) {
23. if ((_val.value.substr(-1) === ' '
24. _val.value.length === 24) {
25. if (this.cardTypeBank === 'bank_card_number') {
26. } else if (this.cardTypeBank === 'bank_account_number') {
27. } else if (this.cardTypeBank === 'bank_username') {
28. } else if (this.cardTypeBank === 'bank_customer_code') {
29. this.cardTypeBank === 'bank_card_number'
30. if (this.cardTypeOcean === 'IB') {
31. } else if (this.cardTypeOcean === 'MB') {
32. if (_val.value.substr(0, 2) === '84') {
33. } else if (this.cardTypeOcean === 'ATM') {
34. if (this.cardTypeBank === 'bank_account_number') {
35. this.cardTypeBank === 'bank_card_number') {
36. if (this.cardTypeBank === 'bank_card_number'
37. if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
38. if (event.keyCode === 8
39. event.key === "Backspace"
40. if (v.length === 2
41. this.flag.length === 3
42. this.flag.charAt(this.flag.length - 1) === '/') {
43. if (v.length === 1) {
44. } else if (v.length === 2) {
45. v.length === 2) {
46. if (len === 2) {
47. this.cardTypeBank === 'bank_account_number') {
48. if ((this.cardTypeBank === 'bank_account_number'
49. this.cardTypeBank === 'bank_username'
50. this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
51. this.cardTypeOcean === 'ATM')
52. || (this.cardTypeOcean === 'IB'
53. if (valIn === this._translate.instant('bank_card_number')) {
54. } else if (valIn === this._translate.instant('bank_account_number')) {
55. } else if (valIn === this._translate.instant('bank_username')) {
56. } else if (valIn === this._translate.instant('bank_customer_code')) {
57. if (_val.value === ''
58. _val.value === null
59. _val.value === undefined) {
60. if (_val.value && (this.cardTypeBank === 'bank_card_number'
61. if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
62. this.cardTypeOcean === 'MB') {
63. this.cardTypeOcean === 'IB'
64. if ((this.cardTypeBank === 'bank_card_number'
65. if (this.cardName === undefined
66. this.cardName === '') {
67. if (this.valueDate === undefined
68. this.valueDate === '') {
69. c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
70. _inExpDate.trim().length === 0)"
71. if (approval.method === 'REDIRECT') {
72. } else if (approval.method === 'POST_REDIRECT') {
73. filteredData.length === 0"
74. if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
75. filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
76. if (valOut === 'auth') {
77. if (this._b === '1'
78. this._b === '20'
79. this._b === '64') {
80. if (this._b === '36'
81. this._b === '18'
82. this._b === '19'
83. if (this._b === '19'
84. this._b === '16'
85. this._b === '25'
86. this._b === '33'
87. this._b === '39'
88. this._b === '11'
89. this._b === '17'
90. this._b === '36'
91. this._b === '44'
92. this._b === '12'
93. this._b === '64'
94. if (this._b === '20'
95. if (this._b === '12'
96. this._b === '18') {
97. if (_formCard.country === 'default') {
98. if ((v.substr(-1) === ' '
99. if (deviceValue === 'CA'
100. deviceValue === 'US') {
101. this.c_country = _val.value === 'default'
102. [ngStyle]="{'border-color': type === 2 ? this.themeColor.border_color : border_color}"
103. d_vrbank===true"
104. this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number === 1
105. if (this._res.state === 'unpaid'
106. this._res.state === 'not_paid') {
107. if ('op' === auth
108. } else if ('bank' === auth
109. return id === 'amex' ? '1234' : '123'
110. if (this.timeLeftPaypal === 0) {
111. filteredData.length === 0
112. filteredDataOther.length === 0"
113. this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
114. this.filteredDataOther = this.appList.filter(item => item.type === 'other');
115. item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
116. filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
117. if (item.type === 'mobile_banking') {
118. item.brand_id === 'visa' ? 'height: 14.42px
119. if (event.keyCode === 13) {
120. && ((item.brand_id === 'amex'
121. return this._i_token_otp != null && !isNaN(+this._i_token_otp) && ((id === 'amex'
122. const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
123. item.method === method) : null;
124. if (M[1] === 'Chrome') {
125. if (param === key) {
126. pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
127. target === 0
128. if (cardTypeBank === 'bank_card_number') {
129. } else if (cardTypeBank === 'bank_account_number') {
130. if (typeof define === 'function'
131. } else if (typeof exports === 'object') {
132. if (number === "") return err("MISSING_FIELD"
133. if (inName.value === "") return err("MISSING_FIELD"
134. if ("PAY" === operation) {
135. if (i % 2 === parity) d *= 2;
136. return (sum % 10) === 0
137. if (typeof queryParams[key] === "undefined") {
138. } else if (typeof queryParams[key] === "string") {
139. if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
140. } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
141. if (xhr.status === 200
142. xhr.status === 201) {
143. if (invoiceState === "unpaid"
144. invoiceState === "not_paid") {
145. if (paymentState === "authorization_required") {
146. if (method === "REDIRECT") {
147. } else if (method === "POST_REDIRECT") {
148. responseCode === "0") {
149. if (inMonth.value === "") return err("MISSING_FIELD"
150. if (inYear.value === "") return err("MISSING_FIELD"
151. year === y
152. if (inPhone.value === "") return err("MISSING_FIELD"
153. } else if (value === "") {
154. if (trPhone.style.display === "") {
155. } else if (trName.style.display === "") {
156. if (trName.style.display === "") {
157. if (xhr.status === 200) {
158. if (insType === "card") {
159. if (insBrandId === "visa"
160. insBrandId === "mastercard"
161. insBrandId === "amex"
162. insBrandId === "jcb"
163. insBrandId === "cup") {
164. } else if (insBrandId === "atm") {
165. } else if (insType === "dongabank_account") {
166. } else if (insType === "techcombank_account") {
167. } else if (insType === "vib_account") {
168. } else if (insType === "bidv_account") {
169. } else if (insType === "tpbank_account") {
170. } else if (insType === "shb_account") {
171. } else if (insType === "shb_customer_id") {
172. } else if (insType === "vpbank_account") {
173. } else if (insType === "oceanbank_online_account") {
174. } else if (insType === "oceanbank_mobile_account") {
175. } else if (insType === "pvcombank_account") {
176. if (inCvv.value === "") return err("MISSING_FIELD"
177. if (inCvv.value === "") {
178. if ((cardno.length === 15
179. cardno.length === 16
180. cardno.length === 19) && isMode10(cardno) === true) {
181. isMode10(cardno) === true) {
182. if (params["CardList"] === undefined
183. params["CardList"] === null) return;
184. if (insSwiftCode === "BFTVVNVX") { //Vietcombank
185. } else if (insSwiftCode === "BIDVVNVX") { //BIDV
186. typeof exports === 'object'
187. typeof define === 'function'
188. selector === '#') {
189. if (typeof element.getRootNode === 'function') {
190. if (typeof $ === 'undefined') {
191. version[0] === minMajor
192. version[1] === minMinor
193. if (config === 'close') {
194. if (input.type === 'radio') {
195. } else if (input.type === 'checkbox') {
196. if (this._element.tagName === 'LABEL'
197. input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
198. if (config === 'toggle') {
199. if (_button.getAttribute('aria-pressed') === 'true') {
200. if (activeIndex === index) {
201. if (this._config.pause === 'hover') {
202. if (_this3._config.pause === 'hover') {
203. var isNextDirection = direction === Direction.NEXT
204. var isPrevDirection = direction === Direction.PREV
205. activeIndex === 0
206. activeIndex === lastItemIndex
207. var delta = direction === Direction.PREV ? -1 : 1
208. return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
209. if (direction === Direction.NEXT) {
210. if (typeof config === 'object') {
211. var action = typeof config === 'string' ? config : _config.slide
212. if (typeof config === 'number') {
213. } else if (typeof action === 'string') {
214. if (typeof data[action] === 'undefined') {
215. return foundElem === element
216. if (typeof _this._config.parent === 'string') {
217. return elem.getAttribute('data-parent') === _this._config.parent
218. if (actives.length === 0) {
219. typeof config === 'object'
220. if (typeof config === 'string') {
221. if (typeof data[config] === 'undefined') {
222. if (event.currentTarget.tagName === 'A') {
223. if (usePopper === void 0) {
224. if (typeof Popper === 'undefined') {
225. if (this._config.reference === 'parent') {
226. $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
227. if (typeof this._config.offset === 'function') {
228. if (this._config.display === 'static') {
229. var _config = typeof config === 'object' ? config : null
230. if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
231. event.type === 'keyup'
232. event.type === 'click') {
233. if (event && (event.type === 'click'
234. event.which === TAB_KEYCODE) && $.contains(parent
235. if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
236. event.which === ESCAPE_KEYCODE) {
237. if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
238. event.which === SPACE_KEYCODE)) {
239. if (event.which === ESCAPE_KEYCODE) {
240. if (items.length === 0) {
241. if (event.which === ARROW_UP_KEYCODE
242. if (event.which === ARROW_DOWN_KEYCODE
243. if (this._config.backdrop === 'static') {
244. $(_this5._element).has(event.target).length === 0) {
245. if (event.which === ESCAPE_KEYCODE$1) {
246. if (this.tagName === 'A'
247. this.tagName === 'AREA') {
248. if (unsafeHtml.length === 0) {
249. typeof sanitizeFn === 'function') {
250. if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
251. if (_ret === "continue") continue;
252. if ($(this.element).css('display') === 'none') {
253. var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
254. if (prevHoverState === HoverState.OUT) {
255. if (typeof content === 'object'
256. title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
257. if (typeof this.config.offset === 'function') {
258. if (this.config.container === false) {
259. if (trigger === 'click') {
260. var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
261. var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
262. context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
263. context._hoverState === HoverState.SHOW) {
264. if (context._hoverState === HoverState.SHOW) {
265. context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
266. if (context._hoverState === HoverState.OUT) {
267. if (typeof config.delay === 'number') {
268. if (typeof config.title === 'number') {
269. if (typeof config.content === 'number') {
270. var _config = typeof config === 'object'
271. if (typeof content === 'function') {
272. this._scrollElement = element.tagName === 'BODY' ? window : element
273. var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
274. var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
275. var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
276. return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
277. return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
278. var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
279. this._element.parentNode.nodeType === Node.ELEMENT_NODE
280. var itemSelector = listElement.nodeName === 'UL'
281. listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
282. var activeElements = container && (container.nodeName === 'UL'
283. container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
284. if (active.getAttribute('role') === 'tab') {
285. if (element.getAttribute('role') === 'tab') {
286. 1>t)throw Error("numRounds must a integer >= 1");if("SHA-1"===c)m=512,q=K,u=Z,f=160,r=function(a){return a.slice()};else if(0===c.lastIndexOf("SHA-",0))if(q=function(a,b){return L(a,b,c)
287. c)},u=function(a,b,h,e){var k,f;if("SHA-224"===c
288. "SHA-256"===c)k=(b+65>>>9<<4)+15,f=16;else if("SHA-384"===c
289. "SHA-512"===c)k=(b+129>>>10<<
290. c);if("SHA-224"===c)a=[e[0],e[1],e[2],e[3],e[4],e[5],e[6]];else if("SHA-256"===c)a=e;else if("SHA-384"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,e[4].b,e[5].a,e[5].b];else if("SHA-512"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,
291. "SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)
292. 0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
293. return a},r=function(a){return a.slice()},"SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)||0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
294. c)m=1152,f=224;else if("SHA3-256"===c)m=1088,f=256;else if("SHA3-384"===c)m=832,f=384;else if("SHA3-512"===c)m=576,f=512;else if("SHAKE128"===c)m=1344,f=-1,F=31,z=!0;else if("SHAKE256"===c)m=1088,f=-1,F=31,z=!0;else throw Error("Chosen SHA variant is not supported");u=function(a
295. 0===64*l%e
296. 5][l/5|0];g.push(a.b);if(32*g.length>=h)break;g.push(a.a);l+=1;0===64*l%e&&D(null,b)}return g}}else throw Error("Chosen SHA variant is not supported");d=M(a,g,x);l=A(c);this.setHMACKey=function(a,b,h){var k;if(!0===I)throw Error("HMAC key already set");if(!0===y)throw Error("Cannot set HMAC key after calling update");if(!0===z)throw Error("SHAKE is not supported for HMAC");g=(h
297. f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
298. )b.push(0);b[h]&=4294967040}for(a=0;a<=h;a+=1)v[a]=b[a]^909522486,w[a]=b[a]^1549556828;l=q(v,l);e=m;I=!0};this.update=function(a){var c,b,k,f=0,g=m>>>5;c=d(a,h,n);a=c.binLen;b=c.value;c=a>>>5;for(k=0;k<c;k+=g)f+m<=a&&(l=q(b.slice(k,k+g),l),f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
299. for(g=1;g<t;g+=1)!0===z
300. f);return k(m)};this.getHMAC=function(a,b){var k,g,d,p;if(!1===I)throw Error("Cannot call getHMAC without first setting HMAC key");d=N(b);switch(a){case "HEX":k=function(a){return O(a,f,x,d)};break;case "B64":k=function(a){return P(a,f,x,d)};break;case "BYTES":k=function(a){return Q(a,f,x)};break;case "ARRAYBUFFER":try{k=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}k=function(a){return R(a,f,x)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
301. d=-1===b?3:0
302. f=-1===b?3:0
303. g=-1===b?3:0
304. !0===c.hasOwnProperty("b64Pad")
305. a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");
306. u=-1===b?3:0
307. l+=2){p=parseInt(a.substr(l,2),16);if(isNaN(p))throw Error("String of HEX type contains invalid characters");m=(l>>>1)+q;for(f=m>>>2;c.length<=f;)c.push(0);c[f]|=p<<8*(u+m%4*b)}return{value:c,binLen:4*g+d}};break;case "TEXT":c=function(c,h,d){var g,l,p=0,f,m,q,u,r,t;h=h||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(t=-1===
308. m+=1){r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=l[m]<<8*(t+r%4*b);p+=1}else if("UTF16BE"===a
309. "UTF16LE"===a)for(t=-1===b?2:0
310. UTF16LE"===a
311. 1===b
312. !0===l
313. !0===l&&(m=g&255,g=m<<8|g>>>8);r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=g<<8*(t+r%4*b);p+=2}return{value:h,binLen:8*p+d}};break;case "B64":c=function(a,c,d){var g=0,l,p,f,m,q,u,r,t;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");p=a.indexOf("=");a=a.replace(/\=/g
314. t=-1===b?3:0
315. q=-1===b?3:0
316. m=-1===b?3:0
317. c.b^a.b)}function A(c){var a=[],d;if("SHA-1"===c)a=[1732584193,4023233417,2562383102,271733878,3285377520];else if(0===c.lastIndexOf("SHA-",0))switch(a=
318. new b(d[2],4271175723),new b(d[3],1595750129),new b(d[4],2917565137),new b(d[5],725511199),new b(d[6],4215389547),new b(d[7],327033209)];break;default:throw Error("Unknown SHA variant");}else if(0===c.lastIndexOf("SHA3-",0)
319. 0===c.lastIndexOf("SHAKE",0))for(c=0
320. a,k){var e,h,n,g,l,p,f,m,q,u,r,t,v,w,y,A,z,x,F,B,C,D,E=[],J;if("SHA-224"===k
321. "SHA-256"===k)u=64,t=1,D=Number,v=G,w=la,y=H,A=ha,z=ja,x=da,F=fa,C=U,B=aa,J=d;else if("SHA-384"===k
322. "SHA-512"===k)u=80,t=2,D=b,v=ma,w=na,y=oa,A=ia,z=ka,x=ea,F=ga,C=ca,B=ba,J=V;else throw Error("Unexpected error in SHA-2 implementation");k=a[0];e=a[1];h=a[2];n=a[3];g=a[4];l=a[5];p=a[6];f=a[7];for(r=0
323. "function"===typeof define
324. 1>u)throw Error("numRounds must a integer >= 1");if(0===c.lastIndexOf("SHA-",0))if(q=function(b,a){return A(b,a,c)
325. c)},y=function(b,a,l,f){var g,e;if("SHA-224"===c
326. "SHA-256"===c)g=(a+65>>>9<<4)+15,e=16;else throw Error("Unexpected error in SHA-2 implementation");for(
327. c);if("SHA-224"===c)b=[f[0],f[1],f[2],f[3],f[4],f[5],f[6]];else if("SHA-256"===c)b=f;else throw Error("Unexpected error in SHA-2 implementation");return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
328. "SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a
329. return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
330. if(!0===z)throw Error("Cannot set HMAC key after calling update");f=(g
331. 5);g=a%h;z=!0};this.getHash=function(a,f){var d,h,k,q;if(!0===m)throw Error("Cannot call getHash after setting HMAC key");k=C(f);switch(a){case "HEX":d=function(a){return D(a,e,k)};break;case "B64":d=function(a){return E(a,e,k)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{h=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("format must be HEX, B64, BYTES, or ARRAYBUFFER");
332. x(c));return d(q)};this.getHMAC=function(a,f){var d,k,t,u;if(!1===m)throw Error("Cannot call getHMAC without first setting HMAC key");t=C(f);switch(a){case "HEX":d=function(a){return D(a,e,t)};break;case "B64":d=function(a){return E(a,e,t)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{d=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
333. h=(d>>>1)+q;for(e=h>>>2;b.length<=e;)b.push(0);b[e]|=k<<8*(3+h%4*-1)}return{value:b,binLen:4*f+c}};break;case "TEXT":d=function(c,b,d){var f,n,k=0,e,h,q,m,p,r;b=b||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(r=3
334. q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=n[h]<<8*(r+p%4*-1);k+=1}else if("UTF16BE"===a
335. "UTF16LE"===a)for(r=2
336. !0===n
337. e+=1){f=c.charCodeAt(e);!0===n&&(h=f&255,f=h<<8|f>>>8);p=k+q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=f<<8*(r+p%4*-1);k+=2}return{value:b,binLen:8*k+d}};break;case "B64":d=function(a,b,c){var f=0,d,k,e,h,q,m,p;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");k=a.indexOf("=");a=a.replace(/\=/g
338. 16)&65535)<<16|b&65535}function R(c,a,d,l,b){var g=(c&65535)+(a&65535)+(d&65535)+(l&65535)+(b&65535);return((c>>>16)+(a>>>16)+(d>>>16)+(l>>>16)+(b>>>16)+(g>>>16)&65535)<<16|g&65535}function x(c){var a=[],d;if(0===c.lastIndexOf("SHA-",0))switch(a=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428]
339. new m,new m,new m,new m,new m,new m];break;case "SHA-512":a=[new m,new m,new m,new m,new m,new m,new m,new m];break;default:throw Error("Unknown SHA variant");}else throw Error("No SHA variants supported");return a}function A(c,a,d){var l,b,g,f,n,k,e,h,m,r,p,w,t,x,u,z,A,B,C,D,E,F,v=[],G;if("SHA-224"===d
340. "SHA-256"===d)r=64,w=1,F=Number,t=P,x=Q,u=R,z=N,A=O,B=L,C=M,E=K,D=J,G=H;else throw Error("Unexpected error in SHA-2 implementation");d=a[0];l=a[1];b=a[2];g=a[3];f=a[4];n=a[5];k=a[6];e=a[7];for(p=
341. if (typeof(index) === 'boolean') {
342. if (typeof(index) === 'number') {
343. if (index === 0
344. _.$slides.length === 0) {
345. if (addBefore === true) {
346. if (_.options.slidesToShow === 1
347. _.options.adaptiveHeight === true
348. _.options.vertical === false) {
349. if (_.options.rtl === true
350. if (_.transformsEnabled === false) {
351. if (_.options.vertical === false) {
352. if (_.cssTransitions === false) {
353. if (_.options.rtl === true) {
354. typeof asNavFor === 'object' ) {
355. if (_.options.fade === false) {
356. if ( _.options.infinite === false ) {
357. if ( _.direction === 1
358. ( _.currentSlide + 1 ) === ( _.slideCount - 1 )) {
359. else if ( _.direction === 0 ) {
360. if ( _.currentSlide - 1 === 0 ) {
361. if (_.options.arrows === true ) {
362. if (_.options.dots === true
363. _.$slideTrack = (_.slideCount === 0) ?
364. if (_.options.centerMode === true
365. _.options.swipeToSlide === true) {
366. _.setSlideClasses(typeof _.currentSlide === 'number' ? _.currentSlide : 0);
367. if (_.options.draggable === true) {
368. if (_.respondTo === 'window') {
369. } else if (_.respondTo === 'slider') {
370. } else if (_.respondTo === 'min') {
371. if (_.originalSettings.mobileFirst === false) {
372. if (_.breakpointSettings[targetBreakpoint] === 'unslick') {
373. if (initial === true) {
374. slideOffset = indexOffset === 0 ? _.options.slidesToScroll : _.options.slidesToShow - indexOffset
375. slideOffset = indexOffset === 0 ? _.options.slidesToScroll : indexOffset
376. var index = event.data.index === 0 ? 0 :
377. if (_.options.accessibility === true) {
378. if (_.options.arrows === true
379. if (_.options.focusOnSelect === true) {
380. if (_.shouldClick === false) {
381. if (_.options.infinite === true) {
382. } else if (_.options.centerMode === true) {
383. if (_.options.vertical === true
384. _.options.centerMode === true) {
385. if (_.options.slidesToShow === 2) {
386. } else if (_.options.slidesToShow === 1) {
387. } else if (_.options.centerMode === true
388. _.options.infinite === true) {
389. if (_.options.variableWidth === true) {
390. _.options.infinite === false) {
391. if (_.options.centerMode === true) {
392. if (_.options.infinite === false) {
393. centerOffset = _.options.centerMode === true ? _.slideWidth * Math.floor(_.options.slidesToShow / 2) : 0
394. if (_.options.swipeToSlide === true) {
395. _.options.pauseOnDotsHover === true
396. if (event.keyCode === 37
397. _.options.accessibility === true) {
398. message: _.options.rtl === true ? 'next' :
399. } else if (event.keyCode === 39
400. message: _.options.rtl === true ? 'previous' : 'next'
401. if (_.options.fade === true) {
402. if (_.options.lazyLoad === 'anticipated') {
403. } else if (_.currentSlide === 0) {
404. if (_.options.lazyLoad === 'progressive') {
405. if ( _.options.adaptiveHeight === true ) {
406. if ( $.type(responsiveSettings) === 'array'
407. _.breakpoints[l] === currentBreakpoint ) {
408. index = removeBefore === true ? 0 : _.slideCount - 1
409. index = removeBefore === true ? --index : index
410. if (removeAll === true) {
411. if (_.options.vertical === false
412. _.options.variableWidth === false) {
413. } else if (_.options.variableWidth === true) {
414. if (_.options.variableWidth === false) _.$slideTrack.children('.slick-slide').width(_.slideWidth - offset);
415. if( $.type( arguments[0] ) === 'object' ) {
416. } else if ( $.type( arguments[0] ) === 'string' ) {
417. if ( arguments[0] === 'responsive'
418. $.type( arguments[1] ) === 'array' ) {
419. if ( type === 'single' ) {
420. } else if ( type === 'multiple' ) {
421. } else if ( type === 'responsive' ) {
422. if( _.options.responsive[l].breakpoint === value[item].breakpoint ) {
423. _.positionProp = _.options.vertical === true ? 'top' : 'left'
424. if (_.positionProp === 'top') {
425. if (_.options.useCSS === true) {
426. if ( typeof _.options.zIndex === 'number' ) {
427. if (bodyStyle.perspectiveProperty === undefined
428. bodyStyle.webkitPerspective === undefined) _.animType = false;
429. bodyStyle.MozPerspective === undefined) _.animType = false;
430. if (bodyStyle.msTransform === undefined) _.animType = false;
431. var evenCoef = _.options.slidesToShow % 2 === 0 ? 1 : 0
432. if (index === 0) {
433. } else if (index === _.slideCount - 1) {
434. indexOffset = _.options.infinite === true ? _.options.slidesToShow + index : index
435. if (_.options.lazyLoad === 'ondemand'
436. _.options.lazyLoad === 'anticipated') {
437. if (_.options.infinite === true
438. _.options.fade === false) {
439. if (_.animating === true
440. _.options.waitForAnimate === true) {
441. if (_.options.fade === true
442. _.currentSlide === index) {
443. if (sync === false) {
444. _.currentLeft = _.swipeLeft === null ? slideLeft : _.swipeLeft
445. if (_.options.infinite === false
446. _.options.centerMode === false
447. } else if (_.options.infinite === false
448. _.options.centerMode === true
449. return (_.options.rtl === false ? 'left' : 'right');
450. return (_.options.rtl === false ? 'right' : 'left');
451. if (_.options.verticalSwiping === true) {
452. if ( _.touchObject.curX === undefined ) {
453. if ( _.touchObject.edgeHit === true ) {
454. if ((_.options.swipe === false) || ('ontouchend' in document
455. _.options.swipe === false)) {
456. } else if (_.options.draggable === false
457. positionOffset = (_.options.rtl === false ? 1 : -1) * (_.touchObject.curX > _.touchObject.startX ? 1 : -1);
458. if ((_.currentSlide === 0
459. swipeDirection === 'right') || (_.currentSlide >= _.getDotCount()
460. swipeDirection === 'left')) {
461. _.options.touchMove === false) {
462. if (_.animating === true) {
463. if ( _.options.arrows === true
464. if (_.currentSlide === 0) {
465. _.options.centerMode === false) {
466. //Customizable =================================================================================================

== (429 điều kiện duy nhất):
------------------------------------------------------------
1. 'vi' == params['locale']) {
2. 'en' == params['locale']) {
3. errorCode == '11'"
4. isSent == false
5. <div class="payment_again" *ngIf="rePayment && !timeOut" [class.select_only]="!(isSent == false
6. <div class="col-md-12 d_btn_cancel_select" *ngIf="errorInitPage && (errorCode == 'overtime'
7. errorCode == '253')"><a
8. <a (click)="leaveNow()" class="leave_now" *ngIf="errorInitPage && !(errorCode == 'overtime'
9. errorInitPage && !(errorCode == 'overtime' || errorCode == '253') && isBack
10. if (this.res.currencies[0] == 'USD') {
11. if(_re.body.themes.theme == 'mafc'){
12. params.response_code == 'overtime') {
13. if (_re.status == '200'
14. _re.status == '201') {
15. if (_re2.status == '200'
16. _re2.status == '201') {
17. if (this.errorCode == 'overtime'
18. this.errorCode == '253') {
19. this.res.state == 'canceled') {
20. if (this.isTimePause == false) {
21. if ((dataPassed.status == '200'
22. dataPassed.status == '201') && dataPassed.body != null) {
23. if (message == '1') {
24. } else if (message == '0') {
25. if (this.locale == 'en') {
26. if (name == 'MAFC')
27. if (bankId == 3
28. bankId == 61
29. bankId == 8
30. bankId == 49
31. bankId == 48
32. bankId == 10
33. bankId == 53
34. bankId == 17
35. bankId == 65
36. bankId == 23
37. bankId == 52
38. bankId == 27
39. bankId == 66
40. bankId == 9
41. bankId == 54
42. bankId == 37
43. bankId == 38
44. bankId == 39
45. bankId == 40
46. bankId == 42
47. bankId == 44
48. bankId == 72
49. bankId == 59
50. bankId == 51
51. bankId == 64
52. bankId == 58
53. bankId == 56
54. bankId == 55
55. bankId == 60
56. bankId == 68
57. bankId == 74
58. bankId == 75
59. token_site == 'onepay'
60. this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
61. if (this._b == 18
62. this._b == 19) {
63. if (this._b == 19) {//19BIDV
64. } else if (this._b == 3
65. this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
66. if (this._b == 27) {
67. } else if (this._b == 12) {// 12SHB
68. } else if (this._b == 18) { //18Oceanbank-ocb
69. if (this._b == 19
70. this._b == 3
71. this._b == 27
72. this._b == 12) {
73. } else if (this._b == 18) {
74. if (this.checkBin(_val.value) && (this._b == 3
75. this._b == 27)) {
76. if (this._b == 3) {
77. this.cardTypeOcean == 'ATM') {
78. if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
79. this._b == 18)) {
80. if (this.checkBin(v) && (this._b == 3
81. event.inputType == 'deleteContentBackward') {
82. if (event.target.name == 'exp_date'
83. event.inputType == 'insertCompositionText') {
84. if (((this.valueDate.length == 4
85. this.valueDate.search('/') == -1) || this.valueDate.length == 5)
86. this.valueDate.length == 5)
87. if (temp.length == 0) {
88. return (counter % 10 == 0);
89. } else if (this._b == 19) {
90. } else if (this._b == 27) {
91. } else if (this._b == 12
92. _formCard.exp_date.length == 5
93. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
94. this._b == 3)) {//27-pvcombank;3-TPB ;
95. if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
96. this._b == 19
97. this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
98. if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
99. if (this.cardTypeOcean == 'IB') {
100. } else if (this.cardTypeOcean == 'MB') {
101. } else if (this.cardTypeOcean == 'ATM') {
102. this.token_site == 'onepay'
103. if (this._res_post.state == 'approved'
104. this._res_post.state == 'failed') {
105. } else if (this._res_post.state == 'authorization_required') {
106. if (this._b == 18) {
107. if (this._b == 27
108. this._b == 18) {
109. if ((cardNo.length == 16
110. if ((cardNo.length == 16 || (cardNo.length == 19
111. && ((this._b == 18
112. cardNo.length == 19) || this._b != 18)
113. if (this._b == +e.id) {
114. if (valIn == 1) {
115. } else if (valIn == 2) {
116. this._b == 3) {
117. if (this._b == 19) {
118. if (this._b == 12) {
119. if (cardType == this._translate.instant('internetbanking')
120. } else if (cardType == this._translate.instant('mobilebanking')
121. } else if (cardType == this._translate.instant('atm')
122. this._b == 18))) {
123. } else if (this._b == 18
124. this.c_expdate = !(((this.valueDate.length == 4
125. this.valueDate.length == 4
126. this.valueDate.search('/') == -1)
127. this.valueDate.length == 5))
128. if (this._b == 8) {//MB Bank
129. if (this._b == 18) {//Oceanbank
130. if (this._b == 8) {
131. if (this._b == 12) { //SHB
132. } else if (this._res.state == 'authorization_required') {
133. if (this.challengeCode == '') {
134. if (this._b == 18) {//8-MB Bank;18-oceanbank
135. if (this._b == 2
136. this._b == 5
137. this._b == 31) {
138. if (this._b == 2) {
139. } else if (this._b == 5) {
140. } else if (this._b == 6) {
141. } else if (this._b == 31) {
142. _b == 6"
143. _b == 68"
144. if (this._b == 1
145. this._b == 20
146. this._b == 36
147. this._b == 64
148. this._b == 55
149. this._b == 47
150. this._b == 48
151. this._b == 59
152. this._b == 67) {
153. return this._b == 3
154. this._b == 9
155. this._b == 16
156. this._b == 17
157. this._b == 25
158. this._b == 44
159. this._b == 57
160. this._b == 61
161. this._b == 63
162. this._b == 69
163. return this._b == 6
164. this._b == 2
165. return this._b == 18
166. return this._b == 11
167. this._b == 33
168. this._b == 39
169. this._b == 43
170. this._b == 45
171. this._b == 67
172. this._b == 72
173. this._b == 73
174. this._b == 68
175. this._b == 74
176. this._b == 75
177. if (parseInt(b) == parseInt(v.substring(0, 6))) {
178. } else if (this._b == 2) {
179. if (this._b == 5){
180. if(this._b == 12){
181. this._b == 14
182. this._b == 15
183. this._b == 24
184. this._b == 8
185. this._b == 10
186. this._b == 18
187. this._b == 22
188. this._b == 23
189. this._b == 30
190. this._b == 11
191. this._b == 12
192. this._b == 9) {
193. (cardNo.length == 19
194. (cardNo.length == 19 && (this._b == 1
195. this._b == 4
196. this._b == 67))
197. this._util.checkMod10(cardNo) == true
198. return ((value.length == 4
199. value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
200. value.length == 5) && parseInt(value.split('/')[0]
201. this._inExpDate.length == 4
202. this._inExpDate.search('/') == -1)
203. this._inExpDate.length == 5))
204. _auth==1) || (token
205. _b == 16)"
206. (token || _auth==1) && _b != 16
207. _auth==1)">
208. this._auth == 0
209. this.tokenList.length == 0) {
210. this.filteredData.length == 1
211. if ($event && ($event == 'true'
212. this._b == '55'
213. this._b == '47'
214. this._b == '48'
215. this._b == '59'
216. this._b == '73'
217. this._b == '67') {
218. this._b == '3'
219. this._b == '43'
220. this._b == '45'
221. this._b == '57'
222. this._b == '61'
223. this._b == '63'
224. this._b == '67'
225. this._b == '68'
226. this._b == '69'
227. this._b == '72'
228. this._b == '9'
229. this._b == '74'
230. this._b == '75') {
231. this._b == '36'
232. if (item['id'] == this._b) {
233. } else if (this._res_post.state == 'failed') { // lỗi mới kiểm tra sang trang lỗi
234. if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
235. } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
236. v.length == 15) || (v.length == 16
237. v.length == 19))
238. this._util.checkMod10(v) == true) {
239. cardNo.length == 15)
240. cardNo.length == 16)
241. || (cardNo.startsWith('6') && (cardNo.length == 16
242. cardNo.length == 19))
243. this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
244. this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
245. v.length == 5) {
246. v.length == 4
247. v.length == 3)
248. _val.value.length == 4
249. _val.value.length == 3)
250. this._i_csc.length == 4) ||
251. this._i_csc.length == 3)
252. token || _auth==1
253. if (!isNaN(_re.status) && (_re.status == '200'
254. _re.status == '201') && _re.body != null) {
255. if (('closed' == this._res_polling.state
256. 'canceled' == this._res_polling.state
257. 'expired' == this._res_polling.state)
258. } else if ('paid' == this._res_polling.state) {
259. this._res_polling.payments == null
260. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
261. this._paymentService.getCurrentPage() == 'enter_card') {
262. } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
263. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
264. } else if ('not_paid' == this._res_polling.state) {
265. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
266. if (!((this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '8'
267. this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '9'
268. this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '12'
269. this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '13'
270. this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '24'
271. this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '25'))) {
272. if (this.type == 5
273. } else if (this.type == 6
274. } else if (this.type == 2
275. } else if (this.type == 7
276. } else if (this.type == 8
277. } else if (this.type == 4
278. } else if (this.type == 3
279. this._auth == 0) {
280. if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
281. this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
282. if (this._res.payments[this._res.payments.length - 1].state == 'approved'
283. } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
284. } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
285. this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
286. } else if (idBrand == 'atm'
287. if ('paid' == this._res.state) {
288. this._res.merchant.token_site == 'onepay')) {
289. if (('closed' == this._res.state
290. 'canceled' == this._res.state
291. 'expired' == this._res.state
292. 'paid' == this._res.state)
293. this._res.payments == null) {
294. this._res.payments[this._res.payments.length - 1].state == 'pending') {
295. if (this._res.currencies[0] == 'USD') {
296. if (item.instrument.issuer.brand.id == 'atm') {
297. } else if (item.instrument.issuer.brand.id == 'visa'
298. item.instrument.issuer.brand.id == 'mastercard') {
299. if (item.instrument.issuer_location == 'd') {
300. } else if (item.instrument.issuer.brand.id == 'amex') {
301. } else if (item.instrument.issuer.brand.id == 'jcb') {
302. uniq.length == 1) {
303. _val == 2) {
304. if (_val == 2
305. } else if (_val == 2
306. _val == 2
307. if (this.type == 4) {
308. filteredData.length == 1) {
309. if (data._locale == 'en') {
310. if (item.available == true) {
311. data['type'] == 'Visa'
312. data['type'] == 'Master'
313. data['type'] == 'JCB'"
314. data['type'] == 'Visa'"
315. data['type'] == 'Master'"
316. data['type'] == 'Amex'"
317. i == tokenList.length - 1"
318. token_main == '1'"
319. if (message == '0') {
320. item.id == element.id ? element['active'] = true : element['active'] = false
321. item.display_cvv == true ? this.flagToken = true : this.flagToken = false
322. if (result == 'success') {
323. if (this.tokenList.length == 0) {
324. } else if (result == 'error') {
325. _val.value.trim().length == 4) || (item.brand_id != 'amex'
326. _val.value.trim().length == 3))
327. _val.value.length == 4) || (item.brand_id != 'amex'
328. _val.value.length == 3)));
329. id == 'amex' ? this.maxLength = 4 : this.maxLength = 3
330. if (_re.body.state == 'more_info_required') {
331. if (this._res_post.state == 'failed') {
332. } else if (_re.body.state == 'authorization_required') {
333. } else if (_re.body.state == 'failed') {
334. if (action == 'blur') {
335. this._i_token_otp.trim().length == 4)
336. this._i_token_otp.trim().length == 3));
337. return ((a.id == id
338. a.code == id) && a.type.includes(type));
339. if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
340. else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';
341. if (e.name == bankSwift) { // TODO: get by swift
342. if (+e.id == bankId) {
343. if (parseInt(b) == number) {
344. if (e.swiftCode == bankSwift) {
345. if (this.checkCount == 1) {
346. if (results == null) {
347. if (c.length == 3) {
348. d = d == undefined ? '.' : d
349. t = t == undefined ? '
350. return results == null ? null : results[1]
351. if (((_val.length == 4
352. _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
353. _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
354. iss_date.length == 4
355. iss_date.search('/') == -1)
356. iss_date.length == 5))
357. if (_dataCache == null) {
358. if ( (0 <= r && r <= 6 && (c == 0
359. c == 6) )
360. || (0 <= c && c <= 6 && (r == 0
361. r == 6) )
362. if (i == 0
363. _modules[r][6] = (r % 2 == 0);
364. _modules[6][c] = (c % 2 == 0);
365. if (r == -2
366. r == 2
367. c == -2
368. c == 2
369. || (r == 0
370. c == 0) ) {
371. ( (bits >> i) & 1) == 1);
372. if (col == 6) col -= 1;
373. if (_modules[row][col - c] == null) {
374. dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
375. if (bitIndex == -1) {
376. margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
377. if (typeof arguments[0] == 'object') {
378. margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
379. if (b == -1) throw 'eof';
380. if (b0 == -1) break;
381. if (typeof b == 'number') {
382. if ( (b & 0xff) == b) {
383. return function(i, j) { return (i + j) % 2 == 0
384. return function(i, j) { return i % 2 == 0
385. return function(i, j) { return j % 3 == 0
386. return function(i, j) { return (i + j) % 3 == 0
387. return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
388. return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
389. return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
390. return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
391. if (r == 0
392. c == 0) {
393. if (dark == qrcode.isDark(row + r, col + c) ) {
394. if (count == 0
395. count == 4) {
396. if (typeof num.length == 'undefined') {
397. num[offset] == 0) {
398. if (typeof rsBlock == 'undefined') {
399. return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
400. _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
401. if (data.length - i == 1) {
402. } else if (data.length - i == 2) {
403. } else if (n == 62) {
404. } else if (n == 63) {
405. if (_buflen == 0) {
406. if (c == '=') {
407. } else if (c == 0x2b) {
408. } else if (c == 0x2f) {
409. if (table.size() == (1 << bitLength) ) {
410. if (cardList.length == 1) {//TOKEN, BIDV, SHB, OCEAN, PVCOM, TP Bank
411. if ( $('.circle_v1').css('display') == 'block'
412. if ($('.circle_v2').css('display') == 'block'
413. $('.circle_v3').css('display') == 'block' ) {
414. $('.circle_v1').css('display') == 'block'
415. document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM'
416. if ( document.getElementsByClassName("select_online")[0].value == 'Easy Online Banking') {
417. if ( document.getElementsByClassName("select_online")[0].value == 'Easy Mobile Banking') {
418. if ( document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM') {
419. if ($('.circle_v1').css('display') == 'block') {
420. if ($('.circle_v1').css('display') == 'block'
421. if ( $('.circle_v1').css('display') == 'block') {
422. else if ($('.circle_v2').css('display') == 'block') {
423. if ( $('.circle_v3').css('display') == 'block' ) {
424. if ($('.circle_v2').css('display') == 'block') {
425. x = _.positionProp == 'left' ? Math.ceil(position) + 'px' : '0px'
426. y = _.positionProp == 'top' ? Math.ceil(position) + 'px' : '0px'
427. if (_.options.slidesToShow == _.options.slidesToScroll
428. if (typeof opt == 'object'
429. typeof opt == 'undefined')

!== (155 điều kiện duy nhất):
------------------------------------------------------------
1. this.lastValue !== $event.target.value)) {
2. if (YY % 400 === 0 || (YY % 100 !== 0
3. if (YYYY % 400 === 0 || (YYYY % 100 !== 0
4. event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
5. key !== '3') {
6. this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
7. codeResponse.toString() !== '0') {
8. cardNo.length !== 0) {
9. if (this.cardTypeBank !== 'bank_card_number') {
10. if (this.cardTypeBank !== 'bank_account_number') {
11. if (this.cardTypeBank !== 'bank_username') {
12. if (this.cardTypeBank !== 'bank_customer_code') {
13. this.lb_card_account !== this._translate.instant('ocb_account')) {
14. this.lb_card_account !== this._translate.instant('ocb_phone')) {
15. this.lb_card_account !== this._translate.instant('ocb_card')) {
16. this._b !== 18) || (this.cardTypeOcean === 'ATM'
17. key !== '8') {
18. codeResponse.toString() !== '0'){
19. event.inputType !== 'deleteContentBackward') || v.length == 5) {
20. if (deviceValue !== 'default') {
21. this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
22. return this._i_country_code !== 'default'
23. this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
24. this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {
25. if (_val !== 3) {
26. this._util.getElementParams(this.currentUrl, 't_id') !== '') {
27. queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
28. if (queryString !== '') {
29. if (target !== 0
30. if (e !== null) {
31. if (lang !== "vi") lang = "en";
32. this.oldValue !== this.value) {
33. if (jLinks !== undefined
34. jLinks !== null) {
35. if (jMerchantReturn !== undefined
36. jMerchantReturn !== null) {
37. if (responseCode !== undefined
38. responseCode !== null
39. if (parentRes !== "{
40. if (value !== "") {
41. if (inMonth.value !== ""
42. if (inYear.value !== ""
43. var month = inMonth.value !== ""
44. var year = parseInt("20" + (inYear.value !== ""
45. if ("2D" !== order["vpc_VerType"]) return err("MISSING_FIELD"
46. inMonth.value !== tokenInsMonth) instrument["month"] = inMonth.value;
47. inYear.value !== tokenInsYear) instrument["year"] = inYear.value;
48. if (inCvv.value !== "") instrument["cvv"] = inCvv.value;
49. if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
50. if (inNumber.style.display !== "none") instrument.number = inNumber.value;
51. if (inDate.style.display !== "none") instrument.date = inDate.value;
52. if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
53. if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
54. if (inName.style.display !== "none") instrument.name = inName.value;
55. typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
56. hrefAttr !== '#' ? hrefAttr.trim() : ''
57. $(this._element).css('visibility') !== 'hidden') {
58. if (selector !== null
59. if (selector !== null) {
60. if (typeof this._config.parent.jquery !== 'undefined') {
61. if (typeof this._config.reference.jquery !== 'undefined') {
62. if (this._config.boundary !== 'scrollParent') {
63. if (this._popper !== null) {
64. event.which !== TAB_KEYCODE)) {
65. event.which !== ESCAPE_KEYCODE
66. if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
67. event.which !== ARROW_UP_KEYCODE
68. this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
69. if (document !== event.target
70. _this5._element !== event.target
71. if (event.target !== event.currentTarget) {
72. if (typeof margin !== 'undefined') {
73. if (allowedAttributeList.indexOf(attrName) !== -1) {
74. if (uriAttrs.indexOf(attrName) !== -1) {
75. var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
76. if (_this2._hoverState !== HoverState.SHOW
77. if (_this2._popper !== null) {
78. if (data.originalPlacement !== data.placement) {
79. } else if (trigger !== Trigger.MANUAL) {
80. titleType !== 'string') {
81. if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
82. if (this.constructor.Default[key] !== this.config[key]) {
83. if (tabClass !== null
84. if (tip.getAttribute('x-placement') !== null) {
85. if (typeof config.target !== 'string') {
86. if (this._scrollHeight !== scrollHeight) {
87. if (this._activeTarget !== target) {
88. var isActiveTarget = this._activeTarget !== this._targets[i]
89. 'use strict';(function(Y){function C(c,a,b){var e=0,h=[],n=0,g,l,d,f,m,q,u,r,I=!1,v=[],w=[],t,y=!1,z=!1,x=-1;b=b||{};g=b.encoding||"UTF8";t=b.numRounds||1;if(t!==parseInt(t,10)
90. 0!==f%32
91. a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8
92. }switch(c){case "HEX":c=function(a,c,d){var g=a.length,l,p,f,m,q,u;if(0!==g%2)throw Error("String of HEX type must be in byte increments");c=c||[0];d=d||0;q=d>>>3;u=-1===b?3:0;for(l=0
93. 1!==b
94. "UTF16LE"!==a
95. "");if(-1!==p
96. function(a,c,d){var g,l,p,f,m,q;c=c||[0];d=d||0;l=d>>>3;m=-1===b?3:0;q=new Uint8Array(a);for(g=0;g<a.byteLength;g+=1)f=g+l,p=f>>>2,c.length<=p&&c.push(0),c[p]|=q[g]<<8*(m+f%4*b);return{value:c,binLen:8*a.byteLength+d}};break;default:throw Error("format must be HEX, TEXT, B64, BYTES, or ARRAYBUFFER");}return c}function y(c,a){return c<<a|c>>>32-a}function S(c,a){return 32<a?(a-=32,new b(c.b<<a|c.a>>>32-a,c.a<<a|c.b>>>32-a)):0!==a?new b(c.a<<a|c.b>>>32-a,c.b<<a|c.a>>>32-a):c}function w(c,a){return c>>>
97. a[7]);return a}function D(c,a){var d,e,h,n,g=[],l=[];if(null!==c)for(e=0
98. define.amd?define(function(){return C}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=C),exports=C):Y.jsSHA=C})(this);
99. return C}):"undefined"!==typeof exports?("undefined"!==typeof module
100. 'use strict';(function(I){function w(c,a,d){var l=0,b=[],g=0,f,n,k,e,h,q,y,p,m=!1,t=[],r=[],u,z=!1;d=d||{};f=d.encoding||"UTF8";u=d.numRounds||1;if(u!==parseInt(u,10)
101. l+=1)g[l]=c[l>>>2]>>>8*(3+l%4*-1)&255;return b}function C(c){var a={outputUpper:!1,b64Pad:"=",shakeLen:-1};c=c||{};a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");
102. if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0;d<f;d+=2){k=parseInt(a.substr(d,2),16);if(isNaN(k))throw Error("String of HEX type contains invalid characters");
103. if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0
104. "");if(-1!==k
105. define.amd?define(function(){return w}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=w),exports=w):I.jsSHA=w})(this);
106. return w}):"undefined"!==typeof exports?("undefined"!==typeof module
107. } else if (typeof exports !== 'undefined') {
108. if (typeof document.mozHidden !== 'undefined') {
109. } else if (typeof document.webkitHidden !== 'undefined') {
110. asNavFor !== null ) {
111. if ( asNavFor !== null
112. if (_.options.infinite !== true) {
113. _.options.responsive !== null) {
114. if (targetBreakpoint !== null) {
115. if (_.activeBreakpoint !== null) {
116. if (targetBreakpoint !== _.activeBreakpoint
117. triggerBreakpoint !== false ) {
118. unevenOffset = (_.slideCount % _.options.slidesToScroll !== 0);
119. _.$dots !== null) {
120. if (filter !== null) {
121. if (_.slideCount % _.options.slidesToScroll !== 0) {
122. if (_.$dots !== null) {
123. if (slideControlIndex !== -1) {
124. _.currentSlide !== 0) {
125. if ($(window).width() !== _.windowWidth) {
126. } else if ( typeof arguments[1] !== 'undefined' ) {
127. if( $.type( _.options.responsive ) !== 'array' ) {
128. if (bodyStyle.WebkitTransition !== undefined
129. bodyStyle.MozTransition !== undefined
130. bodyStyle.msTransition !== undefined) {
131. if (bodyStyle.OTransform !== undefined) {
132. if (bodyStyle.MozTransform !== undefined) {
133. if (bodyStyle.webkitTransform !== undefined) {
134. if (bodyStyle.msTransform !== undefined) {
135. if (bodyStyle.transform !== undefined
136. _.animType !== false) {
137. _.transformsEnabled = _.options.useTransform && (_.animType !== null
138. _.animType !== false);
139. if (dontAnimate !== true
140. if (dontAnimate !== true) {
141. if ( _.touchObject.startX !== _.touchObject.curX ) {
142. event.type.indexOf('mouse') !== -1) {
143. event.originalEvent.touches !== undefined ?
144. touches = event.originalEvent !== undefined ? event.originalEvent.touches : null
145. touches.length !== 1) {
146. _.touchObject.curX = touches !== undefined ? touches[0].pageX : event.clientX
147. _.touchObject.curY = touches !== undefined ? touches[0].pageY : event.clientY
148. if (event.originalEvent !== undefined
149. if (_.touchObject.fingerCount !== 1
150. event.originalEvent.touches !== undefined) {
151. _.touchObject.startX = _.touchObject.curX = touches !== undefined ? touches.pageX : event.clientX
152. _.touchObject.startY = _.touchObject.curY = touches !== undefined ? touches.pageY : event.clientY
153. if (_.$slidesCache !== null) {
154. //if (event.origin !== "http://example.com:8080") return;
155. /*if (contentType !== my_expected_type) {

!= (179 điều kiện duy nhất):
------------------------------------------------------------
1. if (params['locale'] != null
2. } else if (params['locale'] != null
3. if (userLang != null
4. if(value!=null){
5. if (_re != null
6. _re.links != null
7. _re.links.merchant_return != null
8. _re.links.merchant_return.href != null) {
9. if (_re.body != null
10. _re.body.links != null
11. _re.body.links.merchant_return != null
12. _re.body.links.merchant_return.href != null) {
13. if (this._idInvoice != null
14. this._idInvoice != 0) {
15. if (this._paymentService.getInvoiceDetail() != null) {
16. dataPassed.body != null) {
17. dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
18. dataPassed.body.themes.allow_save_token != true ? dataPassed.body.themes.allow_save_token : true
19. dataPassed.body.themes.border_color != true ? dataPassed.body.themes.border_color : ''
20. if (this._translate.currentLang != language) {
21. } else if (this._b != 18) {
22. if (this.htmlDesc != null
23. if (ua.indexOf('safari') != -1
24. if (_val.value != '') {
25. this.auth_method != null) {
26. if (this.valueDate.length != 3) {
27. if (_formCard.exp_date != null
28. if (this.cardName != null
29. if (this._res_post.links != null
30. this._res_post.links.merchant_return != null
31. this._res_post.links.merchant_return.href != null) {
32. if (this._res_post.authorization != null
33. this._res_post.authorization.links != null
34. this._res_post.authorization.links.approval != null) {
35. this._res_post.links.cancel != null) {
36. this._b != 27
37. this._b != 12
38. this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
39. this._b != 18)
40. if (this._b != 18
41. this._b != 19) {
42. if (this._res.links != null
43. this._res.links.merchant_return != null
44. this._res.links.merchant_return.href != null) {
45. if (this._res_post != null
46. this._res_post.links != null
47. if (!(_formCard.otp != null
48. if (!(_formCard.password != null
49. <div class="form-field form-field-small margin-right-2" [class.visible_hidden]="!valid_card" *ngIf="d_card_date && (_b != 3
50. d_card_date && (_b != 3 && _b != 19 && _b != 18)
51. _b != 18)">
52. *ngIf="(_b != 18
53. (_b != 18 && _b != 6 && _b != 2)
54. _b != 2)">
55. _b != 6 && _b != 2
56. this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
57. if (this._inExpDate.length != 3) {
58. if (this._b != 3
59. this._b != 9
60. this._b != 16
61. this._b != 17
62. this._b != 18
63. this._b != 19
64. this._b != 25
65. this._b != 44
66. this._b != 57
67. this._b != 59
68. this._b != 61
69. this._b != 63
70. this._b != 69
71. this._b != 6
72. this._b != 2
73. this._b != 57) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
74. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72' , '73', '74', '75'].indexOf(this._b.toString()) != -1) {
75. if (this._res_post.return_url != null) {
76. let userName = _formCard.name != null ? _formCard.name : ''
77. this._res_post.authorization.links.approval != null
78. this._res_post.authorization.links.approval.href != null) {
79. userName = paramUserName != null ? paramUserName : ''
80. if (params['locale'] != null) {
81. if ('otp' != this._paymentService.getCurrentPage()) {
82. if (!(strInstrument != null
83. if (strInstrument.substring(0, 1) != '^'
84. strInstrument.substr(strInstrument.length - 1) != '$') {
85. _showAVS!=true"
86. } else if (this._res_post.links != null
87. cardNo != null
88. v != null
89. this.c_csc = (!(_val.value != null
90. this._i_csc != null
91. this._paymentService.getState() != 'error') {
92. if (this._paymentService.getCurrentPage() != 'otp') {
93. _re.body != null) {
94. this._res_polling.links != null
95. this._res_polling.links.merchant_return != null //
96. } else if (this._res_polling.merchant != null
97. this._res_polling.merchant_invoice_reference != null
98. } else if (this._res_polling.payments != null
99. this._res_polling.links.merchant_return != null//
100. this._res_polling.payments != null
101. if (this._res_polling.payments != null
102. t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
103. if (count != 1) {
104. if (this._res.merchant != null
105. this._res.merchant_invoice_reference != null) {
106. if (this._res.merchant.address_details != null) {
107. this._res.links != null//
108. } else if (this._res.payments != null
109. this._res.payments[this._res.payments.length - 1].instrument != null
110. this._res.payments[this._res.payments.length - 1].instrument.issuer != null
111. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
112. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
113. this._res.payments[this._res.payments.length - 1].links != null
114. this._res.payments[this._res.payments.length - 1].links.cancel != null
115. this._res.payments[this._res.payments.length - 1].links.cancel.href != null
116. this._res.payments[this._res.payments.length - 1].links.update != null
117. this._res.payments[this._res.payments.length - 1].links.update.href != null) {
118. this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
119. this._res.payments[this._res.payments.length - 1].authorization != null
120. this._res.payments[this._res.payments.length - 1].authorization.links != null) {
121. this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
122. this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
123. if (this._res.payments[this._res.payments.length - 1].authorization != null
124. this._res.payments[this._res.payments.length - 1].authorization.links != null
125. auth = paramUserName != null ? paramUserName : ''
126. this._res.links != null
127. this._res.links.merchant_return != null //
128. } else if (this._res.merchant != null
129. this._res.merchant_invoice_reference != null
130. if (['mastercard', 'visa', 'amex', 'jcb', 'cup', 'card', 'np_card', 'atm'].indexOf(id) != -1) {
131. } else if (['techcombank_account', 'vib_account', 'dongabank_account', 'tpbank_account', 'bidv_account', 'pvcombank_account', 'shb_account', 'oceanbank_online_account'].indexOf(id) != -1) {
132. } else if (['oceanbank_mobile_account'].indexOf(id) != -1) {
133. } else if (['shb_customer_id'].indexOf(id) != -1) {
134. if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1) {
135. return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
136. } else if (this._res.payments != null) {
137. if (appcode != null) {
138. item['feeService']['fee'] != 0
139. item['feeService']['fee'] != 0"
140. this._res.merchant.id != 'OPTEST' ? this._res.on_off_bank : ''
141. if (_val.value != null
142. this.c_token_otp_csc = !(_val.value != null
143. if (_re.body.authorization != null
144. _re.body.authorization.links != null
145. if (_re.body.links != null
146. _re.body.links.cancel != null) {
147. if (_re.body.return_url != null) {
148. } else if (_re.body.links != null
149. if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(item.brand_id) != -1
150. return this._i_token_otp != null
151. || (id != 'amex'
152. if (idInvoice != null
153. idInvoice != 0)
154. idInvoice != 0) {
155. if (this._merchantid != null
156. this._tranref != null
157. this._state != null
158. let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
159. urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
160. if (tem != null) {
161. if ((tem = ua.match(/version\/(\d+)/i)) != null) {
162. if (v.length != 3) {
163. if (_modules[r][6] != null) {
164. if (_modules[6][c] != null) {
165. if (_modules[row][col] != null) {
166. while (buffer.getLengthInBits() % 8 != 0) {
167. if (count != numChars) {
168. throw count + ' != ' + numChars
169. while (data != 0) {
170. if (test.length != 2
171. ( (test[0] << 8) | test[1]) != code) {
172. if (_length % 3 != 0) {
173. if ( (data >>> length) != 0) {
174. return typeof _map[key] != 'undefined'
175. var source = arguments[i] != null ? arguments[i] : {
176. $('[draggable!=true]', _.$slideTrack).off('dragstart', _.preventDefault);
177. $('[draggable!=true]', _.$slideTrack).on('dragstart', _.preventDefault);
178. if( direction != 'vertical' ) {
179. if (typeof ret != 'undefined') return ret;

