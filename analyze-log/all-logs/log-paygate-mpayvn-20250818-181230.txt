====================================================================================================
TRÍCH XUẤT ĐIỀU KIỆN SO SÁNH TỪ CÁC FILE TYPESCRIPT/JAVASCRIPT/HTML
====================================================================================================
Thư mục/File nguồn: /root/projects/onepay/paygate-mpayvn/src
Thời gian: 18:12:30 18/8/2025
Tổng số file xử lý: 129
Tổng số file bị bỏ qua: 0
Tổng số điều kiện tìm thấy: 1164

THỐNG KÊ TỔNG QUAN THEO LOẠI TOÁN TỬ:
--------------------------------------------------
Strict equality (===): 217 lần
Loose equality (==): 658 lần
Strict inequality (!==): 47 lần
Loose inequality (!=): 242 lần
--------------------------------------------------

DANH SÁCH FILE ĐÃ XỬ LÝ:
--------------------------------------------------
1. 4en.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/4en.html
2. 4vn.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/4vn.html
3. app-routing.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/app-routing.module.ts
4. app.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/app.component.html
5. app.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/app.component.spec.ts
6. app.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/app.component.ts
7. app.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/app.module.ts
8. counter.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/counter.directive.spec.ts
9. counter.directive.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/counter.directive.ts
10. format-carno-input.derective.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/directives/format-carno-input.derective.ts
11. uppercase-input.directive.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/directives/uppercase-input.directive.ts
12. error.component.html (7 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/error/error.component.html
13. error.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/error/error.component.spec.ts
14. error.component.ts (17 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/error/error.component.ts
15. support-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/error/support-dialog/support-dialog.html
16. support-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/error/support-dialog/support-dialog.ts
17. format-date.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/format-date.directive.spec.ts
18. format-date.directive.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/format-date.directive.ts
19. mail.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/mail/mail.component.html
20. mail.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/mail/mail.component.ts
21. main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/main.component.html
22. main.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/main.component.spec.ts
23. main.component.ts (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/main.component.ts
24. account-main.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/account-main/account-main.component.html
25. account-main.component.ts (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/account-main/account-main.component.ts
26. bank-amount-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
27. bank-amount-dialog.component.ts (34 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
28. cancel-dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/cancel-dialog-guide-dialog.html
29. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/confirm-dialog/dialog-guide-dialog.html
30. policy-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.html
31. policy-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.ts
32. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/dialog-guide-dialog.html
33. bankaccount.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
34. bankaccount.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
35. bankaccount.component.ts (155 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
36. bank.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/model/bank.ts
37. otp-auth.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
38. otp-auth.component.ts (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
39. techcombank.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
40. techcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
41. techcombank.component.ts (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
42. vibbank.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.html
43. vibbank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.spec.ts
44. vibbank.component.ts (97 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.ts
45. vietcombank.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
46. vietcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
47. vietcombank.component.ts (95 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
48. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
49. domescard-main.component.html (7 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/domescard-main.component.html
50. domescard-main.component.ts (127 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/domescard-main.component.ts
51. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/intercard-main/dialog-guide-dialog.html
52. intercard-main.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/intercard-main/intercard-main.component.html
53. intercard-main.component.ts (69 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/intercard-main/intercard-main.component.ts
54. menu.component.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/menu.component.html
55. menu.component.ts (158 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/menu.component.ts
56. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
57. qr-dialog.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
58. qr-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
59. qr-main.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main/qr-main.component.html
60. qr-main.component.ts (25 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main/qr-main.component.ts
61. safe-html.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main/safe-html.pipe.ts
62. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/dialog/dialog-guide-dialog.html
63. qr-desktop.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.html
64. qr-desktop.component.ts (19 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.ts
65. qr-dialog.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.html
66. qr-dialog.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.ts
67. qr-guide-dialog.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.html
68. qr-guide-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.ts
69. list-bank-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.html
70. list-bank-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.ts
71. qr-main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-main.component.html
72. qr-main.component.ts (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-main.component.ts
73. qr-mobile.component.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.html
74. qr-mobile.component.ts (30 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.ts
75. safe-html.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/safe-html.pipe.ts
76. queuing.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/queuing/queuing.component.html
77. queuing.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/queuing/queuing.component.ts
78. domescard-form.component.html (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.html
79. domescard-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.ts
80. intercard-form.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.html
81. intercard-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.ts
82. deeplink.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/mpayvn-qr/deeplink/deeplink.component.html
83. deeplink.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/mpayvn-qr/deeplink/deeplink.component.ts
84. mpayvn-qr.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/mpayvn-qr/mpayvn-qr.component.html
85. mpayvn-qr.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/mpayvn-qr/mpayvn-qr.component.ts
86. qr.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/mpayvn-qr/qr/qr.component.html
87. qr.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/mpayvn-qr/qr/qr.component.ts
88. paypal-form.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.html
89. paypal-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.ts
90. qr-form.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/qr-form/qr-form.component.html
91. qr-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/qr-form/qr-form.component.ts
92. remove-token-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/token-main/remove-token-dialog/remove-token-dialog.html
93. dialog-guide-dialog.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/token-main/token-dialog/dialog-guide-dialog.html
94. token-main.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/token-main/token-main.component.html
95. token-main.component.ts (65 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/token-main/token-main.component.ts
96. overlay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/overlay/overlay.component.html
97. overlay.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/overlay/overlay.component.spec.ts
98. overlay.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/overlay/overlay.component.ts
99. bank-amount.pipe.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/pipe/bank-amount.pipe.ts
100. account-main.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/services/account-main.service.ts
101. close-dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/services/close-dialog.service.ts
102. data.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/services/data.service.ts
103. deep_link.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/services/deep_link.service.ts
104. dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/services/dialog.service.ts
105. fee.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/services/fee.service.ts
106. multiple_method.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/services/multiple_method.service.ts
107. payment.service.ts (13 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/services/payment.service.ts
108. qr.service.ts (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/services/qr.service.ts
109. time-stop.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/services/time-stop.service.ts
110. token-main.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/services/token-main.service.ts
111. index.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/translate/index.ts
112. lang-en.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/translate/lang-en.ts
113. lang-vi.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/translate/lang-vi.ts
114. translate.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/translate/translate.pipe.ts
115. translate.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/translate/translate.service.ts
116. translations.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/translate/translations.ts
117. apps-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/util/apps-info.ts
118. apps-information.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/util/apps-information.ts
119. banks-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/util/banks-info.ts
120. error-handler.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/util/error-handler.ts
121. util.ts (25 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/util/util.ts
122. qrcode.js (67 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/assets/script/qrcode.js
123. environment.prod.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/environments/environment.prod.ts
124. environment.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/environments/environment.ts
125. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/index.html
126. karma.conf.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/karma.conf.js
127. main.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/main.ts
128. polyfills.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/polyfills.ts
129. test.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/test.ts

====================================================================================================
CHI TIẾT TỪNG FILE:
====================================================================================================

📁 FILE 1: 4en.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/4en.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 2: 4vn.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/4vn.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 3: app-routing.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/app-routing.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 4: app.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/app.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 5: app.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/app.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 6: app.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/app.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 89] return lang === this._translate.currentLang

== (2 điều kiện):
  1. [Dòng 68] 'vi' == params['locale']) {
  2. [Dòng 70] 'en' == params['locale']) {

!= (3 điều kiện):
  1. [Dòng 68] if (params['locale'] != null
  2. [Dòng 70] } else if (params['locale'] != null
  3. [Dòng 77] if (userLang != null

================================================================================

📁 FILE 7: app.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/app.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 8: counter.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/counter.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 9: counter.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/counter.directive.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 10: format-carno-input.derective.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/directives/format-carno-input.derective.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 44] this.lastValue !== $event.target.value)) {

!= (1 điều kiện):
  1. [Dòng 23] if(value!=null){

================================================================================

📁 FILE 11: uppercase-input.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/directives/uppercase-input.directive.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 0 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 23] this.lastValue !== $event.target.value)) {

================================================================================

📁 FILE 12: error.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/error/error.component.html
📊 Thống kê: 7 điều kiện duy nhất
   - === : 1 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 18] isPopupSupport === 'True'"

== (6 điều kiện):
  1. [Dòng 15] errorCode == '11'"
  2. [Dòng 18] isSent == false
  3. [Dòng 40] <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
  4. [Dòng 40] (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
  5. [Dòng 42] <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
  6. [Dòng 42] !(errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending

================================================================================

📁 FILE 13: error.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/error/error.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 14: error.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/error/error.component.ts
📊 Thống kê: 17 điều kiện duy nhất
   - === : 6 lần
   - == : 11 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (6 điều kiện):
  1. [Dòng 105] params.timeout === 'true') {
  2. [Dòng 126] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  3. [Dòng 126] _re.body.state === 'unpaid');
  4. [Dòng 175] if (this.errorCode === 'overtime'
  5. [Dòng 175] this.errorCode === '253') {
  6. [Dòng 293] if (this.timeLeft === 0) {

== (11 điều kiện):
  1. [Dòng 147] _re.body.themes.theme == 'token') {
  2. [Dòng 153] params.response_code == 'overtime') {
  3. [Dòng 198] if (_re.status == '200'
  4. [Dòng 198] _re.status == '201') {
  5. [Dòng 211] if (_re2.status == '200'
  6. [Dòng 211] _re2.status == '201') {
  7. [Dòng 223] if (this.errorCode == 'overtime'
  8. [Dòng 223] this.errorCode == '253') {
  9. [Dòng 225] } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
  10. [Dòng 242] if (lastPayment?.state == 'pending') {
  11. [Dòng 291] if (this.isTimePause == false) {

================================================================================

📁 FILE 15: support-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/error/support-dialog/support-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 16: support-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/error/support-dialog/support-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 17: format-date.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/format-date.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 18: format-date.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/format-date.directive.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0
  2. [Dòng 123] YY % 4 === 0)) {
  3. [Dòng 138] if (YYYY % 400 === 0
  4. [Dòng 138] YYYY % 4 === 0)) {

!== (2 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0 || (YY % 100 !== 0
  2. [Dòng 138] if (YYYY % 400 === 0 || (YYYY % 100 !== 0

================================================================================

📁 FILE 19: mail.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/mail/mail.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 20: mail.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/mail/mail.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 21: main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 22: main.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/main.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 23: main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/main.component.ts
📊 Thống kê: 9 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 7 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 91] if ((dataPassed.status == '200'
  2. [Dòng 91] dataPassed.status == '201') && dataPassed.body != null) {

!= (7 điều kiện):
  1. [Dòng 82] if (this._idInvoice != null
  2. [Dòng 82] this._idInvoice != 0) {
  3. [Dòng 83] if (this._paymentService.getInvoiceDetail() != null) {
  4. [Dòng 91] dataPassed.body != null) {
  5. [Dòng 112] dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
  6. [Dòng 113] dataPassed.body.themes.border_color != true ? dataPassed.body.themes.border_color : ''
  7. [Dòng 167] if (this._translate.currentLang != language) {

================================================================================

📁 FILE 24: account-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/account-main/account-main.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 17] account_amain == '1'"

!= (1 điều kiện):
  1. [Dòng 73] notify != ''"

================================================================================

📁 FILE 25: account-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/account-main/account-main.component.ts
📊 Thống kê: 4 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 216] if (_re.status == '200'
  2. [Dòng 216] _re.status == '201') {

!== (1 điều kiện):
  1. [Dòng 101] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (1 điều kiện):
  1. [Dòng 82] if (params['locale'] != null) {

================================================================================

📁 FILE 26: bank-amount-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 27: bank-amount-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
📊 Thống kê: 34 điều kiện duy nhất
   - === : 0 lần
   - == : 34 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (34 điều kiện):
  1. [Dòng 41] if (this.locale == 'en') {
  2. [Dòng 55] if (name == 'MAFC')
  3. [Dòng 61] if (bankId == 3
  4. [Dòng 61] bankId == 61
  5. [Dòng 62] bankId == 8
  6. [Dòng 62] bankId == 49
  7. [Dòng 63] bankId == 48
  8. [Dòng 64] bankId == 10
  9. [Dòng 64] bankId == 53
  10. [Dòng 65] bankId == 17
  11. [Dòng 65] bankId == 65
  12. [Dòng 66] bankId == 23
  13. [Dòng 66] bankId == 52
  14. [Dòng 67] bankId == 27
  15. [Dòng 67] bankId == 66
  16. [Dòng 68] bankId == 9
  17. [Dòng 68] bankId == 54
  18. [Dòng 69] bankId == 37
  19. [Dòng 70] bankId == 38
  20. [Dòng 71] bankId == 39
  21. [Dòng 72] bankId == 40
  22. [Dòng 73] bankId == 42
  23. [Dòng 74] bankId == 44
  24. [Dòng 75] bankId == 72
  25. [Dòng 76] bankId == 59
  26. [Dòng 79] bankId == 51
  27. [Dòng 80] bankId == 64
  28. [Dòng 81] bankId == 58
  29. [Dòng 82] bankId == 56
  30. [Dòng 85] bankId == 55
  31. [Dòng 86] bankId == 60
  32. [Dòng 87] bankId == 68
  33. [Dòng 88] bankId == 74
  34. [Dòng 89] bankId == 75 //KEB HANA

================================================================================

📁 FILE 28: cancel-dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/cancel-dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 29: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/confirm-dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 30: policy-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 31: policy-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 32: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 33: bankaccount.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 43] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 58] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 58] valueDate.trim().length === 0)"

================================================================================

📁 FILE 34: bankaccount.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 35: bankaccount.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
📊 Thống kê: 155 điều kiện duy nhất
   - === : 48 lần
   - == : 73 lần
   - !== : 13 lần
   - != : 21 lần
--------------------------------------------------------------------------------

=== (48 điều kiện):
  1. [Dòng 122] if (target.tagName === 'A'
  2. [Dòng 147] if (isIE[0] === 'MSIE'
  3. [Dòng 147] +isIE[1] === 10) {
  4. [Dòng 241] if ((_val.value.substr(-1) === ' '
  5. [Dòng 241] _val.value.length === 24) {
  6. [Dòng 251] if (this.cardTypeBank === 'bank_card_number') {
  7. [Dòng 256] } else if (this.cardTypeBank === 'bank_account_number') {
  8. [Dòng 262] } else if (this.cardTypeBank === 'bank_username') {
  9. [Dòng 266] } else if (this.cardTypeBank === 'bank_customer_code') {
  10. [Dòng 272] this.cardTypeBank === 'bank_card_number'
  11. [Dòng 286] if (this.cardTypeOcean === 'IB') {
  12. [Dòng 290] } else if (this.cardTypeOcean === 'MB') {
  13. [Dòng 291] if (_val.value.substr(0, 2) === '84') {
  14. [Dòng 298] } else if (this.cardTypeOcean === 'ATM') {
  15. [Dòng 325] if (this.cardTypeBank === 'bank_account_number') {
  16. [Dòng 344] this.cardTypeBank === 'bank_card_number') {
  17. [Dòng 366] if (this.cardTypeBank === 'bank_card_number'
  18. [Dòng 366] if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  19. [Dòng 688] if (event.keyCode === 8
  20. [Dòng 688] event.key === "Backspace"
  21. [Dòng 728] if (v.length === 2
  22. [Dòng 728] this.flag.length === 3
  23. [Dòng 728] this.flag.charAt(this.flag.length - 1) === '/') {
  24. [Dòng 732] if (v.length === 1) {
  25. [Dòng 734] } else if (v.length === 2) {
  26. [Dòng 737] v.length === 2) {
  27. [Dòng 745] if (len === 2) {
  28. [Dòng 1024] if ((this.cardTypeBank === 'bank_account_number'
  29. [Dòng 1024] this.cardTypeBank === 'bank_username'
  30. [Dòng 1024] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  31. [Dòng 1029] this.cardTypeOcean === 'ATM')
  32. [Dòng 1030] || (this.cardTypeOcean === 'IB'
  33. [Dòng 1089] if (valIn === this._translate.instant('bank_card_number')) {
  34. [Dòng 1114] } else if (valIn === this._translate.instant('bank_account_number')) {
  35. [Dòng 1133] } else if (valIn === this._translate.instant('bank_username')) {
  36. [Dòng 1149] } else if (valIn === this._translate.instant('bank_customer_code')) {
  37. [Dòng 1237] if (_val.value === ''
  38. [Dòng 1237] _val.value === null
  39. [Dòng 1237] _val.value === undefined) {
  40. [Dòng 1246] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  41. [Dòng 1246] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  42. [Dòng 1253] this.cardTypeOcean === 'MB') {
  43. [Dòng 1261] this.cardTypeOcean === 'IB'
  44. [Dòng 1267] if ((this.cardTypeBank === 'bank_card_number'
  45. [Dòng 1299] if (this.cardName === undefined
  46. [Dòng 1299] this.cardName === '') {
  47. [Dòng 1307] if (this.valueDate === undefined
  48. [Dòng 1307] this.valueDate === '') {

== (73 điều kiện):
  1. [Dòng 164] if (this._b == 18
  2. [Dòng 164] this._b == 19) {
  3. [Dòng 167] if (this._b == 19) {//19BIDV
  4. [Dòng 175] } else if (this._b == 3
  5. [Dòng 175] this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  6. [Dòng 180] if (this._b == 27) {
  7. [Dòng 185] } else if (this._b == 12) {// 12SHB
  8. [Dòng 190] } else if (this._b == 18) { //18Oceanbank-ocb
  9. [Dòng 250] if (this._b == 19
  10. [Dòng 250] this._b == 3
  11. [Dòng 250] this._b == 27
  12. [Dòng 250] this._b == 12) {
  13. [Dòng 285] } else if (this._b == 18) {
  14. [Dòng 316] if (this.checkBin(_val.value) && (this._b == 3
  15. [Dòng 316] this._b == 27)) {
  16. [Dòng 321] if (this._b == 3) {
  17. [Dòng 333] this.cardTypeOcean == 'ATM') {
  18. [Dòng 346] if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  19. [Dòng 366] this._b == 18)) {
  20. [Dòng 442] if (this.checkBin(v) && (this._b == 3
  21. [Dòng 688] event.inputType == 'deleteContentBackward') {
  22. [Dòng 689] if (event.target.name == 'exp_date'
  23. [Dòng 697] event.inputType == 'insertCompositionText') {
  24. [Dòng 712] if (((this.valueDate.length == 4
  25. [Dòng 712] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  26. [Dòng 712] this.valueDate.length == 5)
  27. [Dòng 792] if (temp.length == 0) {
  28. [Dòng 799] return (counter % 10 == 0);
  29. [Dòng 819] } else if (this._b == 19) {
  30. [Dòng 821] } else if (this._b == 27) {
  31. [Dòng 826] if (this._b == 12) {
  32. [Dòng 828] if (this.cardTypeBank == 'bank_customer_code') {
  33. [Dòng 830] } else if (this.cardTypeBank == 'bank_account_number') {
  34. [Dòng 847] _formCard.exp_date.length == 5
  35. [Dòng 847] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
  36. [Dòng 847] this._b == 3)) {//27-pvcombank;3-TPB ;
  37. [Dòng 852] if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
  38. [Dòng 852] this._b == 19
  39. [Dòng 852] this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
  40. [Dòng 855] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  41. [Dòng 858] if (this.cardTypeOcean == 'IB') {
  42. [Dòng 860] } else if (this.cardTypeOcean == 'MB') {
  43. [Dòng 862] } else if (this.cardTypeOcean == 'ATM') {
  44. [Dòng 912] if (_re.status == '200'
  45. [Dòng 912] _re.status == '201') {
  46. [Dòng 917] if (this._res_post.state == 'approved'
  47. [Dòng 917] this._res_post.state == 'failed') {
  48. [Dòng 924] } else if (this._res_post.state == 'authorization_required') {
  49. [Dòng 942] if (this._b == 18) {
  50. [Dòng 947] if (this._b == 27
  51. [Dòng 947] this._b == 18) {
  52. [Dòng 1011] if (err.status == 400
  53. [Dòng 1011] err.status == 500) {
  54. [Dòng 1012] if (err.error && (err.error.code == 13
  55. [Dòng 1012] err.error.code == '13')) {
  56. [Dòng 1044] if ((cardNo.length == 16
  57. [Dòng 1044] if ((cardNo.length == 16 || (cardNo.length == 19
  58. [Dòng 1045] && ((this._b == 18
  59. [Dòng 1045] cardNo.length == 19) || this._b != 18)
  60. [Dòng 1058] if (this._b == +e.id) {
  61. [Dòng 1074] if (valIn == 1) {
  62. [Dòng 1076] } else if (valIn == 2) {
  63. [Dòng 1100] this._b == 3) {
  64. [Dòng 1107] if (this._b == 19) {
  65. [Dòng 1170] if (cardType == this._translate.instant('internetbanking')
  66. [Dòng 1178] } else if (cardType == this._translate.instant('mobilebanking')
  67. [Dòng 1186] } else if (cardType == this._translate.instant('atm')
  68. [Dòng 1246] this._b == 18))) {
  69. [Dòng 1253] } else if (this._b == 18
  70. [Dòng 1278] this.c_expdate = !(((this.valueDate.length == 4
  71. [Dòng 1310] this.valueDate.length == 4
  72. [Dòng 1310] this.valueDate.search('/') == -1)
  73. [Dòng 1311] this.valueDate.length == 5))

!== (13 điều kiện):
  1. [Dòng 241] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 898] key !== '3') {
  3. [Dòng 948] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 966] codeResponse.toString() !== '0') {
  5. [Dòng 1024] cardNo.length !== 0) {
  6. [Dòng 1096] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 1117] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 1138] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 1158] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 1170] this.lb_card_account !== this._translate.instant('ocb_account')) {
  11. [Dòng 1178] this.lb_card_account !== this._translate.instant('ocb_phone')) {
  12. [Dòng 1186] this.lb_card_account !== this._translate.instant('ocb_card')) {
  13. [Dòng 1267] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (21 điều kiện):
  1. [Dòng 196] } else if (this._b != 18) {
  2. [Dòng 202] if (this.htmlDesc != null
  3. [Dòng 238] if (ua.indexOf('safari') != -1
  4. [Dòng 248] if (_val.value != '') {
  5. [Dòng 334] this.auth_method != null) {
  6. [Dòng 690] if (this.valueDate.length != 3) {
  7. [Dòng 847] if (_formCard.exp_date != null
  8. [Dòng 852] if (this.cardName != null
  9. [Dòng 920] if (this._res_post.links != null
  10. [Dòng 920] this._res_post.links.merchant_return != null
  11. [Dòng 920] this._res_post.links.merchant_return.href != null) {
  12. [Dòng 928] if (this._res_post.authorization != null
  13. [Dòng 928] this._res_post.authorization.links != null
  14. [Dòng 928] this._res_post.authorization.links.approval != null) {
  15. [Dòng 935] this._res_post.links.cancel != null) {
  16. [Dòng 1044] this._b != 27
  17. [Dòng 1044] this._b != 12
  18. [Dòng 1044] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  19. [Dòng 1045] this._b != 18)
  20. [Dòng 1091] if (this._b != 18
  21. [Dòng 1091] this._b != 19) {

================================================================================

📁 FILE 36: bank.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/model/bank.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 37: otp-auth.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 38: otp-auth.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
📊 Thống kê: 15 điều kiện duy nhất
   - === : 0 lần
   - == : 8 lần
   - !== : 1 lần
   - != : 6 lần
--------------------------------------------------------------------------------

== (8 điều kiện):
  1. [Dòng 96] if (this._b == 8) {//MB Bank
  2. [Dòng 100] if (this._b == 18) {//Oceanbank
  3. [Dòng 140] if (this._b == 8) {
  4. [Dòng 145] if (this._b == 18) {
  5. [Dòng 169] if (_re.status == '200'
  6. [Dòng 169] _re.status == '201') {
  7. [Dòng 178] } else if (this._res.state == 'authorization_required') {
  8. [Dòng 327] if (this._b == 18) {//8-MB Bank;18-oceanbank

!== (1 điều kiện):
  1. [Dòng 184] codeResponse.toString() !== '0') {

!= (6 điều kiện):
  1. [Dòng 174] if (this._res.links != null
  2. [Dòng 174] this._res.links.merchant_return != null
  3. [Dòng 174] this._res.links.merchant_return.href != null) {
  4. [Dòng 322] if (!(_formCard.otp != null
  5. [Dòng 328] if (!(_formCard.password != null
  6. [Dòng 344] if (ua.indexOf('safari') != -1

================================================================================

📁 FILE 39: techcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 40: techcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 41: techcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
📊 Thống kê: 14 điều kiện duy nhất
   - === : 1 lần
   - == : 10 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 72] if (this.timeLeft === 0) {

== (10 điều kiện):
  1. [Dòng 63] if (this._b == 2
  2. [Dòng 63] this._b == 31) {
  3. [Dòng 102] if (this._b == 2) {
  4. [Dòng 104] } else if (this._b == 6) {
  5. [Dòng 106] } else if (this._b == 31) {
  6. [Dòng 136] if (_re.status == '200'
  7. [Dòng 136] _re.status == '201') {
  8. [Dòng 141] if (this._res_post.state == 'approved'
  9. [Dòng 141] this._res_post.state == 'failed') {
  10. [Dòng 145] } else if (this._res_post.state == 'authorization_required') {

!= (3 điều kiện):
  1. [Dòng 149] if (this._res_post.authorization != null
  2. [Dòng 149] this._res_post.authorization.links != null
  3. [Dòng 149] this._res_post.authorization.links.approval != null) {

================================================================================

📁 FILE 42: vibbank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 28] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 42] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 42] valueDate.trim().length === 0)"

================================================================================

📁 FILE 43: vibbank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 44: vibbank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.ts
📊 Thống kê: 97 điều kiện duy nhất
   - === : 37 lần
   - == : 33 lần
   - !== : 10 lần
   - != : 17 lần
--------------------------------------------------------------------------------

=== (37 điều kiện):
  1. [Dòng 111] if (target.tagName === 'A'
  2. [Dòng 136] if (isIE[0] === 'MSIE'
  3. [Dòng 136] +isIE[1] === 10) {
  4. [Dòng 167] if (this.timeLeft === 0) {
  5. [Dòng 216] if ((_val.value.substr(-1) === ' '
  6. [Dòng 216] _val.value.length === 24) {
  7. [Dòng 226] if (this.cardTypeBank === 'bank_card_number') {
  8. [Dòng 231] } else if (this.cardTypeBank === 'bank_account_number') {
  9. [Dòng 237] } else if (this.cardTypeBank === 'bank_username') {
  10. [Dòng 241] } else if (this.cardTypeBank === 'bank_customer_code') {
  11. [Dòng 262] if (this.cardTypeBank === 'bank_account_number') {
  12. [Dòng 273] this.cardTypeBank === 'bank_card_number') {
  13. [Dòng 599] if (event.keyCode === 8
  14. [Dòng 599] event.key === "Backspace"
  15. [Dòng 639] if (v.length === 2
  16. [Dòng 639] this.flag.length === 3
  17. [Dòng 639] this.flag.charAt(this.flag.length - 1) === '/') {
  18. [Dòng 643] if (v.length === 1) {
  19. [Dòng 645] } else if (v.length === 2) {
  20. [Dòng 648] v.length === 2) {
  21. [Dòng 656] if (len === 2) {
  22. [Dòng 893] if ((this.cardTypeBank === 'bank_account_number'
  23. [Dòng 893] this.cardTypeBank === 'bank_username'
  24. [Dòng 893] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  25. [Dòng 943] if (valIn === this._translate.instant('bank_card_number')) {
  26. [Dòng 962] } else if (valIn === this._translate.instant('bank_account_number')) {
  27. [Dòng 974] } else if (valIn === this._translate.instant('bank_username')) {
  28. [Dòng 985] } else if (valIn === this._translate.instant('bank_customer_code')) {
  29. [Dòng 1042] if (_val.value === ''
  30. [Dòng 1042] _val.value === null
  31. [Dòng 1042] _val.value === undefined) {
  32. [Dòng 1053] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  33. [Dòng 1063] if ((this.cardTypeBank === 'bank_card_number'
  34. [Dòng 1095] if (this.cardName === undefined
  35. [Dòng 1095] this.cardName === '') {
  36. [Dòng 1103] if (this.valueDate === undefined
  37. [Dòng 1103] this.valueDate === '') {

== (33 điều kiện):
  1. [Dòng 150] if (this._b == 5) {//5-vib;
  2. [Dòng 225] if (this._b == 5) {
  3. [Dòng 259] if (this.checkBin(_val.value) && (this._b == 5)) {
  4. [Dòng 275] if (this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  5. [Dòng 337] if (this.checkBin(v) && (this._b == 5)) {
  6. [Dòng 599] event.inputType == 'deleteContentBackward') {
  7. [Dòng 600] if (event.target.name == 'exp_date'
  8. [Dòng 608] event.inputType == 'insertCompositionText') {
  9. [Dòng 623] if (((this.valueDate.length == 4
  10. [Dòng 623] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  11. [Dòng 623] this.valueDate.length == 5)
  12. [Dòng 703] if (temp.length == 0) {
  13. [Dòng 710] return (counter % 10 == 0);
  14. [Dòng 741] _formCard.exp_date.length == 5
  15. [Dòng 741] this._b == 5) {//5 vib ;
  16. [Dòng 746] this._b == 5) {//5vib;
  17. [Dòng 793] if (_re.status == '200'
  18. [Dòng 793] _re.status == '201') {
  19. [Dòng 798] if (this._res_post.state == 'approved'
  20. [Dòng 798] this._res_post.state == 'failed') {
  21. [Dòng 805] } else if (this._res_post.state == 'authorization_required') {
  22. [Dòng 898] if ((cardNo.length == 16
  23. [Dòng 898] if ((cardNo.length == 16 || (cardNo.length == 19
  24. [Dòng 899] && ((this._b == 18
  25. [Dòng 899] cardNo.length == 19) || this._b != 18)
  26. [Dòng 912] if (this._b == +e.id) {
  27. [Dòng 928] if (valIn == 1) {
  28. [Dòng 930] } else if (valIn == 2) {
  29. [Dòng 1053] this._b == 18)) {
  30. [Dòng 1075] this.c_expdate = !(((this.valueDate.length == 4
  31. [Dòng 1106] this.valueDate.length == 4
  32. [Dòng 1106] this.valueDate.search('/') == -1)
  33. [Dòng 1107] this.valueDate.length == 5))

!== (10 điều kiện):
  1. [Dòng 216] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 779] key !== '3') {
  3. [Dòng 827] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 844] codeResponse.toString() !== '0') {
  5. [Dòng 893] cardNo.length !== 0) {
  6. [Dòng 950] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 965] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 979] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 992] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 1063] this._b !== 18) || (this._b == 18)) {

!= (17 điều kiện):
  1. [Dòng 177] if (this.htmlDesc != null
  2. [Dòng 213] if (ua.indexOf('safari') != -1
  3. [Dòng 223] if (_val.value != '') {
  4. [Dòng 601] if (this.valueDate.length != 3) {
  5. [Dòng 741] if (_formCard.exp_date != null
  6. [Dòng 746] if (this.cardName != null
  7. [Dòng 801] if (this._res_post.links != null
  8. [Dòng 801] this._res_post.links.merchant_return != null
  9. [Dòng 801] this._res_post.links.merchant_return.href != null) {
  10. [Dòng 809] if (this._res_post.authorization != null
  11. [Dòng 809] this._res_post.authorization.links != null
  12. [Dòng 809] this._res_post.authorization.links.approval != null) {
  13. [Dòng 816] this._res_post.links.cancel != null) {
  14. [Dòng 898] this._b != 27
  15. [Dòng 898] this._b != 12
  16. [Dòng 898] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  17. [Dòng 899] this._b != 18)

================================================================================

📁 FILE 45: vietcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 31] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  2. [Dòng 31] _inExpDate.trim().length === 0)"

== (1 điều kiện):
  1. [Dòng 90] _b==68"

================================================================================

📁 FILE 46: vietcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 47: vietcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
📊 Thống kê: 95 điều kiện duy nhất
   - === : 5 lần
   - == : 62 lần
   - !== : 2 lần
   - != : 26 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 96] if (target.tagName === 'A'
  2. [Dòng 236] if (event.keyCode === 8
  3. [Dòng 236] event.key === "Backspace"
  4. [Dòng 479] if (approval.method === 'REDIRECT') {
  5. [Dòng 482] } else if (approval.method === 'POST_REDIRECT') {

== (62 điều kiện):
  1. [Dòng 135] if (this._b == 1
  2. [Dòng 135] this._b == 20
  3. [Dòng 135] this._b == 36
  4. [Dòng 135] this._b == 64
  5. [Dòng 135] this._b == 55
  6. [Dòng 135] this._b == 47
  7. [Dòng 135] this._b == 48
  8. [Dòng 135] this._b == 59) {
  9. [Dòng 159] return this._b == 11
  10. [Dòng 159] this._b == 33
  11. [Dòng 159] this._b == 39
  12. [Dòng 159] this._b == 43
  13. [Dòng 159] this._b == 45
  14. [Dòng 159] this._b == 67
  15. [Dòng 159] this._b == 68
  16. [Dòng 159] this._b == 72
  17. [Dòng 159] this._b == 73
  18. [Dòng 159] this._b == 74
  19. [Dòng 159] this._b == 75
  20. [Dòng 163] return this._b == 9
  21. [Dòng 163] this._b == 16
  22. [Dòng 163] this._b == 17
  23. [Dòng 163] this._b == 25
  24. [Dòng 163] this._b == 44
  25. [Dòng 164] this._b == 54
  26. [Dòng 164] this._b == 57
  27. [Dòng 164] this._b == 59
  28. [Dòng 164] this._b == 61
  29. [Dòng 164] this._b == 63
  30. [Dòng 164] this._b == 69
  31. [Dòng 236] event.inputType == 'deleteContentBackward') {
  32. [Dòng 237] if (event.target.name == 'exp_date'
  33. [Dòng 245] event.inputType == 'insertCompositionText') {
  34. [Dòng 369] if (this._res_post.state == 'approved'
  35. [Dòng 369] this._res_post.state == 'failed') {
  36. [Dòng 419] } else if (this._res_post.state == 'authorization_required') {
  37. [Dòng 441] this._b == 14
  38. [Dòng 441] this._b == 15
  39. [Dòng 441] this._b == 24
  40. [Dòng 441] this._b == 8
  41. [Dòng 441] this._b == 10
  42. [Dòng 441] this._b == 22
  43. [Dòng 441] this._b == 23
  44. [Dòng 441] this._b == 30
  45. [Dòng 441] this._b == 11
  46. [Dòng 441] this._b == 9) {
  47. [Dòng 510] if (err.status == 400
  48. [Dòng 510] err.status == 500) {
  49. [Dòng 511] if (err.error && (err.error.code == 13
  50. [Dòng 511] err.error.code == '13')) {
  51. [Dòng 524] if ((cardNo.length == 16
  52. [Dòng 525] (cardNo.length == 19
  53. [Dòng 525] (cardNo.length == 19 && (this._b == 1
  54. [Dòng 525] this._b == 4
  55. [Dòng 525] this._b == 59))
  56. [Dòng 527] this._util.checkMod10(cardNo) == true
  57. [Dòng 563] return ((value.length == 4
  58. [Dòng 563] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  59. [Dòng 563] value.length == 5) && parseInt(value.split('/')[0]
  60. [Dòng 597] this._inExpDate.length == 4
  61. [Dòng 597] this._inExpDate.search('/') == -1)
  62. [Dòng 598] this._inExpDate.length == 5))

!== (2 điều kiện):
  1. [Dòng 382] codeResponse.toString() !== '0') {
  2. [Dòng 442] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (26 điều kiện):
  1. [Dòng 140] if (this.htmlDesc != null
  2. [Dòng 173] if (ua.indexOf('safari') != -1
  3. [Dòng 238] if (this._inExpDate.length != 3) {
  4. [Dòng 318] if (this._b != 9
  5. [Dòng 318] this._b != 16
  6. [Dòng 318] this._b != 17
  7. [Dòng 318] this._b != 25
  8. [Dòng 318] this._b != 44
  9. [Dòng 318] this._b != 54
  10. [Dòng 319] this._b != 57
  11. [Dòng 319] this._b != 59
  12. [Dòng 319] this._b != 61
  13. [Dòng 319] this._b != 63
  14. [Dòng 319] this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
  15. [Dòng 332] '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72' , '73', '74', '75'].indexOf(this._b.toString()) != -1) {
  16. [Dòng 371] if (this._res_post.return_url != null) {
  17. [Dòng 374] if (this._res_post.links != null
  18. [Dòng 374] this._res_post.links.merchant_return != null
  19. [Dòng 374] this._res_post.links.merchant_return.href != null) {
  20. [Dòng 424] if (this._res_post.authorization != null
  21. [Dòng 424] this._res_post.authorization.links != null
  22. [Dòng 429] this._res_post.links.cancel != null) {
  23. [Dòng 435] let userName = _formCard.name != null ? _formCard.name : ''
  24. [Dòng 436] this._res_post.authorization.links.approval != null
  25. [Dòng 436] this._res_post.authorization.links.approval.href != null) {
  26. [Dòng 439] userName = paramUserName != null ? paramUserName : ''

================================================================================

📁 FILE 48: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 49: domescard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/domescard-main.component.html
📊 Thống kê: 7 điều kiện duy nhất
   - === : 1 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 16] filteredData.length === 0"

== (6 điều kiện):
  1. [Dòng 24] ((!token && _auth==0 && vietcombankGroupSelected) || (token && _b == 16))
  2. [Dòng 24] _b == 16))">
  3. [Dòng 29] !token&&_auth==0 && techcombankGroupSelected
  4. [Dòng 34] !token&&_auth==0 && bankaccountGroupSelected
  5. [Dòng 39] !token && _auth==0 && vibbankGroupSelected
  6. [Dòng 44] (token || _auth==1) && _b != 16

================================================================================

📁 FILE 50: domescard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/domescard-main/domescard-main.component.ts
📊 Thống kê: 127 điều kiện duy nhất
   - === : 24 lần
   - == : 96 lần
   - !== : 1 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (24 điều kiện):
  1. [Dòng 271] if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
  2. [Dòng 272] filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
  3. [Dòng 313] if (valOut === 'auth') {
  4. [Dòng 427] if (this._b === '1'
  5. [Dòng 427] this._b === '20'
  6. [Dòng 427] this._b === '64') {
  7. [Dòng 430] if (this._b === '36'
  8. [Dòng 430] this._b === '18'
  9. [Dòng 430] this._b === '19'
  10. [Dòng 433] if (this._b === '19'
  11. [Dòng 433] this._b === '16'
  12. [Dòng 433] this._b === '25'
  13. [Dòng 433] this._b === '33'
  14. [Dòng 434] this._b === '39'
  15. [Dòng 434] this._b === '9'
  16. [Dòng 434] this._b === '11'
  17. [Dòng 434] this._b === '17'
  18. [Dòng 435] this._b === '36'
  19. [Dòng 435] this._b === '44'
  20. [Dòng 435] this._b === '12'
  21. [Dòng 436] this._b === '64'
  22. [Dòng 439] if (this._b === '20'
  23. [Dòng 442] if (this._b === '12'
  24. [Dòng 442] this._b === '18') {

== (96 điều kiện):
  1. [Dòng 184] this._auth == 0
  2. [Dòng 184] this.tokenList.length == 0) {
  3. [Dòng 257] this.filteredData.length == 1
  4. [Dòng 294] $event == 'true') {
  5. [Dòng 359] if (bankId == 1
  6. [Dòng 359] bankId == 4
  7. [Dòng 359] bankId == 7
  8. [Dòng 359] bankId == 8
  9. [Dòng 359] bankId == 9
  10. [Dòng 359] bankId == 10
  11. [Dòng 359] bankId == 11
  12. [Dòng 359] bankId == 14
  13. [Dòng 359] bankId == 15
  14. [Dòng 360] bankId == 16
  15. [Dòng 360] bankId == 17
  16. [Dòng 360] bankId == 20
  17. [Dòng 360] bankId == 22
  18. [Dòng 360] bankId == 23
  19. [Dòng 360] bankId == 24
  20. [Dòng 360] bankId == 25
  21. [Dòng 360] bankId == 30
  22. [Dòng 360] bankId == 33
  23. [Dòng 361] bankId == 34
  24. [Dòng 361] bankId == 35
  25. [Dòng 361] bankId == 36
  26. [Dòng 361] bankId == 37
  27. [Dòng 361] bankId == 38
  28. [Dòng 361] bankId == 39
  29. [Dòng 361] bankId == 40
  30. [Dòng 361] bankId == 41
  31. [Dòng 361] bankId == 42
  32. [Dòng 362] bankId == 43
  33. [Dòng 362] bankId == 44
  34. [Dòng 362] bankId == 45
  35. [Dòng 362] bankId == 46
  36. [Dòng 362] bankId == 47
  37. [Dòng 362] bankId == 48
  38. [Dòng 362] bankId == 49
  39. [Dòng 362] bankId == 50
  40. [Dòng 362] bankId == 51
  41. [Dòng 363] bankId == 52
  42. [Dòng 363] bankId == 53
  43. [Dòng 363] bankId == 54
  44. [Dòng 363] bankId == 55
  45. [Dòng 363] bankId == 56
  46. [Dòng 363] bankId == 57
  47. [Dòng 363] bankId == 58
  48. [Dòng 363] bankId == 59
  49. [Dòng 363] bankId == 60
  50. [Dòng 364] bankId == 61
  51. [Dòng 364] bankId == 62
  52. [Dòng 364] bankId == 63
  53. [Dòng 364] bankId == 64
  54. [Dòng 364] bankId == 65
  55. [Dòng 364] bankId == 66
  56. [Dòng 364] bankId == 67
  57. [Dòng 364] bankId == 68
  58. [Dòng 364] bankId == 69
  59. [Dòng 364] bankId == 70
  60. [Dòng 365] bankId == 71
  61. [Dòng 365] bankId == 72
  62. [Dòng 365] bankId == 73
  63. [Dòng 365] bankId == 32
  64. [Dòng 365] bankId == 74
  65. [Dòng 365] bankId == 75) {
  66. [Dòng 367] } else if (bankId == 6
  67. [Dòng 367] bankId == 31
  68. [Dòng 367] bankId == 80
  69. [Dòng 367] bankId == 2) {
  70. [Dòng 369] } else if (bankId == 3
  71. [Dòng 369] bankId == 12
  72. [Dòng 369] bankId == 18
  73. [Dòng 369] bankId == 19
  74. [Dòng 369] bankId == 27) {
  75. [Dòng 371] } else if (bankId == 5) {
  76. [Dòng 430] this._b == '55'
  77. [Dòng 430] this._b == '47'
  78. [Dòng 430] this._b == '48'
  79. [Dòng 430] this._b == '59'
  80. [Dòng 430] this._b == '73') {
  81. [Dòng 433] this._b == '3'
  82. [Dòng 434] this._b == '43'
  83. [Dòng 434] this._b == '45'
  84. [Dòng 435] this._b == '54'
  85. [Dòng 435] this._b == '57'
  86. [Dòng 436] this._b == '61'
  87. [Dòng 436] this._b == '63'
  88. [Dòng 436] this._b == '67'
  89. [Dòng 436] this._b == '68'
  90. [Dòng 436] this._b == '69'
  91. [Dòng 436] this._b == '72'
  92. [Dòng 436] this._b == '73'
  93. [Dòng 436] this._b == '74'
  94. [Dòng 436] this._b == '75') {
  95. [Dòng 439] this._b == '36'
  96. [Dòng 459] if (item['id'] == this._b) {

!== (1 điều kiện):
  1. [Dòng 119] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (6 điều kiện):
  1. [Dòng 171] if (params['locale'] != null) {
  2. [Dòng 177] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 207] if (!(strInstrument != null
  4. [Dòng 210] if (strInstrument.substring(0, 1) != '^'
  5. [Dòng 210] strInstrument.substr(strInstrument.length - 1) != '$') {
  6. [Dòng 339] if (bankid != null) {

================================================================================

📁 FILE 51: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/intercard-main/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 52: intercard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/intercard-main/intercard-main.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 79] _showAVS!=true"

================================================================================

📁 FILE 53: intercard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/intercard-main/intercard-main.component.ts
📊 Thống kê: 69 điều kiện duy nhất
   - === : 8 lần
   - == : 41 lần
   - !== : 8 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 155] if (target.tagName === 'A'
  2. [Dòng 295] if (_formCard.country === 'default') {
  3. [Dòng 619] if (event.keyCode === 8
  4. [Dòng 619] event.key === "Backspace"
  5. [Dòng 694] if ((v.substr(-1) === ' '
  6. [Dòng 918] if (deviceValue === 'CA'
  7. [Dòng 918] deviceValue === 'US') {
  8. [Dòng 939] this.c_country = _val.value === 'default'

== (41 điều kiện):
  1. [Dòng 360] if (this._res_post.state == 'approved'
  2. [Dòng 360] this._res_post.state == 'failed') {
  3. [Dòng 386] } if (this._res_post.state == 'failed') {
  4. [Dòng 416] } else if (this._res_post.state == 'authorization_required') {
  5. [Dòng 417] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  6. [Dòng 429] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  7. [Dòng 455] if (err.status == 400
  8. [Dòng 455] err.status == 500) {
  9. [Dòng 456] if (err.error && (err.error.code == 8
  10. [Dòng 456] err.error.code == '8')) {
  11. [Dòng 457] if (this._type == 5
  12. [Dòng 457] this._type == 6) {
  13. [Dòng 459] } else if (this._type == 7
  14. [Dòng 459] this._type == 8) {
  15. [Dòng 462] } else if (err.error && (err.error.code == 13
  16. [Dòng 462] err.error.code == '13')) {
  17. [Dòng 528] v.length == 15) || (v.length == 16
  18. [Dòng 528] v.length == 19))
  19. [Dòng 529] this._util.checkMod10(v) == true) {
  20. [Dòng 573] cardNo.length == 15)
  21. [Dòng 575] cardNo.length == 16)
  22. [Dòng 576] cardNo.startsWith('81')) && (cardNo.length == 16
  23. [Dòng 576] cardNo.length == 19))
  24. [Dòng 619] event.inputType == 'deleteContentBackward') {
  25. [Dòng 620] if (event.target.name == 'exp_date'
  26. [Dòng 628] event.inputType == 'insertCompositionText') {
  27. [Dòng 643] if (((this.valueDate.length == 4
  28. [Dòng 643] this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  29. [Dòng 643] this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  30. [Dòng 694] v.length == 5) {
  31. [Dòng 702] v.length == 4
  32. [Dòng 706] v.length == 3)
  33. [Dòng 736] _val.value.length == 4
  34. [Dòng 740] _val.value.length == 3)
  35. [Dòng 896] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  36. [Dòng 896] this.valueDate.length == 5)
  37. [Dòng 1064] this.valueDate.length == 4
  38. [Dòng 1064] this.valueDate.search('/') == -1)
  39. [Dòng 1065] this.valueDate.length == 5))
  40. [Dòng 1078] this._i_csc.length == 4) ||
  41. [Dòng 1082] this._i_csc.length == 3)

!== (8 điều kiện):
  1. [Dòng 342] key !== '8') {
  2. [Dòng 370] codeResponse.toString() !== '0'){
  3. [Dòng 694] event.inputType !== 'deleteContentBackward') || v.length == 5) {
  4. [Dòng 915] if (deviceValue !== 'default') {
  5. [Dòng 932] this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
  6. [Dòng 1089] return this._i_country_code !== 'default'
  7. [Dòng 1146] this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
  8. [Dòng 1153] this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {

!= (12 điều kiện):
  1. [Dòng 198] if (params['locale'] != null) {
  2. [Dòng 204] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 362] if (this._res_post.return_url != null) {
  4. [Dòng 364] } else if (this._res_post.links != null
  5. [Dòng 364] this._res_post.links.merchant_return != null
  6. [Dòng 364] this._res_post.links.merchant_return.href != null) {
  7. [Dòng 510] if (ua.indexOf('safari') != -1
  8. [Dòng 573] cardNo != null
  9. [Dòng 621] if (this.valueDate.length != 3) {
  10. [Dòng 701] v != null
  11. [Dòng 735] this.c_csc = (!(_val.value != null
  12. [Dòng 1076] this._i_csc != null

================================================================================

📁 FILE 54: menu.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/menu.component.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 5 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 186] d_vrbank===true"

== (5 điều kiện):
  1. [Dòng 124] merchantId == 'OPTEST'
  2. [Dòng 144] sortMethodArray[i].trim()=='International'"
  3. [Dòng 152] sortMethodArray[i].trim()=='Domestic'"
  4. [Dòng 162] sortMethodArray[i].trim()=='QR'"
  5. [Dòng 171] sortMethodArray[i].trim()=='Paypal'"

================================================================================

📁 FILE 55: menu.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/menu.component.ts
📊 Thống kê: 158 điều kiện duy nhất
   - === : 9 lần
   - == : 87 lần
   - !== : 3 lần
   - != : 59 lần
--------------------------------------------------------------------------------

=== (9 điều kiện):
  1. [Dòng 871] this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number === 1
  2. [Dòng 930] if (this._res.state === 'unpaid'
  3. [Dòng 930] this._res.state === 'not_paid') {
  4. [Dòng 1046] if ('op' === auth
  5. [Dòng 1081] } else if ('bank' === auth
  6. [Dòng 1086] if (approval.method === 'REDIRECT') {
  7. [Dòng 1089] } else if (approval.method === 'POST_REDIRECT') {
  8. [Dòng 1323] return id === 'amex' ? '1234' : '123'
  9. [Dòng 1508] if (this.timeLeftPaypal === 0) {

== (87 điều kiện):
  1. [Dòng 198] if (el == 5) {
  2. [Dòng 200] } else if (el == 6) {
  3. [Dòng 202] } else if (el == 7) {
  4. [Dòng 204] } else if (el == 8) {
  5. [Dòng 206] } else if (el == 2) {
  6. [Dòng 208] } else if (el == 4) {
  7. [Dòng 210] } else if (el == 3) {
  8. [Dòng 239] if (!isNaN(_re.status) && (_re.status == '200'
  9. [Dòng 239] _re.status == '201') && _re.body != null) {
  10. [Dòng 244] if (('closed' == this._res_polling.state
  11. [Dòng 244] 'canceled' == this._res_polling.state
  12. [Dòng 244] 'expired' == this._res_polling.state)
  13. [Dòng 264] } else if ('paid' == this._res_polling.state) {
  14. [Dòng 270] this._res_polling.payments == null
  15. [Dòng 279] if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'authorization_required') {
  16. [Dòng 283] } else if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  17. [Dòng 290] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  18. [Dòng 292] this._paymentService.getCurrentPage() == 'enter_card') {
  19. [Dòng 295] } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
  20. [Dòng 295] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
  21. [Dòng 313] } else if ('not_paid' == this._res_polling.state) {
  22. [Dòng 325] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
  23. [Dòng 482] if (message == '1') {
  24. [Dòng 492] if (this.checkInvoiceState() == 1) {
  25. [Dòng 501] if (_re.status == '200'
  26. [Dòng 501] _re.status == '201') {
  27. [Dòng 507] this.version2 = _re.body?.merchant?.qr_version == "2"
  28. [Dòng 534] if (this.type == 5
  29. [Dòng 537] } else if (this.type == 6
  30. [Dòng 540] } else if (this.type == 2
  31. [Dòng 543] } else if (this.type == 7
  32. [Dòng 546] } else if (this.type == 8
  33. [Dòng 549] } else if (this.type == 4
  34. [Dòng 552] } else if (this.type == 3
  35. [Dòng 591] if (this.themeConfig.default_method == 'International'
  36. [Dòng 593] } else if (this.themeConfig.default_method == 'Domestic'
  37. [Dòng 595] } else if (this.themeConfig.default_method == 'QR'
  38. [Dòng 597] } else if (this.themeConfig.default_method == 'Paypal'
  39. [Dòng 655] if ('REDIRECT' == approval.method) {
  40. [Dòng 657] } else if ('POST_REDIRECT' == approval.method) {
  41. [Dòng 715] if (('closed' == this._res.state
  42. [Dòng 715] 'canceled' == this._res.state
  43. [Dòng 715] 'expired' == this._res.state
  44. [Dòng 715] 'paid' == this._res.state)
  45. [Dòng 872] if ((this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number) == 0
  46. [Dòng 895] this._auth == 0) {
  47. [Dòng 931] if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
  48. [Dòng 931] this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
  49. [Dòng 933] if (this._res.payments[this._res.payments.length - 1].state == 'approved'
  50. [Dòng 935] } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
  51. [Dòng 979] if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required') {
  52. [Dòng 985] } else if (this._res.payments[this._res.payments.length - 1].state == 'pending') {
  53. [Dòng 991] } else if (this._res.payments[this._res.payments.length - 1].state == 'canceled') {
  54. [Dòng 995] } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  55. [Dòng 995] this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
  56. [Dòng 1028] if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  57. [Dòng 1032] } else if (idBrand == 'atm'
  58. [Dòng 1109] if ('paid' == this._res.state) {
  59. [Dòng 1110] this._res.merchant.token_site == 'onepay')) {
  60. [Dòng 1153] this._res.payments == null) {
  61. [Dòng 1155] this._res.payments[this._res.payments.length - 1].state == 'pending') {
  62. [Dòng 1165] if (this._res.currencies[0] == 'USD') {
  63. [Dòng 1229] if (item.instrument.issuer.brand.id == 'atm') {
  64. [Dòng 1231] } else if (item.instrument.issuer.brand.id == 'visa'
  65. [Dòng 1231] item.instrument.issuer.brand.id == 'mastercard') {
  66. [Dòng 1232] if (item.instrument.issuer_location == 'd') {
  67. [Dòng 1237] } else if (item.instrument.issuer.brand.id == 'amex') {
  68. [Dòng 1243] } else if (item.instrument.issuer.brand.id == 'jcb') {
  69. [Dòng 1259] uniq.length == 1) {
  70. [Dòng 1387] if (type == 'qrv1') {
  71. [Dòng 1397] if (type == 'mobile') {
  72. [Dòng 1399] e.type == 'ewallet'
  73. [Dòng 1399] e.code == 'momo')) {
  74. [Dòng 1407] } else if (type == 'desktop') {
  75. [Dòng 1408] e.type == 'vnpayqr') || (regex.test(strTest)
  76. [Dòng 1452] _val == 2) {
  77. [Dòng 1479] if (_val == 2
  78. [Dòng 1481] } else if (_val == 2
  79. [Dòng 1487] _val == 2
  80. [Dòng 1496] if (this.type == 4) {
  81. [Dòng 1576] if (this._res_post.state == 'approved'
  82. [Dòng 1576] this._res_post.state == 'failed') {
  83. [Dòng 1585] } else if (this._res_post.state == 'authorization_required') {
  84. [Dòng 1586] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  85. [Dòng 1600] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  86. [Dòng 1659] filteredData.length == 1) {
  87. [Dòng 1715] if (data._locale == 'en') {

!== (3 điều kiện):
  1. [Dòng 1058] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  2. [Dòng 1443] if (_val !== 3) {
  3. [Dòng 1447] this._util.getElementParams(this.currentUrl, 't_id') !== '') {

!= (59 điều kiện):
  1. [Dòng 232] if (this._idInvoice != null
  2. [Dòng 232] this._paymentService.getState() != 'error') {
  3. [Dòng 238] if (this._paymentService.getCurrentPage() != 'otp') {
  4. [Dòng 239] _re.body != null) {
  5. [Dòng 245] this._res_polling.links != null
  6. [Dòng 245] this._res_polling.links.merchant_return != null //
  7. [Dòng 270] } else if (this._res_polling.merchant != null
  8. [Dòng 270] this._res_polling.merchant_invoice_reference != null
  9. [Dòng 272] } else if (this._res_polling.payments != null
  10. [Dòng 296] this._res_polling.links.merchant_return != null//
  11. [Dòng 316] this._res_polling.payments != null
  12. [Dòng 324] if (this._res_polling.payments != null
  13. [Dòng 328] t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
  14. [Dòng 461] this.type.toString().length != 0) {
  15. [Dòng 467] if (params['locale'] != null) {
  16. [Dòng 475] if ('otp' != this._paymentService.getCurrentPage()) {
  17. [Dòng 490] if (this._paymentService.getInvoiceDetail() != null) {
  18. [Dòng 716] this._res.links != null
  19. [Dòng 716] this._res.links.merchant_return != null
  20. [Dòng 909] if (count != 1) {
  21. [Dòng 919] if (this._res.merchant != null
  22. [Dòng 919] this._res.merchant_invoice_reference != null) {
  23. [Dòng 923] if (this._res.merchant.address_details != null) {
  24. [Dòng 931] this._res.links != null//
  25. [Dòng 974] } else if (this._res.payments != null
  26. [Dòng 996] this._res.payments[this._res.payments.length - 1].instrument != null
  27. [Dòng 996] this._res.payments[this._res.payments.length - 1].instrument.issuer != null
  28. [Dòng 997] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
  29. [Dòng 997] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
  30. [Dòng 998] this._res.payments[this._res.payments.length - 1].links != null
  31. [Dòng 998] this._res.payments[this._res.payments.length - 1].links.cancel != null
  32. [Dòng 998] this._res.payments[this._res.payments.length - 1].links.cancel.href != null
  33. [Dòng 1014] this._res.payments[this._res.payments.length - 1].links.update != null
  34. [Dòng 1014] this._res.payments[this._res.payments.length - 1].links.update.href != null) {
  35. [Dòng 1032] this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
  36. [Dòng 1033] this._res.payments[this._res.payments.length - 1].authorization != null
  37. [Dòng 1033] this._res.payments[this._res.payments.length - 1].authorization.links != null) {
  38. [Dòng 1046] this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
  39. [Dòng 1046] this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
  40. [Dòng 1049] if (this._res.payments[this._res.payments.length - 1].authorization != null
  41. [Dòng 1049] this._res.payments[this._res.payments.length - 1].authorization.links != null
  42. [Dòng 1055] auth = paramUserName != null ? paramUserName : ''
  43. [Dòng 1132] this._res.links.merchant_return != null //
  44. [Dòng 1153] } else if (this._res.merchant != null
  45. [Dòng 1153] this._res.merchant_invoice_reference != null
  46. [Dòng 1267] if (['mastercard', 'visa', 'amex', 'jcb', 'cup', 'card', 'np_card', 'atm'].indexOf(id) != -1) {
  47. [Dòng 1269] } else if (['techcombank_account', 'vib_account', 'dongabank_account', 'tpbank_account', 'bidv_account', 'pvcombank_account', 'shb_account', 'oceanbank_online_account'].indexOf(id) != -1) {
  48. [Dòng 1271] } else if (['oceanbank_mobile_account'].indexOf(id) != -1) {
  49. [Dòng 1273] } else if (['shb_customer_id'].indexOf(id) != -1) {
  50. [Dòng 1290] if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1) {
  51. [Dòng 1318] return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
  52. [Dòng 1355] if (!(strInstrument != null
  53. [Dòng 1372] if (this._translate.currentLang != language) {
  54. [Dòng 1399] e.type != 'ewallet') || (regex.test(strTest)
  55. [Dòng 1449] } else if (this._res.payments != null) {
  56. [Dòng 1578] if (this._res_post.return_url != null) {
  57. [Dòng 1580] } else if (this._res_post.links != null
  58. [Dòng 1580] this._res_post.links.merchant_return != null
  59. [Dòng 1580] this._res_post.links.merchant_return.href != null) {

================================================================================

📁 FILE 56: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 57: qr-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 2] screen=='qr'"
  2. [Dòng 112] screen=='confirm_close'"

================================================================================

📁 FILE 58: qr-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 59: qr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main/qr-main.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 6] filteredData.length === 0
  2. [Dòng 6] filteredDataOther.length === 0"
  3. [Dòng 40] filteredDataMobile.length === 0
  4. [Dòng 40] filteredDataOtherMobile.length === 0"

================================================================================

📁 FILE 60: qr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main/qr-main.component.ts
📊 Thống kê: 25 điều kiện duy nhất
   - === : 11 lần
   - == : 8 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 226] this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
  2. [Dòng 227] this.filteredDataOther = this.appList.filter(item => item.type === 'other');
  3. [Dòng 228] this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
  4. [Dòng 229] this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
  5. [Dòng 234] this.appList.length === 1) {
  6. [Dòng 236] if ((this.filteredDataMobile.length === 1
  7. [Dòng 236] this.filteredDataOtherMobile.length === 1)
  8. [Dòng 261] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  9. [Dòng 262] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  10. [Dòng 268] if (item.type === 'mobile_banking') {
  11. [Dòng 607] this.appList.length === 1

== (8 điều kiện):
  1. [Dòng 153] this.themeConfig.deeplink_status == 'Off' ? false : true
  2. [Dòng 267] if (item.available == true) {
  3. [Dòng 331] if (_re.status == '200'
  4. [Dòng 331] _re.status == '201') {
  5. [Dòng 333] if (appcode == 'grabpay'
  6. [Dòng 333] appcode == 'momo') {
  7. [Dòng 336] if (type == 2
  8. [Dòng 372] err.error.code == '04') {

!= (6 điều kiện):
  1. [Dòng 171] if (params['locale'] != null) {
  2. [Dòng 177] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 203] if (!(strInstrument != null
  4. [Dòng 297] if (appcode != null) {
  5. [Dòng 582] if (_re.status != '200'
  6. [Dòng 582] _re.status != '201')

================================================================================

📁 FILE 61: safe-html.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main/safe-html.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 62: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 63: qr-desktop.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 1 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 168] listVNPayQR.length === 0"

================================================================================

📁 FILE 64: qr-desktop.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.ts
📊 Thống kê: 19 điều kiện duy nhất
   - === : 4 lần
   - == : 10 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 362] this.listWalletQR.length === 1) {
  2. [Dòng 412] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  3. [Dòng 413] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  4. [Dòng 743] this.listWalletQR.length === 1

== (10 điều kiện):
  1. [Dòng 263] e.type == 'vnpayqr') {
  2. [Dòng 270] e.type == 'ewallet') {
  3. [Dòng 311] if (_re.status == '200'
  4. [Dòng 311] _re.status == '201') {
  5. [Dòng 340] e.type == 'wallet')) {
  6. [Dòng 379] if (d.b.code == s) {
  7. [Dòng 418] if (item.available == true) {
  8. [Dòng 477] if (appcode == 'grabpay'
  9. [Dòng 477] appcode == 'momo') {
  10. [Dòng 510] err.error.code == '04') {

!= (5 điều kiện):
  1. [Dòng 225] if (params['locale'] != null) {
  2. [Dòng 253] if (!(strInstrument != null
  3. [Dòng 436] if (appcode != null) {
  4. [Dòng 716] if (_re.status != '200'
  5. [Dòng 716] _re.status != '201')

================================================================================

📁 FILE 65: qr-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1] screen=='qr'"
  2. [Dòng 122] screen=='confirm_close'"

================================================================================

📁 FILE 66: qr-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 49] 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {

================================================================================

📁 FILE 67: qr-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (3 điều kiện):
  1. [Dòng 5] type == 'vnpay'"
  2. [Dòng 6] type == 'bankapp'"
  3. [Dòng 7] type == 'both'"

================================================================================

📁 FILE 68: qr-guide-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 69: list-bank-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 70: list-bank-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 71: qr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 72: qr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-main.component.ts
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 190] this.themeConfig.deeplink_status == 'Off' ? false : true

!= (2 điều kiện):
  1. [Dòng 206] if (params['locale'] != null) {
  2. [Dòng 212] if ('otp' != this._paymentService.getCurrentPage()) {

================================================================================

📁 FILE 73: qr-mobile.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 132] _locale=='vi'"
  2. [Dòng 133] _locale=='en'"
  3. [Dòng 143] _locale == 'vi'"
  4. [Dòng 145] _locale == 'en'"

!= (2 điều kiện):
  1. [Dòng 231] qr_version2 != 'None'"
  2. [Dòng 257] qr_version2 != 'None'

================================================================================

📁 FILE 74: qr-mobile.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.ts
📊 Thống kê: 30 điều kiện duy nhất
   - === : 6 lần
   - == : 19 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (6 điều kiện):
  1. [Dòng 471] if (!this.d_vnpayqr_wallet && !this.d_vnpayqr_deeplink && (this.listWalletQR?.length === 1
  2. [Dòng 471] this.listWalletDeeplink?.length === 1)) {
  3. [Dòng 577] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  4. [Dòng 578] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  5. [Dòng 584] if (item.type === 'deeplink') {
  6. [Dòng 966] this.listWalletQR?.length === 1

== (19 điều kiện):
  1. [Dòng 278] e.type == 'deeplink') {
  2. [Dòng 289] e.type == 'ewallet'
  3. [Dòng 309] e.type == 'vnpayqr') {
  4. [Dòng 323] e.type == 'wallet')) {
  5. [Dòng 352] e.type == 'ewallet') {
  6. [Dòng 382] if (e.type == 'ewallet') {
  7. [Dòng 405] this.listWallet.length == 1
  8. [Dòng 405] this.listWallet[0].code == 'momo') {
  9. [Dòng 407] this.checkEWalletDeeplink.length == 0) {
  10. [Dòng 488] arrayWallet.length == 0) return false;
  11. [Dòng 490] if (arrayWallet[i].code == key) {
  12. [Dòng 512] if (_re.status == '200'
  13. [Dòng 512] _re.status == '201') {
  14. [Dòng 534] if (d.b.code == s) {
  15. [Dòng 583] if (item.available == true) {
  16. [Dòng 659] if (appcode == 'grabpay'
  17. [Dòng 659] appcode == 'momo') {
  18. [Dòng 662] if (type == 2
  19. [Dòng 702] err.error.code == '04') {

!= (5 điều kiện):
  1. [Dòng 232] if (params['locale'] != null) {
  2. [Dòng 259] if (!(strInstrument != null
  3. [Dòng 611] if (appcode != null) {
  4. [Dòng 936] if (_re.status != '200'
  5. [Dòng 936] _re.status != '201')

================================================================================

📁 FILE 75: safe-html.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/qr-main-version2/safe-html.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 76: queuing.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/queuing/queuing.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 77: queuing.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/queuing/queuing.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 62] if (this.locale == 'vi') {

!= (1 điều kiện):
  1. [Dòng 86] if (this.translate.currentLang != language) {

================================================================================

📁 FILE 78: domescard-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.html
📊 Thống kê: 8 điều kiện duy nhất
   - === : 4 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 1] type === 2"
  2. [Dòng 2] [ngStyle]="{'border-color': type === 2 ? this.themeConfig.border_color : border_color}"
  3. [Dòng 22] *ngIf="((type === 2
  4. [Dòng 22] type === '2'

== (2 điều kiện):
  1. [Dòng 5] uniqueTokenBank) || (type == 2
  2. [Dòng 22] type == 2)) || token">

!= (2 điều kiện):
  1. [Dòng 5] (((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token
  2. [Dòng 18] feeService['atm']['fee'] != 0"

================================================================================

📁 FILE 79: domescard-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 80: intercard-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 1] type === 1"
  2. [Dòng 2] [ngStyle]="{'border-color': type === 1 ? this.themeConfig.border_color : border_color}"

!= (1 điều kiện):
  1. [Dòng 26] feeService['visa_mastercard_d']['fee'] != 0"

================================================================================

📁 FILE 81: intercard-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 82: deeplink.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/mpayvn-qr/deeplink/deeplink.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 83: deeplink.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/mpayvn-qr/deeplink/deeplink.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 84: mpayvn-qr.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/mpayvn-qr/mpayvn-qr.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 2] [ngStyle]="{'border-color': type === 5 ? this.themeConfig.border_color : border_color}"
  2. [Dòng 12] type === 5"

!= (1 điều kiện):
  1. [Dòng 13] feeService['qr']['fee'] != 0"

================================================================================

📁 FILE 85: mpayvn-qr.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/mpayvn-qr/mpayvn-qr.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 58] this.themeConfig.deeplink_status == 'Off' ? false : true
  2. [Dòng 65] if (this.type == 5) {
  3. [Dòng 94] if (_re.status == '200'
  4. [Dòng 94] _re.status == '201') {

!= (2 điều kiện):
  1. [Dòng 61] if (params['locale'] != null) {
  2. [Dòng 79] if (appcode != null) {

================================================================================

📁 FILE 86: qr.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/mpayvn-qr/qr/qr.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 87: qr.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/mpayvn-qr/qr/qr.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 88: paypal-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 1] type === 3"
  2. [Dòng 2] [ngStyle]="{'border-color': type === 3 ? this.themeConfig.border_color : border_color}"

!= (1 điều kiện):
  1. [Dòng 15] feeService['pp']['fee'] != 0"

================================================================================

📁 FILE 89: paypal-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 90: qr-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/qr-form/qr-form.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 1] type === 4"
  2. [Dòng 2] [ngStyle]="{'border-color': type === 4 ? this.themeConfig.border_color : border_color}"
  3. [Dòng 19] type === 4

!= (1 điều kiện):
  1. [Dòng 17] feeService['qr']['fee'] != 0"

================================================================================

📁 FILE 91: qr-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/sorting-option/qr-form/qr-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 92: remove-token-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/token-main/remove-token-dialog/remove-token-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 93: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/token-main/token-dialog/dialog-guide-dialog.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (6 điều kiện):
  1. [Dòng 9] data['type'] == 'Visa'
  2. [Dòng 9] data['type'] == 'Master'
  3. [Dòng 9] data['type'] == 'JCB'"
  4. [Dòng 17] data['type'] == 'Visa'"
  5. [Dòng 17] data['type'] == 'Master'"
  6. [Dòng 24] data['type'] == 'Amex'"

================================================================================

📁 FILE 94: token-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/token-main/token-main.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 1 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 8] style="{{item.brand_id === 'visa' ? 'height: 14.42px

== (1 điều kiện):
  1. [Dòng 28] token_main == '1'"

!= (1 điều kiện):
  1. [Dòng 21] item['feeService']['fee'] != 0"

================================================================================

📁 FILE 95: token-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/main/menu/token-main/token-main.component.ts
📊 Thống kê: 65 điều kiện duy nhất
   - === : 6 lần
   - == : 39 lần
   - !== : 1 lần
   - != : 19 lần
--------------------------------------------------------------------------------

=== (6 điều kiện):
  1. [Dòng 78] if (event.keyCode === 13) {
  2. [Dòng 179] && ((item.brand_id === 'amex'
  3. [Dòng 195] return id === 'amex' ? '1234' : '123'
  4. [Dòng 337] if (approval.method === 'REDIRECT') {
  5. [Dòng 340] } else if (approval.method === 'POST_REDIRECT') {
  6. [Dòng 411] return this._i_token_otp != null && !isNaN(+this._i_token_otp) && ((id === 'amex'

== (39 điều kiện):
  1. [Dòng 102] if (message == '0') {
  2. [Dòng 136] item.id == element.id ? element['active'] = true : element['active'] = false
  3. [Dòng 140] item.display_cvv == true ? this.flagToken = true : this.flagToken = false
  4. [Dòng 161] if (result == 'success') {
  5. [Dòng 166] if (this.tokenList.length == 0) {
  6. [Dòng 170] } else if (result == 'error') {
  7. [Dòng 179] _val.value.trim().length == 4) || (item.brand_id != 'amex'
  8. [Dòng 179] _val.value.trim().length == 3))
  9. [Dòng 188] _val.value.length == 4) || (item.brand_id != 'amex'
  10. [Dòng 188] _val.value.length == 3)));
  11. [Dòng 194] id == 'amex' ? this.maxLength = 4 : this.maxLength = 3
  12. [Dòng 216] if (_re.body.state == 'more_info_required') {
  13. [Dòng 231] if (this._res_post.state == 'approved'
  14. [Dòng 231] this._res_post.state == 'failed') {
  15. [Dòng 238] if (this._res_post.state == 'failed') {
  16. [Dòng 254] } else if (this._res_post.state == 'authorization_required') {
  17. [Dòng 255] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  18. [Dòng 267] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  19. [Dòng 283] } else if (_re.body.state == 'authorization_required') {
  20. [Dòng 300] if (this._b == 1
  21. [Dòng 300] this._b == 14
  22. [Dòng 300] this._b == 15
  23. [Dòng 300] this._b == 24
  24. [Dòng 300] this._b == 8
  25. [Dòng 300] this._b == 10
  26. [Dòng 300] this._b == 20
  27. [Dòng 300] this._b == 22
  28. [Dòng 300] this._b == 23
  29. [Dòng 300] this._b == 30
  30. [Dòng 300] this._b == 11
  31. [Dòng 300] this._b == 17
  32. [Dòng 300] this._b == 18
  33. [Dòng 300] this._b == 27) {
  34. [Dòng 359] } else if (_re.body.state == 'failed') {
  35. [Dòng 403] if (action == 'blur') {
  36. [Dòng 411] this._i_token_otp.trim().length == 4)
  37. [Dòng 412] this._i_token_otp.trim().length == 3));
  38. [Dòng 465] if (_re.status == '200'
  39. [Dòng 465] _re.status == '201') {

!== (1 điều kiện):
  1. [Dòng 126] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (19 điều kiện):
  1. [Dòng 94] if (params['locale'] != null) {
  2. [Dòng 178] if (_val.value != null
  3. [Dòng 187] this.c_token_otp_csc = !(_val.value != null
  4. [Dòng 233] if (this._res_post.return_url != null) {
  5. [Dòng 235] } else if (this._res_post.links != null
  6. [Dòng 235] this._res_post.links.merchant_return != null
  7. [Dòng 235] this._res_post.links.merchant_return.href != null) {
  8. [Dòng 288] if (_re.body.authorization != null
  9. [Dòng 288] _re.body.authorization.links != null
  10. [Dòng 295] if (_re.body.links != null
  11. [Dòng 295] _re.body.links.cancel != null) {
  12. [Dòng 361] if (_re.body.return_url != null) {
  13. [Dòng 363] } else if (_re.body.links != null
  14. [Dòng 363] _re.body.links.merchant_return != null
  15. [Dòng 363] _re.body.links.merchant_return.href != null) {
  16. [Dòng 394] return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
  17. [Dòng 399] if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(item.brand_id) != -1
  18. [Dòng 411] return this._i_token_otp != null
  19. [Dòng 412] || (id != 'amex'

================================================================================

📁 FILE 96: overlay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/overlay/overlay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 97: overlay.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/overlay/overlay.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 98: overlay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/overlay/overlay.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 99: bank-amount.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/pipe/bank-amount.pipe.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 11] return ((a.id == id
  2. [Dòng 11] a.code == id) && a.type.includes(type));

================================================================================

📁 FILE 100: account-main.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/services/account-main.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 101: close-dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/services/close-dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 102: data.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/services/data.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 89] const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
  2. [Dòng 89] item.method === method) : null;

================================================================================

📁 FILE 103: deep_link.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/services/deep_link.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 9] if (isIphone == true) {
  2. [Dòng 22] } else if (isAndroid == true) {

================================================================================

📁 FILE 104: dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/services/dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 105: fee.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/services/fee.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 106: multiple_method.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/services/multiple_method.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 107: payment.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/services/payment.service.ts
📊 Thống kê: 13 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 11 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 598] return countPayment == maxPayment
  2. [Dòng 636] if (this.getLatestPayment().state == 'canceled')

!= (11 điều kiện):
  1. [Dòng 116] if (idInvoice != null
  2. [Dòng 116] idInvoice != 0)
  3. [Dòng 126] idInvoice != 0) {
  4. [Dòng 303] if (this._merchantid != null
  5. [Dòng 303] this._tranref != null
  6. [Dòng 303] this._state != null
  7. [Dòng 370] let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
  8. [Dòng 406] urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
  9. [Dòng 428] if (paymentId != null) {
  10. [Dòng 518] if (res?.status != 200
  11. [Dòng 518] res?.status != 201) return;

================================================================================

📁 FILE 108: qr.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/services/qr.service.ts
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 58] if (res?.state == 'canceled') {
  2. [Dòng 99] state == 'authorization_required'

!= (3 điều kiện):
  1. [Dòng 40] if (_re.status != '200'
  2. [Dòng 40] _re.status != '201') {
  3. [Dòng 49] latestPayment?.state != "authorization_required") {

================================================================================

📁 FILE 109: time-stop.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/services/time-stop.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 110: token-main.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/services/token-main.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 111: index.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/translate/index.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 112: lang-en.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/translate/lang-en.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 113: lang-vi.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/translate/lang-vi.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 114: translate.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/translate/translate.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 115: translate.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/translate/translate.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 37] if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
  2. [Dòng 38] else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';

================================================================================

📁 FILE 116: translations.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/translate/translations.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 117: apps-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/util/apps-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 549] if (e.name == bankSwift) { // TODO: get by swift
  2. [Dòng 557] return this.apps.find(e => e.code == appCode);

================================================================================

📁 FILE 118: apps-information.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/util/apps-information.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 119: banks-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/util/banks-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1013] if (+e.id == bankId) {
  2. [Dòng 1063] if (e.swiftCode == bankSwift) {

================================================================================

📁 FILE 120: error-handler.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/util/error-handler.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 9] err?.status === 400
  2. [Dòng 10] err?.error?.name === 'INVALID_CARD_FEE'

================================================================================

📁 FILE 121: util.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/app/util/util.ts
📊 Thống kê: 25 điều kiện duy nhất
   - === : 11 lần
   - == : 9 lần
   - !== : 3 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 54] if (v.length === 2
  2. [Dòng 54] this.flag.length === 3
  3. [Dòng 54] this.flag.charAt(this.flag.length - 1) === '/') {
  4. [Dòng 58] if (v.length === 1) {
  5. [Dòng 60] } else if (v.length === 2) {
  6. [Dòng 63] v.length === 2) {
  7. [Dòng 71] if (len === 2) {
  8. [Dòng 143] if (M[1] === 'Chrome') {
  9. [Dòng 268] if (param === key) {
  10. [Dòng 431] pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
  11. [Dòng 435] target === 0

== (9 điều kiện):
  1. [Dòng 13] if (temp.length == 0) {
  2. [Dòng 20] return (counter % 10 == 0);
  3. [Dòng 24] if (currency == 'USD') {
  4. [Dòng 117] if (this.checkCount == 1) {
  5. [Dòng 129] if (results == null) {
  6. [Dòng 162] if (c.length == 3) {
  7. [Dòng 175] d = d == undefined ? '.' : d
  8. [Dòng 176] t = t == undefined ? '
  9. [Dòng 256] return results == null ? null : results[1]

!== (3 điều kiện):
  1. [Dòng 263] queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
  2. [Dòng 264] if (queryString !== '') {
  3. [Dòng 435] if (target !== 0

!= (2 điều kiện):
  1. [Dòng 145] if (tem != null) {
  2. [Dòng 150] if ((tem = ua.match(/version\/(\d+)/i)) != null) {

================================================================================

📁 FILE 122: qrcode.js
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/assets/script/qrcode.js
📊 Thống kê: 67 điều kiện duy nhất
   - === : 2 lần
   - == : 53 lần
   - !== : 0 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 2255] if (typeof define === 'function'
  2. [Dòng 2257] } else if (typeof exports === 'object') {

== (53 điều kiện):
  1. [Dòng 68] if (_dataCache == null) {
  2. [Dòng 85] if ( (0 <= r && r <= 6 && (c == 0
  3. [Dòng 85] c == 6) )
  4. [Dòng 86] || (0 <= c && c <= 6 && (r == 0
  5. [Dòng 86] r == 6) )
  6. [Dòng 107] if (i == 0
  7. [Dòng 122] _modules[r][6] = (r % 2 == 0);
  8. [Dòng 129] _modules[6][c] = (c % 2 == 0);
  9. [Dòng 152] if (r == -2
  10. [Dòng 152] r == 2
  11. [Dòng 152] c == -2
  12. [Dòng 152] c == 2
  13. [Dòng 153] || (r == 0
  14. [Dòng 153] c == 0) ) {
  15. [Dòng 169] ( (bits >> i) & 1) == 1);
  16. [Dòng 226] if (col == 6) col -= 1;
  17. [Dòng 232] if (_modules[row][col - c] == null) {
  18. [Dòng 237] dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
  19. [Dòng 249] if (bitIndex == -1) {
  20. [Dòng 458] margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
  21. [Dòng 498] if (typeof arguments[0] == 'object') {
  22. [Dòng 587] margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
  23. [Dòng 733] if (b == -1) throw 'eof';
  24. [Dòng 741] if (b0 == -1) break;
  25. [Dòng 767] if (typeof b == 'number') {
  26. [Dòng 768] if ( (b & 0xff) == b) {
  27. [Dòng 910] return function(i, j) { return (i + j) % 2 == 0
  28. [Dòng 912] return function(i, j) { return i % 2 == 0
  29. [Dòng 914] return function(i, j) { return j % 3 == 0
  30. [Dòng 916] return function(i, j) { return (i + j) % 3 == 0
  31. [Dòng 918] return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
  32. [Dòng 920] return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
  33. [Dòng 922] return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
  34. [Dòng 924] return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
  35. [Dòng 1011] if (r == 0
  36. [Dòng 1011] c == 0) {
  37. [Dòng 1015] if (dark == qrcode.isDark(row + r, col + c) ) {
  38. [Dòng 1036] if (count == 0
  39. [Dòng 1036] count == 4) {
  40. [Dòng 1149] if (typeof num.length == 'undefined') {
  41. [Dòng 1155] num[offset] == 0) {
  42. [Dòng 1495] if (typeof rsBlock == 'undefined') {
  43. [Dòng 1538] return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
  44. [Dòng 1543] _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
  45. [Dòng 1599] if (data.length - i == 1) {
  46. [Dòng 1601] } else if (data.length - i == 2) {
  47. [Dòng 1866] } else if (n == 62) {
  48. [Dòng 1868] } else if (n == 63) {
  49. [Dòng 1928] if (_buflen == 0) {
  50. [Dòng 1937] if (c == '=') {
  51. [Dòng 1961] } else if (c == 0x2b) {
  52. [Dòng 1963] } else if (c == 0x2f) {
  53. [Dòng 2133] if (table.size() == (1 << bitLength) ) {

!= (12 điều kiện):
  1. [Dòng 119] if (_modules[r][6] != null) {
  2. [Dòng 126] if (_modules[6][c] != null) {
  3. [Dòng 144] if (_modules[row][col] != null) {
  4. [Dòng 365] while (buffer.getLengthInBits() % 8 != 0) {
  5. [Dòng 750] if (count != numChars) {
  6. [Dòng 751] throw count + ' != ' + numChars
  7. [Dòng 878] while (data != 0) {
  8. [Dòng 1733] if (test.length != 2
  9. [Dòng 1733] ( (test[0] << 8) | test[1]) != code) {
  10. [Dòng 1894] if (_length % 3 != 0) {
  11. [Dòng 2067] if ( (data >>> length) != 0) {
  12. [Dòng 2178] return typeof _map[key] != 'undefined'

================================================================================

📁 FILE 123: environment.prod.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/environments/environment.prod.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 124: environment.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/environments/environment.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 125: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 126: karma.conf.js
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/karma.conf.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 127: main.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/main.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 128: polyfills.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/polyfills.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 129: test.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mpayvn/src/test.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📋 TÓM TẮT TẤT CẢ ĐIỀU KIỆN DUY NHẤT:
====================================================================================================

=== (154 điều kiện duy nhất):
------------------------------------------------------------
1. return lang === this._translate.currentLang
2. isPopupSupport === 'True'"
3. params.timeout === 'true') {
4. this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
5. _re.body.state === 'unpaid');
6. if (this.errorCode === 'overtime'
7. this.errorCode === '253') {
8. if (this.timeLeft === 0) {
9. if (YY % 400 === 0
10. YY % 4 === 0)) {
11. if (YYYY % 400 === 0
12. YYYY % 4 === 0)) {
13. <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
14. [class.red_border_no_background]="c_expdate && (valueDate.length === 0
15. valueDate.trim().length === 0)"
16. if (target.tagName === 'A'
17. if (isIE[0] === 'MSIE'
18. +isIE[1] === 10) {
19. if ((_val.value.substr(-1) === ' '
20. _val.value.length === 24) {
21. if (this.cardTypeBank === 'bank_card_number') {
22. } else if (this.cardTypeBank === 'bank_account_number') {
23. } else if (this.cardTypeBank === 'bank_username') {
24. } else if (this.cardTypeBank === 'bank_customer_code') {
25. this.cardTypeBank === 'bank_card_number'
26. if (this.cardTypeOcean === 'IB') {
27. } else if (this.cardTypeOcean === 'MB') {
28. if (_val.value.substr(0, 2) === '84') {
29. } else if (this.cardTypeOcean === 'ATM') {
30. if (this.cardTypeBank === 'bank_account_number') {
31. this.cardTypeBank === 'bank_card_number') {
32. if (this.cardTypeBank === 'bank_card_number'
33. if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
34. if (event.keyCode === 8
35. event.key === "Backspace"
36. if (v.length === 2
37. this.flag.length === 3
38. this.flag.charAt(this.flag.length - 1) === '/') {
39. if (v.length === 1) {
40. } else if (v.length === 2) {
41. v.length === 2) {
42. if (len === 2) {
43. if ((this.cardTypeBank === 'bank_account_number'
44. this.cardTypeBank === 'bank_username'
45. this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
46. this.cardTypeOcean === 'ATM')
47. || (this.cardTypeOcean === 'IB'
48. if (valIn === this._translate.instant('bank_card_number')) {
49. } else if (valIn === this._translate.instant('bank_account_number')) {
50. } else if (valIn === this._translate.instant('bank_username')) {
51. } else if (valIn === this._translate.instant('bank_customer_code')) {
52. if (_val.value === ''
53. _val.value === null
54. _val.value === undefined) {
55. if (_val.value && (this.cardTypeBank === 'bank_card_number'
56. if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
57. this.cardTypeOcean === 'MB') {
58. this.cardTypeOcean === 'IB'
59. if ((this.cardTypeBank === 'bank_card_number'
60. if (this.cardName === undefined
61. this.cardName === '') {
62. if (this.valueDate === undefined
63. this.valueDate === '') {
64. c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
65. _inExpDate.trim().length === 0)"
66. if (approval.method === 'REDIRECT') {
67. } else if (approval.method === 'POST_REDIRECT') {
68. filteredData.length === 0"
69. if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
70. filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
71. if (valOut === 'auth') {
72. if (this._b === '1'
73. this._b === '20'
74. this._b === '64') {
75. if (this._b === '36'
76. this._b === '18'
77. this._b === '19'
78. if (this._b === '19'
79. this._b === '16'
80. this._b === '25'
81. this._b === '33'
82. this._b === '39'
83. this._b === '9'
84. this._b === '11'
85. this._b === '17'
86. this._b === '36'
87. this._b === '44'
88. this._b === '12'
89. this._b === '64'
90. if (this._b === '20'
91. if (this._b === '12'
92. this._b === '18') {
93. if (_formCard.country === 'default') {
94. if ((v.substr(-1) === ' '
95. if (deviceValue === 'CA'
96. deviceValue === 'US') {
97. this.c_country = _val.value === 'default'
98. d_vrbank===true"
99. this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number === 1
100. if (this._res.state === 'unpaid'
101. this._res.state === 'not_paid') {
102. if ('op' === auth
103. } else if ('bank' === auth
104. return id === 'amex' ? '1234' : '123'
105. if (this.timeLeftPaypal === 0) {
106. filteredData.length === 0
107. filteredDataOther.length === 0"
108. filteredDataMobile.length === 0
109. filteredDataOtherMobile.length === 0"
110. this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
111. this.filteredDataOther = this.appList.filter(item => item.type === 'other');
112. this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
113. this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
114. this.appList.length === 1) {
115. if ((this.filteredDataMobile.length === 1
116. this.filteredDataOtherMobile.length === 1)
117. item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
118. filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
119. if (item.type === 'mobile_banking') {
120. this.appList.length === 1
121. listVNPayQR.length === 0"
122. this.listWalletQR.length === 1) {
123. this.listWalletQR.length === 1
124. if (!this.d_vnpayqr_wallet && !this.d_vnpayqr_deeplink && (this.listWalletQR?.length === 1
125. this.listWalletDeeplink?.length === 1)) {
126. if (item.type === 'deeplink') {
127. this.listWalletQR?.length === 1
128. type === 2"
129. [ngStyle]="{'border-color': type === 2 ? this.themeConfig.border_color : border_color}"
130. *ngIf="((type === 2
131. type === '2'
132. type === 1"
133. [ngStyle]="{'border-color': type === 1 ? this.themeConfig.border_color : border_color}"
134. [ngStyle]="{'border-color': type === 5 ? this.themeConfig.border_color : border_color}"
135. type === 5"
136. type === 3"
137. [ngStyle]="{'border-color': type === 3 ? this.themeConfig.border_color : border_color}"
138. type === 4"
139. [ngStyle]="{'border-color': type === 4 ? this.themeConfig.border_color : border_color}"
140. type === 4
141. style="{{item.brand_id === 'visa' ? 'height: 14.42px
142. if (event.keyCode === 13) {
143. && ((item.brand_id === 'amex'
144. return this._i_token_otp != null && !isNaN(+this._i_token_otp) && ((id === 'amex'
145. const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
146. item.method === method) : null;
147. err?.status === 400
148. err?.error?.name === 'INVALID_CARD_FEE'
149. if (M[1] === 'Chrome') {
150. if (param === key) {
151. pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
152. target === 0
153. if (typeof define === 'function'
154. } else if (typeof exports === 'object') {

== (511 điều kiện duy nhất):
------------------------------------------------------------
1. 'vi' == params['locale']) {
2. 'en' == params['locale']) {
3. errorCode == '11'"
4. isSent == false
5. <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
6. (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
7. <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
8. !(errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
9. _re.body.themes.theme == 'token') {
10. params.response_code == 'overtime') {
11. if (_re.status == '200'
12. _re.status == '201') {
13. if (_re2.status == '200'
14. _re2.status == '201') {
15. if (this.errorCode == 'overtime'
16. this.errorCode == '253') {
17. } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
18. if (lastPayment?.state == 'pending') {
19. if (this.isTimePause == false) {
20. if ((dataPassed.status == '200'
21. dataPassed.status == '201') && dataPassed.body != null) {
22. account_amain == '1'"
23. if (this.locale == 'en') {
24. if (name == 'MAFC')
25. if (bankId == 3
26. bankId == 61
27. bankId == 8
28. bankId == 49
29. bankId == 48
30. bankId == 10
31. bankId == 53
32. bankId == 17
33. bankId == 65
34. bankId == 23
35. bankId == 52
36. bankId == 27
37. bankId == 66
38. bankId == 9
39. bankId == 54
40. bankId == 37
41. bankId == 38
42. bankId == 39
43. bankId == 40
44. bankId == 42
45. bankId == 44
46. bankId == 72
47. bankId == 59
48. bankId == 51
49. bankId == 64
50. bankId == 58
51. bankId == 56
52. bankId == 55
53. bankId == 60
54. bankId == 68
55. bankId == 74
56. bankId == 75 //KEB HANA
57. if (this._b == 18
58. this._b == 19) {
59. if (this._b == 19) {//19BIDV
60. } else if (this._b == 3
61. this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
62. if (this._b == 27) {
63. } else if (this._b == 12) {// 12SHB
64. } else if (this._b == 18) { //18Oceanbank-ocb
65. if (this._b == 19
66. this._b == 3
67. this._b == 27
68. this._b == 12) {
69. } else if (this._b == 18) {
70. if (this.checkBin(_val.value) && (this._b == 3
71. this._b == 27)) {
72. if (this._b == 3) {
73. this.cardTypeOcean == 'ATM') {
74. if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
75. this._b == 18)) {
76. if (this.checkBin(v) && (this._b == 3
77. event.inputType == 'deleteContentBackward') {
78. if (event.target.name == 'exp_date'
79. event.inputType == 'insertCompositionText') {
80. if (((this.valueDate.length == 4
81. this.valueDate.search('/') == -1) || this.valueDate.length == 5)
82. this.valueDate.length == 5)
83. if (temp.length == 0) {
84. return (counter % 10 == 0);
85. } else if (this._b == 19) {
86. } else if (this._b == 27) {
87. if (this._b == 12) {
88. if (this.cardTypeBank == 'bank_customer_code') {
89. } else if (this.cardTypeBank == 'bank_account_number') {
90. _formCard.exp_date.length == 5
91. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
92. this._b == 3)) {//27-pvcombank;3-TPB ;
93. if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
94. this._b == 19
95. this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
96. if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
97. if (this.cardTypeOcean == 'IB') {
98. } else if (this.cardTypeOcean == 'MB') {
99. } else if (this.cardTypeOcean == 'ATM') {
100. if (this._res_post.state == 'approved'
101. this._res_post.state == 'failed') {
102. } else if (this._res_post.state == 'authorization_required') {
103. if (this._b == 18) {
104. if (this._b == 27
105. this._b == 18) {
106. if (err.status == 400
107. err.status == 500) {
108. if (err.error && (err.error.code == 13
109. err.error.code == '13')) {
110. if ((cardNo.length == 16
111. if ((cardNo.length == 16 || (cardNo.length == 19
112. && ((this._b == 18
113. cardNo.length == 19) || this._b != 18)
114. if (this._b == +e.id) {
115. if (valIn == 1) {
116. } else if (valIn == 2) {
117. this._b == 3) {
118. if (this._b == 19) {
119. if (cardType == this._translate.instant('internetbanking')
120. } else if (cardType == this._translate.instant('mobilebanking')
121. } else if (cardType == this._translate.instant('atm')
122. this._b == 18))) {
123. } else if (this._b == 18
124. this.c_expdate = !(((this.valueDate.length == 4
125. this.valueDate.length == 4
126. this.valueDate.search('/') == -1)
127. this.valueDate.length == 5))
128. if (this._b == 8) {//MB Bank
129. if (this._b == 18) {//Oceanbank
130. if (this._b == 8) {
131. } else if (this._res.state == 'authorization_required') {
132. if (this._b == 18) {//8-MB Bank;18-oceanbank
133. if (this._b == 2
134. this._b == 31) {
135. if (this._b == 2) {
136. } else if (this._b == 6) {
137. } else if (this._b == 31) {
138. if (this._b == 5) {//5-vib;
139. if (this._b == 5) {
140. if (this.checkBin(_val.value) && (this._b == 5)) {
141. if (this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
142. if (this.checkBin(v) && (this._b == 5)) {
143. this._b == 5) {//5 vib ;
144. this._b == 5) {//5vib;
145. _b==68"
146. if (this._b == 1
147. this._b == 20
148. this._b == 36
149. this._b == 64
150. this._b == 55
151. this._b == 47
152. this._b == 48
153. this._b == 59) {
154. return this._b == 11
155. this._b == 33
156. this._b == 39
157. this._b == 43
158. this._b == 45
159. this._b == 67
160. this._b == 68
161. this._b == 72
162. this._b == 73
163. this._b == 74
164. this._b == 75
165. return this._b == 9
166. this._b == 16
167. this._b == 17
168. this._b == 25
169. this._b == 44
170. this._b == 54
171. this._b == 57
172. this._b == 59
173. this._b == 61
174. this._b == 63
175. this._b == 69
176. this._b == 14
177. this._b == 15
178. this._b == 24
179. this._b == 8
180. this._b == 10
181. this._b == 22
182. this._b == 23
183. this._b == 30
184. this._b == 11
185. this._b == 9) {
186. (cardNo.length == 19
187. (cardNo.length == 19 && (this._b == 1
188. this._b == 4
189. this._b == 59))
190. this._util.checkMod10(cardNo) == true
191. return ((value.length == 4
192. value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
193. value.length == 5) && parseInt(value.split('/')[0]
194. this._inExpDate.length == 4
195. this._inExpDate.search('/') == -1)
196. this._inExpDate.length == 5))
197. ((!token && _auth==0 && vietcombankGroupSelected) || (token && _b == 16))
198. _b == 16))">
199. !token&&_auth==0 && techcombankGroupSelected
200. !token&&_auth==0 && bankaccountGroupSelected
201. !token && _auth==0 && vibbankGroupSelected
202. (token || _auth==1) && _b != 16
203. this._auth == 0
204. this.tokenList.length == 0) {
205. this.filteredData.length == 1
206. $event == 'true') {
207. if (bankId == 1
208. bankId == 4
209. bankId == 7
210. bankId == 11
211. bankId == 14
212. bankId == 15
213. bankId == 16
214. bankId == 20
215. bankId == 22
216. bankId == 24
217. bankId == 25
218. bankId == 30
219. bankId == 33
220. bankId == 34
221. bankId == 35
222. bankId == 36
223. bankId == 41
224. bankId == 43
225. bankId == 45
226. bankId == 46
227. bankId == 47
228. bankId == 50
229. bankId == 57
230. bankId == 62
231. bankId == 63
232. bankId == 67
233. bankId == 69
234. bankId == 70
235. bankId == 71
236. bankId == 73
237. bankId == 32
238. bankId == 75) {
239. } else if (bankId == 6
240. bankId == 31
241. bankId == 80
242. bankId == 2) {
243. } else if (bankId == 3
244. bankId == 12
245. bankId == 18
246. bankId == 19
247. bankId == 27) {
248. } else if (bankId == 5) {
249. this._b == '55'
250. this._b == '47'
251. this._b == '48'
252. this._b == '59'
253. this._b == '73') {
254. this._b == '3'
255. this._b == '43'
256. this._b == '45'
257. this._b == '54'
258. this._b == '57'
259. this._b == '61'
260. this._b == '63'
261. this._b == '67'
262. this._b == '68'
263. this._b == '69'
264. this._b == '72'
265. this._b == '73'
266. this._b == '74'
267. this._b == '75') {
268. this._b == '36'
269. if (item['id'] == this._b) {
270. } if (this._res_post.state == 'failed') {
271. if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
272. } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
273. if (err.error && (err.error.code == 8
274. err.error.code == '8')) {
275. if (this._type == 5
276. this._type == 6) {
277. } else if (this._type == 7
278. this._type == 8) {
279. } else if (err.error && (err.error.code == 13
280. v.length == 15) || (v.length == 16
281. v.length == 19))
282. this._util.checkMod10(v) == true) {
283. cardNo.length == 15)
284. cardNo.length == 16)
285. cardNo.startsWith('81')) && (cardNo.length == 16
286. cardNo.length == 19))
287. this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
288. this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
289. v.length == 5) {
290. v.length == 4
291. v.length == 3)
292. _val.value.length == 4
293. _val.value.length == 3)
294. this._i_csc.length == 4) ||
295. this._i_csc.length == 3)
296. merchantId == 'OPTEST'
297. sortMethodArray[i].trim()=='International'"
298. sortMethodArray[i].trim()=='Domestic'"
299. sortMethodArray[i].trim()=='QR'"
300. sortMethodArray[i].trim()=='Paypal'"
301. if (el == 5) {
302. } else if (el == 6) {
303. } else if (el == 7) {
304. } else if (el == 8) {
305. } else if (el == 2) {
306. } else if (el == 4) {
307. } else if (el == 3) {
308. if (!isNaN(_re.status) && (_re.status == '200'
309. _re.status == '201') && _re.body != null) {
310. if (('closed' == this._res_polling.state
311. 'canceled' == this._res_polling.state
312. 'expired' == this._res_polling.state)
313. } else if ('paid' == this._res_polling.state) {
314. this._res_polling.payments == null
315. if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'authorization_required') {
316. } else if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
317. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
318. this._paymentService.getCurrentPage() == 'enter_card') {
319. } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
320. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
321. } else if ('not_paid' == this._res_polling.state) {
322. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
323. if (message == '1') {
324. if (this.checkInvoiceState() == 1) {
325. this.version2 = _re.body?.merchant?.qr_version == "2"
326. if (this.type == 5
327. } else if (this.type == 6
328. } else if (this.type == 2
329. } else if (this.type == 7
330. } else if (this.type == 8
331. } else if (this.type == 4
332. } else if (this.type == 3
333. if (this.themeConfig.default_method == 'International'
334. } else if (this.themeConfig.default_method == 'Domestic'
335. } else if (this.themeConfig.default_method == 'QR'
336. } else if (this.themeConfig.default_method == 'Paypal'
337. if ('REDIRECT' == approval.method) {
338. } else if ('POST_REDIRECT' == approval.method) {
339. if (('closed' == this._res.state
340. 'canceled' == this._res.state
341. 'expired' == this._res.state
342. 'paid' == this._res.state)
343. if ((this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number) == 0
344. this._auth == 0) {
345. if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
346. this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
347. if (this._res.payments[this._res.payments.length - 1].state == 'approved'
348. } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
349. if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required') {
350. } else if (this._res.payments[this._res.payments.length - 1].state == 'pending') {
351. } else if (this._res.payments[this._res.payments.length - 1].state == 'canceled') {
352. } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
353. this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
354. if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
355. } else if (idBrand == 'atm'
356. if ('paid' == this._res.state) {
357. this._res.merchant.token_site == 'onepay')) {
358. this._res.payments == null) {
359. this._res.payments[this._res.payments.length - 1].state == 'pending') {
360. if (this._res.currencies[0] == 'USD') {
361. if (item.instrument.issuer.brand.id == 'atm') {
362. } else if (item.instrument.issuer.brand.id == 'visa'
363. item.instrument.issuer.brand.id == 'mastercard') {
364. if (item.instrument.issuer_location == 'd') {
365. } else if (item.instrument.issuer.brand.id == 'amex') {
366. } else if (item.instrument.issuer.brand.id == 'jcb') {
367. uniq.length == 1) {
368. if (type == 'qrv1') {
369. if (type == 'mobile') {
370. e.type == 'ewallet'
371. e.code == 'momo')) {
372. } else if (type == 'desktop') {
373. e.type == 'vnpayqr') || (regex.test(strTest)
374. _val == 2) {
375. if (_val == 2
376. } else if (_val == 2
377. _val == 2
378. if (this.type == 4) {
379. filteredData.length == 1) {
380. if (data._locale == 'en') {
381. screen=='qr'"
382. screen=='confirm_close'"
383. this.themeConfig.deeplink_status == 'Off' ? false : true
384. if (item.available == true) {
385. if (appcode == 'grabpay'
386. appcode == 'momo') {
387. if (type == 2
388. err.error.code == '04') {
389. e.type == 'vnpayqr') {
390. e.type == 'ewallet') {
391. e.type == 'wallet')) {
392. if (d.b.code == s) {
393. type == 'vnpay'"
394. type == 'bankapp'"
395. type == 'both'"
396. _locale=='vi'"
397. _locale=='en'"
398. _locale == 'vi'"
399. _locale == 'en'"
400. e.type == 'deeplink') {
401. if (e.type == 'ewallet') {
402. this.listWallet.length == 1
403. this.listWallet[0].code == 'momo') {
404. this.checkEWalletDeeplink.length == 0) {
405. arrayWallet.length == 0) return false;
406. if (arrayWallet[i].code == key) {
407. if (this.locale == 'vi') {
408. uniqueTokenBank) || (type == 2
409. type == 2)) || token">
410. if (this.type == 5) {
411. data['type'] == 'Visa'
412. data['type'] == 'Master'
413. data['type'] == 'JCB'"
414. data['type'] == 'Visa'"
415. data['type'] == 'Master'"
416. data['type'] == 'Amex'"
417. token_main == '1'"
418. if (message == '0') {
419. item.id == element.id ? element['active'] = true : element['active'] = false
420. item.display_cvv == true ? this.flagToken = true : this.flagToken = false
421. if (result == 'success') {
422. if (this.tokenList.length == 0) {
423. } else if (result == 'error') {
424. _val.value.trim().length == 4) || (item.brand_id != 'amex'
425. _val.value.trim().length == 3))
426. _val.value.length == 4) || (item.brand_id != 'amex'
427. _val.value.length == 3)));
428. id == 'amex' ? this.maxLength = 4 : this.maxLength = 3
429. if (_re.body.state == 'more_info_required') {
430. if (this._res_post.state == 'failed') {
431. } else if (_re.body.state == 'authorization_required') {
432. this._b == 18
433. this._b == 27) {
434. } else if (_re.body.state == 'failed') {
435. if (action == 'blur') {
436. this._i_token_otp.trim().length == 4)
437. this._i_token_otp.trim().length == 3));
438. return ((a.id == id
439. a.code == id) && a.type.includes(type));
440. if (isIphone == true) {
441. } else if (isAndroid == true) {
442. return countPayment == maxPayment
443. if (this.getLatestPayment().state == 'canceled')
444. if (res?.state == 'canceled') {
445. state == 'authorization_required'
446. if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
447. else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';
448. if (e.name == bankSwift) { // TODO: get by swift
449. return this.apps.find(e => e.code == appCode);
450. if (+e.id == bankId) {
451. if (e.swiftCode == bankSwift) {
452. if (currency == 'USD') {
453. if (this.checkCount == 1) {
454. if (results == null) {
455. if (c.length == 3) {
456. d = d == undefined ? '.' : d
457. t = t == undefined ? '
458. return results == null ? null : results[1]
459. if (_dataCache == null) {
460. if ( (0 <= r && r <= 6 && (c == 0
461. c == 6) )
462. || (0 <= c && c <= 6 && (r == 0
463. r == 6) )
464. if (i == 0
465. _modules[r][6] = (r % 2 == 0);
466. _modules[6][c] = (c % 2 == 0);
467. if (r == -2
468. r == 2
469. c == -2
470. c == 2
471. || (r == 0
472. c == 0) ) {
473. ( (bits >> i) & 1) == 1);
474. if (col == 6) col -= 1;
475. if (_modules[row][col - c] == null) {
476. dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
477. if (bitIndex == -1) {
478. margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
479. if (typeof arguments[0] == 'object') {
480. margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
481. if (b == -1) throw 'eof';
482. if (b0 == -1) break;
483. if (typeof b == 'number') {
484. if ( (b & 0xff) == b) {
485. return function(i, j) { return (i + j) % 2 == 0
486. return function(i, j) { return i % 2 == 0
487. return function(i, j) { return j % 3 == 0
488. return function(i, j) { return (i + j) % 3 == 0
489. return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
490. return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
491. return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
492. return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
493. if (r == 0
494. c == 0) {
495. if (dark == qrcode.isDark(row + r, col + c) ) {
496. if (count == 0
497. count == 4) {
498. if (typeof num.length == 'undefined') {
499. num[offset] == 0) {
500. if (typeof rsBlock == 'undefined') {
501. return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
502. _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
503. if (data.length - i == 1) {
504. } else if (data.length - i == 2) {
505. } else if (n == 62) {
506. } else if (n == 63) {
507. if (_buflen == 0) {
508. if (c == '=') {
509. } else if (c == 0x2b) {
510. } else if (c == 0x2f) {
511. if (table.size() == (1 << bitLength) ) {

!== (30 điều kiện duy nhất):
------------------------------------------------------------
1. this.lastValue !== $event.target.value)) {
2. if (YY % 400 === 0 || (YY % 100 !== 0
3. if (YYYY % 400 === 0 || (YYYY % 100 !== 0
4. this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
5. event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
6. key !== '3') {
7. codeResponse.toString() !== '0') {
8. cardNo.length !== 0) {
9. if (this.cardTypeBank !== 'bank_card_number') {
10. if (this.cardTypeBank !== 'bank_account_number') {
11. if (this.cardTypeBank !== 'bank_username') {
12. if (this.cardTypeBank !== 'bank_customer_code') {
13. this.lb_card_account !== this._translate.instant('ocb_account')) {
14. this.lb_card_account !== this._translate.instant('ocb_phone')) {
15. this.lb_card_account !== this._translate.instant('ocb_card')) {
16. this._b !== 18) || (this.cardTypeOcean === 'ATM'
17. this._b !== 18) || (this._b == 18)) {
18. key !== '8') {
19. codeResponse.toString() !== '0'){
20. event.inputType !== 'deleteContentBackward') || v.length == 5) {
21. if (deviceValue !== 'default') {
22. this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
23. return this._i_country_code !== 'default'
24. this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
25. this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {
26. if (_val !== 3) {
27. this._util.getElementParams(this.currentUrl, 't_id') !== '') {
28. queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
29. if (queryString !== '') {
30. if (target !== 0

!= (169 điều kiện duy nhất):
------------------------------------------------------------
1. if (params['locale'] != null
2. } else if (params['locale'] != null
3. if (userLang != null
4. if(value!=null){
5. if (this._idInvoice != null
6. this._idInvoice != 0) {
7. if (this._paymentService.getInvoiceDetail() != null) {
8. dataPassed.body != null) {
9. dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
10. dataPassed.body.themes.border_color != true ? dataPassed.body.themes.border_color : ''
11. if (this._translate.currentLang != language) {
12. notify != ''"
13. if (params['locale'] != null) {
14. } else if (this._b != 18) {
15. if (this.htmlDesc != null
16. if (ua.indexOf('safari') != -1
17. if (_val.value != '') {
18. this.auth_method != null) {
19. if (this.valueDate.length != 3) {
20. if (_formCard.exp_date != null
21. if (this.cardName != null
22. if (this._res_post.links != null
23. this._res_post.links.merchant_return != null
24. this._res_post.links.merchant_return.href != null) {
25. if (this._res_post.authorization != null
26. this._res_post.authorization.links != null
27. this._res_post.authorization.links.approval != null) {
28. this._res_post.links.cancel != null) {
29. this._b != 27
30. this._b != 12
31. this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
32. this._b != 18)
33. if (this._b != 18
34. this._b != 19) {
35. if (this._res.links != null
36. this._res.links.merchant_return != null
37. this._res.links.merchant_return.href != null) {
38. if (!(_formCard.otp != null
39. if (!(_formCard.password != null
40. if (this._inExpDate.length != 3) {
41. if (this._b != 9
42. this._b != 16
43. this._b != 17
44. this._b != 25
45. this._b != 44
46. this._b != 54
47. this._b != 57
48. this._b != 59
49. this._b != 61
50. this._b != 63
51. this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
52. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72' , '73', '74', '75'].indexOf(this._b.toString()) != -1) {
53. if (this._res_post.return_url != null) {
54. let userName = _formCard.name != null ? _formCard.name : ''
55. this._res_post.authorization.links.approval != null
56. this._res_post.authorization.links.approval.href != null) {
57. userName = paramUserName != null ? paramUserName : ''
58. if ('otp' != this._paymentService.getCurrentPage()) {
59. if (!(strInstrument != null
60. if (strInstrument.substring(0, 1) != '^'
61. strInstrument.substr(strInstrument.length - 1) != '$') {
62. if (bankid != null) {
63. _showAVS!=true"
64. } else if (this._res_post.links != null
65. cardNo != null
66. v != null
67. this.c_csc = (!(_val.value != null
68. this._i_csc != null
69. this._paymentService.getState() != 'error') {
70. if (this._paymentService.getCurrentPage() != 'otp') {
71. _re.body != null) {
72. this._res_polling.links != null
73. this._res_polling.links.merchant_return != null //
74. } else if (this._res_polling.merchant != null
75. this._res_polling.merchant_invoice_reference != null
76. } else if (this._res_polling.payments != null
77. this._res_polling.links.merchant_return != null//
78. this._res_polling.payments != null
79. if (this._res_polling.payments != null
80. t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
81. this.type.toString().length != 0) {
82. this._res.links != null
83. if (count != 1) {
84. if (this._res.merchant != null
85. this._res.merchant_invoice_reference != null) {
86. if (this._res.merchant.address_details != null) {
87. this._res.links != null//
88. } else if (this._res.payments != null
89. this._res.payments[this._res.payments.length - 1].instrument != null
90. this._res.payments[this._res.payments.length - 1].instrument.issuer != null
91. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
92. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
93. this._res.payments[this._res.payments.length - 1].links != null
94. this._res.payments[this._res.payments.length - 1].links.cancel != null
95. this._res.payments[this._res.payments.length - 1].links.cancel.href != null
96. this._res.payments[this._res.payments.length - 1].links.update != null
97. this._res.payments[this._res.payments.length - 1].links.update.href != null) {
98. this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
99. this._res.payments[this._res.payments.length - 1].authorization != null
100. this._res.payments[this._res.payments.length - 1].authorization.links != null) {
101. this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
102. this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
103. if (this._res.payments[this._res.payments.length - 1].authorization != null
104. this._res.payments[this._res.payments.length - 1].authorization.links != null
105. auth = paramUserName != null ? paramUserName : ''
106. this._res.links.merchant_return != null //
107. } else if (this._res.merchant != null
108. this._res.merchant_invoice_reference != null
109. if (['mastercard', 'visa', 'amex', 'jcb', 'cup', 'card', 'np_card', 'atm'].indexOf(id) != -1) {
110. } else if (['techcombank_account', 'vib_account', 'dongabank_account', 'tpbank_account', 'bidv_account', 'pvcombank_account', 'shb_account', 'oceanbank_online_account'].indexOf(id) != -1) {
111. } else if (['oceanbank_mobile_account'].indexOf(id) != -1) {
112. } else if (['shb_customer_id'].indexOf(id) != -1) {
113. if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1) {
114. return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
115. e.type != 'ewallet') || (regex.test(strTest)
116. } else if (this._res.payments != null) {
117. if (appcode != null) {
118. if (_re.status != '200'
119. _re.status != '201')
120. 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {
121. qr_version2 != 'None'"
122. qr_version2 != 'None'
123. if (this.translate.currentLang != language) {
124. (((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token
125. feeService['atm']['fee'] != 0"
126. feeService['visa_mastercard_d']['fee'] != 0"
127. feeService['qr']['fee'] != 0"
128. feeService['pp']['fee'] != 0"
129. item['feeService']['fee'] != 0"
130. if (_val.value != null
131. this.c_token_otp_csc = !(_val.value != null
132. if (_re.body.authorization != null
133. _re.body.authorization.links != null
134. if (_re.body.links != null
135. _re.body.links.cancel != null) {
136. if (_re.body.return_url != null) {
137. } else if (_re.body.links != null
138. _re.body.links.merchant_return != null
139. _re.body.links.merchant_return.href != null) {
140. if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(item.brand_id) != -1
141. return this._i_token_otp != null
142. || (id != 'amex'
143. if (idInvoice != null
144. idInvoice != 0)
145. idInvoice != 0) {
146. if (this._merchantid != null
147. this._tranref != null
148. this._state != null
149. let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
150. urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
151. if (paymentId != null) {
152. if (res?.status != 200
153. res?.status != 201) return;
154. _re.status != '201') {
155. latestPayment?.state != "authorization_required") {
156. if (tem != null) {
157. if ((tem = ua.match(/version\/(\d+)/i)) != null) {
158. if (_modules[r][6] != null) {
159. if (_modules[6][c] != null) {
160. if (_modules[row][col] != null) {
161. while (buffer.getLengthInBits() % 8 != 0) {
162. if (count != numChars) {
163. throw count + ' != ' + numChars
164. while (data != 0) {
165. if (test.length != 2
166. ( (test[0] << 8) | test[1]) != code) {
167. if (_length % 3 != 0) {
168. if ( (data >>> length) != 0) {
169. return typeof _map[key] != 'undefined'

