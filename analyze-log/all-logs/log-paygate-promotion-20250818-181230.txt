====================================================================================================
TRÍCH XUẤT ĐIỀU KIỆN SO SÁNH TỪ CÁC FILE TYPESCRIPT/JAVASCRIPT/HTML
====================================================================================================
Thư mục/File nguồn: /root/projects/onepay/paygate-promotion/src
Thời gian: 18:12:30 18/8/2025
Tổng số file xử lý: 223
Tổng số file bị bỏ qua: 3
Tổng số điều kiện tìm thấy: 2431

THỐNG KÊ TỔNG QUAN THEO LOẠI TOÁN TỬ:
--------------------------------------------------
Strict equality (===): 431 lần
Loose equality (==): 1412 lần
Strict inequality (!==): 101 lần
Loose inequality (!=): 487 lần
--------------------------------------------------

DANH SÁCH FILE ĐÃ XỬ LÝ:
--------------------------------------------------
1. 4en.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/4en.html
2. 4vn.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/4vn.html
3. app-routing.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/app-routing.module.ts
4. app.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/app.component.html
5. app.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/app.component.spec.ts
6. app.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/app.component.ts
7. app.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/app.module.ts
8. PaymentType.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/constants/PaymentType.ts
9. counter.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/counter.directive.spec.ts
10. counter.directive.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/counter.directive.ts
11. format-carno-input.derective.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/directives/format-carno-input.derective.ts
12. uppercase-input.directive.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/directives/uppercase-input.directive.ts
13. error.component.html (7 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/error/error.component.html
14. error.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/error/error.component.spec.ts
15. error.component.ts (28 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/error/error.component.ts
16. support-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/error/support-dialog/support-dialog.html
17. support-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/error/support-dialog/support-dialog.ts
18. format-date.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/format-date.directive.spec.ts
19. format-date.directive.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/format-date.directive.ts
20. main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/main.component.html
21. main.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/main.component.spec.ts
22. main.component.ts (10 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/main.component.ts
23. app-result.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/app-result/app-result.component.html
24. app-result.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/app-result/app-result.component.ts
25. bank-amount-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bank-amount-dialog/bank-amount-dialog.component.html
26. bank-amount-dialog.component.ts (34 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bank-amount-dialog/bank-amount-dialog.component.ts
27. amigo-form.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/amigo/amigo-form.component.html
28. amigo-form.component.ts (24 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/amigo/amigo-form.component.ts
29. bnpl-main.component.html (18 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/bnpl-main.component.html
30. bnpl-main.component.ts (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/bnpl-main.component.ts
31. circle-process-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/dialog/circle-process-dialog.html
32. dialog-delete-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/homecredit/dialog-delete-dialog.html
33. dialog-policy.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/homecredit/dialog-policy/dialog-policy.component.html
34. dialog-policy.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/homecredit/dialog-policy/dialog-policy.component.spec.ts
35. dialog-policy.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/homecredit/dialog-policy/dialog-policy.component.ts
36. homecredit-form.component.html (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/homecredit/homecredit-form.component.html
37. homecredit-form.component.ts (37 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/homecredit/homecredit-form.component.ts
38. dialog-delete-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/kbank/dialog-delete-dialog.html
39. kbank-dialog-policy.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/kbank/kbank-dialog-policy/kbank-dialog-policy.component.html
40. kbank-dialog-policy.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/kbank/kbank-dialog-policy/kbank-dialog-policy.component.spec.ts
41. kbank-dialog-policy.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/kbank/kbank-dialog-policy/kbank-dialog-policy.component.ts
42. kbank-form.component.html (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/kbank/kbank-form.component.html
43. kbank-form.component.ts (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/kbank/kbank-form.component.ts
44. kredivo-form.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/kredivo/kredivo-form.component.html
45. kredivo-form.component.ts (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/kredivo/kredivo-form.component.ts
46. otp-auth-bnpl.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/otp-auth-bnpl/otp-auth-bnpl.component.html
47. otp-auth-bnpl.component.ts (12 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/otp-auth-bnpl/otp-auth-bnpl.component.ts
48. process-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/process-dialog/process-dialog.html
49. process-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/process-dialog/process-dialog.ts
50. select-bnpl.html (12 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/select-bnpl-dialog/select-bnpl.html
51. select-bnpl.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/select-bnpl-dialog/select-bnpl.ts
52. cancel-dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/cancel-dialog-guide-dialog.html
53. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/confirm-dialog/dialog-guide-dialog.html
54. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/dialog-guide-dialog.html
55. bankaccount.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
56. bankaccount.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
57. bankaccount.component.ts (155 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
58. bank.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/model/bank.ts
59. onepay-napas.component.html (7 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.html
60. onepay-napas.component.ts (100 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.ts
61. otp-auth.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
62. otp-auth.component.ts (18 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
63. shb.component.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/shb/shb.component.html
64. shb.component.ts (66 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/shb/shb.component.ts
65. techcombank.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
66. techcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
67. techcombank.component.ts (17 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
68. vibbank.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.html
69. vibbank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.spec.ts
70. vibbank.component.ts (155 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.ts
71. vietcombank.component.html (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
72. vietcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
73. vietcombank.component.ts (122 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
74. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
75. off-bank-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
76. off-bank-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
77. policy-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/dialog/policy-dialog/policy-dialog.component.html
78. policy-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/dialog/policy-dialog/policy-dialog.component.ts
79. promotion-apply-failed-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/dialog/promotion-apply-failed-dialog.html
80. domescard-main.component.html (12 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/domescard-main.component.html
81. domescard-main.component.ts (123 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/domescard-main.component.ts
82. bankaccount-promotion.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/bankaccount-promotion/bankaccount-promotion.component.html
83. bankaccount-promotion.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/bankaccount-promotion/bankaccount-promotion.component.spec.ts
84. bankaccount-promotion.component.ts (164 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/bankaccount-promotion/bankaccount-promotion.component.ts
85. bank.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/model/bank.ts
86. otp-auth.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/otp-auth/otp-auth.component.html
87. otp-auth.component.ts (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/otp-auth/otp-auth.component.ts
88. techcombank.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/techcombank/techcombank.component.html
89. techcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/techcombank/techcombank.component.spec.ts
90. techcombank.component.ts (24 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/techcombank/techcombank.component.ts
91. vietcombank-promotion.component.html (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/vietcombank-promotion/vietcombank-promotion.component.html
92. vietcombank-promotion.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/vietcombank-promotion/vietcombank-promotion.component.spec.ts
93. vietcombank-promotion.component.ts (120 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/vietcombank-promotion/vietcombank-promotion.component.ts
94. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/dialog/dialog-guide-dialog.html
95. off-bank-dialog-promotion.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/dialog/off-bank-dialog-promotion/off-bank-dialog-promotion.html
96. off-bank-dialog-promotion.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/dialog/off-bank-dialog-promotion/off-bank-dialog-promotion.ts
97. promotion-apply-failed-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/dialog/promotion-apply-failed-dialog.html
98. domescard-main-promotion.component.html (66 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/domescard-main-promotion.component.html
99. domescard-main-promotion.component.ts (60 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/domescard-main-promotion.component.ts
100. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/intercard-main/dialog-guide-dialog.html
101. intercard-main-form.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/intercard-main/form/intercard-main-form.component.html
102. intercard-main-form.component.ts (79 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/intercard-main/form/intercard-main-form.component.ts
103. intercard-main.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/intercard-main/intercard-main.component.html
104. intercard-main.component.ts (78 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/intercard-main/intercard-main.component.ts
105. menu.component.html (48 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/menu.component.html
106. menu.component.ts (209 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/menu.component.ts
107. applepay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/applepay/applepay.component.html
108. applepay.component.ts (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/applepay/applepay.component.ts
109. dialog-network-not-supported.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/applepay/dialog-network-not-supported/dialog-network-not-supported.component.html
110. dialog-network-not-supported.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/applepay/dialog-network-not-supported/dialog-network-not-supported.component.ts
111. google-pay-button-op.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/googlepay/google-pay-button-op/google-pay-button-op.component.html
112. google-pay-button-op.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/googlepay/google-pay-button-op/google-pay-button-op.component.ts
113. googlepay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/googlepay/googlepay.component.html
114. googlepay.component.ts (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/googlepay/googlepay.component.ts
115. mobile-wallet-main.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/mobile-wallet-main.component.html
116. mobile-wallet-main.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/mobile-wallet-main.component.ts
117. samsung-pay-button-op.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.html
118. samsung-pay-button-op.component.ts (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.ts
119. samsungpay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/samsungpay/samsungpay.component.html
120. samsungpay.component.ts (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/samsungpay/samsungpay.component.ts
121. bottom-guide.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/promotion-main/bottom-guide/bottom-guide.html
122. dialog-guide-dialog.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/promotion-main/promotion-dialog/dialog-guide-dialog.html
123. promotion-main.component.html (26 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/promotion-main/promotion-main.component.html
124. promotion-main.component.ts (17 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/promotion-main/promotion-main.component.ts
125. remove-promotion-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/promotion-main/remove-promotion-dialog/remove-promotion-dialog.html
126. tooltip.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/promotion-main/tooltip/tooltip.component.html
127. tooltip.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/promotion-main/tooltip/tooltip.component.ts
128. tooltip.directive.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/promotion-main/tooltip/tooltip.directive.ts
129. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
130. qr-dialog.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
131. qr-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
132. qr-main.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main/qr-main.component.html
133. qr-main.component.ts (26 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main/qr-main.component.ts
134. safe-html.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main/safe-html.pipe.ts
135. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/dialog/dialog-guide-dialog.html
136. qr-desktop.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.html
137. qr-desktop.component.ts (19 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.ts
138. qr-dialog.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.html
139. qr-dialog.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.ts
140. qr-guide-dialog.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.html
141. qr-guide-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.ts
142. list-bank-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.html
143. list-bank-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.ts
144. qr-main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-main.component.html
145. qr-main.component.ts (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-main.component.ts
146. qr-mobile.component.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.html
147. qr-mobile.component.ts (30 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.ts
148. safe-html.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/safe-html.pipe.ts
149. queuing.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/queuing/queuing.component.html
150. queuing.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/queuing/queuing.component.ts
151. bnpl-form.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/bnpl-form/bnpl-form.component.html
152. bnpl-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/bnpl-form/bnpl-form.component.ts
153. domescard-form.component.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.html
154. domescard-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.ts
155. domescard-form-promotion.component.html (12 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/domescard-form-promotion/domescard-form-promotion.component.html
156. domescard-form-promotion.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/domescard-form-promotion/domescard-form-promotion.component.ts
157. intercard-form.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.html
158. intercard-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.ts
159. mobile-wallet-form.component.html (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/mobile-wallet-form/mobile-wallet-form.component.html
160. mobile-wallet-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/mobile-wallet-form/mobile-wallet-form.component.ts
161. paypal-form.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.html
162. paypal-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.ts
163. qr-form.component.html (16 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/qr-form/qr-form.component.html
164. qr-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/qr-form/qr-form.component.ts
165. token-expired-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/token-expired-dialog/token-expired-dialog.component.html
166. token-expired-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/token-expired-dialog/token-expired-dialog.component.ts
167. remove-token-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/token-main/remove-token-dialog/remove-token-dialog.html
168. dialog-guide-dialog.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/token-main/token-dialog/dialog-guide-dialog.html
169. token-main.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/token-main/token-main.component.html
170. token-main.component.ts (76 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/token-main/token-main.component.ts
171. bnpl-management.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/model/bnpl-management.ts
172. homecredit-management.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/model/homecredit-management.ts
173. kbank-management.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/model/kbank-management.ts
174. overlay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/overlay/overlay.component.html
175. overlay.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/overlay/overlay.component.spec.ts
176. overlay.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/overlay/overlay.component.ts
177. bank-amount.pipe.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/pipe/bank-amount.pipe.ts
178. auth.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/auth.service.ts
179. close-dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/close-dialog.service.ts
180. data.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/data.service.ts
181. deep_link.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/deep_link.service.ts
182. dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/dialog.service.ts
183. digital-wallet.service.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/digital-wallet.service.ts
184. fee.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/fee.service.ts
185. focus-input.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/focus-input.service.ts
186. handle_bnpl_token.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/handle_bnpl_token.service.ts
187. multiple_method.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/multiple_method.service.ts
188. payment.service.ts (13 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/payment.service.ts
189. production-main.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/production-main.service.ts
190. promotion.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/promotion.service.ts
191. qr.service.ts (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/qr.service.ts
192. time-stop.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/time-stop.service.ts
193. token-main.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/token-main.service.ts
194. success.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/success/success.component.html
195. success.component.ts (10 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/success/success.component.ts
196. index.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/translate/index.ts
197. lang-en.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/translate/lang-en.ts
198. lang-vi.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/translate/lang-vi.ts
199. translate.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/translate/translate.pipe.ts
200. translate.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/translate/translate.service.ts
201. translations.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/translate/translations.ts
202. apps-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/util/apps-info.ts
203. apps-information.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/util/apps-information.ts
204. banks-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/util/banks-info.ts
205. bnpl-providers.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/util/bnpl-providers.ts
206. error-handler.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/util/error-handler.ts
207. iso-ca-states.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/util/iso-ca-states.ts
208. iso-us-states.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/util/iso-us-states.ts
209. util.ts (40 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/util/util.ts
210. apple.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/assets/script/apple.js
211. google-pay-intergrate.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/assets/script/google-pay-intergrate.js
212. qrcode.js (67 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/assets/script/qrcode.js
213. environment.development.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/environments/environment.development.ts
214. environment.mtf.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/environments/environment.mtf.ts
215. environment.prod.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/environments/environment.prod.ts
216. environment.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/environments/environment.ts
217. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/index.html
218. karma.conf.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/karma.conf.js
219. main.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/main.ts
220. polyfills.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/polyfills.ts
221. kbank-policy.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/template/kbank-policy/kbank-policy.component.html
222. kbank-policy.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/template/kbank-policy/kbank-policy.component.ts
223. test.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/test.ts

DANH SÁCH FILE BỊ BỎ QUA:
--------------------------------------------------
1. apple-pay-sdk.js
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/assets/script/apple-pay-sdk.js
2. google-pay-sdk.*************.js
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/assets/script/google-pay-sdk.*************.js
3. samsung-pay-sdk.js
   Đường dẫn: /root/projects/onepay/paygate-promotion/src/assets/script/samsung-pay-sdk.js

====================================================================================================
CHI TIẾT TỪNG FILE:
====================================================================================================

📁 FILE 1: 4en.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/4en.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 2: 4vn.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/4vn.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 3: app-routing.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/app-routing.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 4: app.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/app.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 5: app.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/app.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 6: app.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/app.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 82] return lang === this._translate.currentLang

== (2 điều kiện):
  1. [Dòng 61] 'vi' == params['locale']) {
  2. [Dòng 63] 'en' == params['locale']) {

!= (3 điều kiện):
  1. [Dòng 61] if (params['locale'] != null
  2. [Dòng 63] } else if (params['locale'] != null
  3. [Dòng 70] if (userLang != null

================================================================================

📁 FILE 7: app.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/app.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 8: PaymentType.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/constants/PaymentType.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 9: counter.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/counter.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 10: counter.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/counter.directive.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 11: format-carno-input.derective.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/directives/format-carno-input.derective.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 44] this.lastValue !== $event.target.value)) {

!= (1 điều kiện):
  1. [Dòng 23] if(value!=null){

================================================================================

📁 FILE 12: uppercase-input.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/directives/uppercase-input.directive.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 0 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 23] this.lastValue !== $event.target.value)) {

================================================================================

📁 FILE 13: error.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/error/error.component.html
📊 Thống kê: 7 điều kiện duy nhất
   - === : 1 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 41] isPopupSupport === 'True'"

== (6 điều kiện):
  1. [Dòng 19] errorCode == '11'"
  2. [Dòng 41] isSent == false
  3. [Dòng 63] <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
  4. [Dòng 63] (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
  5. [Dòng 66] *ngIf="!(errorCode == 'overtime'
  6. [Dòng 66] errorCode == '253'

================================================================================

📁 FILE 14: error.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/error/error.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 15: error.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/error/error.component.ts
📊 Thống kê: 28 điều kiện duy nhất
   - === : 7 lần
   - == : 18 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 121] params.timeout === 'true') {
  2. [Dòng 145] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  3. [Dòng 145] _re.body.state === 'unpaid');
  4. [Dòng 152] if (this.paymentsNum === this.maxpayment) {
  5. [Dòng 209] if (this.errorCode === 'overtime'
  6. [Dòng 209] this.errorCode === '253') {
  7. [Dòng 349] if (this.timeLeft === 0) {

== (18 điều kiện):
  1. [Dòng 137] if (params && (params['bnpl'] == 'true')) {
  2. [Dòng 173] _re.body.themes.theme == 'token') {
  3. [Dòng 179] params.response_code == 'overtime') {
  4. [Dòng 234] if (_re.status == '200'
  5. [Dòng 234] _re.status == '201') {
  6. [Dòng 246] if (_re2.status == '200'
  7. [Dòng 246] _re2.status == '201') {
  8. [Dòng 258] if (this.errorCode == 'overtime'
  9. [Dòng 258] this.errorCode == '253') {
  10. [Dòng 263] if (this.type == 'bnpl') {
  11. [Dòng 264] if (this.provider == 'amigo'
  12. [Dòng 264] this.errorCode == '2') {
  13. [Dòng 267] else if (this.provider == 'kbank'
  14. [Dòng 270] else if (this.provider == 'homecredit'
  15. [Dòng 273] else if (this.provider == 'kredivo'
  16. [Dòng 279] } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
  17. [Dòng 297] if (lastPayment?.state == 'pending') {
  18. [Dòng 347] if (this.isTimePause == false) {

!= (3 điều kiện):
  1. [Dòng 99] if (message != ''
  2. [Dòng 99] message != null
  3. [Dòng 99] message != undefined) {

================================================================================

📁 FILE 16: support-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/error/support-dialog/support-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 17: support-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/error/support-dialog/support-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 18: format-date.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/format-date.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 19: format-date.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/format-date.directive.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0
  2. [Dòng 123] YY % 4 === 0)) {
  3. [Dòng 138] if (YYYY % 400 === 0
  4. [Dòng 138] YYYY % 4 === 0)) {

!== (2 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0 || (YY % 100 !== 0
  2. [Dòng 138] if (YYYY % 400 === 0 || (YYYY % 100 !== 0

================================================================================

📁 FILE 20: main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 21: main.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/main.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 22: main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/main.component.ts
📊 Thống kê: 10 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 8 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 96] if ((dataPassed.status == '200'
  2. [Dòng 96] dataPassed.status == '201') && dataPassed.body != null) {

!= (8 điều kiện):
  1. [Dòng 87] if (this._idInvoice != null
  2. [Dòng 87] this._idInvoice != 0) {
  3. [Dòng 88] if (this._paymentService.getInvoiceDetail() != null) {
  4. [Dòng 96] dataPassed.body != null) {
  5. [Dòng 119] this.amount_promotion = promotionPassData.data.discount_amount != 0 ? (this._util.formatCurrency(promotionPassData.data.discount_amount).toString() + ' ' + currency) : ''
  6. [Dòng 129] dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
  7. [Dòng 130] dataPassed.body.themes.border_color != true ? dataPassed.body.themes.border_color : ''
  8. [Dòng 184] if (this._translate.currentLang != language) {

================================================================================

📁 FILE 23: app-result.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/app-result/app-result.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 24: app-result.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/app-result/app-result.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 25: bank-amount-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bank-amount-dialog/bank-amount-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 26: bank-amount-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bank-amount-dialog/bank-amount-dialog.component.ts
📊 Thống kê: 34 điều kiện duy nhất
   - === : 0 lần
   - == : 34 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (34 điều kiện):
  1. [Dòng 41] if (this.locale == 'en') {
  2. [Dòng 55] if (name == 'MAFC')
  3. [Dòng 61] if (bankId == 3
  4. [Dòng 61] bankId == 61
  5. [Dòng 62] bankId == 8
  6. [Dòng 62] bankId == 49
  7. [Dòng 63] bankId == 48
  8. [Dòng 64] bankId == 10
  9. [Dòng 64] bankId == 53
  10. [Dòng 65] bankId == 17
  11. [Dòng 65] bankId == 65
  12. [Dòng 66] bankId == 23
  13. [Dòng 66] bankId == 52
  14. [Dòng 67] bankId == 27
  15. [Dòng 67] bankId == 66
  16. [Dòng 68] bankId == 9
  17. [Dòng 68] bankId == 54
  18. [Dòng 69] bankId == 37
  19. [Dòng 70] bankId == 38
  20. [Dòng 71] bankId == 39
  21. [Dòng 72] bankId == 40
  22. [Dòng 73] bankId == 42
  23. [Dòng 74] bankId == 44
  24. [Dòng 75] bankId == 72
  25. [Dòng 76] bankId == 59
  26. [Dòng 79] bankId == 51
  27. [Dòng 80] bankId == 64
  28. [Dòng 81] bankId == 58
  29. [Dòng 82] bankId == 56
  30. [Dòng 85] bankId == 55
  31. [Dòng 86] bankId == 60
  32. [Dòng 87] bankId == 68
  33. [Dòng 88] bankId == 74
  34. [Dòng 89] bankId == 75 //KEB HANA

================================================================================

📁 FILE 27: amigo-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/amigo/amigo-form.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 31] bnplDetail.method == 'SP'"
  2. [Dòng 49] bnplDetail.method == 'PL'"

!= (3 điều kiện):
  1. [Dòng 3] selectedBnpl.status != 'disabled'"
  2. [Dòng 45] bnplDetail.method != 'SP'"
  3. [Dòng 63] bnplDetail.method != 'PL'"

================================================================================

📁 FILE 28: amigo-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/amigo/amigo-form.component.ts
📊 Thống kê: 24 điều kiện duy nhất
   - === : 2 lần
   - == : 17 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 183] return index === array.findIndex(obj => {
  2. [Dòng 184] return JSON.stringify(obj) === _value

== (17 điều kiện):
  1. [Dòng 77] _re.code == '0'
  2. [Dòng 114] packageItem.product_code == productCode) {
  3. [Dòng 132] a.product_code == 'SP') {
  4. [Dòng 145] a.product_code == 'PL') {
  5. [Dòng 197] if (this.selectedIndex == 0
  6. [Dòng 201] } else if ((this.selectedIndex == 1
  7. [Dòng 201] this.payLaterSubmit) || (this.selectedIndex == 0
  8. [Dòng 204] item.prepaid_percent == this.selectedPrePaid
  9. [Dòng 204] item.installment_month == this.selectedPayLater) {
  10. [Dòng 237] if (string == 'SP') {
  11. [Dòng 240] } else if (string == 'PL') {
  12. [Dòng 283] if (this._locale == 'en') {
  13. [Dòng 293] if (this._res_post.state == 'approved'
  14. [Dòng 293] this._res_post.state == 'failed') {
  15. [Dòng 299] if (this._res_post.state == 'failed') {
  16. [Dòng 316] } else if (this._res_post.state == 'authorization_required') {
  17. [Dòng 317] if ('REDIRECT' == this._res_post.authorization.links.approval.method) {

!= (5 điều kiện):
  1. [Dòng 224] 'total_paid': this.data?.amount ? (item.diff_amount != 0 ? this._util.formatMoney(parseFloat(item.diff_amount) + parseFloat(this.data?.amount), 0, '.', ',') : this._util.formatMoney(parseFloat(this.data?.amount), 0, '.', ',')) : 0,
  2. [Dòng 294] if (this._res_post.return_url != null) {
  3. [Dòng 296] } else if (this._res_post.links != null
  4. [Dòng 296] this._res_post.links.merchant_return != null
  5. [Dòng 296] this._res_post.links.merchant_return.href != null) {

================================================================================

📁 FILE 29: bnpl-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/bnpl-main.component.html
📊 Thống kê: 18 điều kiện duy nhất
   - === : 2 lần
   - == : 13 lần
   - !== : 1 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 23] bnpl.code === 'kbank'"
  2. [Dòng 26] bnpl.code === 'homepaylater'"

== (13 điều kiện):
  1. [Dòng 20] bnpl.status == 'disabled'"
  2. [Dòng 21] bnpl.status == 'active'"
  3. [Dòng 21] [class.bnpl-active-item]="bnpl.status == 'active'" (click)="onClick(bnpl.status == 'disabled')">
  4. [Dòng 30] bnpl.code == 'homepaylater'"
  5. [Dòng 31] bnpl.status == 'disabled'
  6. [Dòng 39] bnpl.code == 'kbank'
  7. [Dòng 42] bnpl.code == 'insta'"
  8. [Dòng 46] bnpl.code == 'kredivo'
  9. [Dòng 59] selectedBnpl.code == 'homepaylater'"
  10. [Dòng 64] selectedBnpl.code == 'kbank'"
  11. [Dòng 69] selectedBnpl.code == 'insta'"
  12. [Dòng 75] selectedBnpl.code == 'kredivo'"
  13. [Dòng 82] _auth == 1"

!== (1 điều kiện):
  1. [Dòng 29] bnpl.code !== 'kbank'

!= (2 điều kiện):
  1. [Dòng 1] _auth != 1"
  2. [Dòng 29] bnpl.code != 'homepaylater'"

================================================================================

📁 FILE 30: bnpl-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/bnpl-main.component.ts
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 132] this.kredivoProvider = this.bankAmountArr.find(item => item.code === 'kredivo');
  2. [Dòng 174] this.bnpls.push(this.sublist.find(e => e.name === 'KBank Pay Later'));
  3. [Dòng 177] this.bnpls.push(this.sublist.find(e => e.name === 'Home PayLater'));
  4. [Dòng 180] this.bnpls.push(this.sublist.find(e => e.name === 'Kredivo'));
  5. [Dòng 183] this.bnpls.push(this.sublist.find(e => e.name === 'Insta'));
  6. [Dòng 200] return index === array.findIndex(obj => {
  7. [Dòng 201] return JSON.stringify(obj) === _value

== (2 điều kiện):
  1. [Dòng 209] item.prepaid_percent == this.selectedPrePaid
  2. [Dòng 209] item.installment_month == this.selectedPayLater) {

================================================================================

📁 FILE 31: circle-process-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/dialog/circle-process-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 32: dialog-delete-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/homecredit/dialog-delete-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 33: dialog-policy.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/homecredit/dialog-policy/dialog-policy.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 34: dialog-policy.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/homecredit/dialog-policy/dialog-policy.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 35: dialog-policy.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/homecredit/dialog-policy/dialog-policy.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 36: homecredit-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/homecredit/homecredit-form.component.html
📊 Thống kê: 8 điều kiện duy nhất
   - === : 2 lần
   - == : 5 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 173] object.id === value ? 'select-option-active' : ''"
  2. [Dòng 175] object.id === value"

== (5 điều kiện):
  1. [Dòng 186] value == 'add' ? 'select-option-active' : ''"
  2. [Dòng 187] value == 'add'"
  3. [Dòng 194] value=='add'"
  4. [Dòng 314] value == 'add') || !isOnepayPolicy" [disabled]="(isInvalid()
  5. [Dòng 314] value == 'add') || !isOnepayPolicy">

!= (1 điều kiện):
  1. [Dòng 232] && ((homecreidtDetail['phoneNumber'].length != 10) || !this.homecreidtDetail['phoneNumber'].startsWith('0'))

================================================================================

📁 FILE 37: homecredit-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/homecredit/homecredit-form.component.ts
📊 Thống kê: 37 điều kiện duy nhất
   - === : 3 lần
   - == : 19 lần
   - !== : 1 lần
   - != : 14 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 336] if (this._locale === 'vi') {
  2. [Dòng 339] if (this._locale === 'en') {
  3. [Dòng 367] if (this.value === 'add') {

== (19 điều kiện):
  1. [Dòng 168] if (response.status == '200'
  2. [Dòng 168] response.status == '201') {
  3. [Dòng 171] this.listTokenBnpl.length == 0) {
  4. [Dòng 174] if (this.value == object.id) {
  5. [Dòng 241] if (name == 'email') {
  6. [Dòng 244] if (name == 'phoneNumber') {
  7. [Dòng 247] if (name == 'fullname') {
  8. [Dòng 299] if (_re.status == '200'
  9. [Dòng 299] _re.status == '201') {
  10. [Dòng 300] _re.body.state == 'success')
  11. [Dòng 301] _re.body.data.discount_amount == 0))) {
  12. [Dòng 324] _re.body.state == 'fail'
  13. [Dòng 424] if (this._res_post.state == 'approved'
  14. [Dòng 424] this._res_post.state == 'failed') {
  15. [Dòng 430] if (this._res_post.state == 'failed') {
  16. [Dòng 446] } else if (this._res_post.state == 'authorization_required') {
  17. [Dòng 447] if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  18. [Dòng 495] this._res_post.state == 'authorization_required') {
  19. [Dòng 565] this._res_post.code == 'KB-02') {

!== (1 điều kiện):
  1. [Dòng 517] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (14 điều kiện):
  1. [Dòng 117] this.value = (this.listTokenBnpl.length != 0) ? this.listTokenBnpl[0]['id'] : "add";
  2. [Dòng 170] this.listTokenBnpl = this.listTokenBnpl.filter(item => item.id != result.id);
  3. [Dòng 312] if (ua.indexOf('safari') != -1
  4. [Dòng 425] if (this._res_post.return_url != null) {
  5. [Dòng 427] } else if (this._res_post.links != null
  6. [Dòng 427] this._res_post.links.merchant_return != null
  7. [Dòng 427] this._res_post.links.merchant_return.href != null) {
  8. [Dòng 500] if (this._res_post.authorization != null
  9. [Dòng 500] this._res_post.authorization.links != null
  10. [Dòng 505] if (this._res_post.links != null
  11. [Dòng 505] this._res_post.links.cancel != null) {
  12. [Dòng 512] this._res_post.authorization.links.approval != null
  13. [Dòng 512] this._res_post.authorization.links.approval.href != null) {
  14. [Dòng 515] userName = paramUserName != null ? paramUserName : ''

================================================================================

📁 FILE 38: dialog-delete-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/kbank/dialog-delete-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 39: kbank-dialog-policy.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/kbank/kbank-dialog-policy/kbank-dialog-policy.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 40: kbank-dialog-policy.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/kbank/kbank-dialog-policy/kbank-dialog-policy.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 41: kbank-dialog-policy.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/kbank/kbank-dialog-policy/kbank-dialog-policy.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 42: kbank-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/kbank/kbank-form.component.html
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 5 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 92] object.id === value ? 'select-option-active' : ''"
  2. [Dòng 94] object.id === value"

== (5 điều kiện):
  1. [Dòng 104] value == 'add' ? 'select-option-active' : ''"
  2. [Dòng 105] value == 'add'"
  3. [Dòng 112] value=='add'"
  4. [Dòng 154] !cmndBlur ? 'no-border' : ((kbankDetail['citizen_id'] && !(kbankDetail['citizen_id'].length == 9 || kbankDetail['citizen_id'].length == 12) || !kbankDetail['citizen_id']) &&  bnplForm.controls['citizen_id'].touched) ? 'ng-invalid ng-dirty' : ''
  5. [Dòng 159] bnplForm.controls['citizen_id'].touched && kbankDetail['citizen_id'] && !(kbankDetail['citizen_id'].length == 9 || kbankDetail['citizen_id'].length == 12) && cmndBlur

!== (1 điều kiện):
  1. [Dòng 3] selectedBnpl.status !== 'disabled'"

!= (1 điều kiện):
  1. [Dòng 184] selectedBnpl.status != 'disabled'"

================================================================================

📁 FILE 43: kbank-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/kbank/kbank-form.component.ts
📊 Thống kê: 22 điều kiện duy nhất
   - === : 0 lần
   - == : 13 lần
   - !== : 1 lần
   - != : 8 lần
--------------------------------------------------------------------------------

== (13 điều kiện):
  1. [Dòng 81] this.value == 'add')){
  2. [Dòng 117] list = this.data.customer.tokens.filter((item) => item.instrument.issuer.brand.id == 'kbank')
  3. [Dòng 173] if (response.status == '200'
  4. [Dòng 173] response.status == '201') {
  5. [Dòng 181] this.listTokenBnpl.length == 0) {
  6. [Dòng 184] if (this.value == object.id) {
  7. [Dòng 204] return (this.bnplForm.invalid || (this.kbankDetail['citizen_id'] && !(this.kbankDetail['citizen_id'].length == 9) && !(this.kbankDetail['citizen_id'].length == 12)));
  8. [Dòng 244] if (name == 'citizen_id') {
  9. [Dòng 247] if (name == 'phoneNumber') {
  10. [Dòng 250] if (name == 'fullname') {
  11. [Dòng 310] this._res_post.state == 'authorization_required') {
  12. [Dòng 378] this._res_post.code == 'KB-02') {
  13. [Dòng 396] this._res_post.state == 'failed') {

!== (1 điều kiện):
  1. [Dòng 332] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (8 điều kiện):
  1. [Dòng 179] this.listTokenBnpl = this.listTokenBnpl.filter(item => item.id != result.id);
  2. [Dòng 315] if (this._res_post.authorization != null
  3. [Dòng 315] this._res_post.authorization.links != null
  4. [Dòng 320] if (this._res_post.links != null
  5. [Dòng 320] this._res_post.links.cancel != null) {
  6. [Dòng 327] this._res_post.authorization.links.approval != null
  7. [Dòng 327] this._res_post.authorization.links.approval.href != null) {
  8. [Dòng 330] userName = paramUserName != null ? paramUserName : ''

================================================================================

📁 FILE 44: kredivo-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/kredivo/kredivo-form.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 3 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 3] selectedBnpl.status !== 'disabled'"

!= (3 điều kiện):
  1. [Dòng 100] document.activeElement.id!='fullname'"
  2. [Dòng 116] document.activeElement.id!='phoneNumber'"
  3. [Dòng 133] document.activeElement.id!='email'"

================================================================================

📁 FILE 45: kredivo-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/kredivo/kredivo-form.component.ts
📊 Thống kê: 15 điều kiện duy nhất
   - === : 0 lần
   - == : 10 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

== (10 điều kiện):
  1. [Dòng 139] if (this._locale == 'en') {
  2. [Dòng 149] if (this._res_post.state == 'approved'
  3. [Dòng 149] this._res_post.state == 'failed') {
  4. [Dòng 155] if (this._res_post.state == 'failed') {
  5. [Dòng 171] } else if (this._res_post.state == 'authorization_required') {
  6. [Dòng 172] if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  7. [Dòng 214] "applicable": (type == "6"
  8. [Dòng 214] type == "12") ? "premium_users" : "basic_or_premium_users"
  9. [Dòng 249] this.bnplDetail.fullname?.length == 0 ? this._translate.instant('kredivo_empty_fullname_message')
  10. [Dòng 267] this.bnplDetail.email?.length == 0 ? this._translate.instant('kredivo_empty_email_message')

!= (5 điều kiện):
  1. [Dòng 150] if (this._res_post.return_url != null) {
  2. [Dòng 152] } else if (this._res_post.links != null
  3. [Dòng 152] this._res_post.links.merchant_return != null
  4. [Dòng 152] this._res_post.links.merchant_return.href != null) {
  5. [Dòng 258] this.bnplDetail.phoneNumber?.length != 10 ?

================================================================================

📁 FILE 46: otp-auth-bnpl.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/otp-auth-bnpl/otp-auth-bnpl.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 47: otp-auth-bnpl.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/otp-auth-bnpl/otp-auth-bnpl.component.ts
📊 Thống kê: 12 điều kiện duy nhất
   - === : 0 lần
   - == : 5 lần
   - !== : 1 lần
   - != : 6 lần
--------------------------------------------------------------------------------

== (5 điều kiện):
  1. [Dòng 148] if (_re.status == '200'
  2. [Dòng 148] _re.status == '201') {
  3. [Dòng 159] else if (this._res.code == '2') {
  4. [Dòng 201] _re.body.payments[_re.body.payments.length - 1].reason.code == 'B4') {
  5. [Dòng 308] if (this._b == 18) {//8-MB Bank;18-oceanbank

!== (1 điều kiện):
  1. [Dòng 179] codeResponse.toString() !== '0') {

!= (6 điều kiện):
  1. [Dòng 153] if (this._res.links != null
  2. [Dòng 153] this._res.links.merchant_return != null
  3. [Dòng 153] this._res.links.merchant_return.href != null) {
  4. [Dòng 303] if (!(_formCard.otp != null
  5. [Dòng 309] if (!(_formCard.password != null
  6. [Dòng 325] if (ua.indexOf('safari') != -1

================================================================================

📁 FILE 48: process-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/process-dialog/process-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 49: process-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/process-dialog/process-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 50: select-bnpl.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/select-bnpl-dialog/select-bnpl.html
📊 Thống kê: 12 điều kiện duy nhất
   - === : 1 lần
   - == : 9 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 18] bnpl.code === 'homepaylater'"

== (9 điều kiện):
  1. [Dòng 7] bnpl.status == 'disabled'"
  2. [Dòng 8] bnpl.status == 'active'"
  3. [Dòng 15] bnpl.code == 'kbank'"
  4. [Dòng 23] bnpl.code == 'homepaylater'
  5. [Dòng 26] bnpl.code == 'kbank'
  6. [Dòng 29] bnpl.status == 'disabled'
  7. [Dòng 29] bnpl.code == 'insta'"
  8. [Dòng 33] bnpl.status == 'active'
  9. [Dòng 36] bnpl.code == 'kredivo'

!= (2 điều kiện):
  1. [Dòng 21] bnpl.code != 'kbank'
  2. [Dòng 21] bnpl.code != 'homepaylater'"

================================================================================

📁 FILE 51: select-bnpl.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/bnpl-main/select-bnpl-dialog/select-bnpl.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 28] if (bnpl.status == 'disabled') {

================================================================================

📁 FILE 52: cancel-dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/cancel-dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 53: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/confirm-dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 54: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 55: bankaccount.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 39] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 53] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 53] valueDate.trim().length === 0)"

================================================================================

📁 FILE 56: bankaccount.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 57: bankaccount.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
📊 Thống kê: 155 điều kiện duy nhất
   - === : 47 lần
   - == : 74 lần
   - !== : 13 lần
   - != : 21 lần
--------------------------------------------------------------------------------

=== (47 điều kiện):
  1. [Dòng 123] if (isIE[0] === 'MSIE'
  2. [Dòng 123] +isIE[1] === 10) {
  3. [Dòng 207] if ((_val.value.substr(-1) === ' '
  4. [Dòng 207] _val.value.length === 24) {
  5. [Dòng 217] if (this.cardTypeBank === 'bank_card_number') {
  6. [Dòng 222] } else if (this.cardTypeBank === 'bank_account_number') {
  7. [Dòng 228] } else if (this.cardTypeBank === 'bank_username') {
  8. [Dòng 232] } else if (this.cardTypeBank === 'bank_customer_code') {
  9. [Dòng 238] this.cardTypeBank === 'bank_card_number'
  10. [Dòng 252] if (this.cardTypeOcean === 'IB') {
  11. [Dòng 256] } else if (this.cardTypeOcean === 'MB') {
  12. [Dòng 257] if (_val.value.substr(0, 2) === '84') {
  13. [Dòng 264] } else if (this.cardTypeOcean === 'ATM') {
  14. [Dòng 291] if (this.cardTypeBank === 'bank_account_number') {
  15. [Dòng 310] this.cardTypeBank === 'bank_card_number') {
  16. [Dòng 332] if (this.cardTypeBank === 'bank_card_number'
  17. [Dòng 332] if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  18. [Dòng 653] if (event.keyCode === 8
  19. [Dòng 653] event.key === "Backspace"
  20. [Dòng 693] if (v.length === 2
  21. [Dòng 693] this.flag.length === 3
  22. [Dòng 693] this.flag.charAt(this.flag.length - 1) === '/') {
  23. [Dòng 697] if (v.length === 1) {
  24. [Dòng 699] } else if (v.length === 2) {
  25. [Dòng 702] v.length === 2) {
  26. [Dòng 710] if (len === 2) {
  27. [Dòng 982] if ((this.cardTypeBank === 'bank_account_number'
  28. [Dòng 982] this.cardTypeBank === 'bank_username'
  29. [Dòng 982] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  30. [Dòng 987] this.cardTypeOcean === 'ATM')
  31. [Dòng 988] || (this.cardTypeOcean === 'IB'
  32. [Dòng 1047] if (valIn === this._translate.instant('bank_card_number')) {
  33. [Dòng 1072] } else if (valIn === this._translate.instant('bank_account_number')) {
  34. [Dòng 1091] } else if (valIn === this._translate.instant('bank_username')) {
  35. [Dòng 1107] } else if (valIn === this._translate.instant('bank_customer_code')) {
  36. [Dòng 1196] if (_val.value === ''
  37. [Dòng 1196] _val.value === null
  38. [Dòng 1196] _val.value === undefined) {
  39. [Dòng 1207] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  40. [Dòng 1207] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  41. [Dòng 1214] this.cardTypeOcean === 'MB') {
  42. [Dòng 1222] this.cardTypeOcean === 'IB'
  43. [Dòng 1228] if ((this.cardTypeBank === 'bank_card_number'
  44. [Dòng 1262] if (this.cardName === undefined
  45. [Dòng 1262] this.cardName === '') {
  46. [Dòng 1270] if (this.valueDate === undefined
  47. [Dòng 1270] this.valueDate === '') {

== (74 điều kiện):
  1. [Dòng 119] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 137] if (this._b == 18
  3. [Dòng 137] this._b == 19) {
  4. [Dòng 140] if (this._b == 19) {//19BIDV
  5. [Dòng 148] } else if (this._b == 3
  6. [Dòng 148] this._b == 27
  7. [Dòng 148] this._b == 5) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  8. [Dòng 153] if (this._b == 27
  9. [Dòng 153] this._b == 5) {
  10. [Dòng 158] } else if (this._b == 12) {// 12SHB
  11. [Dòng 163] } else if (this._b == 18) { //18Oceanbank-ocb
  12. [Dòng 216] if (this._b == 19
  13. [Dòng 216] this._b == 3
  14. [Dòng 216] this._b == 12
  15. [Dòng 251] } else if (this._b == 18) {
  16. [Dòng 282] if (this.checkBin(_val.value) && (this._b == 3
  17. [Dòng 282] this._b == 5)) {
  18. [Dòng 287] if (this._b == 3) {
  19. [Dòng 299] this.cardTypeOcean == 'ATM') {
  20. [Dòng 312] if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  21. [Dòng 332] this._b == 18)) {
  22. [Dòng 355] this._b == 12) {
  23. [Dòng 408] if (this.checkBin(v) && (this._b == 3
  24. [Dòng 408] this._b == 27)) {
  25. [Dòng 410] if (this._b == 27) {
  26. [Dòng 653] event.inputType == 'deleteContentBackward') {
  27. [Dòng 654] if (event.target.name == 'exp_date'
  28. [Dòng 662] event.inputType == 'insertCompositionText') {
  29. [Dòng 677] if (((this.valueDate.length == 4
  30. [Dòng 677] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  31. [Dòng 677] this.valueDate.length == 5)
  32. [Dòng 757] if (temp.length == 0) {
  33. [Dòng 764] return (counter % 10 == 0);
  34. [Dòng 784] } else if (this._b == 19) {
  35. [Dòng 786] } else if (this._b == 27) {
  36. [Dòng 788] } else if (this._b == 5) {
  37. [Dòng 793] if (this._b == 12) {
  38. [Dòng 795] if (this.cardTypeBank == 'bank_customer_code') {
  39. [Dòng 797] } else if (this.cardTypeBank == 'bank_account_number') {
  40. [Dòng 814] _formCard.exp_date.length == 5
  41. [Dòng 814] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
  42. [Dòng 814] this._b == 3)) {//27-pvcombank;3-TPB ;
  43. [Dòng 819] if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
  44. [Dòng 819] this._b == 19
  45. [Dòng 819] this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
  46. [Dòng 822] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  47. [Dòng 825] if (this.cardTypeOcean == 'IB') {
  48. [Dòng 827] } else if (this.cardTypeOcean == 'MB') {
  49. [Dòng 829] } else if (this.cardTypeOcean == 'ATM') {
  50. [Dòng 858] this.token_site == 'onepay'
  51. [Dòng 877] if (_re.status == '200'
  52. [Dòng 877] _re.status == '201') {
  53. [Dòng 882] if (this._res_post.state == 'approved'
  54. [Dòng 882] this._res_post.state == 'failed') {
  55. [Dòng 889] } else if (this._res_post.state == 'authorization_required') {
  56. [Dòng 907] if (this._b == 18) {
  57. [Dòng 912] this._b == 18) {
  58. [Dòng 1002] if ((cardNo.length == 16
  59. [Dòng 1002] if ((cardNo.length == 16 || (cardNo.length == 19
  60. [Dòng 1003] && ((this._b == 18
  61. [Dòng 1003] cardNo.length == 19) || this._b != 18)
  62. [Dòng 1016] if (this._b == +e.id) {
  63. [Dòng 1032] if (valIn == 1) {
  64. [Dòng 1034] } else if (valIn == 2) {
  65. [Dòng 1065] if (this._b == 19) {
  66. [Dòng 1129] if (cardType == this._translate.instant('internetbanking')
  67. [Dòng 1137] } else if (cardType == this._translate.instant('mobilebanking')
  68. [Dòng 1145] } else if (cardType == this._translate.instant('atm')
  69. [Dòng 1207] this._b == 18))) {
  70. [Dòng 1214] } else if (this._b == 18
  71. [Dòng 1240] this.c_expdate = !(((this.valueDate.length == 4
  72. [Dòng 1273] this.valueDate.length == 4
  73. [Dòng 1273] this.valueDate.search('/') == -1)
  74. [Dòng 1274] this.valueDate.length == 5))

!== (13 điều kiện):
  1. [Dòng 207] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 863] key !== '3') {
  3. [Dòng 913] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 931] codeResponse.toString() !== '0') {
  5. [Dòng 982] cardNo.length !== 0) {
  6. [Dòng 1054] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 1075] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 1096] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 1116] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 1129] this.lb_card_account !== this._translate.instant('ocb_account')) {
  11. [Dòng 1137] this.lb_card_account !== this._translate.instant('ocb_phone')) {
  12. [Dòng 1145] this.lb_card_account !== this._translate.instant('ocb_card')) {
  13. [Dòng 1228] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (21 điều kiện):
  1. [Dòng 169] } else if (this._b != 18) {
  2. [Dòng 175] if (this.htmlDesc != null
  3. [Dòng 204] if (ua.indexOf('safari') != -1
  4. [Dòng 214] if (_val.value != '') {
  5. [Dòng 300] this.auth_method != null) {
  6. [Dòng 655] if (this.valueDate.length != 3) {
  7. [Dòng 814] if (_formCard.exp_date != null
  8. [Dòng 819] if (this.cardName != null
  9. [Dòng 885] if (this._res_post.links != null
  10. [Dòng 885] this._res_post.links.merchant_return != null
  11. [Dòng 885] this._res_post.links.merchant_return.href != null) {
  12. [Dòng 893] if (this._res_post.authorization != null
  13. [Dòng 893] this._res_post.authorization.links != null
  14. [Dòng 893] this._res_post.authorization.links.approval != null) {
  15. [Dòng 900] this._res_post.links.cancel != null) {
  16. [Dòng 1002] this._b != 27
  17. [Dòng 1002] this._b != 12
  18. [Dòng 1002] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  19. [Dòng 1003] this._b != 18)
  20. [Dòng 1049] if (this._b != 18
  21. [Dòng 1049] this._b != 19) {

================================================================================

📁 FILE 58: bank.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/model/bank.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 59: onepay-napas.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.html
📊 Thống kê: 7 điều kiện duy nhất
   - === : 4 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 26] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 41] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  3. [Dòng 41] _inExpDate.trim().length === 0)"
  4. [Dòng 65] ready===1"

== (3 điều kiện):
  1. [Dòng 23] <div [ngStyle]="{'margin-top': !checkTwoEnabled ? '15px' : '0px' }" *ngIf="(cardTypeBank == 'bank_card_number') &&(_b == 67
  2. [Dòng 65] (cardTypeBank == 'bank_card_number') &&(_b == 67 || checkTwoEnabled)&& ready===1
  3. [Dòng 97] <div [ngStyle]="{'margin-top': !checkTwoEnabled ? '15px' : '0px' }" class="nd-bank-card-two" *ngIf="(cardTypeBank == 'internet_banking')&&(_b == 2

================================================================================

📁 FILE 60: onepay-napas.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.ts
📊 Thống kê: 100 điều kiện duy nhất
   - === : 20 lần
   - == : 45 lần
   - !== : 4 lần
   - != : 31 lần
--------------------------------------------------------------------------------

=== (20 điều kiện):
  1. [Dòng 116] if (isIE[0] === 'MSIE'
  2. [Dòng 116] +isIE[1] === 10) {
  3. [Dòng 140] if (this.timeLeft === 10) {
  4. [Dòng 144] if (this.runTime === true) {
  5. [Dòng 150] if (this.timeLeft === 0) {
  6. [Dòng 152] if (this.runTime === true) this.submitCardBanking();
  7. [Dòng 335] if (event.keyCode === 8
  8. [Dòng 335] event.key === "Backspace"
  9. [Dòng 543] if (approval.method === 'REDIRECT') {
  10. [Dòng 546] } else if (approval.method === 'POST_REDIRECT') {
  11. [Dòng 619] if (valIn === this._translate.instant('bank_card_number')) {
  12. [Dòng 621] if (this.timeLeft === 1) {
  13. [Dòng 638] } else if (valIn === this._translate.instant('internet_banking')) {
  14. [Dòng 718] if (_val.value === ''
  15. [Dòng 718] _val.value === null
  16. [Dòng 718] _val.value === undefined) {
  17. [Dòng 729] if (_val.value && (this.cardTypeBank === 'bank_card_number')) {
  18. [Dòng 739] if ((this.cardTypeBank === 'bank_card_number'
  19. [Dòng 777] if (this.cardName === undefined
  20. [Dòng 777] this.cardName === '') {

== (45 điều kiện):
  1. [Dòng 112] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 132] if (this._b == 67
  3. [Dòng 132] this._b == 2) {//19BIDV
  4. [Dòng 138] if (this._b == 2
  5. [Dòng 162] if (this._b == 67) {
  6. [Dòng 192] this.token_site == 'onepay'
  7. [Dòng 201] if (_re.status == '200'
  8. [Dòng 201] _re.status == '201') {
  9. [Dòng 206] if (this._res_post.state == 'approved'
  10. [Dòng 206] this._res_post.state == 'failed') {
  11. [Dòng 210] } else if (this._res_post.state == 'authorization_required') {
  12. [Dòng 320] return this._b == 2
  13. [Dòng 320] this._b == 67
  14. [Dòng 335] event.inputType == 'deleteContentBackward') {
  15. [Dòng 336] if (event.target.name == 'exp_date'
  16. [Dòng 344] event.inputType == 'insertCompositionText') {
  17. [Dòng 406] if (temp.length == 0) {
  18. [Dòng 413] return (counter % 10 == 0);
  19. [Dòng 429] _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
  20. [Dòng 582] if ((cardNo.length == 16
  21. [Dòng 582] if ((cardNo.length == 16 || (cardNo.length == 19
  22. [Dòng 583] this.checkMod10(cardNo) == true
  23. [Dòng 596] if (this._b == +e.id) {
  24. [Dòng 672] if (this._b == 19) {
  25. [Dòng 676] if (this._b == 27
  26. [Dòng 676] this._b == 3) {
  27. [Dòng 751] if (this._b != 68 || (this._b == 68
  28. [Dòng 760] return ((value.length == 4
  29. [Dòng 760] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  30. [Dòng 760] value.length == 5) && parseInt(value.split('/')[0]
  31. [Dòng 764] || (this._util.checkValidExpireMonthYear(value) && (this._b == 2
  32. [Dòng 764] this._b == 20
  33. [Dòng 764] this._b == 33
  34. [Dòng 765] this._b == 39
  35. [Dòng 765] this._b == 43
  36. [Dòng 765] this._b == 45
  37. [Dòng 765] this._b == 64
  38. [Dòng 765] this._b == 68
  39. [Dòng 765] this._b == 72))) //sonnh them Vietbank 72
  40. [Dòng 786] this._inExpDate.length == 4
  41. [Dòng 786] this._inExpDate.search('/') == -1)
  42. [Dòng 787] this._inExpDate.length == 5))
  43. [Dòng 789] || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 20
  44. [Dòng 789] this._b == 2
  45. [Dòng 789] this._b == 72)));

!== (4 điều kiện):
  1. [Dòng 225] codeResponse.toString() !== '0') {
  2. [Dòng 580] let _b = this._b !== 67 ? 67 : this._b
  3. [Dòng 665] if (this.cardTypeBank !== 'internet_banking') {
  4. [Dòng 739] this._b !== 18)) {

!= (31 điều kiện):
  1. [Dòng 157] if (this.htmlDesc != null
  2. [Dòng 214] if (this._res_post.authorization != null
  3. [Dòng 214] this._res_post.authorization.links != null
  4. [Dòng 214] this._res_post.authorization.links.approval != null) {
  5. [Dòng 272] if (ua.indexOf('safari') != -1
  6. [Dòng 337] if (this._inExpDate.length != 3) {
  7. [Dòng 429] if (_formCard.exp_date != null
  8. [Dòng 434] if (this.cardName != null
  9. [Dòng 471] if (this._res_post.return_url != null) {
  10. [Dòng 474] if (this._res_post.links != null
  11. [Dòng 474] this._res_post.links.merchant_return != null
  12. [Dòng 474] this._res_post.links.merchant_return.href != null) {
  13. [Dòng 528] this._res_post.links.cancel != null) {
  14. [Dòng 533] let userName = _formCard.name != null ? _formCard.name : ''
  15. [Dòng 534] this._res_post.authorization.links.approval != null
  16. [Dòng 534] this._res_post.authorization.links.approval.href != null) {
  17. [Dòng 537] userName = paramUserName != null ? paramUserName : ''
  18. [Dòng 582] this._b != 27
  19. [Dòng 582] this._b != 12
  20. [Dòng 582] this._b != 3))
  21. [Dòng 751] if (this._b != 68
  22. [Dòng 762] this._b != 2
  23. [Dòng 762] this._b != 20
  24. [Dòng 762] this._b != 33
  25. [Dòng 762] this._b != 39
  26. [Dòng 763] this._b != 43
  27. [Dòng 763] this._b != 45
  28. [Dòng 763] this._b != 64
  29. [Dòng 763] this._b != 67
  30. [Dòng 763] this._b != 68
  31. [Dòng 763] this._b != 72)

================================================================================

📁 FILE 61: otp-auth.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 62: otp-auth.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
📊 Thống kê: 18 điều kiện duy nhất
   - === : 0 lần
   - == : 11 lần
   - !== : 1 lần
   - != : 6 lần
--------------------------------------------------------------------------------

== (11 điều kiện):
  1. [Dòng 97] if (this._b == 8) {//MB Bank
  2. [Dòng 101] if (this._b == 18) {//Oceanbank
  3. [Dòng 138] if (this._b == 8) {
  4. [Dòng 143] if (this._b == 18) {
  5. [Dòng 148] if (this._b == 12) { //SHB
  6. [Dòng 171] if (_re.status == '200'
  7. [Dòng 171] _re.status == '201') {
  8. [Dòng 180] } else if (this._res.state == 'authorization_required') {
  9. [Dòng 212] if (this.challengeCode == '') {
  10. [Dòng 308] if (this._b == 12) {
  11. [Dòng 356] if (this._b == 18) {//8-MB Bank;18-oceanbank

!== (1 điều kiện):
  1. [Dòng 186] codeResponse.toString() !== '0') {

!= (6 điều kiện):
  1. [Dòng 176] if (this._res.links != null
  2. [Dòng 176] this._res.links.merchant_return != null
  3. [Dòng 176] this._res.links.merchant_return.href != null) {
  4. [Dòng 351] if (!(_formCard.otp != null
  5. [Dòng 357] if (!(_formCard.password != null
  6. [Dòng 373] if (ua.indexOf('safari') != -1

================================================================================

📁 FILE 63: shb.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/shb/shb.component.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 3 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 25] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 42] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  3. [Dòng 42] _inExpDate.trim().length === 0)"

== (3 điều kiện):
  1. [Dòng 22] <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_card_number')">
  2. [Dòng 66] <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_account_number')">
  3. [Dòng 93] token_site == 'onepay'

================================================================================

📁 FILE 64: shb.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/shb/shb.component.ts
📊 Thống kê: 66 điều kiện duy nhất
   - === : 17 lần
   - == : 33 lần
   - !== : 5 lần
   - != : 11 lần
--------------------------------------------------------------------------------

=== (17 điều kiện):
  1. [Dòng 112] if (isIE[0] === 'MSIE'
  2. [Dòng 112] +isIE[1] === 10) {
  3. [Dòng 154] if (focusElement === 'card_name') {
  4. [Dòng 156] } else if (focusElement === 'exp_date'
  5. [Dòng 177] focusExpDateElement === 'card_name') {
  6. [Dòng 392] if (this.cardTypeBank === 'bank_account_number'
  7. [Dòng 437] if (valIn === this._translate.instant('bank_card_number')) {
  8. [Dòng 443] } else if (valIn === this._translate.instant('bank_account_number')) {
  9. [Dòng 485] if (_val.value === ''
  10. [Dòng 485] _val.value === null
  11. [Dòng 485] _val.value === undefined) {
  12. [Dòng 496] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  13. [Dòng 496] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  14. [Dòng 503] this.cardTypeOcean === 'MB') {
  15. [Dòng 511] this.cardTypeOcean === 'IB'
  16. [Dòng 517] if ((this.cardTypeBank === 'bank_card_number'
  17. [Dòng 540] if (this.cardTypeOcean === 'IB') {

== (33 điều kiện):
  1. [Dòng 108] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 121] if(this._b == 12) this.isShbGroup = true;
  3. [Dòng 142] return this._b == 9
  4. [Dòng 142] this._b == 11
  5. [Dòng 142] this._b == 16
  6. [Dòng 142] this._b == 17
  7. [Dòng 142] this._b == 25
  8. [Dòng 142] this._b == 44
  9. [Dòng 143] this._b == 57
  10. [Dòng 143] this._b == 59
  11. [Dòng 143] this._b == 61
  12. [Dòng 143] this._b == 63
  13. [Dòng 143] this._b == 69
  14. [Dòng 225] if (this._b == 12
  15. [Dòng 225] this.cardTypeBank == 'bank_account_number') {
  16. [Dòng 236] _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
  17. [Dòng 268] this.token_site == 'onepay'
  18. [Dòng 285] if (_re.status == '200'
  19. [Dòng 285] _re.status == '201') {
  20. [Dòng 289] if (this._res_post.state == 'approved'
  21. [Dòng 289] this._res_post.state == 'failed') {
  22. [Dòng 295] } else if (this._res_post.state == 'authorization_required') {
  23. [Dòng 395] if ((cardNo.length == 16
  24. [Dòng 395] cardNo.length == 19) && cardNo.startsWith("970443") && this._util.checkMod10(cardNo) && this.checkBin(cardNo) ) {
  25. [Dòng 407] if (this._b == +e.id) {
  26. [Dòng 423] if (valIn == 1) {
  27. [Dòng 425] } else if (valIn == 2) {
  28. [Dòng 496] this._b == 18))) {
  29. [Dòng 503] } else if (this._b == 18
  30. [Dòng 517] this._b == 18)) {
  31. [Dòng 529] this.c_expdate = !(((this.valueDate.length == 4
  32. [Dòng 529] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  33. [Dòng 529] this.valueDate.length == 5)

!== (5 điều kiện):
  1. [Dòng 273] key !== '3') {
  2. [Dòng 315] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  3. [Dòng 333] codeResponse.toString() !== '0') {
  4. [Dòng 392] cardNo.length !== 0) {
  5. [Dòng 517] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (11 điều kiện):
  1. [Dòng 129] if (this.htmlDesc != null
  2. [Dòng 190] if (ua.indexOf('safari') != -1
  3. [Dòng 236] if (_formCard.exp_date != null
  4. [Dòng 241] if (this.cardName != null
  5. [Dòng 292] if (this._res_post.links != null
  6. [Dòng 292] this._res_post.links.merchant_return != null
  7. [Dòng 292] this._res_post.links.merchant_return.href != null) {
  8. [Dòng 298] if (this._res_post.authorization != null
  9. [Dòng 298] this._res_post.authorization.links != null
  10. [Dòng 298] this._res_post.authorization.links.approval != null) {
  11. [Dòng 305] this._res_post.links.cancel != null) {

================================================================================

📁 FILE 65: techcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 66: techcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 67: techcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
📊 Thống kê: 17 điều kiện duy nhất
   - === : 1 lần
   - == : 12 lần
   - !== : 1 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 67] if (this.timeLeft === 0) {

== (12 điều kiện):
  1. [Dòng 58] if (this._b == 2
  2. [Dòng 58] this._b == 31
  3. [Dòng 58] this._b == 80) {
  4. [Dòng 95] if (this._b == 2) {
  5. [Dòng 97] } else if (this._b == 6) {
  6. [Dòng 99] } else if (this._b == 31) {
  7. [Dòng 101] } else if (this._b == 80) {
  8. [Dòng 131] if (_re.status == '200'
  9. [Dòng 131] _re.status == '201') {
  10. [Dòng 136] if (this._res_post.state == 'approved'
  11. [Dòng 136] this._res_post.state == 'failed') {
  12. [Dòng 140] } else if (this._res_post.state == 'authorization_required') {

!== (1 điều kiện):
  1. [Dòng 153] codeResponse.toString() !== '0') {

!= (3 điều kiện):
  1. [Dòng 144] if (this._res_post.authorization != null
  2. [Dòng 144] this._res_post.authorization.links != null
  3. [Dòng 144] this._res_post.authorization.links.approval != null) {

================================================================================

📁 FILE 68: vibbank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 3 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 28] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 42] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 42] valueDate.trim().length === 0)"

== (1 điều kiện):
  1. [Dòng 68] token_site == 'onepay'

================================================================================

📁 FILE 69: vibbank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 70: vibbank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.ts
📊 Thống kê: 155 điều kiện duy nhất
   - === : 48 lần
   - == : 73 lần
   - !== : 13 lần
   - != : 21 lần
--------------------------------------------------------------------------------

=== (48 điều kiện):
  1. [Dòng 129] if (isIE[0] === 'MSIE'
  2. [Dòng 129] +isIE[1] === 10) {
  3. [Dòng 173] if (this.timeLeft === 0) {
  4. [Dòng 230] if ((_val.value.substr(-1) === ' '
  5. [Dòng 230] _val.value.length === 24) {
  6. [Dòng 240] if (this.cardTypeBank === 'bank_card_number') {
  7. [Dòng 245] } else if (this.cardTypeBank === 'bank_account_number') {
  8. [Dòng 251] } else if (this.cardTypeBank === 'bank_username') {
  9. [Dòng 255] } else if (this.cardTypeBank === 'bank_customer_code') {
  10. [Dòng 261] this.cardTypeBank === 'bank_card_number'
  11. [Dòng 275] if (this.cardTypeOcean === 'IB') {
  12. [Dòng 279] } else if (this.cardTypeOcean === 'MB') {
  13. [Dòng 280] if (_val.value.substr(0, 2) === '84') {
  14. [Dòng 287] } else if (this.cardTypeOcean === 'ATM') {
  15. [Dòng 314] if (this.cardTypeBank === 'bank_account_number') {
  16. [Dòng 336] this.cardTypeBank === 'bank_card_number') {
  17. [Dòng 358] if (this.cardTypeBank === 'bank_card_number'
  18. [Dòng 358] if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  19. [Dòng 685] if (event.keyCode === 8
  20. [Dòng 685] event.key === "Backspace"
  21. [Dòng 725] if (v.length === 2
  22. [Dòng 725] this.flag.length === 3
  23. [Dòng 725] this.flag.charAt(this.flag.length - 1) === '/') {
  24. [Dòng 729] if (v.length === 1) {
  25. [Dòng 731] } else if (v.length === 2) {
  26. [Dòng 734] v.length === 2) {
  27. [Dòng 742] if (len === 2) {
  28. [Dòng 1012] if ((this.cardTypeBank === 'bank_account_number'
  29. [Dòng 1012] this.cardTypeBank === 'bank_username'
  30. [Dòng 1012] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  31. [Dòng 1017] this.cardTypeOcean === 'ATM')
  32. [Dòng 1018] || (this.cardTypeOcean === 'IB'
  33. [Dòng 1077] if (valIn === this._translate.instant('bank_card_number')) {
  34. [Dòng 1102] } else if (valIn === this._translate.instant('bank_account_number')) {
  35. [Dòng 1121] } else if (valIn === this._translate.instant('bank_username')) {
  36. [Dòng 1137] } else if (valIn === this._translate.instant('bank_customer_code')) {
  37. [Dòng 1226] if (_val.value === ''
  38. [Dòng 1226] _val.value === null
  39. [Dòng 1226] _val.value === undefined) {
  40. [Dòng 1237] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  41. [Dòng 1237] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  42. [Dòng 1244] this.cardTypeOcean === 'MB') {
  43. [Dòng 1252] this.cardTypeOcean === 'IB'
  44. [Dòng 1258] if ((this.cardTypeBank === 'bank_card_number'
  45. [Dòng 1292] if (this.cardName === undefined
  46. [Dòng 1292] this.cardName === '') {
  47. [Dòng 1300] if (this.valueDate === undefined
  48. [Dòng 1300] this.valueDate === '') {

== (73 điều kiện):
  1. [Dòng 124] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : false
  2. [Dòng 143] if (this._b == 18
  3. [Dòng 143] this._b == 19) {
  4. [Dòng 146] if (this._b == 19) {//19BIDV
  5. [Dòng 154] } else if (this._b == 3
  6. [Dòng 154] this._b == 27
  7. [Dòng 154] this._b == 5) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  8. [Dòng 160] if (this._b == 27
  9. [Dòng 160] this._b == 5) {
  10. [Dòng 181] } else if (this._b == 12) {// 12SHB
  11. [Dòng 186] } else if (this._b == 18) { //18Oceanbank-ocb
  12. [Dòng 239] if (this._b == 19
  13. [Dòng 239] this._b == 3
  14. [Dòng 239] this._b == 12
  15. [Dòng 274] } else if (this._b == 18) {
  16. [Dòng 305] if (this.checkBin(_val.value) && (this._b == 3
  17. [Dòng 305] this._b == 5)) {
  18. [Dòng 310] if (this._b == 3) {
  19. [Dòng 318] if (this._b == 5) {
  20. [Dòng 325] this.cardTypeOcean == 'ATM') {
  21. [Dòng 338] this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  22. [Dòng 358] this._b == 18)) {
  23. [Dòng 434] if (this.checkBin(v) && (this._b == 3
  24. [Dòng 685] event.inputType == 'deleteContentBackward') {
  25. [Dòng 686] if (event.target.name == 'exp_date'
  26. [Dòng 694] event.inputType == 'insertCompositionText') {
  27. [Dòng 709] if (((this.valueDate.length == 4
  28. [Dòng 709] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  29. [Dòng 709] this.valueDate.length == 5)
  30. [Dòng 789] if (temp.length == 0) {
  31. [Dòng 796] return (counter % 10 == 0);
  32. [Dòng 816] } else if (this._b == 19) {
  33. [Dòng 818] } else if (this._b == 27) {
  34. [Dòng 820] } else if (this._b == 5) {
  35. [Dòng 825] if (this._b == 12) {
  36. [Dòng 827] if (this.cardTypeBank == 'bank_customer_code') {
  37. [Dòng 829] } else if (this.cardTypeBank == 'bank_account_number') {
  38. [Dòng 844] _formCard.exp_date.length == 5
  39. [Dòng 844] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
  40. [Dòng 844] this._b == 5)) {//27-pvcombank;3-TPB ;
  41. [Dòng 849] if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
  42. [Dòng 849] this._b == 19
  43. [Dòng 849] this._b == 5)) {//3-TPB ;19-BIDV;27-pvcombank;
  44. [Dòng 852] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  45. [Dòng 855] if (this.cardTypeOcean == 'IB') {
  46. [Dòng 857] } else if (this.cardTypeOcean == 'MB') {
  47. [Dòng 859] } else if (this.cardTypeOcean == 'ATM') {
  48. [Dòng 888] this.token_site == 'onepay'
  49. [Dòng 907] if (_re.status == '200'
  50. [Dòng 907] _re.status == '201') {
  51. [Dòng 912] if (this._res_post.state == 'approved'
  52. [Dòng 912] this._res_post.state == 'failed') {
  53. [Dòng 919] } else if (this._res_post.state == 'authorization_required') {
  54. [Dòng 937] if (this._b == 18) {
  55. [Dòng 942] this._b == 18
  56. [Dòng 1032] if ((cardNo.length == 16
  57. [Dòng 1032] if ((cardNo.length == 16 || (cardNo.length == 19
  58. [Dòng 1033] && ((this._b == 18
  59. [Dòng 1033] cardNo.length == 19) || this._b != 18)
  60. [Dòng 1046] if (this._b == +e.id) {
  61. [Dòng 1062] if (valIn == 1) {
  62. [Dòng 1064] } else if (valIn == 2) {
  63. [Dòng 1095] if (this._b == 19) {
  64. [Dòng 1116] this._b == 3) {
  65. [Dòng 1159] if (cardType == this._translate.instant('internetbanking')
  66. [Dòng 1167] } else if (cardType == this._translate.instant('mobilebanking')
  67. [Dòng 1175] } else if (cardType == this._translate.instant('atm')
  68. [Dòng 1237] this._b == 18))) {
  69. [Dòng 1244] } else if (this._b == 18
  70. [Dòng 1270] this.c_expdate = !(((this.valueDate.length == 4
  71. [Dòng 1303] this.valueDate.length == 4
  72. [Dòng 1303] this.valueDate.search('/') == -1)
  73. [Dòng 1304] this.valueDate.length == 5))

!== (13 điều kiện):
  1. [Dòng 230] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 893] key !== '3') {
  3. [Dòng 943] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 961] codeResponse.toString() !== '0') {
  5. [Dòng 1012] cardNo.length !== 0) {
  6. [Dòng 1084] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 1105] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 1126] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 1146] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 1159] this.lb_card_account !== this._translate.instant('ocb_account')) {
  11. [Dòng 1167] this.lb_card_account !== this._translate.instant('ocb_phone')) {
  12. [Dòng 1175] this.lb_card_account !== this._translate.instant('ocb_card')) {
  13. [Dòng 1258] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (21 điều kiện):
  1. [Dòng 192] } else if (this._b != 18) {
  2. [Dòng 198] if (this.htmlDesc != null
  3. [Dòng 227] if (ua.indexOf('safari') != -1
  4. [Dòng 237] if (_val.value != '') {
  5. [Dòng 326] this.auth_method != null) {
  6. [Dòng 687] if (this.valueDate.length != 3) {
  7. [Dòng 844] if (_formCard.exp_date != null
  8. [Dòng 849] if (this.cardName != null
  9. [Dòng 915] if (this._res_post.links != null
  10. [Dòng 915] this._res_post.links.merchant_return != null
  11. [Dòng 915] this._res_post.links.merchant_return.href != null) {
  12. [Dòng 923] if (this._res_post.authorization != null
  13. [Dòng 923] this._res_post.authorization.links != null
  14. [Dòng 923] this._res_post.authorization.links.approval != null) {
  15. [Dòng 930] this._res_post.links.cancel != null) {
  16. [Dòng 1032] this._b != 27
  17. [Dòng 1032] this._b != 12
  18. [Dòng 1032] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  19. [Dòng 1033] this._b != 18)
  20. [Dòng 1079] if (this._b != 18
  21. [Dòng 1079] this._b != 19) {

================================================================================

📁 FILE 71: vietcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
📊 Thống kê: 11 điều kiện duy nhất
   - === : 2 lần
   - == : 2 lần
   - !== : 1 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 29] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  2. [Dòng 29] _inExpDate.trim().length === 0)"

== (2 điều kiện):
  1. [Dòng 58] token_site == 'onepay'
  2. [Dòng 68] _b==68"

!== (1 điều kiện):
  1. [Dòng 71] show_condition && _b !== 0

!= (6 điều kiện):
  1. [Dòng 25] d_card_date && (_b != 3 && _b != 19 && _b != 12 && _b != 18)
  2. [Dòng 25] _b != 18)">
  3. [Dòng 38] *ngIf="(_b != 12
  4. [Dòng 38] (_b != 12 && _b != 18 && _b != 6 && _b != 2 && _b != 5 && _b != 48 && _b != 67 && _b != 57)
  5. [Dòng 38] _b != 57)">
  6. [Dòng 83] _b != 6 && _b != 2 && _b != 5 && _b != 48 && _b != 67 && _b != 57

================================================================================

📁 FILE 72: vietcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 73: vietcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
📊 Thống kê: 122 điều kiện duy nhất
   - === : 4 lần
   - == : 80 lần
   - !== : 2 lần
   - != : 36 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 420] if (event.keyCode === 8
  2. [Dòng 420] event.key === "Backspace"
  3. [Dòng 676] if (approval.method === 'REDIRECT') {
  4. [Dòng 679] } else if (approval.method === 'POST_REDIRECT') {

== (80 điều kiện):
  1. [Dòng 140] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 169] if (this._b == 1
  3. [Dòng 169] this._b == 20
  4. [Dòng 169] this._b == 36
  5. [Dòng 169] this._b == 64
  6. [Dòng 169] this._b == 55
  7. [Dòng 169] this._b == 47
  8. [Dòng 169] this._b == 48
  9. [Dòng 169] this._b == 59) {
  10. [Dòng 180] if (message == '1') {
  11. [Dòng 191] return this._b == 3
  12. [Dòng 191] this._b == 9
  13. [Dòng 191] this._b == 16
  14. [Dòng 191] this._b == 17
  15. [Dòng 191] this._b == 19
  16. [Dòng 191] this._b == 25
  17. [Dòng 191] this._b == 44
  18. [Dòng 192] this._b == 57
  19. [Dòng 192] this._b == 59
  20. [Dòng 192] this._b == 61
  21. [Dòng 192] this._b == 63
  22. [Dòng 192] this._b == 69
  23. [Dòng 196] return this._b == 6
  24. [Dòng 196] this._b == 2
  25. [Dòng 196] this._b == 5
  26. [Dòng 196] this._b == 67
  27. [Dòng 200] return this._b == 12
  28. [Dòng 200] this._b == 18
  29. [Dòng 204] return this._b == 11
  30. [Dòng 204] this._b == 73
  31. [Dòng 204] this._b == 33
  32. [Dòng 204] this._b == 39
  33. [Dòng 204] this._b == 43
  34. [Dòng 204] this._b == 45
  35. [Dòng 204] this._b == 68
  36. [Dòng 204] this._b == 72
  37. [Dòng 204] this._b == 74
  38. [Dòng 204] this._b == 75
  39. [Dòng 328] if (_re.status == '200'
  40. [Dòng 328] _re.status == '201') {
  41. [Dòng 329] _re.body.state == 'success')
  42. [Dòng 330] _re.body.data.discount_amount == 0))) {
  43. [Dòng 351] _re.body.state == 'fail') || _re.body.data.discount_amount <= 0) {
  44. [Dòng 420] event.inputType == 'deleteContentBackward') {
  45. [Dòng 421] if (event.target.name == 'exp_date'
  46. [Dòng 429] event.inputType == 'insertCompositionText') {
  47. [Dòng 509] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  48. [Dòng 520] } else if (this._b == 2) {
  49. [Dòng 522] } else if (this._b == 5) {
  50. [Dòng 524] } else if (this._b == 6) {
  51. [Dòng 526] } else if (this._b == 31) {
  52. [Dòng 549] this.token_site == 'onepay'
  53. [Dòng 566] if (this._res_post.state == 'approved'
  54. [Dòng 566] this._res_post.state == 'failed') {
  55. [Dòng 616] } else if (this._res_post.state == 'authorization_required') {
  56. [Dòng 638] this._b == 14
  57. [Dòng 638] this._b == 15
  58. [Dòng 638] this._b == 24
  59. [Dòng 638] this._b == 8
  60. [Dòng 638] this._b == 10
  61. [Dòng 638] this._b == 22
  62. [Dòng 638] this._b == 23
  63. [Dòng 638] this._b == 27
  64. [Dòng 638] this._b == 30
  65. [Dòng 638] this._b == 11
  66. [Dòng 638] this._b == 32
  67. [Dòng 638] this._b == 9) {
  68. [Dòng 716] if ((cardNo.length == 16
  69. [Dòng 717] (cardNo.length == 19
  70. [Dòng 717] (cardNo.length == 19 && (this._b == 1
  71. [Dòng 717] this._b == 4
  72. [Dòng 717] this._b == 5))
  73. [Dòng 719] this._util.checkMod10(cardNo) == true
  74. [Dòng 806] return ((value.length == 4
  75. [Dòng 806] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  76. [Dòng 806] value.length == 5) && parseInt(value.split('/')[0]
  77. [Dòng 838] this._inExpDate.length == 4
  78. [Dòng 838] this._inExpDate.search('/') == -1)
  79. [Dòng 839] this._inExpDate.length == 5))
  80. [Dòng 897] result.data == 'approved') {

!== (2 điều kiện):
  1. [Dòng 579] codeResponse.toString() !== '0') {
  2. [Dòng 639] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (36 điều kiện):
  1. [Dòng 139] this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
  2. [Dòng 174] if (this.htmlDesc != null
  3. [Dòng 217] if (ua.indexOf('safari') != -1
  4. [Dòng 422] if (this._inExpDate.length != 3) {
  5. [Dòng 503] if (this._b != 3
  6. [Dòng 503] this._b != 9
  7. [Dòng 503] this._b != 12
  8. [Dòng 503] this._b != 16
  9. [Dòng 503] this._b != 17
  10. [Dòng 503] this._b != 18
  11. [Dòng 503] this._b != 19
  12. [Dòng 503] this._b != 25
  13. [Dòng 503] this._b != 44
  14. [Dòng 504] this._b != 57
  15. [Dòng 504] this._b != 59
  16. [Dòng 504] this._b != 61
  17. [Dòng 504] this._b != 63
  18. [Dòng 504] this._b != 69
  19. [Dòng 504] this._b != 6
  20. [Dòng 504] this._b != 2
  21. [Dòng 504] this._b != 5
  22. [Dòng 504] this._b != 48
  23. [Dòng 504] this._b != 67
  24. [Dòng 504] this._b != 57) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
  25. [Dòng 518] '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '73','74','75'].indexOf(this._b.toString()) != -1) {
  26. [Dòng 568] if (this._res_post.return_url != null) {
  27. [Dòng 571] if (this._res_post.links != null
  28. [Dòng 571] this._res_post.links.merchant_return != null
  29. [Dòng 571] this._res_post.links.merchant_return.href != null) {
  30. [Dòng 621] if (this._res_post.authorization != null
  31. [Dòng 621] this._res_post.authorization.links != null
  32. [Dòng 626] this._res_post.links.cancel != null) {
  33. [Dòng 632] let userName = _formCard.name != null ? _formCard.name : ''
  34. [Dòng 633] this._res_post.authorization.links.approval != null
  35. [Dòng 633] this._res_post.authorization.links.approval.href != null) {
  36. [Dòng 636] userName = paramUserName != null ? paramUserName : ''

================================================================================

📁 FILE 74: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 75: off-bank-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 76: off-bank-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 77: policy-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/dialog/policy-dialog/policy-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 78: policy-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/dialog/policy-dialog/policy-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 79: promotion-apply-failed-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/dialog/promotion-apply-failed-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 80: domescard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/domescard-main.component.html
📊 Thống kê: 12 điều kiện duy nhất
   - === : 1 lần
   - == : 11 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 15] filteredData.length === 0"

== (11 điều kiện):
  1. [Dòng 22] _auth==1) || (token
  2. [Dòng 22] _b == 16)"
  3. [Dòng 26] (token || _auth==1) && _b != 16
  4. [Dòng 29] _auth==1)">
  5. [Dòng 47] ((!token && _auth==0 && vietcombankGroupSelected) || (token && _b==16))
  6. [Dòng 47] _b==16))">
  7. [Dòng 51] !token && _auth==0 && techcombankGroupSelected
  8. [Dòng 55] !token && _auth==0 && shbGroupSelected
  9. [Dòng 60] !token && _auth==0 && onepaynapasGroupSelected
  10. [Dòng 66] !token && _auth==0 && bankaccountGroupSelected
  11. [Dòng 70] !token && _auth==0 && vibbankGroupSelected

================================================================================

📁 FILE 81: domescard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main/domescard-main.component.ts
📊 Thống kê: 123 điều kiện duy nhất
   - === : 21 lần
   - == : 95 lần
   - !== : 1 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (21 điều kiện):
  1. [Dòng 291] if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
  2. [Dòng 292] filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
  3. [Dòng 336] if (valOut === 'auth') {
  4. [Dòng 502] if (this._b === '1'
  5. [Dòng 502] this._b === '20'
  6. [Dòng 502] this._b === '64') {
  7. [Dòng 505] if (this._b === '36'
  8. [Dòng 505] this._b === '18'
  9. [Dòng 505] this._b === '19'
  10. [Dòng 508] if (this._b === '19'
  11. [Dòng 508] this._b === '16'
  12. [Dòng 508] this._b === '25'
  13. [Dòng 508] this._b === '33'
  14. [Dòng 509] this._b === '39'
  15. [Dòng 509] this._b === '11'
  16. [Dòng 509] this._b === '17'
  17. [Dòng 510] this._b === '36'
  18. [Dòng 510] this._b === '44'
  19. [Dòng 511] this._b === '64'
  20. [Dòng 514] if (this._b === '20'
  21. [Dòng 517] if (this._b === '18') {

== (95 điều kiện):
  1. [Dòng 233] if (e.id == '31') {
  2. [Dòng 235] } else if (e.id == '80') {
  3. [Dòng 277] this.filteredData.length == 1
  4. [Dòng 314] if ($event && ($event == 'true'
  5. [Dòng 419] if (bankId == 1
  6. [Dòng 419] bankId == 4
  7. [Dòng 419] bankId == 7
  8. [Dòng 419] bankId == 8
  9. [Dòng 419] bankId == 9
  10. [Dòng 419] bankId == 10
  11. [Dòng 419] bankId == 11
  12. [Dòng 419] bankId == 14
  13. [Dòng 419] bankId == 15
  14. [Dòng 420] bankId == 16
  15. [Dòng 420] bankId == 17
  16. [Dòng 420] bankId == 20
  17. [Dòng 420] bankId == 22
  18. [Dòng 420] bankId == 23
  19. [Dòng 420] bankId == 24
  20. [Dòng 420] bankId == 25
  21. [Dòng 420] bankId == 30
  22. [Dòng 420] bankId == 33
  23. [Dòng 421] bankId == 34
  24. [Dòng 421] bankId == 35
  25. [Dòng 421] bankId == 36
  26. [Dòng 421] bankId == 37
  27. [Dòng 421] bankId == 38
  28. [Dòng 421] bankId == 39
  29. [Dòng 421] bankId == 40
  30. [Dòng 421] bankId == 41
  31. [Dòng 421] bankId == 42
  32. [Dòng 422] bankId == 43
  33. [Dòng 422] bankId == 44
  34. [Dòng 422] bankId == 45
  35. [Dòng 422] bankId == 46
  36. [Dòng 422] bankId == 47
  37. [Dòng 422] bankId == 48
  38. [Dòng 422] bankId == 49
  39. [Dòng 422] bankId == 50
  40. [Dòng 422] bankId == 51
  41. [Dòng 423] bankId == 52
  42. [Dòng 423] bankId == 53
  43. [Dòng 423] bankId == 54
  44. [Dòng 423] bankId == 55
  45. [Dòng 423] bankId == 56
  46. [Dòng 423] bankId == 57
  47. [Dòng 423] bankId == 58
  48. [Dòng 423] bankId == 59
  49. [Dòng 423] bankId == 60
  50. [Dòng 424] bankId == 61
  51. [Dòng 424] bankId == 62
  52. [Dòng 424] bankId == 63
  53. [Dòng 424] bankId == 64
  54. [Dòng 424] bankId == 65
  55. [Dòng 424] bankId == 66
  56. [Dòng 424] bankId == 68
  57. [Dòng 424] bankId == 69
  58. [Dòng 424] bankId == 70
  59. [Dòng 425] bankId == 71
  60. [Dòng 425] bankId == 72
  61. [Dòng 425] bankId == 73
  62. [Dòng 425] bankId == 32
  63. [Dòng 425] bankId == 74
  64. [Dòng 425] bankId == 75) {
  65. [Dòng 427] } else if (bankId == 6
  66. [Dòng 427] bankId == 31
  67. [Dòng 427] bankId == 80) {
  68. [Dòng 429] } else if (bankId == 2
  69. [Dòng 429] bankId == 67) {
  70. [Dòng 431] } else if (bankId == 3
  71. [Dòng 431] bankId == 18
  72. [Dòng 431] bankId == 19
  73. [Dòng 431] bankId == 27) {
  74. [Dòng 433] } else if (bankId == 5) {
  75. [Dòng 435] } else if (bankId == 12) {
  76. [Dòng 505] this._b == '55'
  77. [Dòng 505] this._b == '47'
  78. [Dòng 505] this._b == '48'
  79. [Dòng 505] this._b == '59'
  80. [Dòng 505] this._b == '73'
  81. [Dòng 505] this._b == '12') {
  82. [Dòng 508] this._b == '3'
  83. [Dòng 509] this._b == '43'
  84. [Dòng 509] this._b == '45'
  85. [Dòng 510] this._b == '57'
  86. [Dòng 511] this._b == '61'
  87. [Dòng 511] this._b == '63'
  88. [Dòng 511] this._b == '67'
  89. [Dòng 511] this._b == '68'
  90. [Dòng 511] this._b == '69'
  91. [Dòng 511] this._b == '9'
  92. [Dòng 511] this._b == '74'
  93. [Dòng 511] this._b == '75') {
  94. [Dòng 514] this._b == '36'
  95. [Dòng 534] if (item['id'] == this._b) {

!== (1 điều kiện):
  1. [Dòng 120] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (6 điều kiện):
  1. [Dòng 172] if (params['locale'] != null) {
  2. [Dòng 178] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 207] if (!(strInstrument != null
  4. [Dòng 210] if (strInstrument.substring(0, 1) != '^'
  5. [Dòng 210] strInstrument.substr(strInstrument.length - 1) != '$') {
  6. [Dòng 363] if (bankid != null) {

================================================================================

📁 FILE 82: bankaccount-promotion.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/bankaccount-promotion/bankaccount-promotion.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 3 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 40] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 55] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 55] valueDate.trim().length === 0)"

== (1 điều kiện):
  1. [Dòng 91] token_site == 'onepay'

================================================================================

📁 FILE 83: bankaccount-promotion.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/bankaccount-promotion/bankaccount-promotion.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 84: bankaccount-promotion.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/bankaccount-promotion/bankaccount-promotion.component.ts
📊 Thống kê: 164 điều kiện duy nhất
   - === : 47 lần
   - == : 83 lần
   - !== : 13 lần
   - != : 21 lần
--------------------------------------------------------------------------------

=== (47 điều kiện):
  1. [Dòng 145] if (isIE[0] === 'MSIE'
  2. [Dòng 145] +isIE[1] === 10) {
  3. [Dòng 237] if ((_val.value.substr(-1) === ' '
  4. [Dòng 237] _val.value.length === 24) {
  5. [Dòng 247] if (this.cardTypeBank === 'bank_card_number') {
  6. [Dòng 252] } else if (this.cardTypeBank === 'bank_account_number') {
  7. [Dòng 258] } else if (this.cardTypeBank === 'bank_username') {
  8. [Dòng 262] } else if (this.cardTypeBank === 'bank_customer_code') {
  9. [Dòng 268] this.cardTypeBank === 'bank_card_number'
  10. [Dòng 282] if (this.cardTypeOcean === 'IB') {
  11. [Dòng 286] } else if (this.cardTypeOcean === 'MB') {
  12. [Dòng 287] if (_val.value.substr(0, 2) === '84') {
  13. [Dòng 294] } else if (this.cardTypeOcean === 'ATM') {
  14. [Dòng 321] if (this.cardTypeBank === 'bank_account_number') {
  15. [Dòng 334] this.cardTypeBank === 'bank_card_number') {
  16. [Dòng 344] if (this.cardTypeBank === 'bank_card_number'
  17. [Dòng 344] if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  18. [Dòng 618] if (event.keyCode === 8
  19. [Dòng 618] event.key === "Backspace"
  20. [Dòng 658] if (v.length === 2
  21. [Dòng 658] this.flag.length === 3
  22. [Dòng 658] this.flag.charAt(this.flag.length - 1) === '/') {
  23. [Dòng 662] if (v.length === 1) {
  24. [Dòng 664] } else if (v.length === 2) {
  25. [Dòng 667] v.length === 2) {
  26. [Dòng 675] if (len === 2) {
  27. [Dòng 957] if ((this.cardTypeBank === 'bank_account_number'
  28. [Dòng 957] this.cardTypeBank === 'bank_username'
  29. [Dòng 957] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  30. [Dòng 962] this.cardTypeOcean === 'ATM')
  31. [Dòng 963] || (this.cardTypeOcean === 'IB'
  32. [Dòng 1022] if (valIn === this._translate.instant('bank_card_number')) {
  33. [Dòng 1047] } else if (valIn === this._translate.instant('bank_account_number')) {
  34. [Dòng 1066] } else if (valIn === this._translate.instant('bank_username')) {
  35. [Dòng 1082] } else if (valIn === this._translate.instant('bank_customer_code')) {
  36. [Dòng 1361] if (_val.value === ''
  37. [Dòng 1361] _val.value === null
  38. [Dòng 1361] _val.value === undefined) {
  39. [Dòng 1370] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  40. [Dòng 1370] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  41. [Dòng 1377] this.cardTypeOcean === 'MB') {
  42. [Dòng 1385] this.cardTypeOcean === 'IB'
  43. [Dòng 1391] if ((this.cardTypeBank === 'bank_card_number'
  44. [Dòng 1423] if (this.cardName === undefined
  45. [Dòng 1423] this.cardName === '') {
  46. [Dòng 1431] if (this.valueDate === undefined
  47. [Dòng 1431] this.valueDate === '') {

== (83 điều kiện):
  1. [Dòng 141] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 166] if (this._b == 18
  3. [Dòng 166] this._b == 19) {
  4. [Dòng 169] if (this._b == 19) {//19BIDV
  5. [Dòng 177] } else if (this._b == 3
  6. [Dòng 177] this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  7. [Dòng 182] if (this._b == 27) {
  8. [Dòng 187] } else if (this._b == 12) {// 12SHB
  9. [Dòng 192] } else if (this._b == 18) { //18Oceanbank-ocb
  10. [Dòng 246] if (this._b == 19
  11. [Dòng 246] this._b == 3
  12. [Dòng 246] this._b == 27
  13. [Dòng 246] this._b == 12) {
  14. [Dòng 281] } else if (this._b == 18) {
  15. [Dòng 312] if (this.checkBin(_val.value) && (this._b == 3
  16. [Dòng 312] this._b == 27)) {
  17. [Dòng 317] if (this._b == 3) {
  18. [Dòng 329] this.cardTypeOcean == 'ATM') {
  19. [Dòng 336] if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  20. [Dòng 344] this._b == 18)) {
  21. [Dòng 414] if (this.checkBin(v) && (this._b == 3
  22. [Dòng 618] event.inputType == 'deleteContentBackward') {
  23. [Dòng 619] if (event.target.name == 'exp_date'
  24. [Dòng 627] event.inputType == 'insertCompositionText') {
  25. [Dòng 642] if (((this.valueDate.length == 4
  26. [Dòng 642] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  27. [Dòng 642] this.valueDate.length == 5)
  28. [Dòng 722] if (temp.length == 0) {
  29. [Dòng 729] return (counter % 10 == 0);
  30. [Dòng 749] } else if (this._b == 19) {
  31. [Dòng 751] } else if (this._b == 27) {
  32. [Dòng 756] if (this._b == 12) {
  33. [Dòng 758] if (this.cardTypeBank == 'bank_customer_code') {
  34. [Dòng 760] } else if (this.cardTypeBank == 'bank_account_number') {
  35. [Dòng 777] _formCard.exp_date.length == 5
  36. [Dòng 777] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
  37. [Dòng 777] this._b == 3)) {//27-pvcombank;3-TPB ;
  38. [Dòng 782] if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
  39. [Dòng 782] this._b == 19
  40. [Dòng 782] this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
  41. [Dòng 785] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  42. [Dòng 788] if (this.cardTypeOcean == 'IB') {
  43. [Dòng 790] } else if (this.cardTypeOcean == 'MB') {
  44. [Dòng 792] } else if (this.cardTypeOcean == 'ATM') {
  45. [Dòng 822] this.token_site == 'onepay'
  46. [Dòng 837] type == 'tpbank_account'
  47. [Dòng 837] type == 'bidv_account'
  48. [Dòng 837] type == 'pvcombank_account'
  49. [Dòng 837] type == 'shb_account'
  50. [Dòng 837] type == 'oceanbank_online_account'
  51. [Dòng 837] type == 'oceanbank_mobile_account') {
  52. [Dòng 850] if (_re.status == '200'
  53. [Dòng 850] _re.status == '201') {
  54. [Dòng 855] if (this._res_post.state == 'approved'
  55. [Dòng 855] this._res_post.state == 'failed') {
  56. [Dòng 862] } else if (this._res_post.state == 'authorization_required') {
  57. [Dòng 880] if (this._b == 18) {
  58. [Dòng 885] if (this._b == 27
  59. [Dòng 885] this._b == 18) {
  60. [Dòng 977] if ((cardNo.length == 16
  61. [Dòng 977] if ((cardNo.length == 16 || (cardNo.length == 19
  62. [Dòng 978] && ((this._b == 18
  63. [Dòng 978] cardNo.length == 19) || this._b != 18)
  64. [Dòng 991] if (this._b == +e.id) {
  65. [Dòng 1007] if (valIn == 1) {
  66. [Dòng 1009] } else if (valIn == 2) {
  67. [Dòng 1033] this._b == 3) {
  68. [Dòng 1040] if (this._b == 19) {
  69. [Dòng 1103] if (cardType == this._translate.instant('internetbanking')
  70. [Dòng 1111] } else if (cardType == this._translate.instant('mobilebanking')
  71. [Dòng 1119] } else if (cardType == this._translate.instant('atm')
  72. [Dòng 1195] _re.body.state == 'success')
  73. [Dòng 1196] _re.body.data.discount_amount == 0))) {
  74. [Dòng 1216] _re.body.state == 'fail') || _re.body.data.discount_amount <= 0) {
  75. [Dòng 1260] _re.body.state == 'success') {
  76. [Dòng 1277] _re.body.state == 'fail') {
  77. [Dòng 1316] result.data == 'approved') {
  78. [Dòng 1370] this._b == 18))) {
  79. [Dòng 1377] } else if (this._b == 18
  80. [Dòng 1402] this.c_expdate = !(((this.valueDate.length == 4
  81. [Dòng 1434] this.valueDate.length == 4
  82. [Dòng 1434] this.valueDate.search('/') == -1)
  83. [Dòng 1435] this.valueDate.length == 5))

!== (13 điều kiện):
  1. [Dòng 237] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 827] key !== '3') {
  3. [Dòng 886] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 904] codeResponse.toString() !== '0') {
  5. [Dòng 957] cardNo.length !== 0) {
  6. [Dòng 1029] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 1050] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 1071] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 1091] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 1103] this.lb_card_account !== this._translate.instant('ocb_account')) {
  11. [Dòng 1111] this.lb_card_account !== this._translate.instant('ocb_phone')) {
  12. [Dòng 1119] this.lb_card_account !== this._translate.instant('ocb_card')) {
  13. [Dòng 1391] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (21 điều kiện):
  1. [Dòng 198] } else if (this._b != 18) {
  2. [Dòng 204] if (this.htmlDesc != null
  3. [Dòng 234] if (ua.indexOf('safari') != -1
  4. [Dòng 244] if (_val.value != '') {
  5. [Dòng 330] this.auth_method != null) {
  6. [Dòng 620] if (this.valueDate.length != 3) {
  7. [Dòng 777] if (_formCard.exp_date != null
  8. [Dòng 782] if (this.cardName != null
  9. [Dòng 858] if (this._res_post.links != null
  10. [Dòng 858] this._res_post.links.merchant_return != null
  11. [Dòng 858] this._res_post.links.merchant_return.href != null) {
  12. [Dòng 866] if (this._res_post.authorization != null
  13. [Dòng 866] this._res_post.authorization.links != null
  14. [Dòng 866] this._res_post.authorization.links.approval != null) {
  15. [Dòng 873] this._res_post.links.cancel != null) {
  16. [Dòng 977] this._b != 27
  17. [Dòng 977] this._b != 12
  18. [Dòng 977] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  19. [Dòng 978] this._b != 18)
  20. [Dòng 1024] if (this._b != 18
  21. [Dòng 1024] this._b != 19) {

================================================================================

📁 FILE 85: bank.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/model/bank.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 86: otp-auth.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/otp-auth/otp-auth.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 87: otp-auth.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/otp-auth/otp-auth.component.ts
📊 Thống kê: 15 điều kiện duy nhất
   - === : 0 lần
   - == : 8 lần
   - !== : 1 lần
   - != : 6 lần
--------------------------------------------------------------------------------

== (8 điều kiện):
  1. [Dòng 93] if (this._b == 8) {//MB Bank
  2. [Dòng 97] if (this._b == 18) {//Oceanbank
  3. [Dòng 131] if (this._b == 8) {
  4. [Dòng 136] if (this._b == 18) {
  5. [Dòng 160] if (_re.status == '200'
  6. [Dòng 160] _re.status == '201') {
  7. [Dòng 169] } else if (this._res.state == 'authorization_required') {
  8. [Dòng 320] if (this._b == 18) {//8-MB Bank;18-oceanbank

!== (1 điều kiện):
  1. [Dòng 175] codeResponse.toString() !== '0') {

!= (6 điều kiện):
  1. [Dòng 165] if (this._res.links != null
  2. [Dòng 165] this._res.links.merchant_return != null
  3. [Dòng 165] this._res.links.merchant_return.href != null) {
  4. [Dòng 315] if (!(_formCard.otp != null
  5. [Dòng 321] if (!(_formCard.password != null
  6. [Dòng 337] if (ua.indexOf('safari') != -1

================================================================================

📁 FILE 88: techcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/techcombank/techcombank.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 11] token_site == 'onepay'

================================================================================

📁 FILE 89: techcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/techcombank/techcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 90: techcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/techcombank/techcombank.component.ts
📊 Thống kê: 24 điều kiện duy nhất
   - === : 1 lần
   - == : 20 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 113] if (this.timeLeft === 0) {

== (20 điều kiện):
  1. [Dòng 91] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 104] if (this._b == 2
  3. [Dòng 104] this._b == 5
  4. [Dòng 104] this._b == 31
  5. [Dòng 104] this._b == 80) {
  6. [Dòng 145] if (this._b == 2) {
  7. [Dòng 147] } else if (this._b == 5) {
  8. [Dòng 149] } else if (this._b == 6) {
  9. [Dòng 151] } else if (this._b == 31) {
  10. [Dòng 153] } else if (this._b == 80) {
  11. [Dòng 178] this.token_site == 'onepay'
  12. [Dòng 203] if (_re.status == '200'
  13. [Dòng 203] _re.status == '201') {
  14. [Dòng 204] _re.body.state == 'success')
  15. [Dòng 205] _re.body.data.discount_amount == 0))) {
  16. [Dòng 223] _re.body.state == 'fail') || (!_re.body.data.discount_accept_zero
  17. [Dòng 267] result.data == 'approved') {
  18. [Dòng 284] if (this._res_post.state == 'approved'
  19. [Dòng 284] this._res_post.state == 'failed') {
  20. [Dòng 288] } else if (this._res_post.state == 'authorization_required') {

!= (3 điều kiện):
  1. [Dòng 292] if (this._res_post.authorization != null
  2. [Dòng 292] this._res_post.authorization.links != null
  3. [Dòng 292] this._res_post.authorization.links.approval != null) {

================================================================================

📁 FILE 91: vietcombank-promotion.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/vietcombank-promotion/vietcombank-promotion.component.html
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 25] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  2. [Dòng 25] _inExpDate.trim().length === 0)"

== (1 điều kiện):
  1. [Dòng 54] token_site == 'onepay'

!= (6 điều kiện):
  1. [Dòng 21] d_card_date && (_b != 3 && _b != 19 && _b != 18)
  2. [Dòng 21] _b != 18)">
  3. [Dòng 34] *ngIf="(_b != 18
  4. [Dòng 34] (_b != 18 && _b != 6 && _b != 2 && _b != 57)
  5. [Dòng 34] _b != 57)">
  6. [Dòng 76] _b != 6 && _b != 2 && _b != 67 && _b != 57

================================================================================

📁 FILE 92: vietcombank-promotion.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/vietcombank-promotion/vietcombank-promotion.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 93: vietcombank-promotion.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/banks/vietcombank-promotion/vietcombank-promotion.component.ts
📊 Thống kê: 120 điều kiện duy nhất
   - === : 4 lần
   - == : 81 lần
   - !== : 2 lần
   - != : 33 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 465] if (event.keyCode === 8
  2. [Dòng 465] event.key === "Backspace"
  3. [Dòng 723] if (approval.method === 'REDIRECT') {
  4. [Dòng 726] } else if (approval.method === 'POST_REDIRECT') {

== (81 điều kiện):
  1. [Dòng 149] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 174] if (this._b == 1
  3. [Dòng 174] this._b == 20
  4. [Dòng 174] this._b == 36
  5. [Dòng 174] this._b == 64
  6. [Dòng 174] this._b == 55
  7. [Dòng 174] this._b == 47
  8. [Dòng 174] this._b == 48
  9. [Dòng 174] this._b == 59) {
  10. [Dòng 185] if (message == '1') {
  11. [Dòng 196] return this._b == 3
  12. [Dòng 196] this._b == 9
  13. [Dòng 196] this._b == 16
  14. [Dòng 196] this._b == 17
  15. [Dòng 196] this._b == 19
  16. [Dòng 196] this._b == 25
  17. [Dòng 196] this._b == 44
  18. [Dòng 197] this._b == 57
  19. [Dòng 197] this._b == 59
  20. [Dòng 197] this._b == 61
  21. [Dòng 197] this._b == 63
  22. [Dòng 197] this._b == 68
  23. [Dòng 197] this._b == 69
  24. [Dòng 201] return this._b == 6
  25. [Dòng 201] this._b == 2
  26. [Dòng 205] return this._b == 18
  27. [Dòng 209] return this._b == 11
  28. [Dòng 209] this._b == 73
  29. [Dòng 209] this._b == 33
  30. [Dòng 209] this._b == 39
  31. [Dòng 209] this._b == 43
  32. [Dòng 209] this._b == 45
  33. [Dòng 209] this._b == 67
  34. [Dòng 209] this._b == 72
  35. [Dòng 209] this._b == 74
  36. [Dòng 209] this._b == 75
  37. [Dòng 245] if (parseInt(b) == parseInt(v.substring(0, 6))) {
  38. [Dòng 321] } else if (this._b == 67) {
  39. [Dòng 364] if (_re.status == '200'
  40. [Dòng 364] _re.status == '201') {
  41. [Dòng 365] _re.body.state == 'success')
  42. [Dòng 366] _re.body.data.discount_amount == 0))) {
  43. [Dòng 392] _re.body.state == 'fail') || _re.body.data.discount_amount <= 0) {
  44. [Dòng 465] event.inputType == 'deleteContentBackward') {
  45. [Dòng 466] if (event.target.name == 'exp_date'
  46. [Dòng 474] event.inputType == 'insertCompositionText') {
  47. [Dòng 555] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  48. [Dòng 566] } else if (this._b == 2) {
  49. [Dòng 568] } else if (this._b == 6) {
  50. [Dòng 570] } else if (this._b == 31) {
  51. [Dòng 593] if(this._b == 12){
  52. [Dòng 596] this.token_site == 'onepay'
  53. [Dòng 613] if (this._res_post.state == 'approved'
  54. [Dòng 613] this._res_post.state == 'failed') {
  55. [Dòng 663] } else if (this._res_post.state == 'authorization_required') {
  56. [Dòng 685] this._b == 14
  57. [Dòng 685] this._b == 15
  58. [Dòng 685] this._b == 24
  59. [Dòng 685] this._b == 8
  60. [Dòng 685] this._b == 10
  61. [Dòng 685] this._b == 18
  62. [Dòng 685] this._b == 22
  63. [Dòng 685] this._b == 23
  64. [Dòng 685] this._b == 27
  65. [Dòng 685] this._b == 30
  66. [Dòng 685] this._b == 11
  67. [Dòng 685] this._b == 12
  68. [Dòng 685] this._b == 9) {
  69. [Dòng 763] if ((cardNo.length == 16
  70. [Dòng 764] (cardNo.length == 19
  71. [Dòng 764] (cardNo.length == 19 && (this._b == 1
  72. [Dòng 764] this._b == 4
  73. [Dòng 764] this._b == 5))
  74. [Dòng 766] this._util.checkMod10(cardNo) == true
  75. [Dòng 853] return ((value.length == 4
  76. [Dòng 853] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  77. [Dòng 853] value.length == 5) && parseInt(value.split('/')[0]
  78. [Dòng 886] this._inExpDate.length == 4
  79. [Dòng 886] this._inExpDate.search('/') == -1)
  80. [Dòng 887] this._inExpDate.length == 5))
  81. [Dòng 945] result.data == 'approved') {

!== (2 điều kiện):
  1. [Dòng 626] codeResponse.toString() !== '0') {
  2. [Dòng 686] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (33 điều kiện):
  1. [Dòng 148] this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
  2. [Dòng 179] if (this.htmlDesc != null
  3. [Dòng 222] if (ua.indexOf('safari') != -1
  4. [Dòng 467] if (this._inExpDate.length != 3) {
  5. [Dòng 549] if (this._b != 3
  6. [Dòng 549] this._b != 9
  7. [Dòng 549] this._b != 16
  8. [Dòng 549] this._b != 17
  9. [Dòng 549] this._b != 18
  10. [Dòng 549] this._b != 19
  11. [Dòng 549] this._b != 25
  12. [Dòng 549] this._b != 44
  13. [Dòng 550] this._b != 57
  14. [Dòng 550] this._b != 59
  15. [Dòng 550] this._b != 61
  16. [Dòng 550] this._b != 63
  17. [Dòng 550] this._b != 68
  18. [Dòng 550] this._b != 69
  19. [Dòng 550] this._b != 6
  20. [Dòng 550] this._b != 2
  21. [Dòng 550] this._b != 57) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
  22. [Dòng 564] '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '74', '75'].indexOf(this._b.toString()) != -1) {
  23. [Dòng 615] if (this._res_post.return_url != null) {
  24. [Dòng 618] if (this._res_post.links != null
  25. [Dòng 618] this._res_post.links.merchant_return != null
  26. [Dòng 618] this._res_post.links.merchant_return.href != null) {
  27. [Dòng 668] if (this._res_post.authorization != null
  28. [Dòng 668] this._res_post.authorization.links != null
  29. [Dòng 673] this._res_post.links.cancel != null) {
  30. [Dòng 679] let userName = _formCard.name != null ? _formCard.name : ''
  31. [Dòng 680] this._res_post.authorization.links.approval != null
  32. [Dòng 680] this._res_post.authorization.links.approval.href != null) {
  33. [Dòng 683] userName = paramUserName != null ? paramUserName : ''

================================================================================

📁 FILE 94: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 95: off-bank-dialog-promotion.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/dialog/off-bank-dialog-promotion/off-bank-dialog-promotion.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 96: off-bank-dialog-promotion.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/dialog/off-bank-dialog-promotion/off-bank-dialog-promotion.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 97: promotion-apply-failed-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/dialog/promotion-apply-failed-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 98: domescard-main-promotion.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/domescard-main-promotion.component.html
📊 Thống kê: 66 điều kiện duy nhất
   - === : 1 lần
   - == : 65 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 29] filteredData.length === 0"

== (65 điều kiện):
  1. [Dòng 12] _auth==1) || (token
  2. [Dòng 12] _b == 16)"
  3. [Dòng 15] (token || _auth==1) && _b != 16
  4. [Dòng 18] _auth==1)">
  5. [Dòng 39] _auth==0
  6. [Dòng 39] <div *ngIf="(!token&&_auth==0&&(_b==1
  7. [Dòng 39] _b==4
  8. [Dòng 39] _b==7
  9. [Dòng 39] _b==8
  10. [Dòng 39] _b==9
  11. [Dòng 39] _b==10
  12. [Dòng 39] _b==11
  13. [Dòng 39] _b==14
  14. [Dòng 39] _b==15
  15. [Dòng 39] _b==16
  16. [Dòng 39] _b==17
  17. [Dòng 39] _b==20
  18. [Dòng 40] _b==22
  19. [Dòng 40] _b==23
  20. [Dòng 40] _b==24
  21. [Dòng 40] _b==25
  22. [Dòng 40] _b==30
  23. [Dòng 40] _b==33
  24. [Dòng 40] _b==34
  25. [Dòng 40] _b==35
  26. [Dòng 40] _b==36
  27. [Dòng 40] _b==37
  28. [Dòng 40] _b==38
  29. [Dòng 40] _b==39
  30. [Dòng 40] _b==40
  31. [Dòng 40] _b==41
  32. [Dòng 41] _b==42
  33. [Dòng 41] _b==43
  34. [Dòng 41] _b==44
  35. [Dòng 41] _b==45
  36. [Dòng 41] this._b==46
  37. [Dòng 41] _b==47
  38. [Dòng 41] _b==48
  39. [Dòng 41] _b==49
  40. [Dòng 41] _b==50
  41. [Dòng 41] _b==51
  42. [Dòng 41] _b==52
  43. [Dòng 41] _b==53
  44. [Dòng 41] _b==54
  45. [Dòng 41] _b==55
  46. [Dòng 41] _b==56
  47. [Dòng 41] _b==57
  48. [Dòng 41] _b==58
  49. [Dòng 41] _b==59
  50. [Dòng 41] _b==60
  51. [Dòng 42] _b==61
  52. [Dòng 42] _b==62
  53. [Dòng 42] _b==63
  54. [Dòng 42] _b==64
  55. [Dòng 42] this._b==65
  56. [Dòng 42] _b==66
  57. [Dòng 42] _b==67
  58. [Dòng 42] _b==68
  59. [Dòng 42] _b==69
  60. [Dòng 42] this._b==70
  61. [Dòng 42] this._b==71)) || (token && _b == 16)">
  62. [Dòng 42] _b == 16)">
  63. [Dòng 46] !token&&_auth==0&&(_b==6 || _b==2 || _b==5 || _b == 31 || _b == 80)
  64. [Dòng 46] _b == 80)">
  65. [Dòng 50] !token&&_auth==0&&(_b==3||_b==12||_b==18||_b==19||_b==27)

================================================================================

📁 FILE 99: domescard-main-promotion.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/domescard-main-promotion/domescard-main-promotion.component.ts
📊 Thống kê: 60 điều kiện duy nhất
   - === : 25 lần
   - == : 27 lần
   - !== : 2 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (25 điều kiện):
  1. [Dòng 223] let bank_napas = banks.filter(item => item.swiftCode === (e.swiftCode + 'NP'));
  2. [Dòng 299] if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
  3. [Dòng 300] filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
  4. [Dòng 340] if (valOut === 'auth') {
  5. [Dòng 485] if (this._b === '1'
  6. [Dòng 485] this._b === '20'
  7. [Dòng 485] this._b === '64') {
  8. [Dòng 488] if (this._b === '36'
  9. [Dòng 488] this._b === '18'
  10. [Dòng 488] this._b === '19'
  11. [Dòng 491] if (this._b === '19'
  12. [Dòng 491] this._b === '16'
  13. [Dòng 491] this._b === '25'
  14. [Dòng 491] this._b === '33'
  15. [Dòng 492] this._b === '39'
  16. [Dòng 492] this._b === '9'
  17. [Dòng 492] this._b === '11'
  18. [Dòng 492] this._b === '17'
  19. [Dòng 493] this._b === '36'
  20. [Dòng 493] this._b === '44'
  21. [Dòng 493] this._b === '12'
  22. [Dòng 494] this._b === '64'
  23. [Dòng 497] if (this._b === '20'
  24. [Dòng 500] if (this._b === '12'
  25. [Dòng 500] this._b === '18') {

== (27 điều kiện):
  1. [Dòng 162] this._auth == 0
  2. [Dòng 162] this.tokenList.length == 0
  3. [Dòng 162] this.promotionList.length == 0) {
  4. [Dòng 201] if (e.id == '31') {
  5. [Dòng 203] } else if (e.id == '80') {
  6. [Dòng 285] this.filteredData.length == 1
  7. [Dòng 322] if ($event && ($event == 'true'
  8. [Dòng 469] if (item['id'] == this._b.toString()) {
  9. [Dòng 488] this._b == '55'
  10. [Dòng 488] this._b == '47'
  11. [Dòng 488] this._b == '48'
  12. [Dòng 488] this._b == '59') {
  13. [Dòng 491] this._b == '3'
  14. [Dòng 492] this._b == '43'
  15. [Dòng 492] this._b == '45'
  16. [Dòng 493] this._b == '54'
  17. [Dòng 493] this._b == '57'
  18. [Dòng 494] this._b == '59'
  19. [Dòng 494] this._b == '61'
  20. [Dòng 494] this._b == '63'
  21. [Dòng 494] this._b == '67'
  22. [Dòng 494] this._b == '68'
  23. [Dòng 494] this._b == '69'
  24. [Dòng 494] this._b == '9'
  25. [Dòng 494] this._b == '74'
  26. [Dòng 494] this._b == '75') {
  27. [Dòng 497] this._b == '36'

!== (2 điều kiện):
  1. [Dòng 112] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  2. [Dòng 222] strTestNapas !== 'card

!= (6 điều kiện):
  1. [Dòng 151] if (params['locale'] != null) {
  2. [Dòng 155] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 185] if (!(strInstrument != null
  4. [Dòng 188] if (strInstrument.substring(0, 1) != '^'
  5. [Dòng 188] strInstrument.substr(strInstrument.length - 1) != '$') {
  6. [Dòng 366] if (bankid != null) {

================================================================================

📁 FILE 100: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/intercard-main/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 101: intercard-main-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/intercard-main/form/intercard-main-form.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 161] token_site == 'onepay'

================================================================================

📁 FILE 102: intercard-main-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/intercard-main/form/intercard-main-form.component.ts
📊 Thống kê: 79 điều kiện duy nhất
   - === : 11 lần
   - == : 44 lần
   - !== : 11 lần
   - != : 13 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 186] if (this.themeConfig && (this.themeConfig.csc_config === false)) {
  2. [Dòng 294] if (_formCard.country === 'default') {
  3. [Dòng 666] if (event.keyCode === 8
  4. [Dòng 666] event.key === "Backspace"
  5. [Dòng 748] if ((v.substr(-1) === ' '
  6. [Dòng 973] this._i_country_code === 'US') {
  7. [Dòng 1010] const insertIndex = this._i_country_code === 'US' ? 5 : 3
  8. [Dòng 1012] if (temp[i] === '-'
  9. [Dòng 1012] temp[i] === ' ') {
  10. [Dòng 1019] insertIndex === 3 ? ' ' : itemRemoved)
  11. [Dòng 1072] this.c_country = _val.value === 'default'

== (44 điều kiện):
  1. [Dòng 213] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 231] this._showCardName = this._showCardName && (this.card_name_token == '');
  3. [Dòng 332] this.token_site == 'onepay'
  4. [Dòng 361] if (this._res_post.state == 'approved'
  5. [Dòng 361] this._res_post.state == 'failed') {
  6. [Dòng 387] } else if (this._res_post.state == 'failed') { // lỗi mới kiểm tra sang trang lỗi
  7. [Dòng 416] } else if (this._res_post.state == 'authorization_required') {
  8. [Dòng 417] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  9. [Dòng 429] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  10. [Dòng 472] v.length == 15) || (v.length == 16
  11. [Dòng 472] v.length == 19))
  12. [Dòng 473] this._util.checkMod10(v) == true) {
  13. [Dòng 493] if (_re.status == '200'
  14. [Dòng 493] _re.status == '201') {
  15. [Dòng 494] _re.body.state == 'success')
  16. [Dòng 495] _re.body.data.discount_amount == 0))) {
  17. [Dòng 519] _re.body.state == 'fail') || _re.body.data.discount_amount <= 0) {
  18. [Dòng 591] result.data == 'approved') {
  19. [Dòng 615] cardNo.length == 15)
  20. [Dòng 617] cardNo.length == 16)
  21. [Dòng 618] || (cardNo.startsWith('6') && (cardNo.length == 16
  22. [Dòng 618] cardNo.length == 19))
  23. [Dòng 666] event.inputType == 'deleteContentBackward') {
  24. [Dòng 667] if (event.target.name == 'exp_date'
  25. [Dòng 675] event.inputType == 'insertCompositionText') {
  26. [Dòng 690] if (((this.valueDate.length == 4
  27. [Dòng 690] this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  28. [Dòng 690] this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  29. [Dòng 748] v.length == 5) {
  30. [Dòng 756] v.length == 4
  31. [Dòng 760] v.length == 3)
  32. [Dòng 791] _val.value.length == 4
  33. [Dòng 795] _val.value.length == 3)
  34. [Dòng 1026] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  35. [Dòng 1026] this.valueDate.length == 5)
  36. [Dòng 1057] this.requireAvs = this.AVS_COUNTRIES.find(e => e.code == this._i_country_code) ? true : false;
  37. [Dòng 1124] this.valueDate.length == 4
  38. [Dòng 1124] this.valueDate.search('/') == -1)
  39. [Dòng 1125] this.valueDate.length == 5))
  40. [Dòng 1138] this._i_csc.length == 4) ||
  41. [Dòng 1142] this._i_csc.length == 3)
  42. [Dòng 1216] country = this.AVS_COUNTRIES.find(e => e.code == res.bin_country);
  43. [Dòng 1237] countryCode == 'US' ? US_STATES
  44. [Dòng 1238] : countryCode == 'CA' ? CA_STATES

!== (11 điều kiện):
  1. [Dòng 343] if (this.tracking.hasOwnProperty(key) && ((key !== '8'
  2. [Dòng 343] !this._showNameOnCard) || (key !== '9'
  3. [Dòng 371] codeResponse.toString() !== '0'){
  4. [Dòng 748] event.inputType !== 'deleteContentBackward') || v.length == 5) {
  5. [Dòng 976] this._i_country_code !== 'US') {
  6. [Dòng 1018] itemRemoved !== '') {
  7. [Dòng 1049] if (deviceValue !== 'default') {
  8. [Dòng 1053] this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
  9. [Dòng 1150] this._i_country_code !== 'default'
  10. [Dòng 1175] this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
  11. [Dòng 1182] this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {

!= (13 điều kiện):
  1. [Dòng 200] if (params['locale'] != null) {
  2. [Dòng 206] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 363] if (this._res_post.return_url != null) {
  4. [Dòng 365] } else if (this._res_post.links != null
  5. [Dòng 365] this._res_post.links.merchant_return != null
  6. [Dòng 365] this._res_post.links.merchant_return.href != null) {
  7. [Dòng 454] if (ua.indexOf('safari') != -1
  8. [Dòng 615] cardNo != null
  9. [Dòng 668] if (this.valueDate.length != 3) {
  10. [Dòng 755] v != null
  11. [Dòng 790] this.c_csc = (!(_val.value != null
  12. [Dòng 1136] this._i_csc != null
  13. [Dòng 1218] this.requireAvs = this.isAvsCountry = country != undefined

================================================================================

📁 FILE 103: intercard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/intercard-main/intercard-main.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 177] token_site == 'onepay'

================================================================================

📁 FILE 104: intercard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/intercard-main/intercard-main.component.ts
📊 Thống kê: 78 điều kiện duy nhất
   - === : 11 lần
   - == : 45 lần
   - !== : 9 lần
   - != : 13 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 367] if (event.index === 1) {
  2. [Dòng 498] if (_formCard.country === 'default') {
  3. [Dòng 873] if (event.keyCode === 8
  4. [Dòng 873] event.key === "Backspace"
  5. [Dòng 948] if ((v.substr(-1) === ' '
  6. [Dòng 1173] this._i_country_code === 'US') {
  7. [Dòng 1210] const insertIndex = this._i_country_code === 'US' ? 5 : 3
  8. [Dòng 1212] if (temp[i] === '-'
  9. [Dòng 1212] temp[i] === ' ') {
  10. [Dòng 1219] insertIndex === 3 ? ' ' : itemRemoved)
  11. [Dòng 1266] this.c_country = _val.value === 'default'

== (45 điều kiện):
  1. [Dòng 180] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 261] if (_re.status == '200'
  3. [Dòng 261] _re.status == '201') {
  4. [Dòng 262] _re.body.state == 'success')
  5. [Dòng 263] _re.body.data.discount_amount == 0))) {
  6. [Dòng 276] _re.body.state == 'fail')
  7. [Dòng 332] this._showCardName = this._showCardName && (this.card_name_token == '');
  8. [Dòng 406] _re.body.state == 'fail') || _re.body.data.discount_amount <= 0) {
  9. [Dòng 536] this.token_site == 'onepay'
  10. [Dòng 565] if (this._res_post.state == 'approved'
  11. [Dòng 565] this._res_post.state == 'failed') {
  12. [Dòng 572] if (this._res_post.state == 'failed') {
  13. [Dòng 603] } else if (this._res_post.state == 'authorization_required') {
  14. [Dòng 604] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  15. [Dòng 616] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  16. [Dòng 675] v.length == 15) || (v.length == 16
  17. [Dòng 675] v.length == 19))
  18. [Dòng 676] this._util.checkMod10(v) == true) {
  19. [Dòng 797] result.data == 'approved') {
  20. [Dòng 821] cardNo.length == 15)
  21. [Dòng 823] cardNo.length == 16)
  22. [Dòng 824] cardNo.startsWith('81')) && (cardNo.length == 16
  23. [Dòng 824] cardNo.length == 19))
  24. [Dòng 873] event.inputType == 'deleteContentBackward') {
  25. [Dòng 874] if (event.target.name == 'exp_date'
  26. [Dòng 882] event.inputType == 'insertCompositionText') {
  27. [Dòng 897] if (((this.valueDate.length == 4
  28. [Dòng 897] this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  29. [Dòng 897] this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  30. [Dòng 948] v.length == 5) {
  31. [Dòng 956] v.length == 4
  32. [Dòng 960] v.length == 3)
  33. [Dòng 996] _val.value.length == 4
  34. [Dòng 1000] _val.value.length == 3)
  35. [Dòng 1226] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  36. [Dòng 1226] this.valueDate.length == 5)
  37. [Dòng 1253] this.requireAvs = this.AVS_COUNTRIES.find(e => e.code == this._i_country_code) ? true : false;
  38. [Dòng 1313] this.valueDate.length == 4
  39. [Dòng 1313] this.valueDate.search('/') == -1)
  40. [Dòng 1314] this.valueDate.length == 5))
  41. [Dòng 1327] this._i_csc.length == 4) ||
  42. [Dòng 1331] this._i_csc.length == 3)
  43. [Dòng 1417] country = this.AVS_COUNTRIES.find(e => e.code == res.bin_country);
  44. [Dòng 1438] countryCode == 'US' ? US_STATES
  45. [Dòng 1439] : countryCode == 'CA' ? CA_STATES

!== (9 điều kiện):
  1. [Dòng 547] key !== '8') {
  2. [Dòng 948] event.inputType !== 'deleteContentBackward') || v.length == 5) {
  3. [Dòng 1176] this._i_country_code !== 'US') {
  4. [Dòng 1218] itemRemoved !== '') {
  5. [Dòng 1245] if (deviceValue !== 'default') {
  6. [Dòng 1249] this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
  7. [Dòng 1339] this._i_country_code !== 'default'
  8. [Dòng 1368] this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
  9. [Dòng 1375] this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {

!= (13 điều kiện):
  1. [Dòng 208] if (params['locale'] != null) {
  2. [Dòng 211] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 567] if (this._res_post.return_url != null) {
  4. [Dòng 569] } else if (this._res_post.links != null
  5. [Dòng 569] this._res_post.links.merchant_return != null
  6. [Dòng 569] this._res_post.links.merchant_return.href != null) {
  7. [Dòng 657] if (ua.indexOf('safari') != -1
  8. [Dòng 821] cardNo != null
  9. [Dòng 875] if (this.valueDate.length != 3) {
  10. [Dòng 955] v != null
  11. [Dòng 995] this.c_csc = (!(_val.value != null
  12. [Dòng 1325] this._i_csc != null
  13. [Dòng 1419] this.requireAvs = this.isAvsCountry = country != undefined

================================================================================

📁 FILE 105: menu.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/menu.component.html
📊 Thống kê: 48 điều kiện duy nhất
   - === : 5 lần
   - == : 38 lần
   - !== : 2 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 274] bnpl.code === 'kbank'"
  2. [Dòng 369] *ngIf="(type === 5
  3. [Dòng 417] type === 5"
  4. [Dòng 418] [ngStyle]="{'border-color': (type === 5
  5. [Dòng 429] d_vrbank===true"

== (38 điều kiện):
  1. [Dòng 87] !token) || (type == 1
  2. [Dòng 105] !token) || (type == 6
  3. [Dòng 140] type == 2)"
  4. [Dòng 149] !token) || (type == 3
  5. [Dòng 163] !token) || (type == 5
  6. [Dòng 198] method?.trim()=='International'
  7. [Dòng 210] method.trim()=='ApplePay'"
  8. [Dòng 218] method.trim()=='GooglePay'"
  9. [Dòng 226] method.trim()=='SamsungPay'"
  10. [Dòng 234] method?.trim()=='Domestic'
  11. [Dòng 244] method?.trim()=='QR'
  12. [Dòng 252] method?.trim()=='Paypal'
  13. [Dòng 263] <div *ngIf="((onePaymentMethod == true
  14. [Dòng 263] d_bnpl) || (onePaymentMethod == false
  15. [Dòng 263] d_bnpl_number == 1))
  16. [Dòng 264] method?.trim()=='Bnpl'
  17. [Dòng 269] bnpl.status == 'disabled'"
  18. [Dòng 281] providerType == bnpl.code"
  19. [Dòng 286] onePaymentMethod == true
  20. [Dòng 286] d_bnpl_number == 1"
  21. [Dòng 312] d_bnpl_number == 1
  22. [Dòng 312] bnpl.status == 'disabled'
  23. [Dòng 312] bnpl.code == 'homepaylater'"
  24. [Dòng 319] onePaymentMethod == false
  25. [Dòng 343] bnpl.code == 'kbank'
  26. [Dòng 348] bnpl.code == 'insta'"
  27. [Dòng 352] bnpl.code == 'homepaylater'
  28. [Dòng 363] bnpl.code == 'kredivo'
  29. [Dòng 369] bnpl.status == 'active'
  30. [Dòng 370] providerType == bnpl.code)
  31. [Dòng 371] || (d_bnpl_number == 1
  32. [Dòng 371] providerType == bnpl.code)))
  33. [Dòng 372] promotionList.length == 0) && bnpl.status !== 'disabled')">
  34. [Dòng 382] bnpl.code == 'kbank'"
  35. [Dòng 394] bnpl.code == 'kredivo'"
  36. [Dòng 419] _auth == 1
  37. [Dòng 419] [class.enable_form]="!onePaymentMethod || (onePaymentMethod && d_bnpl)" *ngIf="_auth == 1 && ((onePaymentMethod == true
  38. [Dòng 419] d_bnpl_number == 1)

!== (2 điều kiện):
  1. [Dòng 277] bnpl.code !== 'kbank'"
  2. [Dòng 372] bnpl.status !== 'disabled')">

!= (3 điều kiện):
  1. [Dòng 174] themeConfig.enable_payment_method != 'false'
  2. [Dòng 264] _auth != '1'"
  3. [Dòng 352] <div *ngIf="bnpl.code == 'homepaylater' && (selectedBnpl != bnpl)"

================================================================================

📁 FILE 106: menu.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/menu.component.ts
📊 Thống kê: 209 điều kiện duy nhất
   - === : 22 lần
   - == : 117 lần
   - !== : 3 lần
   - != : 67 lần
--------------------------------------------------------------------------------

=== (22 điều kiện):
  1. [Dòng 815] item.brand_id === 'atm'
  2. [Dòng 815] item.swift_code.slice(-2) === 'NP') {
  3. [Dòng 840] if (tokenWithPromotion.filter(item => item.id === token.id).length === 0) {
  4. [Dòng 861] let arrUrl = this.currentUrl.split('&').filter(item => item === 'type=2').length;
  5. [Dòng 873] return arr.map(mapObj => mapObj["id"]).indexOf(obj["id"]) === pos
  6. [Dòng 983] this.kredivoProvider = this.bankAmountArr.find(e => e.code === 'kredivo');
  7. [Dòng 1027] this.bnpls.push(this.sublist.find(e => e.name === 'KBank Pay Later'));
  8. [Dòng 1030] this.bnpls.push(this.sublist.find(e => e.name === 'Home PayLater'));
  9. [Dòng 1031] this.bnpls_promotion.push(this.sublist.find(e => e.name === 'Home PayLater'));
  10. [Dòng 1034] this.bnpls.push(this.sublist.find(e => e.name === 'Kredivo'));
  11. [Dòng 1037] this.bnpls.push(this.sublist.find(e => e.name === 'Insta'));
  12. [Dòng 1118] return index === array.findIndex(obj => {
  13. [Dòng 1119] return JSON.stringify(obj) === _value
  14. [Dòng 1236] this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_bnpl + this.d_mobile_wallet === 1
  15. [Dòng 1297] if (this._res.state === 'unpaid'
  16. [Dòng 1297] this._res.state === 'not_paid') {
  17. [Dòng 1417] if ('op' === auth
  18. [Dòng 1456] } else if ('bank' === auth
  19. [Dòng 1461] if (approval.method === 'REDIRECT') {
  20. [Dòng 1464] } else if (approval.method === 'POST_REDIRECT') {
  21. [Dòng 2051] return id === 'amex' ? '1234' : '123'
  22. [Dòng 2249] if (this.timeLeftPaypal === 0) {

== (117 điều kiện):
  1. [Dòng 283] if (el == 5) {
  2. [Dòng 285] } else if (el == 6) {
  3. [Dòng 287] } else if (el == 7) {
  4. [Dòng 289] } else if (el == 8) {
  5. [Dòng 291] } else if (el == 2) {
  6. [Dòng 293] } else if (el == 4) {
  7. [Dòng 295] } else if (el == 3) {
  8. [Dòng 343] if (!isNaN(_re.status) && (_re.status == '200'
  9. [Dòng 343] _re.status == '201') && _re.body != null) {
  10. [Dòng 348] if (('closed' == this._res_polling.state
  11. [Dòng 348] 'canceled' == this._res_polling.state
  12. [Dòng 348] 'expired' == this._res_polling.state)
  13. [Dòng 368] } else if ('paid' == this._res_polling.state) {
  14. [Dòng 376] this._res_polling.payments == null
  15. [Dòng 385] if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'authorization_required') {
  16. [Dòng 389] } else if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  17. [Dòng 396] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  18. [Dòng 398] this._paymentService.getCurrentPage() == 'enter_card'
  19. [Dòng 401] } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
  20. [Dòng 401] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
  21. [Dòng 419] } else if ('not_paid' == this._res_polling.state) {
  22. [Dòng 431] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
  23. [Dòng 574] this.errorRefreshed = params['error_refresh'] == true
  24. [Dòng 574] params['error_refresh'] == 'true' ? true : false
  25. [Dòng 583] if (message == '1') {
  26. [Dòng 611] if (_re.status == '200'
  27. [Dòng 611] _re.status == '201') {
  28. [Dòng 614] this.version2 = _re.body?.merchant?.qr_version == "2"
  29. [Dòng 635] if (this.type == 5
  30. [Dòng 638] } else if (this.type == 6
  31. [Dòng 641] } else if (this.type == 2
  32. [Dòng 644] } else if (this.type == 7
  33. [Dòng 647] } else if (this.type == 8
  34. [Dòng 650] } else if (this.type == 4
  35. [Dòng 653] } else if (this.type == 3
  36. [Dòng 701] if (this.themeConfig.default_method == 'International'
  37. [Dòng 703] } else if (this.themeConfig.default_method == 'Domestic'
  38. [Dòng 705] } else if (this.themeConfig.default_method == 'QR'
  39. [Dòng 707] } else if (this.themeConfig.default_method == 'Paypal'
  40. [Dòng 847] tokenWithPromotion.length == 0)) {
  41. [Dòng 1041] if (this.onePaymentMethod == true
  42. [Dòng 1041] this.d_bnpl == 1) {
  43. [Dòng 1051] _re.code == '0'
  44. [Dòng 1065] a.product_code == 'SP') {
  45. [Dòng 1078] a.product_code == 'PL') {
  46. [Dòng 1127] item.prepaid_percent == this.selectedPrePaid
  47. [Dòng 1127] item.installment_month == this.selectedPayLater) {
  48. [Dòng 1140] packageItem.product_code == productCode) {
  49. [Dòng 1148] if (string == 'SP') {
  50. [Dòng 1151] } else if (string == 'PL') {
  51. [Dòng 1198] if (this.d_amigo == true
  52. [Dòng 1198] this.d_insta == false
  53. [Dòng 1198] this.d_instaplus == false) {
  54. [Dòng 1211] if (this.d_amigo_number == 0
  55. [Dòng 1211] this.d_insta_number == 1) {
  56. [Dòng 1214] else if (this.d_amigo_number == 0
  57. [Dòng 1214] this.d_instaplus_number == 1) {
  58. [Dòng 1237] if ((this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_bnpl + this.d_mobile_wallet) == 0
  59. [Dòng 1263] this._auth == 0) {
  60. [Dòng 1298] if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
  61. [Dòng 1298] this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
  62. [Dòng 1300] if (this._res.payments[this._res.payments.length - 1].state == 'approved'
  63. [Dòng 1302] } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
  64. [Dòng 1346] if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required') {
  65. [Dòng 1352] } else if (this._res.payments[this._res.payments.length - 1].state == 'pending') {
  66. [Dòng 1358] } else if (this._res.payments[this._res.payments.length - 1].state == 'canceled') {
  67. [Dòng 1362] } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  68. [Dòng 1362] this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
  69. [Dòng 1399] if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  70. [Dòng 1403] } else if (idBrand == 'atm'
  71. [Dòng 1477] } else if (idBrand == 'kbank'
  72. [Dòng 1499] detail.merchant.id == 'AMWAY') {
  73. [Dòng 1581] this._res.payments[this._res.payments.length - 1].state == 'pending'
  74. [Dòng 1583] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
  75. [Dòng 1602] if ('paid' == this._res.state) {
  76. [Dòng 1603] this._res.merchant.token_site == 'onepay')) {
  77. [Dòng 1624] if (('closed' == this._res.state
  78. [Dòng 1624] 'canceled' == this._res.state
  79. [Dòng 1624] 'expired' == this._res.state
  80. [Dòng 1624] 'paid' == this._res.state)
  81. [Dòng 1646] this._res.payments == null) {
  82. [Dòng 1648] this._res.payments[this._res.payments.length - 1].state == 'pending') {
  83. [Dòng 1658] if (this._res.currencies[0] == 'USD') {
  84. [Dòng 1717] if (item.instrument.issuer.brand.id == 'atm') {
  85. [Dòng 1719] } else if (item.instrument.issuer.brand.id == 'visa'
  86. [Dòng 1719] item.instrument.issuer.brand.id == 'mastercard') {
  87. [Dòng 1720] if (item.instrument.issuer_location == 'd') {
  88. [Dòng 1725] } else if (item.instrument.issuer.brand.id == 'amex') {
  89. [Dòng 1731] } else if (item.instrument.issuer.brand.id == 'jcb') {
  90. [Dòng 1747] uniq.length == 1) {
  91. [Dòng 1776] if (element.accept_instrument == null
  92. [Dòng 1776] element.accept_instrument == '') {
  93. [Dòng 2024] return merchant.token_cvv == true
  94. [Dòng 2029] return (merchant.token_name == true)
  95. [Dòng 2035] return (merchant.token_email == true)
  96. [Dòng 2041] return (merchant.token_phone == true)
  97. [Dòng 2115] if (type == 'qrv1') {
  98. [Dòng 2125] if (type == 'mobile') {
  99. [Dòng 2127] e.type == 'ewallet'
  100. [Dòng 2127] e.code == 'momo')) {
  101. [Dòng 2135] } else if (type == 'desktop') {
  102. [Dòng 2136] e.type == 'vnpayqr') || (regex.test(strTest)
  103. [Dòng 2191] _val == 2) {
  104. [Dòng 2218] if (_val == 2
  105. [Dòng 2220] } else if (_val == 2
  106. [Dòng 2226] _val == 2
  107. [Dòng 2237] if (this.type == 4) {
  108. [Dòng 2317] if (this._res_post.state == 'approved'
  109. [Dòng 2317] this._res_post.state == 'failed') {
  110. [Dòng 2326] } else if (this._res_post.state == 'authorization_required') {
  111. [Dòng 2327] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  112. [Dòng 2341] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  113. [Dòng 2400] filteredData.length == 1) {
  114. [Dòng 2445] if (col == 2) {
  115. [Dòng 2447] } else if (col == 1) {
  116. [Dòng 2455] } else if (col == 3) {
  117. [Dòng 2492] if (data._locale == 'en') {

!== (3 điều kiện):
  1. [Dòng 862] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  2. [Dòng 2182] if (_val !== 3) {
  3. [Dòng 2186] this._util.getElementParams(this.currentUrl, 't_id') !== '') {

!= (67 điều kiện):
  1. [Dòng 336] if (this._idInvoice != null
  2. [Dòng 336] this._paymentService.getState() != 'error') {
  3. [Dòng 342] if (this._paymentService.getCurrentPage() != 'otp') {
  4. [Dòng 343] _re.body != null) {
  5. [Dòng 349] this._res_polling.links != null
  6. [Dòng 349] this._res_polling.links.merchant_return != null //
  7. [Dòng 376] } else if (this._res_polling.merchant != null
  8. [Dòng 376] this._res_polling.merchant_invoice_reference != null
  9. [Dòng 378] } else if (this._res_polling.payments != null
  10. [Dòng 398] this._res_polling.payments[this._res_polling.payments.length - 1].instrument.issuer.brand.id != 'amigo') {
  11. [Dòng 402] this._res_polling.links.merchant_return != null//
  12. [Dòng 422] this._res_polling.payments != null
  13. [Dòng 430] if (this._res_polling.payments != null
  14. [Dòng 434] t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
  15. [Dòng 554] this.type.toString().length != 0) {
  16. [Dòng 560] if (params['locale'] != null) {
  17. [Dòng 568] if ('otp' != this._paymentService.getCurrentPage()) {
  18. [Dòng 576] params['url_redirect'] != '' ? params['url_redirect'] : ''
  19. [Dòng 593] if (this._paymentService.getInvoiceDetail() != null) {
  20. [Dòng 757] this.amount_promotion = promotionPassData.data.discount_amount != 0 ? (this._util.formatCurrency(promotionPassData.data.discount_amount).toString() + ' ' + this.currency) : ''
  21. [Dòng 1167] if (!(strInstrument != null
  22. [Dòng 1170] if (strInstrument.substring(0, 1) != '^'
  23. [Dòng 1170] strInstrument.substr(strInstrument.length - 1) != '$') {
  24. [Dòng 1277] if (count != 1) {
  25. [Dòng 1287] if (this._res.merchant != null
  26. [Dòng 1287] this._res.merchant_invoice_reference != null) {
  27. [Dòng 1290] if (this._res.merchant.address_details != null) {
  28. [Dòng 1298] this._res.links != null//
  29. [Dòng 1341] } else if (this._res.payments != null
  30. [Dòng 1363] this._res.payments[this._res.payments.length - 1].instrument != null
  31. [Dòng 1363] this._res.payments[this._res.payments.length - 1].instrument.issuer != null
  32. [Dòng 1364] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
  33. [Dòng 1364] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
  34. [Dòng 1365] this._res.payments[this._res.payments.length - 1].links != null
  35. [Dòng 1365] this._res.payments[this._res.payments.length - 1].links.cancel != null
  36. [Dòng 1365] this._res.payments[this._res.payments.length - 1].links.cancel.href != null
  37. [Dòng 1385] this._res.payments[this._res.payments.length - 1].links.update != null
  38. [Dòng 1385] this._res.payments[this._res.payments.length - 1].links.update.href != null) {
  39. [Dòng 1403] this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
  40. [Dòng 1404] this._res.payments[this._res.payments.length - 1].authorization != null
  41. [Dòng 1404] this._res.payments[this._res.payments.length - 1].authorization.links != null) {
  42. [Dòng 1417] this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
  43. [Dòng 1417] this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
  44. [Dòng 1420] if (this._res.payments[this._res.payments.length - 1].authorization != null
  45. [Dòng 1420] this._res.payments[this._res.payments.length - 1].authorization.links != null
  46. [Dòng 1426] auth = paramUserName != null ? paramUserName : ''
  47. [Dòng 1489] if (this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
  48. [Dòng 1625] this._res.links != null
  49. [Dòng 1625] this._res.links.merchant_return != null //
  50. [Dòng 1646] } else if (this._res.merchant != null
  51. [Dòng 1646] this._res.merchant_invoice_reference != null
  52. [Dòng 1650] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != 'amigo') {
  53. [Dòng 1782] if (!(s_paygate != null
  54. [Dòng 1785] if (s_paygate.substring(0, 1) != '^'
  55. [Dòng 1785] s_paygate.substr(s_paygate.length - 1) != '$') {
  56. [Dòng 1947] if (['mastercard', 'visa', 'amex', 'jcb', 'cup', 'card', 'np_card', 'atm'].indexOf(id) != -1) {
  57. [Dòng 1949] } else if (['techcombank_account', 'vib_account', 'dongabank_account', 'tpbank_account', 'bidv_account', 'pvcombank_account', 'shb_account', 'oceanbank_online_account'].indexOf(id) != -1) {
  58. [Dòng 1951] } else if (['oceanbank_mobile_account'].indexOf(id) != -1) {
  59. [Dòng 1953] } else if (['shb_customer_id'].indexOf(id) != -1) {
  60. [Dòng 1996] if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1) {
  61. [Dòng 2100] if (this._translate.currentLang != language) {
  62. [Dòng 2127] e.type != 'ewallet') || (regex.test(strTest)
  63. [Dòng 2188] } else if (this._res.payments != null) {
  64. [Dòng 2319] if (this._res_post.return_url != null) {
  65. [Dòng 2321] } else if (this._res_post.links != null
  66. [Dòng 2321] this._res_post.links.merchant_return != null
  67. [Dòng 2321] this._res_post.links.merchant_return.href != null) {

================================================================================

📁 FILE 107: applepay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/applepay/applepay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 108: applepay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/applepay/applepay.component.ts
📊 Thống kê: 8 điều kiện duy nhất
   - === : 0 lần
   - == : 5 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

== (5 điều kiện):
  1. [Dòng 98] if (event.data?.event_type == 'applepay_network_not_supported') {
  2. [Dòng 100] } else if (event.data?.event_type == 'applepay_promotion_fail') {
  3. [Dòng 104] } else if (event.data?.event_type == 'show_button_applepay') {
  4. [Dòng 106] } else if (event.data?.event_type == 'hide_button_applepay') {
  5. [Dòng 108] } else if (event.data?.event_type == 'close_overlay') {

!= (3 điều kiện):
  1. [Dòng 96] if (event.origin != window.origin) return; // chỉ nhận message từ OnePay
  2. [Dòng 101] if (event.data?.state != 'success'
  3. [Dòng 124] if (_re.body?.state != "success") {

================================================================================

📁 FILE 109: dialog-network-not-supported.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/applepay/dialog-network-not-supported/dialog-network-not-supported.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 110: dialog-network-not-supported.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/applepay/dialog-network-not-supported/dialog-network-not-supported.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 111: google-pay-button-op.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/googlepay/google-pay-button-op/google-pay-button-op.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 112: google-pay-button-op.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/googlepay/google-pay-button-op/google-pay-button-op.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 184] if (GGPaySDKScript.readyState === "loaded"
  2. [Dòng 184] GGPaySDKScript.readyState === "complete") {

================================================================================

📁 FILE 113: googlepay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/googlepay/googlepay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 114: googlepay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/googlepay/googlepay.component.ts
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 25] isTop = window === window.top
  2. [Dòng 58] if (approval.method === 'REDIRECT') {
  3. [Dòng 61] } else if (approval.method === 'POST_REDIRECT') {

== (2 điều kiện):
  1. [Dòng 47] if (res?.body?.state == 'approved') {
  2. [Dòng 56] } else if (res?.body?.state == 'authorization_required') {

================================================================================

📁 FILE 115: mobile-wallet-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/mobile-wallet-main.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (3 điều kiện):
  1. [Dòng 9] paymentType == PaymentType.ApplePay"
  2. [Dòng 10] paymentType == PaymentType.GooglePay"
  3. [Dòng 11] paymentType == PaymentType.SamsungPay"

================================================================================

📁 FILE 116: mobile-wallet-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/mobile-wallet-main.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 102] return currency === 'VND'

== (2 điều kiện):
  1. [Dòng 62] if (this.paymentType == PaymentType.ApplePay) {
  2. [Dòng 90] if (network == 'napas'

!= (3 điều kiện):
  1. [Dòng 73] if (this.paymentType != PaymentType.ApplePay) return;
  2. [Dòng 85] if (this.paymentType != PaymentType.ApplePay) return false;
  3. [Dòng 91] if (network != 'napas'

================================================================================

📁 FILE 117: samsung-pay-button-op.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 118: samsung-pay-button-op.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.ts
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 127] if (GGPaySDKScript.readyState === "loaded"
  2. [Dòng 127] GGPaySDKScript.readyState === "complete") {

== (1 điều kiện):
  1. [Dòng 62] serviceId: this.environment == 'PRODUCTION'? this.serviceIdProd : this.serviceIdTest

================================================================================

📁 FILE 119: samsungpay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/samsungpay/samsungpay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 120: samsungpay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/mobile-wallet-main/samsungpay/samsungpay.component.ts
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 73] if (approval.method === 'REDIRECT') {
  2. [Dòng 76] } else if (approval.method === 'POST_REDIRECT') {

== (2 điều kiện):
  1. [Dòng 61] if (res?.body?.state == 'approved') {
  2. [Dòng 71] } else if (res?.body?.state == 'authorization_required') {

================================================================================

📁 FILE 121: bottom-guide.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/promotion-main/bottom-guide/bottom-guide.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 122: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/promotion-main/promotion-dialog/dialog-guide-dialog.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (6 điều kiện):
  1. [Dòng 9] data['type'] == 'Visa'
  2. [Dòng 9] data['type'] == 'Master'
  3. [Dòng 9] data['type'] == 'JCB'"
  4. [Dòng 17] data['type'] == 'Visa'"
  5. [Dòng 17] data['type'] == 'Master'"
  6. [Dòng 24] data['type'] == 'Amex'"

================================================================================

📁 FILE 123: promotion-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/promotion-main/promotion-main.component.html
📊 Thống kê: 26 điều kiện duy nhất
   - === : 2 lần
   - == : 14 lần
   - !== : 0 lần
   - != : 10 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 11] style="{{item.brand_id === 'visa' ? 'height: 14.42px
  2. [Dòng 213] item.promotionByToken.length === 0"

== (14 điều kiện):
  1. [Dòng 6] (item.international + item.domescard + item.qr + item.mobileWallet) == 0
  2. [Dòng 6] item.bnpl == 1
  3. [Dòng 7] bnpl.status == 'disabled'"
  4. [Dòng 21] item.active == true"
  5. [Dòng 73] bnpls.length == 0"
  6. [Dòng 123] d_homecredit == 1
  7. [Dòng 125] <div *ngIf="(bnpl.status == 'active')">
  8. [Dòng 126] bnpl.code == 'homepaylater'"
  9. [Dòng 143] item.international == 1"
  10. [Dòng 163] item.domescard == 1"
  11. [Dòng 174] item.qr == 1"
  12. [Dòng 187] item.bnpl == 1"
  13. [Dòng 199] item.mobileWallet == 1"
  14. [Dòng 213] item.international + item.domescard + item.qr + item.bnpl + item.mobileWallet == 1

!= (10 điều kiện):
  1. [Dòng 269] *ngIf="item.checkbox_international || (item.mobileWallet != 1
  2. [Dòng 269] item.domescard != 1
  3. [Dòng 269] item.qr != 1
  4. [Dòng 269] item.bnpl != 1)"
  5. [Dòng 274] *ngIf="item.checkbox_domescard || (item.mobileWallet != 1
  6. [Dòng 274] item.international != 1
  7. [Dòng 280] *ngIf="item.checkbox_qr || (item.mobileWallet != 1
  8. [Dòng 290] *ngIf="item.checkbox_bnpl || (item.mobileWallet != 1
  9. [Dòng 290] item.qr != 1)"
  10. [Dòng 295] *ngIf="item.checkbox_mobileWallet || (item.bnpl != 1

================================================================================

📁 FILE 124: promotion-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/promotion-main/promotion-main.component.ts
📊 Thống kê: 17 điều kiện duy nhất
   - === : 0 lần
   - == : 15 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (15 điều kiện):
  1. [Dòng 137] if (message == '0') {
  2. [Dòng 149] this.themeConfig.enable_payment_method == "false"
  3. [Dòng 149] this.promotionList?.length == 1) {
  4. [Dòng 162] if (item.id == data.id) {
  5. [Dòng 163] if (position == 1) {
  6. [Dòng 169] } else if (position == 2) {
  7. [Dòng 183] else if (position == 4) {
  8. [Dòng 190] } else if (position == 5) {
  9. [Dòng 231] item.id == element.id ? element['active'] = true : element['active'] = false
  10. [Dòng 232] if (item.id == element.id
  11. [Dòng 234] } else if (item.id == element.id
  12. [Dòng 242] item.display_cvv == true ? this.flagToken = true : this.flagToken = false
  13. [Dòng 254] $event == 'true') {
  14. [Dòng 329] if (_re.status == '200'
  15. [Dòng 329] _re.status == '201') {

!== (1 điều kiện):
  1. [Dòng 245] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (1 điều kiện):
  1. [Dòng 131] if (params['locale'] != null) {

================================================================================

📁 FILE 125: remove-promotion-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/promotion-main/remove-promotion-dialog/remove-promotion-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 126: tooltip.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/promotion-main/tooltip/tooltip.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 127: tooltip.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/promotion-main/tooltip/tooltip.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 128: tooltip.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/promotion-main/tooltip/tooltip.directive.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 1 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 44] if (typeof(this.content) === 'string') {

================================================================================

📁 FILE 129: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 130: qr-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 2] screen=='qr'"
  2. [Dòng 112] screen=='confirm_close'"

================================================================================

📁 FILE 131: qr-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 132: qr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main/qr-main.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 6] filteredData.length === 0
  2. [Dòng 6] filteredDataOther.length === 0"
  3. [Dòng 40] filteredDataMobile.length === 0
  4. [Dòng 40] filteredDataOtherMobile.length === 0"

================================================================================

📁 FILE 133: qr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main/qr-main.component.ts
📊 Thống kê: 26 điều kiện duy nhất
   - === : 8 lần
   - == : 12 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 251] this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
  2. [Dòng 252] this.filteredDataOther = this.appList.filter(item => item.type === 'other');
  3. [Dòng 253] this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
  4. [Dòng 254] this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
  5. [Dòng 275] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  6. [Dòng 276] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  7. [Dòng 282] if (item.type === 'mobile_banking') {
  8. [Dòng 401] this.appList.length === 1

== (12 điều kiện):
  1. [Dòng 162] this.themeConfig.deeplink_status == 'Off' ? false : true
  2. [Dòng 281] if (item.available == true) {
  3. [Dòng 324] if (_re.status == '200'
  4. [Dòng 324] _re.status == '201') {
  5. [Dòng 325] _re.body.state == 'success')
  6. [Dòng 326] _re.body.data.discount_amount == 0))) {
  7. [Dòng 364] if (appcode == 'grabpay'
  8. [Dòng 364] appcode == 'momo') {
  9. [Dòng 367] if (type == 2
  10. [Dòng 416] _re.body.state == 'fail') || _re.body.data.discount_amount <= 0) {
  11. [Dòng 539] if (result.data == 'approved') {
  12. [Dòng 607] err.error.code == '04') {

!= (6 điều kiện):
  1. [Dòng 180] if (params['locale'] != null) {
  2. [Dòng 186] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 211] if (!(strInstrument != null
  4. [Dòng 337] if (appcode != null) {
  5. [Dòng 632] if (_re.status != '200'
  6. [Dòng 632] _re.status != '201')

================================================================================

📁 FILE 134: safe-html.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main/safe-html.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 135: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 136: qr-desktop.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 1 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 168] listVNPayQR.length === 0"

================================================================================

📁 FILE 137: qr-desktop.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.ts
📊 Thống kê: 19 điều kiện duy nhất
   - === : 4 lần
   - == : 10 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 362] this.listWalletQR.length === 1) {
  2. [Dòng 412] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  3. [Dòng 413] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  4. [Dòng 754] this.listWalletQR.length === 1

== (10 điều kiện):
  1. [Dòng 259] e.type == 'vnpayqr') {
  2. [Dòng 266] e.type == 'ewallet') {
  3. [Dòng 309] if (_re.status == '200'
  4. [Dòng 309] _re.status == '201') {
  5. [Dòng 340] e.type == 'wallet')) {
  6. [Dòng 379] if (d.b.code == s) {
  7. [Dòng 418] if (item.available == true) {
  8. [Dòng 477] if (appcode == 'grabpay'
  9. [Dòng 477] appcode == 'momo') {
  10. [Dòng 511] err.error.code == '04') {

!= (5 điều kiện):
  1. [Dòng 223] if (params['locale'] != null) {
  2. [Dòng 249] if (!(strInstrument != null
  3. [Dòng 436] if (appcode != null) {
  4. [Dòng 728] if (_re.status != '200'
  5. [Dòng 728] _re.status != '201')

================================================================================

📁 FILE 138: qr-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1] screen=='qr'"
  2. [Dòng 121] screen=='confirm_close'"

================================================================================

📁 FILE 139: qr-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 49] 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {

================================================================================

📁 FILE 140: qr-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (3 điều kiện):
  1. [Dòng 5] type == 'vnpay'"
  2. [Dòng 6] type == 'bankapp'"
  3. [Dòng 7] type == 'both'"

================================================================================

📁 FILE 141: qr-guide-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 142: list-bank-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 143: list-bank-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 144: qr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 145: qr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-main.component.ts
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 188] this.themeConfig.deeplink_status == 'Off' ? false : true

!= (2 điều kiện):
  1. [Dòng 204] if (params['locale'] != null) {
  2. [Dòng 210] if ('otp' != this._paymentService.getCurrentPage()) {

================================================================================

📁 FILE 146: qr-mobile.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 132] _locale=='vi'"
  2. [Dòng 133] _locale=='en'"
  3. [Dòng 143] _locale == 'vi'"
  4. [Dòng 145] _locale == 'en'"

!= (2 điều kiện):
  1. [Dòng 231] qr_version2 != 'None'"
  2. [Dòng 257] qr_version2 != 'None'

================================================================================

📁 FILE 147: qr-mobile.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.ts
📊 Thống kê: 30 điều kiện duy nhất
   - === : 6 lần
   - == : 19 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (6 điều kiện):
  1. [Dòng 470] if (!this.d_vnpayqr_wallet && !this.d_vnpayqr_deeplink && (this.listWalletQR?.length === 1
  2. [Dòng 470] this.listWalletDeeplink?.length === 1)) {
  3. [Dòng 578] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  4. [Dòng 579] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  5. [Dòng 585] if (item.type === 'deeplink') {
  6. [Dòng 967] this.listWalletQR?.length === 1

== (19 điều kiện):
  1. [Dòng 277] e.type == 'deeplink') {
  2. [Dòng 288] e.type == 'ewallet'
  3. [Dòng 308] e.type == 'vnpayqr') {
  4. [Dòng 322] e.type == 'wallet')) {
  5. [Dòng 351] e.type == 'ewallet') {
  6. [Dòng 381] if (e.type == 'ewallet') {
  7. [Dòng 404] this.listWallet.length == 1
  8. [Dòng 404] this.listWallet[0].code == 'momo') {
  9. [Dòng 406] this.checkEWalletDeeplink.length == 0) {
  10. [Dòng 487] arrayWallet.length == 0) return false;
  11. [Dòng 489] if (arrayWallet[i].code == key) {
  12. [Dòng 513] if (_re.status == '200'
  13. [Dòng 513] _re.status == '201') {
  14. [Dòng 535] if (d.b.code == s) {
  15. [Dòng 584] if (item.available == true) {
  16. [Dòng 660] if (appcode == 'grabpay'
  17. [Dòng 660] appcode == 'momo') {
  18. [Dòng 663] if (type == 2
  19. [Dòng 703] err.error.code == '04') {

!= (5 điều kiện):
  1. [Dòng 231] if (params['locale'] != null) {
  2. [Dòng 258] if (!(strInstrument != null
  3. [Dòng 612] if (appcode != null) {
  4. [Dòng 937] if (_re.status != '200'
  5. [Dòng 937] _re.status != '201')

================================================================================

📁 FILE 148: safe-html.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/qr-main-version2/safe-html.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 149: queuing.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/queuing/queuing.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 150: queuing.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/queuing/queuing.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 62] if (this.locale == 'vi') {

!= (1 điều kiện):
  1. [Dòng 86] if (this.translate.currentLang != language) {

================================================================================

📁 FILE 151: bnpl-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/bnpl-form/bnpl-form.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 3 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 1] type === 5"
  2. [Dòng 2] [ngStyle]="{'border-color': (type === 5
  3. [Dòng 16] type === 5

== (1 điều kiện):
  1. [Dòng 4] !token) || (type == 5

================================================================================

📁 FILE 152: bnpl-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/bnpl-form/bnpl-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 153: domescard-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 2 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 1] type === 2"
  2. [Dòng 3] [ngStyle]="{'border-color': (type === 2

== (3 điều kiện):
  1. [Dòng 4] !auth) || (type == 2
  2. [Dòng 14] !token) || tokenList?.length > 0 || (tokenList.length == 0
  3. [Dòng 31] !token) || tokenList?.length > 0 || (tokenList?.length == 0

!= (1 điều kiện):
  1. [Dòng 27] feeService['atm']['fee'] != 0"

================================================================================

📁 FILE 154: domescard-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 155: domescard-form-promotion.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/domescard-form-promotion/domescard-form-promotion.component.html
📊 Thống kê: 12 điều kiện duy nhất
   - === : 4 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 1] type === 2"
  2. [Dòng 2] [ngStyle]="{'border-color': type === 2 ? this.themeConfig.border_color : border_color}"
  3. [Dòng 22] *ngIf="(((type === 2
  4. [Dòng 22] type === '2'

== (4 điều kiện):
  1. [Dòng 5] uniqueTokenBank) || (type == 2
  2. [Dòng 22] (((type === 2 || type === '2' || _b || (onePaymentMethod && d_domestic))) && (!tokenList.length || type == 2) && (promotionList && promotionList.length > 0 && type == 2)) || token
  3. [Dòng 22] type == 2)) || token">
  4. [Dòng 31] (((type === 2 || type === '2' || _b || (onePaymentMethod && d_domestic)) && (!tokenList.length || type == 2))) && !(promotionList && promotionList.length > 0) && !token

!= (4 điều kiện):
  1. [Dòng 5] (((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b) || (promotionList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token
  2. [Dòng 18] feeService['atm']['fee'] != 0"
  3. [Dòng 24] ((((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b) || (promotionList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token)
  4. [Dòng 33] ((((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token)

================================================================================

📁 FILE 156: domescard-form-promotion.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/domescard-form-promotion/domescard-form-promotion.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 157: intercard-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 1] type === 1"
  2. [Dòng 2] [ngStyle]="{'border-color': type === 1 ? this.themeConfig.border_color : border_color}"

!= (1 điều kiện):
  1. [Dòng 26] feeService['visa_mastercard_d']['fee'] != 0"

================================================================================

📁 FILE 158: intercard-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 159: mobile-wallet-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/mobile-wallet-form/mobile-wallet-form.component.html
📊 Thống kê: 9 điều kiện duy nhất
   - === : 3 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 1] type === paymentType"
  2. [Dòng 2] [ngStyle]="{'border-color': (type === paymentType
  3. [Dòng 37] type === paymentType

== (6 điều kiện):
  1. [Dòng 4] promotionList?.length == 0"
  2. [Dòng 5] *ngIf="(!token) || (type == paymentType)">
  3. [Dòng 10] paymentType == PaymentType.ApplePay"
  4. [Dòng 16] paymentType == PaymentType.GooglePay"
  5. [Dòng 22] paymentType == PaymentType.SamsungPay"
  6. [Dòng 37] promotionList?.length == 0)">

================================================================================

📁 FILE 160: mobile-wallet-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/mobile-wallet-form/mobile-wallet-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 161: paypal-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 1] type === 3"
  2. [Dòng 2] [ngStyle]="{'border-color': type === 3 ? this.themeConfig.border_color : border_color}"

!= (1 điều kiện):
  1. [Dòng 15] feeService['pp']['fee'] != 0"

================================================================================

📁 FILE 162: paypal-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 163: qr-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/qr-form/qr-form.component.html
📊 Thống kê: 16 điều kiện duy nhất
   - === : 5 lần
   - == : 5 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 1] type === 4"
  2. [Dòng 2] [ngStyle]="{'border-color': (type === 4
  3. [Dòng 29] *ngIf="((type === 4
  4. [Dòng 29] type === '4'
  5. [Dòng 52] *ngIf="(((type === 4

== (5 điều kiện):
  1. [Dòng 5] uniqueTokenBank) || (type == 4
  2. [Dòng 29] ((type === 4 || type === '4' || (onePaymentMethod && d_qr)) || token) && (!tokenList.length || type == 4) && (promotionList && promotionList.length > 0 && type == 4)
  3. [Dòng 29] type == 4)">
  4. [Dòng 31] uniqueTokenBank) || (type == 2
  5. [Dòng 52] (((type === 4 || type === '4' || (onePaymentMethod && d_qr)) && (!tokenList.length || type == 4)) || token) && !(promotionList && promotionList.length > 0)

!= (6 điều kiện):
  1. [Dòng 5] (((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b) || (promotionList.length > 0 && !_b)) && ((type != 4 || uniqueTokenBank) || (type == 4 && !_b))) && !token
  2. [Dòng 18] feeService['qr']['fee'] != 0"
  3. [Dòng 31] ((((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b) || (promotionList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token)
  4. [Dòng 37] ((((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b) || (promotionList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token) && !version2
  5. [Dòng 40] ((((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b) || (promotionList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token) && version2
  6. [Dòng 54] ((((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token)

================================================================================

📁 FILE 164: qr-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/sorting-option/qr-form/qr-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 165: token-expired-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/token-expired-dialog/token-expired-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 166: token-expired-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/token-expired-dialog/token-expired-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 167: remove-token-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/token-main/remove-token-dialog/remove-token-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 168: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/token-main/token-dialog/dialog-guide-dialog.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (6 điều kiện):
  1. [Dòng 9] data['type'] == 'Visa'
  2. [Dòng 9] data['type'] == 'Master'
  3. [Dòng 9] data['type'] == 'JCB'"
  4. [Dòng 17] data['type'] == 'Visa'"
  5. [Dòng 17] data['type'] == 'Master'"
  6. [Dòng 24] data['type'] == 'Amex'"

================================================================================

📁 FILE 169: token-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/token-main/token-main.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 1 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 9] item.brand_id === 'visa' ? {'height': '14.42px', 'width': '44.67px'

!= (2 điều kiện):
  1. [Dòng 22] item['feeService']['fee'] != 0
  2. [Dòng 24] item['feeService']['fee'] != 0"

================================================================================

📁 FILE 170: token-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/main/menu/token-main/token-main.component.ts
📊 Thống kê: 76 điều kiện duy nhất
   - === : 8 lần
   - == : 43 lần
   - !== : 3 lần
   - != : 22 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 117] if (event.keyCode === 13) {
  2. [Dòng 292] token.brand_id === 'atm'
  3. [Dòng 502] && ((token.brand_id === 'amex'
  4. [Dòng 585] return csc != null && !isNaN(+csc) && ((brand_id === 'amex'
  5. [Dòng 611] return id === 'amex' ? '1234' : '123'
  6. [Dòng 775] if (approval.method === 'REDIRECT') {
  7. [Dòng 778] } else if (approval.method === 'POST_REDIRECT') {
  8. [Dòng 849] return this._i_token_otp != null && !isNaN(+this._i_token_otp) && ((id === 'amex'

== (43 điều kiện):
  1. [Dòng 156] if (message == '0') {
  2. [Dòng 206] if (result == 'success') {
  3. [Dòng 211] if (this.tokenList.length == 0) {
  4. [Dòng 218] } else if (result == 'error') {
  5. [Dòng 330] if (_re.status == '200'
  6. [Dòng 330] _re.status == '201') {
  7. [Dòng 331] _re.body.state == 'success')
  8. [Dòng 332] _re.body.data.discount_amount == 0))) {
  9. [Dòng 352] _re.body.state == 'fail') || _re.body.data.discount_amount <= 0) {
  10. [Dòng 502] _val.value.trim().length == 4) || (token.brand_id != 'amex'
  11. [Dòng 502] _val.value.trim().length == 3))
  12. [Dòng 585] csc.trim().length == 4)
  13. [Dòng 586] csc.trim().length == 3));
  14. [Dòng 610] id == 'amex' ? this.maxLength = 4 : this.maxLength = 3
  15. [Dòng 647] if (_re.body.state == 'more_info_required') {
  16. [Dòng 669] if (this._res_post.state == 'approved'
  17. [Dòng 669] this._res_post.state == 'failed') {
  18. [Dòng 676] if (this._res_post.state == 'failed') {
  19. [Dòng 692] } else if (this._res_post.state == 'authorization_required') {
  20. [Dòng 693] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  21. [Dòng 705] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  22. [Dòng 721] } else if (_re.body.state == 'authorization_required') {
  23. [Dòng 738] if (this._b == 1
  24. [Dòng 738] this._b == 14
  25. [Dòng 738] this._b == 15
  26. [Dòng 738] this._b == 24
  27. [Dòng 738] this._b == 8
  28. [Dòng 738] this._b == 10
  29. [Dòng 738] this._b == 20
  30. [Dòng 738] this._b == 22
  31. [Dòng 738] this._b == 23
  32. [Dòng 738] this._b == 30
  33. [Dòng 738] this._b == 11
  34. [Dòng 738] this._b == 17
  35. [Dòng 738] this._b == 18
  36. [Dòng 738] this._b == 27
  37. [Dòng 738] this._b == 5
  38. [Dòng 738] this._b == 12
  39. [Dòng 738] this._b == 9) {
  40. [Dòng 797] } else if (_re.body.state == 'failed') {
  41. [Dòng 841] if (action == 'blur') {
  42. [Dòng 849] this._i_token_otp.trim().length == 4)
  43. [Dòng 850] this._i_token_otp.trim().length == 3));

!== (3 điều kiện):
  1. [Dòng 178] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  2. [Dòng 293] token.brand_id !== 'atm'
  3. [Dòng 376] if (item.id !== token_id) {

!= (22 điều kiện):
  1. [Dòng 146] if (params['locale'] != null) {
  2. [Dòng 501] if (_val.value != null
  3. [Dòng 552] if (ua.indexOf('safari') != -1
  4. [Dòng 585] return csc != null
  5. [Dòng 586] || (brand_id != 'amex'
  6. [Dòng 596] this._i_name != ''
  7. [Dòng 671] if (this._res_post.return_url != null) {
  8. [Dòng 673] } else if (this._res_post.links != null
  9. [Dòng 673] this._res_post.links.merchant_return != null
  10. [Dòng 673] this._res_post.links.merchant_return.href != null) {
  11. [Dòng 726] if (_re.body.authorization != null
  12. [Dòng 726] _re.body.authorization.links != null
  13. [Dòng 733] if (_re.body.links != null
  14. [Dòng 733] _re.body.links.cancel != null) {
  15. [Dòng 799] if (_re.body.return_url != null) {
  16. [Dòng 801] } else if (_re.body.links != null
  17. [Dòng 801] _re.body.links.merchant_return != null
  18. [Dòng 801] _re.body.links.merchant_return.href != null) {
  19. [Dòng 832] return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
  20. [Dòng 837] if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(item.brand_id) != -1
  21. [Dòng 849] return this._i_token_otp != null
  22. [Dòng 850] || (id != 'amex'

================================================================================

📁 FILE 171: bnpl-management.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/model/bnpl-management.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 172: homecredit-management.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/model/homecredit-management.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 173: kbank-management.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/model/kbank-management.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 174: overlay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/overlay/overlay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 175: overlay.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/overlay/overlay.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 176: overlay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/overlay/overlay.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 177: bank-amount.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/pipe/bank-amount.pipe.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 11] return ((a.id == id
  2. [Dòng 11] a.code == id) && a.type.includes(type));

================================================================================

📁 FILE 178: auth.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/auth.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 179: close-dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/close-dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 180: data.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/data.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 112] const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
  2. [Dòng 112] item.method === method) : null;

================================================================================

📁 FILE 181: deep_link.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/deep_link.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 9] if (isIphone == true) {
  2. [Dòng 22] } else if (isAndroid == true) {

================================================================================

📁 FILE 182: dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 183: digital-wallet.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/digital-wallet.service.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 184] element.value == 'true'

================================================================================

📁 FILE 184: fee.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/fee.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 185: focus-input.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/focus-input.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 186: handle_bnpl_token.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/handle_bnpl_token.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 187: multiple_method.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/multiple_method.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 188: payment.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/payment.service.ts
📊 Thống kê: 13 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 11 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 650] return countPayment == maxPayment
  2. [Dòng 693] if (this.getLatestPayment().state == 'canceled')

!= (11 điều kiện):
  1. [Dòng 134] if (idInvoice != null
  2. [Dòng 134] idInvoice != 0)
  3. [Dòng 144] idInvoice != 0) {
  4. [Dòng 322] if (this._merchantid != null
  5. [Dòng 322] this._tranref != null
  6. [Dòng 322] this._state != null
  7. [Dòng 402] let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
  8. [Dòng 438] urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
  9. [Dòng 460] if (paymentId != null) {
  10. [Dòng 561] if (res?.status != 200
  11. [Dòng 561] res?.status != 201) return;

================================================================================

📁 FILE 189: production-main.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/production-main.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 190: promotion.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/promotion.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 191: qr.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/qr.service.ts
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 58] if (res?.state == 'canceled') {
  2. [Dòng 99] state == 'authorization_required'

!= (3 điều kiện):
  1. [Dòng 40] if (_re.status != '200'
  2. [Dòng 40] _re.status != '201') {
  3. [Dòng 49] latestPayment?.state != "authorization_required") {

================================================================================

📁 FILE 192: time-stop.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/time-stop.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 193: token-main.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/services/token-main.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 194: success.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/success/success.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 52] amigo_type == 'SP'"
  2. [Dòng 96] amigo_type == 'PL'"

================================================================================

📁 FILE 195: success.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/success/success.component.ts
📊 Thống kê: 10 điều kiện duy nhất
   - === : 5 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 137] params.timeout === 'true') {
  2. [Dòng 156] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  3. [Dòng 156] _re.body.state === 'unpaid');
  4. [Dòng 219] params.name === 'CUSTOMER_INTIME'
  5. [Dòng 219] params.code === '09') {

== (3 điều kiện):
  1. [Dòng 167] if (this.res.currencies[0] == 'USD') {
  2. [Dòng 195] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
  3. [Dòng 206] this.res.themes.theme == 'general') {

!= (2 điều kiện):
  1. [Dòng 193] } else if (this.res.payments[this.res.payments.length - 1].instrument.issuer.brand != null
  2. [Dòng 194] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id != null

================================================================================

📁 FILE 196: index.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/translate/index.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 197: lang-en.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/translate/lang-en.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 198: lang-vi.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/translate/lang-vi.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 199: translate.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/translate/translate.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 200: translate.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/translate/translate.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 41] if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
  2. [Dòng 42] else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';

================================================================================

📁 FILE 201: translations.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/translate/translations.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 202: apps-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/util/apps-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 546] if (e.name == bankSwift) { // TODO: get by swift
  2. [Dòng 554] return this.apps.find(e => e.code == appCode);

================================================================================

📁 FILE 203: apps-information.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/util/apps-information.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 204: banks-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/util/banks-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1093] if (+e.id == bankId) {
  2. [Dòng 1143] if (e.swiftCode == bankSwift) {

================================================================================

📁 FILE 205: bnpl-providers.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/util/bnpl-providers.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 206: error-handler.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/util/error-handler.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 9] err?.status === 400
  2. [Dòng 10] err?.error?.name === 'INVALID_CARD_FEE'

================================================================================

📁 FILE 207: iso-ca-states.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/util/iso-ca-states.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 208: iso-us-states.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/util/iso-us-states.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 209: util.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/app/util/util.ts
📊 Thống kê: 40 điều kiện duy nhất
   - === : 16 lần
   - == : 17 lần
   - !== : 3 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (16 điều kiện):
  1. [Dòng 70] if (v.length === 2
  2. [Dòng 70] this.flag.length === 3
  3. [Dòng 70] this.flag.charAt(this.flag.length - 1) === '/') {
  4. [Dòng 74] if (v.length === 1) {
  5. [Dòng 76] } else if (v.length === 2) {
  6. [Dòng 79] v.length === 2) {
  7. [Dòng 87] if (len === 2) {
  8. [Dòng 187] if (M[1] === 'Chrome') {
  9. [Dòng 410] if (param === key) {
  10. [Dòng 533] pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
  11. [Dòng 537] target === 0
  12. [Dòng 614] if (cardTypeBank === 'bank_card_number') {
  13. [Dòng 617] } else if (cardTypeBank === 'bank_account_number') {
  14. [Dòng 667] if (event.keyCode === 8
  15. [Dòng 667] event.key === "Backspace"
  16. [Dòng 725] if (target.tagName === 'A'

== (17 điều kiện):
  1. [Dòng 20] if (temp.length == 0) {
  2. [Dòng 27] return (counter % 10 == 0);
  3. [Dòng 161] if (this.checkCount == 1) {
  4. [Dòng 173] if (results == null) {
  5. [Dòng 206] if (c.length == 3) {
  6. [Dòng 219] d = d == undefined ? '.' : d
  7. [Dòng 220] t = t == undefined ? '
  8. [Dòng 398] return results == null ? null : results[1]
  9. [Dòng 667] event.inputType == 'deleteContentBackward') {
  10. [Dòng 668] if (event.target.name == 'exp_date'
  11. [Dòng 676] event.inputType == 'insertCompositionText') {
  12. [Dòng 690] if (((_val.length == 4
  13. [Dòng 690] _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  14. [Dòng 690] _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  15. [Dòng 716] iss_date.length == 4
  16. [Dòng 716] iss_date.search('/') == -1)
  17. [Dòng 717] iss_date.length == 5))

!== (3 điều kiện):
  1. [Dòng 405] queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
  2. [Dòng 406] if (queryString !== '') {
  3. [Dòng 537] if (target !== 0

!= (4 điều kiện):
  1. [Dòng 189] if (tem != null) {
  2. [Dòng 194] if ((tem = ua.match(/version\/(\d+)/i)) != null) {
  3. [Dòng 612] if (ua.indexOf('safari') != -1
  4. [Dòng 669] if (v.length != 3) {

================================================================================

📁 FILE 210: apple.js
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/assets/script/apple.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 0 lần
   - == : 7 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (7 điều kiện):
  1. [Dòng 42] if (typeof promotionPayment?.promotion_amount == 'number') {
  2. [Dòng 105] if(document.getElementById('applepay-merchantAVS').value == 'true'){
  3. [Dòng 159] response.status == '400') {
  4. [Dòng 161] } else if (response.status == '500') {
  5. [Dòng 172] if (data.state == "fail") { // apply promotion fail
  6. [Dòng 177] } else if (data.name == "CARD_TYPE_CAN_NOT_BE_PROCESSED") { // in case response.status == 400
  7. [Dòng 184] } else if (data.state == "approved"){ // in case response.ok

!= (2 điều kiện):
  1. [Dòng 98] if (network != "napas") return true;
  2. [Dòng 99] if (currency != "VND") return false; // napas accept VND only

================================================================================

📁 FILE 211: google-pay-intergrate.js
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/assets/script/google-pay-intergrate.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 212: qrcode.js
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/assets/script/qrcode.js
📊 Thống kê: 67 điều kiện duy nhất
   - === : 2 lần
   - == : 53 lần
   - !== : 0 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 2255] if (typeof define === 'function'
  2. [Dòng 2257] } else if (typeof exports === 'object') {

== (53 điều kiện):
  1. [Dòng 68] if (_dataCache == null) {
  2. [Dòng 85] if ( (0 <= r && r <= 6 && (c == 0
  3. [Dòng 85] c == 6) )
  4. [Dòng 86] || (0 <= c && c <= 6 && (r == 0
  5. [Dòng 86] r == 6) )
  6. [Dòng 107] if (i == 0
  7. [Dòng 122] _modules[r][6] = (r % 2 == 0);
  8. [Dòng 129] _modules[6][c] = (c % 2 == 0);
  9. [Dòng 152] if (r == -2
  10. [Dòng 152] r == 2
  11. [Dòng 152] c == -2
  12. [Dòng 152] c == 2
  13. [Dòng 153] || (r == 0
  14. [Dòng 153] c == 0) ) {
  15. [Dòng 169] ( (bits >> i) & 1) == 1);
  16. [Dòng 226] if (col == 6) col -= 1;
  17. [Dòng 232] if (_modules[row][col - c] == null) {
  18. [Dòng 237] dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
  19. [Dòng 249] if (bitIndex == -1) {
  20. [Dòng 458] margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
  21. [Dòng 498] if (typeof arguments[0] == 'object') {
  22. [Dòng 587] margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
  23. [Dòng 733] if (b == -1) throw 'eof';
  24. [Dòng 741] if (b0 == -1) break;
  25. [Dòng 767] if (typeof b == 'number') {
  26. [Dòng 768] if ( (b & 0xff) == b) {
  27. [Dòng 910] return function(i, j) { return (i + j) % 2 == 0
  28. [Dòng 912] return function(i, j) { return i % 2 == 0
  29. [Dòng 914] return function(i, j) { return j % 3 == 0
  30. [Dòng 916] return function(i, j) { return (i + j) % 3 == 0
  31. [Dòng 918] return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
  32. [Dòng 920] return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
  33. [Dòng 922] return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
  34. [Dòng 924] return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
  35. [Dòng 1011] if (r == 0
  36. [Dòng 1011] c == 0) {
  37. [Dòng 1015] if (dark == qrcode.isDark(row + r, col + c) ) {
  38. [Dòng 1036] if (count == 0
  39. [Dòng 1036] count == 4) {
  40. [Dòng 1149] if (typeof num.length == 'undefined') {
  41. [Dòng 1155] num[offset] == 0) {
  42. [Dòng 1495] if (typeof rsBlock == 'undefined') {
  43. [Dòng 1538] return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
  44. [Dòng 1543] _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
  45. [Dòng 1599] if (data.length - i == 1) {
  46. [Dòng 1601] } else if (data.length - i == 2) {
  47. [Dòng 1866] } else if (n == 62) {
  48. [Dòng 1868] } else if (n == 63) {
  49. [Dòng 1928] if (_buflen == 0) {
  50. [Dòng 1937] if (c == '=') {
  51. [Dòng 1961] } else if (c == 0x2b) {
  52. [Dòng 1963] } else if (c == 0x2f) {
  53. [Dòng 2133] if (table.size() == (1 << bitLength) ) {

!= (12 điều kiện):
  1. [Dòng 119] if (_modules[r][6] != null) {
  2. [Dòng 126] if (_modules[6][c] != null) {
  3. [Dòng 144] if (_modules[row][col] != null) {
  4. [Dòng 365] while (buffer.getLengthInBits() % 8 != 0) {
  5. [Dòng 750] if (count != numChars) {
  6. [Dòng 751] throw count + ' != ' + numChars
  7. [Dòng 878] while (data != 0) {
  8. [Dòng 1733] if (test.length != 2
  9. [Dòng 1733] ( (test[0] << 8) | test[1]) != code) {
  10. [Dòng 1894] if (_length % 3 != 0) {
  11. [Dòng 2067] if ( (data >>> length) != 0) {
  12. [Dòng 2178] return typeof _map[key] != 'undefined'

================================================================================

📁 FILE 213: environment.development.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/environments/environment.development.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 214: environment.mtf.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/environments/environment.mtf.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 215: environment.prod.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/environments/environment.prod.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 216: environment.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/environments/environment.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 217: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 218: karma.conf.js
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/karma.conf.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 219: main.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/main.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 220: polyfills.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/polyfills.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 221: kbank-policy.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/template/kbank-policy/kbank-policy.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 327] _locale == 'vi'"
  2. [Dòng 1407] _locale == 'en'"

================================================================================

📁 FILE 222: kbank-policy.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/template/kbank-policy/kbank-policy.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 22] if (params['locale'] == 'vn') {
  2. [Dòng 24] } else if (params['locale'] == 'en') {

================================================================================

📁 FILE 223: test.ts
📍 Đường dẫn: /root/projects/onepay/paygate-promotion/src/test.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📋 TÓM TẮT TẤT CẢ ĐIỀU KIỆN DUY NHẤT:
====================================================================================================

=== (212 điều kiện duy nhất):
------------------------------------------------------------
1. return lang === this._translate.currentLang
2. isPopupSupport === 'True'"
3. params.timeout === 'true') {
4. this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
5. _re.body.state === 'unpaid');
6. if (this.paymentsNum === this.maxpayment) {
7. if (this.errorCode === 'overtime'
8. this.errorCode === '253') {
9. if (this.timeLeft === 0) {
10. if (YY % 400 === 0
11. YY % 4 === 0)) {
12. if (YYYY % 400 === 0
13. YYYY % 4 === 0)) {
14. return index === array.findIndex(obj => {
15. return JSON.stringify(obj) === _value
16. bnpl.code === 'kbank'"
17. bnpl.code === 'homepaylater'"
18. this.kredivoProvider = this.bankAmountArr.find(item => item.code === 'kredivo');
19. this.bnpls.push(this.sublist.find(e => e.name === 'KBank Pay Later'));
20. this.bnpls.push(this.sublist.find(e => e.name === 'Home PayLater'));
21. this.bnpls.push(this.sublist.find(e => e.name === 'Kredivo'));
22. this.bnpls.push(this.sublist.find(e => e.name === 'Insta'));
23. object.id === value ? 'select-option-active' : ''"
24. object.id === value"
25. if (this._locale === 'vi') {
26. if (this._locale === 'en') {
27. if (this.value === 'add') {
28. <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
29. [class.red_border_no_background]="c_expdate && (valueDate.length === 0
30. valueDate.trim().length === 0)"
31. if (isIE[0] === 'MSIE'
32. +isIE[1] === 10) {
33. if ((_val.value.substr(-1) === ' '
34. _val.value.length === 24) {
35. if (this.cardTypeBank === 'bank_card_number') {
36. } else if (this.cardTypeBank === 'bank_account_number') {
37. } else if (this.cardTypeBank === 'bank_username') {
38. } else if (this.cardTypeBank === 'bank_customer_code') {
39. this.cardTypeBank === 'bank_card_number'
40. if (this.cardTypeOcean === 'IB') {
41. } else if (this.cardTypeOcean === 'MB') {
42. if (_val.value.substr(0, 2) === '84') {
43. } else if (this.cardTypeOcean === 'ATM') {
44. if (this.cardTypeBank === 'bank_account_number') {
45. this.cardTypeBank === 'bank_card_number') {
46. if (this.cardTypeBank === 'bank_card_number'
47. if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
48. if (event.keyCode === 8
49. event.key === "Backspace"
50. if (v.length === 2
51. this.flag.length === 3
52. this.flag.charAt(this.flag.length - 1) === '/') {
53. if (v.length === 1) {
54. } else if (v.length === 2) {
55. v.length === 2) {
56. if (len === 2) {
57. if ((this.cardTypeBank === 'bank_account_number'
58. this.cardTypeBank === 'bank_username'
59. this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
60. this.cardTypeOcean === 'ATM')
61. || (this.cardTypeOcean === 'IB'
62. if (valIn === this._translate.instant('bank_card_number')) {
63. } else if (valIn === this._translate.instant('bank_account_number')) {
64. } else if (valIn === this._translate.instant('bank_username')) {
65. } else if (valIn === this._translate.instant('bank_customer_code')) {
66. if (_val.value === ''
67. _val.value === null
68. _val.value === undefined) {
69. if (_val.value && (this.cardTypeBank === 'bank_card_number'
70. if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
71. this.cardTypeOcean === 'MB') {
72. this.cardTypeOcean === 'IB'
73. if ((this.cardTypeBank === 'bank_card_number'
74. if (this.cardName === undefined
75. this.cardName === '') {
76. if (this.valueDate === undefined
77. this.valueDate === '') {
78. c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
79. _inExpDate.trim().length === 0)"
80. ready===1"
81. if (this.timeLeft === 10) {
82. if (this.runTime === true) {
83. if (this.runTime === true) this.submitCardBanking();
84. if (approval.method === 'REDIRECT') {
85. } else if (approval.method === 'POST_REDIRECT') {
86. if (this.timeLeft === 1) {
87. } else if (valIn === this._translate.instant('internet_banking')) {
88. if (_val.value && (this.cardTypeBank === 'bank_card_number')) {
89. if (focusElement === 'card_name') {
90. } else if (focusElement === 'exp_date'
91. focusExpDateElement === 'card_name') {
92. if (this.cardTypeBank === 'bank_account_number'
93. filteredData.length === 0"
94. if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
95. filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
96. if (valOut === 'auth') {
97. if (this._b === '1'
98. this._b === '20'
99. this._b === '64') {
100. if (this._b === '36'
101. this._b === '18'
102. this._b === '19'
103. if (this._b === '19'
104. this._b === '16'
105. this._b === '25'
106. this._b === '33'
107. this._b === '39'
108. this._b === '11'
109. this._b === '17'
110. this._b === '36'
111. this._b === '44'
112. this._b === '64'
113. if (this._b === '20'
114. if (this._b === '18') {
115. let bank_napas = banks.filter(item => item.swiftCode === (e.swiftCode + 'NP'));
116. this._b === '9'
117. this._b === '12'
118. if (this._b === '12'
119. this._b === '18') {
120. if (this.themeConfig && (this.themeConfig.csc_config === false)) {
121. if (_formCard.country === 'default') {
122. if ((v.substr(-1) === ' '
123. this._i_country_code === 'US') {
124. const insertIndex = this._i_country_code === 'US' ? 5 : 3
125. if (temp[i] === '-'
126. temp[i] === ' ') {
127. insertIndex === 3 ? ' ' : itemRemoved)
128. this.c_country = _val.value === 'default'
129. if (event.index === 1) {
130. *ngIf="(type === 5
131. type === 5"
132. [ngStyle]="{'border-color': (type === 5
133. d_vrbank===true"
134. item.brand_id === 'atm'
135. item.swift_code.slice(-2) === 'NP') {
136. if (tokenWithPromotion.filter(item => item.id === token.id).length === 0) {
137. let arrUrl = this.currentUrl.split('&').filter(item => item === 'type=2').length;
138. return arr.map(mapObj => mapObj["id"]).indexOf(obj["id"]) === pos
139. this.kredivoProvider = this.bankAmountArr.find(e => e.code === 'kredivo');
140. this.bnpls_promotion.push(this.sublist.find(e => e.name === 'Home PayLater'));
141. this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_bnpl + this.d_mobile_wallet === 1
142. if (this._res.state === 'unpaid'
143. this._res.state === 'not_paid') {
144. if ('op' === auth
145. } else if ('bank' === auth
146. return id === 'amex' ? '1234' : '123'
147. if (this.timeLeftPaypal === 0) {
148. if (GGPaySDKScript.readyState === "loaded"
149. GGPaySDKScript.readyState === "complete") {
150. isTop = window === window.top
151. return currency === 'VND'
152. style="{{item.brand_id === 'visa' ? 'height: 14.42px
153. item.promotionByToken.length === 0"
154. if (typeof(this.content) === 'string') {
155. filteredData.length === 0
156. filteredDataOther.length === 0"
157. filteredDataMobile.length === 0
158. filteredDataOtherMobile.length === 0"
159. this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
160. this.filteredDataOther = this.appList.filter(item => item.type === 'other');
161. this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
162. this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
163. item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
164. filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
165. if (item.type === 'mobile_banking') {
166. this.appList.length === 1
167. listVNPayQR.length === 0"
168. this.listWalletQR.length === 1) {
169. this.listWalletQR.length === 1
170. if (!this.d_vnpayqr_wallet && !this.d_vnpayqr_deeplink && (this.listWalletQR?.length === 1
171. this.listWalletDeeplink?.length === 1)) {
172. if (item.type === 'deeplink') {
173. this.listWalletQR?.length === 1
174. type === 5
175. type === 2"
176. [ngStyle]="{'border-color': (type === 2
177. [ngStyle]="{'border-color': type === 2 ? this.themeConfig.border_color : border_color}"
178. *ngIf="(((type === 2
179. type === '2'
180. type === 1"
181. [ngStyle]="{'border-color': type === 1 ? this.themeConfig.border_color : border_color}"
182. type === paymentType"
183. [ngStyle]="{'border-color': (type === paymentType
184. type === paymentType
185. type === 3"
186. [ngStyle]="{'border-color': type === 3 ? this.themeConfig.border_color : border_color}"
187. type === 4"
188. [ngStyle]="{'border-color': (type === 4
189. *ngIf="((type === 4
190. type === '4'
191. *ngIf="(((type === 4
192. item.brand_id === 'visa' ? {'height': '14.42px', 'width': '44.67px'
193. if (event.keyCode === 13) {
194. token.brand_id === 'atm'
195. && ((token.brand_id === 'amex'
196. return csc != null && !isNaN(+csc) && ((brand_id === 'amex'
197. return this._i_token_otp != null && !isNaN(+this._i_token_otp) && ((id === 'amex'
198. const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
199. item.method === method) : null;
200. params.name === 'CUSTOMER_INTIME'
201. params.code === '09') {
202. err?.status === 400
203. err?.error?.name === 'INVALID_CARD_FEE'
204. if (M[1] === 'Chrome') {
205. if (param === key) {
206. pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
207. target === 0
208. if (cardTypeBank === 'bank_card_number') {
209. } else if (cardTypeBank === 'bank_account_number') {
210. if (target.tagName === 'A'
211. if (typeof define === 'function'
212. } else if (typeof exports === 'object') {

== (808 điều kiện duy nhất):
------------------------------------------------------------
1. 'vi' == params['locale']) {
2. 'en' == params['locale']) {
3. errorCode == '11'"
4. isSent == false
5. <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
6. (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
7. *ngIf="!(errorCode == 'overtime'
8. errorCode == '253'
9. if (params && (params['bnpl'] == 'true')) {
10. _re.body.themes.theme == 'token') {
11. params.response_code == 'overtime') {
12. if (_re.status == '200'
13. _re.status == '201') {
14. if (_re2.status == '200'
15. _re2.status == '201') {
16. if (this.errorCode == 'overtime'
17. this.errorCode == '253') {
18. if (this.type == 'bnpl') {
19. if (this.provider == 'amigo'
20. this.errorCode == '2') {
21. else if (this.provider == 'kbank'
22. else if (this.provider == 'homecredit'
23. else if (this.provider == 'kredivo'
24. } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
25. if (lastPayment?.state == 'pending') {
26. if (this.isTimePause == false) {
27. if ((dataPassed.status == '200'
28. dataPassed.status == '201') && dataPassed.body != null) {
29. if (this.locale == 'en') {
30. if (name == 'MAFC')
31. if (bankId == 3
32. bankId == 61
33. bankId == 8
34. bankId == 49
35. bankId == 48
36. bankId == 10
37. bankId == 53
38. bankId == 17
39. bankId == 65
40. bankId == 23
41. bankId == 52
42. bankId == 27
43. bankId == 66
44. bankId == 9
45. bankId == 54
46. bankId == 37
47. bankId == 38
48. bankId == 39
49. bankId == 40
50. bankId == 42
51. bankId == 44
52. bankId == 72
53. bankId == 59
54. bankId == 51
55. bankId == 64
56. bankId == 58
57. bankId == 56
58. bankId == 55
59. bankId == 60
60. bankId == 68
61. bankId == 74
62. bankId == 75 //KEB HANA
63. bnplDetail.method == 'SP'"
64. bnplDetail.method == 'PL'"
65. _re.code == '0'
66. packageItem.product_code == productCode) {
67. a.product_code == 'SP') {
68. a.product_code == 'PL') {
69. if (this.selectedIndex == 0
70. } else if ((this.selectedIndex == 1
71. this.payLaterSubmit) || (this.selectedIndex == 0
72. item.prepaid_percent == this.selectedPrePaid
73. item.installment_month == this.selectedPayLater) {
74. if (string == 'SP') {
75. } else if (string == 'PL') {
76. if (this._locale == 'en') {
77. if (this._res_post.state == 'approved'
78. this._res_post.state == 'failed') {
79. if (this._res_post.state == 'failed') {
80. } else if (this._res_post.state == 'authorization_required') {
81. if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
82. bnpl.status == 'disabled'"
83. bnpl.status == 'active'"
84. [class.bnpl-active-item]="bnpl.status == 'active'" (click)="onClick(bnpl.status == 'disabled')">
85. bnpl.code == 'homepaylater'"
86. bnpl.status == 'disabled'
87. bnpl.code == 'kbank'
88. bnpl.code == 'insta'"
89. bnpl.code == 'kredivo'
90. selectedBnpl.code == 'homepaylater'"
91. selectedBnpl.code == 'kbank'"
92. selectedBnpl.code == 'insta'"
93. selectedBnpl.code == 'kredivo'"
94. _auth == 1"
95. value == 'add' ? 'select-option-active' : ''"
96. value == 'add'"
97. value=='add'"
98. value == 'add') || !isOnepayPolicy" [disabled]="(isInvalid()
99. value == 'add') || !isOnepayPolicy">
100. if (response.status == '200'
101. response.status == '201') {
102. this.listTokenBnpl.length == 0) {
103. if (this.value == object.id) {
104. if (name == 'email') {
105. if (name == 'phoneNumber') {
106. if (name == 'fullname') {
107. _re.body.state == 'success')
108. _re.body.data.discount_amount == 0))) {
109. _re.body.state == 'fail'
110. this._res_post.state == 'authorization_required') {
111. this._res_post.code == 'KB-02') {
112. !cmndBlur ? 'no-border' : ((kbankDetail['citizen_id'] && !(kbankDetail['citizen_id'].length == 9 || kbankDetail['citizen_id'].length == 12) || !kbankDetail['citizen_id']) &&  bnplForm.controls['citizen_id'].touched) ? 'ng-invalid ng-dirty' : ''
113. bnplForm.controls['citizen_id'].touched && kbankDetail['citizen_id'] && !(kbankDetail['citizen_id'].length == 9 || kbankDetail['citizen_id'].length == 12) && cmndBlur
114. this.value == 'add')){
115. list = this.data.customer.tokens.filter((item) => item.instrument.issuer.brand.id == 'kbank')
116. return (this.bnplForm.invalid || (this.kbankDetail['citizen_id'] && !(this.kbankDetail['citizen_id'].length == 9) && !(this.kbankDetail['citizen_id'].length == 12)));
117. if (name == 'citizen_id') {
118. "applicable": (type == "6"
119. type == "12") ? "premium_users" : "basic_or_premium_users"
120. this.bnplDetail.fullname?.length == 0 ? this._translate.instant('kredivo_empty_fullname_message')
121. this.bnplDetail.email?.length == 0 ? this._translate.instant('kredivo_empty_email_message')
122. else if (this._res.code == '2') {
123. _re.body.payments[_re.body.payments.length - 1].reason.code == 'B4') {
124. if (this._b == 18) {//8-MB Bank;18-oceanbank
125. bnpl.code == 'kbank'"
126. bnpl.code == 'homepaylater'
127. bnpl.status == 'active'
128. if (bnpl.status == 'disabled') {
129. this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
130. if (this._b == 18
131. this._b == 19) {
132. if (this._b == 19) {//19BIDV
133. } else if (this._b == 3
134. this._b == 27
135. this._b == 5) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
136. if (this._b == 27
137. this._b == 5) {
138. } else if (this._b == 12) {// 12SHB
139. } else if (this._b == 18) { //18Oceanbank-ocb
140. if (this._b == 19
141. this._b == 3
142. this._b == 12
143. } else if (this._b == 18) {
144. if (this.checkBin(_val.value) && (this._b == 3
145. this._b == 5)) {
146. if (this._b == 3) {
147. this.cardTypeOcean == 'ATM') {
148. if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
149. this._b == 18)) {
150. this._b == 12) {
151. if (this.checkBin(v) && (this._b == 3
152. this._b == 27)) {
153. if (this._b == 27) {
154. event.inputType == 'deleteContentBackward') {
155. if (event.target.name == 'exp_date'
156. event.inputType == 'insertCompositionText') {
157. if (((this.valueDate.length == 4
158. this.valueDate.search('/') == -1) || this.valueDate.length == 5)
159. this.valueDate.length == 5)
160. if (temp.length == 0) {
161. return (counter % 10 == 0);
162. } else if (this._b == 19) {
163. } else if (this._b == 27) {
164. } else if (this._b == 5) {
165. if (this._b == 12) {
166. if (this.cardTypeBank == 'bank_customer_code') {
167. } else if (this.cardTypeBank == 'bank_account_number') {
168. _formCard.exp_date.length == 5
169. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
170. this._b == 3)) {//27-pvcombank;3-TPB ;
171. if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
172. this._b == 19
173. this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
174. if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
175. if (this.cardTypeOcean == 'IB') {
176. } else if (this.cardTypeOcean == 'MB') {
177. } else if (this.cardTypeOcean == 'ATM') {
178. this.token_site == 'onepay'
179. if (this._b == 18) {
180. this._b == 18) {
181. if ((cardNo.length == 16
182. if ((cardNo.length == 16 || (cardNo.length == 19
183. && ((this._b == 18
184. cardNo.length == 19) || this._b != 18)
185. if (this._b == +e.id) {
186. if (valIn == 1) {
187. } else if (valIn == 2) {
188. if (this._b == 19) {
189. if (cardType == this._translate.instant('internetbanking')
190. } else if (cardType == this._translate.instant('mobilebanking')
191. } else if (cardType == this._translate.instant('atm')
192. this._b == 18))) {
193. } else if (this._b == 18
194. this.c_expdate = !(((this.valueDate.length == 4
195. this.valueDate.length == 4
196. this.valueDate.search('/') == -1)
197. this.valueDate.length == 5))
198. <div [ngStyle]="{'margin-top': !checkTwoEnabled ? '15px' : '0px' }" *ngIf="(cardTypeBank == 'bank_card_number') &&(_b == 67
199. (cardTypeBank == 'bank_card_number') &&(_b == 67 || checkTwoEnabled)&& ready===1
200. <div [ngStyle]="{'margin-top': !checkTwoEnabled ? '15px' : '0px' }" class="nd-bank-card-two" *ngIf="(cardTypeBank == 'internet_banking')&&(_b == 2
201. if (this._b == 67
202. this._b == 2) {//19BIDV
203. if (this._b == 2
204. if (this._b == 67) {
205. return this._b == 2
206. this._b == 67
207. _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
208. this.checkMod10(cardNo) == true
209. this._b == 3) {
210. if (this._b != 68 || (this._b == 68
211. return ((value.length == 4
212. value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
213. value.length == 5) && parseInt(value.split('/')[0]
214. || (this._util.checkValidExpireMonthYear(value) && (this._b == 2
215. this._b == 20
216. this._b == 33
217. this._b == 39
218. this._b == 43
219. this._b == 45
220. this._b == 64
221. this._b == 68
222. this._b == 72))) //sonnh them Vietbank 72
223. this._inExpDate.length == 4
224. this._inExpDate.search('/') == -1)
225. this._inExpDate.length == 5))
226. || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 20
227. this._b == 2
228. this._b == 72)));
229. if (this._b == 8) {//MB Bank
230. if (this._b == 18) {//Oceanbank
231. if (this._b == 8) {
232. if (this._b == 12) { //SHB
233. } else if (this._res.state == 'authorization_required') {
234. if (this.challengeCode == '') {
235. <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_card_number')">
236. <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_account_number')">
237. token_site == 'onepay'
238. if(this._b == 12) this.isShbGroup = true;
239. return this._b == 9
240. this._b == 11
241. this._b == 16
242. this._b == 17
243. this._b == 25
244. this._b == 44
245. this._b == 57
246. this._b == 59
247. this._b == 61
248. this._b == 63
249. this._b == 69
250. if (this._b == 12
251. this.cardTypeBank == 'bank_account_number') {
252. cardNo.length == 19) && cardNo.startsWith("970443") && this._util.checkMod10(cardNo) && this.checkBin(cardNo) ) {
253. this._b == 31
254. this._b == 80) {
255. if (this._b == 2) {
256. } else if (this._b == 6) {
257. } else if (this._b == 31) {
258. } else if (this._b == 80) {
259. this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : false
260. if (this._b == 5) {
261. this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
262. this._b == 5)) {//27-pvcombank;3-TPB ;
263. this._b == 5)) {//3-TPB ;19-BIDV;27-pvcombank;
264. this._b == 18
265. _b==68"
266. if (this._b == 1
267. this._b == 36
268. this._b == 55
269. this._b == 47
270. this._b == 48
271. this._b == 59) {
272. if (message == '1') {
273. return this._b == 3
274. this._b == 9
275. return this._b == 6
276. this._b == 5
277. return this._b == 12
278. return this._b == 11
279. this._b == 73
280. this._b == 72
281. this._b == 74
282. this._b == 75
283. _re.body.state == 'fail') || _re.body.data.discount_amount <= 0) {
284. } else if (this._b == 2) {
285. this._b == 14
286. this._b == 15
287. this._b == 24
288. this._b == 8
289. this._b == 10
290. this._b == 22
291. this._b == 23
292. this._b == 30
293. this._b == 32
294. this._b == 9) {
295. (cardNo.length == 19
296. (cardNo.length == 19 && (this._b == 1
297. this._b == 4
298. this._b == 5))
299. this._util.checkMod10(cardNo) == true
300. result.data == 'approved') {
301. _auth==1) || (token
302. _b == 16)"
303. (token || _auth==1) && _b != 16
304. _auth==1)">
305. ((!token && _auth==0 && vietcombankGroupSelected) || (token && _b==16))
306. _b==16))">
307. !token && _auth==0 && techcombankGroupSelected
308. !token && _auth==0 && shbGroupSelected
309. !token && _auth==0 && onepaynapasGroupSelected
310. !token && _auth==0 && bankaccountGroupSelected
311. !token && _auth==0 && vibbankGroupSelected
312. if (e.id == '31') {
313. } else if (e.id == '80') {
314. this.filteredData.length == 1
315. if ($event && ($event == 'true'
316. if (bankId == 1
317. bankId == 4
318. bankId == 7
319. bankId == 11
320. bankId == 14
321. bankId == 15
322. bankId == 16
323. bankId == 20
324. bankId == 22
325. bankId == 24
326. bankId == 25
327. bankId == 30
328. bankId == 33
329. bankId == 34
330. bankId == 35
331. bankId == 36
332. bankId == 41
333. bankId == 43
334. bankId == 45
335. bankId == 46
336. bankId == 47
337. bankId == 50
338. bankId == 57
339. bankId == 62
340. bankId == 63
341. bankId == 69
342. bankId == 70
343. bankId == 71
344. bankId == 73
345. bankId == 32
346. bankId == 75) {
347. } else if (bankId == 6
348. bankId == 31
349. bankId == 80) {
350. } else if (bankId == 2
351. bankId == 67) {
352. } else if (bankId == 3
353. bankId == 18
354. bankId == 19
355. bankId == 27) {
356. } else if (bankId == 5) {
357. } else if (bankId == 12) {
358. this._b == '55'
359. this._b == '47'
360. this._b == '48'
361. this._b == '59'
362. this._b == '73'
363. this._b == '12') {
364. this._b == '3'
365. this._b == '43'
366. this._b == '45'
367. this._b == '57'
368. this._b == '61'
369. this._b == '63'
370. this._b == '67'
371. this._b == '68'
372. this._b == '69'
373. this._b == '9'
374. this._b == '74'
375. this._b == '75') {
376. this._b == '36'
377. if (item['id'] == this._b) {
378. this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
379. type == 'tpbank_account'
380. type == 'bidv_account'
381. type == 'pvcombank_account'
382. type == 'shb_account'
383. type == 'oceanbank_online_account'
384. type == 'oceanbank_mobile_account') {
385. _re.body.state == 'success') {
386. _re.body.state == 'fail') {
387. _re.body.state == 'fail') || (!_re.body.data.discount_accept_zero
388. return this._b == 18
389. if (parseInt(b) == parseInt(v.substring(0, 6))) {
390. } else if (this._b == 67) {
391. if(this._b == 12){
392. _auth==0
393. <div *ngIf="(!token&&_auth==0&&(_b==1
394. _b==4
395. _b==7
396. _b==8
397. _b==9
398. _b==10
399. _b==11
400. _b==14
401. _b==15
402. _b==16
403. _b==17
404. _b==20
405. _b==22
406. _b==23
407. _b==24
408. _b==25
409. _b==30
410. _b==33
411. _b==34
412. _b==35
413. _b==36
414. _b==37
415. _b==38
416. _b==39
417. _b==40
418. _b==41
419. _b==42
420. _b==43
421. _b==44
422. _b==45
423. this._b==46
424. _b==47
425. _b==48
426. _b==49
427. _b==50
428. _b==51
429. _b==52
430. _b==53
431. _b==54
432. _b==55
433. _b==56
434. _b==57
435. _b==58
436. _b==59
437. _b==60
438. _b==61
439. _b==62
440. _b==63
441. _b==64
442. this._b==65
443. _b==66
444. _b==67
445. _b==68
446. _b==69
447. this._b==70
448. this._b==71)) || (token && _b == 16)">
449. _b == 16)">
450. !token&&_auth==0&&(_b==6 || _b==2 || _b==5 || _b == 31 || _b == 80)
451. _b == 80)">
452. !token&&_auth==0&&(_b==3||_b==12||_b==18||_b==19||_b==27)
453. this._auth == 0
454. this.tokenList.length == 0
455. this.promotionList.length == 0) {
456. if (item['id'] == this._b.toString()) {
457. this._b == '59') {
458. this._b == '54'
459. this._showCardName = this._showCardName && (this.card_name_token == '');
460. } else if (this._res_post.state == 'failed') { // lỗi mới kiểm tra sang trang lỗi
461. if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
462. } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
463. v.length == 15) || (v.length == 16
464. v.length == 19))
465. this._util.checkMod10(v) == true) {
466. cardNo.length == 15)
467. cardNo.length == 16)
468. || (cardNo.startsWith('6') && (cardNo.length == 16
469. cardNo.length == 19))
470. this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
471. this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
472. v.length == 5) {
473. v.length == 4
474. v.length == 3)
475. _val.value.length == 4
476. _val.value.length == 3)
477. this.requireAvs = this.AVS_COUNTRIES.find(e => e.code == this._i_country_code) ? true : false;
478. this._i_csc.length == 4) ||
479. this._i_csc.length == 3)
480. country = this.AVS_COUNTRIES.find(e => e.code == res.bin_country);
481. countryCode == 'US' ? US_STATES
482. : countryCode == 'CA' ? CA_STATES
483. _re.body.state == 'fail')
484. cardNo.startsWith('81')) && (cardNo.length == 16
485. !token) || (type == 1
486. !token) || (type == 6
487. type == 2)"
488. !token) || (type == 3
489. !token) || (type == 5
490. method?.trim()=='International'
491. method.trim()=='ApplePay'"
492. method.trim()=='GooglePay'"
493. method.trim()=='SamsungPay'"
494. method?.trim()=='Domestic'
495. method?.trim()=='QR'
496. method?.trim()=='Paypal'
497. <div *ngIf="((onePaymentMethod == true
498. d_bnpl) || (onePaymentMethod == false
499. d_bnpl_number == 1))
500. method?.trim()=='Bnpl'
501. providerType == bnpl.code"
502. onePaymentMethod == true
503. d_bnpl_number == 1"
504. d_bnpl_number == 1
505. onePaymentMethod == false
506. providerType == bnpl.code)
507. || (d_bnpl_number == 1
508. providerType == bnpl.code)))
509. promotionList.length == 0) && bnpl.status !== 'disabled')">
510. bnpl.code == 'kredivo'"
511. _auth == 1
512. [class.enable_form]="!onePaymentMethod || (onePaymentMethod && d_bnpl)" *ngIf="_auth == 1 && ((onePaymentMethod == true
513. d_bnpl_number == 1)
514. if (el == 5) {
515. } else if (el == 6) {
516. } else if (el == 7) {
517. } else if (el == 8) {
518. } else if (el == 2) {
519. } else if (el == 4) {
520. } else if (el == 3) {
521. if (!isNaN(_re.status) && (_re.status == '200'
522. _re.status == '201') && _re.body != null) {
523. if (('closed' == this._res_polling.state
524. 'canceled' == this._res_polling.state
525. 'expired' == this._res_polling.state)
526. } else if ('paid' == this._res_polling.state) {
527. this._res_polling.payments == null
528. if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'authorization_required') {
529. } else if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
530. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
531. this._paymentService.getCurrentPage() == 'enter_card'
532. } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
533. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
534. } else if ('not_paid' == this._res_polling.state) {
535. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
536. this.errorRefreshed = params['error_refresh'] == true
537. params['error_refresh'] == 'true' ? true : false
538. this.version2 = _re.body?.merchant?.qr_version == "2"
539. if (this.type == 5
540. } else if (this.type == 6
541. } else if (this.type == 2
542. } else if (this.type == 7
543. } else if (this.type == 8
544. } else if (this.type == 4
545. } else if (this.type == 3
546. if (this.themeConfig.default_method == 'International'
547. } else if (this.themeConfig.default_method == 'Domestic'
548. } else if (this.themeConfig.default_method == 'QR'
549. } else if (this.themeConfig.default_method == 'Paypal'
550. tokenWithPromotion.length == 0)) {
551. if (this.onePaymentMethod == true
552. this.d_bnpl == 1) {
553. if (this.d_amigo == true
554. this.d_insta == false
555. this.d_instaplus == false) {
556. if (this.d_amigo_number == 0
557. this.d_insta_number == 1) {
558. else if (this.d_amigo_number == 0
559. this.d_instaplus_number == 1) {
560. if ((this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_bnpl + this.d_mobile_wallet) == 0
561. this._auth == 0) {
562. if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
563. this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
564. if (this._res.payments[this._res.payments.length - 1].state == 'approved'
565. } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
566. if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required') {
567. } else if (this._res.payments[this._res.payments.length - 1].state == 'pending') {
568. } else if (this._res.payments[this._res.payments.length - 1].state == 'canceled') {
569. } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
570. this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
571. if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
572. } else if (idBrand == 'atm'
573. } else if (idBrand == 'kbank'
574. detail.merchant.id == 'AMWAY') {
575. this._res.payments[this._res.payments.length - 1].state == 'pending'
576. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
577. if ('paid' == this._res.state) {
578. this._res.merchant.token_site == 'onepay')) {
579. if (('closed' == this._res.state
580. 'canceled' == this._res.state
581. 'expired' == this._res.state
582. 'paid' == this._res.state)
583. this._res.payments == null) {
584. this._res.payments[this._res.payments.length - 1].state == 'pending') {
585. if (this._res.currencies[0] == 'USD') {
586. if (item.instrument.issuer.brand.id == 'atm') {
587. } else if (item.instrument.issuer.brand.id == 'visa'
588. item.instrument.issuer.brand.id == 'mastercard') {
589. if (item.instrument.issuer_location == 'd') {
590. } else if (item.instrument.issuer.brand.id == 'amex') {
591. } else if (item.instrument.issuer.brand.id == 'jcb') {
592. uniq.length == 1) {
593. if (element.accept_instrument == null
594. element.accept_instrument == '') {
595. return merchant.token_cvv == true
596. return (merchant.token_name == true)
597. return (merchant.token_email == true)
598. return (merchant.token_phone == true)
599. if (type == 'qrv1') {
600. if (type == 'mobile') {
601. e.type == 'ewallet'
602. e.code == 'momo')) {
603. } else if (type == 'desktop') {
604. e.type == 'vnpayqr') || (regex.test(strTest)
605. _val == 2) {
606. if (_val == 2
607. } else if (_val == 2
608. _val == 2
609. if (this.type == 4) {
610. filteredData.length == 1) {
611. if (col == 2) {
612. } else if (col == 1) {
613. } else if (col == 3) {
614. if (data._locale == 'en') {
615. if (event.data?.event_type == 'applepay_network_not_supported') {
616. } else if (event.data?.event_type == 'applepay_promotion_fail') {
617. } else if (event.data?.event_type == 'show_button_applepay') {
618. } else if (event.data?.event_type == 'hide_button_applepay') {
619. } else if (event.data?.event_type == 'close_overlay') {
620. if (res?.body?.state == 'approved') {
621. } else if (res?.body?.state == 'authorization_required') {
622. paymentType == PaymentType.ApplePay"
623. paymentType == PaymentType.GooglePay"
624. paymentType == PaymentType.SamsungPay"
625. if (this.paymentType == PaymentType.ApplePay) {
626. if (network == 'napas'
627. serviceId: this.environment == 'PRODUCTION'? this.serviceIdProd : this.serviceIdTest
628. data['type'] == 'Visa'
629. data['type'] == 'Master'
630. data['type'] == 'JCB'"
631. data['type'] == 'Visa'"
632. data['type'] == 'Master'"
633. data['type'] == 'Amex'"
634. (item.international + item.domescard + item.qr + item.mobileWallet) == 0
635. item.bnpl == 1
636. item.active == true"
637. bnpls.length == 0"
638. d_homecredit == 1
639. <div *ngIf="(bnpl.status == 'active')">
640. item.international == 1"
641. item.domescard == 1"
642. item.qr == 1"
643. item.bnpl == 1"
644. item.mobileWallet == 1"
645. item.international + item.domescard + item.qr + item.bnpl + item.mobileWallet == 1
646. if (message == '0') {
647. this.themeConfig.enable_payment_method == "false"
648. this.promotionList?.length == 1) {
649. if (item.id == data.id) {
650. if (position == 1) {
651. } else if (position == 2) {
652. else if (position == 4) {
653. } else if (position == 5) {
654. item.id == element.id ? element['active'] = true : element['active'] = false
655. if (item.id == element.id
656. } else if (item.id == element.id
657. item.display_cvv == true ? this.flagToken = true : this.flagToken = false
658. $event == 'true') {
659. screen=='qr'"
660. screen=='confirm_close'"
661. this.themeConfig.deeplink_status == 'Off' ? false : true
662. if (item.available == true) {
663. if (appcode == 'grabpay'
664. appcode == 'momo') {
665. if (type == 2
666. if (result.data == 'approved') {
667. err.error.code == '04') {
668. e.type == 'vnpayqr') {
669. e.type == 'ewallet') {
670. e.type == 'wallet')) {
671. if (d.b.code == s) {
672. type == 'vnpay'"
673. type == 'bankapp'"
674. type == 'both'"
675. _locale=='vi'"
676. _locale=='en'"
677. _locale == 'vi'"
678. _locale == 'en'"
679. e.type == 'deeplink') {
680. if (e.type == 'ewallet') {
681. this.listWallet.length == 1
682. this.listWallet[0].code == 'momo') {
683. this.checkEWalletDeeplink.length == 0) {
684. arrayWallet.length == 0) return false;
685. if (arrayWallet[i].code == key) {
686. if (this.locale == 'vi') {
687. !auth) || (type == 2
688. !token) || tokenList?.length > 0 || (tokenList.length == 0
689. !token) || tokenList?.length > 0 || (tokenList?.length == 0
690. uniqueTokenBank) || (type == 2
691. (((type === 2 || type === '2' || _b || (onePaymentMethod && d_domestic))) && (!tokenList.length || type == 2) && (promotionList && promotionList.length > 0 && type == 2)) || token
692. type == 2)) || token">
693. (((type === 2 || type === '2' || _b || (onePaymentMethod && d_domestic)) && (!tokenList.length || type == 2))) && !(promotionList && promotionList.length > 0) && !token
694. promotionList?.length == 0"
695. *ngIf="(!token) || (type == paymentType)">
696. promotionList?.length == 0)">
697. uniqueTokenBank) || (type == 4
698. ((type === 4 || type === '4' || (onePaymentMethod && d_qr)) || token) && (!tokenList.length || type == 4) && (promotionList && promotionList.length > 0 && type == 4)
699. type == 4)">
700. (((type === 4 || type === '4' || (onePaymentMethod && d_qr)) && (!tokenList.length || type == 4)) || token) && !(promotionList && promotionList.length > 0)
701. if (result == 'success') {
702. if (this.tokenList.length == 0) {
703. } else if (result == 'error') {
704. _val.value.trim().length == 4) || (token.brand_id != 'amex'
705. _val.value.trim().length == 3))
706. csc.trim().length == 4)
707. csc.trim().length == 3));
708. id == 'amex' ? this.maxLength = 4 : this.maxLength = 3
709. if (_re.body.state == 'more_info_required') {
710. } else if (_re.body.state == 'authorization_required') {
711. } else if (_re.body.state == 'failed') {
712. if (action == 'blur') {
713. this._i_token_otp.trim().length == 4)
714. this._i_token_otp.trim().length == 3));
715. return ((a.id == id
716. a.code == id) && a.type.includes(type));
717. if (isIphone == true) {
718. } else if (isAndroid == true) {
719. element.value == 'true'
720. return countPayment == maxPayment
721. if (this.getLatestPayment().state == 'canceled')
722. if (res?.state == 'canceled') {
723. state == 'authorization_required'
724. amigo_type == 'SP'"
725. amigo_type == 'PL'"
726. if (this.res.currencies[0] == 'USD') {
727. this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
728. this.res.themes.theme == 'general') {
729. if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
730. else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';
731. if (e.name == bankSwift) { // TODO: get by swift
732. return this.apps.find(e => e.code == appCode);
733. if (+e.id == bankId) {
734. if (e.swiftCode == bankSwift) {
735. if (this.checkCount == 1) {
736. if (results == null) {
737. if (c.length == 3) {
738. d = d == undefined ? '.' : d
739. t = t == undefined ? '
740. return results == null ? null : results[1]
741. if (((_val.length == 4
742. _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
743. _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
744. iss_date.length == 4
745. iss_date.search('/') == -1)
746. iss_date.length == 5))
747. if (typeof promotionPayment?.promotion_amount == 'number') {
748. if(document.getElementById('applepay-merchantAVS').value == 'true'){
749. response.status == '400') {
750. } else if (response.status == '500') {
751. if (data.state == "fail") { // apply promotion fail
752. } else if (data.name == "CARD_TYPE_CAN_NOT_BE_PROCESSED") { // in case response.status == 400
753. } else if (data.state == "approved"){ // in case response.ok
754. if (_dataCache == null) {
755. if ( (0 <= r && r <= 6 && (c == 0
756. c == 6) )
757. || (0 <= c && c <= 6 && (r == 0
758. r == 6) )
759. if (i == 0
760. _modules[r][6] = (r % 2 == 0);
761. _modules[6][c] = (c % 2 == 0);
762. if (r == -2
763. r == 2
764. c == -2
765. c == 2
766. || (r == 0
767. c == 0) ) {
768. ( (bits >> i) & 1) == 1);
769. if (col == 6) col -= 1;
770. if (_modules[row][col - c] == null) {
771. dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
772. if (bitIndex == -1) {
773. margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
774. if (typeof arguments[0] == 'object') {
775. margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
776. if (b == -1) throw 'eof';
777. if (b0 == -1) break;
778. if (typeof b == 'number') {
779. if ( (b & 0xff) == b) {
780. return function(i, j) { return (i + j) % 2 == 0
781. return function(i, j) { return i % 2 == 0
782. return function(i, j) { return j % 3 == 0
783. return function(i, j) { return (i + j) % 3 == 0
784. return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
785. return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
786. return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
787. return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
788. if (r == 0
789. c == 0) {
790. if (dark == qrcode.isDark(row + r, col + c) ) {
791. if (count == 0
792. count == 4) {
793. if (typeof num.length == 'undefined') {
794. num[offset] == 0) {
795. if (typeof rsBlock == 'undefined') {
796. return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
797. _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
798. if (data.length - i == 1) {
799. } else if (data.length - i == 2) {
800. } else if (n == 62) {
801. } else if (n == 63) {
802. if (_buflen == 0) {
803. if (c == '=') {
804. } else if (c == 0x2b) {
805. } else if (c == 0x2f) {
806. if (table.size() == (1 << bitLength) ) {
807. if (params['locale'] == 'vn') {
808. } else if (params['locale'] == 'en') {

!== (44 điều kiện duy nhất):
------------------------------------------------------------
1. this.lastValue !== $event.target.value)) {
2. if (YY % 400 === 0 || (YY % 100 !== 0
3. if (YYYY % 400 === 0 || (YYYY % 100 !== 0
4. bnpl.code !== 'kbank'
5. this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
6. selectedBnpl.status !== 'disabled'"
7. codeResponse.toString() !== '0') {
8. event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
9. key !== '3') {
10. cardNo.length !== 0) {
11. if (this.cardTypeBank !== 'bank_card_number') {
12. if (this.cardTypeBank !== 'bank_account_number') {
13. if (this.cardTypeBank !== 'bank_username') {
14. if (this.cardTypeBank !== 'bank_customer_code') {
15. this.lb_card_account !== this._translate.instant('ocb_account')) {
16. this.lb_card_account !== this._translate.instant('ocb_phone')) {
17. this.lb_card_account !== this._translate.instant('ocb_card')) {
18. this._b !== 18) || (this.cardTypeOcean === 'ATM'
19. let _b = this._b !== 67 ? 67 : this._b
20. if (this.cardTypeBank !== 'internet_banking') {
21. this._b !== 18)) {
22. show_condition && _b !== 0
23. strTestNapas !== 'card
24. if (this.tracking.hasOwnProperty(key) && ((key !== '8'
25. !this._showNameOnCard) || (key !== '9'
26. codeResponse.toString() !== '0'){
27. event.inputType !== 'deleteContentBackward') || v.length == 5) {
28. this._i_country_code !== 'US') {
29. itemRemoved !== '') {
30. if (deviceValue !== 'default') {
31. this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
32. this._i_country_code !== 'default'
33. this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
34. this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {
35. key !== '8') {
36. bnpl.code !== 'kbank'"
37. bnpl.status !== 'disabled')">
38. if (_val !== 3) {
39. this._util.getElementParams(this.currentUrl, 't_id') !== '') {
40. token.brand_id !== 'atm'
41. if (item.id !== token_id) {
42. queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
43. if (queryString !== '') {
44. if (target !== 0

!= (256 điều kiện duy nhất):
------------------------------------------------------------
1. if (params['locale'] != null
2. } else if (params['locale'] != null
3. if (userLang != null
4. if(value!=null){
5. if (message != ''
6. message != null
7. message != undefined) {
8. if (this._idInvoice != null
9. this._idInvoice != 0) {
10. if (this._paymentService.getInvoiceDetail() != null) {
11. dataPassed.body != null) {
12. this.amount_promotion = promotionPassData.data.discount_amount != 0 ? (this._util.formatCurrency(promotionPassData.data.discount_amount).toString() + ' ' + currency) : ''
13. dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
14. dataPassed.body.themes.border_color != true ? dataPassed.body.themes.border_color : ''
15. if (this._translate.currentLang != language) {
16. selectedBnpl.status != 'disabled'"
17. bnplDetail.method != 'SP'"
18. bnplDetail.method != 'PL'"
19. 'total_paid': this.data?.amount ? (item.diff_amount != 0 ? this._util.formatMoney(parseFloat(item.diff_amount) + parseFloat(this.data?.amount), 0, '.', ',') : this._util.formatMoney(parseFloat(this.data?.amount), 0, '.', ',')) : 0,
20. if (this._res_post.return_url != null) {
21. } else if (this._res_post.links != null
22. this._res_post.links.merchant_return != null
23. this._res_post.links.merchant_return.href != null) {
24. _auth != 1"
25. bnpl.code != 'homepaylater'"
26. && ((homecreidtDetail['phoneNumber'].length != 10) || !this.homecreidtDetail['phoneNumber'].startsWith('0'))
27. this.value = (this.listTokenBnpl.length != 0) ? this.listTokenBnpl[0]['id'] : "add";
28. this.listTokenBnpl = this.listTokenBnpl.filter(item => item.id != result.id);
29. if (ua.indexOf('safari') != -1
30. if (this._res_post.authorization != null
31. this._res_post.authorization.links != null
32. if (this._res_post.links != null
33. this._res_post.links.cancel != null) {
34. this._res_post.authorization.links.approval != null
35. this._res_post.authorization.links.approval.href != null) {
36. userName = paramUserName != null ? paramUserName : ''
37. document.activeElement.id!='fullname'"
38. document.activeElement.id!='phoneNumber'"
39. document.activeElement.id!='email'"
40. this.bnplDetail.phoneNumber?.length != 10 ?
41. if (this._res.links != null
42. this._res.links.merchant_return != null
43. this._res.links.merchant_return.href != null) {
44. if (!(_formCard.otp != null
45. if (!(_formCard.password != null
46. bnpl.code != 'kbank'
47. } else if (this._b != 18) {
48. if (this.htmlDesc != null
49. if (_val.value != '') {
50. this.auth_method != null) {
51. if (this.valueDate.length != 3) {
52. if (_formCard.exp_date != null
53. if (this.cardName != null
54. this._res_post.authorization.links.approval != null) {
55. this._b != 27
56. this._b != 12
57. this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
58. this._b != 18)
59. if (this._b != 18
60. this._b != 19) {
61. if (this._inExpDate.length != 3) {
62. let userName = _formCard.name != null ? _formCard.name : ''
63. this._b != 3))
64. if (this._b != 68
65. this._b != 2
66. this._b != 20
67. this._b != 33
68. this._b != 39
69. this._b != 43
70. this._b != 45
71. this._b != 64
72. this._b != 67
73. this._b != 68
74. this._b != 72)
75. d_card_date && (_b != 3 && _b != 19 && _b != 12 && _b != 18)
76. _b != 18)">
77. *ngIf="(_b != 12
78. (_b != 12 && _b != 18 && _b != 6 && _b != 2 && _b != 5 && _b != 48 && _b != 67 && _b != 57)
79. _b != 57)">
80. _b != 6 && _b != 2 && _b != 5 && _b != 48 && _b != 67 && _b != 57
81. this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
82. if (this._b != 3
83. this._b != 9
84. this._b != 16
85. this._b != 17
86. this._b != 18
87. this._b != 19
88. this._b != 25
89. this._b != 44
90. this._b != 57
91. this._b != 59
92. this._b != 61
93. this._b != 63
94. this._b != 69
95. this._b != 6
96. this._b != 5
97. this._b != 48
98. this._b != 57) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
99. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '73','74','75'].indexOf(this._b.toString()) != -1) {
100. if (params['locale'] != null) {
101. if ('otp' != this._paymentService.getCurrentPage()) {
102. if (!(strInstrument != null
103. if (strInstrument.substring(0, 1) != '^'
104. strInstrument.substr(strInstrument.length - 1) != '$') {
105. if (bankid != null) {
106. d_card_date && (_b != 3 && _b != 19 && _b != 18)
107. *ngIf="(_b != 18
108. (_b != 18 && _b != 6 && _b != 2 && _b != 57)
109. _b != 6 && _b != 2 && _b != 67 && _b != 57
110. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '74', '75'].indexOf(this._b.toString()) != -1) {
111. cardNo != null
112. v != null
113. this.c_csc = (!(_val.value != null
114. this._i_csc != null
115. this.requireAvs = this.isAvsCountry = country != undefined
116. themeConfig.enable_payment_method != 'false'
117. _auth != '1'"
118. <div *ngIf="bnpl.code == 'homepaylater' && (selectedBnpl != bnpl)"
119. this._paymentService.getState() != 'error') {
120. if (this._paymentService.getCurrentPage() != 'otp') {
121. _re.body != null) {
122. this._res_polling.links != null
123. this._res_polling.links.merchant_return != null //
124. } else if (this._res_polling.merchant != null
125. this._res_polling.merchant_invoice_reference != null
126. } else if (this._res_polling.payments != null
127. this._res_polling.payments[this._res_polling.payments.length - 1].instrument.issuer.brand.id != 'amigo') {
128. this._res_polling.links.merchant_return != null//
129. this._res_polling.payments != null
130. if (this._res_polling.payments != null
131. t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
132. this.type.toString().length != 0) {
133. params['url_redirect'] != '' ? params['url_redirect'] : ''
134. this.amount_promotion = promotionPassData.data.discount_amount != 0 ? (this._util.formatCurrency(promotionPassData.data.discount_amount).toString() + ' ' + this.currency) : ''
135. if (count != 1) {
136. if (this._res.merchant != null
137. this._res.merchant_invoice_reference != null) {
138. if (this._res.merchant.address_details != null) {
139. this._res.links != null//
140. } else if (this._res.payments != null
141. this._res.payments[this._res.payments.length - 1].instrument != null
142. this._res.payments[this._res.payments.length - 1].instrument.issuer != null
143. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
144. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
145. this._res.payments[this._res.payments.length - 1].links != null
146. this._res.payments[this._res.payments.length - 1].links.cancel != null
147. this._res.payments[this._res.payments.length - 1].links.cancel.href != null
148. this._res.payments[this._res.payments.length - 1].links.update != null
149. this._res.payments[this._res.payments.length - 1].links.update.href != null) {
150. this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
151. this._res.payments[this._res.payments.length - 1].authorization != null
152. this._res.payments[this._res.payments.length - 1].authorization.links != null) {
153. this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
154. this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
155. if (this._res.payments[this._res.payments.length - 1].authorization != null
156. this._res.payments[this._res.payments.length - 1].authorization.links != null
157. auth = paramUserName != null ? paramUserName : ''
158. if (this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
159. this._res.links != null
160. this._res.links.merchant_return != null //
161. } else if (this._res.merchant != null
162. this._res.merchant_invoice_reference != null
163. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != 'amigo') {
164. if (!(s_paygate != null
165. if (s_paygate.substring(0, 1) != '^'
166. s_paygate.substr(s_paygate.length - 1) != '$') {
167. if (['mastercard', 'visa', 'amex', 'jcb', 'cup', 'card', 'np_card', 'atm'].indexOf(id) != -1) {
168. } else if (['techcombank_account', 'vib_account', 'dongabank_account', 'tpbank_account', 'bidv_account', 'pvcombank_account', 'shb_account', 'oceanbank_online_account'].indexOf(id) != -1) {
169. } else if (['oceanbank_mobile_account'].indexOf(id) != -1) {
170. } else if (['shb_customer_id'].indexOf(id) != -1) {
171. if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1) {
172. e.type != 'ewallet') || (regex.test(strTest)
173. } else if (this._res.payments != null) {
174. if (event.origin != window.origin) return; // chỉ nhận message từ OnePay
175. if (event.data?.state != 'success'
176. if (_re.body?.state != "success") {
177. if (this.paymentType != PaymentType.ApplePay) return;
178. if (this.paymentType != PaymentType.ApplePay) return false;
179. if (network != 'napas'
180. *ngIf="item.checkbox_international || (item.mobileWallet != 1
181. item.domescard != 1
182. item.qr != 1
183. item.bnpl != 1)"
184. *ngIf="item.checkbox_domescard || (item.mobileWallet != 1
185. item.international != 1
186. *ngIf="item.checkbox_qr || (item.mobileWallet != 1
187. *ngIf="item.checkbox_bnpl || (item.mobileWallet != 1
188. item.qr != 1)"
189. *ngIf="item.checkbox_mobileWallet || (item.bnpl != 1
190. if (appcode != null) {
191. if (_re.status != '200'
192. _re.status != '201')
193. 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {
194. qr_version2 != 'None'"
195. qr_version2 != 'None'
196. if (this.translate.currentLang != language) {
197. feeService['atm']['fee'] != 0"
198. (((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b) || (promotionList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token
199. ((((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b) || (promotionList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token)
200. ((((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token)
201. feeService['visa_mastercard_d']['fee'] != 0"
202. feeService['pp']['fee'] != 0"
203. (((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b) || (promotionList.length > 0 && !_b)) && ((type != 4 || uniqueTokenBank) || (type == 4 && !_b))) && !token
204. feeService['qr']['fee'] != 0"
205. ((((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b) || (promotionList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token) && !version2
206. ((((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b) || (promotionList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token) && version2
207. item['feeService']['fee'] != 0
208. item['feeService']['fee'] != 0"
209. if (_val.value != null
210. return csc != null
211. || (brand_id != 'amex'
212. this._i_name != ''
213. if (_re.body.authorization != null
214. _re.body.authorization.links != null
215. if (_re.body.links != null
216. _re.body.links.cancel != null) {
217. if (_re.body.return_url != null) {
218. } else if (_re.body.links != null
219. _re.body.links.merchant_return != null
220. _re.body.links.merchant_return.href != null) {
221. return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
222. if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(item.brand_id) != -1
223. return this._i_token_otp != null
224. || (id != 'amex'
225. if (idInvoice != null
226. idInvoice != 0)
227. idInvoice != 0) {
228. if (this._merchantid != null
229. this._tranref != null
230. this._state != null
231. let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
232. urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
233. if (paymentId != null) {
234. if (res?.status != 200
235. res?.status != 201) return;
236. _re.status != '201') {
237. latestPayment?.state != "authorization_required") {
238. } else if (this.res.payments[this.res.payments.length - 1].instrument.issuer.brand != null
239. this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id != null
240. if (tem != null) {
241. if ((tem = ua.match(/version\/(\d+)/i)) != null) {
242. if (v.length != 3) {
243. if (network != "napas") return true;
244. if (currency != "VND") return false; // napas accept VND only
245. if (_modules[r][6] != null) {
246. if (_modules[6][c] != null) {
247. if (_modules[row][col] != null) {
248. while (buffer.getLengthInBits() % 8 != 0) {
249. if (count != numChars) {
250. throw count + ' != ' + numChars
251. while (data != 0) {
252. if (test.length != 2
253. ( (test[0] << 8) | test[1]) != code) {
254. if (_length % 3 != 0) {
255. if ( (data >>> length) != 0) {
256. return typeof _map[key] != 'undefined'

