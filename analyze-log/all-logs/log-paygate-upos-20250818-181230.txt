====================================================================================================
TRÍCH XUẤT ĐIỀU KIỆN SO SÁNH TỪ CÁC FILE TYPESCRIPT/JAVASCRIPT/HTML
====================================================================================================
Thư mục/File nguồn: /root/projects/onepay/paygate-upos/src
Thời gian: 18:12:30 18/8/2025
Tổng số file xử lý: 19
Tổng số file bị bỏ qua: 0
Tổng số điều kiện tìm thấy: 107

THỐNG KÊ TỔNG QUAN THEO LOẠI TOÁN TỬ:
--------------------------------------------------
Strict equality (===): 64 lần
Loose equality (==): 24 lần
Strict inequality (!==): 3 lần
Loose inequality (!=): 16 lần
--------------------------------------------------

DANH SÁCH FILE ĐÃ XỬ LÝ:
--------------------------------------------------
1. authService.js (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/services/authService.js
2. cookieService.js (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/services/cookieService.js
3. dataService.js (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/services/dataService.js
4. httpClient.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/services/httpClient.js
5. multipleMethodService.js (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/services/multipleMethodService.js
6. paymentService.js (20 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/services/paymentService.js
7. qrService.js (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/services/qrService.js
8. timestopService.js (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/services/timestopService.js
9. toastrService.js (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/services/toastrService.js
10. translateService.js (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/services/translateService.js
11. vietqrService.js (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/services/vietqrService.js
12. test.js (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/test.js
13. lang-en.js (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/translate/lang-en.js
14. lang-vi.js (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/translate/lang-vi.js
15. apps-info.js (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/util/apps-info.js
16. banks-info.js (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/util/banks-info.js
17. util.js (26 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/util/util.js
18. main.js (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-upos/src/main.js
19. index.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-upos/src/router/index.js

====================================================================================================
CHI TIẾT TỪNG FILE:
====================================================================================================

📁 FILE 1: authService.js
📍 Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/services/authService.js
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 34] ('object' === typeof window
  2. [Dòng 34] 'object' === typeof self
  3. [Dòng 34] 'object' === typeof global

================================================================================

📁 FILE 2: cookieService.js
📍 Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/services/cookieService.js
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 23] ('object' === typeof window
  2. [Dòng 23] 'object' === typeof self
  3. [Dòng 23] 'object' === typeof global

================================================================================

📁 FILE 3: dataService.js
📍 Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/services/dataService.js
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 98] ('object' === typeof window
  2. [Dòng 98] 'object' === typeof self
  3. [Dòng 98] 'object' === typeof global

================================================================================

📁 FILE 4: httpClient.js
📍 Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/services/httpClient.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 3 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 73] ('object' === typeof window
  2. [Dòng 73] 'object' === typeof self
  3. [Dòng 73] 'object' === typeof global

== (1 điều kiện):
  1. [Dòng 8] res["headers"].get = (key) => res["headers"][Object.keys(res["headers"]).find(k => k.toLowerCase() == key.toLowerCase())];

================================================================================

📁 FILE 5: multipleMethodService.js
📍 Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/services/multipleMethodService.js
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 37] ('object' === typeof window
  2. [Dòng 37] 'object' === typeof self
  3. [Dòng 37] 'object' === typeof global

================================================================================

📁 FILE 6: paymentService.js
📍 Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/services/paymentService.js
📊 Thống kê: 20 điều kiện duy nhất
   - === : 5 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 11 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 839] err?.status === 400
  2. [Dòng 840] err?.error?.name === 'INVALID_CARD_FEE'
  3. [Dòng 857] ('object' === typeof window
  4. [Dòng 857] 'object' === typeof self
  5. [Dòng 857] 'object' === typeof global

== (4 điều kiện):
  1. [Dòng 744] if (res.status == '200'
  2. [Dòng 744] res.status == '201') {
  3. [Dòng 778] return countPayment == maxPayment
  4. [Dòng 821] if (this.getLatestPayment().state == 'canceled')

!= (11 điều kiện):
  1. [Dòng 129] typeof cacheData != "object") {
  2. [Dòng 159] if (idInvoice != null
  3. [Dòng 159] idInvoice != 0)
  4. [Dòng 180] idInvoice != 0) {
  5. [Dòng 393] this._merchantid != null
  6. [Dòng 395] this._tranref != null
  7. [Dòng 396] this._state != null
  8. [Dòng 515] this._url_cancel_webtest + (idInvoice != null ? idInvoice : "");
  9. [Dòng 645] if (res?.status != 200
  10. [Dòng 645] res?.status != 201) return;
  11. [Dòng 658] if (paymentId != null) {

================================================================================

📁 FILE 7: qrService.js
📍 Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/services/qrService.js
📊 Thống kê: 8 điều kiện duy nhất
   - === : 3 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 127] ('object' === typeof window
  2. [Dòng 127] 'object' === typeof self
  3. [Dòng 127] 'object' === typeof global

== (2 điều kiện):
  1. [Dòng 54] if (res?.body?.state == 'canceled') {
  2. [Dòng 99] state == 'authorization_required'

!= (3 điều kiện):
  1. [Dòng 31] if (_re.status != '200'
  2. [Dòng 31] _re.status != '201') {
  3. [Dòng 45] latestPayment?.state != "authorization_required") {

================================================================================

📁 FILE 8: timestopService.js
📍 Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/services/timestopService.js
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 26] ('object' === typeof window
  2. [Dòng 26] 'object' === typeof self
  3. [Dòng 26] 'object' === typeof global

================================================================================

📁 FILE 9: toastrService.js
📍 Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/services/toastrService.js
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 47] ('object' === typeof window
  2. [Dòng 47] 'object' === typeof self
  3. [Dòng 47] 'object' === typeof global

================================================================================

📁 FILE 10: translateService.js
📍 Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/services/translateService.js
📊 Thống kê: 6 điều kiện duy nhất
   - === : 3 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 71] ('object' === typeof window
  2. [Dòng 71] 'object' === typeof self
  3. [Dòng 71] 'object' === typeof global

== (3 điều kiện):
  1. [Dòng 41] if (this.currentLang == "en") curr_lang = "LANG_EN_NAME";
  2. [Dòng 42] else if (this.currentLang == "vi"
  3. [Dòng 42] this.currentLang == "")

================================================================================

📁 FILE 11: vietqrService.js
📍 Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/services/vietqrService.js
📊 Thống kê: 5 điều kiện duy nhất
   - === : 5 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 114] err?.status === 400
  2. [Dòng 115] err?.error?.name === 'INVALID_CARD_FEE'
  3. [Dòng 132] ('object' === typeof window
  4. [Dòng 132] 'object' === typeof self
  5. [Dòng 132] 'object' === typeof global

================================================================================

📁 FILE 12: test.js
📍 Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/test.js
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 2] typeof exports === 'object'
  2. [Dòng 3] typeof define === 'function'

!== (1 điều kiện):
  1. [Dòng 2] typeof module !== 'undefined' ? factory(exports) :

================================================================================

📁 FILE 13: lang-en.js
📍 Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/translate/lang-en.js
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 1372] ('object' === typeof window
  2. [Dòng 1372] 'object' === typeof self
  3. [Dòng 1372] 'object' === typeof global

================================================================================

📁 FILE 14: lang-vi.js
📍 Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/translate/lang-vi.js
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 1353] ('object' === typeof window
  2. [Dòng 1353] 'object' === typeof self
  3. [Dòng 1353] 'object' === typeof global

================================================================================

📁 FILE 15: apps-info.js
📍 Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/util/apps-info.js
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 1556] ('object' === typeof window
  2. [Dòng 1556] 'object' === typeof self
  3. [Dòng 1556] 'object' === typeof global

== (2 điều kiện):
  1. [Dòng 1539] if (e.name == bankSwift) {
  2. [Dòng 1548] return this.apps.find(e => e.code == appCode);

================================================================================

📁 FILE 16: banks-info.js
📍 Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/util/banks-info.js
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 1099] ('object' === typeof window
  2. [Dòng 1099] 'object' === typeof self
  3. [Dòng 1099] 'object' === typeof global

== (2 điều kiện):
  1. [Dòng 997] if (+e.id == bankId) {
  2. [Dòng 1072] if (e.swiftCode == bankSwift) {

================================================================================

📁 FILE 17: util.js
📍 Đường dẫn: /root/projects/onepay/paygate-upos/src/assets/util/util.js
📊 Thống kê: 26 điều kiện duy nhất
   - === : 12 lần
   - == : 10 lần
   - !== : 2 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (12 điều kiện):
  1. [Dòng 61] v.length === 2
  2. [Dòng 62] this.flag.length === 3
  3. [Dòng 63] this.flag.charAt(this.flag.length - 1) === "/"
  4. [Dòng 68] if (v.length === 1) {
  5. [Dòng 70] } else if (v.length === 2) {
  6. [Dòng 73] v.length === 2) {
  7. [Dòng 81] if (len === 2) {
  8. [Dòng 203] if (M[1] === "Chrome") {
  9. [Dòng 388] if (param === key) {
  10. [Dòng 610] ('object' === typeof window
  11. [Dòng 610] 'object' === typeof self
  12. [Dòng 610] 'object' === typeof global

== (10 điều kiện):
  1. [Dòng 16] if (temp.length == 0) {
  2. [Dòng 23] return counter % 10 == 0
  3. [Dòng 168] if (this.checkCount == 1) {
  4. [Dòng 185] if (results == null) {
  5. [Dòng 222] if (c.length == 3) {
  6. [Dòng 244] let numfix = (currency == 'USD'
  7. [Dòng 245] return this.formatNumber(amount, numfix, '.', ',') + (currency ? (currency == 'VND' ? 'đ' : '') : '');
  8. [Dòng 259] d = d == undefined ? "."
  9. [Dòng 260] t = t == undefined ? "
  10. [Dòng 375] return results == null ? null : results[1]

!== (2 điều kiện):
  1. [Dòng 383] sourceURL.indexOf("?") !== -1 ? sourceURL.split("?")[1] : ""
  2. [Dòng 384] if (queryString !== "") {

!= (2 điều kiện):
  1. [Dòng 205] if (tem != null) {
  2. [Dòng 210] if ((tem = ua.match(/version\/(\d+)/i)) != null) {

================================================================================

📁 FILE 18: main.js
📍 Đường dẫn: /root/projects/onepay/paygate-upos/src/main.js
📊 Thống kê: 1 điều kiện duy nhất
   - === : 1 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 99] if (typeof el.innerHTML === "string") {

================================================================================

📁 FILE 19: index.js
📍 Đường dẫn: /root/projects/onepay/paygate-upos/src/router/index.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📋 TÓM TẮT TẤT CẢ ĐIỀU KIỆN DUY NHẤT:
====================================================================================================

=== (17 điều kiện duy nhất):
------------------------------------------------------------
1. ('object' === typeof window
2. 'object' === typeof self
3. 'object' === typeof global
4. err?.status === 400
5. err?.error?.name === 'INVALID_CARD_FEE'
6. typeof exports === 'object'
7. typeof define === 'function'
8. v.length === 2
9. this.flag.length === 3
10. this.flag.charAt(this.flag.length - 1) === "/"
11. if (v.length === 1) {
12. } else if (v.length === 2) {
13. v.length === 2) {
14. if (len === 2) {
15. if (M[1] === "Chrome") {
16. if (param === key) {
17. if (typeof el.innerHTML === "string") {

== (24 điều kiện duy nhất):
------------------------------------------------------------
1. res["headers"].get = (key) => res["headers"][Object.keys(res["headers"]).find(k => k.toLowerCase() == key.toLowerCase())];
2. if (res.status == '200'
3. res.status == '201') {
4. return countPayment == maxPayment
5. if (this.getLatestPayment().state == 'canceled')
6. if (res?.body?.state == 'canceled') {
7. state == 'authorization_required'
8. if (this.currentLang == "en") curr_lang = "LANG_EN_NAME";
9. else if (this.currentLang == "vi"
10. this.currentLang == "")
11. if (e.name == bankSwift) {
12. return this.apps.find(e => e.code == appCode);
13. if (+e.id == bankId) {
14. if (e.swiftCode == bankSwift) {
15. if (temp.length == 0) {
16. return counter % 10 == 0
17. if (this.checkCount == 1) {
18. if (results == null) {
19. if (c.length == 3) {
20. let numfix = (currency == 'USD'
21. return this.formatNumber(amount, numfix, '.', ',') + (currency ? (currency == 'VND' ? 'đ' : '') : '');
22. d = d == undefined ? "."
23. t = t == undefined ? "
24. return results == null ? null : results[1]

!== (3 điều kiện duy nhất):
------------------------------------------------------------
1. typeof module !== 'undefined' ? factory(exports) :
2. sourceURL.indexOf("?") !== -1 ? sourceURL.split("?")[1] : ""
3. if (queryString !== "") {

!= (16 điều kiện duy nhất):
------------------------------------------------------------
1. typeof cacheData != "object") {
2. if (idInvoice != null
3. idInvoice != 0)
4. idInvoice != 0) {
5. this._merchantid != null
6. this._tranref != null
7. this._state != null
8. this._url_cancel_webtest + (idInvoice != null ? idInvoice : "");
9. if (res?.status != 200
10. res?.status != 201) return;
11. if (paymentId != null) {
12. if (_re.status != '200'
13. _re.status != '201') {
14. latestPayment?.state != "authorization_required") {
15. if (tem != null) {
16. if ((tem = ua.match(/version\/(\d+)/i)) != null) {

