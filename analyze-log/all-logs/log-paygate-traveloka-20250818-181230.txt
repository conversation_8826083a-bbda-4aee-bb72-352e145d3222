====================================================================================================
TRÍCH XUẤT ĐIỀU KIỆN SO SÁNH TỪ CÁC FILE TYPESCRIPT/JAVASCRIPT/HTML
====================================================================================================
Thư mục/File nguồn: /root/projects/onepay/paygate-traveloka/src
Thời gian: 18:12:30 18/8/2025
Tổng số file xử lý: 115
Tổng số file bị bỏ qua: 0
Tổng số điều kiện tìm thấy: 1409

THỐNG KÊ TỔNG QUAN THEO LOẠI TOÁN TỬ:
--------------------------------------------------
Strict equality (===): 242 lần
Loose equality (==): 775 lần
Strict inequality (!==): 49 lần
Loose inequality (!=): 343 lần
--------------------------------------------------

DANH SÁCH FILE ĐÃ XỬ LÝ:
--------------------------------------------------
1. 4en.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/4en.html
2. 4vn.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/4vn.html
3. app-routing.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/app-routing.module.ts
4. app.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/app.component.html
5. app.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/app.component.spec.ts
6. app.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/app.component.ts
7. app.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/app.module.ts
8. counter.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/counter.directive.spec.ts
9. counter.directive.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/counter.directive.ts
10. format-carno-input.derective.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/directives/format-carno-input.derective.ts
11. uppercase-input.directive.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/directives/uppercase-input.directive.ts
12. error.component.html (16 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/error/error.component.html
13. error.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/error/error.component.spec.ts
14. error.component.ts (41 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/error/error.component.ts
15. support-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/error/support-dialog/support-dialog.html
16. support-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/error/support-dialog/support-dialog.ts
17. format-date.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/format-date.directive.spec.ts
18. format-date.directive.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/format-date.directive.ts
19. main.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/main.component.html
20. main.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/main.component.spec.ts
21. main.component.ts (13 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/main.component.ts
22. app-result.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/app-result/app-result.component.html
23. app-result.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/app-result/app-result.component.ts
24. bank-amount-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
25. bank-amount-dialog.component.ts (34 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
26. policy-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.html
27. policy-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.ts
28. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/dialog-guide-dialog.html
29. bankaccount.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
30. bankaccount.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
31. bankaccount.component.ts (127 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
32. bank.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/model/bank.ts
33. oceanbank.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/oceanbank/oceanbank.component.html
34. oceanbank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/oceanbank/oceanbank.component.spec.ts
35. oceanbank.component.ts (70 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/oceanbank/oceanbank.component.ts
36. onepay-napas.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.html
37. onepay-napas.component.ts (115 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.ts
38. otp-auth.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
39. otp-auth.component.ts (18 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
40. shbbankaccount.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/shbbankaccount/shbbankaccount.component.html
41. shbbankaccount.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/shbbankaccount/shbbankaccount.component.spec.ts
42. shbbankaccount.component.ts (94 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/shbbankaccount/shbbankaccount.component.ts
43. techcombank.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
44. techcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
45. techcombank.component.ts (13 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
46. tpbankaccount.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/tpbankaccount/tpbankaccount.component.html
47. tpbankaccount.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/tpbankaccount/tpbankaccount.component.spec.ts
48. tpbankaccount.component.ts (68 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/tpbankaccount/tpbankaccount.component.ts
49. vibbank.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.html
50. vibbank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.spec.ts
51. vibbank.component.ts (97 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.ts
52. vietcombank.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
53. vietcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
54. vietcombank.component.ts (88 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
55. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
56. off-bank-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
57. off-bank-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
58. domescard-main.component.html (12 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/domescard-main.component.html
59. domescard-main.component.ts (147 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/domescard-main.component.ts
60. ewallet-main.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/ewallet-main/ewallet-main.component.html
61. ewallet-main.component.ts (24 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/ewallet-main/ewallet-main.component.ts
62. qr-ewallet.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/ewallet-main/qr-ewallet/qr-ewallet.component.html
63. qr-ewallet.component.ts (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/ewallet-main/qr-ewallet/qr-ewallet.component.ts
64. menu.component.html (16 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/menu.component.html
65. menu.component.ts (173 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/menu.component.ts
66. mobilebanking-main.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/mobilebanking-main/mobilebanking-main.component.html
67. mobilebanking-main.component.ts (31 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/mobilebanking-main/mobilebanking-main.component.ts
68. qr-vnpay-dialog.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/mobilebanking-main/qr-guide-dialog/qr-vnpay-dialog.html
69. qr-vnpay-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/mobilebanking-main/qr-guide-dialog/qr-vnpay-dialog.ts
70. list-bank-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/mobilebanking-main/qr-listbank/list-bank-dialog.html
71. list-bank-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/mobilebanking-main/qr-listbank/list-bank-dialog.ts
72. queuing.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/queuing/queuing.component.html
73. queuing.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/queuing/queuing.component.ts
74. domescard-form.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.html
75. domescard-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.ts
76. vietqr-main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/vietqr-main/vietqr-main.component.html
77. vietqr-main.component.ts (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/vietqr-main/vietqr-main.component.ts
78. bnpl-management.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/model/bnpl-management.ts
79. homecredit-management.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/model/homecredit-management.ts
80. kbank-management.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/model/kbank-management.ts
81. overlay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/overlay/overlay.component.html
82. overlay.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/overlay/overlay.component.spec.ts
83. overlay.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/overlay/overlay.component.ts
84. bank-amount.pipe.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/pipe/bank-amount.pipe.ts
85. auth.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/services/auth.service.ts
86. card_info.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/services/card_info.service.ts
87. close-dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/services/close-dialog.service.ts
88. data.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/services/data.service.ts
89. deep_link.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/services/deep_link.service.ts
90. dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/services/dialog.service.ts
91. handle_bnpl_token.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/services/handle_bnpl_token.service.ts
92. multiple_method.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/services/multiple_method.service.ts
93. payment.service.ts (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/services/payment.service.ts
94. qr.service.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/services/qr.service.ts
95. vietqr.service.ts (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/services/vietqr.service.ts
96. success.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/success/success.component.html
97. success.component.ts (10 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/success/success.component.ts
98. index.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/translate/index.ts
99. lang-en.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/translate/lang-en.ts
100. lang-vi.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/translate/lang-vi.ts
101. translate.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/translate/translate.pipe.ts
102. translate.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/translate/translate.service.ts
103. translations.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/translate/translations.ts
104. apps-info.ts (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/util/apps-info.ts
105. banks-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/util/banks-info.ts
106. error-handler.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/util/error-handler.ts
107. util.ts (41 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/util/util.ts
108. qrcode.js (67 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/assets/script/qrcode.js
109. environment.prod.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/environments/environment.prod.ts
110. environment.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/environments/environment.ts
111. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/index.html
112. karma.conf.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/karma.conf.js
113. main.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/main.ts
114. polyfills.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/polyfills.ts
115. test.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-traveloka/src/test.ts

====================================================================================================
CHI TIẾT TỪNG FILE:
====================================================================================================

📁 FILE 1: 4en.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/4en.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 2: 4vn.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/4vn.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 3: app-routing.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/app-routing.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 4: app.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/app.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 5: app.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/app.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 6: app.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/app.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 84] return lang === this._translate.currentLang

== (2 điều kiện):
  1. [Dòng 63] 'vi' == params['locale']) {
  2. [Dòng 65] 'en' == params['locale']) {

!= (3 điều kiện):
  1. [Dòng 63] if (params['locale'] != null
  2. [Dòng 65] } else if (params['locale'] != null
  3. [Dòng 72] if (userLang != null

================================================================================

📁 FILE 7: app.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/app.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 8: counter.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/counter.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 9: counter.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/counter.directive.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 10: format-carno-input.derective.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/directives/format-carno-input.derective.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 44] this.lastValue !== $event.target.value)) {

!= (1 điều kiện):
  1. [Dòng 23] if(value!=null){

================================================================================

📁 FILE 11: uppercase-input.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/directives/uppercase-input.directive.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 0 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 23] this.lastValue !== $event.target.value)) {

================================================================================

📁 FILE 12: error.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/error/error.component.html
📊 Thống kê: 16 điều kiện duy nhất
   - === : 4 lần
   - == : 10 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 58] checkDupTran === false"
  2. [Dòng 102] isPopupSupport === 'True') || (rePayment
  3. [Dòng 103] isPopupSupport === 'True'"
  4. [Dòng 109] isPopupSupport === 'True')">

== (10 điều kiện):
  1. [Dòng 3] errorCode == '11'"
  2. [Dòng 29] errorCode && (errorCode == '253' || errorCode == 'overtime')
  3. [Dòng 29] errorCode == 'overtime')">
  4. [Dòng 102] <div class="footer-button" *ngIf="(isSent == false
  5. [Dòng 103] isSent == false
  6. [Dòng 109] <div class="cancel_transaction" *ngIf="checkDupTran" [class.select_only]="!(isSent == false
  7. [Dòng 122] <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
  8. [Dòng 122] (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
  9. [Dòng 124] <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
  10. [Dòng 124] !(errorCode == 'overtime' || errorCode == '253') && isBack && !invoiceNearExpire && !paymentPending

!= (2 điều kiện):
  1. [Dòng 21] errorCode != '253'
  2. [Dòng 21] errorCode != 'overtime'"

================================================================================

📁 FILE 13: error.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/error/error.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 14: error.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/error/error.component.ts
📊 Thống kê: 41 điều kiện duy nhất
   - === : 9 lần
   - == : 21 lần
   - !== : 0 lần
   - != : 11 lần
--------------------------------------------------------------------------------

=== (9 điều kiện):
  1. [Dòng 174] params.timeout === 'true') {
  2. [Dòng 183] params.kbank === 'true') {
  3. [Dòng 200] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  4. [Dòng 200] _re.body.state === 'unpaid');
  5. [Dòng 282] if (this.errorCode === 'overtime'
  6. [Dòng 282] this.errorCode === '253') {
  7. [Dòng 383] params.name === 'CUSTOMER_INTIME'
  8. [Dòng 383] params.code === '09') {
  9. [Dòng 464] if (this.timeLeft === 0) {

== (21 điều kiện):
  1. [Dòng 158] if (params && (params['bnpl'] == 'true')) {
  2. [Dòng 162] if (params && (params['bnpl'] == 'false')) {
  3. [Dòng 232] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'amigo') {
  4. [Dòng 239] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'kredivo') {
  5. [Dòng 256] this.res.themes.theme == 'general') {
  6. [Dòng 262] params.response_code == 'overtime') {
  7. [Dòng 305] if (_re.status == '200'
  8. [Dòng 305] _re.status == '201') {
  9. [Dòng 318] if (_re2.status == '200'
  10. [Dòng 318] _re2.status == '201') {
  11. [Dòng 335] if (this.errorCode == 'overtime'
  12. [Dòng 335] this.errorCode == '253') {
  13. [Dòng 340] if (this.paymentInformation.type == 'bnpl') {
  14. [Dòng 342] if (this.paymentInformation.provider == 'amigo'
  15. [Dòng 342] this.errorCode == '2') {
  16. [Dòng 345] else if (this.paymentInformation.provider == 'kbank'
  17. [Dòng 348] else if (this.paymentInformation.provider == 'homecredit'
  18. [Dòng 351] else if (this.paymentInformation.provider == 'kredivo'
  19. [Dòng 361] this.res.state == 'canceled') {
  20. [Dòng 377] if (lastPayment?.state == 'pending') {
  21. [Dòng 462] if (this.isTimePause == false) {

!= (11 điều kiện):
  1. [Dòng 230] } else if (this.res.payments[this.res.payments.length - 1].instrument.issuer.brand != null
  2. [Dòng 231] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id != null
  3. [Dòng 525] if (_re != null
  4. [Dòng 525] _re.links != null
  5. [Dòng 525] _re.links.merchant_return != null
  6. [Dòng 526] _re.links.merchant_return.href != null) {
  7. [Dòng 528] } else if (_re.body != null
  8. [Dòng 528] _re.body.links != null
  9. [Dòng 528] _re.body.links.merchant_return != null
  10. [Dòng 529] _re.body.links.merchant_return.href != null) {
  11. [Dòng 548] if (this.url_new_invoice != null) {

================================================================================

📁 FILE 15: support-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/error/support-dialog/support-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 16: support-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/error/support-dialog/support-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 17: format-date.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/format-date.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 18: format-date.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/format-date.directive.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0
  2. [Dòng 123] YY % 4 === 0)) {
  3. [Dòng 138] if (YYYY % 400 === 0
  4. [Dòng 138] YYYY % 4 === 0)) {

!== (2 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0 || (YY % 100 !== 0
  2. [Dòng 138] if (YYYY % 400 === 0 || (YYYY % 100 !== 0

================================================================================

📁 FILE 19: main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/main.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 1 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 23] checkDupTran === false"

================================================================================

📁 FILE 20: main.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/main.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 21: main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/main.component.ts
📊 Thống kê: 13 điều kiện duy nhất
   - === : 2 lần
   - == : 5 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 145] params['code'] === '09') {
  2. [Dòng 159] params.name === 'CUSTOMER_INTIME')) {

== (5 điều kiện):
  1. [Dòng 110] if ((dataPassed.status == '200'
  2. [Dòng 110] dataPassed.status == '201') && dataPassed.body != null) {
  3. [Dòng 114] dataPassed.body.themes.logo_full == 'True') {
  4. [Dòng 136] payments[payments.length - 1].state == 'pending'
  5. [Dòng 138] payments[payments.length - 1].instrument.issuer.brand.id == 'amigo') {

!= (6 điều kiện):
  1. [Dòng 101] if (this._idInvoice != null
  2. [Dòng 101] this._idInvoice != 0) {
  3. [Dòng 102] if (this._paymentService.getInvoiceDetail() != null) {
  4. [Dòng 110] dataPassed.body != null) {
  5. [Dòng 130] dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
  6. [Dòng 194] if (this._translate.currentLang != language) {

================================================================================

📁 FILE 22: app-result.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/app-result/app-result.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 23: app-result.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/app-result/app-result.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 24: bank-amount-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 25: bank-amount-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
📊 Thống kê: 34 điều kiện duy nhất
   - === : 0 lần
   - == : 34 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (34 điều kiện):
  1. [Dòng 41] if (this.locale == 'en') {
  2. [Dòng 55] if (bankId == 3
  3. [Dòng 55] bankId == 61
  4. [Dòng 56] bankId == 8
  5. [Dòng 56] bankId == 49
  6. [Dòng 57] bankId == 48
  7. [Dòng 58] bankId == 10
  8. [Dòng 58] bankId == 53
  9. [Dòng 59] bankId == 17
  10. [Dòng 59] bankId == 65
  11. [Dòng 60] bankId == 23
  12. [Dòng 60] bankId == 52
  13. [Dòng 61] bankId == 27
  14. [Dòng 61] bankId == 66
  15. [Dòng 62] bankId == 9
  16. [Dòng 62] bankId == 54
  17. [Dòng 63] bankId == 37
  18. [Dòng 64] bankId == 38
  19. [Dòng 65] bankId == 39
  20. [Dòng 66] bankId == 40
  21. [Dòng 67] bankId == 42
  22. [Dòng 68] bankId == 44
  23. [Dòng 69] bankId == 72
  24. [Dòng 70] bankId == 59
  25. [Dòng 73] bankId == 51
  26. [Dòng 74] bankId == 64
  27. [Dòng 75] bankId == 58
  28. [Dòng 76] bankId == 56
  29. [Dòng 79] bankId == 55
  30. [Dòng 80] bankId == 60
  31. [Dòng 81] bankId == 68
  32. [Dòng 82] bankId == 74
  33. [Dòng 83] bankId == 75 //MAFC Napas
  34. [Dòng 90] if (name == 'MAFC')

================================================================================

📁 FILE 26: policy-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 27: policy-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 28: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 29: bankaccount.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 30: bankaccount.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 31: bankaccount.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
📊 Thống kê: 127 điều kiện duy nhất
   - === : 27 lần
   - == : 61 lần
   - !== : 9 lần
   - != : 30 lần
--------------------------------------------------------------------------------

=== (27 điều kiện):
  1. [Dòng 119] if (target.tagName === 'A'
  2. [Dòng 144] if (isIE[0] === 'MSIE'
  3. [Dòng 144] +isIE[1] === 10) {
  4. [Dòng 226] if ((_val.value.substr(-1) === ' '
  5. [Dòng 226] _val.value.length === 24) {
  6. [Dòng 239] if (this.cardTypeBank === 'bank_card_number') {
  7. [Dòng 245] } else if (this.cardTypeBank === 'bank_account_number') {
  8. [Dòng 251] } else if (this.cardTypeBank === 'bank_username') {
  9. [Dòng 255] } else if (this.cardTypeBank === 'bank_customer_code') {
  10. [Dòng 261] this.cardTypeBank === 'bank_card_number'
  11. [Dòng 292] if (this.cardTypeBank === 'bank_account_number') {
  12. [Dòng 303] this.cardTypeBank === 'bank_card_number') {
  13. [Dòng 409] if (this.cardTypeBank === 'bank_card_number'
  14. [Dòng 409] if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  15. [Dòng 554] if (event.keyCode === 8
  16. [Dòng 554] event.key === "Backspace"
  17. [Dòng 840] if ((this.cardTypeBank === 'bank_account_number'
  18. [Dòng 840] this.cardTypeBank === 'bank_username'
  19. [Dòng 840] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  20. [Dòng 881] if (valIn === this._translate.instant('bank_card_number')) {
  21. [Dòng 906] } else if (valIn === this._translate.instant('bank_account_number')) {
  22. [Dòng 925] } else if (valIn === this._translate.instant('bank_username')) {
  23. [Dòng 941] } else if (valIn === this._translate.instant('bank_customer_code')) {
  24. [Dòng 1038] if (this.cardName === undefined
  25. [Dòng 1038] this.cardName === '') {
  26. [Dòng 1046] if (this.valueDate === undefined
  27. [Dòng 1046] this.valueDate === '') {

== (61 điều kiện):
  1. [Dòng 159] if (this._b == 27
  2. [Dòng 159] this._b == 19) {
  3. [Dòng 162] if (this._b == 19) {//19BIDV
  4. [Dòng 170] } else if (this._b == 27
  5. [Dòng 170] this._b == 32) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  6. [Dòng 171] if (this._b == 27) {
  7. [Dòng 176] if (this._b == 32) {
  8. [Dòng 238] if (this._b == 19
  9. [Dòng 238] this._b == 27
  10. [Dòng 238] this._b == 32) {
  11. [Dòng 286] if (this.checkBin(_val.value) && (this._b == 27
  12. [Dòng 286] this._b == 32)) {
  13. [Dòng 305] this._b == 32) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  14. [Dòng 379] if (this.checkBin(v) && (this._b == 27
  15. [Dòng 409] this._b == 18)) {
  16. [Dòng 554] event.inputType == 'deleteContentBackward') {
  17. [Dòng 555] if (event.target.name == 'exp_date'
  18. [Dòng 563] event.inputType == 'insertCompositionText') {
  19. [Dòng 578] if (((this.valueDate.length == 4
  20. [Dòng 578] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  21. [Dòng 578] this.valueDate.length == 5)
  22. [Dòng 627] if (temp.length == 0) {
  23. [Dòng 634] return (counter % 10 == 0);
  24. [Dòng 665] if (this._b == 19) {
  25. [Dòng 667] } else if (this._b == 27) {
  26. [Dòng 669] } else if (this._b == 32) {
  27. [Dòng 686] if (this.cardName != null && this.cardName.length > 0 && (this._b == 19
  28. [Dòng 686] this._b == 32)) {//3-TPB ;19-BIDV;27-pvcombank;
  29. [Dòng 729] if (_re.status == '200'
  30. [Dòng 729] _re.status == '201') {
  31. [Dòng 734] if (this._res_post.state == 'approved'
  32. [Dòng 734] this._res_post.state == 'failed') {
  33. [Dòng 741] } else if (this._res_post.state == 'authorization_required') {
  34. [Dòng 829] if (cardNo.length == 16
  35. [Dòng 829] cardNo.length == 19) {
  36. [Dòng 844] if ((cardNo.length == 16
  37. [Dòng 844] if ((cardNo.length == 16 || (cardNo.length == 19
  38. [Dòng 845] && ((this._b == 18
  39. [Dòng 845] cardNo.length == 19) || this._b != 18)
  40. [Dòng 859] if (this._b == +e.id) {
  41. [Dòng 1004] return (value.length == 2)
  42. [Dòng 1007] || (this._util.checkValidExpireMonthNew(value) && (this._b == 20
  43. [Dòng 1007] this._b == 33
  44. [Dòng 1008] this._b == 39
  45. [Dòng 1008] this._b == 43
  46. [Dòng 1008] this._b == 45
  47. [Dòng 1008] this._b == 64
  48. [Dòng 1008] this._b == 67
  49. [Dòng 1008] this._b == 68
  50. [Dòng 1008] this._b == 72
  51. [Dòng 1008] this._b == 73
  52. [Dòng 1008] this._b == 36))) //sonnh them Vietbank 72
  53. [Dòng 1015] || (this._util.checkValidExpireYearNew(value) && (this._b == 20
  54. [Dòng 1020] let validMonth = ((this._inExpMonth.length == 2))
  55. [Dòng 1022] || (this._util.checkValidExpireMonthNew(this._inExpMonth) && (this._b == 20
  56. [Dòng 1022] this._b == 36)));
  57. [Dòng 1024] let validYear = ((this._inExpYear.length == 2))
  58. [Dòng 1026] || (this._util.checkValidExpireYearNew(this._inExpYear) && (this._b == 20
  59. [Dòng 1049] this.valueDate.length == 4
  60. [Dòng 1049] this.valueDate.search('/') == -1)
  61. [Dòng 1050] this.valueDate.length == 5))

!== (9 điều kiện):
  1. [Dòng 226] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 715] key !== '3') {
  3. [Dòng 765] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 781] codeResponse.toString() !== '0') {
  5. [Dòng 840] cardNo.length !== 0) {
  6. [Dòng 888] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 909] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 930] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 945] if (this.cardTypeBank !== 'bank_customer_code') {

!= (30 điều kiện):
  1. [Dòng 192] if (this.htmlDesc != null
  2. [Dòng 223] if (ua.indexOf('safari') != -1
  3. [Dòng 233] if (_val.value != '') {
  4. [Dòng 556] if (this.valueDate.length != 3) {
  5. [Dòng 686] if (this.cardName != null
  6. [Dòng 737] if (this._res_post.links != null
  7. [Dòng 737] this._res_post.links.merchant_return != null
  8. [Dòng 737] this._res_post.links.merchant_return.href != null) {
  9. [Dòng 745] if (this._res_post.authorization != null
  10. [Dòng 745] this._res_post.authorization.links != null
  11. [Dòng 745] this._res_post.authorization.links.approval != null) {
  12. [Dòng 752] this._res_post.links.cancel != null) {
  13. [Dòng 844] this._b != 27
  14. [Dòng 844] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  15. [Dòng 845] this._b != 18)
  16. [Dòng 883] if (this._b != 18
  17. [Dòng 883] this._b != 19) {
  18. [Dòng 1005] this._b != 20
  19. [Dòng 1005] this._b != 33
  20. [Dòng 1005] this._b != 39
  21. [Dòng 1006] this._b != 43
  22. [Dòng 1006] this._b != 45
  23. [Dòng 1006] this._b != 64
  24. [Dòng 1006] this._b != 67
  25. [Dòng 1006] this._b != 68
  26. [Dòng 1006] this._b != 72
  27. [Dòng 1006] this._b != 73
  28. [Dòng 1006] this._b != 36)
  29. [Dòng 1124] if (this._inExpMonth.length != 3) {
  30. [Dòng 1174] if (this._inExpYear.length != 3) {

================================================================================

📁 FILE 32: bank.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/model/bank.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 33: oceanbank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/oceanbank/oceanbank.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 59] _b == 68"

================================================================================

📁 FILE 34: oceanbank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/oceanbank/oceanbank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 35: oceanbank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/oceanbank/oceanbank.component.ts
📊 Thống kê: 70 điều kiện duy nhất
   - === : 22 lần
   - == : 22 lần
   - !== : 7 lần
   - != : 19 lần
--------------------------------------------------------------------------------

=== (22 điều kiện):
  1. [Dòng 97] if (target.tagName === 'A'
  2. [Dòng 179] if ((_val.value.substr(-1) === ' '
  3. [Dòng 179] _val.value.length === 24) {
  4. [Dòng 189] if (this.cardTypeOcean === 'IB') {
  5. [Dòng 193] } else if (this.cardTypeOcean === 'MB') {
  6. [Dòng 194] if (_val.value.substr(0, 2) === '84') {
  7. [Dòng 201] } else if (this.cardTypeOcean === 'ATM') {
  8. [Dòng 232] this.cardTypeBank === 'bank_card_number') {
  9. [Dòng 235] if (this.cardTypeBank === 'bank_card_number'
  10. [Dòng 235] if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  11. [Dòng 606] if (approval.method === 'REDIRECT') {
  12. [Dòng 609] } else if (approval.method === 'POST_REDIRECT') {
  13. [Dòng 655] this.cardTypeOcean === 'ATM')
  14. [Dòng 656] || (this.cardTypeOcean === 'IB'
  15. [Dòng 699] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  16. [Dòng 699] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  17. [Dòng 704] if (_val.value === ''
  18. [Dòng 704] _val.value === null
  19. [Dòng 704] _val.value === undefined) {
  20. [Dòng 706] this.cardTypeOcean === 'MB') {
  21. [Dòng 714] this.cardTypeOcean === 'IB'
  22. [Dòng 720] if ((this.cardTypeBank === 'bank_card_number'

== (22 điều kiện):
  1. [Dòng 127] if (this._b == 18) { //18Oceanbank-ocb
  2. [Dòng 188] if (this._b == 18) {
  3. [Dòng 221] if (this._b == 18
  4. [Dòng 221] this.cardTypeOcean == 'ATM') {
  5. [Dòng 235] this._b == 18)) {
  6. [Dòng 402] if (cardType == this._translate.instant('internetbanking')
  7. [Dòng 410] } else if (cardType == this._translate.instant('mobilebanking')
  8. [Dòng 418] } else if (cardType == this._translate.instant('atm')
  9. [Dòng 451] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  10. [Dòng 454] if (this.cardTypeOcean == 'IB') {
  11. [Dòng 456] } else if (this.cardTypeOcean == 'MB') {
  12. [Dòng 458] } else if (this.cardTypeOcean == 'ATM') {
  13. [Dòng 498] if (this._res_post.state == 'approved'
  14. [Dòng 498] this._res_post.state == 'failed') {
  15. [Dòng 545] } else if (this._res_post.state == 'authorization_required') {
  16. [Dòng 670] if ((cardNo.length == 16
  17. [Dòng 670] if ((cardNo.length == 16 || (cardNo.length == 19
  18. [Dòng 671] && ((this._b == 18
  19. [Dòng 671] cardNo.length == 19) || this._b != 18)
  20. [Dòng 684] if (this._b == +e.id) {
  21. [Dòng 699] this._b == 18))) {
  22. [Dòng 706] } else if (this._b == 18

!== (7 điều kiện):
  1. [Dòng 179] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 402] this.lb_card_account !== this._translate.instant('ocb_account')) {
  3. [Dòng 410] this.lb_card_account !== this._translate.instant('ocb_phone')) {
  4. [Dòng 418] this.lb_card_account !== this._translate.instant('ocb_card')) {
  5. [Dòng 511] codeResponse.toString() !== '0') {
  6. [Dòng 568] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  7. [Dòng 720] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (19 điều kiện):
  1. [Dòng 145] if (this.htmlDesc != null
  2. [Dòng 176] if (ua.indexOf('safari') != -1
  3. [Dòng 186] if (_val.value != '') {
  4. [Dòng 222] this.auth_method != null) {
  5. [Dòng 500] if (this._res_post.return_url != null) {
  6. [Dòng 503] if (this._res_post.links != null
  7. [Dòng 503] this._res_post.links.merchant_return != null
  8. [Dòng 503] this._res_post.links.merchant_return.href != null) {
  9. [Dòng 550] if (this._res_post.authorization != null
  10. [Dòng 550] this._res_post.authorization.links != null
  11. [Dòng 555] this._res_post.links.cancel != null) {
  12. [Dòng 561] let userName = _formCard.name != null ? _formCard.name : ''
  13. [Dòng 562] this._res_post.authorization.links.approval != null
  14. [Dòng 562] this._res_post.authorization.links.approval.href != null) {
  15. [Dòng 565] userName = paramUserName != null ? paramUserName : ''
  16. [Dòng 670] this._b != 27
  17. [Dòng 670] this._b != 12
  18. [Dòng 670] this._b != 3)) && this._util.checkMod10(cardNo) && this.checkBin(cardNo)
  19. [Dòng 671] this._b != 18)

================================================================================

📁 FILE 36: onepay-napas.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 1 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 84] ready===1"

== (3 điều kiện):
  1. [Dòng 21] (cardTypeBank == 'bank_card_number') && (_b == 67 || (!isOffTechcombankNapas && checkTwoEnabled))
  2. [Dòng 84] (cardTypeBank == 'bank_card_number') &&(_b == 67 || (_b == 2 && !isOffTechcombankNapas && checkTwoEnabled)) && ready===1
  3. [Dòng 114] (cardTypeBank == 'internet_banking') && (_b == 2 || (!isOffTechcombank && checkTwoEnabled))

================================================================================

📁 FILE 37: onepay-napas.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.ts
📊 Thống kê: 115 điều kiện duy nhất
   - === : 16 lần
   - == : 50 lần
   - !== : 2 lần
   - != : 47 lần
--------------------------------------------------------------------------------

=== (16 điều kiện):
  1. [Dòng 105] if (target.tagName === 'A'
  2. [Dòng 137] if (isIE[0] === 'MSIE'
  3. [Dòng 137] +isIE[1] === 10) {
  4. [Dòng 162] if (this.timeLeft === 10) {
  5. [Dòng 166] if (this.runTime === true) {
  6. [Dòng 172] if (this.timeLeft === 0) {
  7. [Dòng 174] if (this.runTime === true) this.submitCardBanking();
  8. [Dòng 347] if (event.keyCode === 8
  9. [Dòng 347] event.key === "Backspace"
  10. [Dòng 673] if (approval.method === 'REDIRECT') {
  11. [Dòng 676] } else if (approval.method === 'POST_REDIRECT') {
  12. [Dòng 800] if (this.cardName === undefined
  13. [Dòng 800] this.cardName === '') {
  14. [Dòng 861] if (valIn === this._translate.instant('bank_card_number')) {
  15. [Dòng 863] if (this.timeLeft === 1) {
  16. [Dòng 880] } else if (valIn === this._translate.instant('internet_banking')) {

== (50 điều kiện):
  1. [Dòng 153] if (this._b == 67
  2. [Dòng 153] this._b == 2) {//Techcombank Napas
  3. [Dòng 160] if ((this._b == 2
  4. [Dòng 160] !this.checkTwoEnabled) || (this._b == 2
  5. [Dòng 183] } else if ((this._b == 2
  6. [Dòng 183] this._b == 67) {
  7. [Dòng 219] if (_re.status == '200'
  8. [Dòng 219] _re.status == '201') {
  9. [Dòng 224] if (this._res_post.state == 'approved'
  10. [Dòng 224] this._res_post.state == 'failed') {
  11. [Dòng 228] } else if (this._res_post.state == 'authorization_required') {
  12. [Dòng 347] event.inputType == 'deleteContentBackward') {
  13. [Dòng 348] if (event.target.name == 'exp_date'
  14. [Dòng 356] event.inputType == 'insertCompositionText') {
  15. [Dòng 434] return this._b == 2
  16. [Dòng 434] this._b == 67
  17. [Dòng 520] if (temp.length == 0) {
  18. [Dòng 527] return (counter % 10 == 0);
  19. [Dòng 540] this.d_card_date == true)) {
  20. [Dòng 713] if ((cardNo.length == 16
  21. [Dòng 713] if ((cardNo.length == 16 || (cardNo.length == 19
  22. [Dòng 714] this.checkMod10(cardNo) == true
  23. [Dòng 727] if (this._b == +e.id) {
  24. [Dòng 769] if (this._b != 68 || (this._b == 68
  25. [Dòng 778] return ((value.length == 4
  26. [Dòng 778] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  27. [Dòng 778] value.length == 5) && parseInt(value.split('/')[0]
  28. [Dòng 782] || (this._util.checkValidExpireMonthYear(value) && (this._b == 2
  29. [Dòng 782] this._b == 20
  30. [Dòng 782] this._b == 33
  31. [Dòng 783] this._b == 39
  32. [Dòng 783] this._b == 43
  33. [Dòng 783] this._b == 45
  34. [Dòng 783] this._b == 64
  35. [Dòng 783] this._b == 68
  36. [Dòng 783] this._b == 72))) //sonnh them Vietbank 72
  37. [Dòng 795] let validYear = (this._b == 67
  38. [Dòng 795] this._b == 2) ? this._util.checkValidExpireMonthYear2(this._inExpMonth
  39. [Dòng 809] this._inExpDate.length == 4
  40. [Dòng 809] this._inExpDate.search('/') == -1)
  41. [Dòng 810] this._inExpDate.length == 5))
  42. [Dòng 812] || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 20
  43. [Dòng 812] this._b == 2
  44. [Dòng 812] this._b == 72)));
  45. [Dòng 842] return value.length == 2
  46. [Dòng 846] return (value.length == 2)
  47. [Dòng 849] value) && (this._b == 2
  48. [Dòng 850] this._b == 72
  49. [Dòng 850] this._b == 73
  50. [Dòng 850] this._b == 36))) //sonnh them Vietbank 72

!== (2 điều kiện):
  1. [Dòng 243] codeResponse.toString() !== '0') {
  2. [Dòng 907] if (this.cardTypeBank !== 'internet_banking') {

!= (47 điều kiện):
  1. [Dòng 179] if (this.htmlDesc != null
  2. [Dòng 232] if (this._res_post.authorization != null
  3. [Dòng 232] this._res_post.authorization.links != null
  4. [Dòng 232] this._res_post.authorization.links.approval != null) {
  5. [Dòng 288] if (ua.indexOf('safari') != -1
  6. [Dòng 349] if (this._inExpMonth.length != 3) {
  7. [Dòng 398] if (this._inExpYear.length != 3) {
  8. [Dòng 451] if (this._inExpDate.length != 3) {
  9. [Dòng 557] if (this._b != 9
  10. [Dòng 557] this._b != 11
  11. [Dòng 557] this._b != 16
  12. [Dòng 557] this._b != 17
  13. [Dòng 557] this._b != 25
  14. [Dòng 557] this._b != 44
  15. [Dòng 557] this._b != 54
  16. [Dòng 558] this._b != 57
  17. [Dòng 558] this._b != 59
  18. [Dòng 558] this._b != 61
  19. [Dòng 558] this._b != 63
  20. [Dòng 558] this._b != 69) {
  21. [Dòng 568] '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73'].indexOf(this._b.toString()) != -1) {// duongpxt 19255: Them vietbank napas id 72
  22. [Dòng 602] if (this._res_post.return_url != null) {
  23. [Dòng 605] if (this._res_post.links != null
  24. [Dòng 605] this._res_post.links.merchant_return != null
  25. [Dòng 605] this._res_post.links.merchant_return.href != null) {
  26. [Dòng 658] this._res_post.links.cancel != null) {
  27. [Dòng 663] let userName = _formCard.name != null ? _formCard.name : ''
  28. [Dòng 664] this._res_post.authorization.links.approval != null
  29. [Dòng 664] this._res_post.authorization.links.approval.href != null) {
  30. [Dòng 667] userName = paramUserName != null ? paramUserName : ''
  31. [Dòng 713] this._b != 27
  32. [Dòng 713] this._b != 12
  33. [Dòng 713] this._b != 3))
  34. [Dòng 769] if (this._b != 68
  35. [Dòng 780] this._b != 2
  36. [Dòng 780] this._b != 20
  37. [Dòng 780] this._b != 33
  38. [Dòng 780] this._b != 39
  39. [Dòng 781] this._b != 43
  40. [Dòng 781] this._b != 45
  41. [Dòng 781] this._b != 64
  42. [Dòng 781] this._b != 67
  43. [Dòng 781] this._b != 68
  44. [Dòng 781] this._b != 72)
  45. [Dòng 848] this._b != 72
  46. [Dòng 848] this._b != 73
  47. [Dòng 848] this._b != 36)

================================================================================

📁 FILE 38: otp-auth.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 127] _b == 22 || _b == 24
  2. [Dòng 128] _b == 8"
  3. [Dòng 129] _locale == 'en'"
  4. [Dòng 138] _b == 18"

!= (1 điều kiện):
  1. [Dòng 141] _b != 22 && _b != 24 && _b != 8 && _b != 18

================================================================================

📁 FILE 39: otp-auth.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
📊 Thống kê: 18 điều kiện duy nhất
   - === : 0 lần
   - == : 11 lần
   - !== : 1 lần
   - != : 6 lần
--------------------------------------------------------------------------------

== (11 điều kiện):
  1. [Dòng 96] if (this._b == 8) {//MB Bank
  2. [Dòng 100] if (this._b == 18) {//Oceanbank
  3. [Dòng 137] if (this._b == 8) {
  4. [Dòng 142] if (this._b == 18) {
  5. [Dòng 148] if (this._b == 12) { //SHB
  6. [Dòng 169] if (_re.status == '200'
  7. [Dòng 169] _re.status == '201') {
  8. [Dòng 178] } else if (this._res.state == 'authorization_required') {
  9. [Dòng 209] if (this.challengeCode == '') {
  10. [Dòng 306] if (this._b == 12) {
  11. [Dòng 356] if (this._b == 18) {//8-MB Bank;18-oceanbank

!== (1 điều kiện):
  1. [Dòng 184] codeResponse.toString() !== '0') {

!= (6 điều kiện):
  1. [Dòng 174] if (this._res.links != null
  2. [Dòng 174] this._res.links.merchant_return != null
  3. [Dòng 174] this._res.links.merchant_return.href != null) {
  4. [Dòng 351] if (!(_formCard.otp != null
  5. [Dòng 357] if (!(_formCard.password != null
  6. [Dòng 373] if (ua.indexOf('safari') != -1

================================================================================

📁 FILE 40: shbbankaccount.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/shbbankaccount/shbbankaccount.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 26] cardTypeBank == '002'"
  2. [Dòng 83] cardTypeBank == '001'"

================================================================================

📁 FILE 41: shbbankaccount.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/shbbankaccount/shbbankaccount.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 42: shbbankaccount.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/shbbankaccount/shbbankaccount.component.ts
📊 Thống kê: 94 điều kiện duy nhất
   - === : 7 lần
   - == : 58 lần
   - !== : 1 lần
   - != : 28 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 96] if (target.tagName === 'A'
  2. [Dòng 158] if (e === '002') {
  3. [Dòng 162] } else if (e === '001') {
  4. [Dòng 330] if (approval.method === 'REDIRECT') {
  5. [Dòng 333] } else if (approval.method === 'POST_REDIRECT') {
  6. [Dòng 477] if (event.keyCode === 8
  7. [Dòng 477] event.key === "Backspace"

== (58 điều kiện):
  1. [Dòng 135] if (this._b == 12) {// 12SHB
  2. [Dòng 174] this.cardTypeBank == '002') {
  3. [Dòng 206] if (this._b == 12
  4. [Dòng 206] this.cardTypeBank == '001') {
  5. [Dòng 241] if (this._res_post.state == 'approved'
  6. [Dòng 241] this._res_post.state == 'failed') {
  7. [Dòng 269] } else if (this._res_post.state == 'authorization_required') {
  8. [Dòng 291] if (this._b == 1
  9. [Dòng 291] this._b == 14
  10. [Dòng 291] this._b == 15
  11. [Dòng 291] this._b == 24
  12. [Dòng 291] this._b == 8
  13. [Dòng 291] this._b == 10
  14. [Dòng 291] this._b == 20
  15. [Dòng 291] this._b == 22
  16. [Dòng 291] this._b == 23
  17. [Dòng 291] this._b == 30
  18. [Dòng 291] this._b == 11
  19. [Dòng 291] this._b == 17
  20. [Dòng 291] this._b == 12) {
  21. [Dòng 373] if (this._b == +e.id) {
  22. [Dòng 399] return this._b == 20
  23. [Dòng 399] this._b == 73
  24. [Dòng 399] this._b == 33
  25. [Dòng 399] this._b == 39
  26. [Dòng 399] this._b == 43
  27. [Dòng 399] this._b == 45
  28. [Dòng 399] this._b == 67
  29. [Dòng 399] this._b == 64
  30. [Dòng 399] this._b == 68
  31. [Dòng 399] this._b == 72
  32. [Dòng 399] this._b == 36
  33. [Dòng 399] this._b == 74
  34. [Dòng 399] this._b == 75
  35. [Dòng 407] return this._b == 9
  36. [Dòng 407] this._b == 16
  37. [Dòng 407] this._b == 25
  38. [Dòng 407] this._b == 44
  39. [Dòng 408] this._b == 54
  40. [Dòng 408] this._b == 57
  41. [Dòng 408] this._b == 59
  42. [Dòng 408] this._b == 61
  43. [Dòng 408] this._b == 63
  44. [Dòng 408] this._b == 69
  45. [Dòng 477] event.inputType == 'deleteContentBackward') {
  46. [Dòng 478] if (event.target.name == 'exp_date'
  47. [Dòng 486] event.inputType == 'insertCompositionText') {
  48. [Dòng 616] if ((cardNo.length == 16
  49. [Dòng 617] (cardNo.length == 19
  50. [Dòng 617] (cardNo.length == 19 && (this._b == 1
  51. [Dòng 617] this._b == 4
  52. [Dòng 617] this._b == 55
  53. [Dòng 617] this._b == 47
  54. [Dòng 617] this._b == 48
  55. [Dòng 617] this._b == 59))
  56. [Dòng 619] this._util.checkMod10(cardNo) == true
  57. [Dòng 641] return value.length == 2
  58. [Dòng 645] return (value.length == 2)

!== (1 điều kiện):
  1. [Dòng 292] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (28 điều kiện):
  1. [Dòng 142] if (this.htmlDesc != null
  2. [Dòng 190] if (this._b != 9
  3. [Dòng 190] this._b != 11
  4. [Dòng 190] this._b != 16
  5. [Dòng 190] this._b != 17
  6. [Dòng 190] this._b != 25
  7. [Dòng 190] this._b != 44
  8. [Dòng 190] this._b != 54
  9. [Dòng 191] this._b != 57
  10. [Dòng 191] this._b != 59
  11. [Dòng 191] this._b != 61
  12. [Dòng 191] this._b != 63
  13. [Dòng 191] this._b != 69) {
  14. [Dòng 195] if (this._inCardName != null
  15. [Dòng 243] if (this._res_post.return_url != null) {
  16. [Dòng 246] if (this._res_post.links != null
  17. [Dòng 246] this._res_post.links.merchant_return != null
  18. [Dòng 246] this._res_post.links.merchant_return.href != null) {
  19. [Dòng 274] if (this._res_post.authorization != null
  20. [Dòng 274] this._res_post.authorization.links != null
  21. [Dòng 279] this._res_post.links.cancel != null) {
  22. [Dòng 285] let userName = _formCard.name != null ? _formCard.name : ''
  23. [Dòng 286] this._res_post.authorization.links.approval != null
  24. [Dòng 286] this._res_post.authorization.links.approval.href != null) {
  25. [Dòng 289] userName = paramUserName != null ? paramUserName : ''
  26. [Dòng 417] if (ua.indexOf('safari') != -1
  27. [Dòng 479] if (this._inExpMonth.length != 3) {
  28. [Dòng 528] if (this._inExpYear.length != 3) {

================================================================================

📁 FILE 43: techcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 44: techcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 45: techcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
📊 Thống kê: 13 điều kiện duy nhất
   - === : 0 lần
   - == : 9 lần
   - !== : 1 lần
   - != : 3 lần
--------------------------------------------------------------------------------

== (9 điều kiện):
  1. [Dòng 68] if (this._b == 2) {
  2. [Dòng 70] } else if (this._b == 6) {
  3. [Dòng 72] } else if (this._b == 31) {
  4. [Dòng 74] } else if (this._b == 80) {
  5. [Dòng 104] if (_re.status == '200'
  6. [Dòng 104] _re.status == '201') {
  7. [Dòng 109] if (this._res_post.state == 'approved'
  8. [Dòng 109] this._res_post.state == 'failed') {
  9. [Dòng 113] } else if (this._res_post.state == 'authorization_required') {

!== (1 điều kiện):
  1. [Dòng 128] codeResponse.toString() !== '0') {

!= (3 điều kiện):
  1. [Dòng 117] if (this._res_post.authorization != null
  2. [Dòng 117] this._res_post.authorization.links != null
  3. [Dòng 117] this._res_post.authorization.links.approval != null) {

================================================================================

📁 FILE 46: tpbankaccount.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/tpbankaccount/tpbankaccount.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 47: tpbankaccount.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/tpbankaccount/tpbankaccount.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 48: tpbankaccount.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/tpbankaccount/tpbankaccount.component.ts
📊 Thống kê: 68 điều kiện duy nhất
   - === : 5 lần
   - == : 36 lần
   - !== : 1 lần
   - != : 26 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 92] if (target.tagName === 'A'
  2. [Dòng 284] if (approval.method === 'REDIRECT') {
  3. [Dòng 287] } else if (approval.method === 'POST_REDIRECT') {
  4. [Dòng 346] if (event.keyCode === 8
  5. [Dòng 346] event.key === "Backspace"

== (36 điều kiện):
  1. [Dòng 195] if (this._res_post.state == 'approved'
  2. [Dòng 195] this._res_post.state == 'failed') {
  3. [Dòng 223] } else if (this._res_post.state == 'authorization_required') {
  4. [Dòng 245] if (this._b == 1
  5. [Dòng 245] this._b == 14
  6. [Dòng 245] this._b == 15
  7. [Dòng 245] this._b == 24
  8. [Dòng 245] this._b == 8
  9. [Dòng 245] this._b == 10
  10. [Dòng 245] this._b == 20
  11. [Dòng 245] this._b == 22
  12. [Dòng 245] this._b == 23
  13. [Dòng 245] this._b == 30
  14. [Dòng 245] this._b == 11
  15. [Dòng 245] this._b == 17) {
  16. [Dòng 324] let validYear = this._b == 20 ? this._util.checkValidExpireMonthYear2(this._inExpMonth, this._inExpYear) : this._util.checkValidIssueMonthYear2(this._inExpMonth, this._inExpYear)
  17. [Dòng 346] event.inputType == 'deleteContentBackward') {
  18. [Dòng 347] if (event.target.name == 'exp_date'
  19. [Dòng 355] event.inputType == 'insertCompositionText') {
  20. [Dòng 438] if (this._b == +e.id) {
  21. [Dòng 474] if ((cardNo.length == 16
  22. [Dòng 475] cardNo.length == 19)
  23. [Dòng 476] this._util.checkMod10(cardNo) == true
  24. [Dòng 576] return value.length == 2
  25. [Dòng 580] return (value.length == 2)
  26. [Dòng 583] value) && (this._b == 20
  27. [Dòng 583] this._b == 33
  28. [Dòng 584] this._b == 39
  29. [Dòng 584] this._b == 43
  30. [Dòng 584] this._b == 45
  31. [Dòng 584] this._b == 64
  32. [Dòng 584] this._b == 67
  33. [Dòng 584] this._b == 68
  34. [Dòng 584] this._b == 72
  35. [Dòng 584] this._b == 73
  36. [Dòng 584] this._b == 36))) //sonnh them Vietbank 72

!== (1 điều kiện):
  1. [Dòng 246] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (26 điều kiện):
  1. [Dòng 122] if (this.htmlDesc != null
  2. [Dòng 197] if (this._res_post.return_url != null) {
  3. [Dòng 200] if (this._res_post.links != null
  4. [Dòng 200] this._res_post.links.merchant_return != null
  5. [Dòng 200] this._res_post.links.merchant_return.href != null) {
  6. [Dòng 228] if (this._res_post.authorization != null
  7. [Dòng 228] this._res_post.authorization.links != null
  8. [Dòng 233] this._res_post.links.cancel != null) {
  9. [Dòng 239] let userName = _formCard.name != null ? _formCard.name : ''
  10. [Dòng 240] this._res_post.authorization.links.approval != null
  11. [Dòng 240] this._res_post.authorization.links.approval.href != null) {
  12. [Dòng 243] userName = paramUserName != null ? paramUserName : ''
  13. [Dòng 340] if (ua.indexOf('safari') != -1
  14. [Dòng 348] if (this._inExpMonth.length != 3) {
  15. [Dòng 397] if (this._inExpYear.length != 3) {
  16. [Dòng 581] this._b != 20
  17. [Dòng 581] this._b != 33
  18. [Dòng 581] this._b != 39
  19. [Dòng 582] this._b != 43
  20. [Dòng 582] this._b != 45
  21. [Dòng 582] this._b != 64
  22. [Dòng 582] this._b != 67
  23. [Dòng 582] this._b != 68
  24. [Dòng 582] this._b != 72
  25. [Dòng 582] this._b != 73
  26. [Dòng 582] this._b != 36)

================================================================================

📁 FILE 49: vibbank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 26] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 39] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 39] valueDate.trim().length === 0)"

================================================================================

📁 FILE 50: vibbank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 51: vibbank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.ts
📊 Thống kê: 97 điều kiện duy nhất
   - === : 37 lần
   - == : 33 lần
   - !== : 10 lần
   - != : 17 lần
--------------------------------------------------------------------------------

=== (37 điều kiện):
  1. [Dòng 108] if (target.tagName === 'A'
  2. [Dòng 133] if (isIE[0] === 'MSIE'
  3. [Dòng 133] +isIE[1] === 10) {
  4. [Dòng 164] if (this.timeLeft === 0) {
  5. [Dòng 207] if ((_val.value.substr(-1) === ' '
  6. [Dòng 207] _val.value.length === 24) {
  7. [Dòng 217] if (this.cardTypeBank === 'bank_card_number') {
  8. [Dòng 222] } else if (this.cardTypeBank === 'bank_account_number') {
  9. [Dòng 228] } else if (this.cardTypeBank === 'bank_username') {
  10. [Dòng 232] } else if (this.cardTypeBank === 'bank_customer_code') {
  11. [Dòng 253] if (this.cardTypeBank === 'bank_account_number') {
  12. [Dòng 264] this.cardTypeBank === 'bank_card_number') {
  13. [Dòng 501] if (event.keyCode === 8
  14. [Dòng 501] event.key === "Backspace"
  15. [Dòng 541] if (v.length === 2
  16. [Dòng 541] this.flag.length === 3
  17. [Dòng 541] this.flag.charAt(this.flag.length - 1) === '/') {
  18. [Dòng 545] if (v.length === 1) {
  19. [Dòng 547] } else if (v.length === 2) {
  20. [Dòng 550] v.length === 2) {
  21. [Dòng 558] if (len === 2) {
  22. [Dòng 790] if ((this.cardTypeBank === 'bank_account_number'
  23. [Dòng 790] this.cardTypeBank === 'bank_username'
  24. [Dòng 790] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  25. [Dòng 840] if (valIn === this._translate.instant('bank_card_number')) {
  26. [Dòng 859] } else if (valIn === this._translate.instant('bank_account_number')) {
  27. [Dòng 871] } else if (valIn === this._translate.instant('bank_username')) {
  28. [Dòng 882] } else if (valIn === this._translate.instant('bank_customer_code')) {
  29. [Dòng 939] if (_val.value === ''
  30. [Dòng 939] _val.value === null
  31. [Dòng 939] _val.value === undefined) {
  32. [Dòng 950] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  33. [Dòng 960] if ((this.cardTypeBank === 'bank_card_number'
  34. [Dòng 992] if (this.cardName === undefined
  35. [Dòng 992] this.cardName === '') {
  36. [Dòng 1000] if (this.valueDate === undefined
  37. [Dòng 1000] this.valueDate === '') {

== (33 điều kiện):
  1. [Dòng 147] if (this._b == 5) {//5-vib;
  2. [Dòng 216] if (this._b == 5) {
  3. [Dòng 250] if (this.checkBin(_val.value) && (this._b == 5)) {
  4. [Dòng 266] if (this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  5. [Dòng 328] if (this.checkBin(v) && (this._b == 5)) {
  6. [Dòng 501] event.inputType == 'deleteContentBackward') {
  7. [Dòng 502] if (event.target.name == 'exp_date'
  8. [Dòng 510] event.inputType == 'insertCompositionText') {
  9. [Dòng 525] if (((this.valueDate.length == 4
  10. [Dòng 525] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  11. [Dòng 525] this.valueDate.length == 5)
  12. [Dòng 605] if (temp.length == 0) {
  13. [Dòng 612] return (counter % 10 == 0);
  14. [Dòng 643] _formCard.exp_date.length == 5
  15. [Dòng 643] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 5)) {//27-pvcombank;3-TPB ;
  16. [Dòng 648] if (this.cardName != null && this.cardName.length > 0 && (this._b == 5)) {//3-TPB ;19-BIDV;27-pvcombank;
  17. [Dòng 692] if (_re.status == '200'
  18. [Dòng 692] _re.status == '201') {
  19. [Dòng 697] if (this._res_post.state == 'approved'
  20. [Dòng 697] this._res_post.state == 'failed') {
  21. [Dòng 704] } else if (this._res_post.state == 'authorization_required') {
  22. [Dòng 795] if ((cardNo.length == 16
  23. [Dòng 795] if ((cardNo.length == 16 || (cardNo.length == 19
  24. [Dòng 796] && ((this._b == 18
  25. [Dòng 796] cardNo.length == 19) || this._b != 18)
  26. [Dòng 809] if (this._b == +e.id) {
  27. [Dòng 825] if (valIn == 1) {
  28. [Dòng 827] } else if (valIn == 2) {
  29. [Dòng 950] this._b == 18)) {
  30. [Dòng 972] this.c_expdate = !(((this.valueDate.length == 4
  31. [Dòng 1003] this.valueDate.length == 4
  32. [Dòng 1003] this.valueDate.search('/') == -1)
  33. [Dòng 1004] this.valueDate.length == 5))

!== (10 điều kiện):
  1. [Dòng 207] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 678] key !== '3') {
  3. [Dòng 726] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 743] codeResponse.toString() !== '0') {
  5. [Dòng 790] cardNo.length !== 0) {
  6. [Dòng 847] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 862] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 876] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 889] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 960] this._b !== 18) || (this._b == 18)) {

!= (17 điều kiện):
  1. [Dòng 174] if (this.htmlDesc != null
  2. [Dòng 204] if (ua.indexOf('safari') != -1
  3. [Dòng 214] if (_val.value != '') {
  4. [Dòng 503] if (this.valueDate.length != 3) {
  5. [Dòng 643] if (_formCard.exp_date != null
  6. [Dòng 648] if (this.cardName != null
  7. [Dòng 700] if (this._res_post.links != null
  8. [Dòng 700] this._res_post.links.merchant_return != null
  9. [Dòng 700] this._res_post.links.merchant_return.href != null) {
  10. [Dòng 708] if (this._res_post.authorization != null
  11. [Dòng 708] this._res_post.authorization.links != null
  12. [Dòng 708] this._res_post.authorization.links.approval != null) {
  13. [Dòng 715] this._res_post.links.cancel != null) {
  14. [Dòng 795] this._b != 27
  15. [Dòng 795] this._b != 12
  16. [Dòng 795] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  17. [Dòng 796] this._b != 18)

================================================================================

📁 FILE 52: vietcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 90] _b == 68"

================================================================================

📁 FILE 53: vietcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 54: vietcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
📊 Thống kê: 88 điều kiện duy nhất
   - === : 5 lần
   - == : 54 lần
   - !== : 2 lần
   - != : 27 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 82] if (target.tagName === 'A'
  2. [Dòng 213] if (event.keyCode === 8
  3. [Dòng 213] event.key === "Backspace"
  4. [Dòng 501] if (approval.method === 'REDIRECT') {
  5. [Dòng 504] } else if (approval.method === 'POST_REDIRECT') {

== (54 điều kiện):
  1. [Dòng 131] return this._b == 11
  2. [Dòng 131] this._b == 20
  3. [Dòng 131] this._b == 73
  4. [Dòng 131] this._b == 33
  5. [Dòng 131] this._b == 39
  6. [Dòng 131] this._b == 43
  7. [Dòng 131] this._b == 45
  8. [Dòng 131] this._b == 67
  9. [Dòng 131] this._b == 64
  10. [Dòng 131] this._b == 68
  11. [Dòng 131] this._b == 72
  12. [Dòng 131] this._b == 36
  13. [Dòng 131] this._b == 74
  14. [Dòng 131] this._b == 75
  15. [Dòng 139] return this._b == 9
  16. [Dòng 139] this._b == 16
  17. [Dòng 139] this._b == 17
  18. [Dòng 139] this._b == 25
  19. [Dòng 139] this._b == 44
  20. [Dòng 140] this._b == 54
  21. [Dòng 140] this._b == 57
  22. [Dòng 140] this._b == 59
  23. [Dòng 140] this._b == 61
  24. [Dòng 140] this._b == 63
  25. [Dòng 140] this._b == 69
  26. [Dòng 213] event.inputType == 'deleteContentBackward') {
  27. [Dòng 214] if (event.target.name == 'exp_date'
  28. [Dòng 222] event.inputType == 'insertCompositionText') {
  29. [Dòng 335] this.d_card_date == true)){
  30. [Dòng 393] if (this._res_post.state == 'approved'
  31. [Dòng 393] this._res_post.state == 'failed') {
  32. [Dòng 440] } else if (this._res_post.state == 'authorization_required') {
  33. [Dòng 462] if (this._b == 1
  34. [Dòng 462] this._b == 14
  35. [Dòng 462] this._b == 15
  36. [Dòng 462] this._b == 24
  37. [Dòng 462] this._b == 8
  38. [Dòng 462] this._b == 10
  39. [Dòng 462] this._b == 22
  40. [Dòng 462] this._b == 23
  41. [Dòng 462] this._b == 30
  42. [Dòng 462] this._b == 11
  43. [Dòng 462] this._b == 9) {
  44. [Dòng 560] if ((cardNo.length == 16
  45. [Dòng 561] (cardNo.length == 19
  46. [Dòng 561] (cardNo.length == 19 && (this._b == 1
  47. [Dòng 561] this._b == 4
  48. [Dòng 561] this._b == 55
  49. [Dòng 561] this._b == 47
  50. [Dòng 561] this._b == 48
  51. [Dòng 561] this._b == 59))
  52. [Dòng 563] this._util.checkMod10(cardNo) == true
  53. [Dòng 585] return value.length == 2
  54. [Dòng 589] return (value.length == 2)

!== (2 điều kiện):
  1. [Dòng 406] codeResponse.toString() !== '0') {
  2. [Dòng 463] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (27 điều kiện):
  1. [Dòng 123] if (this.htmlDesc != null
  2. [Dòng 149] if (ua.indexOf('safari') != -1
  3. [Dòng 215] if (this._inExpMonth.length != 3) {
  4. [Dòng 264] if (this._inExpYear.length != 3) {
  5. [Dòng 351] if (this._b != 9
  6. [Dòng 351] this._b != 16
  7. [Dòng 351] this._b != 17
  8. [Dòng 351] this._b != 25
  9. [Dòng 351] this._b != 44
  10. [Dòng 351] this._b != 54
  11. [Dòng 352] this._b != 57
  12. [Dòng 352] this._b != 59
  13. [Dòng 352] this._b != 61
  14. [Dòng 352] this._b != 63
  15. [Dòng 352] this._b != 69) {
  16. [Dòng 362] '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75'].indexOf(this._b.toString()) != -1) {// duongpxt 19255: Them vietbank napas id 72
  17. [Dòng 395] if (this._res_post.return_url != null) {
  18. [Dòng 398] if (this._res_post.links != null
  19. [Dòng 398] this._res_post.links.merchant_return != null
  20. [Dòng 398] this._res_post.links.merchant_return.href != null) {
  21. [Dòng 445] if (this._res_post.authorization != null
  22. [Dòng 445] this._res_post.authorization.links != null
  23. [Dòng 450] this._res_post.links.cancel != null) {
  24. [Dòng 456] let userName = _formCard.name != null ? _formCard.name : ''
  25. [Dòng 457] this._res_post.authorization.links.approval != null
  26. [Dòng 457] this._res_post.authorization.links.approval.href != null) {
  27. [Dòng 460] userName = paramUserName != null ? paramUserName : ''

================================================================================

📁 FILE 55: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 56: off-bank-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 57: off-bank-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 58: domescard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/domescard-main.component.html
📊 Thống kê: 12 điều kiện duy nhất
   - === : 1 lần
   - == : 10 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 34] filteredData.length === 0"

== (10 điều kiện):
  1. [Dòng 68] (!token&&_auth==0&&vietcombankGroupSelected) || (token && _b == 16)
  2. [Dòng 68] _b == 16)">
  3. [Dòng 72] _auth==0 && techcombankGroupSelected
  4. [Dòng 76] _auth==0 && onepaynapasGroupSelected
  5. [Dòng 83] _auth==0 && bankaccountGroupSelected
  6. [Dòng 87] _b == 12"
  7. [Dòng 89] _b == 3"
  8. [Dòng 92] _auth==0 && oceanbankGroupSelected
  9. [Dòng 96] _auth==0 && vibbankGroupSelected
  10. [Dòng 101] (token || _auth==1) && _b != 16

!= (1 điều kiện):
  1. [Dòng 84] _b != 12 && _b != 3

================================================================================

📁 FILE 59: domescard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/domescard-main/domescard-main.component.ts
📊 Thống kê: 147 điều kiện duy nhất
   - === : 27 lần
   - == : 111 lần
   - !== : 2 lần
   - != : 7 lần
--------------------------------------------------------------------------------

=== (27 điều kiện):
  1. [Dòng 289] if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
  2. [Dòng 290] filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
  3. [Dòng 334] if (valOut === 'auth') {
  4. [Dòng 508] if (this._b === '1'
  5. [Dòng 508] this._b === '20'
  6. [Dòng 508] this._b === '64') {
  7. [Dòng 511] if (this._b === '36'
  8. [Dòng 511] this._b === '18'
  9. [Dòng 511] this._b === '19'
  10. [Dòng 514] if (this._b === '19'
  11. [Dòng 514] this._b === '16'
  12. [Dòng 514] this._b === '25'
  13. [Dòng 514] this._b === '33'
  14. [Dòng 515] this._b === '39'
  15. [Dòng 515] this._b === '9'
  16. [Dòng 515] this._b === '11'
  17. [Dòng 515] this._b === '17'
  18. [Dòng 516] this._b === '36'
  19. [Dòng 516] this._b === '44'
  20. [Dòng 517] this._b === '64'
  21. [Dòng 520] if (this._b === '20'
  22. [Dòng 523] if (this._b === '18') {
  23. [Dòng 529] return (bankId === '1'
  24. [Dòng 529] bankId === '20'
  25. [Dòng 529] bankId === '64');
  26. [Dòng 533] return (bankId === '36'
  27. [Dòng 533] bankId === '18'

== (111 điều kiện):
  1. [Dòng 167] this._auth == 0) {
  2. [Dòng 225] if (e.id == '31') {
  3. [Dòng 227] } else if (e.id == '80') {
  4. [Dòng 250] if (d.b.card_list == s) {
  5. [Dòng 271] if (item.b.id == '2'
  6. [Dòng 271] item.b.id == '67') {
  7. [Dòng 312] $event == 'true') {
  8. [Dòng 319] _b: this._b == '2' ? '67' : this._b
  9. [Dòng 340] if (bankid == 2
  10. [Dòng 340] bankid == 67) {
  11. [Dòng 344] || (off && !this.enabledTwoBankTech && ((bankid == '2'
  12. [Dòng 344] this.isOffTechcombank) || (bankid == '67'
  13. [Dòng 423] if (bankId == 1
  14. [Dòng 423] bankId == 4
  15. [Dòng 423] bankId == 7
  16. [Dòng 423] bankId == 8
  17. [Dòng 423] bankId == 9
  18. [Dòng 423] bankId == 10
  19. [Dòng 423] bankId == 11
  20. [Dòng 423] bankId == 14
  21. [Dòng 423] bankId == 15
  22. [Dòng 424] bankId == 16
  23. [Dòng 424] bankId == 17
  24. [Dòng 424] bankId == 20
  25. [Dòng 424] bankId == 22
  26. [Dòng 424] bankId == 23
  27. [Dòng 424] bankId == 24
  28. [Dòng 424] bankId == 25
  29. [Dòng 424] bankId == 30
  30. [Dòng 424] bankId == 33
  31. [Dòng 425] bankId == 34
  32. [Dòng 425] bankId == 35
  33. [Dòng 425] bankId == 36
  34. [Dòng 425] bankId == 37
  35. [Dòng 425] bankId == 38
  36. [Dòng 425] bankId == 39
  37. [Dòng 425] bankId == 40
  38. [Dòng 425] bankId == 41
  39. [Dòng 425] bankId == 42
  40. [Dòng 426] bankId == 43
  41. [Dòng 426] bankId == 44
  42. [Dòng 426] bankId == 45
  43. [Dòng 426] bankId == 46
  44. [Dòng 426] bankId == 47
  45. [Dòng 426] bankId == 48
  46. [Dòng 426] bankId == 49
  47. [Dòng 426] bankId == 50
  48. [Dòng 426] bankId == 51
  49. [Dòng 427] bankId == 52
  50. [Dòng 427] bankId == 53
  51. [Dòng 427] bankId == 54
  52. [Dòng 427] bankId == 55
  53. [Dòng 427] bankId == 56
  54. [Dòng 427] bankId == 57
  55. [Dòng 427] bankId == 58
  56. [Dòng 427] bankId == 59
  57. [Dòng 427] bankId == 60
  58. [Dòng 428] bankId == 61
  59. [Dòng 428] bankId == 62
  60. [Dòng 428] bankId == 63
  61. [Dòng 428] bankId == 64
  62. [Dòng 428] bankId == 65
  63. [Dòng 428] bankId == 66
  64. [Dòng 428] bankId == 68
  65. [Dòng 428] bankId == 69
  66. [Dòng 428] bankId == 70
  67. [Dòng 429] bankId == 71
  68. [Dòng 429] bankId == 72
  69. [Dòng 429] bankId == 73
  70. [Dòng 429] bankId == 32
  71. [Dòng 429] bankId == 74
  72. [Dòng 429] bankId == 75) {
  73. [Dòng 431] } else if (bankId == 6
  74. [Dòng 431] bankId == 31
  75. [Dòng 431] bankId == 80) {
  76. [Dòng 433] } else if (bankId == 3
  77. [Dòng 433] bankId == 12
  78. [Dòng 433] bankId == 19
  79. [Dòng 433] bankId == 27) {
  80. [Dòng 435] } else if (bankId == 18) {
  81. [Dòng 437] } else if (bankId == 5) {
  82. [Dòng 439] } else if (bankId == 2
  83. [Dòng 439] bankId == 67) {
  84. [Dòng 511] this._b == '55'
  85. [Dòng 511] this._b == '47'
  86. [Dòng 511] this._b == '48'
  87. [Dòng 511] this._b == '59'
  88. [Dòng 511] this._b == '73'
  89. [Dòng 511] this._b == '12') {
  90. [Dòng 514] this._b == '3'
  91. [Dòng 515] this._b == '43'
  92. [Dòng 515] this._b == '45'
  93. [Dòng 516] this._b == '54'
  94. [Dòng 516] this._b == '57'
  95. [Dòng 517] this._b == '61'
  96. [Dòng 517] this._b == '63'
  97. [Dòng 517] this._b == '67'
  98. [Dòng 517] this._b == '68'
  99. [Dòng 517] this._b == '69'
  100. [Dòng 517] this._b == '72'
  101. [Dòng 517] this._b == '74'
  102. [Dòng 517] this._b == '75') {
  103. [Dòng 520] this._b == '36'
  104. [Dòng 533] bankId == '55'
  105. [Dòng 533] bankId == '47'
  106. [Dòng 533] bankId == '48'
  107. [Dòng 533] bankId == '19'
  108. [Dòng 533] bankId == '59'
  109. [Dòng 533] bankId == '73'
  110. [Dòng 533] bankId == '5');
  111. [Dòng 548] if (item['id'] == this._b) {

!== (2 điều kiện):
  1. [Dòng 118] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  2. [Dòng 276] this.bankList = this.bankList.filter(item => item.b.id !== '67');

!= (7 điều kiện):
  1. [Dòng 156] if (params['locale'] != null) {
  2. [Dòng 159] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 163] this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
  4. [Dòng 192] if (!(strInstrument != null
  5. [Dòng 195] if (strInstrument.substring(0, 1) != '^'
  6. [Dòng 195] strInstrument.substr(strInstrument.length - 1) != '$') {
  7. [Dòng 401] if (bankid != null) {

================================================================================

📁 FILE 60: ewallet-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/ewallet-main/ewallet-main.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 31] filteredDataOtherWithoutZalo.length === 0"
  2. [Dòng 78] filteredDataOther.length === 0"

================================================================================

📁 FILE 61: ewallet-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/ewallet-main/ewallet-main.component.ts
📊 Thống kê: 24 điều kiện duy nhất
   - === : 10 lần
   - == : 8 lần
   - !== : 1 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (10 điều kiện):
  1. [Dòng 233] this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
  2. [Dòng 234] this.filteredDataOther = this.appList.filter(item => item.type === 'other');
  3. [Dòng 235] this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
  4. [Dòng 236] this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
  5. [Dòng 264] this.appList.length === 1) {
  6. [Dòng 266] if ((this.filteredDataMobile.length === 1
  7. [Dòng 266] this.filteredDataOtherMobile.length === 1)
  8. [Dòng 324] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  9. [Dòng 325] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  10. [Dòng 331] if (item.type === 'mobile_banking') {

== (8 điều kiện):
  1. [Dòng 156] this.themeConfig.deeplink_status == 'Off') {
  2. [Dòng 280] if (d.b.code == s) {
  3. [Dòng 330] if (item.available == true) {
  4. [Dòng 388] appcode == 'momo') {
  5. [Dòng 407] if (_re.status == '200'
  6. [Dòng 407] _re.status == '201') {
  7. [Dòng 410] if (appcode == 'grabpay'
  8. [Dòng 425] err.error.code == '04') {

!== (1 điều kiện):
  1. [Dòng 138] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (5 điều kiện):
  1. [Dòng 176] if (params['locale'] != null) {
  2. [Dòng 182] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 208] if (!(strInstrument != null
  4. [Dòng 274] this.filteredDataOtherWithoutZalo = this.filteredDataOther.filter(item => item.code != 'zalopay');
  5. [Dòng 362] if (appcode != null) {

================================================================================

📁 FILE 62: qr-ewallet.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/ewallet-main/qr-ewallet/qr-ewallet.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 63: qr-ewallet.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/ewallet-main/qr-ewallet/qr-ewallet.component.ts
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 83] this.img_code === 'momo') {
  2. [Dòng 161] if (valOut === 'auth') {

== (2 điều kiện):
  1. [Dòng 118] if (_re.status == '200'
  2. [Dòng 118] _re.status == '201') {

================================================================================

📁 FILE 64: menu.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/menu.component.html
📊 Thống kê: 16 điều kiện duy nhất
   - === : 1 lần
   - == : 8 lần
   - !== : 1 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 253] d_vrbank===true"

== (8 điều kiện):
  1. [Dòng 38] cardInfoEnable == 'card_name'
  2. [Dòng 41] cardInfoEnable == 'card_number'
  3. [Dòng 44] cardInfoEnable == 'card_date'"
  4. [Dòng 59] cardInfoEnable == 'card_date'
  5. [Dòng 67] cardInfoEnable == 'card_number' && _b==19
  6. [Dòng 174] d_vietqr == 1"
  7. [Dòng 195] type == 2 && !token
  8. [Dòng 221] _b == 1"

!== (1 điều kiện):
  1. [Dòng 182] d_vietqr !== 1"

!= (6 điều kiện):
  1. [Dòng 7] _paymentService.getCurrentPage() != 'otp'"
  2. [Dòng 38] cardInfoEnable == 'card_name' && _b != 19
  3. [Dòng 41] cardInfoEnable == 'card_number' && _b != 19
  4. [Dòng 52] cardInfoEnable == 'card_name' && _b != 19 && _b != 16
  5. [Dòng 182] type != 2 && ((onePaymentMethod && !token ) || token) && d_vietqr !== 1
  6. [Dòng 225] _b != 1"

================================================================================

📁 FILE 65: menu.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/menu.component.ts
📊 Thống kê: 173 điều kiện duy nhất
   - === : 12 lần
   - == : 97 lần
   - !== : 3 lần
   - != : 61 lần
--------------------------------------------------------------------------------

=== (12 điều kiện):
  1. [Dòng 623] if (this.d_vietqr === 1) {
  2. [Dòng 627] } else if (this.d_domestic === 1) {
  3. [Dòng 630] } else if (this.d_mobilebanking === 1) {
  4. [Dòng 679] if (count === 1
  5. [Dòng 682] if (offBanksArr[i] === this.lastDomescard)
  6. [Dòng 699] if (this._res.state === 'unpaid'
  7. [Dòng 699] this._res.state === 'not_paid') {
  8. [Dòng 811] if ('op' === auth
  9. [Dòng 852] } else if ('bank' === auth
  10. [Dòng 857] if (approval.method === 'REDIRECT') {
  11. [Dòng 860] } else if (approval.method === 'POST_REDIRECT') {
  12. [Dòng 1119] if (this.timeLeftPaypal === 0) {

== (97 điều kiện):
  1. [Dòng 197] if (el == 1) {
  2. [Dòng 199] } else if (el == 2) {
  3. [Dòng 201] } else if (el == 4) {
  4. [Dòng 203] } else if (el == 3) {
  5. [Dòng 231] if (!isNaN(_re.status) && (_re.status == '200'
  6. [Dòng 231] _re.status == '201') && _re.body != null) {
  7. [Dòng 237] if (('closed' == this._res_polling.state
  8. [Dòng 237] 'canceled' == this._res_polling.state
  9. [Dòng 237] 'expired' == this._res_polling.state)
  10. [Dòng 258] } else if ('paid' == this._res_polling.state) {
  11. [Dòng 270] this._res_polling.payments == null
  12. [Dòng 280] if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'authorization_required') {
  13. [Dòng 282] } else if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  14. [Dòng 288] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  15. [Dòng 290] this._paymentService.getCurrentPage() == 'enter_card'
  16. [Dòng 293] } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
  17. [Dòng 293] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
  18. [Dòng 310] } else if ('not_paid' == this._res_polling.state) {
  19. [Dòng 322] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
  20. [Dòng 431] params.type == 'ewallet-qr' ? true : false
  21. [Dòng 434] if (auth == 'auth') {
  22. [Dòng 436] detail.merchant.id == 'AMWAY') {
  23. [Dòng 444] if (cardInfo == 'card_name'
  24. [Dòng 444] cardInfo == 'card_number'
  25. [Dòng 444] cardInfo == 'card_date'
  26. [Dòng 444] cardInfo == '') {
  27. [Dòng 450] if (this._b == 19) {
  28. [Dòng 453] if (this._b == 19
  29. [Dòng 453] this._b == 16) {
  30. [Dòng 474] if (this.checkInvoiceState() == 1) {
  31. [Dòng 484] if (_re.status == '200'
  32. [Dòng 484] _re.status == '201') {
  33. [Dòng 506] if (this.themeConfig.default_method == 'Domestic'
  34. [Dòng 574] if (('closed' == this._res.state
  35. [Dòng 574] 'canceled' == this._res.state
  36. [Dòng 574] 'expired' == this._res.state
  37. [Dòng 574] 'paid' == this._res.state)
  38. [Dòng 578] if ('paid' == this._res.state
  39. [Dòng 646] } else if (this.d_vietqr == 1) {
  40. [Dòng 659] this._auth == 0) {
  41. [Dòng 670] if (count == 2
  42. [Dòng 700] if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
  43. [Dòng 700] this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
  44. [Dòng 702] if (this._res.payments[this._res.payments.length - 1].state == 'approved'
  45. [Dòng 753] if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required') {
  46. [Dòng 763] } else if (this._res.payments[this._res.payments.length - 1].state == 'pending') {
  47. [Dòng 768] } else if (this._res.payments[this._res.payments.length - 1].state == 'canceled') {
  48. [Dòng 772] this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  49. [Dòng 774] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id == 'vietqr'
  50. [Dòng 779] else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  51. [Dòng 779] this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
  52. [Dòng 800] if (idBrand == 'atm'
  53. [Dòng 876] this._res.payments[this._res.payments.length - 1].state == 'pending'
  54. [Dòng 878] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
  55. [Dòng 897] if ('paid' == this._res.state) {
  56. [Dòng 924] 'canceled' == this._res.state) {
  57. [Dòng 943] this._res.payments == null) {
  58. [Dòng 945] this._res.payments[this._res.payments.length - 1].state == 'pending') {
  59. [Dòng 955] if (this._res.currencies[0] == 'USD') {
  60. [Dòng 1029] if (e.type == 'other') {
  61. [Dòng 1049] if (e.type == 'mobile_banking') {
  62. [Dòng 1171] if (this._res_post.state == 'approved'
  63. [Dòng 1171] this._res_post.state == 'failed') {
  64. [Dòng 1180] } else if (this._res_post.state == 'authorization_required') {
  65. [Dòng 1181] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  66. [Dòng 1195] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  67. [Dòng 1292] if (res.status == '200'
  68. [Dòng 1292] res.status == '201') {
  69. [Dòng 1449] if (res?.state == 'canceled') {
  70. [Dòng 1484] return this._b == 3
  71. [Dòng 1484] this._b == 14
  72. [Dòng 1484] this._b == 27
  73. [Dòng 1484] this._b == 30
  74. [Dòng 1484] this._b == 33
  75. [Dòng 1484] this._b == 34
  76. [Dòng 1484] this._b == 35
  77. [Dòng 1484] this._b == 37
  78. [Dòng 1484] this._b == 38
  79. [Dòng 1484] this._b == 39
  80. [Dòng 1485] this._b == 40
  81. [Dòng 1485] this._b == 41
  82. [Dòng 1485] this._b == 42
  83. [Dòng 1485] this._b == 43
  84. [Dòng 1485] this._b == 44
  85. [Dòng 1485] this._b == 45
  86. [Dòng 1485] this._b == 46
  87. [Dòng 1485] this._b == 72
  88. [Dòng 1486] this._b == 74
  89. [Dòng 1486] this._b == 75
  90. [Dòng 1498] return this._b == 2
  91. [Dòng 1498] this._b == 20
  92. [Dòng 1498] this._b == 64
  93. [Dòng 1499] this._b == 67
  94. [Dòng 1499] this._b == 68
  95. [Dòng 1499] this._b == 73
  96. [Dòng 1499] this._b == 36
  97. [Dòng 1515] if (data._locale == 'en') {

!== (3 điều kiện):
  1. [Dòng 187] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  2. [Dòng 1085] if (_val !== 3) {
  3. [Dòng 1089] this._util.getElementParams(this.currentUrl, 't_id') !== '') {

!= (61 điều kiện):
  1. [Dòng 225] if (this._idInvoice != null
  2. [Dòng 225] this._paymentService.getState() != 'error') {
  3. [Dòng 231] _re.body != null) {
  4. [Dòng 238] this._res_polling.links != null
  5. [Dòng 238] this._res_polling.links.merchant_return != null //
  6. [Dòng 270] } else if (this._res_polling.merchant != null
  7. [Dòng 270] this._res_polling.merchant_invoice_reference != null
  8. [Dòng 272] } else if (this._res_polling.payments != null
  9. [Dòng 290] this._res_polling.payments[this._res_polling.payments.length - 1].instrument.brand != 'amigo') {
  10. [Dòng 294] this._res_polling.links.merchant_return != null//
  11. [Dòng 313] this._res_polling.payments != null
  12. [Dòng 321] if (this._res_polling.payments != null
  13. [Dòng 325] t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
  14. [Dòng 456] this.type.toString().length != 0) {
  15. [Dòng 462] if (params['locale'] != null) {
  16. [Dòng 468] if ('otp' != this._paymentService.getCurrentPage()) {
  17. [Dòng 472] if (this._paymentService.getInvoiceDetail() != null) {
  18. [Dòng 567] const tokenListsFilter = tokenLists.filter(item => item.id != tokenData);
  19. [Dòng 575] this._res.links != null
  20. [Dòng 575] this._res.links.merchant_return != null
  21. [Dòng 675] if (count != 1) {
  22. [Dòng 689] if (this._res.merchant != null
  23. [Dòng 689] this._res.merchant_invoice_reference != null) {
  24. [Dòng 692] if (this._res.merchant.address_details != null) {
  25. [Dòng 700] this._res.links != null//
  26. [Dòng 746] } else if (this._res.payments != null
  27. [Dòng 747] this._res.payments[this._res.payments.length - 1].instrument != null
  28. [Dòng 747] this._res.payments[this._res.payments.length - 1].instrument.issuer != null
  29. [Dòng 748] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
  30. [Dòng 779] else if (this._res.payments != null
  31. [Dòng 781] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
  32. [Dòng 782] this._res.payments[this._res.payments.length - 1].links != null
  33. [Dòng 782] this._res.payments[this._res.payments.length - 1].links.cancel != null
  34. [Dòng 782] this._res.payments[this._res.payments.length - 1].links.cancel.href != null
  35. [Dòng 800] this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
  36. [Dòng 801] this._res.payments[this._res.payments.length - 1].authorization != null
  37. [Dòng 801] this._res.payments[this._res.payments.length - 1].authorization.links != null) {
  38. [Dòng 811] this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
  39. [Dòng 811] this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
  40. [Dòng 814] if (this._res.payments[this._res.payments.length - 1].authorization != null
  41. [Dòng 814] this._res.payments[this._res.payments.length - 1].authorization.links != null
  42. [Dòng 820] auth = paramUserName != null ? paramUserName : ''
  43. [Dòng 922] this._res.links.merchant_return != null //
  44. [Dòng 943] } else if (this._res.merchant != null
  45. [Dòng 943] this._res.merchant_invoice_reference != null
  46. [Dòng 947] this._res.payments[this._res.payments.length - 1].instrument.brand != 'amigo') {
  47. [Dòng 993] if (!(strInstrument != null
  48. [Dòng 1014] if (this._translate.currentLang != language) {
  49. [Dòng 1091] } else if (this._res.payments != null) {
  50. [Dòng 1173] if (this._res_post.return_url != null) {
  51. [Dòng 1175] } else if (this._res_post.links != null
  52. [Dòng 1175] this._res_post.links.merchant_return != null
  53. [Dòng 1175] this._res_post.links.merchant_return.href != null) {
  54. [Dòng 1237] _re.body?.state != 'canceled')
  55. [Dòng 1293] if (res.body != null
  56. [Dòng 1293] res.body.links != null
  57. [Dòng 1293] res.body.links.merchant_return != null
  58. [Dòng 1294] res.body.links.merchant_return.href != null) {
  59. [Dòng 1426] if (_re.status != '200'
  60. [Dòng 1426] _re.status != '201') {
  61. [Dòng 1441] if (payment.state != 'authorization_required') {

================================================================================

📁 FILE 66: mobilebanking-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/mobilebanking-main/mobilebanking-main.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 1 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 175] filteredData.length === 0"

================================================================================

📁 FILE 67: mobilebanking-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/mobilebanking-main/mobilebanking-main.component.ts
📊 Thống kê: 31 điều kiện duy nhất
   - === : 12 lần
   - == : 14 lần
   - !== : 1 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (12 điều kiện):
  1. [Dòng 232] item.code === "vietinbankipay") {
  2. [Dòng 365] this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
  3. [Dòng 366] this.filteredDataOther = this.appList.filter(item => item.type === 'other');
  4. [Dòng 367] this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
  5. [Dòng 368] this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
  6. [Dòng 371] this.filteredSupportedApp = this.listAppSupport.filter(item => item.type === 'mobile_banking'
  7. [Dòng 387] this.appList.length === 1) {
  8. [Dòng 389] if ((this.filteredDataMobile.length === 1
  9. [Dòng 389] this.filteredDataOtherMobile.length === 1)
  10. [Dòng 471] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  11. [Dòng 472] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  12. [Dòng 478] if (item.type === 'mobile_banking') {

== (14 điều kiện):
  1. [Dòng 202] if (this.qr == 'MSB'
  2. [Dòng 202] this.qr.toLowerCase() == 'msb')) {
  3. [Dòng 213] item.code == "msbmbank_970426") {
  4. [Dòng 245] if (_re.status == '200'
  5. [Dòng 245] _re.status == '201') {
  6. [Dòng 281] this.themeConfig.deeplink_status == 'Off' ? false : true
  7. [Dòng 369] this.filteredDataDeeplink = this.filteredData.filter(item => item.deep_link == true);
  8. [Dòng 428] if (d.b.code == s) {
  9. [Dòng 477] if (item.available == true) {
  10. [Dòng 555] if (appcode == 'grabpay'
  11. [Dòng 555] appcode == 'momo') {
  12. [Dòng 592] err.error.code == '04') {
  13. [Dòng 758] if (type == 'both'
  14. [Dòng 760] } else if (type == 'both'

!== (1 điều kiện):
  1. [Dòng 262] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (4 điều kiện):
  1. [Dòng 305] if (params['locale'] != null) {
  2. [Dòng 311] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 326] if (!(strInstrument != null
  4. [Dòng 506] if (appcode != null) {

================================================================================

📁 FILE 68: qr-vnpay-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/mobilebanking-main/qr-guide-dialog/qr-vnpay-dialog.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (3 điều kiện):
  1. [Dòng 5] dialogType == 'deeplink'"
  2. [Dòng 6] dialogType == 'qr'"
  3. [Dòng 7] dialogType == 'both'"

================================================================================

📁 FILE 69: qr-vnpay-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/mobilebanking-main/qr-guide-dialog/qr-vnpay-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 70: list-bank-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/mobilebanking-main/qr-listbank/list-bank-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 71: list-bank-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/mobilebanking-main/qr-listbank/list-bank-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 72: queuing.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/queuing/queuing.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 73: queuing.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/queuing/queuing.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 62] if (this.locale == 'vi') {

!= (1 điều kiện):
  1. [Dòng 86] if (this.translate.currentLang != language) {

================================================================================

📁 FILE 74: domescard-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 3] [ngStyle]="{'border-color': (type === 2
  2. [Dòng 7] type === 2
  3. [Dòng 7] type === '2'

================================================================================

📁 FILE 75: domescard-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 76: vietqr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/vietqr-main/vietqr-main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 77: vietqr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/main/menu/vietqr-main/vietqr-main.component.ts
📊 Thống kê: 8 điều kiện duy nhất
   - === : 1 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 261] if (valOut === 'auth') {

== (6 điều kiện):
  1. [Dòng 111] if (params['locale'] == 'en') {
  2. [Dòng 163] if (res.status == '200'
  3. [Dòng 163] res.status == '201') {
  4. [Dòng 193] if (payment?.state == 'failed'
  5. [Dòng 193] payment?.state == 'canceled'
  6. [Dòng 193] payment?.state == 'expired') {

!= (1 điều kiện):
  1. [Dòng 167] if (res.body?.state != 'created') {

================================================================================

📁 FILE 78: bnpl-management.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/model/bnpl-management.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 79: homecredit-management.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/model/homecredit-management.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 80: kbank-management.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/model/kbank-management.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 81: overlay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/overlay/overlay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 82: overlay.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/overlay/overlay.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 83: overlay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/overlay/overlay.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 84: bank-amount.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/pipe/bank-amount.pipe.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 11] return ((a.id == id
  2. [Dòng 11] a.code == id) && a.type.includes(type));

================================================================================

📁 FILE 85: auth.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/services/auth.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 86: card_info.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/services/card_info.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 87: close-dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/services/close-dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 88: data.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/services/data.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 80] const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
  2. [Dòng 80] item.method === method) : null;

================================================================================

📁 FILE 89: deep_link.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/services/deep_link.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 9] if (isIphone == true) {
  2. [Dòng 22] } else if (isAndroid == true) {

================================================================================

📁 FILE 90: dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/services/dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 91: handle_bnpl_token.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/services/handle_bnpl_token.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 92: multiple_method.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/services/multiple_method.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 93: payment.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/services/payment.service.ts
📊 Thống kê: 15 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 11 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 601] if (res.status == '200'
  2. [Dòng 601] res.status == '201') {
  3. [Dòng 612] return countPayment == maxPayment
  4. [Dòng 635] if (this.getLatestPayment().state == 'canceled')

!= (11 điều kiện):
  1. [Dòng 115] if (idInvoice != null
  2. [Dòng 115] idInvoice != 0)
  3. [Dòng 125] idInvoice != 0) {
  4. [Dòng 267] let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
  5. [Dòng 282] if (this._merchantid != null
  6. [Dòng 282] this._tranref != null
  7. [Dòng 282] this._state != null
  8. [Dòng 370] urlCancel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
  9. [Dòng 397] if (paymentId != null) {
  10. [Dòng 488] if (res?.status != 200
  11. [Dòng 488] res?.status != 201) return;

================================================================================

📁 FILE 94: qr.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/services/qr.service.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 28] state == 'authorization_required'

================================================================================

📁 FILE 95: vietqr.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/services/vietqr.service.ts
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 44] if (_re.status == '200'
  2. [Dòng 44] _re.status == '201') {
  3. [Dòng 59] } else if (latestPayment?.state == "failed") {
  4. [Dòng 65] if (res?.state == 'canceled') {

!= (1 điều kiện):
  1. [Dòng 56] if (latestPayment?.instrument?.type != "vietqr") {

================================================================================

📁 FILE 96: success.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/success/success.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 52] amigo_type == 'SP'"
  2. [Dòng 96] amigo_type == 'PL'"

================================================================================

📁 FILE 97: success.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/success/success.component.ts
📊 Thống kê: 10 điều kiện duy nhất
   - === : 5 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 137] params.timeout === 'true') {
  2. [Dòng 156] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  3. [Dòng 156] _re.body.state === 'unpaid');
  4. [Dòng 219] params.name === 'CUSTOMER_INTIME'
  5. [Dòng 219] params.code === '09') {

== (3 điều kiện):
  1. [Dòng 167] if (this.res.currencies[0] == 'USD') {
  2. [Dòng 195] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
  3. [Dòng 206] this.res.themes.theme == 'general') {

!= (2 điều kiện):
  1. [Dòng 193] } else if (this.res.payments[this.res.payments.length - 1].instrument.issuer.brand != null
  2. [Dòng 194] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id != null

================================================================================

📁 FILE 98: index.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/translate/index.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 99: lang-en.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/translate/lang-en.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 100: lang-vi.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/translate/lang-vi.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 101: translate.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/translate/translate.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 102: translate.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/translate/translate.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 37] if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
  2. [Dòng 38] else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';

================================================================================

📁 FILE 103: translations.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/translate/translations.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 104: apps-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/util/apps-info.ts
📊 Thống kê: 4 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 569] if (e.name == bankSwift) { // TODO: get by swift
  2. [Dòng 579] if (e.code == appCode) { // TODO: get by swift
  3. [Dòng 589] if (e.id == appId) { // TODO: get by swift
  4. [Dòng 597] return this.apps.find(e => e.code == appCode);

================================================================================

📁 FILE 105: banks-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/util/banks-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1110] if (+e.id == bankId) {
  2. [Dòng 1160] if (e.swiftCode == bankSwift) {

================================================================================

📁 FILE 106: error-handler.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/util/error-handler.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 9] err?.status === 400
  2. [Dòng 10] err?.error?.name === 'INVALID_CARD_FEE'

================================================================================

📁 FILE 107: util.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/app/util/util.ts
📊 Thống kê: 41 điều kiện duy nhất
   - === : 15 lần
   - == : 19 lần
   - !== : 3 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (15 điều kiện):
  1. [Dòng 49] if (v.length === 2
  2. [Dòng 49] this.flag.length === 3
  3. [Dòng 49] this.flag.charAt(this.flag.length - 1) === '/') {
  4. [Dòng 53] if (v.length === 1) {
  5. [Dòng 55] } else if (v.length === 2) {
  6. [Dòng 58] v.length === 2) {
  7. [Dòng 66] if (len === 2) {
  8. [Dòng 179] if (M[1] === 'Chrome') {
  9. [Dòng 404] if (param === key) {
  10. [Dòng 566] pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
  11. [Dòng 570] target === 0
  12. [Dòng 642] if (cardTypeBank === 'bank_card_number') {
  13. [Dòng 645] } else if (cardTypeBank === 'bank_account_number') {
  14. [Dòng 695] if (event.keyCode === 8
  15. [Dòng 695] event.key === "Backspace"

== (19 điều kiện):
  1. [Dòng 16] if (temp.length == 0) {
  2. [Dòng 23] return (counter % 10 == 0);
  3. [Dòng 153] if (this.checkCount == 1) {
  4. [Dòng 165] if (results == null) {
  5. [Dòng 198] if (c.length == 3) {
  6. [Dòng 211] d = d == undefined ? '
  7. [Dòng 212] t = t == undefined ? '.' : t
  8. [Dòng 239] expMonth.length == 2
  9. [Dòng 243] expYear.length == 2
  10. [Dòng 392] return results == null ? null : results[1]
  11. [Dòng 695] event.inputType == 'deleteContentBackward') {
  12. [Dòng 696] if (event.target.name == 'exp_date'
  13. [Dòng 704] event.inputType == 'insertCompositionText') {
  14. [Dòng 718] if (((_val.length == 4
  15. [Dòng 718] _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  16. [Dòng 718] _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  17. [Dòng 744] iss_date.length == 4
  18. [Dòng 744] iss_date.search('/') == -1)
  19. [Dòng 745] iss_date.length == 5))

!== (3 điều kiện):
  1. [Dòng 399] queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
  2. [Dòng 400] if (queryString !== '') {
  3. [Dòng 570] if (target !== 0

!= (4 điều kiện):
  1. [Dòng 181] if (tem != null) {
  2. [Dòng 186] if ((tem = ua.match(/version\/(\d+)/i)) != null) {
  3. [Dòng 640] if (ua.indexOf('safari') != -1
  4. [Dòng 697] if (v.length != 3) {

================================================================================

📁 FILE 108: qrcode.js
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/assets/script/qrcode.js
📊 Thống kê: 67 điều kiện duy nhất
   - === : 2 lần
   - == : 53 lần
   - !== : 0 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 2255] if (typeof define === 'function'
  2. [Dòng 2257] } else if (typeof exports === 'object') {

== (53 điều kiện):
  1. [Dòng 68] if (_dataCache == null) {
  2. [Dòng 85] if ( (0 <= r && r <= 6 && (c == 0
  3. [Dòng 85] c == 6) )
  4. [Dòng 86] || (0 <= c && c <= 6 && (r == 0
  5. [Dòng 86] r == 6) )
  6. [Dòng 107] if (i == 0
  7. [Dòng 122] _modules[r][6] = (r % 2 == 0);
  8. [Dòng 129] _modules[6][c] = (c % 2 == 0);
  9. [Dòng 152] if (r == -2
  10. [Dòng 152] r == 2
  11. [Dòng 152] c == -2
  12. [Dòng 152] c == 2
  13. [Dòng 153] || (r == 0
  14. [Dòng 153] c == 0) ) {
  15. [Dòng 169] ( (bits >> i) & 1) == 1);
  16. [Dòng 226] if (col == 6) col -= 1;
  17. [Dòng 232] if (_modules[row][col - c] == null) {
  18. [Dòng 237] dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
  19. [Dòng 249] if (bitIndex == -1) {
  20. [Dòng 458] margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
  21. [Dòng 498] if (typeof arguments[0] == 'object') {
  22. [Dòng 587] margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
  23. [Dòng 733] if (b == -1) throw 'eof';
  24. [Dòng 741] if (b0 == -1) break;
  25. [Dòng 767] if (typeof b == 'number') {
  26. [Dòng 768] if ( (b & 0xff) == b) {
  27. [Dòng 910] return function(i, j) { return (i + j) % 2 == 0
  28. [Dòng 912] return function(i, j) { return i % 2 == 0
  29. [Dòng 914] return function(i, j) { return j % 3 == 0
  30. [Dòng 916] return function(i, j) { return (i + j) % 3 == 0
  31. [Dòng 918] return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
  32. [Dòng 920] return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
  33. [Dòng 922] return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
  34. [Dòng 924] return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
  35. [Dòng 1011] if (r == 0
  36. [Dòng 1011] c == 0) {
  37. [Dòng 1015] if (dark == qrcode.isDark(row + r, col + c) ) {
  38. [Dòng 1036] if (count == 0
  39. [Dòng 1036] count == 4) {
  40. [Dòng 1149] if (typeof num.length == 'undefined') {
  41. [Dòng 1155] num[offset] == 0) {
  42. [Dòng 1495] if (typeof rsBlock == 'undefined') {
  43. [Dòng 1538] return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
  44. [Dòng 1543] _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
  45. [Dòng 1599] if (data.length - i == 1) {
  46. [Dòng 1601] } else if (data.length - i == 2) {
  47. [Dòng 1866] } else if (n == 62) {
  48. [Dòng 1868] } else if (n == 63) {
  49. [Dòng 1928] if (_buflen == 0) {
  50. [Dòng 1937] if (c == '=') {
  51. [Dòng 1961] } else if (c == 0x2b) {
  52. [Dòng 1963] } else if (c == 0x2f) {
  53. [Dòng 2133] if (table.size() == (1 << bitLength) ) {

!= (12 điều kiện):
  1. [Dòng 119] if (_modules[r][6] != null) {
  2. [Dòng 126] if (_modules[6][c] != null) {
  3. [Dòng 144] if (_modules[row][col] != null) {
  4. [Dòng 365] while (buffer.getLengthInBits() % 8 != 0) {
  5. [Dòng 750] if (count != numChars) {
  6. [Dòng 751] throw count + ' != ' + numChars
  7. [Dòng 878] while (data != 0) {
  8. [Dòng 1733] if (test.length != 2
  9. [Dòng 1733] ( (test[0] << 8) | test[1]) != code) {
  10. [Dòng 1894] if (_length % 3 != 0) {
  11. [Dòng 2067] if ( (data >>> length) != 0) {
  12. [Dòng 2178] return typeof _map[key] != 'undefined'

================================================================================

📁 FILE 109: environment.prod.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/environments/environment.prod.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 110: environment.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/environments/environment.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 111: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 112: karma.conf.js
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/karma.conf.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 113: main.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/main.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 114: polyfills.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/polyfills.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 115: test.ts
📍 Đường dẫn: /root/projects/onepay/paygate-traveloka/src/test.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📋 TÓM TẮT TẤT CẢ ĐIỀU KIỆN DUY NHẤT:
====================================================================================================

=== (150 điều kiện duy nhất):
------------------------------------------------------------
1. return lang === this._translate.currentLang
2. checkDupTran === false"
3. isPopupSupport === 'True') || (rePayment
4. isPopupSupport === 'True'"
5. isPopupSupport === 'True')">
6. params.timeout === 'true') {
7. params.kbank === 'true') {
8. this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
9. _re.body.state === 'unpaid');
10. if (this.errorCode === 'overtime'
11. this.errorCode === '253') {
12. params.name === 'CUSTOMER_INTIME'
13. params.code === '09') {
14. if (this.timeLeft === 0) {
15. if (YY % 400 === 0
16. YY % 4 === 0)) {
17. if (YYYY % 400 === 0
18. YYYY % 4 === 0)) {
19. params['code'] === '09') {
20. params.name === 'CUSTOMER_INTIME')) {
21. if (target.tagName === 'A'
22. if (isIE[0] === 'MSIE'
23. +isIE[1] === 10) {
24. if ((_val.value.substr(-1) === ' '
25. _val.value.length === 24) {
26. if (this.cardTypeBank === 'bank_card_number') {
27. } else if (this.cardTypeBank === 'bank_account_number') {
28. } else if (this.cardTypeBank === 'bank_username') {
29. } else if (this.cardTypeBank === 'bank_customer_code') {
30. this.cardTypeBank === 'bank_card_number'
31. if (this.cardTypeBank === 'bank_account_number') {
32. this.cardTypeBank === 'bank_card_number') {
33. if (this.cardTypeBank === 'bank_card_number'
34. if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
35. if (event.keyCode === 8
36. event.key === "Backspace"
37. if ((this.cardTypeBank === 'bank_account_number'
38. this.cardTypeBank === 'bank_username'
39. this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
40. if (valIn === this._translate.instant('bank_card_number')) {
41. } else if (valIn === this._translate.instant('bank_account_number')) {
42. } else if (valIn === this._translate.instant('bank_username')) {
43. } else if (valIn === this._translate.instant('bank_customer_code')) {
44. if (this.cardName === undefined
45. this.cardName === '') {
46. if (this.valueDate === undefined
47. this.valueDate === '') {
48. if (this.cardTypeOcean === 'IB') {
49. } else if (this.cardTypeOcean === 'MB') {
50. if (_val.value.substr(0, 2) === '84') {
51. } else if (this.cardTypeOcean === 'ATM') {
52. if (approval.method === 'REDIRECT') {
53. } else if (approval.method === 'POST_REDIRECT') {
54. this.cardTypeOcean === 'ATM')
55. || (this.cardTypeOcean === 'IB'
56. if (_val.value && (this.cardTypeBank === 'bank_card_number'
57. if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
58. if (_val.value === ''
59. _val.value === null
60. _val.value === undefined) {
61. this.cardTypeOcean === 'MB') {
62. this.cardTypeOcean === 'IB'
63. if ((this.cardTypeBank === 'bank_card_number'
64. ready===1"
65. if (this.timeLeft === 10) {
66. if (this.runTime === true) {
67. if (this.runTime === true) this.submitCardBanking();
68. if (this.timeLeft === 1) {
69. } else if (valIn === this._translate.instant('internet_banking')) {
70. if (e === '002') {
71. } else if (e === '001') {
72. <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
73. [class.red_border_no_background]="c_expdate && (valueDate.length === 0
74. valueDate.trim().length === 0)"
75. if (v.length === 2
76. this.flag.length === 3
77. this.flag.charAt(this.flag.length - 1) === '/') {
78. if (v.length === 1) {
79. } else if (v.length === 2) {
80. v.length === 2) {
81. if (len === 2) {
82. filteredData.length === 0"
83. if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
84. filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
85. if (valOut === 'auth') {
86. if (this._b === '1'
87. this._b === '20'
88. this._b === '64') {
89. if (this._b === '36'
90. this._b === '18'
91. this._b === '19'
92. if (this._b === '19'
93. this._b === '16'
94. this._b === '25'
95. this._b === '33'
96. this._b === '39'
97. this._b === '9'
98. this._b === '11'
99. this._b === '17'
100. this._b === '36'
101. this._b === '44'
102. this._b === '64'
103. if (this._b === '20'
104. if (this._b === '18') {
105. return (bankId === '1'
106. bankId === '20'
107. bankId === '64');
108. return (bankId === '36'
109. bankId === '18'
110. filteredDataOtherWithoutZalo.length === 0"
111. filteredDataOther.length === 0"
112. this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
113. this.filteredDataOther = this.appList.filter(item => item.type === 'other');
114. this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
115. this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
116. this.appList.length === 1) {
117. if ((this.filteredDataMobile.length === 1
118. this.filteredDataOtherMobile.length === 1)
119. item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
120. filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
121. if (item.type === 'mobile_banking') {
122. this.img_code === 'momo') {
123. d_vrbank===true"
124. if (this.d_vietqr === 1) {
125. } else if (this.d_domestic === 1) {
126. } else if (this.d_mobilebanking === 1) {
127. if (count === 1
128. if (offBanksArr[i] === this.lastDomescard)
129. if (this._res.state === 'unpaid'
130. this._res.state === 'not_paid') {
131. if ('op' === auth
132. } else if ('bank' === auth
133. if (this.timeLeftPaypal === 0) {
134. item.code === "vietinbankipay") {
135. this.filteredSupportedApp = this.listAppSupport.filter(item => item.type === 'mobile_banking'
136. [ngStyle]="{'border-color': (type === 2
137. type === 2
138. type === '2'
139. const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
140. item.method === method) : null;
141. err?.status === 400
142. err?.error?.name === 'INVALID_CARD_FEE'
143. if (M[1] === 'Chrome') {
144. if (param === key) {
145. pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
146. target === 0
147. if (cardTypeBank === 'bank_card_number') {
148. } else if (cardTypeBank === 'bank_account_number') {
149. if (typeof define === 'function'
150. } else if (typeof exports === 'object') {

== (527 điều kiện duy nhất):
------------------------------------------------------------
1. 'vi' == params['locale']) {
2. 'en' == params['locale']) {
3. errorCode == '11'"
4. errorCode && (errorCode == '253' || errorCode == 'overtime')
5. errorCode == 'overtime')">
6. <div class="footer-button" *ngIf="(isSent == false
7. isSent == false
8. <div class="cancel_transaction" *ngIf="checkDupTran" [class.select_only]="!(isSent == false
9. <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
10. (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
11. <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
12. !(errorCode == 'overtime' || errorCode == '253') && isBack && !invoiceNearExpire && !paymentPending
13. if (params && (params['bnpl'] == 'true')) {
14. if (params && (params['bnpl'] == 'false')) {
15. this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'amigo') {
16. this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'kredivo') {
17. this.res.themes.theme == 'general') {
18. params.response_code == 'overtime') {
19. if (_re.status == '200'
20. _re.status == '201') {
21. if (_re2.status == '200'
22. _re2.status == '201') {
23. if (this.errorCode == 'overtime'
24. this.errorCode == '253') {
25. if (this.paymentInformation.type == 'bnpl') {
26. if (this.paymentInformation.provider == 'amigo'
27. this.errorCode == '2') {
28. else if (this.paymentInformation.provider == 'kbank'
29. else if (this.paymentInformation.provider == 'homecredit'
30. else if (this.paymentInformation.provider == 'kredivo'
31. this.res.state == 'canceled') {
32. if (lastPayment?.state == 'pending') {
33. if (this.isTimePause == false) {
34. if ((dataPassed.status == '200'
35. dataPassed.status == '201') && dataPassed.body != null) {
36. dataPassed.body.themes.logo_full == 'True') {
37. payments[payments.length - 1].state == 'pending'
38. payments[payments.length - 1].instrument.issuer.brand.id == 'amigo') {
39. if (this.locale == 'en') {
40. if (bankId == 3
41. bankId == 61
42. bankId == 8
43. bankId == 49
44. bankId == 48
45. bankId == 10
46. bankId == 53
47. bankId == 17
48. bankId == 65
49. bankId == 23
50. bankId == 52
51. bankId == 27
52. bankId == 66
53. bankId == 9
54. bankId == 54
55. bankId == 37
56. bankId == 38
57. bankId == 39
58. bankId == 40
59. bankId == 42
60. bankId == 44
61. bankId == 72
62. bankId == 59
63. bankId == 51
64. bankId == 64
65. bankId == 58
66. bankId == 56
67. bankId == 55
68. bankId == 60
69. bankId == 68
70. bankId == 74
71. bankId == 75 //MAFC Napas
72. if (name == 'MAFC')
73. if (this._b == 27
74. this._b == 19) {
75. if (this._b == 19) {//19BIDV
76. } else if (this._b == 27
77. this._b == 32) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
78. if (this._b == 27) {
79. if (this._b == 32) {
80. if (this._b == 19
81. this._b == 27
82. this._b == 32) {
83. if (this.checkBin(_val.value) && (this._b == 27
84. this._b == 32)) {
85. this._b == 32) {//** check đúng số thẻ 3-tpb;27-pvcombank;
86. if (this.checkBin(v) && (this._b == 27
87. this._b == 18)) {
88. event.inputType == 'deleteContentBackward') {
89. if (event.target.name == 'exp_date'
90. event.inputType == 'insertCompositionText') {
91. if (((this.valueDate.length == 4
92. this.valueDate.search('/') == -1) || this.valueDate.length == 5)
93. this.valueDate.length == 5)
94. if (temp.length == 0) {
95. return (counter % 10 == 0);
96. if (this._b == 19) {
97. } else if (this._b == 27) {
98. } else if (this._b == 32) {
99. if (this.cardName != null && this.cardName.length > 0 && (this._b == 19
100. this._b == 32)) {//3-TPB ;19-BIDV;27-pvcombank;
101. if (this._res_post.state == 'approved'
102. this._res_post.state == 'failed') {
103. } else if (this._res_post.state == 'authorization_required') {
104. if (cardNo.length == 16
105. cardNo.length == 19) {
106. if ((cardNo.length == 16
107. if ((cardNo.length == 16 || (cardNo.length == 19
108. && ((this._b == 18
109. cardNo.length == 19) || this._b != 18)
110. if (this._b == +e.id) {
111. return (value.length == 2)
112. || (this._util.checkValidExpireMonthNew(value) && (this._b == 20
113. this._b == 33
114. this._b == 39
115. this._b == 43
116. this._b == 45
117. this._b == 64
118. this._b == 67
119. this._b == 68
120. this._b == 72
121. this._b == 73
122. this._b == 36))) //sonnh them Vietbank 72
123. || (this._util.checkValidExpireYearNew(value) && (this._b == 20
124. let validMonth = ((this._inExpMonth.length == 2))
125. || (this._util.checkValidExpireMonthNew(this._inExpMonth) && (this._b == 20
126. this._b == 36)));
127. let validYear = ((this._inExpYear.length == 2))
128. || (this._util.checkValidExpireYearNew(this._inExpYear) && (this._b == 20
129. this.valueDate.length == 4
130. this.valueDate.search('/') == -1)
131. this.valueDate.length == 5))
132. _b == 68"
133. if (this._b == 18) { //18Oceanbank-ocb
134. if (this._b == 18) {
135. if (this._b == 18
136. this.cardTypeOcean == 'ATM') {
137. if (cardType == this._translate.instant('internetbanking')
138. } else if (cardType == this._translate.instant('mobilebanking')
139. } else if (cardType == this._translate.instant('atm')
140. if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
141. if (this.cardTypeOcean == 'IB') {
142. } else if (this.cardTypeOcean == 'MB') {
143. } else if (this.cardTypeOcean == 'ATM') {
144. this._b == 18))) {
145. } else if (this._b == 18
146. (cardTypeBank == 'bank_card_number') && (_b == 67 || (!isOffTechcombankNapas && checkTwoEnabled))
147. (cardTypeBank == 'bank_card_number') &&(_b == 67 || (_b == 2 && !isOffTechcombankNapas && checkTwoEnabled)) && ready===1
148. (cardTypeBank == 'internet_banking') && (_b == 2 || (!isOffTechcombank && checkTwoEnabled))
149. if (this._b == 67
150. this._b == 2) {//Techcombank Napas
151. if ((this._b == 2
152. !this.checkTwoEnabled) || (this._b == 2
153. } else if ((this._b == 2
154. this._b == 67) {
155. return this._b == 2
156. this.d_card_date == true)) {
157. this.checkMod10(cardNo) == true
158. if (this._b != 68 || (this._b == 68
159. return ((value.length == 4
160. value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
161. value.length == 5) && parseInt(value.split('/')[0]
162. || (this._util.checkValidExpireMonthYear(value) && (this._b == 2
163. this._b == 20
164. this._b == 72))) //sonnh them Vietbank 72
165. let validYear = (this._b == 67
166. this._b == 2) ? this._util.checkValidExpireMonthYear2(this._inExpMonth
167. this._inExpDate.length == 4
168. this._inExpDate.search('/') == -1)
169. this._inExpDate.length == 5))
170. || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 20
171. this._b == 2
172. this._b == 72)));
173. return value.length == 2
174. value) && (this._b == 2
175. _b == 22 || _b == 24
176. _b == 8"
177. _locale == 'en'"
178. _b == 18"
179. if (this._b == 8) {//MB Bank
180. if (this._b == 18) {//Oceanbank
181. if (this._b == 8) {
182. if (this._b == 12) { //SHB
183. } else if (this._res.state == 'authorization_required') {
184. if (this.challengeCode == '') {
185. if (this._b == 12) {
186. if (this._b == 18) {//8-MB Bank;18-oceanbank
187. cardTypeBank == '002'"
188. cardTypeBank == '001'"
189. if (this._b == 12) {// 12SHB
190. this.cardTypeBank == '002') {
191. if (this._b == 12
192. this.cardTypeBank == '001') {
193. if (this._b == 1
194. this._b == 14
195. this._b == 15
196. this._b == 24
197. this._b == 8
198. this._b == 10
199. this._b == 22
200. this._b == 23
201. this._b == 30
202. this._b == 11
203. this._b == 17
204. this._b == 12) {
205. return this._b == 20
206. this._b == 36
207. this._b == 74
208. this._b == 75
209. return this._b == 9
210. this._b == 16
211. this._b == 25
212. this._b == 44
213. this._b == 54
214. this._b == 57
215. this._b == 59
216. this._b == 61
217. this._b == 63
218. this._b == 69
219. (cardNo.length == 19
220. (cardNo.length == 19 && (this._b == 1
221. this._b == 4
222. this._b == 55
223. this._b == 47
224. this._b == 48
225. this._b == 59))
226. this._util.checkMod10(cardNo) == true
227. if (this._b == 2) {
228. } else if (this._b == 6) {
229. } else if (this._b == 31) {
230. } else if (this._b == 80) {
231. this._b == 17) {
232. let validYear = this._b == 20 ? this._util.checkValidExpireMonthYear2(this._inExpMonth, this._inExpYear) : this._util.checkValidIssueMonthYear2(this._inExpMonth, this._inExpYear)
233. cardNo.length == 19)
234. value) && (this._b == 20
235. if (this._b == 5) {//5-vib;
236. if (this._b == 5) {
237. if (this.checkBin(_val.value) && (this._b == 5)) {
238. if (this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
239. if (this.checkBin(v) && (this._b == 5)) {
240. _formCard.exp_date.length == 5
241. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 5)) {//27-pvcombank;3-TPB ;
242. if (this.cardName != null && this.cardName.length > 0 && (this._b == 5)) {//3-TPB ;19-BIDV;27-pvcombank;
243. if (valIn == 1) {
244. } else if (valIn == 2) {
245. this.c_expdate = !(((this.valueDate.length == 4
246. return this._b == 11
247. this.d_card_date == true)){
248. this._b == 9) {
249. (!token&&_auth==0&&vietcombankGroupSelected) || (token && _b == 16)
250. _b == 16)">
251. _auth==0 && techcombankGroupSelected
252. _auth==0 && onepaynapasGroupSelected
253. _auth==0 && bankaccountGroupSelected
254. _b == 12"
255. _b == 3"
256. _auth==0 && oceanbankGroupSelected
257. _auth==0 && vibbankGroupSelected
258. (token || _auth==1) && _b != 16
259. this._auth == 0) {
260. if (e.id == '31') {
261. } else if (e.id == '80') {
262. if (d.b.card_list == s) {
263. if (item.b.id == '2'
264. item.b.id == '67') {
265. $event == 'true') {
266. _b: this._b == '2' ? '67' : this._b
267. if (bankid == 2
268. bankid == 67) {
269. || (off && !this.enabledTwoBankTech && ((bankid == '2'
270. this.isOffTechcombank) || (bankid == '67'
271. if (bankId == 1
272. bankId == 4
273. bankId == 7
274. bankId == 11
275. bankId == 14
276. bankId == 15
277. bankId == 16
278. bankId == 20
279. bankId == 22
280. bankId == 24
281. bankId == 25
282. bankId == 30
283. bankId == 33
284. bankId == 34
285. bankId == 35
286. bankId == 36
287. bankId == 41
288. bankId == 43
289. bankId == 45
290. bankId == 46
291. bankId == 47
292. bankId == 50
293. bankId == 57
294. bankId == 62
295. bankId == 63
296. bankId == 69
297. bankId == 70
298. bankId == 71
299. bankId == 73
300. bankId == 32
301. bankId == 75) {
302. } else if (bankId == 6
303. bankId == 31
304. bankId == 80) {
305. } else if (bankId == 3
306. bankId == 12
307. bankId == 19
308. bankId == 27) {
309. } else if (bankId == 18) {
310. } else if (bankId == 5) {
311. } else if (bankId == 2
312. bankId == 67) {
313. this._b == '55'
314. this._b == '47'
315. this._b == '48'
316. this._b == '59'
317. this._b == '73'
318. this._b == '12') {
319. this._b == '3'
320. this._b == '43'
321. this._b == '45'
322. this._b == '54'
323. this._b == '57'
324. this._b == '61'
325. this._b == '63'
326. this._b == '67'
327. this._b == '68'
328. this._b == '69'
329. this._b == '72'
330. this._b == '74'
331. this._b == '75') {
332. this._b == '36'
333. bankId == '55'
334. bankId == '47'
335. bankId == '48'
336. bankId == '19'
337. bankId == '59'
338. bankId == '73'
339. bankId == '5');
340. if (item['id'] == this._b) {
341. this.themeConfig.deeplink_status == 'Off') {
342. if (d.b.code == s) {
343. if (item.available == true) {
344. appcode == 'momo') {
345. if (appcode == 'grabpay'
346. err.error.code == '04') {
347. cardInfoEnable == 'card_name'
348. cardInfoEnable == 'card_number'
349. cardInfoEnable == 'card_date'"
350. cardInfoEnable == 'card_date'
351. cardInfoEnable == 'card_number' && _b==19
352. d_vietqr == 1"
353. type == 2 && !token
354. _b == 1"
355. if (el == 1) {
356. } else if (el == 2) {
357. } else if (el == 4) {
358. } else if (el == 3) {
359. if (!isNaN(_re.status) && (_re.status == '200'
360. _re.status == '201') && _re.body != null) {
361. if (('closed' == this._res_polling.state
362. 'canceled' == this._res_polling.state
363. 'expired' == this._res_polling.state)
364. } else if ('paid' == this._res_polling.state) {
365. this._res_polling.payments == null
366. if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'authorization_required') {
367. } else if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
368. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
369. this._paymentService.getCurrentPage() == 'enter_card'
370. } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
371. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
372. } else if ('not_paid' == this._res_polling.state) {
373. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
374. params.type == 'ewallet-qr' ? true : false
375. if (auth == 'auth') {
376. detail.merchant.id == 'AMWAY') {
377. if (cardInfo == 'card_name'
378. cardInfo == 'card_number'
379. cardInfo == 'card_date'
380. cardInfo == '') {
381. this._b == 16) {
382. if (this.checkInvoiceState() == 1) {
383. if (this.themeConfig.default_method == 'Domestic'
384. if (('closed' == this._res.state
385. 'canceled' == this._res.state
386. 'expired' == this._res.state
387. 'paid' == this._res.state)
388. if ('paid' == this._res.state
389. } else if (this.d_vietqr == 1) {
390. if (count == 2
391. if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
392. this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
393. if (this._res.payments[this._res.payments.length - 1].state == 'approved'
394. if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required') {
395. } else if (this._res.payments[this._res.payments.length - 1].state == 'pending') {
396. } else if (this._res.payments[this._res.payments.length - 1].state == 'canceled') {
397. this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
398. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id == 'vietqr'
399. else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
400. this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
401. if (idBrand == 'atm'
402. this._res.payments[this._res.payments.length - 1].state == 'pending'
403. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
404. if ('paid' == this._res.state) {
405. 'canceled' == this._res.state) {
406. this._res.payments == null) {
407. this._res.payments[this._res.payments.length - 1].state == 'pending') {
408. if (this._res.currencies[0] == 'USD') {
409. if (e.type == 'other') {
410. if (e.type == 'mobile_banking') {
411. if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
412. } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
413. if (res.status == '200'
414. res.status == '201') {
415. if (res?.state == 'canceled') {
416. return this._b == 3
417. this._b == 34
418. this._b == 35
419. this._b == 37
420. this._b == 38
421. this._b == 40
422. this._b == 41
423. this._b == 42
424. this._b == 46
425. if (data._locale == 'en') {
426. if (this.qr == 'MSB'
427. this.qr.toLowerCase() == 'msb')) {
428. item.code == "msbmbank_970426") {
429. this.themeConfig.deeplink_status == 'Off' ? false : true
430. this.filteredDataDeeplink = this.filteredData.filter(item => item.deep_link == true);
431. if (type == 'both'
432. } else if (type == 'both'
433. dialogType == 'deeplink'"
434. dialogType == 'qr'"
435. dialogType == 'both'"
436. if (this.locale == 'vi') {
437. if (params['locale'] == 'en') {
438. if (payment?.state == 'failed'
439. payment?.state == 'canceled'
440. payment?.state == 'expired') {
441. return ((a.id == id
442. a.code == id) && a.type.includes(type));
443. if (isIphone == true) {
444. } else if (isAndroid == true) {
445. return countPayment == maxPayment
446. if (this.getLatestPayment().state == 'canceled')
447. state == 'authorization_required'
448. } else if (latestPayment?.state == "failed") {
449. amigo_type == 'SP'"
450. amigo_type == 'PL'"
451. if (this.res.currencies[0] == 'USD') {
452. this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
453. if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
454. else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';
455. if (e.name == bankSwift) { // TODO: get by swift
456. if (e.code == appCode) { // TODO: get by swift
457. if (e.id == appId) { // TODO: get by swift
458. return this.apps.find(e => e.code == appCode);
459. if (+e.id == bankId) {
460. if (e.swiftCode == bankSwift) {
461. if (this.checkCount == 1) {
462. if (results == null) {
463. if (c.length == 3) {
464. d = d == undefined ? '
465. t = t == undefined ? '.' : t
466. expMonth.length == 2
467. expYear.length == 2
468. return results == null ? null : results[1]
469. if (((_val.length == 4
470. _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
471. _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
472. iss_date.length == 4
473. iss_date.search('/') == -1)
474. iss_date.length == 5))
475. if (_dataCache == null) {
476. if ( (0 <= r && r <= 6 && (c == 0
477. c == 6) )
478. || (0 <= c && c <= 6 && (r == 0
479. r == 6) )
480. if (i == 0
481. _modules[r][6] = (r % 2 == 0);
482. _modules[6][c] = (c % 2 == 0);
483. if (r == -2
484. r == 2
485. c == -2
486. c == 2
487. || (r == 0
488. c == 0) ) {
489. ( (bits >> i) & 1) == 1);
490. if (col == 6) col -= 1;
491. if (_modules[row][col - c] == null) {
492. dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
493. if (bitIndex == -1) {
494. margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
495. if (typeof arguments[0] == 'object') {
496. margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
497. if (b == -1) throw 'eof';
498. if (b0 == -1) break;
499. if (typeof b == 'number') {
500. if ( (b & 0xff) == b) {
501. return function(i, j) { return (i + j) % 2 == 0
502. return function(i, j) { return i % 2 == 0
503. return function(i, j) { return j % 3 == 0
504. return function(i, j) { return (i + j) % 3 == 0
505. return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
506. return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
507. return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
508. return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
509. if (r == 0
510. c == 0) {
511. if (dark == qrcode.isDark(row + r, col + c) ) {
512. if (count == 0
513. count == 4) {
514. if (typeof num.length == 'undefined') {
515. num[offset] == 0) {
516. if (typeof rsBlock == 'undefined') {
517. return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
518. _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
519. if (data.length - i == 1) {
520. } else if (data.length - i == 2) {
521. } else if (n == 62) {
522. } else if (n == 63) {
523. if (_buflen == 0) {
524. if (c == '=') {
525. } else if (c == 0x2b) {
526. } else if (c == 0x2f) {
527. if (table.size() == (1 << bitLength) ) {

!== (25 điều kiện duy nhất):
------------------------------------------------------------
1. this.lastValue !== $event.target.value)) {
2. if (YY % 400 === 0 || (YY % 100 !== 0
3. if (YYYY % 400 === 0 || (YYYY % 100 !== 0
4. event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
5. key !== '3') {
6. this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
7. codeResponse.toString() !== '0') {
8. cardNo.length !== 0) {
9. if (this.cardTypeBank !== 'bank_card_number') {
10. if (this.cardTypeBank !== 'bank_account_number') {
11. if (this.cardTypeBank !== 'bank_username') {
12. if (this.cardTypeBank !== 'bank_customer_code') {
13. this.lb_card_account !== this._translate.instant('ocb_account')) {
14. this.lb_card_account !== this._translate.instant('ocb_phone')) {
15. this.lb_card_account !== this._translate.instant('ocb_card')) {
16. this._b !== 18) || (this.cardTypeOcean === 'ATM'
17. if (this.cardTypeBank !== 'internet_banking') {
18. this._b !== 18) || (this._b == 18)) {
19. this.bankList = this.bankList.filter(item => item.b.id !== '67');
20. d_vietqr !== 1"
21. if (_val !== 3) {
22. this._util.getElementParams(this.currentUrl, 't_id') !== '') {
23. queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
24. if (queryString !== '') {
25. if (target !== 0

!= (184 điều kiện duy nhất):
------------------------------------------------------------
1. if (params['locale'] != null
2. } else if (params['locale'] != null
3. if (userLang != null
4. if(value!=null){
5. errorCode != '253'
6. errorCode != 'overtime'"
7. } else if (this.res.payments[this.res.payments.length - 1].instrument.issuer.brand != null
8. this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id != null
9. if (_re != null
10. _re.links != null
11. _re.links.merchant_return != null
12. _re.links.merchant_return.href != null) {
13. } else if (_re.body != null
14. _re.body.links != null
15. _re.body.links.merchant_return != null
16. _re.body.links.merchant_return.href != null) {
17. if (this.url_new_invoice != null) {
18. if (this._idInvoice != null
19. this._idInvoice != 0) {
20. if (this._paymentService.getInvoiceDetail() != null) {
21. dataPassed.body != null) {
22. dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
23. if (this._translate.currentLang != language) {
24. if (this.htmlDesc != null
25. if (ua.indexOf('safari') != -1
26. if (_val.value != '') {
27. if (this.valueDate.length != 3) {
28. if (this.cardName != null
29. if (this._res_post.links != null
30. this._res_post.links.merchant_return != null
31. this._res_post.links.merchant_return.href != null) {
32. if (this._res_post.authorization != null
33. this._res_post.authorization.links != null
34. this._res_post.authorization.links.approval != null) {
35. this._res_post.links.cancel != null) {
36. this._b != 27
37. this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
38. this._b != 18)
39. if (this._b != 18
40. this._b != 19) {
41. this._b != 20
42. this._b != 33
43. this._b != 39
44. this._b != 43
45. this._b != 45
46. this._b != 64
47. this._b != 67
48. this._b != 68
49. this._b != 72
50. this._b != 73
51. this._b != 36)
52. if (this._inExpMonth.length != 3) {
53. if (this._inExpYear.length != 3) {
54. this.auth_method != null) {
55. if (this._res_post.return_url != null) {
56. let userName = _formCard.name != null ? _formCard.name : ''
57. this._res_post.authorization.links.approval != null
58. this._res_post.authorization.links.approval.href != null) {
59. userName = paramUserName != null ? paramUserName : ''
60. this._b != 12
61. this._b != 3)) && this._util.checkMod10(cardNo) && this.checkBin(cardNo)
62. if (this._inExpDate.length != 3) {
63. if (this._b != 9
64. this._b != 11
65. this._b != 16
66. this._b != 17
67. this._b != 25
68. this._b != 44
69. this._b != 54
70. this._b != 57
71. this._b != 59
72. this._b != 61
73. this._b != 63
74. this._b != 69) {
75. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73'].indexOf(this._b.toString()) != -1) {// duongpxt 19255: Them vietbank napas id 72
76. this._b != 3))
77. if (this._b != 68
78. this._b != 2
79. this._b != 72)
80. _b != 22 && _b != 24 && _b != 8 && _b != 18
81. if (this._res.links != null
82. this._res.links.merchant_return != null
83. this._res.links.merchant_return.href != null) {
84. if (!(_formCard.otp != null
85. if (!(_formCard.password != null
86. if (this._inCardName != null
87. if (_formCard.exp_date != null
88. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75'].indexOf(this._b.toString()) != -1) {// duongpxt 19255: Them vietbank napas id 72
89. _b != 12 && _b != 3
90. if (params['locale'] != null) {
91. if ('otp' != this._paymentService.getCurrentPage()) {
92. this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
93. if (!(strInstrument != null
94. if (strInstrument.substring(0, 1) != '^'
95. strInstrument.substr(strInstrument.length - 1) != '$') {
96. if (bankid != null) {
97. this.filteredDataOtherWithoutZalo = this.filteredDataOther.filter(item => item.code != 'zalopay');
98. if (appcode != null) {
99. _paymentService.getCurrentPage() != 'otp'"
100. cardInfoEnable == 'card_name' && _b != 19
101. cardInfoEnable == 'card_number' && _b != 19
102. cardInfoEnable == 'card_name' && _b != 19 && _b != 16
103. type != 2 && ((onePaymentMethod && !token ) || token) && d_vietqr !== 1
104. _b != 1"
105. this._paymentService.getState() != 'error') {
106. _re.body != null) {
107. this._res_polling.links != null
108. this._res_polling.links.merchant_return != null //
109. } else if (this._res_polling.merchant != null
110. this._res_polling.merchant_invoice_reference != null
111. } else if (this._res_polling.payments != null
112. this._res_polling.payments[this._res_polling.payments.length - 1].instrument.brand != 'amigo') {
113. this._res_polling.links.merchant_return != null//
114. this._res_polling.payments != null
115. if (this._res_polling.payments != null
116. t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
117. this.type.toString().length != 0) {
118. const tokenListsFilter = tokenLists.filter(item => item.id != tokenData);
119. this._res.links != null
120. if (count != 1) {
121. if (this._res.merchant != null
122. this._res.merchant_invoice_reference != null) {
123. if (this._res.merchant.address_details != null) {
124. this._res.links != null//
125. } else if (this._res.payments != null
126. this._res.payments[this._res.payments.length - 1].instrument != null
127. this._res.payments[this._res.payments.length - 1].instrument.issuer != null
128. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
129. else if (this._res.payments != null
130. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
131. this._res.payments[this._res.payments.length - 1].links != null
132. this._res.payments[this._res.payments.length - 1].links.cancel != null
133. this._res.payments[this._res.payments.length - 1].links.cancel.href != null
134. this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
135. this._res.payments[this._res.payments.length - 1].authorization != null
136. this._res.payments[this._res.payments.length - 1].authorization.links != null) {
137. this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
138. this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
139. if (this._res.payments[this._res.payments.length - 1].authorization != null
140. this._res.payments[this._res.payments.length - 1].authorization.links != null
141. auth = paramUserName != null ? paramUserName : ''
142. this._res.links.merchant_return != null //
143. } else if (this._res.merchant != null
144. this._res.merchant_invoice_reference != null
145. this._res.payments[this._res.payments.length - 1].instrument.brand != 'amigo') {
146. } else if (this._res.payments != null) {
147. } else if (this._res_post.links != null
148. _re.body?.state != 'canceled')
149. if (res.body != null
150. res.body.links != null
151. res.body.links.merchant_return != null
152. res.body.links.merchant_return.href != null) {
153. if (_re.status != '200'
154. _re.status != '201') {
155. if (payment.state != 'authorization_required') {
156. if (this.translate.currentLang != language) {
157. if (res.body?.state != 'created') {
158. if (idInvoice != null
159. idInvoice != 0)
160. idInvoice != 0) {
161. let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
162. if (this._merchantid != null
163. this._tranref != null
164. this._state != null
165. urlCancel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
166. if (paymentId != null) {
167. if (res?.status != 200
168. res?.status != 201) return;
169. if (latestPayment?.instrument?.type != "vietqr") {
170. if (tem != null) {
171. if ((tem = ua.match(/version\/(\d+)/i)) != null) {
172. if (v.length != 3) {
173. if (_modules[r][6] != null) {
174. if (_modules[6][c] != null) {
175. if (_modules[row][col] != null) {
176. while (buffer.getLengthInBits() % 8 != 0) {
177. if (count != numChars) {
178. throw count + ' != ' + numChars
179. while (data != 0) {
180. if (test.length != 2
181. ( (test[0] << 8) | test[1]) != code) {
182. if (_length % 3 != 0) {
183. if ( (data >>> length) != 0) {
184. return typeof _map[key] != 'undefined'

