====================================================================================================
TRÍCH XUẤT ĐIỀU KIỆN SO SÁNH TỪ CÁC FILE TYPESCRIPT/JAVASCRIPT/HTML
====================================================================================================
Thư mục/File nguồn: /root/projects/onepay/paygate-invoice/src
Thời gian: 18:12:29 18/8/2025
Tổng số file xử lý: 51
Tổng số file bị bỏ qua: 0
Tổng số điều kiện tìm thấy: 328

THỐNG KÊ TỔNG QUAN THEO LOẠI TOÁN TỬ:
--------------------------------------------------
Strict equality (===): 45 lần
Loose equality (==): 158 lần
Strict inequality (!==): 20 lần
Loose inequality (!=): 105 lần
--------------------------------------------------

DANH SÁCH FILE ĐÃ XỬ LÝ:
--------------------------------------------------
1. 4en.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/4en.html
2. 4vn.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/4vn.html
3. app-routing.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/app-routing.module.ts
4. app.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/app.component.html
5. app.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/app.component.spec.ts
6. app.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/app.component.ts
7. app.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/app.module.ts
8. counter.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/counter.directive.spec.ts
9. counter.directive.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/counter.directive.ts
10. format-carno-input.derective.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/directives/format-carno-input.derective.ts
11. uppercase-input.directive.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/directives/uppercase-input.directive.ts
12. error.component.html (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/error/error.component.html
13. error.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/error/error.component.spec.ts
14. error.component.ts (30 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/error/error.component.ts
15. format-date.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/format-date.directive.spec.ts
16. format-date.directive.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/format-date.directive.ts
17. international.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/international/international.component.html
18. international.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/international/international.component.spec.ts
19. international.component.ts (13 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/international/international.component.ts
20. main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/main/main.component.html
21. main.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/main/main.component.spec.ts
22. main.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/main/main.component.ts
23. overlay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/overlay/overlay.component.html
24. overlay.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/overlay/overlay.component.spec.ts
25. overlay.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/overlay/overlay.component.ts
26. policy-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/select-bank/dialog/policy-dialog/policy-dialog.component.html
27. policy-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/select-bank/dialog/policy-dialog/policy-dialog.component.ts
28. select-bank.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/select-bank/select-bank.component.html
29. select-bank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/select-bank/select-bank.component.spec.ts
30. select-bank.component.ts (139 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/select-bank/select-bank.component.ts
31. payment.service.ts (12 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/services/payment.service.ts
32. index.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/translate/index.ts
33. lang-en.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/translate/lang-en.ts
34. lang-vi.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/translate/lang-vi.ts
35. translate.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/translate/translate.pipe.ts
36. translate.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/translate/translate.service.ts
37. translations.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/translate/translations.ts
38. banks-fee.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/util/banks-fee.ts
39. banks-info.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/util/banks-info.ts
40. installment-utils.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/util/installment-utils.ts
41. iso-ca-states.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/util/iso-ca-states.ts
42. iso-us-states.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/util/iso-us-states.ts
43. util.ts (20 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/util/util.ts
44. qrcode.js (67 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/assets/script/qrcode.js
45. environment.prod.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/environments/environment.prod.ts
46. environment.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/environments/environment.ts
47. index.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/index.html
48. karma.conf.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/karma.conf.js
49. main.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/main.ts
50. polyfills.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/polyfills.ts
51. test.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-invoice/src/test.ts

====================================================================================================
CHI TIẾT TỪNG FILE:
====================================================================================================

📁 FILE 1: 4en.html
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/4en.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 2: 4vn.html
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/4vn.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 3: app-routing.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/app-routing.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 4: app.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/app.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 5: app.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/app.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 6: app.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/app.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 76] return lang === this._translate.currentLang

== (2 điều kiện):
  1. [Dòng 57] 'vi'==params['locale']){
  2. [Dòng 59] 'en'==params['locale']){

!= (3 điều kiện):
  1. [Dòng 57] if(params['locale']!=null
  2. [Dòng 59] }else if(params['locale']!=null
  3. [Dòng 66] if(userLang!=null

================================================================================

📁 FILE 7: app.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/app.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 8: counter.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/counter.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 9: counter.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/counter.directive.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 10: format-carno-input.derective.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/directives/format-carno-input.derective.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 44] this.lastValue !== $event.target.value)) {

!= (1 điều kiện):
  1. [Dòng 23] if(value!=null){

================================================================================

📁 FILE 11: uppercase-input.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/directives/uppercase-input.directive.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 0 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 23] this.lastValue !== $event.target.value)) {

================================================================================

📁 FILE 12: error.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/error/error.component.html
📊 Thống kê: 9 điều kiện duy nhất
   - === : 0 lần
   - == : 7 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (7 điều kiện):
  1. [Dòng 3] errorCode == '11'"
  2. [Dòng 24] errorCode && (errorCode == '253' || errorCode == 'overtime')
  3. [Dòng 24] errorCode == 'overtime')">
  4. [Dòng 95] errorCode == 'overtime'
  5. [Dòng 95] errorCode == '253'"
  6. [Dòng 97] <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
  7. [Dòng 97] !(errorCode == 'overtime' || errorCode == '253') && isBack

!= (2 điều kiện):
  1. [Dòng 16] errorCode != '253'
  2. [Dòng 16] errorCode != 'overtime'"

================================================================================

📁 FILE 13: error.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/error/error.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 14: error.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/error/error.component.ts
📊 Thống kê: 30 điều kiện duy nhất
   - === : 7 lần
   - == : 12 lần
   - !== : 0 lần
   - != : 11 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 160] params.timeout === 'true') {
  2. [Dòng 169] params.kbank === 'true') {
  3. [Dòng 184] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  4. [Dòng 184] _re.body.state === 'unpaid');
  5. [Dòng 269] if (this.errorCode === 'overtime'
  6. [Dòng 269] this.errorCode === '253') {
  7. [Dòng 380] if (this.timeLeft === 0) {

== (12 điều kiện):
  1. [Dòng 185] if (_re.body.state == 'closed')
  2. [Dòng 194] if (this.res.currencies[0] == 'USD') numfix = 2;
  3. [Dòng 246] params.response_code == 'overtime') {
  4. [Dòng 292] if (_re.status == '200'
  5. [Dòng 292] _re.status == '201') {
  6. [Dòng 305] if (_re2.status == '200'
  7. [Dòng 305] _re2.status == '201') {
  8. [Dòng 318] if (this.errorCode == 'overtime'
  9. [Dòng 318] this.errorCode == '253') {
  10. [Dòng 321] } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
  11. [Dòng 326] this.res.state == 'canceled') {
  12. [Dòng 378] if (this.isTimePause == false) {

!= (11 điều kiện):
  1. [Dòng 138] if (message != ''
  2. [Dòng 138] message != null
  3. [Dòng 138] message != undefined) {
  4. [Dòng 444] if (_re != null
  5. [Dòng 444] _re.links != null
  6. [Dòng 444] _re.links.merchant_return != null
  7. [Dòng 445] _re.links.merchant_return.href != null) {
  8. [Dòng 447] } else if (_re.body != null
  9. [Dòng 447] _re.body.links != null
  10. [Dòng 447] _re.body.links.merchant_return != null
  11. [Dòng 448] _re.body.links.merchant_return.href != null) {

================================================================================

📁 FILE 15: format-date.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/format-date.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 16: format-date.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/format-date.directive.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 122] if (YY % 400 === 0
  2. [Dòng 122] YY % 4 === 0)) {
  3. [Dòng 137] if (YYYY % 400 === 0
  4. [Dòng 137] YYYY % 4 === 0)) {

!== (2 điều kiện):
  1. [Dòng 122] if (YY % 400 === 0 || (YY % 100 !== 0
  2. [Dòng 137] if (YYYY % 400 === 0 || (YYYY % 100 !== 0

================================================================================

📁 FILE 17: international.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/international/international.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 18: international.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/international/international.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 19: international.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/international/international.component.ts
📊 Thống kê: 13 điều kiện duy nhất
   - === : 6 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (6 điều kiện):
  1. [Dòng 76] if (_re.status === '201'
  2. [Dòng 76] _re.status === '201') {
  3. [Dòng 79] if (_re.status === 'approved'
  4. [Dòng 79] _re.status === 'failed') {
  5. [Dòng 82] } else if (_re.status === 'authorization_required') {
  6. [Dòng 126] if (valid.indexOf(temp) === -1) bNum = false;

== (6 điều kiện):
  1. [Dòng 97] if ((_val.value.length == 16
  2. [Dòng 97] _val.value.length == 19) && this.Mod10(_val.value) == true) {
  3. [Dòng 97] this.Mod10(_val.value) == true) {
  4. [Dòng 104] if (_val.value.length == 4) {
  5. [Dòng 131] if (len == 0) { /* nothing, field is blank */
  6. [Dòng 170] if ((iTotal % 10) == 0) {

!= (1 điều kiện):
  1. [Dòng 43] if (this._paymentService.getInvoiceDetail() != null) {

================================================================================

📁 FILE 20: main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/main/main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 21: main.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/main/main.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 22: main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/main/main.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 4 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 57] if ((_re.status == '200'
  2. [Dòng 57] _re.status == '201') && _re.body != null) {

!= (4 điều kiện):
  1. [Dòng 47] if (this._idInvoice != null
  2. [Dòng 47] this._idInvoice != 0) {
  3. [Dòng 48] if (this._paymentService.getInvoiceDetail() != null) {
  4. [Dòng 57] _re.body != null) {

================================================================================

📁 FILE 23: overlay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/overlay/overlay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 24: overlay.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/overlay/overlay.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 25: overlay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/overlay/overlay.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 26: policy-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/select-bank/dialog/policy-dialog/policy-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 27: policy-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/select-bank/dialog/policy-dialog/policy-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 28: select-bank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/select-bank/select-bank.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 1 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 896] d_paypal === true"

!= (3 điều kiện):
  1. [Dòng 202] _showCardName != true"
  2. [Dòng 238] _showEmail != true"
  3. [Dòng 259] _showPhone != true"

================================================================================

📁 FILE 29: select-bank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/select-bank/select-bank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 30: select-bank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/select-bank/select-bank.component.ts
📊 Thống kê: 139 điều kiện duy nhất
   - === : 12 lần
   - == : 59 lần
   - !== : 14 lần
   - != : 54 lần
--------------------------------------------------------------------------------

=== (12 điều kiện):
  1. [Dòng 182] if (target.tagName === 'A'
  2. [Dòng 469] this._res.merchant.avs === true) {
  3. [Dòng 681] this._res_post.reason.name === 'FRAUD') {
  4. [Dòng 937] if (event.keyCode === 8
  5. [Dòng 937] event.key === "Backspace"
  6. [Dòng 1018] if ((v.substr(-1) === ' '
  7. [Dòng 1146] this._i_country_code === 'US') {
  8. [Dòng 1173] if (valid.indexOf(temp) === -1) bNum = false;
  9. [Dòng 1547] const insertIndex = this._i_country_code === 'US' ? 5 : 3
  10. [Dòng 1549] if (temp[i] === '-'
  11. [Dòng 1549] temp[i] === ' ') {
  12. [Dòng 1556] insertIndex === 3 ? ' ' : itemRemoved)

== (59 điều kiện):
  1. [Dòng 209] if (!isNaN(_re.status) && (_re.status == '200'
  2. [Dòng 209] _re.status == '201') && _re.body != null) {
  3. [Dòng 212] if ('not_paid' == this._res.state) {
  4. [Dòng 219] if (('canceled' == this._res.state
  5. [Dòng 219] 'paid' == this._res.state)
  6. [Dòng 223] this._res.payments[this._res.payments.length - 1].state == 'pending') {
  7. [Dòng 225] this._paymentService.getCurrentPage() == 'enter_card') {
  8. [Dòng 232] this._res.payments[this._res.payments.length - 1].state == 'failed'
  9. [Dòng 238] if ((this.curr_payment == this._res.payments.length) && (notRedirectPostCard
  10. [Dòng 257] } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  11. [Dòng 257] this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
  12. [Dòng 271] } if('expired' == this._res.state){
  13. [Dòng 324] if (isIE[0] == 'MSIE'
  14. [Dòng 324] +isIE[1] == 10) {
  15. [Dòng 366] if (this.checkInvoiceState() == 1) {
  16. [Dòng 375] if (_re.status == '200'
  17. [Dòng 375] _re.status == '201') {
  18. [Dòng 524] } else if (this._res.payments && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  19. [Dòng 542] if (this._res.currencies[0] == 'USD') numfix = 2;
  20. [Dòng 672] if (this._res_post.state == 'approved'
  21. [Dòng 672] this._res_post.state == 'failed') {
  22. [Dòng 681] if (this._res_post.state == 'failed'
  23. [Dòng 691] } else if(this._res_post.state == 'failed'
  24. [Dòng 704] } else if (this._res_post.state == 'failed') {
  25. [Dòng 736] } else if (this._res_post.state == 'authorization_required') {
  26. [Dòng 737] if ("POST_REDIRECT" == this._res_post.authorization.links.approval.method) {
  27. [Dòng 751] } else if ("REDIRECT" == this._res_post.authorization.links.approval.method) {
  28. [Dòng 817] v.length == 15) || (v.length == 16
  29. [Dòng 817] v.length == 19))
  30. [Dòng 818] this._util.checkMod10(v) == true) {
  31. [Dòng 865] cardNo.length == 15)
  32. [Dòng 867] cardNo.length == 16)
  33. [Dòng 868] cardNo.startsWith('81'))&& (cardNo.length == 16
  34. [Dòng 868] cardNo.length == 19))
  35. [Dòng 937] event.inputType == 'deleteContentBackward') {
  36. [Dòng 938] if (event.target.name == 'exp_date'
  37. [Dòng 946] event.inputType == 'insertCompositionText') {
  38. [Dòng 961] if (((this.valueDate.length == 4
  39. [Dòng 961] this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  40. [Dòng 961] this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  41. [Dòng 975] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  42. [Dòng 975] this.valueDate.length == 5)
  43. [Dòng 1018] v.length == 5) {
  44. [Dòng 1026] v.length == 4
  45. [Dòng 1030] v.length == 3)
  46. [Dòng 1046] _val.value.length == 4
  47. [Dòng 1050] _val.value.length == 3)
  48. [Dòng 1106] this.requireAvs = this.AVS_COUNTRIES.find(e => e.code == this._i_country_code)? true : false;
  49. [Dòng 1180] if (len == 0) { /* nothing, field is blank */
  50. [Dòng 1215] if ((iTotal % 10) == 0) {
  51. [Dòng 1284] cardNo.startsWith('81')) && (cardNo.length == 16
  52. [Dòng 1286] this._util.checkMod10(cardNo) == true
  53. [Dòng 1309] expdate.length == 5) {
  54. [Dòng 1345] _formCard.csc.length == 4) ||
  55. [Dòng 1349] _formCard.csc.length == 3)
  56. [Dòng 1361] if (this._showCardName == true) {
  57. [Dòng 1711] country = this.AVS_COUNTRIES.find(e => e.code == res.bin_country);
  58. [Dòng 1753] countryCode == 'US' ? US_STATES
  59. [Dòng 1754] : countryCode == 'CA' ? CA_STATES

!== (14 điều kiện):
  1. [Dòng 656] if (this.tracking.hasOwnProperty(key) && ((key !== '3'
  2. [Dòng 656] !this._showCardName) || (key !== '4'
  3. [Dòng 691] codeResponse.toString() !== '0' ){ // xử lý lỗi qúa số lần thanh toán thì sang màn lỗi
  4. [Dòng 1018] event.inputType !== 'deleteContentBackward') || v.length == 5) {
  5. [Dòng 1149] this._i_country_code !== 'US') {
  6. [Dòng 1452] this.tracking['1'].charAt(this.tracking['1'].length - 1) !== 't')) {
  7. [Dòng 1460] this.tracking['1'].charAt(this.tracking['1'].length - 1) !== 'p')) {
  8. [Dòng 1469] this.tracking['2'].charAt(this.tracking['2'].length - 1) !== 't')) {
  9. [Dòng 1477] this.tracking['2'].charAt(this.tracking['2'].length - 1) !== 'p')) {
  10. [Dòng 1485] this.tracking['3'].charAt(this.tracking['3'].length - 1) !== 't')) {
  11. [Dòng 1493] this.tracking['3'].charAt(this.tracking['3'].length - 1) !== 'p')) {
  12. [Dòng 1501] this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 't')) {
  13. [Dòng 1509] this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 'p')) {
  14. [Dòng 1555] itemRemoved !== '') {

!= (54 điều kiện):
  1. [Dòng 204] if (this._idInvoice != null
  2. [Dòng 204] this._paymentService.getCurrentPage() != 'error') {
  3. [Dòng 209] _re.body != null) {
  4. [Dòng 257] } else if (this._res.payments != null
  5. [Dòng 357] if (params['locale'] != null) {
  6. [Dòng 364] if (this._paymentService.getInvoiceDetail() != null) {
  7. [Dòng 400] if (this._idInvoice != null) {
  8. [Dòng 420] this._res.links != null
  9. [Dòng 420] this._res.links.merchant_return != null
  10. [Dòng 458] if (this._res.merchant != null
  11. [Dòng 458] this._res.merchant_invoice_reference != null) {
  12. [Dòng 464] if (this._res.merchant.address_details != null) {
  13. [Dòng 469] this._res.merchant.avs != null
  14. [Dòng 473] if (this._res.billing != null
  15. [Dòng 473] this._res.billing.address != null) {
  16. [Dòng 474] if (this._res.billing.address.city != null
  17. [Dòng 474] this._res.billing.address.city != 'null') this._i_city = this._res.billing.address.city;
  18. [Dòng 475] if (this._res.billing.address.line1 != null
  19. [Dòng 475] this._res.billing.address.line1 != 'null') this._i_address = this._res.billing.address.line1;
  20. [Dòng 476] if (this._res.billing.address.postal_code != null
  21. [Dòng 476] this._res.billing.address.postal_code != 'null') this._i_postal_code = this._res.billing.address.postal_code;
  22. [Dòng 477] if (this._res.billing.address.state != null
  23. [Dòng 477] this._res.billing.address.state != 'null') this._i_state = this._res.billing.address.state;
  24. [Dòng 478] if (this._res.billing.address.country_code != null
  25. [Dòng 478] this._res.billing.address.country_code != 'null') {
  26. [Dòng 674] if (this._res_post.return_url != null) {
  27. [Dòng 676] } else if (this._res_post.links != null
  28. [Dòng 676] this._res_post.links.merchant_return != null
  29. [Dòng 676] this._res_post.links.merchant_return.href != null) {
  30. [Dòng 814] if (ua.indexOf('safari') != -1
  31. [Dòng 865] cardNo != null
  32. [Dòng 907] if (imglogo != null) {
  33. [Dòng 939] if (this.valueDate.length != 3) {
  34. [Dòng 1025] v != null
  35. [Dòng 1045] this.c_csc = (!(_val.value != null
  36. [Dòng 1277] if (_formCard.card_number != null) cardNo = _formCard.card_number.replace(/\s+/g
  37. [Dòng 1295] if (cardNo != null
  38. [Dòng 1309] if (expdate != null
  39. [Dòng 1344] _formCard.csc != null
  40. [Dòng 1362] if (!(_formCard.card_name != null
  41. [Dòng 1368] if (_formCard.email != ''
  42. [Dòng 1374] if (_formCard.phone != ''
  43. [Dòng 1382] if (!(_formCard.country != null
  44. [Dòng 1387] if (!(_formCard.address != null
  45. [Dòng 1392] if (!(_formCard.city != null
  46. [Dòng 1397] if (!(_formCard.province != null
  47. [Dòng 1404] if (!(postalCode != null
  48. [Dòng 1423] if (this.current != 'card') {
  49. [Dòng 1432] if (this.current != 'date') {
  50. [Dòng 1442] if (this.current != 'csc') {
  51. [Dòng 1607] if (this._res_post.return_url != null)
  52. [Dòng 1609] else if (this._res_post.links != null
  53. [Dòng 1609] this._res_post.links.merchant_return.href != null)
  54. [Dòng 1713] this.requireAvs = this.isAvsCountry = country != undefined

================================================================================

📁 FILE 31: payment.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/services/payment.service.ts
📊 Thống kê: 12 điều kiện duy nhất
   - === : 2 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 9 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 500] const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
  2. [Dòng 500] item.method === method) : null;

== (1 điều kiện):
  1. [Dòng 261] this._state == 'completed') return of([]);

!= (9 điều kiện):
  1. [Dòng 109] if (idInvoice != null
  2. [Dòng 109] idInvoice != 0)
  3. [Dòng 227] if (this._merchantid != null
  4. [Dòng 227] this._tranref != null
  5. [Dòng 227] this._state != null
  6. [Dòng 261] if (this._state != null
  7. [Dòng 270] } else if (idInvoice != null
  8. [Dòng 270] idInvoice != 0) {
  9. [Dòng 337] let urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');

================================================================================

📁 FILE 32: index.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/translate/index.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 33: lang-en.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/translate/lang-en.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 34: lang-vi.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/translate/lang-vi.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 35: translate.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/translate/translate.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 36: translate.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/translate/translate.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 33] if(this.currentLang=='en') curr_lang='LANG_EN_NAME';
  2. [Dòng 34] else if(this.currentLang=='vi') curr_lang='LANG_VI_NAME';

================================================================================

📁 FILE 37: translations.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/translate/translations.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 38: banks-fee.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/util/banks-fee.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 39: banks-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/util/banks-info.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (6 điều kiện):
  1. [Dòng 75] if(+e.id==bankId) b=e;
  2. [Dòng 83] if(+e.id==bankId) b=e.name;
  3. [Dòng 90] if(+e.id==bankId) b=e.swiftCode;
  4. [Dòng 97] if(+e.id==bankId) b=e.pattern;
  5. [Dòng 105] if(e.swiftCode==bankSwift) b=e.auth;
  6. [Dòng 113] if(e.swiftCode==bankSwift) b=e.id;

================================================================================

📁 FILE 40: installment-utils.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/util/installment-utils.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 41: iso-ca-states.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/util/iso-ca-states.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 42: iso-us-states.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/util/iso-us-states.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 43: util.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/app/util/util.ts
📊 Thống kê: 20 điều kiện duy nhất
   - === : 9 lần
   - == : 7 lần
   - !== : 2 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (9 điều kiện):
  1. [Dòng 61] if (v.length === 2
  2. [Dòng 61] this.flag.length === 3
  3. [Dòng 61] this.flag.charAt(this.flag.length - 1) === '/') {
  4. [Dòng 65] if (v.length === 1) {
  5. [Dòng 67] } else if (v.length === 2) {
  6. [Dòng 70] v.length === 2) {
  7. [Dòng 78] if (len === 2) {
  8. [Dòng 150] if (M[1] === 'Chrome') {
  9. [Dòng 525] if (param === key) {

== (7 điều kiện):
  1. [Dòng 13] if (temp.length == 0) {
  2. [Dòng 20] return (counter % 10 == 0);
  3. [Dòng 124] if (this.checkCount == 1) {
  4. [Dòng 136] if (results == null) {
  5. [Dòng 167] d = d == undefined ? '.' : d
  6. [Dòng 168] t = t == undefined ? '
  7. [Dòng 513] return results == null ? null : results[1]

!== (2 điều kiện):
  1. [Dòng 520] queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
  2. [Dòng 521] if (queryString !== '') {

!= (2 điều kiện):
  1. [Dòng 152] if (tem != null) {
  2. [Dòng 157] if ((tem = ua.match(/version\/(\d+)/i)) != null) {

================================================================================

📁 FILE 44: qrcode.js
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/assets/script/qrcode.js
📊 Thống kê: 67 điều kiện duy nhất
   - === : 2 lần
   - == : 53 lần
   - !== : 0 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 2255] if (typeof define === 'function'
  2. [Dòng 2257] } else if (typeof exports === 'object') {

== (53 điều kiện):
  1. [Dòng 68] if (_dataCache == null) {
  2. [Dòng 85] if ( (0 <= r && r <= 6 && (c == 0
  3. [Dòng 85] c == 6) )
  4. [Dòng 86] || (0 <= c && c <= 6 && (r == 0
  5. [Dòng 86] r == 6) )
  6. [Dòng 107] if (i == 0
  7. [Dòng 122] _modules[r][6] = (r % 2 == 0);
  8. [Dòng 129] _modules[6][c] = (c % 2 == 0);
  9. [Dòng 152] if (r == -2
  10. [Dòng 152] r == 2
  11. [Dòng 152] c == -2
  12. [Dòng 152] c == 2
  13. [Dòng 153] || (r == 0
  14. [Dòng 153] c == 0) ) {
  15. [Dòng 169] ( (bits >> i) & 1) == 1);
  16. [Dòng 226] if (col == 6) col -= 1;
  17. [Dòng 232] if (_modules[row][col - c] == null) {
  18. [Dòng 237] dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
  19. [Dòng 249] if (bitIndex == -1) {
  20. [Dòng 458] margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
  21. [Dòng 498] if (typeof arguments[0] == 'object') {
  22. [Dòng 587] margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
  23. [Dòng 733] if (b == -1) throw 'eof';
  24. [Dòng 741] if (b0 == -1) break;
  25. [Dòng 767] if (typeof b == 'number') {
  26. [Dòng 768] if ( (b & 0xff) == b) {
  27. [Dòng 910] return function(i, j) { return (i + j) % 2 == 0
  28. [Dòng 912] return function(i, j) { return i % 2 == 0
  29. [Dòng 914] return function(i, j) { return j % 3 == 0
  30. [Dòng 916] return function(i, j) { return (i + j) % 3 == 0
  31. [Dòng 918] return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
  32. [Dòng 920] return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
  33. [Dòng 922] return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
  34. [Dòng 924] return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
  35. [Dòng 1011] if (r == 0
  36. [Dòng 1011] c == 0) {
  37. [Dòng 1015] if (dark == qrcode.isDark(row + r, col + c) ) {
  38. [Dòng 1036] if (count == 0
  39. [Dòng 1036] count == 4) {
  40. [Dòng 1149] if (typeof num.length == 'undefined') {
  41. [Dòng 1155] num[offset] == 0) {
  42. [Dòng 1495] if (typeof rsBlock == 'undefined') {
  43. [Dòng 1538] return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
  44. [Dòng 1543] _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
  45. [Dòng 1599] if (data.length - i == 1) {
  46. [Dòng 1601] } else if (data.length - i == 2) {
  47. [Dòng 1866] } else if (n == 62) {
  48. [Dòng 1868] } else if (n == 63) {
  49. [Dòng 1928] if (_buflen == 0) {
  50. [Dòng 1937] if (c == '=') {
  51. [Dòng 1961] } else if (c == 0x2b) {
  52. [Dòng 1963] } else if (c == 0x2f) {
  53. [Dòng 2133] if (table.size() == (1 << bitLength) ) {

!= (12 điều kiện):
  1. [Dòng 119] if (_modules[r][6] != null) {
  2. [Dòng 126] if (_modules[6][c] != null) {
  3. [Dòng 144] if (_modules[row][col] != null) {
  4. [Dòng 365] while (buffer.getLengthInBits() % 8 != 0) {
  5. [Dòng 750] if (count != numChars) {
  6. [Dòng 751] throw count + ' != ' + numChars
  7. [Dòng 878] while (data != 0) {
  8. [Dòng 1733] if (test.length != 2
  9. [Dòng 1733] ( (test[0] << 8) | test[1]) != code) {
  10. [Dòng 1894] if (_length % 3 != 0) {
  11. [Dòng 2067] if ( (data >>> length) != 0) {
  12. [Dòng 2178] return typeof _map[key] != 'undefined'

================================================================================

📁 FILE 45: environment.prod.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/environments/environment.prod.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 46: environment.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/environments/environment.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 47: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/index.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 1 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 39] if(M[1]=== 'Chrome'){

== (1 điều kiện):
  1. [Dòng 50] if(checkIE[0]=='MSIE'

!= (3 điều kiện):
  1. [Dòng 28] document.documentMode!=null
  2. [Dòng 41] if(tem!= null) return tem.slice(1).join(' ').replace('OPR'
  3. [Dòng 44] if((tem= ua.match(/version\/(\d+)/i))!= null) M.splice(1

================================================================================

📁 FILE 48: karma.conf.js
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/karma.conf.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 49: main.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/main.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 50: polyfills.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/polyfills.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 51: test.ts
📍 Đường dẫn: /root/projects/onepay/paygate-invoice/src/test.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📋 TÓM TẮT TẤT CẢ ĐIỀU KIỆN DUY NHẤT:
====================================================================================================

=== (44 điều kiện duy nhất):
------------------------------------------------------------
1. return lang === this._translate.currentLang
2. params.timeout === 'true') {
3. params.kbank === 'true') {
4. this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
5. _re.body.state === 'unpaid');
6. if (this.errorCode === 'overtime'
7. this.errorCode === '253') {
8. if (this.timeLeft === 0) {
9. if (YY % 400 === 0
10. YY % 4 === 0)) {
11. if (YYYY % 400 === 0
12. YYYY % 4 === 0)) {
13. if (_re.status === '201'
14. _re.status === '201') {
15. if (_re.status === 'approved'
16. _re.status === 'failed') {
17. } else if (_re.status === 'authorization_required') {
18. if (valid.indexOf(temp) === -1) bNum = false;
19. d_paypal === true"
20. if (target.tagName === 'A'
21. this._res.merchant.avs === true) {
22. this._res_post.reason.name === 'FRAUD') {
23. if (event.keyCode === 8
24. event.key === "Backspace"
25. if ((v.substr(-1) === ' '
26. this._i_country_code === 'US') {
27. const insertIndex = this._i_country_code === 'US' ? 5 : 3
28. if (temp[i] === '-'
29. temp[i] === ' ') {
30. insertIndex === 3 ? ' ' : itemRemoved)
31. const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
32. item.method === method) : null;
33. if (v.length === 2
34. this.flag.length === 3
35. this.flag.charAt(this.flag.length - 1) === '/') {
36. if (v.length === 1) {
37. } else if (v.length === 2) {
38. v.length === 2) {
39. if (len === 2) {
40. if (M[1] === 'Chrome') {
41. if (param === key) {
42. if (typeof define === 'function'
43. } else if (typeof exports === 'object') {
44. if(M[1]=== 'Chrome'){

== (153 điều kiện duy nhất):
------------------------------------------------------------
1. 'vi'==params['locale']){
2. 'en'==params['locale']){
3. errorCode == '11'"
4. errorCode && (errorCode == '253' || errorCode == 'overtime')
5. errorCode == 'overtime')">
6. errorCode == 'overtime'
7. errorCode == '253'"
8. <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
9. !(errorCode == 'overtime' || errorCode == '253') && isBack
10. if (_re.body.state == 'closed')
11. if (this.res.currencies[0] == 'USD') numfix = 2;
12. params.response_code == 'overtime') {
13. if (_re.status == '200'
14. _re.status == '201') {
15. if (_re2.status == '200'
16. _re2.status == '201') {
17. if (this.errorCode == 'overtime'
18. this.errorCode == '253') {
19. } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
20. this.res.state == 'canceled') {
21. if (this.isTimePause == false) {
22. if ((_val.value.length == 16
23. _val.value.length == 19) && this.Mod10(_val.value) == true) {
24. this.Mod10(_val.value) == true) {
25. if (_val.value.length == 4) {
26. if (len == 0) { /* nothing, field is blank */
27. if ((iTotal % 10) == 0) {
28. if ((_re.status == '200'
29. _re.status == '201') && _re.body != null) {
30. if (!isNaN(_re.status) && (_re.status == '200'
31. if ('not_paid' == this._res.state) {
32. if (('canceled' == this._res.state
33. 'paid' == this._res.state)
34. this._res.payments[this._res.payments.length - 1].state == 'pending') {
35. this._paymentService.getCurrentPage() == 'enter_card') {
36. this._res.payments[this._res.payments.length - 1].state == 'failed'
37. if ((this.curr_payment == this._res.payments.length) && (notRedirectPostCard
38. } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
39. this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
40. } if('expired' == this._res.state){
41. if (isIE[0] == 'MSIE'
42. +isIE[1] == 10) {
43. if (this.checkInvoiceState() == 1) {
44. } else if (this._res.payments && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
45. if (this._res.currencies[0] == 'USD') numfix = 2;
46. if (this._res_post.state == 'approved'
47. this._res_post.state == 'failed') {
48. if (this._res_post.state == 'failed'
49. } else if(this._res_post.state == 'failed'
50. } else if (this._res_post.state == 'failed') {
51. } else if (this._res_post.state == 'authorization_required') {
52. if ("POST_REDIRECT" == this._res_post.authorization.links.approval.method) {
53. } else if ("REDIRECT" == this._res_post.authorization.links.approval.method) {
54. v.length == 15) || (v.length == 16
55. v.length == 19))
56. this._util.checkMod10(v) == true) {
57. cardNo.length == 15)
58. cardNo.length == 16)
59. cardNo.startsWith('81'))&& (cardNo.length == 16
60. cardNo.length == 19))
61. event.inputType == 'deleteContentBackward') {
62. if (event.target.name == 'exp_date'
63. event.inputType == 'insertCompositionText') {
64. if (((this.valueDate.length == 4
65. this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
66. this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
67. this.valueDate.search('/') == -1) || this.valueDate.length == 5)
68. this.valueDate.length == 5)
69. v.length == 5) {
70. v.length == 4
71. v.length == 3)
72. _val.value.length == 4
73. _val.value.length == 3)
74. this.requireAvs = this.AVS_COUNTRIES.find(e => e.code == this._i_country_code)? true : false;
75. cardNo.startsWith('81')) && (cardNo.length == 16
76. this._util.checkMod10(cardNo) == true
77. expdate.length == 5) {
78. _formCard.csc.length == 4) ||
79. _formCard.csc.length == 3)
80. if (this._showCardName == true) {
81. country = this.AVS_COUNTRIES.find(e => e.code == res.bin_country);
82. countryCode == 'US' ? US_STATES
83. : countryCode == 'CA' ? CA_STATES
84. this._state == 'completed') return of([]);
85. if(this.currentLang=='en') curr_lang='LANG_EN_NAME';
86. else if(this.currentLang=='vi') curr_lang='LANG_VI_NAME';
87. if(+e.id==bankId) b=e;
88. if(+e.id==bankId) b=e.name;
89. if(+e.id==bankId) b=e.swiftCode;
90. if(+e.id==bankId) b=e.pattern;
91. if(e.swiftCode==bankSwift) b=e.auth;
92. if(e.swiftCode==bankSwift) b=e.id;
93. if (temp.length == 0) {
94. return (counter % 10 == 0);
95. if (this.checkCount == 1) {
96. if (results == null) {
97. d = d == undefined ? '.' : d
98. t = t == undefined ? '
99. return results == null ? null : results[1]
100. if (_dataCache == null) {
101. if ( (0 <= r && r <= 6 && (c == 0
102. c == 6) )
103. || (0 <= c && c <= 6 && (r == 0
104. r == 6) )
105. if (i == 0
106. _modules[r][6] = (r % 2 == 0);
107. _modules[6][c] = (c % 2 == 0);
108. if (r == -2
109. r == 2
110. c == -2
111. c == 2
112. || (r == 0
113. c == 0) ) {
114. ( (bits >> i) & 1) == 1);
115. if (col == 6) col -= 1;
116. if (_modules[row][col - c] == null) {
117. dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
118. if (bitIndex == -1) {
119. margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
120. if (typeof arguments[0] == 'object') {
121. margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
122. if (b == -1) throw 'eof';
123. if (b0 == -1) break;
124. if (typeof b == 'number') {
125. if ( (b & 0xff) == b) {
126. return function(i, j) { return (i + j) % 2 == 0
127. return function(i, j) { return i % 2 == 0
128. return function(i, j) { return j % 3 == 0
129. return function(i, j) { return (i + j) % 3 == 0
130. return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
131. return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
132. return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
133. return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
134. if (r == 0
135. c == 0) {
136. if (dark == qrcode.isDark(row + r, col + c) ) {
137. if (count == 0
138. count == 4) {
139. if (typeof num.length == 'undefined') {
140. num[offset] == 0) {
141. if (typeof rsBlock == 'undefined') {
142. return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
143. _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
144. if (data.length - i == 1) {
145. } else if (data.length - i == 2) {
146. } else if (n == 62) {
147. } else if (n == 63) {
148. if (_buflen == 0) {
149. if (c == '=') {
150. } else if (c == 0x2b) {
151. } else if (c == 0x2f) {
152. if (table.size() == (1 << bitLength) ) {
153. if(checkIE[0]=='MSIE'

!== (19 điều kiện duy nhất):
------------------------------------------------------------
1. this.lastValue !== $event.target.value)) {
2. if (YY % 400 === 0 || (YY % 100 !== 0
3. if (YYYY % 400 === 0 || (YYYY % 100 !== 0
4. if (this.tracking.hasOwnProperty(key) && ((key !== '3'
5. !this._showCardName) || (key !== '4'
6. codeResponse.toString() !== '0' ){ // xử lý lỗi qúa số lần thanh toán thì sang màn lỗi
7. event.inputType !== 'deleteContentBackward') || v.length == 5) {
8. this._i_country_code !== 'US') {
9. this.tracking['1'].charAt(this.tracking['1'].length - 1) !== 't')) {
10. this.tracking['1'].charAt(this.tracking['1'].length - 1) !== 'p')) {
11. this.tracking['2'].charAt(this.tracking['2'].length - 1) !== 't')) {
12. this.tracking['2'].charAt(this.tracking['2'].length - 1) !== 'p')) {
13. this.tracking['3'].charAt(this.tracking['3'].length - 1) !== 't')) {
14. this.tracking['3'].charAt(this.tracking['3'].length - 1) !== 'p')) {
15. this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 't')) {
16. this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 'p')) {
17. itemRemoved !== '') {
18. queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
19. if (queryString !== '') {

!= (101 điều kiện duy nhất):
------------------------------------------------------------
1. if(params['locale']!=null
2. }else if(params['locale']!=null
3. if(userLang!=null
4. if(value!=null){
5. errorCode != '253'
6. errorCode != 'overtime'"
7. if (message != ''
8. message != null
9. message != undefined) {
10. if (_re != null
11. _re.links != null
12. _re.links.merchant_return != null
13. _re.links.merchant_return.href != null) {
14. } else if (_re.body != null
15. _re.body.links != null
16. _re.body.links.merchant_return != null
17. _re.body.links.merchant_return.href != null) {
18. if (this._paymentService.getInvoiceDetail() != null) {
19. if (this._idInvoice != null
20. this._idInvoice != 0) {
21. _re.body != null) {
22. _showCardName != true"
23. _showEmail != true"
24. _showPhone != true"
25. this._paymentService.getCurrentPage() != 'error') {
26. } else if (this._res.payments != null
27. if (params['locale'] != null) {
28. if (this._idInvoice != null) {
29. this._res.links != null
30. this._res.links.merchant_return != null
31. if (this._res.merchant != null
32. this._res.merchant_invoice_reference != null) {
33. if (this._res.merchant.address_details != null) {
34. this._res.merchant.avs != null
35. if (this._res.billing != null
36. this._res.billing.address != null) {
37. if (this._res.billing.address.city != null
38. this._res.billing.address.city != 'null') this._i_city = this._res.billing.address.city;
39. if (this._res.billing.address.line1 != null
40. this._res.billing.address.line1 != 'null') this._i_address = this._res.billing.address.line1;
41. if (this._res.billing.address.postal_code != null
42. this._res.billing.address.postal_code != 'null') this._i_postal_code = this._res.billing.address.postal_code;
43. if (this._res.billing.address.state != null
44. this._res.billing.address.state != 'null') this._i_state = this._res.billing.address.state;
45. if (this._res.billing.address.country_code != null
46. this._res.billing.address.country_code != 'null') {
47. if (this._res_post.return_url != null) {
48. } else if (this._res_post.links != null
49. this._res_post.links.merchant_return != null
50. this._res_post.links.merchant_return.href != null) {
51. if (ua.indexOf('safari') != -1
52. cardNo != null
53. if (imglogo != null) {
54. if (this.valueDate.length != 3) {
55. v != null
56. this.c_csc = (!(_val.value != null
57. if (_formCard.card_number != null) cardNo = _formCard.card_number.replace(/\s+/g
58. if (cardNo != null
59. if (expdate != null
60. _formCard.csc != null
61. if (!(_formCard.card_name != null
62. if (_formCard.email != ''
63. if (_formCard.phone != ''
64. if (!(_formCard.country != null
65. if (!(_formCard.address != null
66. if (!(_formCard.city != null
67. if (!(_formCard.province != null
68. if (!(postalCode != null
69. if (this.current != 'card') {
70. if (this.current != 'date') {
71. if (this.current != 'csc') {
72. if (this._res_post.return_url != null)
73. else if (this._res_post.links != null
74. this._res_post.links.merchant_return.href != null)
75. this.requireAvs = this.isAvsCountry = country != undefined
76. if (idInvoice != null
77. idInvoice != 0)
78. if (this._merchantid != null
79. this._tranref != null
80. this._state != null
81. if (this._state != null
82. } else if (idInvoice != null
83. idInvoice != 0) {
84. let urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
85. if (tem != null) {
86. if ((tem = ua.match(/version\/(\d+)/i)) != null) {
87. if (_modules[r][6] != null) {
88. if (_modules[6][c] != null) {
89. if (_modules[row][col] != null) {
90. while (buffer.getLengthInBits() % 8 != 0) {
91. if (count != numChars) {
92. throw count + ' != ' + numChars
93. while (data != 0) {
94. if (test.length != 2
95. ( (test[0] << 8) | test[1]) != code) {
96. if (_length % 3 != 0) {
97. if ( (data >>> length) != 0) {
98. return typeof _map[key] != 'undefined'
99. document.documentMode!=null
100. if(tem!= null) return tem.slice(1).join(' ').replace('OPR'
101. if((tem= ua.match(/version\/(\d+)/i))!= null) M.splice(1

