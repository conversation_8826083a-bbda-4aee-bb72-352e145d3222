====================================================================================================
TRÍCH XUẤT ĐIỀU KIỆN SO SÁNH TỪ CÁC FILE TYPESCRIPT/JAVASCRIPT/HTML
====================================================================================================
Thư mục/File nguồn: /root/projects/onepay/paygate-bachhoaxanh/src
Thời gian: 18:12:27 18/8/2025
Tổng số file xử lý: 212
Tổng số file bị bỏ qua: 51
Tổng số điều kiện tìm thấy: 3674

THỐNG KÊ TỔNG QUAN THEO LOẠI TOÁN TỬ:
--------------------------------------------------
Strict equality (===): 1941 lần
Loose equality (==): 769 lần
Strict inequality (!==): 682 lần
Loose inequality (!=): 282 lần
--------------------------------------------------

DANH SÁCH FILE ĐÃ XỬ LÝ:
--------------------------------------------------
1. 4en.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/4en.html
2. 4vn.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/4vn.html
3. app-routing.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/app-routing.module.ts
4. app.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/app.component.html
5. app.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/app.component.spec.ts
6. app.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/app.component.ts
7. app.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/app.module.ts
8. PaymentType.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/constants/PaymentType.ts
9. counter.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/counter.directive.spec.ts
10. counter.directive.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/counter.directive.ts
11. format-carno-input.derective.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/directives/format-carno-input.derective.ts
12. uppercase-input.directive.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/directives/uppercase-input.directive.ts
13. error.component.html (7 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/error/error.component.html
14. error.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/error/error.component.spec.ts
15. error.component.ts (28 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/error/error.component.ts
16. support-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/error/support-dialog/support-dialog.html
17. support-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/error/support-dialog/support-dialog.ts
18. format-date.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/format-date.directive.spec.ts
19. format-date.directive.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/format-date.directive.ts
20. main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/main.component.html
21. main.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/main.component.spec.ts
22. main.component.ts (10 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/main.component.ts
23. app-result.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/app-result/app-result.component.html
24. app-result.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/app-result/app-result.component.ts
25. bank-amount-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
26. bank-amount-dialog.component.ts (34 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
27. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/dialog-guide-dialog.html
28. bankaccount.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
29. bankaccount.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
30. bankaccount.component.ts (151 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
31. bank.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/model/bank.ts
32. onepay-napas.component.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.html
33. onepay-napas.component.ts (102 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.ts
34. otp-auth.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
35. otp-auth.component.ts (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
36. shb.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/shb/shb.component.html
37. shb.component.ts (65 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/shb/shb.component.ts
38. techcombank.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
39. techcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
40. techcombank.component.ts (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
41. vibbank.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.html
42. vibbank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.spec.ts
43. vibbank.component.ts (155 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.ts
44. vietcombank.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
45. vietcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
46. vietcombank.component.ts (88 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
47. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
48. dialog-policy.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/dialog/dialog-policy/dialog-policy.component.html
49. dialog-policy.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/dialog/dialog-policy/dialog-policy.component.ts
50. off-bank-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
51. off-bank-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
52. domescard-main.component.html (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/domescard-main.component.html
53. domescard-main.component.ts (131 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/domescard-main.component.ts
54. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/intercard-main/dialog-guide-dialog.html
55. intercard-main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/intercard-main/intercard-main.component.html
56. intercard-main.component.ts (69 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/intercard-main/intercard-main.component.ts
57. menu.component.html (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/menu.component.html
58. menu.component.ts (122 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/menu.component.ts
59. applepay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/applepay/applepay.component.html
60. applepay.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/applepay/applepay.component.ts
61. dialog-network-not-supported.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/applepay/dialog-network-not-supported/dialog-network-not-supported.component.html
62. dialog-network-not-supported.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/applepay/dialog-network-not-supported/dialog-network-not-supported.component.ts
63. google-pay-button-op.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/googlepay/google-pay-button-op/google-pay-button-op.component.html
64. google-pay-button-op.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/googlepay/google-pay-button-op/google-pay-button-op.component.ts
65. googlepay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/googlepay/googlepay.component.html
66. googlepay.component.ts (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/googlepay/googlepay.component.ts
67. mobile-wallet-main.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/mobile-wallet-main.component.html
68. mobile-wallet-main.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/mobile-wallet-main.component.ts
69. samsung-pay-button-op.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.html
70. samsung-pay-button-op.component.ts (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.ts
71. samsungpay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/samsungpay/samsungpay.component.html
72. samsungpay.component.ts (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/samsungpay/samsungpay.component.ts
73. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
74. qr-dialog.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
75. qr-dialog.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
76. qr-main.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main/qr-main.component.html
77. qr-main.component.ts (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main/qr-main.component.ts
78. safe-html.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main/safe-html.pipe.ts
79. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/dialog/dialog-guide-dialog.html
80. qr-desktop.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.html
81. qr-desktop.component.ts (19 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.ts
82. qr-dialog.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.html
83. qr-dialog.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.ts
84. qr-guide-dialog.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.html
85. qr-guide-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.ts
86. list-bank-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.html
87. list-bank-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.ts
88. qr-main.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-main.component.html
89. qr-main.component.ts (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-main.component.ts
90. qr-mobile.component.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.html
91. qr-mobile.component.ts (30 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.ts
92. safe-html.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/safe-html.pipe.ts
93. queuing.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/queuing/queuing.component.html
94. queuing.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/queuing/queuing.component.ts
95. domescard-form.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.html
96. domescard-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.ts
97. intercard-form.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.html
98. intercard-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.ts
99. mobile-wallet-form.component.html (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/sorting-option/mobile-wallet-form/mobile-wallet-form.component.html
100. mobile-wallet-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/sorting-option/mobile-wallet-form/mobile-wallet-form.component.ts
101. paypal-form.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.html
102. paypal-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.ts
103. qr-form.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/sorting-option/qr-form/qr-form.component.html
104. qr-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/sorting-option/qr-form/qr-form.component.ts
105. overlay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/overlay/overlay.component.html
106. overlay.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/overlay/overlay.component.spec.ts
107. overlay.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/overlay/overlay.component.ts
108. bank-amount.pipe.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/pipe/bank-amount.pipe.ts
109. auth.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/services/auth.service.ts
110. close-dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/services/close-dialog.service.ts
111. data.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/services/data.service.ts
112. deep_link.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/services/deep_link.service.ts
113. dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/services/dialog.service.ts
114. digital-wallet.service.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/services/digital-wallet.service.ts
115. multiple_method.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/services/multiple_method.service.ts
116. payment.service.ts (12 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/services/payment.service.ts
117. qr.service.ts (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/services/qr.service.ts
118. time-stop.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/services/time-stop.service.ts
119. index.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/translate/index.ts
120. lang-en.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/translate/lang-en.ts
121. lang-vi.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/translate/lang-vi.ts
122. translate.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/translate/translate.pipe.ts
123. translate.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/translate/translate.service.ts
124. translations.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/translate/translations.ts
125. apps-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/util/apps-info.ts
126. apps-information.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/util/apps-information.ts
127. banks-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/util/banks-info.ts
128. error-handler.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/util/error-handler.ts
129. iso-ca-states.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/util/iso-ca-states.ts
130. iso-us-states.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/util/iso-us-states.ts
131. util.ts (39 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/util/util.ts
132. apple.js (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/assets/script/apple.js
133. google-pay-intergrate.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/assets/script/google-pay-intergrate.js
134. qrcode.js (67 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/assets/script/qrcode.js
135. environment.development.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/environments/environment.development.ts
136. environment.mtf.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/environments/environment.mtf.ts
137. environment.prod.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/environments/environment.prod.ts
138. environment.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/environments/environment.ts
139. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/acc_bidv/index.html
140. script.js (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/acc_bidv/script.js
141. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/acc_oceanbank/index.html
142. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/acc_oceanbank/script.js
143. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/acc_pvcombank/index.html
144. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/acc_pvcombank/script.js
145. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/acc_shb/index.html
146. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/acc_shb/script.js
147. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/acc_tpbank/index.html
148. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/acc_tpbank/script.js
149. common.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/common.js
150. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/domestic_card/index.html
151. script.js (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/domestic_card/script.js
152. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/domestic_token/index.html
153. script.js (25 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/domestic_token/script.js
154. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/index.html
155. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/international_card/index.html
156. script.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/international_card/script.js
157. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/international_token/index.html
158. script.js (36 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/international_token/script.js
159. script.js (54 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/script.js
160. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.js
161. bidv.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Bidv/assets/js/bidv.js
162. bidv2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Bidv/bidv2.js
163. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Bidv/index.html
164. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.js
165. ocean.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Ocean/assets/js/ocean.js
166. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Ocean/index.html
167. ocean2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Ocean/ocean2.js
168. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
169. pvbank.js (23 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Pv_Bank/assets/js/pvbank.js
170. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Pv_Bank/index.html
171. pvbank2.js (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Pv_Bank/pvbank2.js
172. Sea2.js (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/SeaBank/Sea2.js
173. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
174. Sea.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/SeaBank/assets/js/Sea.js
175. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/SeaBank/index.html
176. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.js
177. shb.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Shb/assets/js/shb.js
178. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Shb/index.html
179. shb2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Shb/shb2.js
180. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
181. tpbank.js (23 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Tp_Bank/assets/js/tpbank.js
182. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Tp_Bank/index.html
183. tpbank2.js (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Tp_Bank/tpbank2.js
184. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.js
185. onepay.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Visa/assets/js/onepay.js
186. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Visa/index.html
187. index.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Visa/index.js
188. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
189. vpbank.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Vp_Bank/assets/js/vpbank.js
190. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Vp_Bank/index.html
191. vpbank2.js (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Vp_Bank/vpbank2.js
192. sha.js (49 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/assets/js/sha.js
193. sha256.js (28 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/assets/js/sha256.js
194. slick.js (182 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/assets/libraries/slick/slick.js
195. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.js
196. atm_b1.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_1/assets/js/atm_b1.js
197. atm_b1_2.js (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_1/atm_b1_2.js
198. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_1/index.html
199. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.js
200. atm_b2.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_2/assets/js/atm_b2.js
201. atm_b2_2.js (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_2/atm_b2_2.js
202. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_2/index.html
203. common.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/common.js
204. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.js
205. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/international_card/index.html
206. script.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/international_card/script.js
207. index.html (35 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/index.html
208. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/index.html
209. karma.conf.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/karma.conf.js
210. main.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/main.ts
211. polyfills.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/polyfills.ts
212. test.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/test.ts

DANH SÁCH FILE BỊ BỎ QUA:
--------------------------------------------------
1. apple-pay-sdk.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/assets/script/apple-pay-sdk.js
2. google-pay-sdk.1726636373844.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/assets/script/google-pay-sdk.1726636373844.js
3. samsung-pay-sdk.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/assets/script/samsung-pay-sdk.js
4. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/bootstrap.min.js
5. jquery-3.0.0.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/jquery-3.0.0.min.js
6. popper.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/popper.min.js
7. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
8. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
9. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
10. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Bidv/assets/js/jquery-3.4.1.min.js
11. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
12. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
13. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
14. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Ocean/assets/js/jquery-3.4.1.min.js
15. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
16. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
17. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
18. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Pv_Bank/assets/js/jquery-3.4.1.min.js
19. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
20. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
21. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
22. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/SeaBank/assets/js/jquery-3.4.1.min.js
23. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
24. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
25. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
26. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Shb/assets/js/jquery-3.4.1.min.js
27. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
28. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
29. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
30. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Tp_Bank/assets/js/jquery-3.4.1.min.js
31. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
32. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
33. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
34. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Visa/assets/js/jquery-3.4.1.min.js
35. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
36. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
37. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
38. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Vp_Bank/assets/js/jquery-3.4.1.min.js
39. instafeed.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/assets/js/instafeed.min.js
40. slick.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/assets/libraries/slick/slick.min.js
41. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
42. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
43. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
44. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_1/assets/js/jquery-3.4.1.min.js
45. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
46. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
47. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
48. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_2/assets/js/jquery-3.4.1.min.js
49. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
50. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
51. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js

====================================================================================================
CHI TIẾT TỪNG FILE:
====================================================================================================

📁 FILE 1: 4en.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/4en.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 2: 4vn.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/4vn.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 3: app-routing.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/app-routing.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 4: app.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/app.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 5: app.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/app.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 6: app.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/app.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 79] return lang === this._translate.currentLang

== (2 điều kiện):
  1. [Dòng 59] 'vi' == params['locale']) {
  2. [Dòng 61] 'en' == params['locale']) {

!= (3 điều kiện):
  1. [Dòng 59] if (params['locale'] != null
  2. [Dòng 61] } else if (params['locale'] != null
  3. [Dòng 67] if (userLang != null

================================================================================

📁 FILE 7: app.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/app.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 8: PaymentType.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/constants/PaymentType.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 9: counter.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/counter.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 10: counter.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/counter.directive.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 11: format-carno-input.derective.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/directives/format-carno-input.derective.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 44] this.lastValue !== $event.target.value)) {

!= (1 điều kiện):
  1. [Dòng 23] if(value!=null){

================================================================================

📁 FILE 12: uppercase-input.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/directives/uppercase-input.directive.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 0 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 23] this.lastValue !== $event.target.value)) {

================================================================================

📁 FILE 13: error.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/error/error.component.html
📊 Thống kê: 7 điều kiện duy nhất
   - === : 0 lần
   - == : 7 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (7 điều kiện):
  1. [Dòng 3] errorCode == '11'"
  2. [Dòng 18] isAppleError ==false
  3. [Dòng 21] isAppleError==true
  4. [Dòng 105] <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
  5. [Dòng 105] (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
  6. [Dòng 108] *ngIf="!(errorCode == 'overtime'
  7. [Dòng 108] errorCode == '253'

================================================================================

📁 FILE 14: error.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/error/error.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 15: error.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/error/error.component.ts
📊 Thống kê: 28 điều kiện duy nhất
   - === : 8 lần
   - == : 17 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 141] params.timeout === 'true') {
  2. [Dòng 161] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  3. [Dòng 161] _re.body.state === 'unpaid');
  4. [Dòng 255] if (this.errorCode === 'overtime'
  5. [Dòng 255] this.errorCode === '253') {
  6. [Dòng 338] params.name === 'CUSTOMER_INTIME'
  7. [Dòng 338] params.code === '09') {
  8. [Dòng 385] if (this.timeLeft === 0) {

== (17 điều kiện):
  1. [Dòng 169] if (_re.body.currencies[0] == 'USD') {
  2. [Dòng 195] if (this.paymentInformation.type == "applepay_napas") {
  3. [Dòng 206] if (this.paymentInformation.type == "applepay") {
  4. [Dòng 211] } else if (this.paymentInformation.type == "googlepay") {
  5. [Dòng 214] } else if (this.paymentInformation.type == "samsungpay") {
  6. [Dòng 225] _re.body.themes.theme == 'general') {
  7. [Dòng 231] params.response_code == 'overtime') {
  8. [Dòng 278] if (_re.status == '200'
  9. [Dòng 278] _re.status == '201') {
  10. [Dòng 290] if (_re2.status == '200'
  11. [Dòng 290] _re2.status == '201') {
  12. [Dòng 303] if (this.errorCode == 'overtime'
  13. [Dòng 303] this.errorCode == '253') {
  14. [Dòng 306] } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
  15. [Dòng 311] _re.body.state == 'canceled') {
  16. [Dòng 332] if (lastPayment?.state == 'pending') {
  17. [Dòng 383] if (this.isTimePause == false) {

!= (3 điều kiện):
  1. [Dòng 118] if (message != ''
  2. [Dòng 118] message != null
  3. [Dòng 118] message != undefined) {

================================================================================

📁 FILE 16: support-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/error/support-dialog/support-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 17: support-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/error/support-dialog/support-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 18: format-date.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/format-date.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 19: format-date.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/format-date.directive.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0
  2. [Dòng 123] YY % 4 === 0)) {
  3. [Dòng 138] if (YYYY % 400 === 0
  4. [Dòng 138] YYYY % 4 === 0)) {

!== (2 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0 || (YY % 100 !== 0
  2. [Dòng 138] if (YYYY % 400 === 0 || (YYYY % 100 !== 0

================================================================================

📁 FILE 20: main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 21: main.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/main.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 22: main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/main.component.ts
📊 Thống kê: 10 điều kiện duy nhất
   - === : 1 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 117] params.name === 'CUSTOMER_INTIME')) {

== (3 điều kiện):
  1. [Dòng 84] if ((dataPassed.status == '200'
  2. [Dòng 84] dataPassed.status == '201') && dataPassed.body != null) {
  3. [Dòng 88] dataPassed.body.themes.logo_full == 'True') {

!= (6 điều kiện):
  1. [Dòng 75] if (this._idInvoice != null
  2. [Dòng 75] this._idInvoice != 0) {
  3. [Dòng 76] if (this._paymentService.getInvoiceDetail() != null) {
  4. [Dòng 84] dataPassed.body != null) {
  5. [Dòng 102] dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
  6. [Dòng 151] if (this._translate.currentLang != language) {

================================================================================

📁 FILE 23: app-result.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/app-result/app-result.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 24: app-result.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/app-result/app-result.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 25: bank-amount-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 26: bank-amount-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
📊 Thống kê: 34 điều kiện duy nhất
   - === : 0 lần
   - == : 34 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (34 điều kiện):
  1. [Dòng 41] if (this.locale == 'en') {
  2. [Dòng 55] if (name == 'MAFC')
  3. [Dòng 61] if (bankId == 3
  4. [Dòng 61] bankId == 61
  5. [Dòng 62] bankId == 8
  6. [Dòng 62] bankId == 49
  7. [Dòng 63] bankId == 48
  8. [Dòng 64] bankId == 10
  9. [Dòng 64] bankId == 53
  10. [Dòng 65] bankId == 17
  11. [Dòng 65] bankId == 65
  12. [Dòng 66] bankId == 23
  13. [Dòng 66] bankId == 52
  14. [Dòng 67] bankId == 27
  15. [Dòng 67] bankId == 66
  16. [Dòng 68] bankId == 9
  17. [Dòng 68] bankId == 54
  18. [Dòng 69] bankId == 37
  19. [Dòng 70] bankId == 38
  20. [Dòng 71] bankId == 39
  21. [Dòng 72] bankId == 40
  22. [Dòng 73] bankId == 42
  23. [Dòng 74] bankId == 44
  24. [Dòng 75] bankId == 72
  25. [Dòng 76] bankId == 59
  26. [Dòng 79] bankId == 51
  27. [Dòng 80] bankId == 64
  28. [Dòng 81] bankId == 58
  29. [Dòng 82] bankId == 56
  30. [Dòng 85] bankId == 55
  31. [Dòng 86] bankId == 60
  32. [Dòng 87] bankId == 68
  33. [Dòng 88] bankId == 74
  34. [Dòng 89] bankId == 75

================================================================================

📁 FILE 27: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 28: bankaccount.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 39] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 53] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 53] valueDate.trim().length === 0)"

================================================================================

📁 FILE 29: bankaccount.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 30: bankaccount.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
📊 Thống kê: 151 điều kiện duy nhất
   - === : 48 lần
   - == : 69 lần
   - !== : 13 lần
   - != : 21 lần
--------------------------------------------------------------------------------

=== (48 điều kiện):
  1. [Dòng 107] if (target.tagName === 'A'
  2. [Dòng 132] if (isIE[0] === 'MSIE'
  3. [Dòng 132] +isIE[1] === 10) {
  4. [Dòng 216] if ((_val.value.substr(-1) === ' '
  5. [Dòng 216] _val.value.length === 24) {
  6. [Dòng 226] if (this.cardTypeBank === 'bank_card_number') {
  7. [Dòng 231] } else if (this.cardTypeBank === 'bank_account_number') {
  8. [Dòng 237] } else if (this.cardTypeBank === 'bank_username') {
  9. [Dòng 241] } else if (this.cardTypeBank === 'bank_customer_code') {
  10. [Dòng 247] this.cardTypeBank === 'bank_card_number'
  11. [Dòng 261] if (this.cardTypeOcean === 'IB') {
  12. [Dòng 265] } else if (this.cardTypeOcean === 'MB') {
  13. [Dòng 266] if (_val.value.substr(0, 2) === '84') {
  14. [Dòng 273] } else if (this.cardTypeOcean === 'ATM') {
  15. [Dòng 300] if (this.cardTypeBank === 'bank_account_number') {
  16. [Dòng 319] this.cardTypeBank === 'bank_card_number') {
  17. [Dòng 341] if (this.cardTypeBank === 'bank_card_number'
  18. [Dòng 341] if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  19. [Dòng 662] if (event.keyCode === 8
  20. [Dòng 662] event.key === "Backspace"
  21. [Dòng 702] if (v.length === 2
  22. [Dòng 702] this.flag.length === 3
  23. [Dòng 702] this.flag.charAt(this.flag.length - 1) === '/') {
  24. [Dòng 706] if (v.length === 1) {
  25. [Dòng 708] } else if (v.length === 2) {
  26. [Dòng 711] v.length === 2) {
  27. [Dòng 719] if (len === 2) {
  28. [Dòng 992] if ((this.cardTypeBank === 'bank_account_number'
  29. [Dòng 992] this.cardTypeBank === 'bank_username'
  30. [Dòng 992] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  31. [Dòng 997] this.cardTypeOcean === 'ATM')
  32. [Dòng 998] || (this.cardTypeOcean === 'IB'
  33. [Dòng 1057] if (valIn === this._translate.instant('bank_card_number')) {
  34. [Dòng 1082] } else if (valIn === this._translate.instant('bank_account_number')) {
  35. [Dòng 1101] } else if (valIn === this._translate.instant('bank_username')) {
  36. [Dòng 1117] } else if (valIn === this._translate.instant('bank_customer_code')) {
  37. [Dòng 1206] if (_val.value === ''
  38. [Dòng 1206] _val.value === null
  39. [Dòng 1206] _val.value === undefined) {
  40. [Dòng 1215] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  41. [Dòng 1215] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  42. [Dòng 1222] this.cardTypeOcean === 'MB') {
  43. [Dòng 1230] this.cardTypeOcean === 'IB'
  44. [Dòng 1236] if ((this.cardTypeBank === 'bank_card_number'
  45. [Dòng 1268] if (this.cardName === undefined
  46. [Dòng 1268] this.cardName === '') {
  47. [Dòng 1276] if (this.valueDate === undefined
  48. [Dòng 1276] this.valueDate === '') {

== (69 điều kiện):
  1. [Dòng 146] if (this._b == 18
  2. [Dòng 146] this._b == 19) {
  3. [Dòng 149] if (this._b == 19) {//19BIDV
  4. [Dòng 157] } else if (this._b == 3
  5. [Dòng 157] this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  6. [Dòng 162] if (this._b == 27) {
  7. [Dòng 167] } else if (this._b == 12) {// 12SHB
  8. [Dòng 172] } else if (this._b == 18) { //18Oceanbank-ocb
  9. [Dòng 225] if (this._b == 19
  10. [Dòng 225] this._b == 3
  11. [Dòng 225] this._b == 27
  12. [Dòng 225] this._b == 12) {
  13. [Dòng 260] } else if (this._b == 18) {
  14. [Dòng 291] if (this.checkBin(_val.value) && (this._b == 3
  15. [Dòng 291] this._b == 27)) {
  16. [Dòng 296] if (this._b == 3) {
  17. [Dòng 308] this.cardTypeOcean == 'ATM') {
  18. [Dòng 321] if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  19. [Dòng 341] this._b == 18)) {
  20. [Dòng 417] if (this.checkBin(v) && (this._b == 3
  21. [Dòng 662] event.inputType == 'deleteContentBackward') {
  22. [Dòng 663] if (event.target.name == 'exp_date'
  23. [Dòng 671] event.inputType == 'insertCompositionText') {
  24. [Dòng 686] if (((this.valueDate.length == 4
  25. [Dòng 686] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  26. [Dòng 686] this.valueDate.length == 5)
  27. [Dòng 770] if (temp.length == 0) {
  28. [Dòng 777] return (counter % 10 == 0);
  29. [Dòng 800] } else if (this._b == 19) {
  30. [Dòng 802] } else if (this._b == 27) {
  31. [Dòng 807] if (this._b == 12) {
  32. [Dòng 809] if (this.cardTypeBank == 'bank_customer_code') {
  33. [Dòng 811] } else if (this.cardTypeBank == 'bank_account_number') {
  34. [Dòng 828] _formCard.exp_date.length == 5
  35. [Dòng 828] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
  36. [Dòng 828] this._b == 3)) {//27-pvcombank;3-TPB ;
  37. [Dòng 833] if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
  38. [Dòng 833] this._b == 19
  39. [Dòng 833] this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
  40. [Dòng 836] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  41. [Dòng 839] if (this.cardTypeOcean == 'IB') {
  42. [Dòng 841] } else if (this.cardTypeOcean == 'MB') {
  43. [Dòng 843] } else if (this.cardTypeOcean == 'ATM') {
  44. [Dòng 889] if (_re.status == '200'
  45. [Dòng 889] _re.status == '201') {
  46. [Dòng 894] if (this._res_post.state == 'approved'
  47. [Dòng 894] this._res_post.state == 'failed') {
  48. [Dòng 901] } else if (this._res_post.state == 'authorization_required') {
  49. [Dòng 919] if (this._b == 18) {
  50. [Dòng 924] if (this._b == 27
  51. [Dòng 924] this._b == 18) {
  52. [Dòng 1012] if ((cardNo.length == 16
  53. [Dòng 1012] if ((cardNo.length == 16 || (cardNo.length == 19
  54. [Dòng 1013] && ((this._b == 18
  55. [Dòng 1013] cardNo.length == 19) || this._b != 18)
  56. [Dòng 1026] if (this._b == +e.id) {
  57. [Dòng 1042] if (valIn == 1) {
  58. [Dòng 1044] } else if (valIn == 2) {
  59. [Dòng 1068] this._b == 3) {
  60. [Dòng 1075] if (this._b == 19) {
  61. [Dòng 1139] if (cardType == this._translate.instant('internetbanking')
  62. [Dòng 1147] } else if (cardType == this._translate.instant('mobilebanking')
  63. [Dòng 1155] } else if (cardType == this._translate.instant('atm')
  64. [Dòng 1215] this._b == 18))) {
  65. [Dòng 1222] } else if (this._b == 18
  66. [Dòng 1247] this.c_expdate = !(((this.valueDate.length == 4
  67. [Dòng 1279] this.valueDate.length == 4
  68. [Dòng 1279] this.valueDate.search('/') == -1)
  69. [Dòng 1280] this.valueDate.length == 5))

!== (13 điều kiện):
  1. [Dòng 216] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 875] key !== '3') {
  3. [Dòng 925] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 943] codeResponse.toString() !== '0') {
  5. [Dòng 992] cardNo.length !== 0) {
  6. [Dòng 1064] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 1085] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 1106] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 1126] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 1139] this.lb_card_account !== this._translate.instant('ocb_account')) {
  11. [Dòng 1147] this.lb_card_account !== this._translate.instant('ocb_phone')) {
  12. [Dòng 1155] this.lb_card_account !== this._translate.instant('ocb_card')) {
  13. [Dòng 1236] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (21 điều kiện):
  1. [Dòng 178] } else if (this._b != 18) {
  2. [Dòng 184] if (this.htmlDesc != null
  3. [Dòng 213] if (ua.indexOf('safari') != -1
  4. [Dòng 223] if (_val.value != '') {
  5. [Dòng 309] this.auth_method != null) {
  6. [Dòng 664] if (this.valueDate.length != 3) {
  7. [Dòng 828] if (_formCard.exp_date != null
  8. [Dòng 833] if (this.cardName != null
  9. [Dòng 897] if (this._res_post.links != null
  10. [Dòng 897] this._res_post.links.merchant_return != null
  11. [Dòng 897] this._res_post.links.merchant_return.href != null) {
  12. [Dòng 905] if (this._res_post.authorization != null
  13. [Dòng 905] this._res_post.authorization.links != null
  14. [Dòng 905] this._res_post.authorization.links.approval != null) {
  15. [Dòng 912] this._res_post.links.cancel != null) {
  16. [Dòng 1012] this._b != 27
  17. [Dòng 1012] this._b != 12
  18. [Dòng 1012] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  19. [Dòng 1013] this._b != 18)
  20. [Dòng 1059] if (this._b != 18
  21. [Dòng 1059] this._b != 19) {

================================================================================

📁 FILE 31: bank.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/model/bank.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 32: onepay-napas.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 3 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 26] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 41] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  3. [Dòng 41] _inExpDate.trim().length === 0)"

== (3 điều kiện):
  1. [Dòng 23] <div [ngStyle]="{'margin-top': !(checkTwoEnabled && !isOffTechcombankNapas && !isOffTechcombank) ? '15px' : '0px' }" *ngIf="(cardTypeBank == 'bank_card_number') &&(_b == 67
  2. [Dòng 65] (cardTypeBank == 'bank_card_number') &&(_b == 67 || checkTwoEnabled)&& ready===1 && !isOffTechcombankNapas
  3. [Dòng 95] <div [ngStyle]="{'margin-top': !(checkTwoEnabled && !isOffTechcombankNapas && !isOffTechcombank) ? '15px' : '0px' }" class="nd-bank-card-two" *ngIf="(cardTypeBank == 'internet_banking')&&(_b == 2

================================================================================

📁 FILE 33: onepay-napas.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.ts
📊 Thống kê: 102 điều kiện duy nhất
   - === : 21 lần
   - == : 45 lần
   - !== : 4 lần
   - != : 32 lần
--------------------------------------------------------------------------------

=== (21 điều kiện):
  1. [Dòng 105] if (target.tagName === 'A'
  2. [Dòng 130] if (isIE[0] === 'MSIE'
  3. [Dòng 130] +isIE[1] === 10) {
  4. [Dòng 154] if (this.timeLeft === 10) {
  5. [Dòng 158] if (this.runTime === true) {
  6. [Dòng 164] if (this.timeLeft === 0) {
  7. [Dòng 166] if (this.runTime === true) this.submitCardBanking();
  8. [Dòng 347] if (event.keyCode === 8
  9. [Dòng 347] event.key === "Backspace"
  10. [Dòng 567] if (approval.method === 'REDIRECT') {
  11. [Dòng 570] } else if (approval.method === 'POST_REDIRECT') {
  12. [Dòng 644] if (valIn === this._translate.instant('bank_card_number')) {
  13. [Dòng 646] if (this.timeLeft === 1) {
  14. [Dòng 663] } else if (valIn === this._translate.instant('internet_banking')) {
  15. [Dòng 743] if (_val.value === ''
  16. [Dòng 743] _val.value === null
  17. [Dòng 743] _val.value === undefined) {
  18. [Dòng 754] if (_val.value && (this.cardTypeBank === 'bank_card_number')) {
  19. [Dòng 764] if ((this.cardTypeBank === 'bank_card_number'
  20. [Dòng 802] if (this.cardName === undefined
  21. [Dòng 802] this.cardName === '') {

== (45 điều kiện):
  1. [Dòng 146] if (this._b == 67
  2. [Dòng 146] this._b == 2) {//19BIDV
  3. [Dòng 152] if((this._b == 2
  4. [Dòng 152] !this.checkTwoEnabled) || (this._b == 2
  5. [Dòng 175] } else if (this._b == 2
  6. [Dòng 178] if(this._b == 67 ){
  7. [Dòng 215] if (_re.status == '200'
  8. [Dòng 215] _re.status == '201') {
  9. [Dòng 220] if (this._res_post.state == 'approved'
  10. [Dòng 220] this._res_post.state == 'failed') {
  11. [Dòng 224] } else if (this._res_post.state == 'authorization_required') {
  12. [Dòng 332] return this._b == 2
  13. [Dòng 332] this._b == 67
  14. [Dòng 347] event.inputType == 'deleteContentBackward') {
  15. [Dòng 348] if (event.target.name == 'exp_date'
  16. [Dòng 356] event.inputType == 'insertCompositionText') {
  17. [Dòng 426] if (temp.length == 0) {
  18. [Dòng 433] return (counter % 10 == 0);
  19. [Dòng 452] _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
  20. [Dòng 607] if ((cardNo.length == 16
  21. [Dòng 607] if ((cardNo.length == 16 || (cardNo.length == 19
  22. [Dòng 608] this.checkMod10(cardNo) == true
  23. [Dòng 621] if (this._b == +e.id) {
  24. [Dòng 697] if (this._b == 19) {
  25. [Dòng 701] if (this._b == 27
  26. [Dòng 701] this._b == 3) {
  27. [Dòng 776] if (this._b != 68 || (this._b == 68
  28. [Dòng 785] return ((value.length == 4
  29. [Dòng 785] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  30. [Dòng 785] value.length == 5) && parseInt(value.split('/')[0]
  31. [Dòng 789] || (this._util.checkValidExpireMonthYear(value) && (this._b == 2
  32. [Dòng 789] this._b == 20
  33. [Dòng 789] this._b == 33
  34. [Dòng 790] this._b == 39
  35. [Dòng 790] this._b == 43
  36. [Dòng 790] this._b == 45
  37. [Dòng 790] this._b == 64
  38. [Dòng 790] this._b == 68
  39. [Dòng 790] this._b == 72))) //sonnh them Vietbank 72
  40. [Dòng 811] this._inExpDate.length == 4
  41. [Dòng 811] this._inExpDate.search('/') == -1)
  42. [Dòng 812] this._inExpDate.length == 5))
  43. [Dòng 814] || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 20
  44. [Dòng 814] this._b == 2
  45. [Dòng 814] this._b == 72)));

!== (4 điều kiện):
  1. [Dòng 239] codeResponse.toString() !== '0') {
  2. [Dòng 605] let _b = this._b !== 67 ? 67 : this._b
  3. [Dòng 690] if (this.cardTypeBank !== 'internet_banking') {
  4. [Dòng 764] this._b !== 18)) {

!= (32 điều kiện):
  1. [Dòng 171] if (this.htmlDesc != null
  2. [Dòng 228] if (this._res_post.authorization != null
  3. [Dòng 228] this._res_post.authorization.links != null
  4. [Dòng 228] this._res_post.authorization.links.approval != null) {
  5. [Dòng 284] if (ua.indexOf('safari') != -1
  6. [Dòng 349] if (this._inExpDate.length != 3) {
  7. [Dòng 452] if (_formCard.exp_date != null
  8. [Dòng 457] if (this.cardName != null
  9. [Dòng 495] if (this._res_post.return_url != null) {
  10. [Dòng 498] if (this._res_post.links != null
  11. [Dòng 498] this._res_post.links.merchant_return != null
  12. [Dòng 498] this._res_post.links.merchant_return.href != null) {
  13. [Dòng 552] this._res_post.links.cancel != null) {
  14. [Dòng 557] let userName = _formCard.name != null ? _formCard.name : ''
  15. [Dòng 558] this._res_post.authorization.links.approval != null
  16. [Dòng 558] this._res_post.authorization.links.approval.href != null) {
  17. [Dòng 561] userName = paramUserName != null ? paramUserName : ''
  18. [Dòng 607] this._b != 27
  19. [Dòng 607] this._b != 12
  20. [Dòng 607] this._b != 3))
  21. [Dòng 776] if (this._b != 68
  22. [Dòng 787] this._b != 2
  23. [Dòng 787] this._b != 20
  24. [Dòng 787] this._b != 33
  25. [Dòng 787] this._b != 39
  26. [Dòng 788] this._b != 43
  27. [Dòng 788] this._b != 45
  28. [Dòng 788] this._b != 64
  29. [Dòng 788] this._b != 67
  30. [Dòng 788] this._b != 68
  31. [Dòng 788] this._b != 72)
  32. [Dòng 813] this._b != 72 )

================================================================================

📁 FILE 34: otp-auth.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 35: otp-auth.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
📊 Thống kê: 22 điều kiện duy nhất
   - === : 0 lần
   - == : 11 lần
   - !== : 1 lần
   - != : 10 lần
--------------------------------------------------------------------------------

== (11 điều kiện):
  1. [Dòng 98] if (this._b == 8) {//MB Bank
  2. [Dòng 102] if (this._b == 18) {//Oceanbank
  3. [Dòng 139] if (this._b == 8) {
  4. [Dòng 144] if (this._b == 18) {
  5. [Dòng 149] if (this._b == 12) { //SHB
  6. [Dòng 170] if (_re.status == '200'
  7. [Dòng 170] _re.status == '201') {
  8. [Dòng 179] } else if (this._res.state == 'authorization_required') {
  9. [Dòng 211] if (this.challengeCode == '') {
  10. [Dòng 305] if (this._b == 12) {
  11. [Dòng 358] if (this._b == 18) {//8-MB Bank;18-oceanbank

!== (1 điều kiện):
  1. [Dòng 185] codeResponse.toString() !== '0') {

!= (10 điều kiện):
  1. [Dòng 175] if (this._res.links != null
  2. [Dòng 175] this._res.links.merchant_return != null
  3. [Dòng 175] this._res.links.merchant_return.href != null) {
  4. [Dòng 327] if (this._res_post != null
  5. [Dòng 327] this._res_post.links != null
  6. [Dòng 327] this._res_post.links.merchant_return != null
  7. [Dòng 327] this._res_post.links.merchant_return.href != null) {
  8. [Dòng 353] if (!(_formCard.otp != null
  9. [Dòng 359] if (!(_formCard.password != null
  10. [Dòng 375] if (ua.indexOf('safari') != -1

================================================================================

📁 FILE 36: shb.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/shb/shb.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 25] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 42] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  3. [Dòng 42] _inExpDate.trim().length === 0)"

== (2 điều kiện):
  1. [Dòng 22] <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_card_number')">
  2. [Dòng 66] <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_account_number')">

================================================================================

📁 FILE 37: shb.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/shb/shb.component.ts
📊 Thống kê: 65 điều kiện duy nhất
   - === : 18 lần
   - == : 31 lần
   - !== : 5 lần
   - != : 11 lần
--------------------------------------------------------------------------------

=== (18 điều kiện):
  1. [Dòng 95] if (target.tagName === 'A'
  2. [Dòng 120] if (isIE[0] === 'MSIE'
  3. [Dòng 120] +isIE[1] === 10) {
  4. [Dòng 162] if (focusElement === 'card_name') {
  5. [Dòng 164] } else if (focusElement === 'exp_date'
  6. [Dòng 185] focusExpDateElement === 'card_name') {
  7. [Dòng 400] if (this.cardTypeBank === 'bank_account_number'
  8. [Dòng 445] if (valIn === this._translate.instant('bank_card_number')) {
  9. [Dòng 451] } else if (valIn === this._translate.instant('bank_account_number')) {
  10. [Dòng 493] if (_val.value === ''
  11. [Dòng 493] _val.value === null
  12. [Dòng 493] _val.value === undefined) {
  13. [Dòng 504] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  14. [Dòng 504] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  15. [Dòng 511] this.cardTypeOcean === 'MB') {
  16. [Dòng 519] this.cardTypeOcean === 'IB'
  17. [Dòng 525] if ((this.cardTypeBank === 'bank_card_number'
  18. [Dòng 548] if (this.cardTypeOcean === 'IB') {

== (31 điều kiện):
  1. [Dòng 129] if(this._b == 12) this.isShbGroup = true;
  2. [Dòng 150] return this._b == 9
  3. [Dòng 150] this._b == 11
  4. [Dòng 150] this._b == 16
  5. [Dòng 150] this._b == 17
  6. [Dòng 150] this._b == 25
  7. [Dòng 150] this._b == 44
  8. [Dòng 151] this._b == 57
  9. [Dòng 151] this._b == 59
  10. [Dòng 151] this._b == 61
  11. [Dòng 151] this._b == 63
  12. [Dòng 151] this._b == 69
  13. [Dòng 240] if (this._b == 12
  14. [Dòng 240] this.cardTypeBank == 'bank_account_number') {
  15. [Dòng 251] _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
  16. [Dòng 297] if (_re.status == '200'
  17. [Dòng 297] _re.status == '201') {
  18. [Dòng 301] if (this._res_post.state == 'approved'
  19. [Dòng 301] this._res_post.state == 'failed') {
  20. [Dòng 307] } else if (this._res_post.state == 'authorization_required') {
  21. [Dòng 403] if ((cardNo.length == 16
  22. [Dòng 403] cardNo.length == 19) && cardNo.startsWith("970443") && this._util.checkMod10(cardNo) && this.checkBin(cardNo) ) {
  23. [Dòng 415] if (this._b == +e.id) {
  24. [Dòng 431] if (valIn == 1) {
  25. [Dòng 433] } else if (valIn == 2) {
  26. [Dòng 504] this._b == 18))) {
  27. [Dòng 511] } else if (this._b == 18
  28. [Dòng 525] this._b == 18)) {
  29. [Dòng 537] this.c_expdate = !(((this.valueDate.length == 4
  30. [Dòng 537] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  31. [Dòng 537] this.valueDate.length == 5)

!== (5 điều kiện):
  1. [Dòng 285] key !== '3') {
  2. [Dòng 327] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  3. [Dòng 345] codeResponse.toString() !== '0') {
  4. [Dòng 400] cardNo.length !== 0) {
  5. [Dòng 525] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (11 điều kiện):
  1. [Dòng 137] if (this.htmlDesc != null
  2. [Dòng 198] if (ua.indexOf('safari') != -1
  3. [Dòng 251] if (_formCard.exp_date != null
  4. [Dòng 256] if (this.cardName != null
  5. [Dòng 304] if (this._res_post.links != null
  6. [Dòng 304] this._res_post.links.merchant_return != null
  7. [Dòng 304] this._res_post.links.merchant_return.href != null) {
  8. [Dòng 310] if (this._res_post.authorization != null
  9. [Dòng 310] this._res_post.authorization.links != null
  10. [Dòng 310] this._res_post.authorization.links.approval != null) {
  11. [Dòng 317] this._res_post.links.cancel != null) {

================================================================================

📁 FILE 38: techcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 39: techcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 40: techcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
📊 Thống kê: 15 điều kiện duy nhất
   - === : 1 lần
   - == : 10 lần
   - !== : 1 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 63] if (this.timeLeft === 0) {

== (10 điều kiện):
  1. [Dòng 54] if (this._b == 2
  2. [Dòng 54] this._b == 31) {
  3. [Dòng 91] if (this._b == 2) {
  4. [Dòng 93] } else if (this._b == 6) {
  5. [Dòng 95] } else if (this._b == 31) {
  6. [Dòng 125] if (_re.status == '200'
  7. [Dòng 125] _re.status == '201') {
  8. [Dòng 130] if (this._res_post.state == 'approved'
  9. [Dòng 130] this._res_post.state == 'failed') {
  10. [Dòng 134] } else if (this._res_post.state == 'authorization_required') {

!== (1 điều kiện):
  1. [Dòng 149] codeResponse.toString() !== '0') {

!= (3 điều kiện):
  1. [Dòng 138] if (this._res_post.authorization != null
  2. [Dòng 138] this._res_post.authorization.links != null
  3. [Dòng 138] this._res_post.authorization.links.approval != null) {

================================================================================

📁 FILE 41: vibbank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 39] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 53] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 53] valueDate.trim().length === 0)"

================================================================================

📁 FILE 42: vibbank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 43: vibbank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.ts
📊 Thống kê: 155 điều kiện duy nhất
   - === : 49 lần
   - == : 72 lần
   - !== : 13 lần
   - != : 21 lần
--------------------------------------------------------------------------------

=== (49 điều kiện):
  1. [Dòng 112] if (target.tagName === 'A'
  2. [Dòng 137] if (isIE[0] === 'MSIE'
  3. [Dòng 137] +isIE[1] === 10) {
  4. [Dòng 181] if (this.timeLeft === 0) {
  5. [Dòng 238] if ((_val.value.substr(-1) === ' '
  6. [Dòng 238] _val.value.length === 24) {
  7. [Dòng 248] if (this.cardTypeBank === 'bank_card_number') {
  8. [Dòng 253] } else if (this.cardTypeBank === 'bank_account_number') {
  9. [Dòng 259] } else if (this.cardTypeBank === 'bank_username') {
  10. [Dòng 263] } else if (this.cardTypeBank === 'bank_customer_code') {
  11. [Dòng 269] this.cardTypeBank === 'bank_card_number'
  12. [Dòng 283] if (this.cardTypeOcean === 'IB') {
  13. [Dòng 287] } else if (this.cardTypeOcean === 'MB') {
  14. [Dòng 288] if (_val.value.substr(0, 2) === '84') {
  15. [Dòng 295] } else if (this.cardTypeOcean === 'ATM') {
  16. [Dòng 322] if (this.cardTypeBank === 'bank_account_number') {
  17. [Dòng 344] this.cardTypeBank === 'bank_card_number') {
  18. [Dòng 366] if (this.cardTypeBank === 'bank_card_number'
  19. [Dòng 366] if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  20. [Dòng 693] if (event.keyCode === 8
  21. [Dòng 693] event.key === "Backspace"
  22. [Dòng 733] if (v.length === 2
  23. [Dòng 733] this.flag.length === 3
  24. [Dòng 733] this.flag.charAt(this.flag.length - 1) === '/') {
  25. [Dòng 737] if (v.length === 1) {
  26. [Dòng 739] } else if (v.length === 2) {
  27. [Dòng 742] v.length === 2) {
  28. [Dòng 750] if (len === 2) {
  29. [Dòng 1022] if ((this.cardTypeBank === 'bank_account_number'
  30. [Dòng 1022] this.cardTypeBank === 'bank_username'
  31. [Dòng 1022] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  32. [Dòng 1027] this.cardTypeOcean === 'ATM')
  33. [Dòng 1028] || (this.cardTypeOcean === 'IB'
  34. [Dòng 1087] if (valIn === this._translate.instant('bank_card_number')) {
  35. [Dòng 1112] } else if (valIn === this._translate.instant('bank_account_number')) {
  36. [Dòng 1131] } else if (valIn === this._translate.instant('bank_username')) {
  37. [Dòng 1147] } else if (valIn === this._translate.instant('bank_customer_code')) {
  38. [Dòng 1236] if (_val.value === ''
  39. [Dòng 1236] _val.value === null
  40. [Dòng 1236] _val.value === undefined) {
  41. [Dòng 1247] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  42. [Dòng 1247] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  43. [Dòng 1254] this.cardTypeOcean === 'MB') {
  44. [Dòng 1262] this.cardTypeOcean === 'IB'
  45. [Dòng 1268] if ((this.cardTypeBank === 'bank_card_number'
  46. [Dòng 1302] if (this.cardName === undefined
  47. [Dòng 1302] this.cardName === '') {
  48. [Dòng 1310] if (this.valueDate === undefined
  49. [Dòng 1310] this.valueDate === '') {

== (72 điều kiện):
  1. [Dòng 151] if (this._b == 18
  2. [Dòng 151] this._b == 19) {
  3. [Dòng 154] if (this._b == 19) {//19BIDV
  4. [Dòng 162] } else if (this._b == 3
  5. [Dòng 162] this._b == 27
  6. [Dòng 162] this._b == 5 ) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  7. [Dòng 168] if (this._b == 27
  8. [Dòng 168] this._b == 5) {
  9. [Dòng 189] } else if (this._b == 12) {// 12SHB
  10. [Dòng 194] } else if (this._b == 18) { //18Oceanbank-ocb
  11. [Dòng 247] if (this._b == 19
  12. [Dòng 247] this._b == 3
  13. [Dòng 247] this._b == 12
  14. [Dòng 282] } else if (this._b == 18) {
  15. [Dòng 313] if (this.checkBin(_val.value) && (this._b == 3
  16. [Dòng 313] this._b == 5)) {
  17. [Dòng 318] if (this._b == 3) {
  18. [Dòng 326] if (this._b == 5) {
  19. [Dòng 333] this.cardTypeOcean == 'ATM') {
  20. [Dòng 346] this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  21. [Dòng 366] this._b == 18)) {
  22. [Dòng 442] if (this.checkBin(v) && (this._b == 3
  23. [Dòng 693] event.inputType == 'deleteContentBackward') {
  24. [Dòng 694] if (event.target.name == 'exp_date'
  25. [Dòng 702] event.inputType == 'insertCompositionText') {
  26. [Dòng 717] if (((this.valueDate.length == 4
  27. [Dòng 717] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  28. [Dòng 717] this.valueDate.length == 5)
  29. [Dòng 801] if (temp.length == 0) {
  30. [Dòng 808] return (counter % 10 == 0);
  31. [Dòng 830] } else if (this._b == 19) {
  32. [Dòng 832] } else if (this._b == 27) {
  33. [Dòng 834] } else if (this._b == 5) {
  34. [Dòng 839] if (this._b == 12) {
  35. [Dòng 841] if (this.cardTypeBank == 'bank_customer_code') {
  36. [Dòng 843] } else if (this.cardTypeBank == 'bank_account_number') {
  37. [Dòng 858] _formCard.exp_date.length == 5
  38. [Dòng 858] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
  39. [Dòng 858] this._b == 5)) {//27-pvcombank;3-TPB ;
  40. [Dòng 863] if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
  41. [Dòng 863] this._b == 19
  42. [Dòng 863] this._b == 5)) {//3-TPB ;19-BIDV;27-pvcombank;
  43. [Dòng 866] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  44. [Dòng 869] if (this.cardTypeOcean == 'IB') {
  45. [Dòng 871] } else if (this.cardTypeOcean == 'MB') {
  46. [Dòng 873] } else if (this.cardTypeOcean == 'ATM') {
  47. [Dòng 919] if (_re.status == '200'
  48. [Dòng 919] _re.status == '201') {
  49. [Dòng 924] if (this._res_post.state == 'approved'
  50. [Dòng 924] this._res_post.state == 'failed') {
  51. [Dòng 931] } else if (this._res_post.state == 'authorization_required') {
  52. [Dòng 949] if (this._b == 18) {
  53. [Dòng 954] this._b == 18
  54. [Dòng 1042] if ((cardNo.length == 16
  55. [Dòng 1042] if ((cardNo.length == 16 || (cardNo.length == 19
  56. [Dòng 1043] && ((this._b == 18
  57. [Dòng 1043] cardNo.length == 19) || this._b != 18)
  58. [Dòng 1056] if (this._b == +e.id) {
  59. [Dòng 1072] if (valIn == 1) {
  60. [Dòng 1074] } else if (valIn == 2) {
  61. [Dòng 1105] if (this._b == 19) {
  62. [Dòng 1126] this._b == 3) {
  63. [Dòng 1169] if (cardType == this._translate.instant('internetbanking')
  64. [Dòng 1177] } else if (cardType == this._translate.instant('mobilebanking')
  65. [Dòng 1185] } else if (cardType == this._translate.instant('atm')
  66. [Dòng 1247] this._b == 18))) {
  67. [Dòng 1254] } else if (this._b == 18
  68. [Dòng 1280] this.c_expdate = !(((this.valueDate.length == 4
  69. [Dòng 1313] this.valueDate.length == 4
  70. [Dòng 1313] this.valueDate.search('/') == -1)
  71. [Dòng 1314] this.valueDate.length == 5))
  72. [Dòng 1333] if(this._b == 5) {

!== (13 điều kiện):
  1. [Dòng 238] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 905] key !== '3') {
  3. [Dòng 955] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 973] codeResponse.toString() !== '0') {
  5. [Dòng 1022] cardNo.length !== 0) {
  6. [Dòng 1094] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 1115] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 1136] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 1156] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 1169] this.lb_card_account !== this._translate.instant('ocb_account')) {
  11. [Dòng 1177] this.lb_card_account !== this._translate.instant('ocb_phone')) {
  12. [Dòng 1185] this.lb_card_account !== this._translate.instant('ocb_card')) {
  13. [Dòng 1268] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (21 điều kiện):
  1. [Dòng 200] } else if (this._b != 18) {
  2. [Dòng 206] if (this.htmlDesc != null
  3. [Dòng 235] if (ua.indexOf('safari') != -1
  4. [Dòng 245] if (_val.value != '') {
  5. [Dòng 334] this.auth_method != null) {
  6. [Dòng 695] if (this.valueDate.length != 3) {
  7. [Dòng 858] if (_formCard.exp_date != null
  8. [Dòng 863] if (this.cardName != null
  9. [Dòng 927] if (this._res_post.links != null
  10. [Dòng 927] this._res_post.links.merchant_return != null
  11. [Dòng 927] this._res_post.links.merchant_return.href != null) {
  12. [Dòng 935] if (this._res_post.authorization != null
  13. [Dòng 935] this._res_post.authorization.links != null
  14. [Dòng 935] this._res_post.authorization.links.approval != null) {
  15. [Dòng 942] this._res_post.links.cancel != null) {
  16. [Dòng 1042] this._b != 27
  17. [Dòng 1042] this._b != 12
  18. [Dòng 1042] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  19. [Dòng 1043] this._b != 18)
  20. [Dòng 1089] if (this._b != 18
  21. [Dòng 1089] this._b != 19) {

================================================================================

📁 FILE 44: vietcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 27] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  2. [Dòng 27] _inExpDate.trim().length === 0)"

== (1 điều kiện):
  1. [Dòng 62] _b == 68"

================================================================================

📁 FILE 45: vietcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 46: vietcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
📊 Thống kê: 88 điều kiện duy nhất
   - === : 5 lần
   - == : 56 lần
   - !== : 2 lần
   - != : 25 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 83] if (target.tagName === 'A'
  2. [Dòng 208] if (event.keyCode === 8
  3. [Dòng 208] event.key === "Backspace"
  4. [Dòng 451] if (approval.method === 'REDIRECT') {
  5. [Dòng 454] } else if (approval.method === 'POST_REDIRECT') {

== (56 điều kiện):
  1. [Dòng 122] if (this._b == 1
  2. [Dòng 122] this._b == 20
  3. [Dòng 122] this._b == 36
  4. [Dòng 122] this._b == 64
  5. [Dòng 122] this._b == 55
  6. [Dòng 122] this._b == 47
  7. [Dòng 122] this._b == 48
  8. [Dòng 122] this._b == 59) {
  9. [Dòng 135] return this._b == 9
  10. [Dòng 135] this._b == 16
  11. [Dòng 135] this._b == 17
  12. [Dòng 135] this._b == 25
  13. [Dòng 135] this._b == 44
  14. [Dòng 136] this._b == 57
  15. [Dòng 136] this._b == 59
  16. [Dòng 136] this._b == 61
  17. [Dòng 136] this._b == 63
  18. [Dòng 136] this._b == 69
  19. [Dòng 208] event.inputType == 'deleteContentBackward') {
  20. [Dòng 209] if (event.target.name == 'exp_date'
  21. [Dòng 217] event.inputType == 'insertCompositionText') {
  22. [Dòng 341] if (this._res_post.state == 'approved'
  23. [Dòng 341] this._res_post.state == 'failed') {
  24. [Dòng 390] } else if (this._res_post.state == 'authorization_required') {
  25. [Dòng 412] this._b == 14
  26. [Dòng 412] this._b == 15
  27. [Dòng 412] this._b == 24
  28. [Dòng 412] this._b == 8
  29. [Dòng 412] this._b == 10
  30. [Dòng 412] this._b == 22
  31. [Dòng 412] this._b == 23
  32. [Dòng 412] this._b == 30
  33. [Dòng 412] this._b == 11
  34. [Dòng 412] this._b == 9) {
  35. [Dòng 492] if ((cardNo.length == 16
  36. [Dòng 493] (cardNo.length == 19
  37. [Dòng 493] (cardNo.length == 19 && (this._b == 1
  38. [Dòng 493] this._b == 4
  39. [Dòng 493] this._b == 59))
  40. [Dòng 495] this._util.checkMod10(cardNo) == true
  41. [Dòng 531] return ((value.length == 4
  42. [Dòng 531] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  43. [Dòng 531] value.length == 5) && parseInt(value.split('/')[0]
  44. [Dòng 542] this._b == 33
  45. [Dòng 542] this._b == 39
  46. [Dòng 542] this._b == 43
  47. [Dòng 542] this._b == 45
  48. [Dòng 543] this._b == 67
  49. [Dòng 543] this._b == 72
  50. [Dòng 543] this._b == 73
  51. [Dòng 543] this._b == 68
  52. [Dòng 543] this._b == 74
  53. [Dòng 543] this._b == 75
  54. [Dòng 574] this._inExpDate.length == 4
  55. [Dòng 574] this._inExpDate.search('/') == -1)
  56. [Dòng 575] this._inExpDate.length == 5))

!== (2 điều kiện):
  1. [Dòng 354] codeResponse.toString() !== '0') {
  2. [Dòng 413] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (25 điều kiện):
  1. [Dòng 127] if (this.htmlDesc != null
  2. [Dòng 145] if (ua.indexOf('safari') != -1
  3. [Dòng 210] if (this._inExpDate.length != 3) {
  4. [Dòng 294] if ( this._b != 9
  5. [Dòng 294] this._b != 16
  6. [Dòng 294] this._b != 17
  7. [Dòng 294] this._b != 25
  8. [Dòng 294] this._b != 44
  9. [Dòng 295] this._b != 57
  10. [Dòng 295] this._b != 59
  11. [Dòng 295] this._b != 61
  12. [Dòng 295] this._b != 63
  13. [Dòng 295] this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
  14. [Dòng 308] '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75'].indexOf(this._b.toString()) != -1) {// duongpxt 19255: Them vietbank napas id 72
  15. [Dòng 343] if (this._res_post.return_url != null) {
  16. [Dòng 346] if (this._res_post.links != null
  17. [Dòng 346] this._res_post.links.merchant_return != null
  18. [Dòng 346] this._res_post.links.merchant_return.href != null) {
  19. [Dòng 395] if (this._res_post.authorization != null
  20. [Dòng 395] this._res_post.authorization.links != null
  21. [Dòng 400] this._res_post.links.cancel != null) {
  22. [Dòng 406] let userName = _formCard.name != null ? _formCard.name : ''
  23. [Dòng 407] this._res_post.authorization.links.approval != null
  24. [Dòng 407] this._res_post.authorization.links.approval.href != null) {
  25. [Dòng 410] userName = paramUserName != null ? paramUserName : ''

================================================================================

📁 FILE 47: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 48: dialog-policy.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/dialog/dialog-policy/dialog-policy.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 49: dialog-policy.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/dialog/dialog-policy/dialog-policy.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 50: off-bank-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 51: off-bank-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 52: domescard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/domescard-main.component.html
📊 Thống kê: 9 điều kiện duy nhất
   - === : 1 lần
   - == : 8 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 15] filteredData.length === 0"

== (8 điều kiện):
  1. [Dòng 24] (!token&&_auth==0&& vietcombankGroupSelected )|| (token && _b == 16)
  2. [Dòng 24] _b == 16)">
  3. [Dòng 28] _auth==0 && techcombankGroupSelected
  4. [Dòng 32] _auth==0 && shbGroupSelected
  5. [Dòng 37] _auth==0 && onepaynapasGroupSelected
  6. [Dòng 43] _auth==0 && bankaccountGroupSelected
  7. [Dòng 47] _auth==0 && vibbankGroupSelected
  8. [Dòng 51] (token || _auth==1) && _b != 16

================================================================================

📁 FILE 53: domescard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/domescard-main/domescard-main.component.ts
📊 Thống kê: 131 điều kiện duy nhất
   - === : 20 lần
   - == : 102 lần
   - !== : 2 lần
   - != : 7 lần
--------------------------------------------------------------------------------

=== (20 điều kiện):
  1. [Dòng 264] if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
  2. [Dòng 265] filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
  3. [Dòng 311] if (valOut === 'auth') {
  4. [Dòng 474] if (this._b === '1'
  5. [Dòng 474] this._b === '20'
  6. [Dòng 474] this._b === '64') {
  7. [Dòng 477] if (this._b === '36'
  8. [Dòng 477] this._b === '18'
  9. [Dòng 480] if (this._b === '19'
  10. [Dòng 480] this._b === '16'
  11. [Dòng 480] this._b === '25'
  12. [Dòng 480] this._b === '33'
  13. [Dòng 481] this._b === '39'
  14. [Dòng 481] this._b === '11'
  15. [Dòng 481] this._b === '17'
  16. [Dòng 482] this._b === '36'
  17. [Dòng 482] this._b === '44'
  18. [Dòng 483] this._b === '64'
  19. [Dòng 486] if (this._b === '20'
  20. [Dòng 489] if (this._b === '18') {

== (102 điều kiện):
  1. [Dòng 169] this._auth == 0) {
  2. [Dòng 247] if (item.b.id == '2'
  3. [Dòng 247] item.b.id == '67') {
  4. [Dòng 288] $event == 'true') {
  5. [Dòng 296] _b: this._b == '2' ? '67' : this._b
  6. [Dòng 317] if (bankid == 2
  7. [Dòng 317] bankid == 67) {
  8. [Dòng 321] || (off && !this.enabledTwoBankTech && ((bankid == '2'
  9. [Dòng 321] this.isOffTechcombank) || (bankid == '67'
  10. [Dòng 401] if (bankId == 1
  11. [Dòng 401] bankId == 4
  12. [Dòng 401] bankId == 7
  13. [Dòng 401] bankId == 8
  14. [Dòng 401] bankId == 9
  15. [Dòng 401] bankId == 10
  16. [Dòng 401] bankId == 11
  17. [Dòng 401] bankId == 14
  18. [Dòng 401] bankId == 15
  19. [Dòng 402] bankId == 16
  20. [Dòng 402] bankId == 17
  21. [Dòng 402] bankId == 20
  22. [Dòng 402] bankId == 22
  23. [Dòng 402] bankId == 23
  24. [Dòng 402] bankId == 24
  25. [Dòng 402] bankId == 25
  26. [Dòng 402] bankId == 30
  27. [Dòng 402] bankId == 33
  28. [Dòng 403] bankId == 34
  29. [Dòng 403] bankId == 35
  30. [Dòng 403] bankId == 36
  31. [Dòng 403] bankId == 37
  32. [Dòng 403] bankId == 38
  33. [Dòng 403] bankId == 39
  34. [Dòng 403] bankId == 40
  35. [Dòng 403] bankId == 41
  36. [Dòng 403] bankId == 42
  37. [Dòng 404] bankId == 43
  38. [Dòng 404] bankId == 44
  39. [Dòng 404] bankId == 45
  40. [Dòng 404] bankId == 46
  41. [Dòng 404] bankId == 47
  42. [Dòng 404] bankId == 48
  43. [Dòng 404] bankId == 49
  44. [Dòng 404] bankId == 50
  45. [Dòng 404] bankId == 51
  46. [Dòng 405] bankId == 52
  47. [Dòng 405] bankId == 53
  48. [Dòng 405] bankId == 54
  49. [Dòng 405] bankId == 55
  50. [Dòng 405] bankId == 56
  51. [Dòng 405] bankId == 57
  52. [Dòng 405] bankId == 58
  53. [Dòng 405] bankId == 59
  54. [Dòng 405] bankId == 60
  55. [Dòng 406] bankId == 61
  56. [Dòng 406] bankId == 62
  57. [Dòng 406] bankId == 63
  58. [Dòng 406] bankId == 64
  59. [Dòng 406] bankId == 65
  60. [Dòng 406] bankId == 66
  61. [Dòng 406] bankId == 68
  62. [Dòng 406] bankId == 69
  63. [Dòng 406] bankId == 70
  64. [Dòng 407] bankId == 71
  65. [Dòng 407] bankId == 72
  66. [Dòng 407] bankId == 73
  67. [Dòng 407] bankId == 32
  68. [Dòng 407] bankId == 74
  69. [Dòng 407] bankId == 75) {
  70. [Dòng 409] } else if (bankId == 6
  71. [Dòng 409] bankId == 31) {
  72. [Dòng 411] } else if (bankId == 2
  73. [Dòng 411] bankId == 67) {
  74. [Dòng 413] } else if (bankId == 3
  75. [Dòng 413] bankId == 18
  76. [Dòng 413] bankId == 19
  77. [Dòng 413] bankId == 27) {
  78. [Dòng 415] } else if (bankId == 5) {
  79. [Dòng 417] } else if (bankId == 12) {
  80. [Dòng 477] this._b == '55'
  81. [Dòng 477] this._b == '47'
  82. [Dòng 477] this._b == '48'
  83. [Dòng 477] this._b == '19'
  84. [Dòng 477] this._b == '59'
  85. [Dòng 477] this._b == '73'
  86. [Dòng 477] this._b == '12') {
  87. [Dòng 480] this._b == '3'
  88. [Dòng 481] this._b == '43'
  89. [Dòng 481] this._b == '45'
  90. [Dòng 482] this._b == '57'
  91. [Dòng 483] this._b == '61'
  92. [Dòng 483] this._b == '63'
  93. [Dòng 483] this._b == '67'
  94. [Dòng 483] this._b == '68'
  95. [Dòng 483] this._b == '69'
  96. [Dòng 483] this._b == '72'
  97. [Dòng 483] this._b == '9'
  98. [Dòng 483] this._b == '74'
  99. [Dòng 483] this._b == '75') {
  100. [Dòng 486] this._b == '36'
  101. [Dòng 486] this._b == '75') { //sonnh them 72 Vietbank
  102. [Dòng 506] if (item['id'] == this._b) {

!== (2 điều kiện):
  1. [Dòng 118] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  2. [Dòng 252] this.bankList = this.bankList.filter(item => item.b.id !== '67');

!= (7 điều kiện):
  1. [Dòng 155] if (params['locale'] != null) {
  2. [Dòng 161] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 165] this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
  4. [Dòng 192] if (!(strInstrument != null
  5. [Dòng 195] if (strInstrument.substring(0, 1) != '^'
  6. [Dòng 195] strInstrument.substr(strInstrument.length - 1) != '$') {
  7. [Dòng 380] if (bankid != null) {

================================================================================

📁 FILE 54: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/intercard-main/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 55: intercard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/intercard-main/intercard-main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 56: intercard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/intercard-main/intercard-main.component.ts
📊 Thống kê: 69 điều kiện duy nhất
   - === : 11 lần
   - == : 35 lần
   - !== : 10 lần
   - != : 13 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 145] if (target.tagName === 'A'
  2. [Dòng 289] if (_formCard.country === 'default') {
  3. [Dòng 622] if (event.keyCode === 8
  4. [Dòng 622] event.key === "Backspace"
  5. [Dòng 697] if ((v.substr(-1) === ' '
  6. [Dòng 830] this._i_country_code === 'US') {
  7. [Dòng 866] const insertIndex = this._i_country_code === 'US' ? 5 : 3
  8. [Dòng 868] if (temp[i] === '-'
  9. [Dòng 868] temp[i] === ' ') {
  10. [Dòng 875] insertIndex === 3 ? ' ' : itemRemoved)
  11. [Dòng 919] this.c_country = _val.value === 'default'

== (35 điều kiện):
  1. [Dòng 351] if (this._res_post.state == 'approved'
  2. [Dòng 351] this._res_post.state == 'failed') {
  3. [Dòng 376] } else if (this._res_post.state == 'failed') { // lỗi mới kiểm tra sang trang lỗi
  4. [Dòng 406] } else if (this._res_post.state == 'authorization_required') {
  5. [Dòng 407] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  6. [Dòng 419] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  7. [Dòng 469] v.length == 15) || (v.length == 16
  8. [Dòng 469] v.length == 19))
  9. [Dòng 470] this._util.checkMod10(v) == true) {
  10. [Dòng 576] cardNo.length == 15)
  11. [Dòng 578] cardNo.length == 16)
  12. [Dòng 579] cardNo.startsWith('81')) && (cardNo.length == 16
  13. [Dòng 579] cardNo.length == 19))
  14. [Dòng 622] event.inputType == 'deleteContentBackward') {
  15. [Dòng 623] if (event.target.name == 'exp_date'
  16. [Dòng 631] event.inputType == 'insertCompositionText') {
  17. [Dòng 646] if (((this.valueDate.length == 4
  18. [Dòng 646] this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  19. [Dòng 646] this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  20. [Dòng 697] v.length == 5) {
  21. [Dòng 705] v.length == 4
  22. [Dòng 709] v.length == 3)
  23. [Dòng 735] _val.value.length == 4
  24. [Dòng 739] _val.value.length == 3)
  25. [Dòng 882] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  26. [Dòng 882] this.valueDate.length == 5)
  27. [Dòng 910] this.requireAvs = this.AVS_COUNTRIES.find(e => e.code == this._i_country_code) ? true : false;
  28. [Dòng 966] this.valueDate.length == 4
  29. [Dòng 966] this.valueDate.search('/') == -1)
  30. [Dòng 967] this.valueDate.length == 5))
  31. [Dòng 981] this._i_csc.length == 4) ||
  32. [Dòng 985] this._i_csc.length == 3)
  33. [Dòng 1026] country = this.AVS_COUNTRIES.find(e => e.code == res.bin_country);
  34. [Dòng 1048] countryCode == 'US' ? US_STATES
  35. [Dòng 1049] : countryCode == 'CA' ? CA_STATES

!== (10 điều kiện):
  1. [Dòng 333] key !== '8') {
  2. [Dòng 360] codeResponse.toString() !== '0'){
  3. [Dòng 697] event.inputType !== 'deleteContentBackward') || v.length == 5) {
  4. [Dòng 833] this._i_country_code !== 'US') {
  5. [Dòng 874] itemRemoved !== '') {
  6. [Dòng 901] if (deviceValue !== 'default') {
  7. [Dòng 906] this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
  8. [Dòng 993] this._i_country_code !== 'default'
  9. [Dòng 1072] this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
  10. [Dòng 1079] this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {

!= (13 điều kiện):
  1. [Dòng 182] if (params['locale'] != null) {
  2. [Dòng 185] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 353] if (this._res_post.return_url != null) {
  4. [Dòng 355] } else if (this._res_post.links != null
  5. [Dòng 355] this._res_post.links.merchant_return != null
  6. [Dòng 355] this._res_post.links.merchant_return.href != null) {
  7. [Dòng 451] if (ua.indexOf('safari') != -1
  8. [Dòng 576] cardNo != null
  9. [Dòng 624] if (this.valueDate.length != 3) {
  10. [Dòng 704] v != null
  11. [Dòng 734] this.c_csc = (!(_val.value != null
  12. [Dòng 979] this._i_csc != null
  13. [Dòng 1028] this.requireAvs = this.isAvsCountry = country != undefined

================================================================================

📁 FILE 57: menu.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/menu.component.html
📊 Thống kê: 11 điều kiện duy nhất
   - === : 1 lần
   - == : 10 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 197] d_vrbank===true"

== (10 điều kiện):
  1. [Dòng 61] !token) || (type == 1
  2. [Dòng 79] !token) || (type == 6
  3. [Dòng 124] !token) || (type == 3
  4. [Dòng 139] method?.trim()=='International'"
  5. [Dòng 147] method.trim()=='ApplePay'"
  6. [Dòng 154] method.trim()=='GooglePay'"
  7. [Dòng 161] method.trim()=='SamsungPay'"
  8. [Dòng 167] method?.trim()=='Domestic'"
  9. [Dòng 175] method?.trim()=='QR'"
  10. [Dòng 182] method?.trim()=='Paypal'"

================================================================================

📁 FILE 58: menu.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/menu.component.ts
📊 Thống kê: 122 điều kiện duy nhất
   - === : 8 lần
   - == : 58 lần
   - !== : 3 lần
   - != : 53 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 646] this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_mobile_wallet === 1
  2. [Dòng 683] if (this._res.state === 'unpaid'
  3. [Dòng 683] this._res.state === 'not_paid') {
  4. [Dòng 795] if ('op' === auth
  5. [Dòng 836] } else if ('bank' === auth
  6. [Dòng 841] if (approval.method === 'REDIRECT') {
  7. [Dòng 844] } else if (approval.method === 'POST_REDIRECT') {
  8. [Dòng 1064] if (this.timeLeftPaypal === 0) {

== (58 điều kiện):
  1. [Dòng 184] if (el == 1) {
  2. [Dòng 186] } else if (el == 2) {
  3. [Dòng 188] } else if (el == 4) {
  4. [Dòng 190] } else if (el == 3) {
  5. [Dòng 234] if (!isNaN(_re.status) && (_re.status == '200'
  6. [Dòng 234] _re.status == '201') && _re.body != null) {
  7. [Dòng 239] if (('closed' == this._res_polling.state
  8. [Dòng 239] 'canceled' == this._res_polling.state
  9. [Dòng 239] 'expired' == this._res_polling.state)
  10. [Dòng 259] } else if ('paid' == this._res_polling.state) {
  11. [Dòng 269] this._res_polling.payments == null
  12. [Dòng 278] if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'authorization_required') {
  13. [Dòng 282] } else if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  14. [Dòng 289] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  15. [Dòng 291] this._paymentService.getCurrentPage() == 'enter_card') {
  16. [Dòng 294] } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
  17. [Dòng 294] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
  18. [Dòng 311] } else if ('not_paid' == this._res_polling.state) {
  19. [Dòng 323] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
  20. [Dòng 439] if (auth == 'auth') {
  21. [Dòng 441] detail.merchant.id == 'AMWAY') {
  22. [Dòng 466] if (this.checkInvoiceState() == 1) {
  23. [Dòng 473] if (_re.status == '200'
  24. [Dòng 473] _re.status == '201') {
  25. [Dòng 479] this.version2 = _re.body?.merchant?.qr_version == "2"
  26. [Dòng 498] if (this.themeConfig.default_method == 'International'
  27. [Dòng 500] } else if (this.themeConfig.default_method == 'Domestic'
  28. [Dòng 502] } else if (this.themeConfig.default_method == 'QR'
  29. [Dòng 504] } else if (this.themeConfig.default_method == 'Paypal'
  30. [Dòng 553] if (('closed' == this._res.state
  31. [Dòng 553] 'canceled' == this._res.state
  32. [Dòng 553] 'expired' == this._res.state
  33. [Dòng 553] 'paid' == this._res.state)
  34. [Dòng 557] if ('paid' == this._res.state
  35. [Dòng 652] this._auth == 0) {
  36. [Dòng 663] if (count == 2
  37. [Dòng 684] if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
  38. [Dòng 684] this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
  39. [Dòng 686] if (this._res.payments[this._res.payments.length - 1].state == 'approved'
  40. [Dòng 692] } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
  41. [Dòng 735] if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required') {
  42. [Dòng 741] } else if (this._res.payments[this._res.payments.length - 1].state == 'pending') {
  43. [Dòng 747] } else if (this._res.payments[this._res.payments.length - 1].state == 'canceled') {
  44. [Dòng 751] } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  45. [Dòng 751] this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
  46. [Dòng 781] if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  47. [Dòng 784] } else if (idBrand == 'atm'
  48. [Dòng 865] if ('paid' == this._res.state) {
  49. [Dòng 892] 'canceled' == this._res.state) {
  50. [Dòng 911] this._res.payments == null) {
  51. [Dòng 913] this._res.payments[this._res.payments.length - 1].state == 'pending') {
  52. [Dòng 923] if (this._res.currencies[0] == 'USD') {
  53. [Dòng 1112] if (this._res_post.state == 'approved'
  54. [Dòng 1112] this._res_post.state == 'failed') {
  55. [Dòng 1120] } else if (this._res_post.state == 'authorization_required') {
  56. [Dòng 1121] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  57. [Dòng 1135] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  58. [Dòng 1211] if (data._locale == 'en') {

!== (3 điều kiện):
  1. [Dòng 811] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  2. [Dòng 1030] if (_val !== 3) {
  3. [Dòng 1034] this._util.getElementParams(this.currentUrl, 't_id') !== '') {

!= (53 điều kiện):
  1. [Dòng 228] if (this._idInvoice != null) {
  2. [Dòng 233] if (this._paymentService.getCurrentPage() != 'otp') {
  3. [Dòng 234] _re.body != null) {
  4. [Dòng 240] this._res_polling.links != null
  5. [Dòng 240] this._res_polling.links.merchant_return != null //
  6. [Dòng 243] this._util.getElementParams(url_redirect, 'vpc_TxnResponseCode') != '99') {
  7. [Dòng 269] } else if (this._res_polling.merchant != null
  8. [Dòng 269] this._res_polling.merchant_invoice_reference != null
  9. [Dòng 269] this._paymentService.getState() != 'error') {
  10. [Dòng 271] } else if (this._res_polling.payments != null
  11. [Dòng 295] this._res_polling.links.merchant_return != null//
  12. [Dòng 314] this._res_polling.payments != null
  13. [Dòng 322] if (this._res_polling.payments != null
  14. [Dòng 326] t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
  15. [Dòng 448] this.type.toString().length != 0) {
  16. [Dòng 454] if (params['locale'] != null) {
  17. [Dòng 460] if ('otp' != this._paymentService.getCurrentPage()) {
  18. [Dòng 464] if (this._paymentService.getInvoiceDetail() != null) {
  19. [Dòng 554] this._res.links != null
  20. [Dòng 554] this._res.links.merchant_return != null
  21. [Dòng 667] if (count != 1) {
  22. [Dòng 673] if (this._res.merchant != null
  23. [Dòng 673] this._res.merchant_invoice_reference != null) {
  24. [Dòng 676] if (this._res.merchant.address_details != null) {
  25. [Dòng 684] this._res.links != null//
  26. [Dòng 731] } else if (this._res.payments != null
  27. [Dòng 752] this._res.payments[this._res.payments.length - 1].instrument != null
  28. [Dòng 752] this._res.payments[this._res.payments.length - 1].instrument.issuer != null
  29. [Dòng 753] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
  30. [Dòng 753] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
  31. [Dòng 754] this._res.payments[this._res.payments.length - 1].links != null
  32. [Dòng 754] this._res.payments[this._res.payments.length - 1].links.cancel != null
  33. [Dòng 754] this._res.payments[this._res.payments.length - 1].links.cancel.href != null
  34. [Dòng 768] this._res.payments[this._res.payments.length - 1].links.update != null
  35. [Dòng 768] this._res.payments[this._res.payments.length - 1].links.update.href != null) {
  36. [Dòng 784] this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
  37. [Dòng 785] this._res.payments[this._res.payments.length - 1].authorization != null
  38. [Dòng 785] this._res.payments[this._res.payments.length - 1].authorization.links != null) {
  39. [Dòng 795] this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
  40. [Dòng 795] this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
  41. [Dòng 798] if (this._res.payments[this._res.payments.length - 1].authorization != null
  42. [Dòng 798] this._res.payments[this._res.payments.length - 1].authorization.links != null
  43. [Dòng 804] auth = paramUserName != null ? paramUserName : ''
  44. [Dòng 890] this._res.links.merchant_return != null //
  45. [Dòng 911] } else if (this._res.merchant != null
  46. [Dòng 911] this._res.merchant_invoice_reference != null
  47. [Dòng 974] if (!(strInstrument != null
  48. [Dòng 991] if (this._translate.currentLang != language) {
  49. [Dòng 1036] } else if (this._res.payments != null) {
  50. [Dòng 1114] if (this._res_post.return_url != null) {
  51. [Dòng 1116] } else if (this._res_post.links != null
  52. [Dòng 1116] this._res_post.links.merchant_return != null
  53. [Dòng 1116] this._res_post.links.merchant_return.href != null) {

================================================================================

📁 FILE 59: applepay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/applepay/applepay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 60: applepay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/applepay/applepay.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 39] if (event.data?.event_type == 'applepay_network_not_supported') {

!= (1 điều kiện):
  1. [Dòng 37] if (event.origin != window.origin) return; // chỉ nhận message từ OnePay

================================================================================

📁 FILE 61: dialog-network-not-supported.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/applepay/dialog-network-not-supported/dialog-network-not-supported.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 62: dialog-network-not-supported.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/applepay/dialog-network-not-supported/dialog-network-not-supported.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 63: google-pay-button-op.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/googlepay/google-pay-button-op/google-pay-button-op.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 64: google-pay-button-op.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/googlepay/google-pay-button-op/google-pay-button-op.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 184] if (GGPaySDKScript.readyState === "loaded"
  2. [Dòng 184] GGPaySDKScript.readyState === "complete") {

================================================================================

📁 FILE 65: googlepay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/googlepay/googlepay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 66: googlepay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/googlepay/googlepay.component.ts
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 25] isTop = window === window.top
  2. [Dòng 59] if (approval.method === 'REDIRECT') {
  3. [Dòng 62] } else if (approval.method === 'POST_REDIRECT') {

== (2 điều kiện):
  1. [Dòng 48] if (res?.body?.state == 'approved') {
  2. [Dòng 57] } else if (res?.body?.state == 'authorization_required') {

================================================================================

📁 FILE 67: mobile-wallet-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/mobile-wallet-main.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (3 điều kiện):
  1. [Dòng 7] paymentType == PaymentType.ApplePay"
  2. [Dòng 8] paymentType == PaymentType.GooglePay"
  3. [Dòng 9] paymentType == PaymentType.SamsungPay"

================================================================================

📁 FILE 68: mobile-wallet-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/mobile-wallet-main.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 86] return currency === 'VND'

== (2 điều kiện):
  1. [Dòng 47] if (this.paymentType == PaymentType.ApplePay) {
  2. [Dòng 74] if (network == 'napas'

!= (3 điều kiện):
  1. [Dòng 57] if (this.paymentType != PaymentType.ApplePay) return;
  2. [Dòng 69] if (this.paymentType != PaymentType.ApplePay) return false;
  3. [Dòng 75] if (network != 'napas'

================================================================================

📁 FILE 69: samsung-pay-button-op.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 70: samsung-pay-button-op.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.ts
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 127] if (GGPaySDKScript.readyState === "loaded"
  2. [Dòng 127] GGPaySDKScript.readyState === "complete") {

== (1 điều kiện):
  1. [Dòng 62] serviceId: this.environment == 'PRODUCTION'? this.serviceIdProd : this.serviceIdTest

================================================================================

📁 FILE 71: samsungpay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/samsungpay/samsungpay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 72: samsungpay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/mobile-wallet-main/samsungpay/samsungpay.component.ts
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 74] if (approval.method === 'REDIRECT') {
  2. [Dòng 77] } else if (approval.method === 'POST_REDIRECT') {

== (2 điều kiện):
  1. [Dòng 62] if (res?.body?.state == 'approved') {
  2. [Dòng 72] } else if (res?.body?.state == 'authorization_required') {

================================================================================

📁 FILE 73: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 74: qr-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 2] screen=='qr'"
  2. [Dòng 112] screen=='confirm_close'"

================================================================================

📁 FILE 75: qr-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 46] 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {

================================================================================

📁 FILE 76: qr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main/qr-main.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 6] filteredData.length === 0
  2. [Dòng 6] filteredDataOther.length === 0"
  3. [Dòng 40] filteredDataMobile.length === 0
  4. [Dòng 40] filteredDataOtherMobile.length === 0"

================================================================================

📁 FILE 77: qr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main/qr-main.component.ts
📊 Thống kê: 22 điều kiện duy nhất
   - === : 8 lần
   - == : 8 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 220] this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
  2. [Dòng 221] this.filteredDataOther = this.appList.filter(item => item.type === 'other');
  3. [Dòng 222] this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
  4. [Dòng 223] this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
  5. [Dòng 244] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  6. [Dòng 245] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  7. [Dòng 251] if (item.type === 'mobile_banking') {
  8. [Dòng 350] this.appList.length === 1

== (8 điều kiện):
  1. [Dòng 148] this.themeColor.deeplink_status == 'Off' ? false : true
  2. [Dòng 250] if (item.available == true) {
  3. [Dòng 316] if (_re.status == '200'
  4. [Dòng 316] _re.status == '201') {
  5. [Dòng 319] if (appcode == 'grabpay'
  6. [Dòng 319] appcode == 'momo') {
  7. [Dòng 322] if (type == 2
  8. [Dòng 361] err.error.code == '04') {

!= (6 điều kiện):
  1. [Dòng 166] if (params['locale'] != null) {
  2. [Dòng 172] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 197] if (!(strInstrument != null
  4. [Dòng 280] if (appcode != null) {
  5. [Dòng 405] if (_re.status != '200'
  6. [Dòng 405] _re.status != '201')

================================================================================

📁 FILE 78: safe-html.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main/safe-html.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 79: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 80: qr-desktop.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 1 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 168] listVNPayQR.length === 0"

================================================================================

📁 FILE 81: qr-desktop.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.ts
📊 Thống kê: 19 điều kiện duy nhất
   - === : 4 lần
   - == : 10 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 380] this.listWalletQR.length === 1) {
  2. [Dòng 430] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  3. [Dòng 431] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  4. [Dòng 785] this.listWalletQR.length === 1

== (10 điều kiện):
  1. [Dòng 272] e.type == 'vnpayqr') {
  2. [Dòng 279] e.type == 'ewallet') {
  3. [Dòng 329] if (_re.status == '200'
  4. [Dòng 329] _re.status == '201') {
  5. [Dòng 358] e.type == 'wallet')) {
  6. [Dòng 397] if (d.b.code == s) {
  7. [Dòng 436] if (item.available == true) {
  8. [Dòng 496] if (appcode == 'grabpay'
  9. [Dòng 496] appcode == 'momo') {
  10. [Dòng 529] err.error.code == '04') {

!= (5 điều kiện):
  1. [Dòng 225] if (params['locale'] != null) {
  2. [Dòng 262] if (!(strInstrument != null
  3. [Dòng 454] if (appcode != null) {
  4. [Dòng 759] if (_re.status != '200'
  5. [Dòng 759] _re.status != '201')

================================================================================

📁 FILE 82: qr-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1] screen=='qr'"
  2. [Dòng 122] screen=='confirm_close'"

================================================================================

📁 FILE 83: qr-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 50] 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {

================================================================================

📁 FILE 84: qr-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (3 điều kiện):
  1. [Dòng 5] type == 'vnpay'"
  2. [Dòng 6] type == 'bankapp'"
  3. [Dòng 7] type == 'both'"

================================================================================

📁 FILE 85: qr-guide-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 86: list-bank-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 87: list-bank-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 88: qr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-main.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 6] filteredData.length === 0
  2. [Dòng 6] filteredDataOther.length === 0"
  3. [Dòng 47] filteredDataMobile.length === 0
  4. [Dòng 47] filteredDataOtherMobile.length === 0"

================================================================================

📁 FILE 89: qr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-main.component.ts
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 190] this.themeConfig.deeplink_status == 'Off' ? false : true

!= (2 điều kiện):
  1. [Dòng 206] if (params['locale'] != null) {
  2. [Dòng 212] if ('otp' != this._paymentService.getCurrentPage()) {

================================================================================

📁 FILE 90: qr-mobile.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 134] _locale=='vi'"
  2. [Dòng 135] _locale=='en'"
  3. [Dòng 145] _locale == 'vi'"
  4. [Dòng 147] _locale == 'en'"

!= (2 điều kiện):
  1. [Dòng 234] qr_version2 != 'None'"
  2. [Dòng 260] qr_version2 != 'None'

================================================================================

📁 FILE 91: qr-mobile.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.ts
📊 Thống kê: 30 điều kiện duy nhất
   - === : 6 lần
   - == : 19 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (6 điều kiện):
  1. [Dòng 486] if (!this.d_vnpayqr_wallet && !this.d_vnpayqr_deeplink && (this.listWalletQR?.length === 1
  2. [Dòng 486] this.listWalletDeeplink?.length === 1)) {
  3. [Dòng 604] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  4. [Dòng 605] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  5. [Dòng 611] if (item.type === 'deeplink') {
  6. [Dòng 1002] this.listWalletQR?.length === 1

== (19 điều kiện):
  1. [Dòng 294] e.type == 'deeplink') {
  2. [Dòng 305] e.type == 'ewallet'
  3. [Dòng 325] e.type == 'vnpayqr') {
  4. [Dòng 339] e.type == 'wallet')) {
  5. [Dòng 368] e.type == 'ewallet') {
  6. [Dòng 398] if (e.type == 'ewallet') {
  7. [Dòng 421] this.listWallet.length == 1
  8. [Dòng 421] this.listWallet[0].code == 'momo') {
  9. [Dòng 423] this.checkEWalletDeeplink.length == 0) {
  10. [Dòng 504] arrayWallet.length == 0) return false;
  11. [Dòng 506] if (arrayWallet[i].code == key) {
  12. [Dòng 539] if (_re.status == '200'
  13. [Dòng 539] _re.status == '201') {
  14. [Dòng 561] if (d.b.code == s) {
  15. [Dòng 610] if (item.available == true) {
  16. [Dòng 687] if (appcode == 'grabpay'
  17. [Dòng 687] appcode == 'momo') {
  18. [Dòng 690] if (type == 2
  19. [Dòng 730] err.error.code == '04') {

!= (5 điều kiện):
  1. [Dòng 232] if (params['locale'] != null) {
  2. [Dòng 275] if (!(strInstrument != null
  3. [Dòng 638] if (appcode != null) {
  4. [Dòng 974] if (_re.status != '200'
  5. [Dòng 974] _re.status != '201')

================================================================================

📁 FILE 92: safe-html.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/qr-main-version2/safe-html.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 93: queuing.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/queuing/queuing.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 94: queuing.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/queuing/queuing.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 62] if (this.locale == 'vi') {

!= (1 điều kiện):
  1. [Dòng 86] if (this.translate.currentLang != language) {

================================================================================

📁 FILE 95: domescard-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 1] type === 2"
  2. [Dòng 3] [ngStyle]="{'border-color': (type === 2
  3. [Dòng 14] type === 2
  4. [Dòng 14] type === '2'

================================================================================

📁 FILE 96: domescard-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 97: intercard-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 3 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 1] type === 1"
  2. [Dòng 2] [ngStyle]="{'border-color': (type === 1
  3. [Dòng 19] type === 1

== (1 điều kiện):
  1. [Dòng 4] !token) || (type == 1

================================================================================

📁 FILE 98: intercard-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 99: mobile-wallet-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/sorting-option/mobile-wallet-form/mobile-wallet-form.component.html
📊 Thống kê: 8 điều kiện duy nhất
   - === : 3 lần
   - == : 5 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 1] type === paymentType"
  2. [Dòng 2] [ngStyle]="{'border-color': (type === paymentType
  3. [Dòng 38] type === paymentType

== (5 điều kiện):
  1. [Dòng 5] *ngIf="(!token) || (type == paymentType)"
  2. [Dòng 8] paymentType == PaymentType.ApplePay"
  3. [Dòng 14] paymentType == PaymentType.GooglePay"
  4. [Dòng 20] paymentType == PaymentType.SamsungPay"
  5. [Dòng 35] type == paymentType"

================================================================================

📁 FILE 100: mobile-wallet-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/sorting-option/mobile-wallet-form/mobile-wallet-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 101: paypal-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 1] type === 3"
  2. [Dòng 3] [ngStyle]="{'border-color': (type === 3

================================================================================

📁 FILE 102: paypal-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 103: qr-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/sorting-option/qr-form/qr-form.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 1] type === 4"
  2. [Dòng 2] [ngStyle]="{'border-color': (type === 4
  3. [Dòng 13] type === 4

================================================================================

📁 FILE 104: qr-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/main/menu/sorting-option/qr-form/qr-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 105: overlay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/overlay/overlay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 106: overlay.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/overlay/overlay.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 107: overlay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/overlay/overlay.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 108: bank-amount.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/pipe/bank-amount.pipe.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 11] return ((a.id == id
  2. [Dòng 11] a.code == id) && a.type.includes(type));

================================================================================

📁 FILE 109: auth.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/services/auth.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 110: close-dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/services/close-dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 111: data.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/services/data.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 66] const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
  2. [Dòng 66] item.method === method) : null;

================================================================================

📁 FILE 112: deep_link.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/services/deep_link.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 9] if (isIphone == true) {
  2. [Dòng 22] } else if (isAndroid == true) {

================================================================================

📁 FILE 113: dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/services/dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 114: digital-wallet.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/services/digital-wallet.service.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 195] element.value == 'true'

================================================================================

📁 FILE 115: multiple_method.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/services/multiple_method.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 116: payment.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/services/payment.service.ts
📊 Thống kê: 12 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 10 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 548] return countPayment == maxPayment
  2. [Dòng 586] if (this.getLatestPayment().state == 'canceled')

!= (10 điều kiện):
  1. [Dòng 109] if (idInvoice != null
  2. [Dòng 109] idInvoice != 0)
  3. [Dòng 119] idInvoice != 0) {
  4. [Dòng 301] if (this._merchantid != null
  5. [Dòng 301] this._tranref != null
  6. [Dòng 301] this._state != null
  7. [Dòng 379] urlCancel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
  8. [Dòng 401] if (paymentId != null) {
  9. [Dòng 484] if (res?.status != 200
  10. [Dòng 484] res?.status != 201) return;

================================================================================

📁 FILE 117: qr.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/services/qr.service.ts
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 59] if (res?.state == 'canceled') {
  2. [Dòng 100] state == 'authorization_required'

!= (3 điều kiện):
  1. [Dòng 41] if (_re.status != '200'
  2. [Dòng 41] _re.status != '201') {
  3. [Dòng 50] latestPayment?.state != "authorization_required") {

================================================================================

📁 FILE 118: time-stop.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/services/time-stop.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 119: index.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/translate/index.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 120: lang-en.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/translate/lang-en.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 121: lang-vi.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/translate/lang-vi.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 122: translate.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/translate/translate.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 123: translate.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/translate/translate.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 37] if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
  2. [Dòng 38] else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';

================================================================================

📁 FILE 124: translations.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/translate/translations.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 125: apps-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/util/apps-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 474] if (e.name == bankSwift) { // TODO: get by swift
  2. [Dòng 482] return this.apps.find(e => e.code == appCode);

================================================================================

📁 FILE 126: apps-information.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/util/apps-information.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 127: banks-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/util/banks-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1013] if (+e.id == bankId) {
  2. [Dòng 1063] if (e.swiftCode == bankSwift) {

================================================================================

📁 FILE 128: error-handler.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/util/error-handler.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 9] err?.status === 400
  2. [Dòng 10] err?.error?.name === 'INVALID_CARD_FEE'

================================================================================

📁 FILE 129: iso-ca-states.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/util/iso-ca-states.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 130: iso-us-states.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/util/iso-us-states.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 131: util.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/app/util/util.ts
📊 Thống kê: 39 điều kiện duy nhất
   - === : 15 lần
   - == : 17 lần
   - !== : 3 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (15 điều kiện):
  1. [Dòng 141] if (v.length === 2
  2. [Dòng 141] this.flag.length === 3
  3. [Dòng 141] this.flag.charAt(this.flag.length - 1) === '/') {
  4. [Dòng 145] if (v.length === 1) {
  5. [Dòng 147] } else if (v.length === 2) {
  6. [Dòng 150] v.length === 2) {
  7. [Dòng 158] if (len === 2) {
  8. [Dòng 230] if (M[1] === 'Chrome') {
  9. [Dòng 356] if (param === key) {
  10. [Dòng 479] pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
  11. [Dòng 483] target === 0
  12. [Dòng 575] if (cardTypeBank === 'bank_card_number') {
  13. [Dòng 578] } else if (cardTypeBank === 'bank_account_number') {
  14. [Dòng 628] if (event.keyCode === 8
  15. [Dòng 628] event.key === "Backspace"

== (17 điều kiện):
  1. [Dòng 13] if (temp.length == 0) {
  2. [Dòng 20] return (counter % 10 == 0);
  3. [Dòng 204] if (this.checkCount == 1) {
  4. [Dòng 216] if (results == null) {
  5. [Dòng 249] if (c.length == 3) {
  6. [Dòng 262] d = d == undefined ? '.' : d
  7. [Dòng 263] t = t == undefined ? '
  8. [Dòng 344] return results == null ? null : results[1]
  9. [Dòng 628] event.inputType == 'deleteContentBackward') {
  10. [Dòng 629] if (event.target.name == 'exp_date'
  11. [Dòng 637] event.inputType == 'insertCompositionText') {
  12. [Dòng 651] if (((_val.length == 4
  13. [Dòng 651] _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  14. [Dòng 651] _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  15. [Dòng 677] iss_date.length == 4
  16. [Dòng 677] iss_date.search('/') == -1)
  17. [Dòng 678] iss_date.length == 5))

!== (3 điều kiện):
  1. [Dòng 351] queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
  2. [Dòng 352] if (queryString !== '') {
  3. [Dòng 483] if (target !== 0

!= (4 điều kiện):
  1. [Dòng 232] if (tem != null) {
  2. [Dòng 237] if ((tem = ua.match(/version\/(\d+)/i)) != null) {
  3. [Dòng 573] if (ua.indexOf('safari') != -1
  4. [Dòng 630] if (v.length != 3) {

================================================================================

📁 FILE 132: apple.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/assets/script/apple.js
📊 Thống kê: 15 điều kiện duy nhất
   - === : 0 lần
   - == : 13 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (13 điều kiện):
  1. [Dòng 65] let applepayNapas = document.getElementById('applepay-napas')?.value == 'true'
  2. [Dòng 66] let applepayVisa = document.getElementById('applepay-visa')?.value == 'true'
  3. [Dòng 67] let applepayMasterCard = document.getElementById('applepay-masterCard')?.value == 'true'
  4. [Dòng 68] let applepayJCB = document.getElementById('applepay-jcb')?.value == 'true'
  5. [Dòng 72] if (applepayNapas == true) {
  6. [Dòng 76] if (applepayVisa == true) {
  7. [Dòng 80] if (applepayMasterCard == true) {
  8. [Dòng 84] if (applepayJCB == true) {
  9. [Dòng 140] if(document.getElementById('applepay-merchantAVS').value == 'true'){
  10. [Dòng 192] response.status == '400') {
  11. [Dòng 194] } else if (response.status == '500') {
  12. [Dòng 205] if (data.name == "CARD_TYPE_CAN_NOT_BE_PROCESSED") { // in case response.status == 400
  13. [Dòng 212] } else if (data.state == "approved"){ // in case response.ok

!= (2 điều kiện):
  1. [Dòng 128] if (network != "napas") return true;
  2. [Dòng 129] if (currency != "VND") return false; // napas accept VND only

================================================================================

📁 FILE 133: google-pay-intergrate.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/assets/script/google-pay-intergrate.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 134: qrcode.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/assets/script/qrcode.js
📊 Thống kê: 67 điều kiện duy nhất
   - === : 2 lần
   - == : 53 lần
   - !== : 0 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 2255] if (typeof define === 'function'
  2. [Dòng 2257] } else if (typeof exports === 'object') {

== (53 điều kiện):
  1. [Dòng 68] if (_dataCache == null) {
  2. [Dòng 85] if ( (0 <= r && r <= 6 && (c == 0
  3. [Dòng 85] c == 6) )
  4. [Dòng 86] || (0 <= c && c <= 6 && (r == 0
  5. [Dòng 86] r == 6) )
  6. [Dòng 107] if (i == 0
  7. [Dòng 122] _modules[r][6] = (r % 2 == 0);
  8. [Dòng 129] _modules[6][c] = (c % 2 == 0);
  9. [Dòng 152] if (r == -2
  10. [Dòng 152] r == 2
  11. [Dòng 152] c == -2
  12. [Dòng 152] c == 2
  13. [Dòng 153] || (r == 0
  14. [Dòng 153] c == 0) ) {
  15. [Dòng 169] ( (bits >> i) & 1) == 1);
  16. [Dòng 226] if (col == 6) col -= 1;
  17. [Dòng 232] if (_modules[row][col - c] == null) {
  18. [Dòng 237] dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
  19. [Dòng 249] if (bitIndex == -1) {
  20. [Dòng 458] margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
  21. [Dòng 498] if (typeof arguments[0] == 'object') {
  22. [Dòng 587] margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
  23. [Dòng 733] if (b == -1) throw 'eof';
  24. [Dòng 741] if (b0 == -1) break;
  25. [Dòng 767] if (typeof b == 'number') {
  26. [Dòng 768] if ( (b & 0xff) == b) {
  27. [Dòng 910] return function(i, j) { return (i + j) % 2 == 0
  28. [Dòng 912] return function(i, j) { return i % 2 == 0
  29. [Dòng 914] return function(i, j) { return j % 3 == 0
  30. [Dòng 916] return function(i, j) { return (i + j) % 3 == 0
  31. [Dòng 918] return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
  32. [Dòng 920] return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
  33. [Dòng 922] return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
  34. [Dòng 924] return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
  35. [Dòng 1011] if (r == 0
  36. [Dòng 1011] c == 0) {
  37. [Dòng 1015] if (dark == qrcode.isDark(row + r, col + c) ) {
  38. [Dòng 1036] if (count == 0
  39. [Dòng 1036] count == 4) {
  40. [Dòng 1149] if (typeof num.length == 'undefined') {
  41. [Dòng 1155] num[offset] == 0) {
  42. [Dòng 1495] if (typeof rsBlock == 'undefined') {
  43. [Dòng 1538] return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
  44. [Dòng 1543] _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
  45. [Dòng 1599] if (data.length - i == 1) {
  46. [Dòng 1601] } else if (data.length - i == 2) {
  47. [Dòng 1866] } else if (n == 62) {
  48. [Dòng 1868] } else if (n == 63) {
  49. [Dòng 1928] if (_buflen == 0) {
  50. [Dòng 1937] if (c == '=') {
  51. [Dòng 1961] } else if (c == 0x2b) {
  52. [Dòng 1963] } else if (c == 0x2f) {
  53. [Dòng 2133] if (table.size() == (1 << bitLength) ) {

!= (12 điều kiện):
  1. [Dòng 119] if (_modules[r][6] != null) {
  2. [Dòng 126] if (_modules[6][c] != null) {
  3. [Dòng 144] if (_modules[row][col] != null) {
  4. [Dòng 365] while (buffer.getLengthInBits() % 8 != 0) {
  5. [Dòng 750] if (count != numChars) {
  6. [Dòng 751] throw count + ' != ' + numChars
  7. [Dòng 878] while (data != 0) {
  8. [Dòng 1733] if (test.length != 2
  9. [Dòng 1733] ( (test[0] << 8) | test[1]) != code) {
  10. [Dòng 1894] if (_length % 3 != 0) {
  11. [Dòng 2067] if ( (data >>> length) != 0) {
  12. [Dòng 2178] return typeof _map[key] != 'undefined'

================================================================================

📁 FILE 135: environment.development.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/environments/environment.development.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 136: environment.mtf.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/environments/environment.mtf.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 137: environment.prod.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/environments/environment.prod.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 138: environment.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/environments/environment.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 139: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/acc_bidv/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 140: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/acc_bidv/script.js
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 9] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 12] if (inName.value === "") return err("MISSING_FIELD"
  3. [Dòng 21] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 23] if (e !== null) {
  2. [Dòng 55] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 141: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/acc_oceanbank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 142: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/acc_oceanbank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 10] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 19] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 21] if (e !== null) {
  2. [Dòng 53] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 143: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/acc_pvcombank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 144: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/acc_pvcombank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 7] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 16] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 18] if (e !== null) {
  2. [Dòng 50] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 145: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/acc_shb/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 146: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/acc_shb/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 10] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 19] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 21] if (e !== null) {
  2. [Dòng 53] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 147: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/acc_tpbank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 148: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/acc_tpbank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 7] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 16] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 18] if (e !== null) {
  2. [Dòng 50] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 149: common.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/common.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 89] if (i % 2 === parity) d *= 2;
  2. [Dòng 93] return (sum % 10) === 0
  3. [Dòng 163] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 166] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 252] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 258] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 274] if (xhr.status === 200
  8. [Dòng 274] xhr.status === 201) {
  9. [Dòng 277] if (invoiceState === "unpaid"
  10. [Dòng 277] invoiceState === "not_paid") {
  11. [Dòng 282] if (paymentState === "authorization_required") {
  12. [Dòng 288] if (method === "REDIRECT") {
  13. [Dòng 291] } else if (method === "POST_REDIRECT") {
  14. [Dòng 330] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 141] this.oldValue !== this.value) {
  2. [Dòng 313] if (jLinks !== undefined
  3. [Dòng 313] jLinks !== null) {
  4. [Dòng 315] if (jMerchantReturn !== undefined
  5. [Dòng 315] jMerchantReturn !== null) {
  6. [Dòng 330] if (responseCode !== undefined
  7. [Dòng 330] responseCode !== null
  8. [Dòng 358] if (parentRes !== "{

================================================================================

📁 FILE 150: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/domestic_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 151: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/domestic_card/script.js
📊 Thống kê: 14 điều kiện duy nhất
   - === : 11 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 18] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 60] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 63] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 72] year === y
  5. [Dòng 74] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 106] if (inPhone.value === "") return err("MISSING_FIELD"
  7. [Dòng 136] if ("PAY" === operation) {
  8. [Dòng 525] } else if (value === "") {
  9. [Dòng 542] if (trPhone.style.display === "") {
  10. [Dòng 544] } else if (trName.style.display === "") {
  11. [Dòng 565] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 138] if (e !== null) {
  2. [Dòng 404] if (lang !== "vi") lang = "en";
  3. [Dòng 470] if (value !== "") {

================================================================================

📁 FILE 152: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/domestic_token/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 153: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/domestic_token/script.js
📊 Thống kê: 25 điều kiện duy nhất
   - === : 23 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (23 điều kiện):
  1. [Dòng 20] if ("PAY" === operation) {
  2. [Dòng 93] if (trName.style.display === "") {
  3. [Dòng 117] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  4. [Dòng 123] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  5. [Dòng 137] if (xhr.status === 200) {
  6. [Dòng 165] if (insType === "card") {
  7. [Dòng 166] if (insBrandId === "visa"
  8. [Dòng 166] insBrandId === "mastercard"
  9. [Dòng 166] insBrandId === "amex"
  10. [Dòng 166] insBrandId === "jcb"
  11. [Dòng 166] insBrandId === "cup") {
  12. [Dòng 170] } else if (insBrandId === "atm") {
  13. [Dòng 241] } else if (insType === "dongabank_account") {
  14. [Dòng 242] } else if (insType === "techcombank_account") {
  15. [Dòng 243] } else if (insType === "vib_account") {
  16. [Dòng 244] } else if (insType === "bidv_account") {
  17. [Dòng 245] } else if (insType === "tpbank_account") {
  18. [Dòng 246] } else if (insType === "shb_account") {
  19. [Dòng 247] } else if (insType === "shb_customer_id") {
  20. [Dòng 248] } else if (insType === "vpbank_account") {
  21. [Dòng 249] } else if (insType === "oceanbank_online_account") {
  22. [Dòng 250] } else if (insType === "oceanbank_mobile_account") {
  23. [Dòng 251] } else if (insType === "pvcombank_account") {

!== (2 điều kiện):
  1. [Dòng 54] if (lang !== "vi") lang = "en";
  2. [Dòng 273] if (parentRes !== "{

================================================================================

📁 FILE 154: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 155: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/international_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 156: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/international_card/script.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 13] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 23] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 26] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 38] year === y
  5. [Dòng 40] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 71] if ("PAY" === operation) {
  7. [Dòng 161] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 73] if (e !== null) {
  2. [Dòng 118] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 157: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/international_token/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 158: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/international_token/script.js
📊 Thống kê: 36 điều kiện duy nhất
   - === : 25 lần
   - == : 0 lần
   - !== : 11 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (25 điều kiện):
  1. [Dòng 32] year === y
  2. [Dòng 34] if (inCvv.value === "") {
  3. [Dòng 67] if ("PAY" === operation) {
  4. [Dòng 129] } else if (value === "") {
  5. [Dòng 190] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 196] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 209] if (xhr.status === 200) {
  8. [Dòng 245] if (insType === "card") {
  9. [Dòng 246] if (insBrandId === "visa"
  10. [Dòng 246] insBrandId === "mastercard"
  11. [Dòng 246] insBrandId === "amex"
  12. [Dòng 246] insBrandId === "jcb"
  13. [Dòng 246] insBrandId === "cup") {
  14. [Dòng 248] } else if (insBrandId === "atm") {
  15. [Dòng 319] } else if (insType === "dongabank_account") {
  16. [Dòng 320] } else if (insType === "techcombank_account") {
  17. [Dòng 321] } else if (insType === "vib_account") {
  18. [Dòng 322] } else if (insType === "bidv_account") {
  19. [Dòng 323] } else if (insType === "tpbank_account") {
  20. [Dòng 324] } else if (insType === "shb_account") {
  21. [Dòng 325] } else if (insType === "shb_customer_id") {
  22. [Dòng 326] } else if (insType === "vpbank_account") {
  23. [Dòng 327] } else if (insType === "oceanbank_online_account") {
  24. [Dòng 328] } else if (insType === "oceanbank_mobile_account") {
  25. [Dòng 329] } else if (insType === "pvcombank_account") {

!== (11 điều kiện):
  1. [Dòng 18] if (inMonth.value !== ""
  2. [Dòng 21] if (inYear.value !== ""
  3. [Dòng 23] var month = inMonth.value !== ""
  4. [Dòng 24] var year = parseInt("20" + (inYear.value !== ""
  5. [Dòng 35] if ("2D" !== order["vpc_VerType"]) return err("MISSING_FIELD"
  6. [Dòng 71] if (e !== null) {
  7. [Dòng 79] inMonth.value !== tokenInsMonth) instrument["month"] = inMonth.value;
  8. [Dòng 80] inYear.value !== tokenInsYear) instrument["year"] = inYear.value;
  9. [Dòng 81] if (inCvv.value !== "") instrument["cvv"] = inCvv.value;
  10. [Dòng 95] if (lang !== "vi") lang = "en";
  11. [Dòng 351] if (parentRes !== "{

================================================================================

📁 FILE 159: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/amway/script.js
📊 Thống kê: 54 điều kiện duy nhất
   - === : 41 lần
   - == : 1 lần
   - !== : 12 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (41 điều kiện):
  1. [Dòng 4] if ((cardno.length === 15
  2. [Dòng 4] cardno.length === 16
  3. [Dòng 4] cardno.length === 19) && isMode10(cardno) === true) {
  4. [Dòng 4] isMode10(cardno) === true) {
  5. [Dòng 23] if (i % 2 === parity) d *= 2;
  6. [Dòng 27] return (sum % 10) === 0
  7. [Dòng 48] if ("PAY" === operation) {
  8. [Dòng 69] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  9. [Dòng 75] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  10. [Dòng 91] if (xhr.status === 200
  11. [Dòng 91] xhr.status === 201) {
  12. [Dòng 94] if (invoiceState === "unpaid"
  13. [Dòng 94] invoiceState === "not_paid") {
  14. [Dòng 99] if (paymentState === "authorization_required") {
  15. [Dòng 105] if (method === "REDIRECT") {
  16. [Dòng 110] } else if (method === "POST_REDIRECT") {
  17. [Dòng 223] if (typeof queryParams[key] === "undefined") {
  18. [Dòng 226] } else if (typeof queryParams[key] === "string") {
  19. [Dòng 256] if (params["CardList"] === undefined
  20. [Dòng 256] params["CardList"] === null) return;
  21. [Dòng 302] if (xhr.status === 200) {
  22. [Dòng 312] if (insType === "card") {
  23. [Dòng 313] if (insBrandId === "visa"
  24. [Dòng 313] insBrandId === "mastercard"
  25. [Dòng 313] insBrandId === "amex"
  26. [Dòng 313] insBrandId === "jcb"
  27. [Dòng 313] insBrandId === "cup") {
  28. [Dòng 317] } else if (insBrandId === "atm") {
  29. [Dòng 318] if (insSwiftCode === "BFTVVNVX") { //Vietcombank
  30. [Dòng 322] } else if (insSwiftCode === "BIDVVNVX") { //BIDV
  31. [Dòng 329] } else if (insType === "dongabank_account") {
  32. [Dòng 331] } else if (insType === "techcombank_account") {
  33. [Dòng 333] } else if (insType === "vib_account") {
  34. [Dòng 335] } else if (insType === "bidv_account") {
  35. [Dòng 336] } else if (insType === "tpbank_account") {
  36. [Dòng 337] } else if (insType === "shb_account") {
  37. [Dòng 338] } else if (insType === "shb_customer_id") {
  38. [Dòng 339] } else if (insType === "vpbank_account") {
  39. [Dòng 340] } else if (insType === "oceanbank_online_account") {
  40. [Dòng 341] } else if (insType === "oceanbank_mobile_account") {
  41. [Dòng 342] } else if (insType === "pvcombank_account") {

== (1 điều kiện):
  1. [Dòng 270] if (cardList.length == 1) {//TOKEN, BIDV, SHB, OCEAN, PVCOM, TP Bank

!== (12 điều kiện):
  1. [Dòng 52] if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
  2. [Dòng 53] if (inNumber.style.display !== "none") instrument.number = inNumber.value;
  3. [Dòng 54] if (inDate.style.display !== "none") instrument.date = inDate.value;
  4. [Dòng 55] if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
  5. [Dòng 56] if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
  6. [Dòng 57] if (inName.style.display !== "none") instrument.name = inName.value;
  7. [Dòng 132] if (jLinks !== undefined
  8. [Dòng 132] jLinks !== null) {
  9. [Dòng 134] if (jMerchantReturn !== undefined
  10. [Dòng 134] jMerchantReturn !== null) {
  11. [Dòng 166] if (parentRes !== "{
  12. [Dòng 255] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 160: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 161: bidv.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Bidv/assets/js/bidv.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 233] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 239] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 255] if (xhr.status === 200
  8. [Dòng 255] xhr.status === 201) {
  9. [Dòng 258] if (invoiceState === "unpaid"
  10. [Dòng 258] invoiceState === "not_paid") {
  11. [Dòng 263] if (paymentState === "authorization_required") {
  12. [Dòng 269] if (method === "REDIRECT") {
  13. [Dòng 272] } else if (method === "POST_REDIRECT") {
  14. [Dòng 311] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 294] if (jLinks !== undefined
  3. [Dòng 294] jLinks !== null) {
  4. [Dòng 296] if (jMerchantReturn !== undefined
  5. [Dòng 296] jMerchantReturn !== null) {
  6. [Dòng 311] if (responseCode !== undefined
  7. [Dòng 311] responseCode !== null
  8. [Dòng 339] if (parentRes !== "{

================================================================================

📁 FILE 162: bidv2.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Bidv/bidv2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 76] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 88] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 78] if ( $('.circle_v1').css('display') == 'block'
  2. [Dòng 112] if ($('.circle_v2').css('display') == 'block'
  3. [Dòng 112] $('.circle_v3').css('display') == 'block' ) {
  4. [Dòng 302] $('.circle_v1').css('display') == 'block'

!== (3 điều kiện):
  1. [Dòng 90] if (e !== null) {
  2. [Dòng 273] if (lang !== "vi") lang = "en";
  3. [Dòng 305] if (value !== "") {

================================================================================

📁 FILE 163: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Bidv/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 164: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 165: ocean.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Ocean/assets/js/ocean.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 232] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 237] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 251] if (xhr.status === 200
  8. [Dòng 251] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 257] if (paymentState === "authorization_required") {
  12. [Dòng 261] if (method === "REDIRECT") {
  13. [Dòng 264] } else if (method === "POST_REDIRECT") {
  14. [Dòng 303] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 286] if (jLinks !== undefined
  3. [Dòng 286] jLinks !== null) {
  4. [Dòng 288] if (jMerchantReturn !== undefined
  5. [Dòng 288] jMerchantReturn !== null) {
  6. [Dòng 303] if (responseCode !== undefined
  7. [Dòng 303] responseCode !== null
  8. [Dòng 331] if (parentRes !== "{

================================================================================

📁 FILE 166: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Ocean/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 167: ocean2.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Ocean/ocean2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 72] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 92] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 74] document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM'
  2. [Dòng 116] if ( document.getElementsByClassName("select_online")[0].value == 'Easy Online Banking') {
  3. [Dòng 119] if ( document.getElementsByClassName("select_online")[0].value == 'Easy Mobile Banking') {
  4. [Dòng 304] if ( document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM') {

!== (3 điều kiện):
  1. [Dòng 94] if (e !== null) {
  2. [Dòng 276] if (lang !== "vi") lang = "en";
  3. [Dòng 306] if (value !== "") {

================================================================================

📁 FILE 168: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 169: pvbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Pv_Bank/assets/js/pvbank.js
📊 Thống kê: 23 điều kiện duy nhất
   - === : 14 lần
   - == : 1 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 239] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 261] if (xhr.status === 200
  8. [Dòng 261] xhr.status === 201) {
  9. [Dòng 264] if (invoiceState === "unpaid"
  10. [Dòng 264] invoiceState === "not_paid") {
  11. [Dòng 269] if (paymentState === "authorization_required") {
  12. [Dòng 275] if (method === "REDIRECT") {
  13. [Dòng 278] } else if (method === "POST_REDIRECT") {
  14. [Dòng 317] responseCode === "0") {

== (1 điều kiện):
  1. [Dòng 167] if ($('.circle_v1').css('display') == 'block') {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 300] if (jLinks !== undefined
  3. [Dòng 300] jLinks !== null) {
  4. [Dòng 302] if (jMerchantReturn !== undefined
  5. [Dòng 302] jMerchantReturn !== null) {
  6. [Dòng 317] if (responseCode !== undefined
  7. [Dòng 317] responseCode !== null
  8. [Dòng 345] if (parentRes !== "{

================================================================================

📁 FILE 170: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Pv_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 171: pvbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Pv_Bank/pvbank2.js
📊 Thống kê: 15 điều kiện duy nhất
   - === : 8 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 71] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 76] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 79] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 88] year === y
  5. [Dòng 90] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 100] if ("PAY" === operation) {
  7. [Dòng 347] } else if (value === "") {
  8. [Dòng 364] if (trName.style.display === "") {

== (4 điều kiện):
  1. [Dòng 73] if ($('.circle_v1').css('display') == 'block'
  2. [Dòng 75] $('.circle_v1').css('display') == 'block'
  3. [Dòng 124] if ( $('.circle_v1').css('display') == 'block') {
  4. [Dòng 129] else if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 102] if (e !== null) {
  2. [Dòng 286] if (lang !== "vi") lang = "en";
  3. [Dòng 316] if (value !== "") {

================================================================================

📁 FILE 172: Sea2.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/SeaBank/Sea2.js
📊 Thống kê: 11 điều kiện duy nhất
   - === : 8 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 23] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 31] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 34] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 43] year === y
  5. [Dòng 45] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 55] if ("PAY" === operation) {
  7. [Dòng 287] } else if (value === "") {
  8. [Dòng 304] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 57] if (e !== null) {
  2. [Dòng 233] if (lang !== "vi") lang = "en";
  3. [Dòng 263] if (value !== "") {

================================================================================

📁 FILE 173: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 174: Sea.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/SeaBank/assets/js/Sea.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 228] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 234] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 258] if (paymentState === "authorization_required") {
  12. [Dòng 264] if (method === "REDIRECT") {
  13. [Dòng 267] } else if (method === "POST_REDIRECT") {
  14. [Dòng 306] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 289] if (jLinks !== undefined
  3. [Dòng 289] jLinks !== null) {
  4. [Dòng 291] if (jMerchantReturn !== undefined
  5. [Dòng 291] jMerchantReturn !== null) {
  6. [Dòng 306] if (responseCode !== undefined
  7. [Dòng 306] responseCode !== null
  8. [Dòng 334] if (parentRes !== "{

================================================================================

📁 FILE 175: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/SeaBank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 176: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 177: shb.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Shb/assets/js/shb.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 234] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 240] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 256] if (xhr.status === 200
  8. [Dòng 256] xhr.status === 201) {
  9. [Dòng 259] if (invoiceState === "unpaid"
  10. [Dòng 259] invoiceState === "not_paid") {
  11. [Dòng 264] if (paymentState === "authorization_required") {
  12. [Dòng 270] if (method === "REDIRECT") {
  13. [Dòng 273] } else if (method === "POST_REDIRECT") {
  14. [Dòng 312] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 295] if (jLinks !== undefined
  3. [Dòng 295] jLinks !== null) {
  4. [Dòng 297] if (jMerchantReturn !== undefined
  5. [Dòng 297] jMerchantReturn !== null) {
  6. [Dòng 312] if (responseCode !== undefined
  7. [Dòng 312] responseCode !== null
  8. [Dòng 340] if (parentRes !== "{

================================================================================

📁 FILE 178: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Shb/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 179: shb2.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Shb/shb2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 73] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 85] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 74] if ($('.circle_v2').css('display') == 'block'
  2. [Dòng 110] if ($('.circle_v1').css('display') == 'block'
  3. [Dòng 114] if ( $('.circle_v3').css('display') == 'block' ) {
  4. [Dòng 299] if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 87] if (e !== null) {
  2. [Dòng 271] if (lang !== "vi") lang = "en";
  3. [Dòng 301] if (value !== "") {

================================================================================

📁 FILE 180: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 181: tpbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Tp_Bank/assets/js/tpbank.js
📊 Thống kê: 23 điều kiện duy nhất
   - === : 14 lần
   - == : 1 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 239] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 261] if (xhr.status === 200
  8. [Dòng 261] xhr.status === 201) {
  9. [Dòng 264] if (invoiceState === "unpaid"
  10. [Dòng 264] invoiceState === "not_paid") {
  11. [Dòng 269] if (paymentState === "authorization_required") {
  12. [Dòng 275] if (method === "REDIRECT") {
  13. [Dòng 278] } else if (method === "POST_REDIRECT") {
  14. [Dòng 317] responseCode === "0") {

== (1 điều kiện):
  1. [Dòng 167] if ($('.circle_v1').css('display') == 'block') {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 300] if (jLinks !== undefined
  3. [Dòng 300] jLinks !== null) {
  4. [Dòng 302] if (jMerchantReturn !== undefined
  5. [Dòng 302] jMerchantReturn !== null) {
  6. [Dòng 317] if (responseCode !== undefined
  7. [Dòng 317] responseCode !== null
  8. [Dòng 345] if (parentRes !== "{

================================================================================

📁 FILE 182: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Tp_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 183: tpbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Tp_Bank/tpbank2.js
📊 Thống kê: 15 điều kiện duy nhất
   - === : 8 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 71] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 78] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 81] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 90] year === y
  5. [Dòng 92] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 102] if ("PAY" === operation) {
  7. [Dòng 349] } else if (value === "") {
  8. [Dòng 366] if (trName.style.display === "") {

== (4 điều kiện):
  1. [Dòng 73] if ($('.circle_v1').css('display') == 'block'
  2. [Dòng 76] $('.circle_v1').css('display') == 'block'
  3. [Dòng 126] if ( $('.circle_v1').css('display') == 'block') {
  4. [Dòng 131] else if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 104] if (e !== null) {
  2. [Dòng 288] if (lang !== "vi") lang = "en";
  3. [Dòng 318] if (value !== "") {

================================================================================

📁 FILE 184: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 185: onepay.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Visa/assets/js/onepay.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 78] if (i % 2 === parity) d *= 2;
  2. [Dòng 82] return (sum % 10) === 0
  3. [Dòng 152] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 155] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 240] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 259] if (xhr.status === 200
  8. [Dòng 259] xhr.status === 201) {
  9. [Dòng 261] if (invoiceState === "unpaid"
  10. [Dòng 261] invoiceState === "not_paid") {
  11. [Dòng 265] if (paymentState === "authorization_required") {
  12. [Dòng 269] if (method === "REDIRECT") {
  13. [Dòng 272] } else if (method === "POST_REDIRECT") {
  14. [Dòng 311] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 130] this.oldValue !== this.value) {
  2. [Dòng 294] if (jLinks !== undefined
  3. [Dòng 294] jLinks !== null) {
  4. [Dòng 296] if (jMerchantReturn !== undefined
  5. [Dòng 296] jMerchantReturn !== null) {
  6. [Dòng 311] if (responseCode !== undefined
  7. [Dòng 311] responseCode !== null
  8. [Dòng 339] if (parentRes !== "{

================================================================================

📁 FILE 186: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Visa/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 187: index.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Visa/index.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 19] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 29] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 32] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 44] year === y
  5. [Dòng 46] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 77] if ("PAY" === operation) {
  7. [Dòng 168] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 79] if (e !== null) {
  2. [Dòng 125] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 188: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 189: vpbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Vp_Bank/assets/js/vpbank.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 230] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 236] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 252] if (xhr.status === 200
  8. [Dòng 252] xhr.status === 201) {
  9. [Dòng 255] if (invoiceState === "unpaid"
  10. [Dòng 255] invoiceState === "not_paid") {
  11. [Dòng 260] if (paymentState === "authorization_required") {
  12. [Dòng 266] if (method === "REDIRECT") {
  13. [Dòng 269] } else if (method === "POST_REDIRECT") {
  14. [Dòng 308] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 291] if (jLinks !== undefined
  3. [Dòng 291] jLinks !== null) {
  4. [Dòng 293] if (jMerchantReturn !== undefined
  5. [Dòng 293] jMerchantReturn !== null) {
  6. [Dòng 308] if (responseCode !== undefined
  7. [Dòng 308] responseCode !== null
  8. [Dòng 336] if (parentRes !== "{

================================================================================

📁 FILE 190: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Vp_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 191: vpbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/Vp_Bank/vpbank2.js
📊 Thống kê: 14 điều kiện duy nhất
   - === : 11 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 32] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 35] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 44] year === y
  5. [Dòng 46] if (inPhone.value === "") return err("MISSING_FIELD"
  6. [Dòng 49] if (inName.value === "") return err("MISSING_FIELD"
  7. [Dòng 59] if ("PAY" === operation) {
  8. [Dòng 292] } else if (value === "") {
  9. [Dòng 309] if (trPhone.style.display === "") {
  10. [Dòng 311] } else if (trName.style.display === "") {
  11. [Dòng 332] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 61] if (e !== null) {
  2. [Dòng 237] if (lang !== "vi") lang = "en";
  3. [Dòng 267] if (value !== "") {

================================================================================

📁 FILE 192: sha.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/assets/js/sha.js
📊 Thống kê: 49 điều kiện duy nhất
   - === : 38 lần
   - == : 0 lần
   - !== : 11 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (38 điều kiện):
  1. [Dòng 12] 1>t)throw Error("numRounds must a integer >= 1");if("SHA-1"===c)m=512,q=K,u=Z,f=160,r=function(a){return a.slice()};else if(0===c.lastIndexOf("SHA-",0))if(q=function(a,b){return L(a,b,c)
  2. [Dòng 12] c)},u=function(a,b,h,e){var k,f;if("SHA-224"===c
  3. [Dòng 12] "SHA-256"===c)k=(b+65>>>9<<4)+15,f=16;else if("SHA-384"===c
  4. [Dòng 12] "SHA-512"===c)k=(b+129>>>10<<
  5. [Dòng 13] c);if("SHA-224"===c)a=[e[0],e[1],e[2],e[3],e[4],e[5],e[6]];else if("SHA-256"===c)a=e;else if("SHA-384"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,e[4].b,e[5].a,e[5].b];else if("SHA-512"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,
  6. [Dòng 14] "SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)
  7. [Dòng 14] 0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
  8. [Dòng 14] return a},r=function(a){return a.slice()},"SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)||0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
  9. [Dòng 15] c)m=1152,f=224;else if("SHA3-256"===c)m=1088,f=256;else if("SHA3-384"===c)m=832,f=384;else if("SHA3-512"===c)m=576,f=512;else if("SHAKE128"===c)m=1344,f=-1,F=31,z=!0;else if("SHAKE256"===c)m=1088,f=-1,F=31,z=!0;else throw Error("Chosen SHA variant is not supported");u=function(a
  10. [Dòng 16] 0===64*l%e
  11. [Dòng 16] 5][l/5|0];g.push(a.b);if(32*g.length>=h)break;g.push(a.a);l+=1;0===64*l%e&&D(null,b)}return g}}else throw Error("Chosen SHA variant is not supported");d=M(a,g,x);l=A(c);this.setHMACKey=function(a,b,h){var k;if(!0===I)throw Error("HMAC key already set");if(!0===y)throw Error("Cannot set HMAC key after calling update");if(!0===z)throw Error("SHAKE is not supported for HMAC");g=(h
  12. [Dòng 17] f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
  13. [Dòng 17] )b.push(0);b[h]&=4294967040}for(a=0;a<=h;a+=1)v[a]=b[a]^909522486,w[a]=b[a]^1549556828;l=q(v,l);e=m;I=!0};this.update=function(a){var c,b,k,f=0,g=m>>>5;c=d(a,h,n);a=c.binLen;b=c.value;c=a>>>5;for(k=0;k<c;k+=g)f+m<=a&&(l=q(b.slice(k,k+g),l),f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
  14. [Dòng 18] for(g=1;g<t;g+=1)!0===z
  15. [Dòng 19] f);return k(m)};this.getHMAC=function(a,b){var k,g,d,p;if(!1===I)throw Error("Cannot call getHMAC without first setting HMAC key");d=N(b);switch(a){case "HEX":k=function(a){return O(a,f,x,d)};break;case "B64":k=function(a){return P(a,f,x,d)};break;case "BYTES":k=function(a){return Q(a,f,x)};break;case "ARRAYBUFFER":try{k=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}k=function(a){return R(a,f,x)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
  16. [Dòng 20] d=-1===b?3:0
  17. [Dòng 20] f=-1===b?3:0
  18. [Dòng 21] g=-1===b?3:0
  19. [Dòng 22] !0===c.hasOwnProperty("b64Pad")
  20. [Dòng 22] a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");
  21. [Dòng 23] u=-1===b?3:0
  22. [Dòng 23] l+=2){p=parseInt(a.substr(l,2),16);if(isNaN(p))throw Error("String of HEX type contains invalid characters");m=(l>>>1)+q;for(f=m>>>2;c.length<=f;)c.push(0);c[f]|=p<<8*(u+m%4*b)}return{value:c,binLen:4*g+d}};break;case "TEXT":c=function(c,h,d){var g,l,p=0,f,m,q,u,r,t;h=h||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(t=-1===
  23. [Dòng 24] m+=1){r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=l[m]<<8*(t+r%4*b);p+=1}else if("UTF16BE"===a
  24. [Dòng 24] "UTF16LE"===a)for(t=-1===b?2:0
  25. [Dòng 24] UTF16LE"===a
  26. [Dòng 24] 1===b
  27. [Dòng 25] !0===l
  28. [Dòng 25] !0===l&&(m=g&255,g=m<<8|g>>>8);r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=g<<8*(t+r%4*b);p+=2}return{value:h,binLen:8*p+d}};break;case "B64":c=function(a,c,d){var g=0,l,p,f,m,q,u,r,t;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");p=a.indexOf("=");a=a.replace(/\=/g
  29. [Dòng 25] t=-1===b?3:0
  30. [Dòng 26] q=-1===b?3:0
  31. [Dòng 27] m=-1===b?3:0
  32. [Dòng 32] c.b^a.b)}function A(c){var a=[],d;if("SHA-1"===c)a=[1732584193,4023233417,2562383102,271733878,3285377520];else if(0===c.lastIndexOf("SHA-",0))switch(a=
  33. [Dòng 34] new b(d[2],4271175723),new b(d[3],1595750129),new b(d[4],2917565137),new b(d[5],725511199),new b(d[6],4215389547),new b(d[7],327033209)];break;default:throw Error("Unknown SHA variant");}else if(0===c.lastIndexOf("SHA3-",0)
  34. [Dòng 34] 0===c.lastIndexOf("SHAKE",0))for(c=0
  35. [Dòng 36] a,k){var e,h,n,g,l,p,f,m,q,u,r,t,v,w,y,A,z,x,F,B,C,D,E=[],J;if("SHA-224"===k
  36. [Dòng 36] "SHA-256"===k)u=64,t=1,D=Number,v=G,w=la,y=H,A=ha,z=ja,x=da,F=fa,C=U,B=aa,J=d;else if("SHA-384"===k
  37. [Dòng 36] "SHA-512"===k)u=80,t=2,D=b,v=ma,w=na,y=oa,A=ia,z=ka,x=ea,F=ga,C=ca,B=ba,J=V;else throw Error("Unexpected error in SHA-2 implementation");k=a[0];e=a[1];h=a[2];n=a[3];g=a[4];l=a[5];p=a[6];f=a[7];for(r=0
  38. [Dòng 45] "function"===typeof define

!== (11 điều kiện):
  1. [Dòng 12] 'use strict';(function(Y){function C(c,a,b){var e=0,h=[],n=0,g,l,d,f,m,q,u,r,I=!1,v=[],w=[],t,y=!1,z=!1,x=-1;b=b||{};g=b.encoding||"UTF8";t=b.numRounds||1;if(t!==parseInt(t,10)
  2. [Dòng 18] 0!==f%32
  3. [Dòng 22] a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8
  4. [Dòng 23] }switch(c){case "HEX":c=function(a,c,d){var g=a.length,l,p,f,m,q,u;if(0!==g%2)throw Error("String of HEX type must be in byte increments");c=c||[0];d=d||0;q=d>>>3;u=-1===b?3:0;for(l=0
  5. [Dòng 24] 1!==b
  6. [Dòng 24] "UTF16LE"!==a
  7. [Dòng 25] "");if(-1!==p
  8. [Dòng 27] function(a,c,d){var g,l,p,f,m,q;c=c||[0];d=d||0;l=d>>>3;m=-1===b?3:0;q=new Uint8Array(a);for(g=0;g<a.byteLength;g+=1)f=g+l,p=f>>>2,c.length<=p&&c.push(0),c[p]|=q[g]<<8*(m+f%4*b);return{value:c,binLen:8*a.byteLength+d}};break;default:throw Error("format must be HEX, TEXT, B64, BYTES, or ARRAYBUFFER");}return c}function y(c,a){return c<<a|c>>>32-a}function S(c,a){return 32<a?(a-=32,new b(c.b<<a|c.a>>>32-a,c.a<<a|c.b>>>32-a)):0!==a?new b(c.a<<a|c.b>>>32-a,c.b<<a|c.a>>>32-a):c}function w(c,a){return c>>>
  9. [Dòng 37] a[7]);return a}function D(c,a){var d,e,h,n,g=[],l=[];if(null!==c)for(e=0
  10. [Dòng 45] define.amd?define(function(){return C}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=C),exports=C):Y.jsSHA=C})(this);
  11. [Dòng 45] return C}):"undefined"!==typeof exports?("undefined"!==typeof module

================================================================================

📁 FILE 193: sha256.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/assets/js/sha256.js
📊 Thống kê: 28 điều kiện duy nhất
   - === : 20 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (20 điều kiện):
  1. [Dòng 12] 1>u)throw Error("numRounds must a integer >= 1");if(0===c.lastIndexOf("SHA-",0))if(q=function(b,a){return A(b,a,c)
  2. [Dòng 12] c)},y=function(b,a,l,f){var g,e;if("SHA-224"===c
  3. [Dòng 12] "SHA-256"===c)g=(a+65>>>9<<4)+15,e=16;else throw Error("Unexpected error in SHA-2 implementation");for(
  4. [Dòng 13] c);if("SHA-224"===c)b=[f[0],f[1],f[2],f[3],f[4],f[5],f[6]];else if("SHA-256"===c)b=f;else throw Error("Unexpected error in SHA-2 implementation");return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
  5. [Dòng 13] "SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a
  6. [Dòng 13] return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
  7. [Dòng 14] if(!0===z)throw Error("Cannot set HMAC key after calling update");f=(g
  8. [Dòng 15] 5);g=a%h;z=!0};this.getHash=function(a,f){var d,h,k,q;if(!0===m)throw Error("Cannot call getHash after setting HMAC key");k=C(f);switch(a){case "HEX":d=function(a){return D(a,e,k)};break;case "B64":d=function(a){return E(a,e,k)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{h=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("format must be HEX, B64, BYTES, or ARRAYBUFFER");
  9. [Dòng 16] x(c));return d(q)};this.getHMAC=function(a,f){var d,k,t,u;if(!1===m)throw Error("Cannot call getHMAC without first setting HMAC key");t=C(f);switch(a){case "HEX":d=function(a){return D(a,e,t)};break;case "B64":d=function(a){return E(a,e,t)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{d=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
  10. [Dòng 18] !0===c.hasOwnProperty("b64Pad")
  11. [Dòng 20] h=(d>>>1)+q;for(e=h>>>2;b.length<=e;)b.push(0);b[e]|=k<<8*(3+h%4*-1)}return{value:b,binLen:4*f+c}};break;case "TEXT":d=function(c,b,d){var f,n,k=0,e,h,q,m,p,r;b=b||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(r=3
  12. [Dòng 21] q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=n[h]<<8*(r+p%4*-1);k+=1}else if("UTF16BE"===a
  13. [Dòng 21] "UTF16LE"===a)for(r=2
  14. [Dòng 21] UTF16LE"===a
  15. [Dòng 21] !0===n
  16. [Dòng 21] e+=1){f=c.charCodeAt(e);!0===n&&(h=f&255,f=h<<8|f>>>8);p=k+q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=f<<8*(r+p%4*-1);k+=2}return{value:b,binLen:8*k+d}};break;case "B64":d=function(a,b,c){var f=0,d,k,e,h,q,m,p;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");k=a.indexOf("=");a=a.replace(/\=/g
  17. [Dòng 25] 16)&65535)<<16|b&65535}function R(c,a,d,l,b){var g=(c&65535)+(a&65535)+(d&65535)+(l&65535)+(b&65535);return((c>>>16)+(a>>>16)+(d>>>16)+(l>>>16)+(b>>>16)+(g>>>16)&65535)<<16|g&65535}function x(c){var a=[],d;if(0===c.lastIndexOf("SHA-",0))switch(a=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428]
  18. [Dòng 26] new m,new m,new m,new m,new m,new m];break;case "SHA-512":a=[new m,new m,new m,new m,new m,new m,new m,new m];break;default:throw Error("Unknown SHA variant");}else throw Error("No SHA variants supported");return a}function A(c,a,d){var l,b,g,f,n,k,e,h,m,r,p,w,t,x,u,z,A,B,C,D,E,F,v=[],G;if("SHA-224"===d
  19. [Dòng 26] "SHA-256"===d)r=64,w=1,F=Number,t=P,x=Q,u=R,z=N,A=O,B=L,C=M,E=K,D=J,G=H;else throw Error("Unexpected error in SHA-2 implementation");d=a[0];l=a[1];b=a[2];g=a[3];f=a[4];n=a[5];k=a[6];e=a[7];for(p=
  20. [Dòng 29] "function"===typeof define

!== (8 điều kiện):
  1. [Dòng 12] 'use strict';(function(I){function w(c,a,d){var l=0,b=[],g=0,f,n,k,e,h,q,y,p,m=!1,t=[],r=[],u,z=!1;d=d||{};f=d.encoding||"UTF8";u=d.numRounds||1;if(u!==parseInt(u,10)
  2. [Dòng 18] l+=1)g[l]=c[l>>>2]>>>8*(3+l%4*-1)&255;return b}function C(c){var a={outputUpper:!1,b64Pad:"=",shakeLen:-1};c=c||{};a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");
  3. [Dòng 19] if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0;d<f;d+=2){k=parseInt(a.substr(d,2),16);if(isNaN(k))throw Error("String of HEX type contains invalid characters");
  4. [Dòng 19] if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0
  5. [Dòng 21] "UTF16LE"!==a
  6. [Dòng 22] "");if(-1!==k
  7. [Dòng 29] define.amd?define(function(){return w}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=w),exports=w):I.jsSHA=w})(this);
  8. [Dòng 29] return w}):"undefined"!==typeof exports?("undefined"!==typeof module

================================================================================

📁 FILE 194: slick.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/assets/libraries/slick/slick.js
📊 Thống kê: 182 điều kiện duy nhất
   - === : 126 lần
   - == : 5 lần
   - !== : 47 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (126 điều kiện):
  1. [Dòng 20] if (typeof define === 'function'
  2. [Dòng 206] if (typeof(index) === 'boolean') {
  3. [Dòng 215] if (typeof(index) === 'number') {
  4. [Dòng 216] if (index === 0
  5. [Dòng 216] _.$slides.length === 0) {
  6. [Dòng 224] if (addBefore === true) {
  7. [Dòng 249] if (_.options.slidesToShow === 1
  8. [Dòng 249] _.options.adaptiveHeight === true
  9. [Dòng 249] _.options.vertical === false) {
  10. [Dòng 264] if (_.options.rtl === true
  11. [Dòng 267] if (_.transformsEnabled === false) {
  12. [Dòng 268] if (_.options.vertical === false) {
  13. [Dòng 280] if (_.cssTransitions === false) {
  14. [Dòng 281] if (_.options.rtl === true) {
  15. [Dòng 355] typeof asNavFor === 'object' ) {
  16. [Dòng 371] if (_.options.fade === false) {
  17. [Dòng 414] if ( _.options.infinite === false ) {
  18. [Dòng 416] if ( _.direction === 1
  19. [Dòng 416] ( _.currentSlide + 1 ) === ( _.slideCount - 1 )) {
  20. [Dòng 420] else if ( _.direction === 0 ) {
  21. [Dòng 424] if ( _.currentSlide - 1 === 0 ) {
  22. [Dòng 442] if (_.options.arrows === true ) {
  23. [Dòng 487] if (_.options.dots === true
  24. [Dòng 524] _.$slideTrack = (_.slideCount === 0) ?
  25. [Dòng 532] if (_.options.centerMode === true
  26. [Dòng 532] _.options.swipeToSlide === true) {
  27. [Dòng 547] _.setSlideClasses(typeof _.currentSlide === 'number' ? _.currentSlide : 0);
  28. [Dòng 549] if (_.options.draggable === true) {
  29. [Dòng 602] if (_.respondTo === 'window') {
  30. [Dòng 604] } else if (_.respondTo === 'slider') {
  31. [Dòng 606] } else if (_.respondTo === 'min') {
  32. [Dòng 618] if (_.originalSettings.mobileFirst === false) {
  33. [Dòng 635] if (_.breakpointSettings[targetBreakpoint] === 'unslick') {
  34. [Dòng 641] if (initial === true) {
  35. [Dòng 705] slideOffset = indexOffset === 0 ? _.options.slidesToScroll : _.options.slidesToShow - indexOffset
  36. [Dòng 712] slideOffset = indexOffset === 0 ? _.options.slidesToScroll : indexOffset
  37. [Dòng 719] var index = event.data.index === 0 ? 0 :
  38. [Dòng 765] if (_.options.accessibility === true) {
  39. [Dòng 772] if (_.options.arrows === true
  40. [Dòng 797] if (_.options.focusOnSelect === true) {
  41. [Dòng 836] if (_.shouldClick === false) {
  42. [Dòng 1051] if (_.options.infinite === true) {
  43. [Dòng 1061] } else if (_.options.centerMode === true) {
  44. [Dòng 1094] if (_.options.vertical === true
  45. [Dòng 1094] _.options.centerMode === true) {
  46. [Dòng 1095] if (_.options.slidesToShow === 2) {
  47. [Dòng 1097] } else if (_.options.slidesToShow === 1) {
  48. [Dòng 1128] } else if (_.options.centerMode === true
  49. [Dòng 1128] _.options.infinite === true) {
  50. [Dòng 1141] if (_.options.variableWidth === true) {
  51. [Dòng 1143] _.options.infinite === false) {
  52. [Dòng 1159] if (_.options.centerMode === true) {
  53. [Dòng 1200] if (_.options.infinite === false) {
  54. [Dòng 1229] centerOffset = _.options.centerMode === true ? _.slideWidth * Math.floor(_.options.slidesToShow / 2) : 0
  55. [Dòng 1231] if (_.options.swipeToSlide === true) {
  56. [Dòng 1406] _.options.pauseOnDotsHover === true
  57. [Dòng 1498] if (event.keyCode === 37
  58. [Dòng 1498] _.options.accessibility === true) {
  59. [Dòng 1501] message: _.options.rtl === true ? 'next' :
  60. [Dòng 1504] } else if (event.keyCode === 39
  61. [Dòng 1507] message: _.options.rtl === true ? 'previous' : 'next'
  62. [Dòng 1585] if (_.options.fade === true) {
  63. [Dòng 1593] if (_.options.lazyLoad === 'anticipated') {
  64. [Dòng 1616] } else if (_.currentSlide === 0) {
  65. [Dòng 1637] if (_.options.lazyLoad === 'progressive') {
  66. [Dòng 1773] if ( _.options.adaptiveHeight === true ) {
  67. [Dòng 1864] if ( $.type(responsiveSettings) === 'array'
  68. [Dòng 1878] _.breakpoints[l] === currentBreakpoint ) {
  69. [Dòng 1969] index = removeBefore === true ? 0 : _.slideCount - 1
  70. [Dòng 1971] index = removeBefore === true ? --index : index
  71. [Dòng 1980] if (removeAll === true) {
  72. [Dòng 2050] if (_.options.vertical === false
  73. [Dòng 2050] _.options.variableWidth === false) {
  74. [Dòng 2054] } else if (_.options.variableWidth === true) {
  75. [Dòng 2062] if (_.options.variableWidth === false) _.$slideTrack.children('.slick-slide').width(_.slideWidth - offset);
  76. [Dòng 2128] if( $.type( arguments[0] ) === 'object' ) {
  77. [Dòng 2134] } else if ( $.type( arguments[0] ) === 'string' ) {
  78. [Dòng 2140] if ( arguments[0] === 'responsive'
  79. [Dòng 2140] $.type( arguments[1] ) === 'array' ) {
  80. [Dòng 2152] if ( type === 'single' ) {
  81. [Dòng 2157] } else if ( type === 'multiple' ) {
  82. [Dòng 2166] } else if ( type === 'responsive' ) {
  83. [Dòng 2181] if( _.options.responsive[l].breakpoint === value[item].breakpoint ) {
  84. [Dòng 2231] _.positionProp = _.options.vertical === true ? 'top' : 'left'
  85. [Dòng 2233] if (_.positionProp === 'top') {
  86. [Dòng 2242] if (_.options.useCSS === true) {
  87. [Dòng 2248] if ( typeof _.options.zIndex === 'number' ) {
  88. [Dòng 2261] if (bodyStyle.perspectiveProperty === undefined
  89. [Dòng 2261] bodyStyle.webkitPerspective === undefined) _.animType = false;
  90. [Dòng 2267] bodyStyle.MozPerspective === undefined) _.animType = false;
  91. [Dòng 2279] if (bodyStyle.msTransform === undefined) _.animType = false;
  92. [Dòng 2306] var evenCoef = _.options.slidesToShow % 2 === 0 ? 1 : 0
  93. [Dòng 2328] if (index === 0) {
  94. [Dòng 2334] } else if (index === _.slideCount - 1) {
  95. [Dòng 2366] indexOffset = _.options.infinite === true ? _.options.slidesToShow + index : index
  96. [Dòng 2388] if (_.options.lazyLoad === 'ondemand'
  97. [Dòng 2388] _.options.lazyLoad === 'anticipated') {
  98. [Dòng 2402] if (_.options.infinite === true
  99. [Dòng 2402] _.options.fade === false) {
  100. [Dòng 2479] if (_.animating === true
  101. [Dòng 2479] _.options.waitForAnimate === true) {
  102. [Dòng 2483] if (_.options.fade === true
  103. [Dòng 2483] _.currentSlide === index) {
  104. [Dòng 2487] if (sync === false) {
  105. [Dòng 2495] _.currentLeft = _.swipeLeft === null ? slideLeft : _.swipeLeft
  106. [Dòng 2497] if (_.options.infinite === false
  107. [Dòng 2497] _.options.centerMode === false
  108. [Dòng 2509] } else if (_.options.infinite === false
  109. [Dòng 2509] _.options.centerMode === true
  110. [Dòng 2627] return (_.options.rtl === false ? 'left' : 'right');
  111. [Dòng 2633] return (_.options.rtl === false ? 'right' : 'left');
  112. [Dòng 2635] if (_.options.verticalSwiping === true) {
  113. [Dòng 2664] if ( _.touchObject.curX === undefined ) {
  114. [Dòng 2668] if ( _.touchObject.edgeHit === true ) {
  115. [Dòng 2732] if ((_.options.swipe === false) || ('ontouchend' in document
  116. [Dòng 2732] _.options.swipe === false)) {
  117. [Dòng 2734] } else if (_.options.draggable === false
  118. [Dòng 2806] positionOffset = (_.options.rtl === false ? 1 : -1) * (_.touchObject.curX > _.touchObject.startX ? 1 : -1);
  119. [Dòng 2817] if ((_.currentSlide === 0
  120. [Dòng 2817] swipeDirection === 'right') || (_.currentSlide >= _.getDotCount()
  121. [Dòng 2817] swipeDirection === 'left')) {
  122. [Dòng 2832] _.options.touchMove === false) {
  123. [Dòng 2836] if (_.animating === true) {
  124. [Dòng 2926] if ( _.options.arrows === true
  125. [Dòng 2933] if (_.currentSlide === 0) {
  126. [Dòng 2938] _.options.centerMode === false) {

== (5 điều kiện):
  1. [Dòng 2007] x = _.positionProp == 'left' ? Math.ceil(position) + 'px' : '0px'
  2. [Dòng 2008] y = _.positionProp == 'top' ? Math.ceil(position) + 'px' : '0px'
  3. [Dòng 2368] if (_.options.slidesToShow == _.options.slidesToScroll
  4. [Dòng 3002] if (typeof opt == 'object'
  5. [Dòng 3002] typeof opt == 'undefined')

!== (47 điều kiện):
  1. [Dòng 22] } else if (typeof exports !== 'undefined') {
  2. [Dòng 155] if (typeof document.mozHidden !== 'undefined') {
  3. [Dòng 158] } else if (typeof document.webkitHidden !== 'undefined') {
  4. [Dòng 342] asNavFor !== null ) {
  5. [Dòng 355] if ( asNavFor !== null
  6. [Dòng 460] if (_.options.infinite !== true) {
  7. [Dòng 612] _.options.responsive !== null) {
  8. [Dòng 630] if (targetBreakpoint !== null) {
  9. [Dòng 631] if (_.activeBreakpoint !== null) {
  10. [Dòng 632] if (targetBreakpoint !== _.activeBreakpoint
  11. [Dòng 676] triggerBreakpoint !== false ) {
  12. [Dòng 699] unevenOffset = (_.slideCount % _.options.slidesToScroll !== 0);
  13. [Dòng 758] _.$dots !== null) {
  14. [Dòng 997] if (filter !== null) {
  15. [Dòng 1103] if (_.slideCount % _.options.slidesToScroll !== 0) {
  16. [Dòng 1314] if (_.$dots !== null) {
  17. [Dòng 1324] if (slideControlIndex !== -1) {
  18. [Dòng 1910] _.currentSlide !== 0) {
  19. [Dòng 1953] if ($(window).width() !== _.windowWidth) {
  20. [Dòng 2144] } else if ( typeof arguments[1] !== 'undefined' ) {
  21. [Dòng 2170] if( $.type( _.options.responsive ) !== 'array' ) {
  22. [Dòng 2239] if (bodyStyle.WebkitTransition !== undefined
  23. [Dòng 2240] bodyStyle.MozTransition !== undefined
  24. [Dòng 2241] bodyStyle.msTransition !== undefined) {
  25. [Dòng 2257] if (bodyStyle.OTransform !== undefined) {
  26. [Dòng 2263] if (bodyStyle.MozTransform !== undefined) {
  27. [Dòng 2269] if (bodyStyle.webkitTransform !== undefined) {
  28. [Dòng 2275] if (bodyStyle.msTransform !== undefined) {
  29. [Dòng 2281] if (bodyStyle.transform !== undefined
  30. [Dòng 2281] _.animType !== false) {
  31. [Dòng 2286] _.transformsEnabled = _.options.useTransform && (_.animType !== null
  32. [Dòng 2286] _.animType !== false);
  33. [Dòng 2500] if (dontAnimate !== true
  34. [Dòng 2567] if (dontAnimate !== true) {
  35. [Dòng 2717] if ( _.touchObject.startX !== _.touchObject.curX ) {
  36. [Dòng 2734] event.type.indexOf('mouse') !== -1) {
  37. [Dòng 2738] event.originalEvent.touches !== undefined ?
  38. [Dòng 2773] touches = event.originalEvent !== undefined ? event.originalEvent.touches : null
  39. [Dòng 2775] touches.length !== 1) {
  40. [Dòng 2781] _.touchObject.curX = touches !== undefined ? touches[0].pageX : event.clientX
  41. [Dòng 2782] _.touchObject.curY = touches !== undefined ? touches[0].pageY : event.clientY
  42. [Dòng 2801] if (event.originalEvent !== undefined
  43. [Dòng 2852] if (_.touchObject.fingerCount !== 1
  44. [Dòng 2857] event.originalEvent.touches !== undefined) {
  45. [Dòng 2861] _.touchObject.startX = _.touchObject.curX = touches !== undefined ? touches.pageX : event.clientX
  46. [Dòng 2862] _.touchObject.startY = _.touchObject.curY = touches !== undefined ? touches.pageY : event.clientY
  47. [Dòng 2872] if (_.$slidesCache !== null) {

!= (4 điều kiện):
  1. [Dòng 805] $('[draggable!=true]', _.$slideTrack).off('dragstart', _.preventDefault);
  2. [Dòng 1467] $('[draggable!=true]', _.$slideTrack).on('dragstart', _.preventDefault);
  3. [Dòng 2707] if( direction != 'vertical' ) {
  4. [Dòng 3006] if (typeof ret != 'undefined') return ret;

================================================================================

📁 FILE 195: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 196: atm_b1.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_1/assets/js/atm_b1.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 228] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 234] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 258] if (paymentState === "authorization_required") {
  12. [Dòng 264] if (method === "REDIRECT") {
  13. [Dòng 267] } else if (method === "POST_REDIRECT") {
  14. [Dòng 306] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 289] if (jLinks !== undefined
  3. [Dòng 289] jLinks !== null) {
  4. [Dòng 291] if (jMerchantReturn !== undefined
  5. [Dòng 291] jMerchantReturn !== null) {
  6. [Dòng 306] if (responseCode !== undefined
  7. [Dòng 306] responseCode !== null
  8. [Dòng 334] if (parentRes !== "{

================================================================================

📁 FILE 197: atm_b1_2.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_1/atm_b1_2.js
📊 Thống kê: 11 điều kiện duy nhất
   - === : 8 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 52] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 55] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 64] year === y
  5. [Dòng 66] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 75] if ("PAY" === operation) {
  7. [Dòng 319] } else if (value === "") {
  8. [Dòng 336] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 77] if (e !== null) {
  2. [Dòng 253] if (lang !== "vi") lang = "en";
  3. [Dòng 283] if (value !== "") {

================================================================================

📁 FILE 198: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_1/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 199: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 200: atm_b2.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_2/assets/js/atm_b2.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 231] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 236] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 252] if (invoiceState === "unpaid"
  10. [Dòng 252] invoiceState === "not_paid") {
  11. [Dòng 256] if (paymentState === "authorization_required") {
  12. [Dòng 260] if (method === "REDIRECT") {
  13. [Dòng 263] } else if (method === "POST_REDIRECT") {
  14. [Dòng 302] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 285] if (jLinks !== undefined
  3. [Dòng 285] jLinks !== null) {
  4. [Dòng 287] if (jMerchantReturn !== undefined
  5. [Dòng 287] jMerchantReturn !== null) {
  6. [Dòng 302] if (responseCode !== undefined
  7. [Dòng 302] responseCode !== null
  8. [Dòng 330] if (parentRes !== "{

================================================================================

📁 FILE 201: atm_b2_2.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_2/atm_b2_2.js
📊 Thống kê: 6 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 41] if (inName.value === "") return err("MISSING_FIELD"
  3. [Dòng 51] if ("PAY" === operation) {

!== (3 điều kiện):
  1. [Dòng 53] if (e !== null) {
  2. [Dòng 229] if (lang !== "vi") lang = "en";
  3. [Dòng 259] if (value !== "") {

================================================================================

📁 FILE 202: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/atm_bank_2/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 203: common.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/common.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 42] if (i % 2 === parity) d *= 2;
  2. [Dòng 46] return (sum % 10) === 0
  3. [Dòng 116] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 119] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 205] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 211] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 227] if (xhr.status === 200
  8. [Dòng 227] xhr.status === 201) {
  9. [Dòng 230] if (invoiceState === "unpaid"
  10. [Dòng 230] invoiceState === "not_paid") {
  11. [Dòng 235] if (paymentState === "authorization_required") {
  12. [Dòng 241] if (method === "REDIRECT") {
  13. [Dòng 244] } else if (method === "POST_REDIRECT") {
  14. [Dòng 283] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 94] this.oldValue !== this.value) {
  2. [Dòng 266] if (jLinks !== undefined
  3. [Dòng 266] jLinks !== null) {
  4. [Dòng 268] if (jMerchantReturn !== undefined
  5. [Dòng 268] jMerchantReturn !== null) {
  6. [Dòng 283] if (responseCode !== undefined
  7. [Dòng 283] responseCode !== null
  8. [Dòng 311] if (parentRes !== "{

================================================================================

📁 FILE 204: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 205: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/international_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 206: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/begodi/international_card/script.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 12] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 22] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 25] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 37] year === y
  5. [Dòng 39] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 70] if ("PAY" === operation) {
  7. [Dòng 144] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 72] if (e !== null) {
  2. [Dòng 117] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 207: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/iframe/index.html
📊 Thống kê: 35 điều kiện duy nhất
   - === : 21 lần
   - == : 0 lần
   - !== : 14 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (21 điều kiện):
  1. [Dòng 10] if ((cardno.length === 15
  2. [Dòng 10] cardno.length === 16
  3. [Dòng 10] cardno.length === 19) && isMode10(cardno) === true) {
  4. [Dòng 10] isMode10(cardno) === true) {
  5. [Dòng 29] if (i % 2 === parity) d *= 2;
  6. [Dòng 33] return (sum % 10) === 0
  7. [Dòng 54] if ("PAY" === operation) {
  8. [Dòng 75] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  9. [Dòng 81] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  10. [Dòng 97] if (xhr.status === 200
  11. [Dòng 97] xhr.status === 201) {
  12. [Dòng 100] if (invoiceState === "unpaid"
  13. [Dòng 100] invoiceState === "not_paid") {
  14. [Dòng 105] if (paymentState === "authorization_required") {
  15. [Dòng 111] if (method === "REDIRECT") {
  16. [Dòng 116] } else if (method === "POST_REDIRECT") {
  17. [Dòng 183] //Customizable =================================================================================================
  18. [Dòng 229] if (typeof queryParams[key] === "undefined") {
  19. [Dòng 232] } else if (typeof queryParams[key] === "string") {
  20. [Dòng 246] if (params["CardList"] === undefined
  21. [Dòng 246] params["CardList"] === null) return;

!== (14 điều kiện):
  1. [Dòng 40] //if (event.origin !== "http://example.com:8080") return;
  2. [Dòng 58] if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
  3. [Dòng 59] if (inNumber.style.display !== "none") instrument.number = inNumber.value;
  4. [Dòng 60] if (inDate.style.display !== "none") instrument.date = inDate.value;
  5. [Dòng 61] if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
  6. [Dòng 62] if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
  7. [Dòng 63] if (inName.style.display !== "none") instrument.name = inName.value;
  8. [Dòng 78] /*if (contentType !== my_expected_type) {
  9. [Dòng 138] if (jLinks !== undefined
  10. [Dòng 138] jLinks !== null) {
  11. [Dòng 140] if (jMerchantReturn !== undefined
  12. [Dòng 140] jMerchantReturn !== null) {
  13. [Dòng 172] if (parentRes !== "{
  14. [Dòng 245] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 208: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 209: karma.conf.js
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/karma.conf.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 210: main.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/main.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 211: polyfills.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/polyfills.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 212: test.ts
📍 Đường dẫn: /root/projects/onepay/paygate-bachhoaxanh/src/test.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📋 TÓM TẮT TẤT CẢ ĐIỀU KIỆN DUY NHẤT:
====================================================================================================

=== (500 điều kiện duy nhất):
------------------------------------------------------------
1. return lang === this._translate.currentLang
2. params.timeout === 'true') {
3. this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
4. _re.body.state === 'unpaid');
5. if (this.errorCode === 'overtime'
6. this.errorCode === '253') {
7. params.name === 'CUSTOMER_INTIME'
8. params.code === '09') {
9. if (this.timeLeft === 0) {
10. if (YY % 400 === 0
11. YY % 4 === 0)) {
12. if (YYYY % 400 === 0
13. YYYY % 4 === 0)) {
14. params.name === 'CUSTOMER_INTIME')) {
15. <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
16. [class.red_border_no_background]="c_expdate && (valueDate.length === 0
17. valueDate.trim().length === 0)"
18. if (target.tagName === 'A'
19. if (isIE[0] === 'MSIE'
20. +isIE[1] === 10) {
21. if ((_val.value.substr(-1) === ' '
22. _val.value.length === 24) {
23. if (this.cardTypeBank === 'bank_card_number') {
24. } else if (this.cardTypeBank === 'bank_account_number') {
25. } else if (this.cardTypeBank === 'bank_username') {
26. } else if (this.cardTypeBank === 'bank_customer_code') {
27. this.cardTypeBank === 'bank_card_number'
28. if (this.cardTypeOcean === 'IB') {
29. } else if (this.cardTypeOcean === 'MB') {
30. if (_val.value.substr(0, 2) === '84') {
31. } else if (this.cardTypeOcean === 'ATM') {
32. if (this.cardTypeBank === 'bank_account_number') {
33. this.cardTypeBank === 'bank_card_number') {
34. if (this.cardTypeBank === 'bank_card_number'
35. if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
36. if (event.keyCode === 8
37. event.key === "Backspace"
38. if (v.length === 2
39. this.flag.length === 3
40. this.flag.charAt(this.flag.length - 1) === '/') {
41. if (v.length === 1) {
42. } else if (v.length === 2) {
43. v.length === 2) {
44. if (len === 2) {
45. if ((this.cardTypeBank === 'bank_account_number'
46. this.cardTypeBank === 'bank_username'
47. this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
48. this.cardTypeOcean === 'ATM')
49. || (this.cardTypeOcean === 'IB'
50. if (valIn === this._translate.instant('bank_card_number')) {
51. } else if (valIn === this._translate.instant('bank_account_number')) {
52. } else if (valIn === this._translate.instant('bank_username')) {
53. } else if (valIn === this._translate.instant('bank_customer_code')) {
54. if (_val.value === ''
55. _val.value === null
56. _val.value === undefined) {
57. if (_val.value && (this.cardTypeBank === 'bank_card_number'
58. if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
59. this.cardTypeOcean === 'MB') {
60. this.cardTypeOcean === 'IB'
61. if ((this.cardTypeBank === 'bank_card_number'
62. if (this.cardName === undefined
63. this.cardName === '') {
64. if (this.valueDate === undefined
65. this.valueDate === '') {
66. c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
67. _inExpDate.trim().length === 0)"
68. if (this.timeLeft === 10) {
69. if (this.runTime === true) {
70. if (this.runTime === true) this.submitCardBanking();
71. if (approval.method === 'REDIRECT') {
72. } else if (approval.method === 'POST_REDIRECT') {
73. if (this.timeLeft === 1) {
74. } else if (valIn === this._translate.instant('internet_banking')) {
75. if (_val.value && (this.cardTypeBank === 'bank_card_number')) {
76. if (focusElement === 'card_name') {
77. } else if (focusElement === 'exp_date'
78. focusExpDateElement === 'card_name') {
79. if (this.cardTypeBank === 'bank_account_number'
80. filteredData.length === 0"
81. if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
82. filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
83. if (valOut === 'auth') {
84. if (this._b === '1'
85. this._b === '20'
86. this._b === '64') {
87. if (this._b === '36'
88. this._b === '18'
89. if (this._b === '19'
90. this._b === '16'
91. this._b === '25'
92. this._b === '33'
93. this._b === '39'
94. this._b === '11'
95. this._b === '17'
96. this._b === '36'
97. this._b === '44'
98. this._b === '64'
99. if (this._b === '20'
100. if (this._b === '18') {
101. if (_formCard.country === 'default') {
102. if ((v.substr(-1) === ' '
103. this._i_country_code === 'US') {
104. const insertIndex = this._i_country_code === 'US' ? 5 : 3
105. if (temp[i] === '-'
106. temp[i] === ' ') {
107. insertIndex === 3 ? ' ' : itemRemoved)
108. this.c_country = _val.value === 'default'
109. d_vrbank===true"
110. this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_mobile_wallet === 1
111. if (this._res.state === 'unpaid'
112. this._res.state === 'not_paid') {
113. if ('op' === auth
114. } else if ('bank' === auth
115. if (this.timeLeftPaypal === 0) {
116. if (GGPaySDKScript.readyState === "loaded"
117. GGPaySDKScript.readyState === "complete") {
118. isTop = window === window.top
119. return currency === 'VND'
120. filteredData.length === 0
121. filteredDataOther.length === 0"
122. filteredDataMobile.length === 0
123. filteredDataOtherMobile.length === 0"
124. this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
125. this.filteredDataOther = this.appList.filter(item => item.type === 'other');
126. this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
127. this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
128. item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
129. filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
130. if (item.type === 'mobile_banking') {
131. this.appList.length === 1
132. listVNPayQR.length === 0"
133. this.listWalletQR.length === 1) {
134. this.listWalletQR.length === 1
135. if (!this.d_vnpayqr_wallet && !this.d_vnpayqr_deeplink && (this.listWalletQR?.length === 1
136. this.listWalletDeeplink?.length === 1)) {
137. if (item.type === 'deeplink') {
138. this.listWalletQR?.length === 1
139. type === 2"
140. [ngStyle]="{'border-color': (type === 2
141. type === 2
142. type === '2'
143. type === 1"
144. [ngStyle]="{'border-color': (type === 1
145. type === 1
146. type === paymentType"
147. [ngStyle]="{'border-color': (type === paymentType
148. type === paymentType
149. type === 3"
150. [ngStyle]="{'border-color': (type === 3
151. type === 4"
152. [ngStyle]="{'border-color': (type === 4
153. type === 4
154. const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
155. item.method === method) : null;
156. err?.status === 400
157. err?.error?.name === 'INVALID_CARD_FEE'
158. if (M[1] === 'Chrome') {
159. if (param === key) {
160. pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
161. target === 0
162. if (cardTypeBank === 'bank_card_number') {
163. } else if (cardTypeBank === 'bank_account_number') {
164. if (typeof define === 'function'
165. } else if (typeof exports === 'object') {
166. if (number === "") return err("MISSING_FIELD"
167. if (inName.value === "") return err("MISSING_FIELD"
168. if ("PAY" === operation) {
169. if (i % 2 === parity) d *= 2;
170. return (sum % 10) === 0
171. if (typeof queryParams[key] === "undefined") {
172. } else if (typeof queryParams[key] === "string") {
173. if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
174. } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
175. if (xhr.status === 200
176. xhr.status === 201) {
177. if (invoiceState === "unpaid"
178. invoiceState === "not_paid") {
179. if (paymentState === "authorization_required") {
180. if (method === "REDIRECT") {
181. } else if (method === "POST_REDIRECT") {
182. responseCode === "0") {
183. if (inMonth.value === "") return err("MISSING_FIELD"
184. if (inYear.value === "") return err("MISSING_FIELD"
185. year === y
186. if (inPhone.value === "") return err("MISSING_FIELD"
187. } else if (value === "") {
188. if (trPhone.style.display === "") {
189. } else if (trName.style.display === "") {
190. if (trName.style.display === "") {
191. if (xhr.status === 200) {
192. if (insType === "card") {
193. if (insBrandId === "visa"
194. insBrandId === "mastercard"
195. insBrandId === "amex"
196. insBrandId === "jcb"
197. insBrandId === "cup") {
198. } else if (insBrandId === "atm") {
199. } else if (insType === "dongabank_account") {
200. } else if (insType === "techcombank_account") {
201. } else if (insType === "vib_account") {
202. } else if (insType === "bidv_account") {
203. } else if (insType === "tpbank_account") {
204. } else if (insType === "shb_account") {
205. } else if (insType === "shb_customer_id") {
206. } else if (insType === "vpbank_account") {
207. } else if (insType === "oceanbank_online_account") {
208. } else if (insType === "oceanbank_mobile_account") {
209. } else if (insType === "pvcombank_account") {
210. if (inCvv.value === "") return err("MISSING_FIELD"
211. if (inCvv.value === "") {
212. if ((cardno.length === 15
213. cardno.length === 16
214. cardno.length === 19) && isMode10(cardno) === true) {
215. isMode10(cardno) === true) {
216. if (params["CardList"] === undefined
217. params["CardList"] === null) return;
218. if (insSwiftCode === "BFTVVNVX") { //Vietcombank
219. } else if (insSwiftCode === "BIDVVNVX") { //BIDV
220. typeof exports === 'object'
221. typeof define === 'function'
222. selector === '#') {
223. if (typeof element.getRootNode === 'function') {
224. if (typeof $ === 'undefined') {
225. version[0] === minMajor
226. version[1] === minMinor
227. if (config === 'close') {
228. if (input.type === 'radio') {
229. } else if (input.type === 'checkbox') {
230. if (this._element.tagName === 'LABEL'
231. input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
232. if (config === 'toggle') {
233. if (_button.getAttribute('aria-pressed') === 'true') {
234. if (activeIndex === index) {
235. if (this._config.pause === 'hover') {
236. if (_this3._config.pause === 'hover') {
237. var isNextDirection = direction === Direction.NEXT
238. var isPrevDirection = direction === Direction.PREV
239. activeIndex === 0
240. activeIndex === lastItemIndex
241. var delta = direction === Direction.PREV ? -1 : 1
242. return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
243. if (direction === Direction.NEXT) {
244. if (typeof config === 'object') {
245. var action = typeof config === 'string' ? config : _config.slide
246. if (typeof config === 'number') {
247. } else if (typeof action === 'string') {
248. if (typeof data[action] === 'undefined') {
249. return foundElem === element
250. if (typeof _this._config.parent === 'string') {
251. return elem.getAttribute('data-parent') === _this._config.parent
252. if (actives.length === 0) {
253. typeof config === 'object'
254. if (typeof config === 'string') {
255. if (typeof data[config] === 'undefined') {
256. if (event.currentTarget.tagName === 'A') {
257. if (usePopper === void 0) {
258. if (typeof Popper === 'undefined') {
259. if (this._config.reference === 'parent') {
260. $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
261. if (typeof this._config.offset === 'function') {
262. if (this._config.display === 'static') {
263. var _config = typeof config === 'object' ? config : null
264. if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
265. event.type === 'keyup'
266. event.type === 'click') {
267. if (event && (event.type === 'click'
268. event.which === TAB_KEYCODE) && $.contains(parent
269. if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
270. event.which === ESCAPE_KEYCODE) {
271. if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
272. event.which === SPACE_KEYCODE)) {
273. if (event.which === ESCAPE_KEYCODE) {
274. if (items.length === 0) {
275. if (event.which === ARROW_UP_KEYCODE
276. if (event.which === ARROW_DOWN_KEYCODE
277. if (this._config.backdrop === 'static') {
278. $(_this5._element).has(event.target).length === 0) {
279. if (event.which === ESCAPE_KEYCODE$1) {
280. if (this.tagName === 'A'
281. this.tagName === 'AREA') {
282. if (unsafeHtml.length === 0) {
283. typeof sanitizeFn === 'function') {
284. if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
285. if (_ret === "continue") continue;
286. if ($(this.element).css('display') === 'none') {
287. var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
288. if (prevHoverState === HoverState.OUT) {
289. if (typeof content === 'object'
290. title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
291. if (typeof this.config.offset === 'function') {
292. if (this.config.container === false) {
293. if (trigger === 'click') {
294. var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
295. var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
296. context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
297. context._hoverState === HoverState.SHOW) {
298. if (context._hoverState === HoverState.SHOW) {
299. context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
300. if (context._hoverState === HoverState.OUT) {
301. if (typeof config.delay === 'number') {
302. if (typeof config.title === 'number') {
303. if (typeof config.content === 'number') {
304. var _config = typeof config === 'object'
305. if (typeof content === 'function') {
306. this._scrollElement = element.tagName === 'BODY' ? window : element
307. var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
308. var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
309. var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
310. return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
311. return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
312. var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
313. this._element.parentNode.nodeType === Node.ELEMENT_NODE
314. var itemSelector = listElement.nodeName === 'UL'
315. listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
316. var activeElements = container && (container.nodeName === 'UL'
317. container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
318. if (active.getAttribute('role') === 'tab') {
319. if (element.getAttribute('role') === 'tab') {
320. 1>t)throw Error("numRounds must a integer >= 1");if("SHA-1"===c)m=512,q=K,u=Z,f=160,r=function(a){return a.slice()};else if(0===c.lastIndexOf("SHA-",0))if(q=function(a,b){return L(a,b,c)
321. c)},u=function(a,b,h,e){var k,f;if("SHA-224"===c
322. "SHA-256"===c)k=(b+65>>>9<<4)+15,f=16;else if("SHA-384"===c
323. "SHA-512"===c)k=(b+129>>>10<<
324. c);if("SHA-224"===c)a=[e[0],e[1],e[2],e[3],e[4],e[5],e[6]];else if("SHA-256"===c)a=e;else if("SHA-384"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,e[4].b,e[5].a,e[5].b];else if("SHA-512"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,
325. "SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)
326. 0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
327. return a},r=function(a){return a.slice()},"SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)||0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
328. c)m=1152,f=224;else if("SHA3-256"===c)m=1088,f=256;else if("SHA3-384"===c)m=832,f=384;else if("SHA3-512"===c)m=576,f=512;else if("SHAKE128"===c)m=1344,f=-1,F=31,z=!0;else if("SHAKE256"===c)m=1088,f=-1,F=31,z=!0;else throw Error("Chosen SHA variant is not supported");u=function(a
329. 0===64*l%e
330. 5][l/5|0];g.push(a.b);if(32*g.length>=h)break;g.push(a.a);l+=1;0===64*l%e&&D(null,b)}return g}}else throw Error("Chosen SHA variant is not supported");d=M(a,g,x);l=A(c);this.setHMACKey=function(a,b,h){var k;if(!0===I)throw Error("HMAC key already set");if(!0===y)throw Error("Cannot set HMAC key after calling update");if(!0===z)throw Error("SHAKE is not supported for HMAC");g=(h
331. f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
332. )b.push(0);b[h]&=4294967040}for(a=0;a<=h;a+=1)v[a]=b[a]^909522486,w[a]=b[a]^1549556828;l=q(v,l);e=m;I=!0};this.update=function(a){var c,b,k,f=0,g=m>>>5;c=d(a,h,n);a=c.binLen;b=c.value;c=a>>>5;for(k=0;k<c;k+=g)f+m<=a&&(l=q(b.slice(k,k+g),l),f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
333. for(g=1;g<t;g+=1)!0===z
334. f);return k(m)};this.getHMAC=function(a,b){var k,g,d,p;if(!1===I)throw Error("Cannot call getHMAC without first setting HMAC key");d=N(b);switch(a){case "HEX":k=function(a){return O(a,f,x,d)};break;case "B64":k=function(a){return P(a,f,x,d)};break;case "BYTES":k=function(a){return Q(a,f,x)};break;case "ARRAYBUFFER":try{k=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}k=function(a){return R(a,f,x)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
335. d=-1===b?3:0
336. f=-1===b?3:0
337. g=-1===b?3:0
338. !0===c.hasOwnProperty("b64Pad")
339. a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");
340. u=-1===b?3:0
341. l+=2){p=parseInt(a.substr(l,2),16);if(isNaN(p))throw Error("String of HEX type contains invalid characters");m=(l>>>1)+q;for(f=m>>>2;c.length<=f;)c.push(0);c[f]|=p<<8*(u+m%4*b)}return{value:c,binLen:4*g+d}};break;case "TEXT":c=function(c,h,d){var g,l,p=0,f,m,q,u,r,t;h=h||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(t=-1===
342. m+=1){r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=l[m]<<8*(t+r%4*b);p+=1}else if("UTF16BE"===a
343. "UTF16LE"===a)for(t=-1===b?2:0
344. UTF16LE"===a
345. 1===b
346. !0===l
347. !0===l&&(m=g&255,g=m<<8|g>>>8);r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=g<<8*(t+r%4*b);p+=2}return{value:h,binLen:8*p+d}};break;case "B64":c=function(a,c,d){var g=0,l,p,f,m,q,u,r,t;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");p=a.indexOf("=");a=a.replace(/\=/g
348. t=-1===b?3:0
349. q=-1===b?3:0
350. m=-1===b?3:0
351. c.b^a.b)}function A(c){var a=[],d;if("SHA-1"===c)a=[1732584193,4023233417,2562383102,271733878,3285377520];else if(0===c.lastIndexOf("SHA-",0))switch(a=
352. new b(d[2],4271175723),new b(d[3],1595750129),new b(d[4],2917565137),new b(d[5],725511199),new b(d[6],4215389547),new b(d[7],327033209)];break;default:throw Error("Unknown SHA variant");}else if(0===c.lastIndexOf("SHA3-",0)
353. 0===c.lastIndexOf("SHAKE",0))for(c=0
354. a,k){var e,h,n,g,l,p,f,m,q,u,r,t,v,w,y,A,z,x,F,B,C,D,E=[],J;if("SHA-224"===k
355. "SHA-256"===k)u=64,t=1,D=Number,v=G,w=la,y=H,A=ha,z=ja,x=da,F=fa,C=U,B=aa,J=d;else if("SHA-384"===k
356. "SHA-512"===k)u=80,t=2,D=b,v=ma,w=na,y=oa,A=ia,z=ka,x=ea,F=ga,C=ca,B=ba,J=V;else throw Error("Unexpected error in SHA-2 implementation");k=a[0];e=a[1];h=a[2];n=a[3];g=a[4];l=a[5];p=a[6];f=a[7];for(r=0
357. "function"===typeof define
358. 1>u)throw Error("numRounds must a integer >= 1");if(0===c.lastIndexOf("SHA-",0))if(q=function(b,a){return A(b,a,c)
359. c)},y=function(b,a,l,f){var g,e;if("SHA-224"===c
360. "SHA-256"===c)g=(a+65>>>9<<4)+15,e=16;else throw Error("Unexpected error in SHA-2 implementation");for(
361. c);if("SHA-224"===c)b=[f[0],f[1],f[2],f[3],f[4],f[5],f[6]];else if("SHA-256"===c)b=f;else throw Error("Unexpected error in SHA-2 implementation");return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
362. "SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a
363. return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
364. if(!0===z)throw Error("Cannot set HMAC key after calling update");f=(g
365. 5);g=a%h;z=!0};this.getHash=function(a,f){var d,h,k,q;if(!0===m)throw Error("Cannot call getHash after setting HMAC key");k=C(f);switch(a){case "HEX":d=function(a){return D(a,e,k)};break;case "B64":d=function(a){return E(a,e,k)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{h=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("format must be HEX, B64, BYTES, or ARRAYBUFFER");
366. x(c));return d(q)};this.getHMAC=function(a,f){var d,k,t,u;if(!1===m)throw Error("Cannot call getHMAC without first setting HMAC key");t=C(f);switch(a){case "HEX":d=function(a){return D(a,e,t)};break;case "B64":d=function(a){return E(a,e,t)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{d=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
367. h=(d>>>1)+q;for(e=h>>>2;b.length<=e;)b.push(0);b[e]|=k<<8*(3+h%4*-1)}return{value:b,binLen:4*f+c}};break;case "TEXT":d=function(c,b,d){var f,n,k=0,e,h,q,m,p,r;b=b||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(r=3
368. q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=n[h]<<8*(r+p%4*-1);k+=1}else if("UTF16BE"===a
369. "UTF16LE"===a)for(r=2
370. !0===n
371. e+=1){f=c.charCodeAt(e);!0===n&&(h=f&255,f=h<<8|f>>>8);p=k+q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=f<<8*(r+p%4*-1);k+=2}return{value:b,binLen:8*k+d}};break;case "B64":d=function(a,b,c){var f=0,d,k,e,h,q,m,p;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");k=a.indexOf("=");a=a.replace(/\=/g
372. 16)&65535)<<16|b&65535}function R(c,a,d,l,b){var g=(c&65535)+(a&65535)+(d&65535)+(l&65535)+(b&65535);return((c>>>16)+(a>>>16)+(d>>>16)+(l>>>16)+(b>>>16)+(g>>>16)&65535)<<16|g&65535}function x(c){var a=[],d;if(0===c.lastIndexOf("SHA-",0))switch(a=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428]
373. new m,new m,new m,new m,new m,new m];break;case "SHA-512":a=[new m,new m,new m,new m,new m,new m,new m,new m];break;default:throw Error("Unknown SHA variant");}else throw Error("No SHA variants supported");return a}function A(c,a,d){var l,b,g,f,n,k,e,h,m,r,p,w,t,x,u,z,A,B,C,D,E,F,v=[],G;if("SHA-224"===d
374. "SHA-256"===d)r=64,w=1,F=Number,t=P,x=Q,u=R,z=N,A=O,B=L,C=M,E=K,D=J,G=H;else throw Error("Unexpected error in SHA-2 implementation");d=a[0];l=a[1];b=a[2];g=a[3];f=a[4];n=a[5];k=a[6];e=a[7];for(p=
375. if (typeof(index) === 'boolean') {
376. if (typeof(index) === 'number') {
377. if (index === 0
378. _.$slides.length === 0) {
379. if (addBefore === true) {
380. if (_.options.slidesToShow === 1
381. _.options.adaptiveHeight === true
382. _.options.vertical === false) {
383. if (_.options.rtl === true
384. if (_.transformsEnabled === false) {
385. if (_.options.vertical === false) {
386. if (_.cssTransitions === false) {
387. if (_.options.rtl === true) {
388. typeof asNavFor === 'object' ) {
389. if (_.options.fade === false) {
390. if ( _.options.infinite === false ) {
391. if ( _.direction === 1
392. ( _.currentSlide + 1 ) === ( _.slideCount - 1 )) {
393. else if ( _.direction === 0 ) {
394. if ( _.currentSlide - 1 === 0 ) {
395. if (_.options.arrows === true ) {
396. if (_.options.dots === true
397. _.$slideTrack = (_.slideCount === 0) ?
398. if (_.options.centerMode === true
399. _.options.swipeToSlide === true) {
400. _.setSlideClasses(typeof _.currentSlide === 'number' ? _.currentSlide : 0);
401. if (_.options.draggable === true) {
402. if (_.respondTo === 'window') {
403. } else if (_.respondTo === 'slider') {
404. } else if (_.respondTo === 'min') {
405. if (_.originalSettings.mobileFirst === false) {
406. if (_.breakpointSettings[targetBreakpoint] === 'unslick') {
407. if (initial === true) {
408. slideOffset = indexOffset === 0 ? _.options.slidesToScroll : _.options.slidesToShow - indexOffset
409. slideOffset = indexOffset === 0 ? _.options.slidesToScroll : indexOffset
410. var index = event.data.index === 0 ? 0 :
411. if (_.options.accessibility === true) {
412. if (_.options.arrows === true
413. if (_.options.focusOnSelect === true) {
414. if (_.shouldClick === false) {
415. if (_.options.infinite === true) {
416. } else if (_.options.centerMode === true) {
417. if (_.options.vertical === true
418. _.options.centerMode === true) {
419. if (_.options.slidesToShow === 2) {
420. } else if (_.options.slidesToShow === 1) {
421. } else if (_.options.centerMode === true
422. _.options.infinite === true) {
423. if (_.options.variableWidth === true) {
424. _.options.infinite === false) {
425. if (_.options.centerMode === true) {
426. if (_.options.infinite === false) {
427. centerOffset = _.options.centerMode === true ? _.slideWidth * Math.floor(_.options.slidesToShow / 2) : 0
428. if (_.options.swipeToSlide === true) {
429. _.options.pauseOnDotsHover === true
430. if (event.keyCode === 37
431. _.options.accessibility === true) {
432. message: _.options.rtl === true ? 'next' :
433. } else if (event.keyCode === 39
434. message: _.options.rtl === true ? 'previous' : 'next'
435. if (_.options.fade === true) {
436. if (_.options.lazyLoad === 'anticipated') {
437. } else if (_.currentSlide === 0) {
438. if (_.options.lazyLoad === 'progressive') {
439. if ( _.options.adaptiveHeight === true ) {
440. if ( $.type(responsiveSettings) === 'array'
441. _.breakpoints[l] === currentBreakpoint ) {
442. index = removeBefore === true ? 0 : _.slideCount - 1
443. index = removeBefore === true ? --index : index
444. if (removeAll === true) {
445. if (_.options.vertical === false
446. _.options.variableWidth === false) {
447. } else if (_.options.variableWidth === true) {
448. if (_.options.variableWidth === false) _.$slideTrack.children('.slick-slide').width(_.slideWidth - offset);
449. if( $.type( arguments[0] ) === 'object' ) {
450. } else if ( $.type( arguments[0] ) === 'string' ) {
451. if ( arguments[0] === 'responsive'
452. $.type( arguments[1] ) === 'array' ) {
453. if ( type === 'single' ) {
454. } else if ( type === 'multiple' ) {
455. } else if ( type === 'responsive' ) {
456. if( _.options.responsive[l].breakpoint === value[item].breakpoint ) {
457. _.positionProp = _.options.vertical === true ? 'top' : 'left'
458. if (_.positionProp === 'top') {
459. if (_.options.useCSS === true) {
460. if ( typeof _.options.zIndex === 'number' ) {
461. if (bodyStyle.perspectiveProperty === undefined
462. bodyStyle.webkitPerspective === undefined) _.animType = false;
463. bodyStyle.MozPerspective === undefined) _.animType = false;
464. if (bodyStyle.msTransform === undefined) _.animType = false;
465. var evenCoef = _.options.slidesToShow % 2 === 0 ? 1 : 0
466. if (index === 0) {
467. } else if (index === _.slideCount - 1) {
468. indexOffset = _.options.infinite === true ? _.options.slidesToShow + index : index
469. if (_.options.lazyLoad === 'ondemand'
470. _.options.lazyLoad === 'anticipated') {
471. if (_.options.infinite === true
472. _.options.fade === false) {
473. if (_.animating === true
474. _.options.waitForAnimate === true) {
475. if (_.options.fade === true
476. _.currentSlide === index) {
477. if (sync === false) {
478. _.currentLeft = _.swipeLeft === null ? slideLeft : _.swipeLeft
479. if (_.options.infinite === false
480. _.options.centerMode === false
481. } else if (_.options.infinite === false
482. _.options.centerMode === true
483. return (_.options.rtl === false ? 'left' : 'right');
484. return (_.options.rtl === false ? 'right' : 'left');
485. if (_.options.verticalSwiping === true) {
486. if ( _.touchObject.curX === undefined ) {
487. if ( _.touchObject.edgeHit === true ) {
488. if ((_.options.swipe === false) || ('ontouchend' in document
489. _.options.swipe === false)) {
490. } else if (_.options.draggable === false
491. positionOffset = (_.options.rtl === false ? 1 : -1) * (_.touchObject.curX > _.touchObject.startX ? 1 : -1);
492. if ((_.currentSlide === 0
493. swipeDirection === 'right') || (_.currentSlide >= _.getDotCount()
494. swipeDirection === 'left')) {
495. _.options.touchMove === false) {
496. if (_.animating === true) {
497. if ( _.options.arrows === true
498. if (_.currentSlide === 0) {
499. _.options.centerMode === false) {
500. //Customizable =================================================================================================

== (544 điều kiện duy nhất):
------------------------------------------------------------
1. 'vi' == params['locale']) {
2. 'en' == params['locale']) {
3. errorCode == '11'"
4. isAppleError ==false
5. isAppleError==true
6. <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
7. (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
8. *ngIf="!(errorCode == 'overtime'
9. errorCode == '253'
10. if (_re.body.currencies[0] == 'USD') {
11. if (this.paymentInformation.type == "applepay_napas") {
12. if (this.paymentInformation.type == "applepay") {
13. } else if (this.paymentInformation.type == "googlepay") {
14. } else if (this.paymentInformation.type == "samsungpay") {
15. _re.body.themes.theme == 'general') {
16. params.response_code == 'overtime') {
17. if (_re.status == '200'
18. _re.status == '201') {
19. if (_re2.status == '200'
20. _re2.status == '201') {
21. if (this.errorCode == 'overtime'
22. this.errorCode == '253') {
23. } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
24. _re.body.state == 'canceled') {
25. if (lastPayment?.state == 'pending') {
26. if (this.isTimePause == false) {
27. if ((dataPassed.status == '200'
28. dataPassed.status == '201') && dataPassed.body != null) {
29. dataPassed.body.themes.logo_full == 'True') {
30. if (this.locale == 'en') {
31. if (name == 'MAFC')
32. if (bankId == 3
33. bankId == 61
34. bankId == 8
35. bankId == 49
36. bankId == 48
37. bankId == 10
38. bankId == 53
39. bankId == 17
40. bankId == 65
41. bankId == 23
42. bankId == 52
43. bankId == 27
44. bankId == 66
45. bankId == 9
46. bankId == 54
47. bankId == 37
48. bankId == 38
49. bankId == 39
50. bankId == 40
51. bankId == 42
52. bankId == 44
53. bankId == 72
54. bankId == 59
55. bankId == 51
56. bankId == 64
57. bankId == 58
58. bankId == 56
59. bankId == 55
60. bankId == 60
61. bankId == 68
62. bankId == 74
63. bankId == 75
64. if (this._b == 18
65. this._b == 19) {
66. if (this._b == 19) {//19BIDV
67. } else if (this._b == 3
68. this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
69. if (this._b == 27) {
70. } else if (this._b == 12) {// 12SHB
71. } else if (this._b == 18) { //18Oceanbank-ocb
72. if (this._b == 19
73. this._b == 3
74. this._b == 27
75. this._b == 12) {
76. } else if (this._b == 18) {
77. if (this.checkBin(_val.value) && (this._b == 3
78. this._b == 27)) {
79. if (this._b == 3) {
80. this.cardTypeOcean == 'ATM') {
81. if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
82. this._b == 18)) {
83. if (this.checkBin(v) && (this._b == 3
84. event.inputType == 'deleteContentBackward') {
85. if (event.target.name == 'exp_date'
86. event.inputType == 'insertCompositionText') {
87. if (((this.valueDate.length == 4
88. this.valueDate.search('/') == -1) || this.valueDate.length == 5)
89. this.valueDate.length == 5)
90. if (temp.length == 0) {
91. return (counter % 10 == 0);
92. } else if (this._b == 19) {
93. } else if (this._b == 27) {
94. if (this._b == 12) {
95. if (this.cardTypeBank == 'bank_customer_code') {
96. } else if (this.cardTypeBank == 'bank_account_number') {
97. _formCard.exp_date.length == 5
98. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
99. this._b == 3)) {//27-pvcombank;3-TPB ;
100. if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
101. this._b == 19
102. this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
103. if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
104. if (this.cardTypeOcean == 'IB') {
105. } else if (this.cardTypeOcean == 'MB') {
106. } else if (this.cardTypeOcean == 'ATM') {
107. if (this._res_post.state == 'approved'
108. this._res_post.state == 'failed') {
109. } else if (this._res_post.state == 'authorization_required') {
110. if (this._b == 18) {
111. if (this._b == 27
112. this._b == 18) {
113. if ((cardNo.length == 16
114. if ((cardNo.length == 16 || (cardNo.length == 19
115. && ((this._b == 18
116. cardNo.length == 19) || this._b != 18)
117. if (this._b == +e.id) {
118. if (valIn == 1) {
119. } else if (valIn == 2) {
120. this._b == 3) {
121. if (this._b == 19) {
122. if (cardType == this._translate.instant('internetbanking')
123. } else if (cardType == this._translate.instant('mobilebanking')
124. } else if (cardType == this._translate.instant('atm')
125. this._b == 18))) {
126. } else if (this._b == 18
127. this.c_expdate = !(((this.valueDate.length == 4
128. this.valueDate.length == 4
129. this.valueDate.search('/') == -1)
130. this.valueDate.length == 5))
131. <div [ngStyle]="{'margin-top': !(checkTwoEnabled && !isOffTechcombankNapas && !isOffTechcombank) ? '15px' : '0px' }" *ngIf="(cardTypeBank == 'bank_card_number') &&(_b == 67
132. (cardTypeBank == 'bank_card_number') &&(_b == 67 || checkTwoEnabled)&& ready===1 && !isOffTechcombankNapas
133. <div [ngStyle]="{'margin-top': !(checkTwoEnabled && !isOffTechcombankNapas && !isOffTechcombank) ? '15px' : '0px' }" class="nd-bank-card-two" *ngIf="(cardTypeBank == 'internet_banking')&&(_b == 2
134. if (this._b == 67
135. this._b == 2) {//19BIDV
136. if((this._b == 2
137. !this.checkTwoEnabled) || (this._b == 2
138. } else if (this._b == 2
139. if(this._b == 67 ){
140. return this._b == 2
141. this._b == 67
142. _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
143. this.checkMod10(cardNo) == true
144. if (this._b != 68 || (this._b == 68
145. return ((value.length == 4
146. value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
147. value.length == 5) && parseInt(value.split('/')[0]
148. || (this._util.checkValidExpireMonthYear(value) && (this._b == 2
149. this._b == 20
150. this._b == 33
151. this._b == 39
152. this._b == 43
153. this._b == 45
154. this._b == 64
155. this._b == 68
156. this._b == 72))) //sonnh them Vietbank 72
157. this._inExpDate.length == 4
158. this._inExpDate.search('/') == -1)
159. this._inExpDate.length == 5))
160. || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 20
161. this._b == 2
162. this._b == 72)));
163. if (this._b == 8) {//MB Bank
164. if (this._b == 18) {//Oceanbank
165. if (this._b == 8) {
166. if (this._b == 12) { //SHB
167. } else if (this._res.state == 'authorization_required') {
168. if (this.challengeCode == '') {
169. if (this._b == 18) {//8-MB Bank;18-oceanbank
170. <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_card_number')">
171. <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_account_number')">
172. if(this._b == 12) this.isShbGroup = true;
173. return this._b == 9
174. this._b == 11
175. this._b == 16
176. this._b == 17
177. this._b == 25
178. this._b == 44
179. this._b == 57
180. this._b == 59
181. this._b == 61
182. this._b == 63
183. this._b == 69
184. if (this._b == 12
185. this.cardTypeBank == 'bank_account_number') {
186. cardNo.length == 19) && cardNo.startsWith("970443") && this._util.checkMod10(cardNo) && this.checkBin(cardNo) ) {
187. if (this._b == 2
188. this._b == 31) {
189. if (this._b == 2) {
190. } else if (this._b == 6) {
191. } else if (this._b == 31) {
192. this._b == 5 ) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
193. this._b == 5) {
194. this._b == 12
195. this._b == 5)) {
196. if (this._b == 5) {
197. this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
198. } else if (this._b == 5) {
199. this._b == 5)) {//27-pvcombank;3-TPB ;
200. this._b == 5)) {//3-TPB ;19-BIDV;27-pvcombank;
201. this._b == 18
202. if(this._b == 5) {
203. _b == 68"
204. if (this._b == 1
205. this._b == 36
206. this._b == 55
207. this._b == 47
208. this._b == 48
209. this._b == 59) {
210. this._b == 14
211. this._b == 15
212. this._b == 24
213. this._b == 8
214. this._b == 10
215. this._b == 22
216. this._b == 23
217. this._b == 30
218. this._b == 9) {
219. (cardNo.length == 19
220. (cardNo.length == 19 && (this._b == 1
221. this._b == 4
222. this._b == 59))
223. this._util.checkMod10(cardNo) == true
224. this._b == 72
225. this._b == 73
226. this._b == 74
227. this._b == 75
228. (!token&&_auth==0&& vietcombankGroupSelected )|| (token && _b == 16)
229. _b == 16)">
230. _auth==0 && techcombankGroupSelected
231. _auth==0 && shbGroupSelected
232. _auth==0 && onepaynapasGroupSelected
233. _auth==0 && bankaccountGroupSelected
234. _auth==0 && vibbankGroupSelected
235. (token || _auth==1) && _b != 16
236. this._auth == 0) {
237. if (item.b.id == '2'
238. item.b.id == '67') {
239. $event == 'true') {
240. _b: this._b == '2' ? '67' : this._b
241. if (bankid == 2
242. bankid == 67) {
243. || (off && !this.enabledTwoBankTech && ((bankid == '2'
244. this.isOffTechcombank) || (bankid == '67'
245. if (bankId == 1
246. bankId == 4
247. bankId == 7
248. bankId == 11
249. bankId == 14
250. bankId == 15
251. bankId == 16
252. bankId == 20
253. bankId == 22
254. bankId == 24
255. bankId == 25
256. bankId == 30
257. bankId == 33
258. bankId == 34
259. bankId == 35
260. bankId == 36
261. bankId == 41
262. bankId == 43
263. bankId == 45
264. bankId == 46
265. bankId == 47
266. bankId == 50
267. bankId == 57
268. bankId == 62
269. bankId == 63
270. bankId == 69
271. bankId == 70
272. bankId == 71
273. bankId == 73
274. bankId == 32
275. bankId == 75) {
276. } else if (bankId == 6
277. bankId == 31) {
278. } else if (bankId == 2
279. bankId == 67) {
280. } else if (bankId == 3
281. bankId == 18
282. bankId == 19
283. bankId == 27) {
284. } else if (bankId == 5) {
285. } else if (bankId == 12) {
286. this._b == '55'
287. this._b == '47'
288. this._b == '48'
289. this._b == '19'
290. this._b == '59'
291. this._b == '73'
292. this._b == '12') {
293. this._b == '3'
294. this._b == '43'
295. this._b == '45'
296. this._b == '57'
297. this._b == '61'
298. this._b == '63'
299. this._b == '67'
300. this._b == '68'
301. this._b == '69'
302. this._b == '72'
303. this._b == '9'
304. this._b == '74'
305. this._b == '75') {
306. this._b == '36'
307. this._b == '75') { //sonnh them 72 Vietbank
308. if (item['id'] == this._b) {
309. } else if (this._res_post.state == 'failed') { // lỗi mới kiểm tra sang trang lỗi
310. if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
311. } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
312. v.length == 15) || (v.length == 16
313. v.length == 19))
314. this._util.checkMod10(v) == true) {
315. cardNo.length == 15)
316. cardNo.length == 16)
317. cardNo.startsWith('81')) && (cardNo.length == 16
318. cardNo.length == 19))
319. this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
320. this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
321. v.length == 5) {
322. v.length == 4
323. v.length == 3)
324. _val.value.length == 4
325. _val.value.length == 3)
326. this.requireAvs = this.AVS_COUNTRIES.find(e => e.code == this._i_country_code) ? true : false;
327. this._i_csc.length == 4) ||
328. this._i_csc.length == 3)
329. country = this.AVS_COUNTRIES.find(e => e.code == res.bin_country);
330. countryCode == 'US' ? US_STATES
331. : countryCode == 'CA' ? CA_STATES
332. !token) || (type == 1
333. !token) || (type == 6
334. !token) || (type == 3
335. method?.trim()=='International'"
336. method.trim()=='ApplePay'"
337. method.trim()=='GooglePay'"
338. method.trim()=='SamsungPay'"
339. method?.trim()=='Domestic'"
340. method?.trim()=='QR'"
341. method?.trim()=='Paypal'"
342. if (el == 1) {
343. } else if (el == 2) {
344. } else if (el == 4) {
345. } else if (el == 3) {
346. if (!isNaN(_re.status) && (_re.status == '200'
347. _re.status == '201') && _re.body != null) {
348. if (('closed' == this._res_polling.state
349. 'canceled' == this._res_polling.state
350. 'expired' == this._res_polling.state)
351. } else if ('paid' == this._res_polling.state) {
352. this._res_polling.payments == null
353. if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'authorization_required') {
354. } else if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
355. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
356. this._paymentService.getCurrentPage() == 'enter_card') {
357. } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
358. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
359. } else if ('not_paid' == this._res_polling.state) {
360. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
361. if (auth == 'auth') {
362. detail.merchant.id == 'AMWAY') {
363. if (this.checkInvoiceState() == 1) {
364. this.version2 = _re.body?.merchant?.qr_version == "2"
365. if (this.themeConfig.default_method == 'International'
366. } else if (this.themeConfig.default_method == 'Domestic'
367. } else if (this.themeConfig.default_method == 'QR'
368. } else if (this.themeConfig.default_method == 'Paypal'
369. if (('closed' == this._res.state
370. 'canceled' == this._res.state
371. 'expired' == this._res.state
372. 'paid' == this._res.state)
373. if ('paid' == this._res.state
374. if (count == 2
375. if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
376. this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
377. if (this._res.payments[this._res.payments.length - 1].state == 'approved'
378. } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
379. if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required') {
380. } else if (this._res.payments[this._res.payments.length - 1].state == 'pending') {
381. } else if (this._res.payments[this._res.payments.length - 1].state == 'canceled') {
382. } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
383. this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
384. if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
385. } else if (idBrand == 'atm'
386. if ('paid' == this._res.state) {
387. 'canceled' == this._res.state) {
388. this._res.payments == null) {
389. this._res.payments[this._res.payments.length - 1].state == 'pending') {
390. if (this._res.currencies[0] == 'USD') {
391. if (data._locale == 'en') {
392. if (event.data?.event_type == 'applepay_network_not_supported') {
393. if (res?.body?.state == 'approved') {
394. } else if (res?.body?.state == 'authorization_required') {
395. paymentType == PaymentType.ApplePay"
396. paymentType == PaymentType.GooglePay"
397. paymentType == PaymentType.SamsungPay"
398. if (this.paymentType == PaymentType.ApplePay) {
399. if (network == 'napas'
400. serviceId: this.environment == 'PRODUCTION'? this.serviceIdProd : this.serviceIdTest
401. screen=='qr'"
402. screen=='confirm_close'"
403. this.themeColor.deeplink_status == 'Off' ? false : true
404. if (item.available == true) {
405. if (appcode == 'grabpay'
406. appcode == 'momo') {
407. if (type == 2
408. err.error.code == '04') {
409. e.type == 'vnpayqr') {
410. e.type == 'ewallet') {
411. e.type == 'wallet')) {
412. if (d.b.code == s) {
413. type == 'vnpay'"
414. type == 'bankapp'"
415. type == 'both'"
416. this.themeConfig.deeplink_status == 'Off' ? false : true
417. _locale=='vi'"
418. _locale=='en'"
419. _locale == 'vi'"
420. _locale == 'en'"
421. e.type == 'deeplink') {
422. e.type == 'ewallet'
423. if (e.type == 'ewallet') {
424. this.listWallet.length == 1
425. this.listWallet[0].code == 'momo') {
426. this.checkEWalletDeeplink.length == 0) {
427. arrayWallet.length == 0) return false;
428. if (arrayWallet[i].code == key) {
429. if (this.locale == 'vi') {
430. *ngIf="(!token) || (type == paymentType)"
431. type == paymentType"
432. return ((a.id == id
433. a.code == id) && a.type.includes(type));
434. if (isIphone == true) {
435. } else if (isAndroid == true) {
436. element.value == 'true'
437. return countPayment == maxPayment
438. if (this.getLatestPayment().state == 'canceled')
439. if (res?.state == 'canceled') {
440. state == 'authorization_required'
441. if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
442. else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';
443. if (e.name == bankSwift) { // TODO: get by swift
444. return this.apps.find(e => e.code == appCode);
445. if (+e.id == bankId) {
446. if (e.swiftCode == bankSwift) {
447. if (this.checkCount == 1) {
448. if (results == null) {
449. if (c.length == 3) {
450. d = d == undefined ? '.' : d
451. t = t == undefined ? '
452. return results == null ? null : results[1]
453. if (((_val.length == 4
454. _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
455. _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
456. iss_date.length == 4
457. iss_date.search('/') == -1)
458. iss_date.length == 5))
459. let applepayNapas = document.getElementById('applepay-napas')?.value == 'true'
460. let applepayVisa = document.getElementById('applepay-visa')?.value == 'true'
461. let applepayMasterCard = document.getElementById('applepay-masterCard')?.value == 'true'
462. let applepayJCB = document.getElementById('applepay-jcb')?.value == 'true'
463. if (applepayNapas == true) {
464. if (applepayVisa == true) {
465. if (applepayMasterCard == true) {
466. if (applepayJCB == true) {
467. if(document.getElementById('applepay-merchantAVS').value == 'true'){
468. response.status == '400') {
469. } else if (response.status == '500') {
470. if (data.name == "CARD_TYPE_CAN_NOT_BE_PROCESSED") { // in case response.status == 400
471. } else if (data.state == "approved"){ // in case response.ok
472. if (_dataCache == null) {
473. if ( (0 <= r && r <= 6 && (c == 0
474. c == 6) )
475. || (0 <= c && c <= 6 && (r == 0
476. r == 6) )
477. if (i == 0
478. _modules[r][6] = (r % 2 == 0);
479. _modules[6][c] = (c % 2 == 0);
480. if (r == -2
481. r == 2
482. c == -2
483. c == 2
484. || (r == 0
485. c == 0) ) {
486. ( (bits >> i) & 1) == 1);
487. if (col == 6) col -= 1;
488. if (_modules[row][col - c] == null) {
489. dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
490. if (bitIndex == -1) {
491. margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
492. if (typeof arguments[0] == 'object') {
493. margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
494. if (b == -1) throw 'eof';
495. if (b0 == -1) break;
496. if (typeof b == 'number') {
497. if ( (b & 0xff) == b) {
498. return function(i, j) { return (i + j) % 2 == 0
499. return function(i, j) { return i % 2 == 0
500. return function(i, j) { return j % 3 == 0
501. return function(i, j) { return (i + j) % 3 == 0
502. return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
503. return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
504. return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
505. return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
506. if (r == 0
507. c == 0) {
508. if (dark == qrcode.isDark(row + r, col + c) ) {
509. if (count == 0
510. count == 4) {
511. if (typeof num.length == 'undefined') {
512. num[offset] == 0) {
513. if (typeof rsBlock == 'undefined') {
514. return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
515. _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
516. if (data.length - i == 1) {
517. } else if (data.length - i == 2) {
518. } else if (n == 62) {
519. } else if (n == 63) {
520. if (_buflen == 0) {
521. if (c == '=') {
522. } else if (c == 0x2b) {
523. } else if (c == 0x2f) {
524. if (table.size() == (1 << bitLength) ) {
525. if (cardList.length == 1) {//TOKEN, BIDV, SHB, OCEAN, PVCOM, TP Bank
526. if ( $('.circle_v1').css('display') == 'block'
527. if ($('.circle_v2').css('display') == 'block'
528. $('.circle_v3').css('display') == 'block' ) {
529. $('.circle_v1').css('display') == 'block'
530. document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM'
531. if ( document.getElementsByClassName("select_online")[0].value == 'Easy Online Banking') {
532. if ( document.getElementsByClassName("select_online")[0].value == 'Easy Mobile Banking') {
533. if ( document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM') {
534. if ($('.circle_v1').css('display') == 'block') {
535. if ($('.circle_v1').css('display') == 'block'
536. if ( $('.circle_v1').css('display') == 'block') {
537. else if ($('.circle_v2').css('display') == 'block') {
538. if ( $('.circle_v3').css('display') == 'block' ) {
539. if ($('.circle_v2').css('display') == 'block') {
540. x = _.positionProp == 'left' ? Math.ceil(position) + 'px' : '0px'
541. y = _.positionProp == 'top' ? Math.ceil(position) + 'px' : '0px'
542. if (_.options.slidesToShow == _.options.slidesToScroll
543. if (typeof opt == 'object'
544. typeof opt == 'undefined')

!== (161 điều kiện duy nhất):
------------------------------------------------------------
1. this.lastValue !== $event.target.value)) {
2. if (YY % 400 === 0 || (YY % 100 !== 0
3. if (YYYY % 400 === 0 || (YYYY % 100 !== 0
4. event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
5. key !== '3') {
6. this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
7. codeResponse.toString() !== '0') {
8. cardNo.length !== 0) {
9. if (this.cardTypeBank !== 'bank_card_number') {
10. if (this.cardTypeBank !== 'bank_account_number') {
11. if (this.cardTypeBank !== 'bank_username') {
12. if (this.cardTypeBank !== 'bank_customer_code') {
13. this.lb_card_account !== this._translate.instant('ocb_account')) {
14. this.lb_card_account !== this._translate.instant('ocb_phone')) {
15. this.lb_card_account !== this._translate.instant('ocb_card')) {
16. this._b !== 18) || (this.cardTypeOcean === 'ATM'
17. let _b = this._b !== 67 ? 67 : this._b
18. if (this.cardTypeBank !== 'internet_banking') {
19. this._b !== 18)) {
20. this.bankList = this.bankList.filter(item => item.b.id !== '67');
21. key !== '8') {
22. codeResponse.toString() !== '0'){
23. event.inputType !== 'deleteContentBackward') || v.length == 5) {
24. this._i_country_code !== 'US') {
25. itemRemoved !== '') {
26. if (deviceValue !== 'default') {
27. this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
28. this._i_country_code !== 'default'
29. this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
30. this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {
31. if (_val !== 3) {
32. this._util.getElementParams(this.currentUrl, 't_id') !== '') {
33. queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
34. if (queryString !== '') {
35. if (target !== 0
36. if (e !== null) {
37. if (lang !== "vi") lang = "en";
38. this.oldValue !== this.value) {
39. if (jLinks !== undefined
40. jLinks !== null) {
41. if (jMerchantReturn !== undefined
42. jMerchantReturn !== null) {
43. if (responseCode !== undefined
44. responseCode !== null
45. if (parentRes !== "{
46. if (value !== "") {
47. if (inMonth.value !== ""
48. if (inYear.value !== ""
49. var month = inMonth.value !== ""
50. var year = parseInt("20" + (inYear.value !== ""
51. if ("2D" !== order["vpc_VerType"]) return err("MISSING_FIELD"
52. inMonth.value !== tokenInsMonth) instrument["month"] = inMonth.value;
53. inYear.value !== tokenInsYear) instrument["year"] = inYear.value;
54. if (inCvv.value !== "") instrument["cvv"] = inCvv.value;
55. if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
56. if (inNumber.style.display !== "none") instrument.number = inNumber.value;
57. if (inDate.style.display !== "none") instrument.date = inDate.value;
58. if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
59. if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
60. if (inName.style.display !== "none") instrument.name = inName.value;
61. typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
62. hrefAttr !== '#' ? hrefAttr.trim() : ''
63. $(this._element).css('visibility') !== 'hidden') {
64. if (selector !== null
65. if (selector !== null) {
66. if (typeof this._config.parent.jquery !== 'undefined') {
67. if (typeof this._config.reference.jquery !== 'undefined') {
68. if (this._config.boundary !== 'scrollParent') {
69. if (this._popper !== null) {
70. event.which !== TAB_KEYCODE)) {
71. event.which !== ESCAPE_KEYCODE
72. if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
73. event.which !== ARROW_UP_KEYCODE
74. this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
75. if (document !== event.target
76. _this5._element !== event.target
77. if (event.target !== event.currentTarget) {
78. if (typeof margin !== 'undefined') {
79. if (allowedAttributeList.indexOf(attrName) !== -1) {
80. if (uriAttrs.indexOf(attrName) !== -1) {
81. var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
82. if (_this2._hoverState !== HoverState.SHOW
83. if (_this2._popper !== null) {
84. if (data.originalPlacement !== data.placement) {
85. } else if (trigger !== Trigger.MANUAL) {
86. titleType !== 'string') {
87. if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
88. if (this.constructor.Default[key] !== this.config[key]) {
89. if (tabClass !== null
90. if (tip.getAttribute('x-placement') !== null) {
91. if (typeof config.target !== 'string') {
92. if (this._scrollHeight !== scrollHeight) {
93. if (this._activeTarget !== target) {
94. var isActiveTarget = this._activeTarget !== this._targets[i]
95. 'use strict';(function(Y){function C(c,a,b){var e=0,h=[],n=0,g,l,d,f,m,q,u,r,I=!1,v=[],w=[],t,y=!1,z=!1,x=-1;b=b||{};g=b.encoding||"UTF8";t=b.numRounds||1;if(t!==parseInt(t,10)
96. 0!==f%32
97. a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8
98. }switch(c){case "HEX":c=function(a,c,d){var g=a.length,l,p,f,m,q,u;if(0!==g%2)throw Error("String of HEX type must be in byte increments");c=c||[0];d=d||0;q=d>>>3;u=-1===b?3:0;for(l=0
99. 1!==b
100. "UTF16LE"!==a
101. "");if(-1!==p
102. function(a,c,d){var g,l,p,f,m,q;c=c||[0];d=d||0;l=d>>>3;m=-1===b?3:0;q=new Uint8Array(a);for(g=0;g<a.byteLength;g+=1)f=g+l,p=f>>>2,c.length<=p&&c.push(0),c[p]|=q[g]<<8*(m+f%4*b);return{value:c,binLen:8*a.byteLength+d}};break;default:throw Error("format must be HEX, TEXT, B64, BYTES, or ARRAYBUFFER");}return c}function y(c,a){return c<<a|c>>>32-a}function S(c,a){return 32<a?(a-=32,new b(c.b<<a|c.a>>>32-a,c.a<<a|c.b>>>32-a)):0!==a?new b(c.a<<a|c.b>>>32-a,c.b<<a|c.a>>>32-a):c}function w(c,a){return c>>>
103. a[7]);return a}function D(c,a){var d,e,h,n,g=[],l=[];if(null!==c)for(e=0
104. define.amd?define(function(){return C}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=C),exports=C):Y.jsSHA=C})(this);
105. return C}):"undefined"!==typeof exports?("undefined"!==typeof module
106. 'use strict';(function(I){function w(c,a,d){var l=0,b=[],g=0,f,n,k,e,h,q,y,p,m=!1,t=[],r=[],u,z=!1;d=d||{};f=d.encoding||"UTF8";u=d.numRounds||1;if(u!==parseInt(u,10)
107. l+=1)g[l]=c[l>>>2]>>>8*(3+l%4*-1)&255;return b}function C(c){var a={outputUpper:!1,b64Pad:"=",shakeLen:-1};c=c||{};a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");
108. if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0;d<f;d+=2){k=parseInt(a.substr(d,2),16);if(isNaN(k))throw Error("String of HEX type contains invalid characters");
109. if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0
110. "");if(-1!==k
111. define.amd?define(function(){return w}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=w),exports=w):I.jsSHA=w})(this);
112. return w}):"undefined"!==typeof exports?("undefined"!==typeof module
113. } else if (typeof exports !== 'undefined') {
114. if (typeof document.mozHidden !== 'undefined') {
115. } else if (typeof document.webkitHidden !== 'undefined') {
116. asNavFor !== null ) {
117. if ( asNavFor !== null
118. if (_.options.infinite !== true) {
119. _.options.responsive !== null) {
120. if (targetBreakpoint !== null) {
121. if (_.activeBreakpoint !== null) {
122. if (targetBreakpoint !== _.activeBreakpoint
123. triggerBreakpoint !== false ) {
124. unevenOffset = (_.slideCount % _.options.slidesToScroll !== 0);
125. _.$dots !== null) {
126. if (filter !== null) {
127. if (_.slideCount % _.options.slidesToScroll !== 0) {
128. if (_.$dots !== null) {
129. if (slideControlIndex !== -1) {
130. _.currentSlide !== 0) {
131. if ($(window).width() !== _.windowWidth) {
132. } else if ( typeof arguments[1] !== 'undefined' ) {
133. if( $.type( _.options.responsive ) !== 'array' ) {
134. if (bodyStyle.WebkitTransition !== undefined
135. bodyStyle.MozTransition !== undefined
136. bodyStyle.msTransition !== undefined) {
137. if (bodyStyle.OTransform !== undefined) {
138. if (bodyStyle.MozTransform !== undefined) {
139. if (bodyStyle.webkitTransform !== undefined) {
140. if (bodyStyle.msTransform !== undefined) {
141. if (bodyStyle.transform !== undefined
142. _.animType !== false) {
143. _.transformsEnabled = _.options.useTransform && (_.animType !== null
144. _.animType !== false);
145. if (dontAnimate !== true
146. if (dontAnimate !== true) {
147. if ( _.touchObject.startX !== _.touchObject.curX ) {
148. event.type.indexOf('mouse') !== -1) {
149. event.originalEvent.touches !== undefined ?
150. touches = event.originalEvent !== undefined ? event.originalEvent.touches : null
151. touches.length !== 1) {
152. _.touchObject.curX = touches !== undefined ? touches[0].pageX : event.clientX
153. _.touchObject.curY = touches !== undefined ? touches[0].pageY : event.clientY
154. if (event.originalEvent !== undefined
155. if (_.touchObject.fingerCount !== 1
156. event.originalEvent.touches !== undefined) {
157. _.touchObject.startX = _.touchObject.curX = touches !== undefined ? touches.pageX : event.clientX
158. _.touchObject.startY = _.touchObject.curY = touches !== undefined ? touches.pageY : event.clientY
159. if (_.$slidesCache !== null) {
160. //if (event.origin !== "http://example.com:8080") return;
161. /*if (contentType !== my_expected_type) {

!= (172 điều kiện duy nhất):
------------------------------------------------------------
1. if (params['locale'] != null
2. } else if (params['locale'] != null
3. if (userLang != null
4. if(value!=null){
5. if (message != ''
6. message != null
7. message != undefined) {
8. if (this._idInvoice != null
9. this._idInvoice != 0) {
10. if (this._paymentService.getInvoiceDetail() != null) {
11. dataPassed.body != null) {
12. dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
13. if (this._translate.currentLang != language) {
14. } else if (this._b != 18) {
15. if (this.htmlDesc != null
16. if (ua.indexOf('safari') != -1
17. if (_val.value != '') {
18. this.auth_method != null) {
19. if (this.valueDate.length != 3) {
20. if (_formCard.exp_date != null
21. if (this.cardName != null
22. if (this._res_post.links != null
23. this._res_post.links.merchant_return != null
24. this._res_post.links.merchant_return.href != null) {
25. if (this._res_post.authorization != null
26. this._res_post.authorization.links != null
27. this._res_post.authorization.links.approval != null) {
28. this._res_post.links.cancel != null) {
29. this._b != 27
30. this._b != 12
31. this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
32. this._b != 18)
33. if (this._b != 18
34. this._b != 19) {
35. if (this._inExpDate.length != 3) {
36. if (this._res_post.return_url != null) {
37. let userName = _formCard.name != null ? _formCard.name : ''
38. this._res_post.authorization.links.approval != null
39. this._res_post.authorization.links.approval.href != null) {
40. userName = paramUserName != null ? paramUserName : ''
41. this._b != 3))
42. if (this._b != 68
43. this._b != 2
44. this._b != 20
45. this._b != 33
46. this._b != 39
47. this._b != 43
48. this._b != 45
49. this._b != 64
50. this._b != 67
51. this._b != 68
52. this._b != 72)
53. this._b != 72 )
54. if (this._res.links != null
55. this._res.links.merchant_return != null
56. this._res.links.merchant_return.href != null) {
57. if (this._res_post != null
58. this._res_post.links != null
59. if (!(_formCard.otp != null
60. if (!(_formCard.password != null
61. if ( this._b != 9
62. this._b != 16
63. this._b != 17
64. this._b != 25
65. this._b != 44
66. this._b != 57
67. this._b != 59
68. this._b != 61
69. this._b != 63
70. this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
71. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75'].indexOf(this._b.toString()) != -1) {// duongpxt 19255: Them vietbank napas id 72
72. if (params['locale'] != null) {
73. if ('otp' != this._paymentService.getCurrentPage()) {
74. this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
75. if (!(strInstrument != null
76. if (strInstrument.substring(0, 1) != '^'
77. strInstrument.substr(strInstrument.length - 1) != '$') {
78. if (bankid != null) {
79. } else if (this._res_post.links != null
80. cardNo != null
81. v != null
82. this.c_csc = (!(_val.value != null
83. this._i_csc != null
84. this.requireAvs = this.isAvsCountry = country != undefined
85. if (this._idInvoice != null) {
86. if (this._paymentService.getCurrentPage() != 'otp') {
87. _re.body != null) {
88. this._res_polling.links != null
89. this._res_polling.links.merchant_return != null //
90. this._util.getElementParams(url_redirect, 'vpc_TxnResponseCode') != '99') {
91. } else if (this._res_polling.merchant != null
92. this._res_polling.merchant_invoice_reference != null
93. this._paymentService.getState() != 'error') {
94. } else if (this._res_polling.payments != null
95. this._res_polling.links.merchant_return != null//
96. this._res_polling.payments != null
97. if (this._res_polling.payments != null
98. t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
99. this.type.toString().length != 0) {
100. this._res.links != null
101. if (count != 1) {
102. if (this._res.merchant != null
103. this._res.merchant_invoice_reference != null) {
104. if (this._res.merchant.address_details != null) {
105. this._res.links != null//
106. } else if (this._res.payments != null
107. this._res.payments[this._res.payments.length - 1].instrument != null
108. this._res.payments[this._res.payments.length - 1].instrument.issuer != null
109. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
110. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
111. this._res.payments[this._res.payments.length - 1].links != null
112. this._res.payments[this._res.payments.length - 1].links.cancel != null
113. this._res.payments[this._res.payments.length - 1].links.cancel.href != null
114. this._res.payments[this._res.payments.length - 1].links.update != null
115. this._res.payments[this._res.payments.length - 1].links.update.href != null) {
116. this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
117. this._res.payments[this._res.payments.length - 1].authorization != null
118. this._res.payments[this._res.payments.length - 1].authorization.links != null) {
119. this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
120. this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
121. if (this._res.payments[this._res.payments.length - 1].authorization != null
122. this._res.payments[this._res.payments.length - 1].authorization.links != null
123. auth = paramUserName != null ? paramUserName : ''
124. this._res.links.merchant_return != null //
125. } else if (this._res.merchant != null
126. this._res.merchant_invoice_reference != null
127. } else if (this._res.payments != null) {
128. if (event.origin != window.origin) return; // chỉ nhận message từ OnePay
129. if (this.paymentType != PaymentType.ApplePay) return;
130. if (this.paymentType != PaymentType.ApplePay) return false;
131. if (network != 'napas'
132. 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {
133. if (appcode != null) {
134. if (_re.status != '200'
135. _re.status != '201')
136. qr_version2 != 'None'"
137. qr_version2 != 'None'
138. if (this.translate.currentLang != language) {
139. if (idInvoice != null
140. idInvoice != 0)
141. idInvoice != 0) {
142. if (this._merchantid != null
143. this._tranref != null
144. this._state != null
145. urlCancel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
146. if (paymentId != null) {
147. if (res?.status != 200
148. res?.status != 201) return;
149. _re.status != '201') {
150. latestPayment?.state != "authorization_required") {
151. if (tem != null) {
152. if ((tem = ua.match(/version\/(\d+)/i)) != null) {
153. if (v.length != 3) {
154. if (network != "napas") return true;
155. if (currency != "VND") return false; // napas accept VND only
156. if (_modules[r][6] != null) {
157. if (_modules[6][c] != null) {
158. if (_modules[row][col] != null) {
159. while (buffer.getLengthInBits() % 8 != 0) {
160. if (count != numChars) {
161. throw count + ' != ' + numChars
162. while (data != 0) {
163. if (test.length != 2
164. ( (test[0] << 8) | test[1]) != code) {
165. if (_length % 3 != 0) {
166. if ( (data >>> length) != 0) {
167. return typeof _map[key] != 'undefined'
168. var source = arguments[i] != null ? arguments[i] : {
169. $('[draggable!=true]', _.$slideTrack).off('dragstart', _.preventDefault);
170. $('[draggable!=true]', _.$slideTrack).on('dragstart', _.preventDefault);
171. if( direction != 'vertical' ) {
172. if (typeof ret != 'undefined') return ret;

