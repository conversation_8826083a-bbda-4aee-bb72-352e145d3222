====================================================================================================
TRÍCH XUẤT ĐIỀU KIỆN SO SÁNH TỪ CÁC FILE TYPESCRIPT/JAVASCRIPT/HTML
====================================================================================================
Thư mục/File nguồn: /root/projects/onepay/paygate-generalv2/src
Thời gian: 18:12:29 18/8/2025
Tổng số file xử lý: 263
Tổng số file bị bỏ qua: 3
Tổng số điều kiện tìm thấy: 2521

THỐNG KÊ TỔNG QUAN THEO LOẠI TOÁN TỬ:
--------------------------------------------------
Strict equality (===): 503 lần
Loose equality (==): 1371 lần
Strict inequality (!==): 141 lần
Loose inequality (!=): 506 lần
--------------------------------------------------

DANH SÁCH FILE ĐÃ XỬ LÝ:
--------------------------------------------------
1. 4en.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/4en.html
2. 4vn.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/4vn.html
3. app-routing.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/app-routing.module.ts
4. app.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/app.component.html
5. app.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/app.component.spec.ts
6. app.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/app.component.ts
7. app.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/app.module.ts
8. cancel-tran.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/cancel-tran/cancel-tran.component.html
9. cancel-tran.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/cancel-tran/cancel-tran.component.spec.ts
10. cancel-tran.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/cancel-tran/cancel-tran.component.ts
11. dialog-common.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/dialog-common/dialog-common.component.html
12. dialog-common.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/dialog-common/dialog-common.component.spec.ts
13. dialog-common.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/dialog-common/dialog-common.component.ts
14. footer.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/footer/footer.component.html
15. footer.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/footer/footer.component.spec.ts
16. footer.component.ts (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/footer/footer.component.ts
17. template-qr.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/template-qr/template-qr.component.html
18. template-qr.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/template-qr/template-qr.component.spec.ts
19. template-qr.component.ts (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/template-qr/template-qr.component.ts
20. main.component.html (7 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/main.component.html
21. main.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/main.component.spec.ts
22. main.component.ts (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/main.component.ts
23. bank-amount-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/dialog/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
24. bank-amount-dialog.component.ts (35 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/dialog/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
25. confirm-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/dialog/confirm-dialog/confirm-dialog.component.html
26. confirm-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/dialog/confirm-dialog/confirm-dialog.component.ts
27. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/dialog/dialog-guide-dialog.html
28. policy-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.html
29. policy-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.ts
30. menu.component.html (89 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/menu.component.html
31. menu.component.ts (213 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/menu.component.ts
32. samsung-pay-button-op.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.html
33. samsung-pay-button-op.component.ts (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.ts
34. types-samsung-pay.d.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/types-samsung-pay.d.ts
35. amigo-form.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/amigo/amigo-form.component.html
36. amigo-form.component.ts (24 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/amigo/amigo-form.component.ts
37. bnpl-main.component.html (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/bnpl-main.component.html
38. bnpl-main.component.ts (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/bnpl-main.component.ts
39. circle-process-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/dialog/circle-process-dialog.html
40. dialog-delete-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/fundiin/dialog-delete-dialog.html
41. dialog-policy.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/fundiin/dialog-policy/dialog-policy.component.html
42. dialog-policy.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/fundiin/dialog-policy/dialog-policy.component.spec.ts
43. dialog-policy.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/fundiin/dialog-policy/dialog-policy.component.ts
44. fundiin-form.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/fundiin/fundiin-form.component.html
45. fundiin-form.component.ts (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/fundiin/fundiin-form.component.ts
46. select-data.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/fundiin/select-data/select-data.html
47. select-data.ts (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/fundiin/select-data/select-data.ts
48. dialog-delete-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/homecredit/dialog-delete-dialog.html
49. dialog-policy.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/homecredit/dialog-policy/dialog-policy.component.html
50. dialog-policy.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/homecredit/dialog-policy/dialog-policy.component.spec.ts
51. dialog-policy.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/homecredit/dialog-policy/dialog-policy.component.ts
52. homecredit-form.component.html (10 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/homecredit/homecredit-form.component.html
53. homecredit-form.component.ts (39 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/homecredit/homecredit-form.component.ts
54. dialog-delete-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/kbank/dialog-delete-dialog.html
55. kbank-dialog-policy.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/kbank/kbank-dialog-policy/kbank-dialog-policy.component.html
56. kbank-dialog-policy.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/kbank/kbank-dialog-policy/kbank-dialog-policy.component.spec.ts
57. kbank-dialog-policy.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/kbank/kbank-dialog-policy/kbank-dialog-policy.component.ts
58. kbank-form.component.html (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/kbank/kbank-form.component.html
59. kbank-form.component.ts (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/kbank/kbank-form.component.ts
60. kredivo-form.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/kredivo/kredivo-form.component.html
61. kredivo-form.component.ts (23 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/kredivo/kredivo-form.component.ts
62. otp-auth-bnpl.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/otp-auth-bnpl/otp-auth-bnpl.component.html
63. otp-auth-bnpl.component.ts (12 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/otp-auth-bnpl/otp-auth-bnpl.component.ts
64. process-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/process-dialog/process-dialog.html
65. process-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/process-dialog/process-dialog.ts
66. select-bnpl.html (12 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/select-bnpl-dialog/select-bnpl.html
67. select-bnpl.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/select-bnpl-dialog/select-bnpl.ts
68. bankaccount.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/bankaccount/bankaccount.component.html
69. bankaccount.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/bankaccount/bankaccount.component.spec.ts
70. bankaccount.component.ts (159 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/bankaccount/bankaccount.component.ts
71. bank.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/model/bank.ts
72. onepay-napas.component.html (7 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/onepay-napas/onepay-napas.component.html
73. onepay-napas.component.ts (106 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/onepay-napas/onepay-napas.component.ts
74. otp-auth.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/otp-auth/otp-auth.component.html
75. otp-auth.component.ts (19 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/otp-auth/otp-auth.component.ts
76. shb.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/shb/shb.component.html
77. shb.component.ts (69 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/shb/shb.component.ts
78. techcombank.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/techcombank/techcombank.component.html
79. techcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/techcombank/techcombank.component.spec.ts
80. techcombank.component.ts (20 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/techcombank/techcombank.component.ts
81. vibbank.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/vibbank/vibbank.component.html
82. vibbank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/vibbank/vibbank.component.spec.ts
83. vibbank.component.ts (101 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/vibbank/vibbank.component.ts
84. vietcombank.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/vietcombank/vietcombank.component.html
85. vietcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/vietcombank/vietcombank.component.spec.ts
86. vietcombank.component.ts (95 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/vietcombank/vietcombank.component.ts
87. vietqr-domes.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/vietqr-domes/vietqr-domes.component.html
88. vietqr-domes.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/vietqr-domes/vietqr-domes.component.ts
89. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/dialog/dialog-guide-dialog.html
90. off-bank-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/dialog/off-bank-dialog/off-bank-dialog.html
91. off-bank-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/dialog/off-bank-dialog/off-bank-dialog.ts
92. domescard-main.component.html (12 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/domescard-main.component.html
93. domescard-main.component.ts (155 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/domescard-main.component.ts
94. bank-domescard-support.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version2/bank-domescard-support/bank-domescard-support.component.html
95. bank-domescard-support.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version2/bank-domescard-support/bank-domescard-support.component.ts
96. bank.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version2/banks/model/bank.ts
97. otp-auth.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version2/banks/otp-auth/otp-auth.component.html
98. otp-auth.component.ts (18 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version2/banks/otp-auth/otp-auth.component.ts
99. vietcombank.component.html (10 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version2/banks/vietcombank/vietcombank.component.html
100. vietcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version2/banks/vietcombank/vietcombank.component.spec.ts
101. vietcombank.component.ts (131 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version2/banks/vietcombank/vietcombank.component.ts
102. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version2/dialog/dialog-guide-dialog.html
103. domescard-main.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version2/domescard-main.component.html
104. domescard-main.component.ts (68 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version2/domescard-main.component.ts
105. domescard-main.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main.component.html
106. domescard-main.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main.component.ts
107. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/intercard-main/dialog-guide-dialog.html
108. component-select.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/intercard-main/form/component-select/component-select.component.html
109. component-select.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/intercard-main/form/component-select/component-select.component.spec.ts
110. component-select.component.ts (10 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/intercard-main/form/component-select/component-select.component.ts
111. intercard-main-form.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/intercard-main/form/intercard-main-form.component.html
112. intercard-main-form.component.ts (70 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/intercard-main/form/intercard-main-form.component.ts
113. intercard-main.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/intercard-main/intercard-main.component.html
114. intercard-main.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/intercard-main/intercard-main.component.ts
115. applepay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/applepay/applepay.component.html
116. applepay.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/applepay/applepay.component.ts
117. dialog-network-not-supported.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/applepay/dialog-network-not-supported/dialog-network-not-supported.component.html
118. dialog-network-not-supported.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/applepay/dialog-network-not-supported/dialog-network-not-supported.component.ts
119. applepay-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/applepay-main/applepay-guide-dialog.html
120. applepay-main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/applepay-main/applepay-main.component.html
121. applepay-main.component.ts (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/applepay-main/applepay-main.component.ts
122. dialog-rule-nobile-dialog.html (10 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/dialog-rule-nobile-dialog.html
123. google-pay-button-op.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/googlepay/google-pay-button-op/google-pay-button-op.component.html
124. google-pay-button-op.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/googlepay/google-pay-button-op/google-pay-button-op.component.ts
125. types-google-pay.d.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/googlepay/google-pay-button-op/types-google-pay.d.ts
126. googlepay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/googlepay/googlepay.component.html
127. googlepay.component.ts (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/googlepay/googlepay.component.ts
128. googlepay-main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/googlepay-main/googlepay-main.component.html
129. googlepay-main.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/googlepay-main/googlepay-main.component.ts
130. samsung-pay-button-op.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.html
131. samsung-pay-button-op.component.ts (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.ts
132. types-samsung-pay.d.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/samsungpay/samsung-pay-button-op/types-samsung-pay.d.ts
133. samsungpay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/samsungpay/samsungpay.component.html
134. samsungpay.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/samsungpay/samsungpay.component.spec.ts
135. samsungpay.component.ts (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/samsungpay/samsungpay.component.ts
136. samsungpay-main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/samsungpay-main/samsungpay-main.component.html
137. samsungpay-main.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/samsungpay-main/samsungpay-main.component.ts
138. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version1/dialog/dialog-guide-dialog.html
139. qr-detail.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version1/qr-detail/qr-detail.component.html
140. qr-detail.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version1/qr-detail/qr-detail.component.spec.ts
141. qr-detail.component.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version1/qr-detail/qr-detail.component.ts
142. qr-dialog.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version1/qr-dialog/qr-dialog.html
143. qr-dialog.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version1/qr-dialog/qr-dialog.ts
144. qr-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version1/qr-guide-dialog/qr-guide-dialog.html
145. qr-guide-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version1/qr-guide-dialog/qr-guide-dialog.ts
146. qr-main.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version1/qr-main.component.html
147. qr-main.component.ts (25 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version1/qr-main.component.ts
148. safe-html.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version1/safe-html.pipe.ts
149. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/dialog/dialog-guide-dialog.html
150. qr-desktop.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-desktop/qr-desktop.component.html
151. qr-desktop.component.ts (19 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-desktop/qr-desktop.component.ts
152. qr-dialog-version2.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-dialog/qr-dialog-version2.html
153. qr-dialog-version2.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-dialog/qr-dialog-version2.ts
154. qr-guide-dialog-version2.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-guide-dialog/qr-guide-dialog-version2.html
155. qr-guide-dialog-version2.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-guide-dialog/qr-guide-dialog-version2.ts
156. list-bank-dialog-version2.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-listbank/list-bank-dialog-version2.html
157. list-bank-dialog-version2.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-listbank/list-bank-dialog-version2.ts
158. qr-main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-main.component.html
159. qr-main.component.ts (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-main.component.ts
160. qr-mobile.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-mobile/qr-mobile.component.html
161. qr-mobile.component.ts (30 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-mobile/qr-mobile.component.ts
162. safe-html.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/safe-html.pipe.ts
163. qr-main.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main.component.html
164. qr-main.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main.component.ts
165. vietqr-guide.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/vietqr-main/vietqr-guide/vietqr-guide.component.html
166. vietqr-guide.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/vietqr-main/vietqr-guide/vietqr-guide.component.ts
167. vietqr-listbank.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/vietqr-main/vietqr-listbank/vietqr-listbank.component.html
168. vietqr-listbank.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/vietqr-main/vietqr-listbank/vietqr-listbank.component.ts
169. vietqr-main.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/vietqr-main/vietqr-main.component.html
170. vietqr-main.component.ts (7 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/vietqr-main/vietqr-main.component.ts
171. bnpl-form.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/bnpl-form/bnpl-form.component.html
172. bnpl-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/bnpl-form/bnpl-form.component.ts
173. domescard-form.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/domescard-form/domescard-form.component.html
174. domescard-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/domescard-form/domescard-form.component.ts
175. intercard-form.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/intercard-form/intercard-form.component.html
176. intercard-form.component.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/intercard-form/intercard-form.component.ts
177. mobile-wallet-form.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/mobile-wallet-form/mobile-wallet-form.component.html
178. mobile-wallet-form.component.ts (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/mobile-wallet-form/mobile-wallet-form.component.ts
179. paypal-form.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/paypal-form/paypal-form.component.html
180. paypal-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/paypal-form/paypal-form.component.ts
181. qr-form.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/qr-form/qr-form.component.html
182. qr-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/qr-form/qr-form.component.ts
183. vietqr-form.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/vietqr-form/vietqr-form.component.html
184. vietqr-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/vietqr-form/vietqr-form.component.ts
185. preview-card.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-right/preview-card/preview-card.component.html
186. preview-card.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-right/preview-card/preview-card.component.spec.ts
187. preview-card.component.ts (7 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-right/preview-card/preview-card.component.ts
188. app-result.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/result/app-result/app-result.component.html
189. app-result.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/result/app-result/app-result.component.ts
190. error.component.html (19 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/result/error/error.component.html
191. error.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/result/error/error.component.spec.ts
192. error.component.ts (65 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/result/error/error.component.ts
193. support-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/result/error/support-dialog/support-dialog.html
194. support-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/result/error/support-dialog/support-dialog.ts
195. queuing.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/result/queuing/queuing.component.html
196. queuing.component.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/result/queuing/queuing.component.ts
197. success.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/result/success/success.component.html
198. success.component.ts (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/result/success/success.component.ts
199. bnpl-management.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/model/bnpl-management.ts
200. fundiin-management.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/model/fundiin-management.ts
201. homecredit-management.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/model/homecredit-management.ts
202. kbank-management.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/model/kbank-management.ts
203. auth.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/auth.service.ts
204. cancel-tran.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/cancel-tran.service.ts
205. change-color.service.ts (12 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/change-color.service.ts
206. close-dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/close-dialog.service.ts
207. confirm.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/confirm.service.ts
208. countdown.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/countdown.service.ts
209. counter.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/counter/counter.directive.spec.ts
210. counter.directive.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/counter/counter.directive.ts
211. currency-service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/currency-service.ts
212. data.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/data.service.ts
213. deep_link.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/deep_link.service.ts
214. dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/dialog.service.ts
215. digital-wallet.service.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/digital-wallet.service.ts
216. focus-input.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/focus-input.service.ts
217. format-date.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/format-date/format-date.directive.spec.ts
218. format-date.directive.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/format-date/format-date.directive.ts
219. handle_bnpl_token.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/handle_bnpl_token.service.ts
220. on-off-scroll.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/on-off-scroll.service.ts
221. overlay.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/overlay/overlay.component.html
222. overlay.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/overlay/overlay.component.spec.ts
223. overlay.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/overlay/overlay.component.ts
224. payment.service.ts (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/payment.service.ts
225. bank-amount.pipe.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/pipe/bank-amount.pipe.ts
226. preview-card.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/preview-card.service.ts
227. qr.service.ts (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/qr.service.ts
228. search-bank.service.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/search-bank.service.spec.ts
229. search-bank.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/search-bank.service.ts
230. step-screen-mobile.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/step-screen-mobile.service.ts
231. time-stop.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/time-stop.service.ts
232. token-main.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/token-main.service.ts
233. index.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/translate/index.ts
234. lang-en.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/translate/lang-en.ts
235. lang-vi.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/translate/lang-vi.ts
236. translate.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/translate/translate.pipe.ts
237. translate.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/translate/translate.service.ts
238. translations.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/translate/translations.ts
239. vietqr.service.ts (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/vietqr.service.ts
240. apps-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/util/apps-info.ts
241. apps-information.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/util/apps-information.ts
242. apps-infov2.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/util/apps-infov2.ts
243. banks-info.ts (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/util/banks-info.ts
244. error-handler.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/util/error-handler.ts
245. iso-ca-states.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/util/iso-ca-states.ts
246. iso-us-states.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/util/iso-us-states.ts
247. mobile-wallet-type.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/util/mobile-wallet-type.ts
248. util.ts (40 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/util/util.ts
249. apple.js (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/assets/script/apple.js
250. google-pay-intergrate.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/assets/script/google-pay-intergrate.js
251. google-pay-sdk.js (363 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/assets/script/google-pay-sdk.js
252. qrcode.js (67 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/assets/script/qrcode.js
253. environment.dev.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/environments/environment.dev.ts
254. environment.mtf.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/environments/environment.mtf.ts
255. environment.prod.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/environments/environment.prod.ts
256. environment.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/environments/environment.ts
257. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/index.html
258. karma.conf.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/karma.conf.js
259. main.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/main.ts
260. polyfills.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/polyfills.ts
261. kbank-policy.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/template/kbank-policy/kbank-policy.component.html
262. kbank-policy.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/template/kbank-policy/kbank-policy.component.ts
263. test.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/test.ts

DANH SÁCH FILE BỊ BỎ QUA:
--------------------------------------------------
1. apple-pay-sdk.js
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/assets/script/apple-pay-sdk.js
2. google-pay-sdk.*************.js
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/assets/script/google-pay-sdk.*************.js
3. samsung-pay-sdk.js
   Đường dẫn: /root/projects/onepay/paygate-generalv2/src/assets/script/samsung-pay-sdk.js

====================================================================================================
CHI TIẾT TỪNG FILE:
====================================================================================================

📁 FILE 1: 4en.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/4en.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 2: 4vn.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/4vn.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 3: app-routing.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/app-routing.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 4: app.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/app.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 5: app.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/app.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 6: app.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/app.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 83] return lang === this._translate.currentLang

== (2 điều kiện):
  1. [Dòng 62] 'vi' == params['locale']) {
  2. [Dòng 64] 'en' == params['locale']) {

!= (3 điều kiện):
  1. [Dòng 62] if (params['locale'] != null
  2. [Dòng 64] } else if (params['locale'] != null
  3. [Dòng 71] if (userLang != null

================================================================================

📁 FILE 7: app.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/app.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 8: cancel-tran.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/cancel-tran/cancel-tran.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 9: cancel-tran.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/cancel-tran/cancel-tran.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 10: cancel-tran.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/cancel-tran/cancel-tran.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 11: dialog-common.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/dialog-common/dialog-common.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 12: dialog-common.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/dialog-common/dialog-common.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 13: dialog-common.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/dialog-common/dialog-common.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 14: footer.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/footer/footer.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 5 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (5 điều kiện):
  1. [Dòng 17] <div class="d_language" [class.language_logo_full]="logoFull"  (click)="changeLanguage(_translate?.currentLang=='vi' ? 'en' : 'vi')">
  2. [Dòng 18] _translate?.currentLang=='en'"
  3. [Dòng 18] _translate?.currentLang=='vi'"
  4. [Dòng 18] _translate?.currentLang=='en' ? 'filter:grayscale(1)' : ''"
  5. [Dòng 19] _translate?.currentLang=='vi' ? 'filter:grayscale(1)' : ''"

================================================================================

📁 FILE 15: footer.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/footer/footer.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 16: footer.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/footer/footer.component.ts
📊 Thống kê: 8 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 4 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 35] if (this._paymentService.getInvoiceDetail() == null) {
  2. [Dòng 37] if ((dataPassed.status == '200'
  3. [Dòng 37] dataPassed.status == '201') && dataPassed.body != null) {
  4. [Dòng 40] dataPassed.body.themes.logo_full == 'True') {

!= (4 điều kiện):
  1. [Dòng 34] if (_idInvoice != null
  2. [Dòng 34] _idInvoice != 0) {
  3. [Dòng 37] dataPassed.body != null) {
  4. [Dòng 59] if (this._translate.currentLang != language) {

================================================================================

📁 FILE 17: template-qr.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/template-qr/template-qr.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 18: template-qr.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/template-qr/template-qr.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 19: template-qr.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/common/template-qr/template-qr.component.ts
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 43] if ((dataPassed.status == '200'
  2. [Dòng 43] dataPassed.status == '201') && dataPassed.body != null) {

!= (1 điều kiện):
  1. [Dòng 43] dataPassed.body != null) {

================================================================================

📁 FILE 20: main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/main.component.html
📊 Thống kê: 7 điều kiện duy nhất
   - === : 1 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 48] checkDupTran === false"

== (6 điều kiện):
  1. [Dòng 22] (click)="changeLanguage(_translate?.currentLang=='vi' ? 'en' : 'vi')">
  2. [Dòng 23] _translate?.currentLang=='en'"
  3. [Dòng 24] _translate?.currentLang=='en' ? 'filter:grayscale(1)' : ''"
  4. [Dòng 26] _translate?.currentLang=='vi'"
  5. [Dòng 27] _translate?.currentLang=='vi' ? 'filter:grayscale(1)' : ''"
  6. [Dòng 50] _paymentService.getCurrentPage()=='queuing'"

================================================================================

📁 FILE 21: main.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/main.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 22: main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/main.component.ts
📊 Thống kê: 14 điều kiện duy nhất
   - === : 2 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 149] params['code'] === '09') {
  2. [Dòng 162] params.name === 'CUSTOMER_INTIME')) {

== (6 điều kiện):
  1. [Dòng 111] if ((dataPassed.status == '200'
  2. [Dòng 111] dataPassed.status == '201') && dataPassed.body != null) {
  3. [Dòng 115] dataPassed.body.themes.logo_full == 'True') {
  4. [Dòng 140] payments[payments.length - 1].state == 'pending'
  5. [Dòng 142] payments[payments.length - 1].instrument.issuer.brand.id == 'amigo') {
  6. [Dòng 179] this.isThemeWhite = res.themeType == ThemeType.white

!= (6 điều kiện):
  1. [Dòng 102] if (this._idInvoice != null
  2. [Dòng 102] this._idInvoice != 0) {
  3. [Dòng 103] if (this._paymentService.getInvoiceDetail() != null) {
  4. [Dòng 111] dataPassed.body != null) {
  5. [Dòng 134] dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
  6. [Dòng 216] if (this._translate.currentLang != language) {

================================================================================

📁 FILE 23: bank-amount-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/dialog/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 24: bank-amount-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/dialog/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
📊 Thống kê: 35 điều kiện duy nhất
   - === : 1 lần
   - == : 34 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 59] this.isHideBullet = count === 1

== (34 điều kiện):
  1. [Dòng 46] if (this.locale == 'en') {
  2. [Dòng 64] if (name == 'MAFC')
  3. [Dòng 70] if (bankId == 3
  4. [Dòng 70] bankId == 61
  5. [Dòng 71] bankId == 8
  6. [Dòng 71] bankId == 49
  7. [Dòng 72] bankId == 48
  8. [Dòng 73] bankId == 10
  9. [Dòng 73] bankId == 53
  10. [Dòng 74] bankId == 17
  11. [Dòng 74] bankId == 65
  12. [Dòng 75] bankId == 23
  13. [Dòng 75] bankId == 52
  14. [Dòng 76] bankId == 27
  15. [Dòng 76] bankId == 66
  16. [Dòng 77] bankId == 9
  17. [Dòng 77] bankId == 54
  18. [Dòng 78] bankId == 37
  19. [Dòng 79] bankId == 38
  20. [Dòng 80] bankId == 39
  21. [Dòng 81] bankId == 40
  22. [Dòng 82] bankId == 42
  23. [Dòng 83] bankId == 44
  24. [Dòng 84] bankId == 72
  25. [Dòng 85] bankId == 59
  26. [Dòng 88] bankId == 51
  27. [Dòng 89] bankId == 64
  28. [Dòng 90] bankId == 58
  29. [Dòng 91] bankId == 56
  30. [Dòng 94] bankId == 55
  31. [Dòng 95] bankId == 60
  32. [Dòng 96] bankId == 68
  33. [Dòng 97] bankId == 74
  34. [Dòng 98] bankId == 75 //MAFC Napas

================================================================================

📁 FILE 25: confirm-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/dialog/confirm-dialog/confirm-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 26: confirm-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/dialog/confirm-dialog/confirm-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 27: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 28: policy-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 29: policy-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 30: menu.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/menu.component.html
📊 Thống kê: 89 điều kiện duy nhất
   - === : 18 lần
   - == : 62 lần
   - !== : 2 lần
   - != : 7 lần
--------------------------------------------------------------------------------

=== (18 điều kiện):
  1. [Dòng 311] token)) && (token || (type === 1
  2. [Dòng 312] (type == 2 && token)) && (type === 2
  3. [Dòng 312] type === '2'
  4. [Dòng 314] (type == 7 && token)) && (type === 7
  5. [Dòng 315] d_qr) && (type === 4
  6. [Dòng 316] (type === 5
  7. [Dòng 332] *ngIf="token || (type === 1
  8. [Dòng 336] type === 1
  9. [Dòng 349] type === 2
  10. [Dòng 388] type === 7
  11. [Dòng 398] type === 4
  12. [Dòng 409] type === 5
  13. [Dòng 427] type === 3"
  14. [Dòng 472] bnpl.code === 'kbank'"
  15. [Dòng 475] bnpl.code === 'homepaylater'"
  16. [Dòng 557] <div *ngIf="(type === 5
  17. [Dòng 626] type === 5"
  18. [Dòng 635] d_vrbank===true"

== (62 điều kiện):
  1. [Dòng 2] onePaymentMethod == true
  2. [Dòng 2] token == true"
  3. [Dòng 93] stepScreenMobile == 1"
  4. [Dòng 94] stepScreenMobile == 2"
  5. [Dòng 95] onePaymentMethod == false
  6. [Dòng 95] _auth == 0
  7. [Dòng 98] stepScreenMobile == 1) || d_desktop" [class.scroll-main-content]="d_mobile">
  8. [Dòng 108] !token) || (type == 1
  9. [Dòng 129] !token) || (type == 6
  10. [Dòng 140] type == 2)"
  11. [Dòng 148] !token) || (type == 3
  12. [Dòng 159] !token) || (type == 5
  13. [Dòng 182] method?.trim()=='International'"
  14. [Dòng 194] method.trim()=='ApplePay'"
  15. [Dòng 206] method.trim()=='GooglePay'"
  16. [Dòng 218] method.trim()=='SamsungPay'"
  17. [Dòng 228] method?.trim()=='Domestic'"
  18. [Dòng 238] method?.trim()=='QR'"
  19. [Dòng 247] method?.trim()=='Paypal'"
  20. [Dòng 257] method?.trim()=='VietQR'
  21. [Dòng 266] method?.trim()=='Bnpl'
  22. [Dòng 283] stepScreenMobile == 1
  23. [Dòng 303] stepScreenMobile == 2) || d_desktop || onePaymentMethod || token">
  24. [Dòng 312] !token)  || (type == 2
  25. [Dòng 313] !token) || (type == PaymentTypeMobile.ApplePay
  26. [Dòng 313] type == PaymentTypeMobile.GooglePay
  27. [Dòng 313] type == PaymentTypeMobile.SamsungPay)) && ((type == PaymentTypeMobile.ApplePay
  28. [Dòng 313] type == PaymentTypeMobile.SamsungPay) || (onePaymentMethod
  29. [Dòng 314] !token) || (type == 7
  30. [Dòng 359] type == PaymentTypeMobile.SamsungPay)">
  31. [Dòng 361] *ngIf="(type == PaymentTypeMobile.ApplePay
  32. [Dòng 361] type == PaymentTypeMobile.SamsungPay) || onePaymentMethod">
  33. [Dòng 368] <applepay-main *ngIf="(type == PaymentTypeMobile.ApplePay) || (onePaymentMethod
  34. [Dòng 374] <googlepay-main *ngIf="(type == PaymentTypeMobile.GooglePay) || (onePaymentMethod
  35. [Dòng 377] <samsungpay-main *ngIf="(type == PaymentTypeMobile.SamsungPay) || (onePaymentMethod
  36. [Dòng 459] sortMethodArray[i].trim()=='Bnpl'
  37. [Dòng 460] <div *ngIf="((onePaymentMethod == true
  38. [Dòng 460] d_bnpl) || (onePaymentMethod == false
  39. [Dòng 467] bnpl.status == 'disabled'"
  40. [Dòng 486] providerType == bnpl.code"
  41. [Dòng 487] bnpl.status == 'disabled'
  42. [Dòng 491] d_bnpl_number == 1"
  43. [Dòng 510] type==5 && providerType==bnpl.code
  44. [Dòng 510] providerType==bnpl.code"
  45. [Dòng 531] bnpl.code == 'kbank'
  46. [Dòng 536] bnpl.code == 'insta'
  47. [Dòng 541] bnpl.code == 'homepaylater'
  48. [Dòng 552] bnpl.code == 'kredivo'
  49. [Dòng 557] bnpl.status == 'active'
  50. [Dòng 558] providerType == bnpl.code)
  51. [Dòng 559] || (d_bnpl_number == 1
  52. [Dòng 559] providerType == bnpl.code))
  53. [Dòng 561] || (bnpl.status == 'active'
  54. [Dòng 561] d_bnpl_number == 1)">
  55. [Dòng 563] bnpl.code == 'insta'"
  56. [Dòng 572] bnpl.code == 'kbank'"
  57. [Dòng 579] bnpl.code == 'homepaylater'"
  58. [Dòng 587] bnpl.code == 'kredivo'"
  59. [Dòng 594] bnpl.code == 'fundiin'"
  60. [Dòng 627] _auth == 1
  61. [Dòng 627] [class.enable_form]="!onePaymentMethod || (onePaymentMethod && d_bnpl)" *ngIf="_auth == 1 && ((onePaymentMethod == true
  62. [Dòng 627] d_bnpl_number == 1)

!== (2 điều kiện):
  1. [Dòng 479] bnpl.code !== 'kbank'
  2. [Dòng 498] bnpl.code !== 'kbank'"

!= (7 điều kiện):
  1. [Dòng 2] _auth != 0
  2. [Dòng 3] _auth != 0"
  3. [Dòng 459] _auth != '1' -->
  4. [Dòng 461] _auth != '1'"
  5. [Dòng 479] bnpl.code != 'homepaylater'"
  6. [Dòng 536] <div *ngIf="bnpl.code == 'insta' && ((selectedBnpl != bnpl) || (d_bnpl_number == 1))"
  7. [Dòng 541] <div *ngIf="bnpl.code == 'homepaylater' && ((selectedBnpl != bnpl) || (d_bnpl_number == 1))"

================================================================================

📁 FILE 31: menu.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/menu.component.ts
📊 Thống kê: 213 điều kiện duy nhất
   - === : 33 lần
   - == : 106 lần
   - !== : 6 lần
   - != : 68 lần
--------------------------------------------------------------------------------

=== (33 điều kiện):
  1. [Dòng 353] if (document.visibilityState === 'visible') {
  2. [Dòng 516] if (Number(selected) === 1
  3. [Dòng 516] this.type === 1) {
  4. [Dòng 520] if (Number(selected) === 2
  5. [Dòng 520] this.type === 2) {
  6. [Dòng 925] oneBnplProvider === 1) {
  7. [Dòng 1086] if (this.type === 1) {
  8. [Dòng 1090] if (this.type === 2) {
  9. [Dòng 1233] this.homecreditProvider = this.bankAmountArr.find(e => e.code === 'homepaylater');
  10. [Dòng 1238] this.kredivoProvider = this.bankAmountArr.find(e => e.code === 'kredivo');
  11. [Dòng 1242] this.fundiinProvider = this.bankAmountArr.find(e => e.code === 'fundiin');
  12. [Dòng 1294] this.bnpls.push(this.sublist.find(e => e.name === 'KBank Pay Later'));
  13. [Dòng 1297] this.bnpls.push(this.sublist.find(e => e.name === 'Home PayLater'));
  14. [Dòng 1300] this.bnpls.push(this.sublist.find(e => e.name === 'Kredivo'));
  15. [Dòng 1303] this.bnpls.push(this.sublist.find(e => e.name === 'Insta'));
  16. [Dòng 1306] this.bnpls.push(this.sublist.find(e => e.name === 'Fundiin'));
  17. [Dòng 1359] this.cardInternations['number_card'] = Object.values(this.cardInternations).filter(value => value === true).length
  18. [Dòng 1398] this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_bnpl + this.d_mobile_wallet + this.d_vietqr_1 === 1
  19. [Dòng 1451] if (count === 1
  20. [Dòng 1456] if (offBanksArr[i] === this.lastDomescard) {
  21. [Dòng 1473] if (this._res.state === 'unpaid'
  22. [Dòng 1473] this._res.state === 'not_paid') {
  23. [Dòng 1608] if ('op' === auth
  24. [Dòng 1653] } else if ('bank' === auth
  25. [Dòng 1658] if (approval.method === 'REDIRECT') {
  26. [Dòng 1661] } else if (approval.method === 'POST_REDIRECT') {
  27. [Dòng 2414] if (this.timeLeftPaypal === 0) {
  28. [Dòng 2429] this.token)) && (this.token || (this.type === 1
  29. [Dòng 2430] (this.type == 2 && this.token)) && (this.type === 2
  30. [Dòng 2430] this.type === '2'
  31. [Dòng 2432] (this.type == 7 && this.token)) && (this.type === 7
  32. [Dòng 2433] this.d_qr) && (this.type === 4
  33. [Dòng 2434] (this.type === 5

== (106 điều kiện):
  1. [Dòng 537] if (!isNaN(_re.status) && (_re.status == '200'
  2. [Dòng 537] _re.status == '201') && _re.body != null) {
  3. [Dòng 542] if (('closed' == this._res_polling.state
  4. [Dòng 542] 'canceled' == this._res_polling.state
  5. [Dòng 542] 'expired' == this._res_polling.state)
  6. [Dòng 565] } else if ('paid' == this._res_polling.state) {
  7. [Dòng 581] this._res_polling.payments == null
  8. [Dòng 590] if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'authorization_required') {
  9. [Dòng 594] } else if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  10. [Dòng 601] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  11. [Dòng 603] this._paymentService.getCurrentPage() == 'enter_card'
  12. [Dòng 606] } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
  13. [Dòng 606] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
  14. [Dòng 623] } else if ('not_paid' == this._res_polling.state) {
  15. [Dòng 635] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
  16. [Dòng 797] if (auth == 'auth') {
  17. [Dòng 799] detail.merchant.id == 'AMWAY') {
  18. [Dòng 831] if (this.checkInvoiceState() == 1) {
  19. [Dòng 844] _re.body?.merchant?.qr_version == '2');
  20. [Dòng 849] if (domescardVersion == '2'
  21. [Dòng 857] if (_re.status == '200'
  22. [Dòng 857] _re.status == '201') {
  23. [Dòng 865] this._res.payments[this._res.payments.length - 1].state == 'pending'
  24. [Dòng 866] this._res.payments[this._res.payments.length - 1].instrument.type == 'applepay') {
  25. [Dòng 889] if (this.themeConfig.default_method == 'International'
  26. [Dòng 891] } else if (this.themeConfig.default_method == 'Domestic'
  27. [Dòng 893] } else if (this.themeConfig.default_method == 'QR'
  28. [Dòng 895] } else if (this.themeConfig.default_method == 'Paypal'
  29. [Dòng 980] if ((dataPassed.status == '200'
  30. [Dòng 980] dataPassed.status == '201') && dataPassed.body != null) {
  31. [Dòng 983] dataPassed.body.themes.logo_full == 'True') {
  32. [Dòng 1004] if (this.type == 3) this.setIntervalPaypal()
  33. [Dòng 1036] if (value == true) {
  34. [Dòng 1098] if (('closed' == this._res.state
  35. [Dòng 1098] 'canceled' == this._res.state
  36. [Dòng 1098] 'expired' == this._res.state
  37. [Dòng 1098] 'paid' == this._res.state)
  38. [Dòng 1102] if ('paid' == this._res.state
  39. [Dòng 1146] this.stopCounter == 'stop')) {  // Check if X is true
  40. [Dòng 1309] if (this.onePaymentMethod == true
  41. [Dòng 1309] this.d_bnpl_number == 1
  42. [Dòng 1309] this.d_bnpl == 1) {
  43. [Dòng 1361] if (this.d_amigo == true
  44. [Dòng 1361] this.d_insta == false
  45. [Dòng 1361] this.d_instaplus == false) {
  46. [Dòng 1368] if (this.version2 == "2") {
  47. [Dòng 1390] if (this.d_amigo_number == 0
  48. [Dòng 1390] this.d_insta_number == 1) {
  49. [Dòng 1393] else if (this.d_amigo_number == 0
  50. [Dòng 1393] this.d_instaplus_number == 1) {
  51. [Dòng 1399] if ((this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_bnpl + this.d_mobile_wallet + this.d_vietqr_1) == 0
  52. [Dòng 1424] this._auth == 0) {
  53. [Dòng 1435] this._res.themes.techcombankCard == false ? false : true
  54. [Dòng 1437] if (count == 2
  55. [Dòng 1474] if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
  56. [Dòng 1474] this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
  57. [Dòng 1476] if (this._res.payments[this._res.payments.length - 1].state == 'approved'
  58. [Dòng 1482] } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
  59. [Dòng 1496] || (responseCode == '1'
  60. [Dòng 1496] instrumentType == 'vietqr'))
  61. [Dòng 1529] if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required') {
  62. [Dòng 1536] } else if (this._res.payments[this._res.payments.length - 1].state == 'pending') {
  63. [Dòng 1542] } else if (this._res.payments[this._res.payments.length - 1].state == 'canceled') {
  64. [Dòng 1546] this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  65. [Dòng 1548] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id == 'vietqr'
  66. [Dòng 1555] } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  67. [Dòng 1555] this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
  68. [Dòng 1591] if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  69. [Dòng 1595] } else if (idBrand == 'atm'
  70. [Dòng 1678] else if (idBrand == 'kbank'
  71. [Dòng 1782] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
  72. [Dòng 1797] if (this._res.payments[this._res.payments.length - 1].state == 'pending'
  73. [Dòng 1807] if ('paid' == this._res.state) {
  74. [Dòng 1834] 'canceled' == this._res.state) {
  75. [Dòng 1853] this._res.payments[this._res.payments.length - 1].state == 'pending') {
  76. [Dòng 1874] if (this.d_inter == 1) {
  77. [Dòng 1881] } else if (this.type == 1) {
  78. [Dòng 1988] if (type == 'qrv1') {
  79. [Dòng 1998] if (type == 'mobile') {
  80. [Dòng 2000] e.type == 'ewallet'
  81. [Dòng 2000] e.code == 'momo')) {
  82. [Dòng 2008] } else if (type == 'desktop') {
  83. [Dòng 2009] e.type == 'vnpayqr') || (regex.test(strTest)
  84. [Dòng 2132] if (this._res_post.state == 'approved'
  85. [Dòng 2132] this._res_post.state == 'failed') {
  86. [Dòng 2141] } else if (this._res_post.state == 'authorization_required') {
  87. [Dòng 2142] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  88. [Dòng 2156] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  89. [Dòng 2232] if (bankid == 2
  90. [Dòng 2232] bankid == 67) {
  91. [Dòng 2233] if (isBankOff && ((bankid == '2'
  92. [Dòng 2233] !this.isOffTechcombank) || (bankid == '67'
  93. [Dòng 2285] if (res.status == '200'
  94. [Dòng 2285] res.status == '201') {
  95. [Dòng 2315] return (this.currentMethod == 7) // PTTT riêng VietQR
  96. [Dòng 2316] || (this.currentMethod == 2
  97. [Dòng 2316] this._b == 'vietqr')    // PTTT domes vietqr
  98. [Dòng 2373] this.isThemeWhite = res.themeType == ThemeType.white
  99. [Dòng 2429] !this.token) || (this.type == 1
  100. [Dòng 2430] !this.token)  || (this.type == 2
  101. [Dòng 2431] !this.token) || (this.type == PaymentTypeMobile.ApplePay
  102. [Dòng 2431] this.type == PaymentTypeMobile.GooglePay
  103. [Dòng 2431] this.type == PaymentTypeMobile.SamsungPay)) && ((this.type == PaymentTypeMobile.ApplePay
  104. [Dòng 2431] this.type == PaymentTypeMobile.SamsungPay) || (this.onePaymentMethod
  105. [Dòng 2432] !this.token) || (this.type == 7
  106. [Dòng 2458] if (data._locale == 'en') {

!== (6 điều kiện):
  1. [Dòng 948] if (this.qr_version2 !== 'qrV1'
  2. [Dòng 948] this.qr_version2 !== '') {
  3. [Dòng 1624] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 2053] if (_val !== 3) {
  5. [Dòng 2057] this._util.getElementParams(this.currentUrl, 't_id') !== '') {
  6. [Dòng 2331] if (parseInt(oldStep) !== 1

!= (68 điều kiện):
  1. [Dòng 489] this.currentMethod != selected) {
  2. [Dòng 530] if (this._idInvoice != null
  3. [Dòng 530] this._paymentService.getState() != 'error') {
  4. [Dòng 536] if (this._paymentService.getCurrentPage() != 'otp') {
  5. [Dòng 537] _re.body != null) {
  6. [Dòng 543] this._res_polling.links != null
  7. [Dòng 543] this._res_polling.links.merchant_return != null //
  8. [Dòng 546] this._util.getElementParams(url_redirect, 'vpc_TxnResponseCode') != '99') {
  9. [Dòng 581] } else if (this._res_polling.merchant != null
  10. [Dòng 581] this._res_polling.merchant_invoice_reference != null
  11. [Dòng 583] } else if (this._res_polling.payments != null
  12. [Dòng 603] this._res_polling.payments[this._res_polling.payments.length - 1].instrument.brand != 'amigo') {
  13. [Dòng 607] this._res_polling.links.merchant_return != null//
  14. [Dòng 626] this._res_polling.payments != null
  15. [Dòng 634] if (this._res_polling.payments != null
  16. [Dòng 638] t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
  17. [Dòng 806] this.type.toString().length != 0) {
  18. [Dòng 812] if (params['locale'] != null) {
  19. [Dòng 819] if ('otp' != this._paymentService.getCurrentPage()) {
  20. [Dòng 823] if (this._paymentService.getInvoiceDetail() != null) {
  21. [Dòng 849] params['b'] != ''
  22. [Dòng 849] params['b'] != undefined) {
  23. [Dòng 865] if (this._res.payments != null
  24. [Dòng 868] this._res.payments[this._res.payments.length - 1].instrument.brand != 'amigo') {
  25. [Dòng 980] dataPassed.body != null) {
  26. [Dòng 999] if (this._idInvoice != null) {
  27. [Dòng 1031] const tokenListsFilter = tokenLists.filter(item => item.id != tokenData);
  28. [Dòng 1099] this._res.links != null
  29. [Dòng 1099] this._res.links.merchant_return != null
  30. [Dòng 1447] if (count != 1) {
  31. [Dòng 1463] if (this._res.merchant != null
  32. [Dòng 1463] this._res.merchant_invoice_reference != null) {
  33. [Dòng 1466] if (this._res.merchant.address_details != null) {
  34. [Dòng 1474] this._res.links != null//
  35. [Dòng 1524] } else if (this._res.payments != null
  36. [Dòng 1547] this._res.payments[this._res.payments.length - 1].instrument != null
  37. [Dòng 1547] this._res.payments[this._res.payments.length - 1].instrument.issuer != null
  38. [Dòng 1548] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
  39. [Dòng 1552] if (this.type != 7) {
  40. [Dòng 1557] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
  41. [Dòng 1558] this._res.payments[this._res.payments.length - 1].links != null
  42. [Dòng 1558] this._res.payments[this._res.payments.length - 1].links.cancel != null
  43. [Dòng 1558] this._res.payments[this._res.payments.length - 1].links.cancel.href != null
  44. [Dòng 1576] this._res.payments[this._res.payments.length - 1].links.update != null
  45. [Dòng 1576] this._res.payments[this._res.payments.length - 1].links.update.href != null) {
  46. [Dòng 1595] this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
  47. [Dòng 1596] this._res.payments[this._res.payments.length - 1].authorization != null
  48. [Dòng 1596] this._res.payments[this._res.payments.length - 1].authorization.links != null) {
  49. [Dòng 1608] this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
  50. [Dòng 1608] this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
  51. [Dòng 1611] if (this._res.payments[this._res.payments.length - 1].authorization != null
  52. [Dòng 1611] this._res.payments[this._res.payments.length - 1].authorization.links != null
  53. [Dòng 1617] auth = paramUserName != null ? paramUserName : ''
  54. [Dòng 1691] if (this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
  55. [Dòng 1832] this._res.links.merchant_return != null //
  56. [Dòng 1956] if (!(strInstrument != null
  57. [Dòng 1973] if (this._translate.currentLang != language) {
  58. [Dòng 2000] e.type != 'ewallet') || (regex.test(strTest)
  59. [Dòng 2059] } else if (this._res.payments != null) {
  60. [Dòng 2134] if (this._res_post.return_url != null) {
  61. [Dòng 2136] } else if (this._res_post.links != null
  62. [Dòng 2136] this._res_post.links.merchant_return != null
  63. [Dòng 2136] this._res_post.links.merchant_return.href != null) {
  64. [Dòng 2199] _re.body?.state != 'canceled')
  65. [Dòng 2286] if (res.body != null
  66. [Dòng 2286] res.body.links != null
  67. [Dòng 2286] res.body.links.merchant_return != null
  68. [Dòng 2287] res.body.links.merchant_return.href != null) {

================================================================================

📁 FILE 32: samsung-pay-button-op.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 33: samsung-pay-button-op.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.ts
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 128] if (GGPaySDKScript.readyState === "loaded"
  2. [Dòng 128] GGPaySDKScript.readyState === "complete") {

== (1 điều kiện):
  1. [Dòng 63] serviceId: this.environment == 'PRODUCTION'? this.serviceIdProd : this.serviceIdTest

================================================================================

📁 FILE 34: types-samsung-pay.d.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/types-samsung-pay.d.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 35: amigo-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/amigo/amigo-form.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 31] bnplDetail.method == 'SP'"
  2. [Dòng 49] bnplDetail.method == 'PL'"

!= (3 điều kiện):
  1. [Dòng 3] selectedBnpl.status != 'disabled'"
  2. [Dòng 45] bnplDetail.method != 'SP'"
  3. [Dòng 63] bnplDetail.method != 'PL'"

================================================================================

📁 FILE 36: amigo-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/amigo/amigo-form.component.ts
📊 Thống kê: 24 điều kiện duy nhất
   - === : 2 lần
   - == : 17 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 196] return index === array.findIndex(obj => {
  2. [Dòng 197] return JSON.stringify(obj) === _value

== (17 điều kiện):
  1. [Dòng 90] _re.code == '0'
  2. [Dòng 127] packageItem.product_code == productCode) {
  3. [Dòng 145] a.product_code == 'SP') {
  4. [Dòng 158] a.product_code == 'PL') {
  5. [Dòng 207] if (this.selectedIndex == 0
  6. [Dòng 211] } else if ((this.selectedIndex == 1
  7. [Dòng 211] this.payLaterSubmit) || (this.selectedIndex == 0
  8. [Dòng 214] item.prepaid_percent == this.selectedPrePaid
  9. [Dòng 214] item.installment_month == this.selectedPayLater) {
  10. [Dòng 251] if (string == 'SP') {
  11. [Dòng 254] } else if (string == 'PL') {
  12. [Dòng 298] if(this._locale == 'en'){
  13. [Dòng 308] if (this._res_post.state == 'approved'
  14. [Dòng 308] this._res_post.state == 'failed') {
  15. [Dòng 314] if (this._res_post.state == 'failed') {
  16. [Dòng 339] } else if (this._res_post.state == 'authorization_required') {
  17. [Dòng 340] if ('REDIRECT' == this._res_post.authorization.links.approval.method) {

!= (5 điều kiện):
  1. [Dòng 238] 'total_paid': this.data?.amount ? (item.diff_amount !=0 ? this._util.formatMoney(parseFloat(item.diff_amount) + parseFloat(this.data?.amount), 0, '.', ',') : this._util.formatMoney(parseFloat(this.data?.amount), 0, '.', ',')) :0,
  2. [Dòng 309] if (this._res_post.return_url != null) {
  3. [Dòng 311] } else if (this._res_post.links != null
  4. [Dòng 311] this._res_post.links.merchant_return != null
  5. [Dòng 311] this._res_post.links.merchant_return.href != null) {

================================================================================

📁 FILE 37: bnpl-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/bnpl-main.component.html
📊 Thống kê: 22 điều kiện duy nhất
   - === : 2 lần
   - == : 16 lần
   - !== : 2 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 35] bnpl.code === 'kbank'"
  2. [Dòng 38] bnpl.code === 'homepaylater'"

== (16 điều kiện):
  1. [Dòng 26] bnpl.status == 'disabled'"
  2. [Dòng 27] bnpl.status == 'active'"
  3. [Dòng 28] bnpl.status == 'disabled')" [@listAnimation]>
  4. [Dòng 48] bnpl.code == 'kbank'
  5. [Dòng 51] bnpl.code == 'insta'"
  6. [Dòng 59] bnpl.code == 'homepaylater'"
  7. [Dòng 60] bnpl.status == 'disabled'
  8. [Dòng 73] bnpl.code == 'kredivo'
  9. [Dòng 89] [class.bnpl-active-item]="bnpl.status == 'active'" (click)="onClick(bnpl.status == 'disabled')">
  10. [Dòng 128] selectedBnpl.code == 'insta'"
  11. [Dòng 129] bnpls?.length == 1"
  12. [Dòng 148] selectedBnpl.code == 'kbank'"
  13. [Dòng 165] selectedBnpl.code == 'homepaylater'"
  14. [Dòng 187] selectedBnpl.code == 'kredivo'"
  15. [Dòng 205] selectedBnpl.code == 'fundiin'"
  16. [Dòng 233] _auth == 1"

!== (2 điều kiện):
  1. [Dòng 30] bnpl.code !== 'kredivo'"
  2. [Dòng 97] bnpl.code !== 'kbank'

!= (2 điều kiện):
  1. [Dòng 1] _auth != 1"
  2. [Dòng 97] bnpl.code != 'homepaylater'"

================================================================================

📁 FILE 38: bnpl-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/bnpl-main.component.ts
📊 Thống kê: 14 điều kiện duy nhất
   - === : 10 lần
   - == : 3 lần
   - !== : 1 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (10 điều kiện):
  1. [Dòng 164] this.homecreditProvider = this.bankAmountArr.find(e => e.code === 'homepaylater');
  2. [Dòng 169] this.kredivoProvider = this.bankAmountArr.find(e => e.code === 'kredivo');
  3. [Dòng 173] this.fundiinProvider = this.bankAmountArr.find(e => e.code === 'fundiin');
  4. [Dòng 185] this.bnpls.push(this.sublist.find(e => e.name === 'KBank Pay Later'));
  5. [Dòng 188] this.bnpls.push(this.sublist.find(e => e.name === 'Home PayLater'));
  6. [Dòng 191] this.bnpls.push(this.sublist.find(e => e.name === 'Kredivo'));
  7. [Dòng 194] this.bnpls.push(this.sublist.find(e => e.name === 'Insta'));
  8. [Dòng 197] this.bnpls.push(this.sublist.find(e => e.name === 'Fundiin'));
  9. [Dòng 211] if (!(bnpl.status === 'disabled')) this.oneProvider = true
  10. [Dòng 212] bnpl.status === 'disabled');

== (3 điều kiện):
  1. [Dòng 209] if (this?.bnpls?.length == 1) {
  2. [Dòng 275] let bnplDetail = this.bnpls.find(o => o.status == 'active'
  3. [Dòng 275] detail == o.code);

!== (1 điều kiện):
  1. [Dòng 258] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

================================================================================

📁 FILE 39: circle-process-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/dialog/circle-process-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 40: dialog-delete-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/fundiin/dialog-delete-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 41: dialog-policy.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/fundiin/dialog-policy/dialog-policy.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 42: dialog-policy.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/fundiin/dialog-policy/dialog-policy.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 43: dialog-policy.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/fundiin/dialog-policy/dialog-policy.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 44: fundiin-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/fundiin/fundiin-form.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 4 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 9] _locale == 'vi'"

!= (4 điều kiện):
  1. [Dòng 3] selectedBnpl.status != 'disabled'"
  2. [Dòng 10] _locale != 'vi'"
  3. [Dòng 68] document.activeElement.id!='fullName'"
  4. [Dòng 85] document.activeElement.id!='phoneNumber'"

================================================================================

📁 FILE 45: fundiin-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/fundiin/fundiin-form.component.ts
📊 Thống kê: 22 điều kiện duy nhất
   - === : 5 lần
   - == : 7 lần
   - !== : 2 lần
   - != : 8 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 168] params.key === 'Backspace') {
  2. [Dòng 169] if (type === 'email') {
  3. [Dòng 174] if (type === 'phoneNumber') {
  4. [Dòng 298] if (name === 'phoneNumber') {
  5. [Dòng 300] } else if (name === 'email') {

== (7 điều kiện):
  1. [Dòng 188] this.fullNameInvalidMessage = this.fundiinDetail.fullName?.length == 0 ? this._translate.instant('fill_full_name')
  2. [Dòng 195] this.fundiinDetail.email?.length == 0 ? this._translate.instant('fill_email')
  3. [Dòng 371] if (this._res_post.state == 'approved'
  4. [Dòng 371] this._res_post.state == 'failed') {
  5. [Dòng 377] if (this._res_post.state == 'failed') {
  6. [Dòng 402] } else if (this._res_post.state == 'authorization_required') {
  7. [Dòng 403] if ('REDIRECT' == this._res_post.authorization.links.approval.method) {

!== (2 điều kiện):
  1. [Dòng 136] if (this.fundiinDetail.phoneNumber && ((this.fundiinDetail.phoneNumber.length !== 10
  2. [Dòng 136] this.fundiinDetail.phoneNumber.length !== 11)

!= (8 điều kiện):
  1. [Dòng 204] if ((this.fundiinDetail.phoneNumber?.length != 10
  2. [Dòng 204] this.fundiinDetail.phoneNumber?.length != 11) || !this.fundiinDetail.phoneNumber.match(/^[*]{8,9
  3. [Dòng 209] (this.fundiinDetail.phoneNumber?.length != 10
  4. [Dòng 209] this.fundiinDetail.phoneNumber?.length != 11) ?
  5. [Dòng 372] if (this._res_post.return_url != null) {
  6. [Dòng 374] } else if (this._res_post.links != null
  7. [Dòng 374] this._res_post.links.merchant_return != null
  8. [Dòng 374] this._res_post.links.merchant_return.href != null) {

================================================================================

📁 FILE 46: select-data.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/fundiin/select-data/select-data.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 47: select-data.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/fundiin/select-data/select-data.ts
📊 Thống kê: 8 điều kiện duy nhất
   - === : 5 lần
   - == : 1 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 28] if (this.id === 'city') {
  2. [Dòng 31] this.priorityCity.push(this.dataSource.find(({ Id }) => Id === "01"));
  3. [Dòng 32] this.priorityCity.push(this.dataSource.find(({ Id }) => Id === "79"));
  4. [Dòng 34] } else if (this.id === 'district') {
  5. [Dòng 39] } else if (this.id === 'ward') {

== (1 điều kiện):
  1. [Dòng 63] if (bnpl.status == 'disabled') {

!== (2 điều kiện):
  1. [Dòng 33] this.filteredData = this.dataSource.filter(i => i.Id !== "01"
  2. [Dòng 33] i.Id !== "79").sort((a, b) => a.Name.localeCompare(b.Name));

================================================================================

📁 FILE 48: dialog-delete-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/homecredit/dialog-delete-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 49: dialog-policy.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/homecredit/dialog-policy/dialog-policy.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 50: dialog-policy.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/homecredit/dialog-policy/dialog-policy.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 51: dialog-policy.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/homecredit/dialog-policy/dialog-policy.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 52: homecredit-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/homecredit/homecredit-form.component.html
📊 Thống kê: 10 điều kiện duy nhất
   - === : 2 lần
   - == : 5 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 188] object.id === value ? 'select-option-active' : ''"
  2. [Dòng 190] object.id === value"

== (5 điều kiện):
  1. [Dòng 202] value == 'add' ? 'select-option-active' : ''"
  2. [Dòng 203] value == 'add'"
  3. [Dòng 210] value=='add'"
  4. [Dòng 346] value == 'add') || !isOnepayPolicy" [disabled]="(isInvalid()
  5. [Dòng 346] value == 'add') || !isOnepayPolicy">

!= (3 điều kiện):
  1. [Dòng 3] selectedBnpl.status != 'disabled'"
  2. [Dòng 252] homecreditDetail['phoneNumber'].length != 10
  3. [Dòng 252] homecreditDetail['phoneNumber'].length != 11)

================================================================================

📁 FILE 53: homecredit-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/homecredit/homecredit-form.component.ts
📊 Thống kê: 39 điều kiện duy nhất
   - === : 7 lần
   - == : 16 lần
   - !== : 1 lần
   - != : 15 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 162] params.key === 'Backspace') {
  2. [Dòng 163] if (type === 'email') {
  3. [Dòng 168] if (type === 'phoneNumber') {
  4. [Dòng 321] (key === 8
  5. [Dòng 321] key === 46
  6. [Dòng 324] (key === 46
  7. [Dòng 405] if (this.value === 'add') {

== (16 điều kiện):
  1. [Dòng 230] if (response.status == '200'
  2. [Dòng 230] response.status == '201') {
  3. [Dòng 233] this.listTokenBnpl.length == 0) {
  4. [Dòng 236] if (this.value == object.id) {
  5. [Dòng 352] if (name == 'email') {
  6. [Dòng 354] } else if (name == 'phoneNumber') {
  7. [Dòng 361] if (name == 'phoneNumber') {
  8. [Dòng 368] if (name == 'fullname') {
  9. [Dòng 371] if (name == 'email'
  10. [Dòng 443] if (this._res_post.state == 'approved'
  11. [Dòng 443] this._res_post.state == 'failed') {
  12. [Dòng 449] if (this._res_post.state == 'failed') {
  13. [Dòng 474] } else if (this._res_post.state == 'authorization_required') {
  14. [Dòng 475] if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  15. [Dòng 536] this._res_post.state == 'authorization_required') {
  16. [Dòng 606] this._res_post.code == 'KB-02') {

!== (1 điều kiện):
  1. [Dòng 558] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (15 điều kiện):
  1. [Dòng 109] this.value = (this.listTokenBnpl.length != 0) ? this.listTokenBnpl[0]['id'] : "add";
  2. [Dòng 141] if ((this.homecreditDetail['phoneNumber'].length != 10
  3. [Dòng 141] this.homecreditDetail['phoneNumber'].length != 11)
  4. [Dòng 232] this.listTokenBnpl = this.listTokenBnpl.filter(item => item.id != result.id);
  5. [Dòng 444] if (this._res_post.return_url != null) {
  6. [Dòng 446] } else if (this._res_post.links != null
  7. [Dòng 446] this._res_post.links.merchant_return != null
  8. [Dòng 446] this._res_post.links.merchant_return.href != null) {
  9. [Dòng 541] if (this._res_post.authorization != null
  10. [Dòng 541] this._res_post.authorization.links != null
  11. [Dòng 546] if (this._res_post.links != null
  12. [Dòng 546] this._res_post.links.cancel != null) {
  13. [Dòng 553] this._res_post.authorization.links.approval != null
  14. [Dòng 553] this._res_post.authorization.links.approval.href != null) {
  15. [Dòng 556] userName = paramUserName != null ? paramUserName : ''

================================================================================

📁 FILE 54: dialog-delete-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/kbank/dialog-delete-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 55: kbank-dialog-policy.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/kbank/kbank-dialog-policy/kbank-dialog-policy.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 56: kbank-dialog-policy.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/kbank/kbank-dialog-policy/kbank-dialog-policy.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 57: kbank-dialog-policy.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/kbank/kbank-dialog-policy/kbank-dialog-policy.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 58: kbank-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/kbank/kbank-form.component.html
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 5 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 94] object.id === value ? 'select-option-active' : ''"
  2. [Dòng 96] object.id === value"

== (5 điều kiện):
  1. [Dòng 108] value == 'add' ? 'select-option-active' : ''"
  2. [Dòng 109] value == 'add'"
  3. [Dòng 118] value=='add'"
  4. [Dòng 169] !cmndBlur ? 'no-border' : ((kbankDetail['citizen_id'] && !(kbankDetail['citizen_id'].length == 9 || kbankDetail['citizen_id'].length == 12) || !kbankDetail['citizen_id']) &&  bnplForm.controls['citizen_id'].touched) ? 'ng-invalid ng-dirty' : ''
  5. [Dòng 174] bnplForm.controls['citizen_id'].touched && kbankDetail['citizen_id'] && !(kbankDetail['citizen_id'].length == 9 || kbankDetail['citizen_id'].length == 12) && cmndBlur

!== (1 điều kiện):
  1. [Dòng 3] selectedBnpl.status !== 'disabled'"

!= (1 điều kiện):
  1. [Dòng 206] selectedBnpl.status != 'disabled'"

================================================================================

📁 FILE 59: kbank-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/kbank/kbank-form.component.ts
📊 Thống kê: 22 điều kiện duy nhất
   - === : 0 lần
   - == : 13 lần
   - !== : 1 lần
   - != : 8 lần
--------------------------------------------------------------------------------

== (13 điều kiện):
  1. [Dòng 81] this.value == 'add')){
  2. [Dòng 117] list = this.data.customer.tokens.filter((item) => item.instrument.issuer.brand.id == 'kbank')
  3. [Dòng 168] if (response.status == '200'
  4. [Dòng 168] response.status == '201') {
  5. [Dòng 176] this.listTokenBnpl.length == 0) {
  6. [Dòng 179] if (this.value == object.id) {
  7. [Dòng 199] return (this.bnplForm.invalid || (this.kbankDetail['citizen_id'] && !(this.kbankDetail['citizen_id'].length == 9) && !(this.kbankDetail['citizen_id'].length == 12)));
  8. [Dòng 238] if (name == 'citizen_id') {
  9. [Dòng 241] if (name == 'phoneNumber') {
  10. [Dòng 244] if (name == 'fullname') {
  11. [Dòng 301] this._res_post.state == 'authorization_required') {
  12. [Dòng 369] this._res_post.code == 'KB-02') {
  13. [Dòng 387] this._res_post.state == 'failed') {

!== (1 điều kiện):
  1. [Dòng 323] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (8 điều kiện):
  1. [Dòng 174] this.listTokenBnpl = this.listTokenBnpl.filter(item => item.id != result.id);
  2. [Dòng 306] if (this._res_post.authorization != null
  3. [Dòng 306] this._res_post.authorization.links != null
  4. [Dòng 311] if (this._res_post.links != null
  5. [Dòng 311] this._res_post.links.cancel != null) {
  6. [Dòng 318] this._res_post.authorization.links.approval != null
  7. [Dòng 318] this._res_post.authorization.links.approval.href != null) {
  8. [Dòng 321] userName = paramUserName != null ? paramUserName : ''

================================================================================

📁 FILE 60: kredivo-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/kredivo/kredivo-form.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 3 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 3] selectedBnpl.status !== 'disabled'"

!= (3 điều kiện):
  1. [Dòng 139] document.activeElement.id!='fullname'"
  2. [Dòng 156] document.activeElement.id!='phoneNumber'"
  3. [Dòng 172] document.activeElement.id!='email'"

================================================================================

📁 FILE 61: kredivo-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/kredivo/kredivo-form.component.ts
📊 Thống kê: 23 điều kiện duy nhất
   - === : 5 lần
   - == : 8 lần
   - !== : 2 lần
   - != : 8 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 154] params.key === 'Backspace') {
  2. [Dòng 155] if (type === 'email') {
  3. [Dòng 160] if (type === 'phoneNumber') {
  4. [Dòng 213] if (name === 'phoneNumber') {
  5. [Dòng 215] } else if (name === 'email') {

== (8 điều kiện):
  1. [Dòng 253] if (this._locale == 'en') {
  2. [Dòng 264] if (this._res_post.state == 'approved'
  3. [Dòng 264] this._res_post.state == 'failed') {
  4. [Dòng 270] if (this._res_post.state == 'failed') {
  5. [Dòng 295] } else if (this._res_post.state == 'authorization_required') {
  6. [Dòng 296] if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  7. [Dòng 415] this.bnplDetail.fullname?.length == 0 ? this._translate.instant('kredivo_empty_fullname_message')
  8. [Dòng 439] this.bnplDetail.email?.length == 0 ? this._translate.instant('kredivo_empty_email_message')

!== (2 điều kiện):
  1. [Dòng 125] if ((this.bnplDetail.phoneNumber.length !== 10
  2. [Dòng 125] this.bnplDetail.phoneNumber.length !== 11)

!= (8 điều kiện):
  1. [Dòng 265] if (this._res_post.return_url != null) {
  2. [Dòng 267] } else if (this._res_post.links != null
  3. [Dòng 267] this._res_post.links.merchant_return != null
  4. [Dòng 267] this._res_post.links.merchant_return.href != null) {
  5. [Dòng 424] if ((this.bnplDetail.phoneNumber?.length != 10
  6. [Dòng 424] this.bnplDetail.phoneNumber?.length != 11) || !this.bnplDetail.phoneNumber.match(/^[*]{8,9
  7. [Dòng 429] (this.bnplDetail.phoneNumber?.length != 10
  8. [Dòng 429] this.bnplDetail.phoneNumber?.length != 11) ?

================================================================================

📁 FILE 62: otp-auth-bnpl.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/otp-auth-bnpl/otp-auth-bnpl.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 63: otp-auth-bnpl.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/otp-auth-bnpl/otp-auth-bnpl.component.ts
📊 Thống kê: 12 điều kiện duy nhất
   - === : 0 lần
   - == : 5 lần
   - !== : 1 lần
   - != : 6 lần
--------------------------------------------------------------------------------

== (5 điều kiện):
  1. [Dòng 148] if (_re.status == '200'
  2. [Dòng 148] _re.status == '201') {
  3. [Dòng 158] else if (this._res.code == '2') {
  4. [Dòng 202] _re.body.payments[_re.body.payments.length - 1].reason.code == 'B4') {
  5. [Dòng 333] if (this._b == 18) {//8-MB Bank;18-oceanbank

!== (1 điều kiện):
  1. [Dòng 178] codeResponse.toString() !== '0') {

!= (6 điều kiện):
  1. [Dòng 152] if (this._res.links != null
  2. [Dòng 152] this._res.links.merchant_return != null
  3. [Dòng 152] this._res.links.merchant_return.href != null) {
  4. [Dòng 328] if (!(_formCard.otp != null
  5. [Dòng 334] if (!(_formCard.password != null
  6. [Dòng 350] if (ua.indexOf('safari') != -1

================================================================================

📁 FILE 64: process-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/process-dialog/process-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 65: process-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/process-dialog/process-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 66: select-bnpl.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/select-bnpl-dialog/select-bnpl.html
📊 Thống kê: 12 điều kiện duy nhất
   - === : 1 lần
   - == : 9 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 18] bnpl.code === 'homepaylater'"

== (9 điều kiện):
  1. [Dòng 7] bnpl.status == 'disabled'"
  2. [Dòng 8] bnpl.status == 'active'"
  3. [Dòng 15] bnpl.code == 'kbank'"
  4. [Dòng 23] bnpl.code == 'kbank'
  5. [Dòng 26] bnpl.status == 'disabled'
  6. [Dòng 26] bnpl.code == 'insta'"
  7. [Dòng 30] bnpl.status == 'active'
  8. [Dòng 33] bnpl.code == 'homepaylater'
  9. [Dòng 36] bnpl.code == 'kredivo'

!= (2 điều kiện):
  1. [Dòng 21] bnpl.code != 'kbank'
  2. [Dòng 21] bnpl.code != 'homepaylater'"

================================================================================

📁 FILE 67: select-bnpl.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/bnpl-main/select-bnpl-dialog/select-bnpl.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 29] if (bnpl.status == 'disabled') {

================================================================================

📁 FILE 68: bankaccount.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/bankaccount/bankaccount.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 55] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 71] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 71] valueDate.trim().length === 0)"

================================================================================

📁 FILE 69: bankaccount.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/bankaccount/bankaccount.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 70: bankaccount.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/bankaccount/bankaccount.component.ts
📊 Thống kê: 159 điều kiện duy nhất
   - === : 47 lần
   - == : 76 lần
   - !== : 13 lần
   - != : 23 lần
--------------------------------------------------------------------------------

=== (47 điều kiện):
  1. [Dòng 118] if (isIE[0] === 'MSIE'
  2. [Dòng 118] +isIE[1] === 10) {
  3. [Dòng 205] if ((_val.value.substr(-1) === ' '
  4. [Dòng 205] _val.value.length === 24) {
  5. [Dòng 215] if (this.cardTypeBank === 'bank_card_number') {
  6. [Dòng 220] } else if (this.cardTypeBank === 'bank_account_number') {
  7. [Dòng 226] } else if (this.cardTypeBank === 'bank_username') {
  8. [Dòng 230] } else if (this.cardTypeBank === 'bank_customer_code') {
  9. [Dòng 236] this.cardTypeBank === 'bank_card_number'
  10. [Dòng 250] if (this.cardTypeOcean === 'IB') {
  11. [Dòng 254] } else if (this.cardTypeOcean === 'MB') {
  12. [Dòng 255] if (_val.value.substr(0, 2) === '84') {
  13. [Dòng 262] } else if (this.cardTypeOcean === 'ATM') {
  14. [Dòng 289] if (this.cardTypeBank === 'bank_account_number') {
  15. [Dòng 308] this.cardTypeBank === 'bank_card_number') {
  16. [Dòng 330] if (this.cardTypeBank === 'bank_card_number'
  17. [Dòng 330] if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  18. [Dòng 651] if (event.keyCode === 8
  19. [Dòng 651] event.key === "Backspace"
  20. [Dòng 691] if (v.length === 2
  21. [Dòng 691] this.flag.length === 3
  22. [Dòng 691] this.flag.charAt(this.flag.length - 1) === '/') {
  23. [Dòng 695] if (v.length === 1) {
  24. [Dòng 697] } else if (v.length === 2) {
  25. [Dòng 700] v.length === 2) {
  26. [Dòng 708] if (len === 2) {
  27. [Dòng 985] if ((this.cardTypeBank === 'bank_account_number'
  28. [Dòng 985] this.cardTypeBank === 'bank_username'
  29. [Dòng 985] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  30. [Dòng 990] this.cardTypeOcean === 'ATM')
  31. [Dòng 991] || (this.cardTypeOcean === 'IB'
  32. [Dòng 1050] if (valIn === this._translate.instant('bank_card_number')) {
  33. [Dòng 1075] } else if (valIn === this._translate.instant('bank_account_number')) {
  34. [Dòng 1094] } else if (valIn === this._translate.instant('bank_username')) {
  35. [Dòng 1110] } else if (valIn === this._translate.instant('bank_customer_code')) {
  36. [Dòng 1215] if (_val.value === ''
  37. [Dòng 1215] _val.value === null
  38. [Dòng 1215] _val.value === undefined) {
  39. [Dòng 1227] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  40. [Dòng 1227] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  41. [Dòng 1234] this.cardTypeOcean === 'MB') {
  42. [Dòng 1242] this.cardTypeOcean === 'IB'
  43. [Dòng 1248] if ((this.cardTypeBank === 'bank_card_number'
  44. [Dòng 1282] if (this.cardName === undefined
  45. [Dòng 1282] this.cardName === '') {
  46. [Dòng 1290] if (this.valueDate === undefined
  47. [Dòng 1290] this.valueDate === '') {

== (76 điều kiện):
  1. [Dòng 132] if (this._b == 18
  2. [Dòng 132] this._b == 19) {
  3. [Dòng 135] if (this._b == 19) {//19BIDV
  4. [Dòng 143] } else if (this._b == 3
  5. [Dòng 143] this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  6. [Dòng 148] if (this._b == 27) {
  7. [Dòng 153] } else if (this._b == 12) {// 12SHB
  8. [Dòng 158] } else if (this._b == 18) { //18Oceanbank-ocb
  9. [Dòng 214] if (this._b == 19
  10. [Dòng 214] this._b == 3
  11. [Dòng 214] this._b == 27
  12. [Dòng 214] this._b == 12) {
  13. [Dòng 249] } else if (this._b == 18) {
  14. [Dòng 280] if (this.checkBin(_val.value) && (this._b == 3
  15. [Dòng 280] this._b == 27)) {
  16. [Dòng 285] if (this._b == 3) {
  17. [Dòng 297] this.cardTypeOcean == 'ATM') {
  18. [Dòng 310] if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  19. [Dòng 330] this._b == 18)) {
  20. [Dòng 406] if (this.checkBin(v) && (this._b == 3
  21. [Dòng 651] event.inputType == 'deleteContentBackward') {
  22. [Dòng 652] if (event.target.name == 'exp_date'
  23. [Dòng 660] event.inputType == 'insertCompositionText') {
  24. [Dòng 675] if (((this.valueDate.length == 4
  25. [Dòng 675] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  26. [Dòng 675] this.valueDate.length == 5)
  27. [Dòng 755] if (temp.length == 0) {
  28. [Dòng 762] return (counter % 10 == 0);
  29. [Dòng 782] } else if (this._b == 19) {
  30. [Dòng 784] } else if (this._b == 27) {
  31. [Dòng 789] if (this._b == 12) {
  32. [Dòng 791] if (this.cardTypeBank == 'bank_customer_code') {
  33. [Dòng 793] } else if (this.cardTypeBank == 'bank_account_number') {
  34. [Dòng 810] _formCard.exp_date.length == 5
  35. [Dòng 810] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
  36. [Dòng 810] this._b == 3)) {//27-pvcombank;3-TPB ;
  37. [Dòng 815] if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
  38. [Dòng 815] this._b == 19
  39. [Dòng 815] this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
  40. [Dòng 818] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  41. [Dòng 821] if (this.cardTypeOcean == 'IB') {
  42. [Dòng 823] } else if (this.cardTypeOcean == 'MB') {
  43. [Dòng 825] } else if (this.cardTypeOcean == 'ATM') {
  44. [Dòng 870] if (_re.status == '200'
  45. [Dòng 870] _re.status == '201') {
  46. [Dòng 875] if (this._res_post.state == 'approved'
  47. [Dòng 875] this._res_post.state == 'failed') {
  48. [Dòng 882] } else if (this._res_post.state == 'authorization_required') {
  49. [Dòng 900] if (this._b == 18) {
  50. [Dòng 905] if (this._b == 27
  51. [Dòng 905] this._b == 18) {
  52. [Dòng 973] if (err.status == 400
  53. [Dòng 973] err.error['name'] == 'INVALID_INPUT_BIN') {
  54. [Dòng 1005] if ((cardNo.length == 16
  55. [Dòng 1005] if ((cardNo.length == 16 || (cardNo.length == 19
  56. [Dòng 1006] && ((this._b == 18
  57. [Dòng 1006] cardNo.length == 19) || this._b != 18)
  58. [Dòng 1019] if (this._b == +e.id) {
  59. [Dòng 1035] if (valIn == 1) {
  60. [Dòng 1037] } else if (valIn == 2) {
  61. [Dòng 1061] this._b == 3) {
  62. [Dòng 1068] if (this._b == 19) {
  63. [Dòng 1126] if(e.index == 0
  64. [Dòng 1135] if (cardType == this._translate.instant('internetbanking')
  65. [Dòng 1143] } else if (cardType == this._translate.instant('mobilebanking')
  66. [Dòng 1151] } else if (cardType == this._translate.instant('atm')
  67. [Dòng 1163] if(groupE.index == 0
  68. [Dòng 1227] this._b == 18))) {
  69. [Dòng 1234] } else if (this._b == 18
  70. [Dòng 1260] this.c_expdate = !(((this.valueDate.length == 4
  71. [Dòng 1293] this.valueDate.length == 4
  72. [Dòng 1293] this.valueDate.search('/') == -1)
  73. [Dòng 1294] this.valueDate.length == 5))
  74. [Dòng 1311] if(id == 1){
  75. [Dòng 1314] if(id == 2){
  76. [Dòng 1317] if(id == 3){

!== (13 điều kiện):
  1. [Dòng 205] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 856] key !== '3') {
  3. [Dòng 906] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 924] codeResponse.toString() !== '0') {
  5. [Dòng 985] cardNo.length !== 0) {
  6. [Dòng 1057] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 1078] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 1099] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 1119] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 1135] this.lb_card_account !== this._translate.instant('ocb_account')) {
  11. [Dòng 1143] this.lb_card_account !== this._translate.instant('ocb_phone')) {
  12. [Dòng 1151] this.lb_card_account !== this._translate.instant('ocb_card')) {
  13. [Dòng 1248] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (23 điều kiện):
  1. [Dòng 165] } else if (this._b != 18) {
  2. [Dòng 171] if (this.htmlDesc != null
  3. [Dòng 202] if (ua.indexOf('safari') != -1
  4. [Dòng 212] if (_val.value != '') {
  5. [Dòng 298] this.auth_method != null) {
  6. [Dòng 653] if (this.valueDate.length != 3) {
  7. [Dòng 810] if (_formCard.exp_date != null
  8. [Dòng 815] if (this.cardName != null
  9. [Dòng 878] if (this._res_post.links != null
  10. [Dòng 878] this._res_post.links.merchant_return != null
  11. [Dòng 878] this._res_post.links.merchant_return.href != null) {
  12. [Dòng 886] if (this._res_post.authorization != null
  13. [Dòng 886] this._res_post.authorization.links != null
  14. [Dòng 886] this._res_post.authorization.links.approval != null) {
  15. [Dòng 893] this._res_post.links.cancel != null) {
  16. [Dòng 1005] this._b != 27
  17. [Dòng 1005] this._b != 12
  18. [Dòng 1005] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  19. [Dòng 1006] this._b != 18)
  20. [Dòng 1052] if (this._b != 18
  21. [Dòng 1052] this._b != 19) {
  22. [Dòng 1126] this._b != 18) this.previewCardService.setDisplayCard(true)
  23. [Dòng 1163] this._b != 18) this.previewCardService.setDisplayCard(true) // tab 0 , thẻ atm thì hiện

================================================================================

📁 FILE 71: bank.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/model/bank.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 72: onepay-napas.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/onepay-napas/onepay-napas.component.html
📊 Thống kê: 7 điều kiện duy nhất
   - === : 3 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 38] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 54] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  3. [Dòng 54] _inExpDate.trim().length === 0)"

== (4 điều kiện):
  1. [Dòng 20] <div class="nd-form-input" [class.custome_padding]="(cardTypeBank == 'internet_banking')&&(_b == 2
  2. [Dòng 35] (cardTypeBank == 'bank_card_number') && (_b == 67 || (checkTwoEnabled && techcombankCard)) && !isOffTechcombankNapas
  3. [Dòng 83] (cardTypeBank == 'bank_card_number') &&(_b == 67 || (checkTwoEnabled && techcombankCard))&& ready===1 && !isOffTechcombankNapas
  4. [Dòng 130] (cardTypeBank == 'internet_banking')&&(_b == 2 || (checkTwoEnabled && !techcombankCard))

================================================================================

📁 FILE 73: onepay-napas.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/onepay-napas/onepay-napas.component.ts
📊 Thống kê: 106 điều kiện duy nhất
   - === : 21 lần
   - == : 50 lần
   - !== : 4 lần
   - != : 31 lần
--------------------------------------------------------------------------------

=== (21 điều kiện):
  1. [Dòng 115] if (isIE[0] === 'MSIE'
  2. [Dòng 115] +isIE[1] === 10) {
  3. [Dòng 138] if (this.cardListTech === "op") {
  4. [Dòng 145] if (this.timeLeft === 10) {
  5. [Dòng 148] if (this.runTime === true) {
  6. [Dòng 154] if (this.timeLeft === 0) {
  7. [Dòng 156] if (this.runTime === true) this.submitCardBanking();
  8. [Dòng 349] if (event.keyCode === 8
  9. [Dòng 349] event.key === "Backspace"
  10. [Dòng 562] if (approval.method === 'REDIRECT') {
  11. [Dòng 565] } else if (approval.method === 'POST_REDIRECT') {
  12. [Dòng 644] if (valIn === this._translate.instant('bank_card_number')) {
  13. [Dòng 646] if (this.timeLeft === 1) {
  14. [Dòng 663] } else if (valIn === this._translate.instant('internet_banking')) {
  15. [Dòng 752] if (_val.value === ''
  16. [Dòng 752] _val.value === null
  17. [Dòng 752] _val.value === undefined) {
  18. [Dòng 764] if (_val.value && (this.cardTypeBank === 'bank_card_number')) {
  19. [Dòng 774] if ((this.cardTypeBank === 'bank_card_number'
  20. [Dòng 812] if (this.cardName === undefined
  21. [Dòng 812] this.cardName === '') {

== (50 điều kiện):
  1. [Dòng 131] if (this._b == 67
  2. [Dòng 131] this._b == 2) {//19BIDV
  3. [Dòng 141] if ((this._b == 2
  4. [Dòng 141] !this.checkTwoEnabled) || (this._b == 2
  5. [Dòng 142] this._b == 2
  6. [Dòng 165] } else if (this._b == 2
  7. [Dòng 169] if (this._b == 67) {
  8. [Dòng 213] if (_re.status == '200'
  9. [Dòng 213] _re.status == '201') {
  10. [Dòng 218] if (this._res_post.state == 'approved'
  11. [Dòng 218] this._res_post.state == 'failed') {
  12. [Dòng 222] } else if (this._res_post.state == 'authorization_required') {
  13. [Dòng 334] return this._b == 2
  14. [Dòng 334] this._b == 67
  15. [Dòng 349] event.inputType == 'deleteContentBackward') {
  16. [Dòng 350] if (event.target.name == 'exp_date'
  17. [Dòng 358] event.inputType == 'insertCompositionText') {
  18. [Dòng 420] if (temp.length == 0) {
  19. [Dòng 427] return (counter % 10 == 0);
  20. [Dòng 444] _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
  21. [Dòng 593] if (err.status == 400
  22. [Dòng 593] err.error['name'] == 'INVALID_INPUT_BIN') {
  23. [Dòng 607] if ((cardNo.length == 16
  24. [Dòng 607] if ((cardNo.length == 16 || (cardNo.length == 19
  25. [Dòng 608] this.checkMod10(cardNo) == true
  26. [Dòng 621] if (this._b == +e.id) {
  27. [Dòng 697] if (this._b == 19) {
  28. [Dòng 701] if (this._b == 27
  29. [Dòng 701] this._b == 3) {
  30. [Dòng 786] if (this._b != 68 || (this._b == 68
  31. [Dòng 795] return ((value.length == 4
  32. [Dòng 795] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  33. [Dòng 795] value.length == 5) && parseInt(value.split('/')[0]
  34. [Dòng 799] || (this._util.checkValidExpireMonthYear(value) && (this._b == 2
  35. [Dòng 799] this._b == 20
  36. [Dòng 799] this._b == 33
  37. [Dòng 800] this._b == 39
  38. [Dòng 800] this._b == 43
  39. [Dòng 800] this._b == 45
  40. [Dòng 800] this._b == 64
  41. [Dòng 800] this._b == 68
  42. [Dòng 800] this._b == 72))) //sonnh them Vietbank 72
  43. [Dòng 821] this._inExpDate.length == 4
  44. [Dòng 821] this._inExpDate.search('/') == -1)
  45. [Dòng 822] this._inExpDate.length == 5))
  46. [Dòng 824] || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 20
  47. [Dòng 824] this._b == 72)));
  48. [Dòng 843] if(id == 1){
  49. [Dòng 846] if(id == 2){
  50. [Dòng 849] if(id == 3){

!== (4 điều kiện):
  1. [Dòng 237] codeResponse.toString() !== '0') {
  2. [Dòng 605] let _b = this._b !== 67 ? 67 : this._b
  3. [Dòng 690] if (this.cardTypeBank !== 'internet_banking') {
  4. [Dòng 774] this._b !== 18)) {

!= (31 điều kiện):
  1. [Dòng 161] if (this.htmlDesc != null
  2. [Dòng 226] if (this._res_post.authorization != null
  3. [Dòng 226] this._res_post.authorization.links != null
  4. [Dòng 226] this._res_post.authorization.links.approval != null) {
  5. [Dòng 286] if (ua.indexOf('safari') != -1
  6. [Dòng 351] if (this._inExpDate.length != 3) {
  7. [Dòng 444] if (_formCard.exp_date != null
  8. [Dòng 449] if (this.cardName != null
  9. [Dòng 486] if (this._res_post.return_url != null) {
  10. [Dòng 489] if (this._res_post.links != null
  11. [Dòng 489] this._res_post.links.merchant_return != null
  12. [Dòng 489] this._res_post.links.merchant_return.href != null) {
  13. [Dòng 547] this._res_post.links.cancel != null) {
  14. [Dòng 552] let userName = _formCard.name != null ? _formCard.name : ''
  15. [Dòng 553] this._res_post.authorization.links.approval != null
  16. [Dòng 553] this._res_post.authorization.links.approval.href != null) {
  17. [Dòng 556] userName = paramUserName != null ? paramUserName : ''
  18. [Dòng 607] this._b != 27
  19. [Dòng 607] this._b != 12
  20. [Dòng 607] this._b != 3))
  21. [Dòng 786] if (this._b != 68
  22. [Dòng 797] this._b != 2
  23. [Dòng 797] this._b != 20
  24. [Dòng 797] this._b != 33
  25. [Dòng 797] this._b != 39
  26. [Dòng 798] this._b != 43
  27. [Dòng 798] this._b != 45
  28. [Dòng 798] this._b != 64
  29. [Dòng 798] this._b != 67
  30. [Dòng 798] this._b != 68
  31. [Dòng 798] this._b != 72)

================================================================================

📁 FILE 74: otp-auth.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/otp-auth/otp-auth.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 75: otp-auth.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/otp-auth/otp-auth.component.ts
📊 Thống kê: 19 điều kiện duy nhất
   - === : 0 lần
   - == : 11 lần
   - !== : 2 lần
   - != : 6 lần
--------------------------------------------------------------------------------

== (11 điều kiện):
  1. [Dòng 104] if (this._b == 8) {//MB Bank
  2. [Dòng 108] if (this._b == 18) {//Oceanbank
  3. [Dòng 157] if (this._b == 8) {
  4. [Dòng 162] if (this._b == 18) {
  5. [Dòng 167] if (this._b == 12) { //SHB
  6. [Dòng 189] if (_re.status == '200'
  7. [Dòng 189] _re.status == '201') {
  8. [Dòng 198] } else if (this._res.state == 'authorization_required') {
  9. [Dòng 233] if (this.challengeCode == '') {
  10. [Dòng 333] if (this._b == 12) {
  11. [Dòng 389] if (this._b == 18) {//8-MB Bank;18-oceanbank

!== (2 điều kiện):
  1. [Dòng 136] return sentences.filter(sentence => sentence.trim() !== '');
  2. [Dòng 204] codeResponse.toString() !== '0') {

!= (6 điều kiện):
  1. [Dòng 194] if (this._res.links != null
  2. [Dòng 194] this._res.links.merchant_return != null
  3. [Dòng 194] this._res.links.merchant_return.href != null) {
  4. [Dòng 384] if (!(_formCard.otp != null
  5. [Dòng 390] if (!(_formCard.password != null
  6. [Dòng 406] if (ua.indexOf('safari') != -1

================================================================================

📁 FILE 76: shb.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/shb/shb.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 36] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 54] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  3. [Dòng 54] _inExpDate.trim().length === 0)"

== (2 điều kiện):
  1. [Dòng 33] <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_card_number')">
  2. [Dòng 78] <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_account_number')">

================================================================================

📁 FILE 77: shb.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/shb/shb.component.ts
📊 Thống kê: 69 điều kiện duy nhất
   - === : 17 lần
   - == : 36 lần
   - !== : 5 lần
   - != : 11 lần
--------------------------------------------------------------------------------

=== (17 điều kiện):
  1. [Dòng 107] if (isIE[0] === 'MSIE'
  2. [Dòng 107] +isIE[1] === 10) {
  3. [Dòng 151] if (focusElement === 'card_name') {
  4. [Dòng 153] } else if (focusElement === 'exp_date'
  5. [Dòng 174] focusExpDateElement === 'card_name') {
  6. [Dòng 394] if (this.cardTypeBank === 'bank_account_number'
  7. [Dòng 439] if (valIn === this._translate.instant('bank_card_number')) {
  8. [Dòng 445] } else if (valIn === this._translate.instant('bank_account_number')) {
  9. [Dòng 498] if (_val.value === ''
  10. [Dòng 498] _val.value === null
  11. [Dòng 498] _val.value === undefined) {
  12. [Dòng 510] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  13. [Dòng 510] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  14. [Dòng 517] this.cardTypeOcean === 'MB') {
  15. [Dòng 525] this.cardTypeOcean === 'IB'
  16. [Dòng 531] if ((this.cardTypeBank === 'bank_card_number'
  17. [Dòng 554] if (this.cardTypeOcean === 'IB') {

== (36 điều kiện):
  1. [Dòng 116] if (this._b == 12) this.isShbGroup = true;
  2. [Dòng 139] return this._b == 9
  3. [Dòng 139] this._b == 11
  4. [Dòng 139] this._b == 16
  5. [Dòng 139] this._b == 17
  6. [Dòng 139] this._b == 25
  7. [Dòng 139] this._b == 44
  8. [Dòng 140] this._b == 57
  9. [Dòng 140] this._b == 59
  10. [Dòng 140] this._b == 61
  11. [Dòng 140] this._b == 63
  12. [Dòng 140] this._b == 69
  13. [Dòng 222] if (this.cardTypeBank == 'bank_account_number') {
  14. [Dòng 233] _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
  15. [Dòng 279] if (_re.status == '200'
  16. [Dòng 279] _re.status == '201') {
  17. [Dòng 283] if (this._res_post.state == 'approved'
  18. [Dòng 283] this._res_post.state == 'failed') {
  19. [Dòng 289] } else if (this._res_post.state == 'authorization_required') {
  20. [Dòng 381] if (err.status == 400
  21. [Dòng 381] err.error['name'] == 'INVALID_INPUT_BIN') {
  22. [Dòng 397] if ((cardNo.length == 16
  23. [Dòng 397] cardNo.length == 19) && cardNo.startsWith("970443") && this._util.checkMod10(cardNo) && this.checkBin(cardNo)) {
  24. [Dòng 409] if (this._b == +e.id) {
  25. [Dòng 425] if (valIn == 1) {
  26. [Dòng 427] } else if (valIn == 2) {
  27. [Dòng 455] if(e.index == 0) this.previewCardService.setDisplayCard(true)
  28. [Dòng 510] this._b == 18))) {
  29. [Dòng 517] } else if (this._b == 18
  30. [Dòng 531] this._b == 18)) {
  31. [Dòng 543] this.c_expdate = !(((this.valueDate.length == 4
  32. [Dòng 543] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  33. [Dòng 543] this.valueDate.length == 5)
  34. [Dòng 579] if(id == 1){
  35. [Dòng 582] if(id == 2){
  36. [Dòng 585] if(id == 3){

!== (5 điều kiện):
  1. [Dòng 267] key !== '3') {
  2. [Dòng 309] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  3. [Dòng 327] codeResponse.toString() !== '0') {
  4. [Dòng 394] cardNo.length !== 0) {
  5. [Dòng 531] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (11 điều kiện):
  1. [Dòng 124] if (this.htmlDesc != null
  2. [Dòng 187] if (ua.indexOf('safari') != -1
  3. [Dòng 233] if (_formCard.exp_date != null
  4. [Dòng 238] if (this.cardName != null
  5. [Dòng 286] if (this._res_post.links != null
  6. [Dòng 286] this._res_post.links.merchant_return != null
  7. [Dòng 286] this._res_post.links.merchant_return.href != null) {
  8. [Dòng 292] if (this._res_post.authorization != null
  9. [Dòng 292] this._res_post.authorization.links != null
  10. [Dòng 292] this._res_post.authorization.links.approval != null) {
  11. [Dòng 299] this._res_post.links.cancel != null) {

================================================================================

📁 FILE 78: techcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/techcombank/techcombank.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 79: techcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/techcombank/techcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 80: techcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/techcombank/techcombank.component.ts
📊 Thống kê: 20 điều kiện duy nhất
   - === : 1 lần
   - == : 15 lần
   - !== : 1 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 66] if (this.timeLeft === 0) {

== (15 điều kiện):
  1. [Dòng 58] if (this._b == 2
  2. [Dòng 58] this._b == 31
  3. [Dòng 58] this._b == 80) {
  4. [Dòng 95] if (this._b == 2) {
  5. [Dòng 97] } else if (this._b == 6) {
  6. [Dòng 99] } else if (this._b == 31) {
  7. [Dòng 101] } else if (this._b == 80) {
  8. [Dòng 131] if (_re.status == '200'
  9. [Dòng 131] _re.status == '201') {
  10. [Dòng 136] if (this._res_post.state == 'approved'
  11. [Dòng 136] this._res_post.state == 'failed') {
  12. [Dòng 140] } else if (this._res_post.state == 'authorization_required') {
  13. [Dòng 205] if(id == 1){
  14. [Dòng 208] if(id == 2){
  15. [Dòng 211] if(id == 3){

!== (1 điều kiện):
  1. [Dòng 155] codeResponse.toString() !== '0') {

!= (3 điều kiện):
  1. [Dòng 144] if (this._res_post.authorization != null
  2. [Dòng 144] this._res_post.authorization.links != null
  3. [Dòng 144] this._res_post.authorization.links.approval != null) {

================================================================================

📁 FILE 81: vibbank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/vibbank/vibbank.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 37] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 53] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 53] valueDate.trim().length === 0)"

================================================================================

📁 FILE 82: vibbank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/vibbank/vibbank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 83: vibbank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/vibbank/vibbank.component.ts
📊 Thống kê: 101 điều kiện duy nhất
   - === : 36 lần
   - == : 38 lần
   - !== : 10 lần
   - != : 17 lần
--------------------------------------------------------------------------------

=== (36 điều kiện):
  1. [Dòng 119] if (isIE[0] === 'MSIE'
  2. [Dòng 119] +isIE[1] === 10) {
  3. [Dòng 150] if (this.timeLeft === 0) {
  4. [Dòng 195] if ((_val.value.substr(-1) === ' '
  5. [Dòng 195] _val.value.length === 24) {
  6. [Dòng 205] if (this.cardTypeBank === 'bank_card_number') {
  7. [Dòng 210] } else if (this.cardTypeBank === 'bank_account_number') {
  8. [Dòng 216] } else if (this.cardTypeBank === 'bank_username') {
  9. [Dòng 220] } else if (this.cardTypeBank === 'bank_customer_code') {
  10. [Dòng 241] if (this.cardTypeBank === 'bank_account_number') {
  11. [Dòng 252] this.cardTypeBank === 'bank_card_number') {
  12. [Dòng 489] if (event.keyCode === 8
  13. [Dòng 489] event.key === "Backspace"
  14. [Dòng 529] if (v.length === 2
  15. [Dòng 529] this.flag.length === 3
  16. [Dòng 529] this.flag.charAt(this.flag.length - 1) === '/') {
  17. [Dòng 533] if (v.length === 1) {
  18. [Dòng 535] } else if (v.length === 2) {
  19. [Dòng 538] v.length === 2) {
  20. [Dòng 546] if (len === 2) {
  21. [Dòng 789] if ((this.cardTypeBank === 'bank_account_number'
  22. [Dòng 789] this.cardTypeBank === 'bank_username'
  23. [Dòng 789] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  24. [Dòng 839] if (valIn === this._translate.instant('bank_card_number')) {
  25. [Dòng 859] } else if (valIn === this._translate.instant('bank_account_number')) {
  26. [Dòng 872] } else if (valIn === this._translate.instant('bank_username')) {
  27. [Dòng 884] } else if (valIn === this._translate.instant('bank_customer_code')) {
  28. [Dòng 951] if (_val.value === ''
  29. [Dòng 951] _val.value === null
  30. [Dòng 951] _val.value === undefined) {
  31. [Dòng 963] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  32. [Dòng 973] if ((this.cardTypeBank === 'bank_card_number'
  33. [Dòng 1005] if (this.cardName === undefined
  34. [Dòng 1005] this.cardName === '') {
  35. [Dòng 1013] if (this.valueDate === undefined
  36. [Dòng 1013] this.valueDate === '') {

== (38 điều kiện):
  1. [Dòng 133] if (this._b == 5) {//5-vib;
  2. [Dòng 204] if (this._b == 5) {
  3. [Dòng 238] if (this.checkBin(_val.value) && (this._b == 5)) {
  4. [Dòng 254] if (this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  5. [Dòng 316] if (this.checkBin(v) && (this._b == 5)) {
  6. [Dòng 489] event.inputType == 'deleteContentBackward') {
  7. [Dòng 490] if (event.target.name == 'exp_date'
  8. [Dòng 498] event.inputType == 'insertCompositionText') {
  9. [Dòng 513] if (((this.valueDate.length == 4
  10. [Dòng 513] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  11. [Dòng 513] this.valueDate.length == 5)
  12. [Dòng 593] if (temp.length == 0) {
  13. [Dòng 600] return (counter % 10 == 0);
  14. [Dòng 630] _formCard.exp_date.length == 5
  15. [Dòng 630] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 5)) {//27-pvcombank;3-TPB ;
  16. [Dòng 635] if (this.cardName != null && this.cardName.length > 0 && (this._b == 5)) {//3-TPB ;19-BIDV;27-pvcombank;
  17. [Dòng 679] if (_re.status == '200'
  18. [Dòng 679] _re.status == '201') {
  19. [Dòng 684] if (this._res_post.state == 'approved'
  20. [Dòng 684] this._res_post.state == 'failed') {
  21. [Dòng 691] } else if (this._res_post.state == 'authorization_required') {
  22. [Dòng 777] if (err.status == 400
  23. [Dòng 777] err.error['name'] == 'INVALID_INPUT_BIN') {
  24. [Dòng 794] if ((cardNo.length == 16
  25. [Dòng 794] if ((cardNo.length == 16 || (cardNo.length == 19
  26. [Dòng 795] && ((this._b == 18
  27. [Dòng 795] cardNo.length == 19) || this._b != 18)
  28. [Dòng 808] if (this._b == +e.id) {
  29. [Dòng 824] if (valIn == 1) {
  30. [Dòng 826] } else if (valIn == 2) {
  31. [Dòng 963] this._b == 18)) {
  32. [Dòng 985] this.c_expdate = !(((this.valueDate.length == 4
  33. [Dòng 1016] this.valueDate.length == 4
  34. [Dòng 1016] this.valueDate.search('/') == -1)
  35. [Dòng 1017] this.valueDate.length == 5))
  36. [Dòng 1121] if(id == 1){
  37. [Dòng 1124] if(id == 2){
  38. [Dòng 1127] if(id == 3){

!== (10 điều kiện):
  1. [Dòng 195] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 665] key !== '3') {
  3. [Dòng 713] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 730] codeResponse.toString() !== '0') {
  5. [Dòng 789] cardNo.length !== 0) {
  6. [Dòng 846] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 862] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 877] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 891] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 973] this._b !== 18) || (this._b == 18)) {

!= (17 điều kiện):
  1. [Dòng 160] if (this.htmlDesc != null
  2. [Dòng 192] if (ua.indexOf('safari') != -1
  3. [Dòng 202] if (_val.value != '') {
  4. [Dòng 491] if (this.valueDate.length != 3) {
  5. [Dòng 630] if (_formCard.exp_date != null
  6. [Dòng 635] if (this.cardName != null
  7. [Dòng 687] if (this._res_post.links != null
  8. [Dòng 687] this._res_post.links.merchant_return != null
  9. [Dòng 687] this._res_post.links.merchant_return.href != null) {
  10. [Dòng 695] if (this._res_post.authorization != null
  11. [Dòng 695] this._res_post.authorization.links != null
  12. [Dòng 695] this._res_post.authorization.links.approval != null) {
  13. [Dòng 702] this._res_post.links.cancel != null) {
  14. [Dòng 794] this._b != 27
  15. [Dòng 794] this._b != 12
  16. [Dòng 794] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  17. [Dòng 795] this._b != 18)

================================================================================

📁 FILE 84: vietcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/vietcombank/vietcombank.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 38] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  2. [Dòng 38] _inExpDate.trim().length === 0)"

== (1 điều kiện):
  1. [Dòng 73] _b == 68"

================================================================================

📁 FILE 85: vietcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/vietcombank/vietcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 86: vietcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/vietcombank/vietcombank.component.ts
📊 Thống kê: 95 điều kiện duy nhất
   - === : 4 lần
   - == : 64 lần
   - !== : 2 lần
   - != : 25 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 204] if (event.keyCode === 8
  2. [Dòng 204] event.key === "Backspace"
  3. [Dòng 441] if (approval.method === 'REDIRECT') {
  4. [Dòng 444] } else if (approval.method === 'POST_REDIRECT') {

== (64 điều kiện):
  1. [Dòng 104] if (this._b == 1
  2. [Dòng 104] this._b == 20
  3. [Dòng 104] this._b == 73
  4. [Dòng 104] this._b == 36
  5. [Dòng 104] this._b == 64
  6. [Dòng 104] this._b == 55
  7. [Dòng 104] this._b == 47
  8. [Dòng 104] this._b == 48
  9. [Dòng 104] this._b == 59) {
  10. [Dòng 121] return this._b == 11
  11. [Dòng 121] this._b == 33
  12. [Dòng 121] this._b == 39
  13. [Dòng 121] this._b == 43
  14. [Dòng 121] this._b == 45
  15. [Dòng 121] this._b == 67
  16. [Dòng 122] this._b == 68
  17. [Dòng 122] this._b == 72
  18. [Dòng 122] this._b == 74
  19. [Dòng 122] this._b == 75
  20. [Dòng 130] return this._b == 9
  21. [Dòng 130] this._b == 16
  22. [Dòng 130] this._b == 17
  23. [Dòng 130] this._b == 25
  24. [Dòng 130] this._b == 44
  25. [Dòng 131] this._b == 57
  26. [Dòng 131] this._b == 59
  27. [Dòng 131] this._b == 61
  28. [Dòng 131] this._b == 63
  29. [Dòng 131] this._b == 69
  30. [Dòng 204] event.inputType == 'deleteContentBackward') {
  31. [Dòng 205] if (event.target.name == 'exp_date'
  32. [Dòng 213] event.inputType == 'insertCompositionText') {
  33. [Dòng 328] if (this._res_post.state == 'approved'
  34. [Dòng 328] this._res_post.state == 'failed') {
  35. [Dòng 380] } else if (this._res_post.state == 'authorization_required') {
  36. [Dòng 402] this._b == 14
  37. [Dòng 402] this._b == 15
  38. [Dòng 402] this._b == 24
  39. [Dòng 402] this._b == 8
  40. [Dòng 402] this._b == 10
  41. [Dòng 402] this._b == 22
  42. [Dòng 402] this._b == 23
  43. [Dòng 402] this._b == 30
  44. [Dòng 402] this._b == 11
  45. [Dòng 402] this._b == 9) {
  46. [Dòng 472] if (err.status == 400
  47. [Dòng 472] err.error['name'] == 'INVALID_INPUT_BIN') {
  48. [Dòng 475] } else if(err.status == 400
  49. [Dòng 475] err.error['name'] == 'INVALID_MERCHANT') { // merchant inactive
  50. [Dòng 500] if ((cardNo.length == 16
  51. [Dòng 501] (cardNo.length == 19
  52. [Dòng 501] (cardNo.length == 19 && (this._b == 1
  53. [Dòng 501] this._b == 4
  54. [Dòng 501] this._b == 59))
  55. [Dòng 503] this._util.checkMod10(cardNo) == true
  56. [Dòng 540] return ((value.length == 4
  57. [Dòng 540] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  58. [Dòng 540] value.length == 5) && parseInt(value.split('/')[0]
  59. [Dòng 574] this._inExpDate.length == 4
  60. [Dòng 574] this._inExpDate.search('/') == -1)
  61. [Dòng 575] this._inExpDate.length == 5))
  62. [Dòng 618] if (id == 1) {
  63. [Dòng 621] if (id == 2) {
  64. [Dòng 624] if (id == 3) {

!== (2 điều kiện):
  1. [Dòng 341] codeResponse.toString() !== '0') {
  2. [Dòng 403] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (25 điều kiện):
  1. [Dòng 109] if (this.htmlDesc != null
  2. [Dòng 140] if (ua.indexOf('safari') != -1
  3. [Dòng 206] if (this._inExpDate.length != 3) {
  4. [Dòng 282] if (this._b != 9
  5. [Dòng 282] this._b != 16
  6. [Dòng 282] this._b != 17
  7. [Dòng 282] this._b != 25
  8. [Dòng 282] this._b != 44
  9. [Dòng 283] this._b != 57
  10. [Dòng 283] this._b != 59
  11. [Dòng 283] this._b != 61
  12. [Dòng 283] this._b != 63
  13. [Dòng 283] this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
  14. [Dòng 296] '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75'].indexOf(this._b.toString()) != -1) {
  15. [Dòng 330] if (this._res_post.return_url != null) {
  16. [Dòng 333] if (this._res_post.links != null
  17. [Dòng 333] this._res_post.links.merchant_return != null
  18. [Dòng 333] this._res_post.links.merchant_return.href != null) {
  19. [Dòng 385] if (this._res_post.authorization != null
  20. [Dòng 385] this._res_post.authorization.links != null
  21. [Dòng 390] this._res_post.links.cancel != null) {
  22. [Dòng 396] let userName = _formCard.name != null ? _formCard.name : ''
  23. [Dòng 397] this._res_post.authorization.links.approval != null
  24. [Dòng 397] this._res_post.authorization.links.approval.href != null) {
  25. [Dòng 400] userName = paramUserName != null ? paramUserName : ''

================================================================================

📁 FILE 87: vietqr-domes.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/vietqr-domes/vietqr-domes.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 88: vietqr-domes.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/banks/vietqr-domes/vietqr-domes.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 89: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 90: off-bank-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/dialog/off-bank-dialog/off-bank-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 91: off-bank-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/dialog/off-bank-dialog/off-bank-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 92: domescard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/domescard-main.component.html
📊 Thống kê: 12 điều kiện duy nhất
   - === : 1 lần
   - == : 11 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 49] filteredData.length === 0"

== (11 điều kiện):
  1. [Dòng 8] (token || _auth==1) && _b != 16
  2. [Dòng 10] (token || _auth==1) && _b != 16; else normalHeader
  3. [Dòng 23] bankSelected && (token || _auth == 1) && _b != 16
  4. [Dòng 62] (!token && _auth==0 && vietcombankGroupSelected) || (token && _b == 16)
  5. [Dòng 62] _b == 16)">
  6. [Dòng 67] _auth==0 && techcombankGroupSelected
  7. [Dòng 72] _auth==0 && shbGroupSelected
  8. [Dòng 78] _auth==0 && onepaynapasGroupSelected
  9. [Dòng 86] _auth==0 && bankaccountGroupSelected
  10. [Dòng 91] _auth==0 && vibbankGroupSelected
  11. [Dòng 97] _auth==0 && vietQRSelected

================================================================================

📁 FILE 93: domescard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version1/domescard-main.component.ts
📊 Thống kê: 155 điều kiện duy nhất
   - === : 28 lần
   - == : 117 lần
   - !== : 2 lần
   - != : 8 lần
--------------------------------------------------------------------------------

=== (28 điều kiện):
  1. [Dòng 148] if (this.bankList?.length === 1) {
  2. [Dòng 177] this.themeConfig.techcombankCard === false ? false : true
  3. [Dòng 348] if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
  4. [Dòng 349] filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
  5. [Dòng 403] if (valOut === 'auth') {
  6. [Dòng 596] if (this._b === '1'
  7. [Dòng 596] this._b === '20'
  8. [Dòng 596] this._b === '64') {
  9. [Dòng 599] if (this._b === '36'
  10. [Dòng 599] this._b === '18'
  11. [Dòng 602] if (this._b === '19'
  12. [Dòng 602] this._b === '16'
  13. [Dòng 602] this._b === '25'
  14. [Dòng 602] this._b === '33'
  15. [Dòng 603] this._b === '39'
  16. [Dòng 603] this._b === '9'
  17. [Dòng 603] this._b === '11'
  18. [Dòng 603] this._b === '17'
  19. [Dòng 604] this._b === '36'
  20. [Dòng 604] this._b === '44'
  21. [Dòng 605] this._b === '64'
  22. [Dòng 608] if (this._b === '20'
  23. [Dòng 611] if (this._b === '18') {
  24. [Dòng 628] return (bankId === '1'
  25. [Dòng 628] bankId === '20'
  26. [Dòng 628] bankId === '64');
  27. [Dòng 632] return (bankId === '36'
  28. [Dòng 632] bankId === '18'

== (117 điều kiện):
  1. [Dòng 191] if (type == 2) {
  2. [Dòng 203] if(type == 2
  3. [Dòng 225] this._auth == 0) {
  4. [Dòng 282] if (e.id == '31') {
  5. [Dòng 284] } else if (e.id == '80') {
  6. [Dòng 290] if (!(strTest == 'card
  7. [Dòng 307] if (d.b.card_list == s) {
  8. [Dòng 328] if (item.b.id == '2'
  9. [Dòng 328] item.b.id == '67') {
  10. [Dòng 380] $event == 'true') {
  11. [Dòng 387] _b: this._b == '2' ? '67' : this._b
  12. [Dòng 409] if (bankid == 2
  13. [Dòng 409] bankid == 67) {
  14. [Dòng 412] || (off && !this.enabledTwoBankTech && ((bankid == '2'
  15. [Dòng 412] this.isOffTechcombank) || (bankid == '67'
  16. [Dòng 494] if (bankId == 1
  17. [Dòng 494] bankId == 4
  18. [Dòng 494] bankId == 7
  19. [Dòng 494] bankId == 8
  20. [Dòng 494] bankId == 9
  21. [Dòng 494] bankId == 10
  22. [Dòng 494] bankId == 11
  23. [Dòng 494] bankId == 14
  24. [Dòng 494] bankId == 15
  25. [Dòng 495] bankId == 16
  26. [Dòng 495] bankId == 17
  27. [Dòng 495] bankId == 20
  28. [Dòng 495] bankId == 22
  29. [Dòng 495] bankId == 23
  30. [Dòng 495] bankId == 24
  31. [Dòng 495] bankId == 25
  32. [Dòng 495] bankId == 30
  33. [Dòng 495] bankId == 33
  34. [Dòng 496] bankId == 34
  35. [Dòng 496] bankId == 35
  36. [Dòng 496] bankId == 36
  37. [Dòng 496] bankId == 37
  38. [Dòng 496] bankId == 38
  39. [Dòng 496] bankId == 39
  40. [Dòng 496] bankId == 40
  41. [Dòng 496] bankId == 41
  42. [Dòng 496] bankId == 42
  43. [Dòng 497] bankId == 43
  44. [Dòng 497] bankId == 44
  45. [Dòng 497] bankId == 45
  46. [Dòng 497] bankId == 46
  47. [Dòng 497] bankId == 47
  48. [Dòng 497] bankId == 48
  49. [Dòng 497] bankId == 49
  50. [Dòng 497] bankId == 50
  51. [Dòng 497] bankId == 51
  52. [Dòng 498] bankId == 52
  53. [Dòng 498] bankId == 53
  54. [Dòng 498] bankId == 54
  55. [Dòng 498] bankId == 55
  56. [Dòng 498] bankId == 56
  57. [Dòng 498] bankId == 57
  58. [Dòng 498] bankId == 58
  59. [Dòng 498] bankId == 59
  60. [Dòng 498] bankId == 60
  61. [Dòng 499] bankId == 61
  62. [Dòng 499] bankId == 62
  63. [Dòng 499] bankId == 63
  64. [Dòng 499] bankId == 64
  65. [Dòng 499] bankId == 65
  66. [Dòng 499] bankId == 66
  67. [Dòng 499] bankId == 68
  68. [Dòng 499] bankId == 69
  69. [Dòng 499] bankId == 70
  70. [Dòng 500] bankId == 71
  71. [Dòng 500] bankId == 72
  72. [Dòng 500] bankId == 73
  73. [Dòng 500] bankId == 32
  74. [Dòng 500] bankId == 74
  75. [Dòng 500] bankId == 75) {
  76. [Dòng 502] } else if (bankId == 6
  77. [Dòng 502] bankId == 31
  78. [Dòng 502] bankId == 80) {
  79. [Dòng 504] } else if (bankId == 2
  80. [Dòng 504] bankId == 67) {
  81. [Dòng 506] } else if (bankId == 3
  82. [Dòng 506] bankId == 18
  83. [Dòng 506] bankId == 19
  84. [Dòng 506] bankId == 27) {
  85. [Dòng 508] } else if (bankId == 5) {
  86. [Dòng 510] } else if (bankId == 12) {
  87. [Dòng 512] } else if (bankId == 'vietqr') {
  88. [Dòng 599] this._b == '55'
  89. [Dòng 599] this._b == '47'
  90. [Dòng 599] this._b == '48'
  91. [Dòng 599] this._b == '19'
  92. [Dòng 599] this._b == '59'
  93. [Dòng 599] this._b == '73'
  94. [Dòng 599] this._b == '12') {
  95. [Dòng 602] this._b == '3'
  96. [Dòng 603] this._b == '43'
  97. [Dòng 603] this._b == '45'
  98. [Dòng 604] this._b == '57'
  99. [Dòng 605] this._b == '61'
  100. [Dòng 605] this._b == '63'
  101. [Dòng 605] this._b == '67'
  102. [Dòng 605] this._b == '68'
  103. [Dòng 605] this._b == '69'
  104. [Dòng 605] this._b == '72'
  105. [Dòng 605] this._b == '74'
  106. [Dòng 605] this._b == '75') {
  107. [Dòng 608] this._b == '36'
  108. [Dòng 608] this._b == '75') { //sonnh them 72 Vietbank
  109. [Dòng 632] bankId == '55'
  110. [Dòng 632] bankId == '47'
  111. [Dòng 632] bankId == '48'
  112. [Dòng 632] bankId == '19'
  113. [Dòng 632] bankId == '59'
  114. [Dòng 632] bankId == '73'
  115. [Dòng 632] bankId == '5'
  116. [Dòng 632] bankId == '12');
  117. [Dòng 647] if (item['id'] == this._b) {

!== (2 điều kiện):
  1. [Dòng 126] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  2. [Dòng 333] this.bankList = this.bankList.filter(item => item.b.id !== '67');

!= (8 điều kiện):
  1. [Dòng 207] if (params['locale'] != null) {
  2. [Dòng 213] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 217] this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
  4. [Dòng 249] if (!(strInstrument != null
  5. [Dòng 252] if (strInstrument.substring(0, 1) != '^'
  6. [Dòng 252] strInstrument.substr(strInstrument.length - 1) != '$') {
  7. [Dòng 471] if (bankid != null) {
  8. [Dòng 531] this.data?.merchant.id != 'OPTEST' ? this.data?.on_off_bank : ''

================================================================================

📁 FILE 94: bank-domescard-support.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version2/bank-domescard-support/bank-domescard-support.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 95: bank-domescard-support.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version2/bank-domescard-support/bank-domescard-support.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 96: bank.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version2/banks/model/bank.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 97: otp-auth.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version2/banks/otp-auth/otp-auth.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 98: otp-auth.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version2/banks/otp-auth/otp-auth.component.ts
📊 Thống kê: 18 điều kiện duy nhất
   - === : 0 lần
   - == : 11 lần
   - !== : 2 lần
   - != : 5 lần
--------------------------------------------------------------------------------

== (11 điều kiện):
  1. [Dòng 99] if (this._b == 8) {//MB Bank
  2. [Dòng 103] if (this._b == 18) {//Oceanbank
  3. [Dòng 161] if (this._b == 8) {
  4. [Dòng 166] if (this._b == 18) {
  5. [Dòng 171] if (this._b == 12) { //SHB
  6. [Dòng 193] if (_re.status == '200'
  7. [Dòng 193] _re.status == '201') {
  8. [Dòng 202] } else if (this._res.state == 'authorization_required') {
  9. [Dòng 233] if (this.challengeCode == '') {
  10. [Dòng 327] if (this._b == 12) {
  11. [Dòng 378] if (this._b == 18) {//8-MB Bank;18-oceanbank

!== (2 điều kiện):
  1. [Dòng 132] return sentences.filter(sentence => sentence.trim() !== '');
  2. [Dòng 208] codeResponse.toString() !== '0') {

!= (5 điều kiện):
  1. [Dòng 198] if (this._res.links != null
  2. [Dòng 198] this._res.links.merchant_return != null
  3. [Dòng 198] this._res.links.merchant_return.href != null) {
  4. [Dòng 373] if (!(_formCard.otp != null
  5. [Dòng 379] if (!(_formCard.password != null

================================================================================

📁 FILE 99: vietcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version2/banks/vietcombank/vietcombank.component.html
📊 Thống kê: 10 điều kiện duy nhất
   - === : 2 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 32] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  2. [Dòng 32] _inExpDate.trim().length === 0)"

== (2 điều kiện):
  1. [Dòng 54] _b == 6"
  2. [Dòng 67] _b == 68"

!= (6 điều kiện):
  1. [Dòng 28] d_card_date && (_b != 3 && _b != 19 && _b != 18)
  2. [Dòng 28] _b != 18)">
  3. [Dòng 40] *ngIf="(_b != 18
  4. [Dòng 40] (_b != 18 && _b != 6 && _b != 2)
  5. [Dòng 40] _b != 2)">
  6. [Dòng 69] _b != 6 && _b != 2

================================================================================

📁 FILE 100: vietcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version2/banks/vietcombank/vietcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 101: vietcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version2/banks/vietcombank/vietcombank.component.ts
📊 Thống kê: 131 điều kiện duy nhất
   - === : 4 lần
   - == : 86 lần
   - !== : 3 lần
   - != : 38 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 430] if (event.keyCode === 8
  2. [Dòng 430] event.key === "Backspace"
  3. [Dòng 701] if (approval.method === 'REDIRECT') {
  4. [Dòng 704] } else if (approval.method === 'POST_REDIRECT') {

== (86 điều kiện):
  1. [Dòng 132] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 159] if (this._b == 6) this.htmlDesc = this._translate.instant('6_1_html_desc'); // thằng đông á bank custome lại
  3. [Dòng 166] if (message == '1') {
  4. [Dòng 192] return this._b == 3
  5. [Dòng 192] this._b == 9
  6. [Dòng 192] this._b == 11
  7. [Dòng 192] this._b == 16
  8. [Dòng 192] this._b == 17
  9. [Dòng 192] this._b == 19
  10. [Dòng 192] this._b == 25
  11. [Dòng 192] this._b == 44
  12. [Dòng 193] this._b == 57
  13. [Dòng 193] this._b == 59
  14. [Dòng 193] this._b == 61
  15. [Dòng 193] this._b == 63
  16. [Dòng 193] this._b == 69
  17. [Dòng 197] return this._b == 6
  18. [Dòng 197] this._b == 2
  19. [Dòng 201] return this._b == 18
  20. [Dòng 209] return this._b == 20
  21. [Dòng 209] this._b == 33
  22. [Dòng 209] this._b == 39
  23. [Dòng 209] this._b == 43
  24. [Dòng 209] this._b == 45
  25. [Dòng 209] this._b == 64
  26. [Dòng 210] this._b == 67
  27. [Dòng 210] this._b == 72
  28. [Dòng 210] this._b == 73
  29. [Dòng 210] this._b == 36
  30. [Dòng 210] this._b == 68
  31. [Dòng 210] this._b == 74
  32. [Dòng 210] this._b == 75
  33. [Dòng 252] if (parseInt(b) == parseInt(v.substring(0, 6))) {
  34. [Dòng 430] event.inputType == 'deleteContentBackward') {
  35. [Dòng 431] if (event.target.name == 'exp_date'
  36. [Dòng 439] event.inputType == 'insertCompositionText') {
  37. [Dòng 520] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  38. [Dòng 531] } else if (this._b == 2) {
  39. [Dòng 533] } else if (this._b == 6) {
  40. [Dòng 535] } else if (this._b == 31) {
  41. [Dòng 559] this.token_site == 'onepay'
  42. [Dòng 562] if (this._b == 5) {
  43. [Dòng 575] if (this._b == 12) {
  44. [Dòng 590] if (this._res_post.state == 'approved'
  45. [Dòng 590] this._res_post.state == 'failed') {
  46. [Dòng 639] } else if (this._res_post.state == 'authorization_required') {
  47. [Dòng 661] if (this._b == 1
  48. [Dòng 661] this._b == 14
  49. [Dòng 661] this._b == 15
  50. [Dòng 661] this._b == 24
  51. [Dòng 661] this._b == 8
  52. [Dòng 661] this._b == 10
  53. [Dòng 661] this._b == 18
  54. [Dòng 661] this._b == 20
  55. [Dòng 661] this._b == 22
  56. [Dòng 661] this._b == 23
  57. [Dòng 661] this._b == 27
  58. [Dòng 661] this._b == 30
  59. [Dòng 661] this._b == 12
  60. [Dòng 661] this._b == 5
  61. [Dòng 661] this._b == 9) {
  62. [Dòng 732] if (err.status == 400
  63. [Dòng 732] err.error['name'] == 'INVALID_INPUT_BIN') {
  64. [Dòng 749] if ((cardNo.length == 16
  65. [Dòng 750] (cardNo.length == 19
  66. [Dòng 750] (cardNo.length == 19 && (this._b == 1
  67. [Dòng 750] this._b == 4
  68. [Dòng 750] this._b == 55
  69. [Dòng 750] this._b == 47
  70. [Dòng 750] this._b == 48
  71. [Dòng 750] this._b == 67))
  72. [Dòng 752] this._util.checkMod10(cardNo) == true
  73. [Dòng 771] this.c_card = !this.filteredData.some(obj=> this._b == parseInt(obj?.b?.id));
  74. [Dòng 842] return ((value.length == 4
  75. [Dòng 842] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  76. [Dòng 842] value.length == 5) && parseInt(value.split('/')[0]
  77. [Dòng 875] this._inExpDate.length == 4
  78. [Dòng 875] this._inExpDate.search('/') == -1)
  79. [Dòng 876] this._inExpDate.length == 5))
  80. [Dòng 913] if (id == 1) {
  81. [Dòng 916] if (id == 2) {
  82. [Dòng 919] if (id == 3) {
  83. [Dòng 938] numberBank == 2) { // bank bảo trì
  84. [Dòng 1095] this._b == 67) {
  85. [Dòng 1110] if (res.status == '200'
  86. [Dòng 1110] res.status == '201') {

!== (3 điều kiện):
  1. [Dòng 565] key !== '3') {
  2. [Dòng 603] codeResponse.toString() !== '0') {
  3. [Dòng 663] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (38 điều kiện):
  1. [Dòng 131] this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
  2. [Dòng 160] if (this.htmlDesc != null
  3. [Dòng 347] if (this.oldBankId != this._b) this.previewCardService.setResetData(true)
  4. [Dòng 369] if (this.oldBankId != 0) {
  5. [Dòng 379] if (ua.indexOf('safari') != -1
  6. [Dòng 432] if (this._inExpDate.length != 3) {
  7. [Dòng 514] if (this._b != 3
  8. [Dòng 514] this._b != 9
  9. [Dòng 514] this._b != 16
  10. [Dòng 514] this._b != 17
  11. [Dòng 514] this._b != 18
  12. [Dòng 514] this._b != 19
  13. [Dòng 514] this._b != 25
  14. [Dòng 514] this._b != 44
  15. [Dòng 515] this._b != 57
  16. [Dòng 515] this._b != 59
  17. [Dòng 515] this._b != 61
  18. [Dòng 515] this._b != 63
  19. [Dòng 515] this._b != 69
  20. [Dòng 515] this._b != 6
  21. [Dòng 515] this._b != 2
  22. [Dòng 515] this._b != 57) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
  23. [Dòng 529] '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75'].indexOf(this._b.toString()) != -1) {
  24. [Dòng 592] if (this._res_post.return_url != null) {
  25. [Dòng 595] if (this._res_post.links != null
  26. [Dòng 595] this._res_post.links.merchant_return != null
  27. [Dòng 595] this._res_post.links.merchant_return.href != null) {
  28. [Dòng 644] if (this._res_post.authorization != null
  29. [Dòng 644] this._res_post.authorization.links != null
  30. [Dòng 649] this._res_post.links.cancel != null) {
  31. [Dòng 655] let userName = _formCard.name != null ? _formCard.name : ''
  32. [Dòng 656] this._res_post.authorization.links.approval != null
  33. [Dòng 656] this._res_post.authorization.links.approval.href != null) {
  34. [Dòng 659] userName = paramUserName != null ? paramUserName : ''
  35. [Dòng 1111] if (res.body != null
  36. [Dòng 1111] res.body.links != null
  37. [Dòng 1111] res.body.links.merchant_return != null
  38. [Dòng 1112] res.body.links.merchant_return.href != null) {

================================================================================

📁 FILE 102: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version2/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 103: domescard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version2/domescard-main.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 2] (token || _auth==1) && _b != 16
  2. [Dòng 4] _auth==1) && _b != 16; else noBankSelected">{{'otp_header_title' | translate}}</h3>
  3. [Dòng 18] _auth==1) || (token
  4. [Dòng 18] _b == 16)"

!= (1 điều kiện):
  1. [Dòng 4] (token || _auth==1) && _b != 16; else noBankSelected

================================================================================

📁 FILE 104: domescard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main-version2/domescard-main.component.ts
📊 Thống kê: 68 điều kiện duy nhất
   - === : 26 lần
   - == : 35 lần
   - !== : 1 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (26 điều kiện):
  1. [Dòng 230] if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
  2. [Dòng 231] filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
  3. [Dòng 276] if (valOut === 'auth') {
  4. [Dòng 356] if (this._b === '1'
  5. [Dòng 356] this._b === '20'
  6. [Dòng 356] this._b === '64') {
  7. [Dòng 359] if (this._b === '36'
  8. [Dòng 359] this._b === '18'
  9. [Dòng 362] if (this._b === '19'
  10. [Dòng 362] this._b === '16'
  11. [Dòng 362] this._b === '25'
  12. [Dòng 362] this._b === '33'
  13. [Dòng 363] this._b === '39'
  14. [Dòng 363] this._b === '9'
  15. [Dòng 363] this._b === '11'
  16. [Dòng 363] this._b === '17'
  17. [Dòng 364] this._b === '36'
  18. [Dòng 364] this._b === '44'
  19. [Dòng 365] this._b === '64'
  20. [Dòng 368] if (this._b === '20'
  21. [Dòng 371] if (this._b === '18') {
  22. [Dòng 388] return (bankId === '1'
  23. [Dòng 388] bankId === '20'
  24. [Dòng 388] bankId === '64');
  25. [Dòng 392] return (bankId === '36'
  26. [Dòng 392] bankId === '18'

== (35 điều kiện):
  1. [Dòng 154] this._auth == 0
  2. [Dòng 154] this.tokenList?.length == 0) {
  3. [Dòng 217] if (this.filteredData && (this.filteredData?.length == 1
  4. [Dòng 217] this.filteredData?.length == 2) && !this.tokenList?.length && this.onePaymentMethod) {
  5. [Dòng 253] if ($event && ($event == 'true'
  6. [Dòng 359] this._b == '55'
  7. [Dòng 359] this._b == '47'
  8. [Dòng 359] this._b == '48'
  9. [Dòng 359] this._b == '19'
  10. [Dòng 359] this._b == '59'
  11. [Dòng 359] this._b == '73'
  12. [Dòng 359] this._b == '12') {
  13. [Dòng 362] this._b == '3'
  14. [Dòng 363] this._b == '43'
  15. [Dòng 363] this._b == '45'
  16. [Dòng 364] this._b == '57'
  17. [Dòng 365] this._b == '61'
  18. [Dòng 365] this._b == '63'
  19. [Dòng 365] this._b == '67'
  20. [Dòng 365] this._b == '68'
  21. [Dòng 365] this._b == '69'
  22. [Dòng 365] this._b == '72'
  23. [Dòng 365] this._b == '74'
  24. [Dòng 365] this._b == '75') {
  25. [Dòng 368] this._b == '36'
  26. [Dòng 368] this._b == '75') { //sonnh them 72 Vietbank
  27. [Dòng 392] bankId == '55'
  28. [Dòng 392] bankId == '47'
  29. [Dòng 392] bankId == '48'
  30. [Dòng 392] bankId == '19'
  31. [Dòng 392] bankId == '59'
  32. [Dòng 392] bankId == '73'
  33. [Dòng 392] bankId == '5'
  34. [Dòng 392] bankId == '12');
  35. [Dòng 407] if (item['id'] == this._b) {

!== (1 điều kiện):
  1. [Dòng 105] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (6 điều kiện):
  1. [Dòng 134] if (params['locale'] != null) {
  2. [Dòng 140] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 180] if (!(strInstrument != null
  4. [Dòng 183] if (strInstrument.substring(0, 1) != '^'
  5. [Dòng 183] strInstrument.substr(strInstrument.length - 1) != '$') {
  6. [Dòng 284] if (bankid != null) {

================================================================================

📁 FILE 105: domescard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1] domescard_version == '1'"
  2. [Dòng 4] domescard_version == '2'"

================================================================================

📁 FILE 106: domescard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/domescard-main/domescard-main.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 107: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/intercard-main/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 108: component-select.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/intercard-main/form/component-select/component-select.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 18] viewName == 'auth_method_ocean'"
  2. [Dòng 20] (click)="selectData(viewName == 'province' ? item.label : item.name
  3. [Dòng 21] item.code == codeSelected"
  4. [Dòng 22] {{ viewName == 'province' ? item.label : item.name }}

================================================================================

📁 FILE 109: component-select.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/intercard-main/form/component-select/component-select.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 110: component-select.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/intercard-main/form/component-select/component-select.component.ts
📊 Thống kê: 10 điều kiện duy nhất
   - === : 3 lần
   - == : 5 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 77] index === self.findIndex((c) =>
  2. [Dòng 78] c.code === country.code
  3. [Dòng 130] const findData = this.datas.find(item => item?.code === this.codeSelected);

== (5 điều kiện):
  1. [Dòng 40] if (this.viewName == ViewName.country) this.codeSelected = 'default'
  2. [Dòng 50] if (this.viewName == ViewName.country
  3. [Dòng 54] if (this.viewName == ViewName.province
  4. [Dòng 63] const findInArray = this.datas.find(o => o.code == this.defaultValueByCode)
  5. [Dòng 91] if (this.viewName == ViewName.province) this.filteredDatas = this.datas.filter(item => item?.label?.toLowerCase().includes(this.searchTerm.toLowerCase()));

!= (2 điều kiện):
  1. [Dòng 103] if (code != this.codeSelected) {
  2. [Dòng 120] event.target != elementBg) {

================================================================================

📁 FILE 111: intercard-main-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/intercard-main/form/intercard-main-form.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 1 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 97] <div *ngIf="!((+!!exp_date + +!!csc + +!!card_name) === 2)" style="clear: both;" ></div>

== (1 điều kiện):
  1. [Dòng 79] (!!exp_date + !!csc + !!card_name)==2"

================================================================================

📁 FILE 112: intercard-main-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/intercard-main/form/intercard-main-form.component.ts
📊 Thống kê: 70 điều kiện duy nhất
   - === : 8 lần
   - == : 40 lần
   - !== : 9 lần
   - != : 13 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 188] if (this.themeConfig && (this.themeConfig.csc_config === false)) {
  2. [Dòng 232] this._showName = (config.name === "0")? false : true;
  3. [Dòng 237] this._showEmailPhone = (config.email_phone === "0")? false : true;
  4. [Dòng 617] if (event.keyCode === 8
  5. [Dòng 617] event.key === "Backspace"
  6. [Dòng 711] if ((v.substr(-1) === ' '
  7. [Dòng 1036] this.c_country = _val.value === 'default'
  8. [Dòng 1040] this.c_country = _val === 'default'

== (40 điều kiện):
  1. [Dòng 395] if (this._res_post.state == 'approved'
  2. [Dòng 395] this._res_post.state == 'failed') {
  3. [Dòng 422] } else if (this._res_post.state == 'failed') { // lỗi mới kiểm tra sang trang lỗi
  4. [Dòng 451] } else if (this._res_post.state == 'authorization_required') {
  5. [Dòng 452] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  6. [Dòng 464] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  7. [Dòng 517] v.length == 15) || (v.length == 16
  8. [Dòng 517] v.length == 19))
  9. [Dòng 518] this._util.checkMod10(v) == true) {
  10. [Dòng 568] cardNo.length == 15)
  11. [Dòng 570] cardNo.length == 16)
  12. [Dòng 571] cardNo.startsWith('81')) && (cardNo.length == 16
  13. [Dòng 571] cardNo.length == 19))
  14. [Dòng 617] event.inputType == 'deleteContentBackward') {
  15. [Dòng 618] if (event.target.name == 'exp_date'
  16. [Dòng 626] event.inputType == 'insertCompositionText') {
  17. [Dòng 642] if (((this.valueDate.length == 4
  18. [Dòng 642] this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  19. [Dòng 642] this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  20. [Dòng 711] v.length == 5) {
  21. [Dòng 719] v.length == 4
  22. [Dòng 723] v.length == 3)
  23. [Dòng 752] _val.value.length == 4
  24. [Dòng 756] _val.value.length == 3)
  25. [Dòng 798] this._i_first_name.trim().length == 0
  26. [Dòng 805] this._i_last_name.trim().length == 0
  27. [Dòng 980] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  28. [Dòng 980] this.valueDate.length == 5)
  29. [Dòng 1022] this.requireAvs = this.AVS_COUNTRIES.find(e => e.code == this._i_country_code) ? true : false;
  30. [Dòng 1103] this.valueDate.length == 4
  31. [Dòng 1103] this.valueDate.search('/') == -1)
  32. [Dòng 1104] this.valueDate.length == 5))
  33. [Dòng 1118] this._i_csc.length == 4) ||
  34. [Dòng 1122] this._i_csc.length == 3)
  35. [Dòng 1162] if (id == 1) {
  36. [Dòng 1165] if (id == 2) {
  37. [Dòng 1168] if (id == 3) {
  38. [Dòng 1210] country = this.AVS_COUNTRIES.find(e => e.code == res.bin_country);
  39. [Dòng 1245] countryCode == 'US' ? US_STATES
  40. [Dòng 1246] : countryCode == 'CA' ? CA_STATES

!== (9 điều kiện):
  1. [Dòng 377] if (this.tracking.hasOwnProperty(key) && ((key !== '8'
  2. [Dòng 377] !this._showNameOnCard) || (key !== '9'
  3. [Dòng 404] codeResponse.toString() !== '0'){
  4. [Dòng 711] event.inputType !== 'deleteContentBackward') || v.length == 5) {
  5. [Dòng 1014] if (deviceValue !== 'default') {
  6. [Dòng 1018] this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
  7. [Dòng 1130] this._i_country_code !== 'default'
  8. [Dòng 1155] this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
  9. [Dòng 1176] this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {

!= (13 điều kiện):
  1. [Dòng 202] if (params['locale'] != null) {
  2. [Dòng 208] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 397] if (this._res_post.return_url != null) {
  4. [Dòng 399] } else if (this._res_post.links != null
  5. [Dòng 399] this._res_post.links.merchant_return != null
  6. [Dòng 399] this._res_post.links.merchant_return.href != null) {
  7. [Dòng 497] if (ua.indexOf('safari') != -1
  8. [Dòng 568] cardNo != null
  9. [Dòng 619] if (this.valueDate.length != 3) {
  10. [Dòng 718] v != null
  11. [Dòng 751] this.c_csc = (!(_val.value != null
  12. [Dòng 1116] this._i_csc != null
  13. [Dòng 1212] this.requireAvs = this.isAvsCountry = country != undefined

================================================================================

📁 FILE 113: intercard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/intercard-main/intercard-main.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (3 điều kiện):
  1. [Dòng 33] numberCardActive == 1}"
  2. [Dòng 34] numberCardActive == 1 ? '8px' : '' }">
  3. [Dòng 89] indexTab == 0"

================================================================================

📁 FILE 114: intercard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/intercard-main/intercard-main.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 5 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 139] this.numberCardActive === 1) {

== (5 điều kiện):
  1. [Dòng 108] if (_re.status == '200'
  2. [Dòng 108] _re.status == '201') {
  3. [Dòng 132] if (this.numberCardActive == 1
  4. [Dòng 149] cardActive == 1) this.previewCardService.setInitDataCard({ logo: this.detectLogo(), fixedLogo: true, numberCard: '', name: '', csc: ''
  5. [Dòng 208] if (index == 0) {

================================================================================

📁 FILE 115: applepay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/applepay/applepay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 116: applepay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/applepay/applepay.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 39] if (event.data?.event_type == 'applepay_network_not_supported') {

!= (1 điều kiện):
  1. [Dòng 37] if (event.origin != window.origin) return; // chỉ nhận message từ OnePay

================================================================================

📁 FILE 117: dialog-network-not-supported.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/applepay/dialog-network-not-supported/dialog-network-not-supported.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 118: dialog-network-not-supported.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/applepay/dialog-network-not-supported/dialog-network-not-supported.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 119: applepay-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/applepay-main/applepay-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 120: applepay-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/applepay-main/applepay-main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 121: applepay-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/applepay-main/applepay-main.component.ts
📊 Thống kê: 3 điều kiện duy nhất
   - === : 1 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 111] return currency === 'VND'

== (1 điều kiện):
  1. [Dòng 99] if (network == 'napas'

!= (1 điều kiện):
  1. [Dòng 100] if (network != 'napas'

================================================================================

📁 FILE 122: dialog-rule-nobile-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/dialog-rule-nobile-dialog.html
📊 Thống kê: 10 điều kiện duy nhất
   - === : 0 lần
   - == : 9 lần
   - !== : 1 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (9 điều kiện):
  1. [Dòng 6] <h6>{{ data?.type == 'apple_not_support' ? ('suitable_payment_device' | translate) : ('vietqr_payment_guide' |
  2. [Dòng 15] data?.type == 'apple_not_support'"
  3. [Dòng 31] data?.number_method == 1"
  4. [Dòng 32] data?.number_method == 2"
  5. [Dòng 32] data?.number_method == 3"
  6. [Dòng 171] data?.number_method==1"
  7. [Dòng 172] data?.number_method==2"
  8. [Dòng 172] data?.number_method==3 "
  9. [Dòng 320] data?.type == 'all'"

!== (1 điều kiện):
  1. [Dòng 33] data?.type !== 'apple_not_support'"

================================================================================

📁 FILE 123: google-pay-button-op.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/googlepay/google-pay-button-op/google-pay-button-op.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 124: google-pay-button-op.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/googlepay/google-pay-button-op/google-pay-button-op.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 186] if (GGPaySDKScript.readyState === "loaded"
  2. [Dòng 186] GGPaySDKScript.readyState === "complete") {

================================================================================

📁 FILE 125: types-google-pay.d.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/googlepay/google-pay-button-op/types-google-pay.d.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 126: googlepay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/googlepay/googlepay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 127: googlepay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/googlepay/googlepay.component.ts
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 27] isTop = window === window.top
  2. [Dòng 76] if (approval.method === 'REDIRECT') {
  3. [Dòng 79] } else if (approval.method === 'POST_REDIRECT') {

== (2 điều kiện):
  1. [Dòng 65] if (res?.body?.state == 'approved') {
  2. [Dòng 74] } else if (res?.body?.state == 'authorization_required') {

================================================================================

📁 FILE 128: googlepay-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/googlepay-main/googlepay-main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 129: googlepay-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/googlepay-main/googlepay-main.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 130: samsung-pay-button-op.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 131: samsung-pay-button-op.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.ts
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 124] if (GGPaySDKScript.readyState === "loaded"
  2. [Dòng 124] GGPaySDKScript.readyState === "complete") {

== (1 điều kiện):
  1. [Dòng 63] serviceId: this.environment == 'PRODUCTION' ? this.serviceIdProd : this.serviceIdTest

================================================================================

📁 FILE 132: types-samsung-pay.d.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/samsungpay/samsung-pay-button-op/types-samsung-pay.d.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 133: samsungpay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/samsungpay/samsungpay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 134: samsungpay.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/samsungpay/samsungpay.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 135: samsungpay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/samsungpay/samsungpay.component.ts
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 73] if (approval.method === 'REDIRECT') {
  2. [Dòng 76] } else if (approval.method === 'POST_REDIRECT') {

== (2 điều kiện):
  1. [Dòng 61] if (res?.body?.state == 'approved') {
  2. [Dòng 71] } else if (res?.body?.state == 'authorization_required') {

================================================================================

📁 FILE 136: samsungpay-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/samsungpay-main/samsungpay-main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 137: samsungpay-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/mobile-wallet-main/samsungpay-main/samsungpay-main.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 138: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version1/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 139: qr-detail.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version1/qr-detail/qr-detail.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 157] language==='en' ? 'flex-start' : ''"
  2. [Dòng 161] language==='en' ? '-10px' : ''"

================================================================================

📁 FILE 140: qr-detail.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version1/qr-detail/qr-detail.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 141: qr-detail.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version1/qr-detail/qr-detail.component.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 58] 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {

================================================================================

📁 FILE 142: qr-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version1/qr-dialog/qr-dialog.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 2] screen=='qr'"
  2. [Dòng 93] screen=='confirm_close'"

================================================================================

📁 FILE 143: qr-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version1/qr-dialog/qr-dialog.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 54] 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {

================================================================================

📁 FILE 144: qr-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version1/qr-guide-dialog/qr-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 145: qr-guide-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version1/qr-guide-dialog/qr-guide-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 146: qr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version1/qr-main.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 5 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 39] filteredData.length === 0
  2. [Dòng 39] filteredDataOther.length === 0"
  3. [Dòng 95] filteredDataMobile.length === 0
  4. [Dòng 95] filteredDataOtherMobile.length === 0"
  5. [Dòng 133] appList?.length === 1)" ></app-qr-detail>

================================================================================

📁 FILE 147: qr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version1/qr-main.component.ts
📊 Thống kê: 25 điều kiện duy nhất
   - === : 10 lần
   - == : 9 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (10 điều kiện):
  1. [Dòng 253] this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
  2. [Dòng 254] this.filteredDataOther = this.appList.filter(item => item.type === 'other');
  3. [Dòng 255] this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
  4. [Dòng 256] this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
  5. [Dòng 282] this.appList.length === 1) {
  6. [Dòng 284] if ((this.filteredDataMobile.length === 1
  7. [Dòng 284] this.filteredDataOtherMobile.length === 1)
  8. [Dòng 343] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  9. [Dòng 344] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  10. [Dòng 350] if (item.type === 'mobile_banking') {

== (9 điều kiện):
  1. [Dòng 181] this.themeConfig.deeplink_status == 'Off' ? false : true
  2. [Dòng 300] if (d.b.code == s) {
  3. [Dòng 349] if (item.available == true) {
  4. [Dòng 423] if (_re.status == '200'
  5. [Dòng 423] _re.status == '201') {
  6. [Dòng 426] if (appcode == 'grabpay'
  7. [Dòng 426] appcode == 'momo') {
  8. [Dòng 429] if (type == 2
  9. [Dòng 488] err.error.code == '04') {

!= (6 điều kiện):
  1. [Dòng 197] if (params['locale'] != null) {
  2. [Dòng 203] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 230] if (!(strInstrument != null
  4. [Dòng 379] if (appcode != null) {
  5. [Dòng 741] if (_re.status != '200'
  6. [Dòng 741] _re.status != '201')

================================================================================

📁 FILE 148: safe-html.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version1/safe-html.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 149: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 150: qr-desktop.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-desktop/qr-desktop.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 1 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 130] language==='en' ? '-10px' : ''"

== (1 điều kiện):
  1. [Dòng 30] _translate?.currentLang=='en'"

================================================================================

📁 FILE 151: qr-desktop.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-desktop/qr-desktop.component.ts
📊 Thống kê: 19 điều kiện duy nhất
   - === : 4 lần
   - == : 10 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 394] this.listWalletQR.length === 1) {
  2. [Dòng 444] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  3. [Dòng 445] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  4. [Dòng 799] this.listWalletQR.length === 1

== (10 điều kiện):
  1. [Dòng 290] e.type == 'vnpayqr') {
  2. [Dòng 297] e.type == 'ewallet') {
  3. [Dòng 340] if (_re.status == '200'
  4. [Dòng 340] _re.status == '201') {
  5. [Dòng 372] e.type == 'wallet')) {
  6. [Dòng 411] if (d.b.code == s) {
  7. [Dòng 450] if (item.available == true) {
  8. [Dòng 511] if (appcode == 'grabpay'
  9. [Dòng 511] appcode == 'momo') {
  10. [Dòng 546] err.error.code == '04') {

!= (5 điều kiện):
  1. [Dòng 240] if (params['locale'] != null) {
  2. [Dòng 280] if (!(strInstrument != null
  3. [Dòng 468] if (appcode != null) {
  4. [Dòng 770] if (_re.status != '200'
  5. [Dòng 770] _re.status != '201')

================================================================================

📁 FILE 152: qr-dialog-version2.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-dialog/qr-dialog-version2.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (3 điều kiện):
  1. [Dòng 2] screen=='qr'"
  2. [Dòng 18] bank=='ShopeePay'"
  3. [Dòng 162] screen=='confirm_close'"

================================================================================

📁 FILE 153: qr-dialog-version2.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-dialog/qr-dialog-version2.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 57] 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {

================================================================================

📁 FILE 154: qr-guide-dialog-version2.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-guide-dialog/qr-guide-dialog-version2.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (3 điều kiện):
  1. [Dòng 11] type == 'vnpay'"
  2. [Dòng 12] type == 'bankapp'"
  3. [Dòng 13] type == 'both'"

================================================================================

📁 FILE 155: qr-guide-dialog-version2.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-guide-dialog/qr-guide-dialog-version2.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 156: list-bank-dialog-version2.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-listbank/list-bank-dialog-version2.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 157: list-bank-dialog-version2.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-listbank/list-bank-dialog-version2.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 158: qr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 159: qr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-main.component.ts
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 174] this.themeConfig.deeplink_status == 'Off' ? false : true

!= (2 điều kiện):
  1. [Dòng 190] if (params['locale'] != null) {
  2. [Dòng 196] if ('otp' != this._paymentService.getCurrentPage()) {

================================================================================

📁 FILE 160: qr-mobile.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-mobile/qr-mobile.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 161: qr-mobile.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/qr-mobile/qr-mobile.component.ts
📊 Thống kê: 30 điều kiện duy nhất
   - === : 6 lần
   - == : 19 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (6 điều kiện):
  1. [Dòng 490] if (!this.d_vnpayqr_wallet && !this.d_vnpayqr_deeplink && (this.listWalletQR?.length === 1
  2. [Dòng 490] this.listWalletDeeplink?.length === 1)) {
  3. [Dòng 607] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  4. [Dòng 608] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  5. [Dòng 614] if (item.type === 'deeplink') {
  6. [Dòng 1047] this.listWalletQR.length === 1

== (19 điều kiện):
  1. [Dòng 307] e.type == 'deeplink') {
  2. [Dòng 318] e.type == 'ewallet'
  3. [Dòng 338] e.type == 'vnpayqr') {
  4. [Dòng 352] e.type == 'wallet')) {
  5. [Dòng 381] e.type == 'ewallet') {
  6. [Dòng 422] this.listWallet.length == 1
  7. [Dòng 422] this.listWallet[0].code == 'momo') {
  8. [Dòng 424] this.checkEWalletDeeplink.length == 0) {
  9. [Dòng 508] arrayWallet.length == 0) return false;
  10. [Dòng 510] if (arrayWallet[i].code == key) {
  11. [Dòng 542] if (_re.status == '200'
  12. [Dòng 542] _re.status == '201') {
  13. [Dòng 564] if (d.b.code == s) {
  14. [Dòng 613] if (item.available == true) {
  15. [Dòng 692] if (appcode == 'grabpay'
  16. [Dòng 692] appcode == 'momo') {
  17. [Dòng 695] if (type == 2) {
  18. [Dòng 736] err.error.code == '04') {
  19. [Dòng 831] if (type == 2

!= (5 điều kiện):
  1. [Dòng 245] if (params['locale'] != null) {
  2. [Dòng 288] if (!(strInstrument != null
  3. [Dòng 641] if (appcode != null) {
  4. [Dòng 1007] if (_re.status != '200'
  5. [Dòng 1007] _re.status != '201')

================================================================================

📁 FILE 162: safe-html.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main-version2/safe-html.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 163: qr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (3 điều kiện):
  1. [Dòng 1] <qr-main-version1 *ngIf="((qr_version2 == 'qrV1'
  2. [Dòng 1] version2 == 'default') || (version2 == '1')" [data]="data"
  3. [Dòng 5] version2 == '2'"

================================================================================

📁 FILE 164: qr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/qr-main/qr-main.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 165: vietqr-guide.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/vietqr-main/vietqr-guide/vietqr-guide.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 166: vietqr-guide.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/vietqr-main/vietqr-guide/vietqr-guide.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 167: vietqr-listbank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/vietqr-main/vietqr-listbank/vietqr-listbank.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 168: vietqr-listbank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/vietqr-main/vietqr-listbank/vietqr-listbank.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 41] if (banksRes?.status == 200
  2. [Dòng 44] if (appRes?.status == 200

================================================================================

📁 FILE 169: vietqr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/vietqr-main/vietqr-main.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 9] vietQRData?.state=='created' else state_failed"
  2. [Dòng 118] vietQRData?.state=='created'"

================================================================================

📁 FILE 170: vietqr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-method/vietqr-main/vietqr-main.component.ts
📊 Thống kê: 7 điều kiện duy nhất
   - === : 1 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 75] if (this.locale === 'en') {

== (6 điều kiện):
  1. [Dòng 101] if (res.status == '200'
  2. [Dòng 101] res.status == '201') {
  3. [Dòng 105] if (res.body?.state == 'created') {
  4. [Dòng 129] if (payment?.state == 'failed'
  5. [Dòng 129] payment?.state == 'canceled'
  6. [Dòng 129] payment?.state == 'expired') {

================================================================================

📁 FILE 171: bnpl-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/bnpl-form/bnpl-form.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 4] type === 5"

== (2 điều kiện):
  1. [Dòng 6] !token) || (type == 5
  2. [Dòng 14] type == 5"

================================================================================

📁 FILE 172: bnpl-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/bnpl-form/bnpl-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 173: domescard-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/domescard-form/domescard-form.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 3] type === 2"
  2. [Dòng 7] domescardVersionNumber===1

== (2 điều kiện):
  1. [Dòng 1] !token)  || (type == 2
  2. [Dòng 15] type == 2"

================================================================================

📁 FILE 174: domescard-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/domescard-form/domescard-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 175: intercard-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/intercard-form/intercard-form.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 3] type === 1"

== (2 điều kiện):
  1. [Dòng 1] !token) || (type == 1
  2. [Dòng 21] type == 1"

================================================================================

📁 FILE 176: intercard-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/intercard-form/intercard-form.component.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 1 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 45] if (Number(this.numberCardActive) === 1

================================================================================

📁 FILE 177: mobile-wallet-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/mobile-wallet-form/mobile-wallet-form.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 1 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 3] type === paymentType"

== (4 điều kiện):
  1. [Dòng 1] type == paymentType"
  2. [Dòng 9] paymentType == PaymentType.ApplePay"
  3. [Dòng 12] paymentType == PaymentType.GooglePay"
  4. [Dòng 15] paymentType == PaymentType.SamsungPay"

================================================================================

📁 FILE 178: mobile-wallet-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/mobile-wallet-form/mobile-wallet-form.component.ts
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (3 điều kiện):
  1. [Dòng 43] this.paymentType == PaymentTypeMobile.ApplePay) {
  2. [Dòng 47] this.paymentType == PaymentTypeMobile.GooglePay) {
  3. [Dòng 51] this.paymentType == PaymentTypeMobile.SamsungPay) {

================================================================================

📁 FILE 179: paypal-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/paypal-form/paypal-form.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 1 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 3] type === 3"

== (1 điều kiện):
  1. [Dòng 10] type == 3"

================================================================================

📁 FILE 180: paypal-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/paypal-form/paypal-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 181: qr-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/qr-form/qr-form.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 1 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 3] type === 4"

== (1 điều kiện):
  1. [Dòng 14] type == 4"

================================================================================

📁 FILE 182: qr-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/qr-form/qr-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 183: vietqr-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/vietqr-form/vietqr-form.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 3] type === 7"

== (2 điều kiện):
  1. [Dòng 1] !token) || (type == 7
  2. [Dòng 13] type == 7"

================================================================================

📁 FILE 184: vietqr-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-option/vietqr-form/vietqr-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 185: preview-card.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-right/preview-card/preview-card.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 186: preview-card.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-right/preview-card/preview-card.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 187: preview-card.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/menu/payment-right/preview-card/preview-card.component.ts
📊 Thống kê: 7 điều kiện duy nhất
   - === : 3 lần
   - == : 2 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 63] const isSingleCardActive = Object.values(this.cardInternations).filter(value => value === true).length === 1;
  2. [Dòng 63] const isSingleCardActive = Object.values(this.cardInternations).filter(value => value === true).length === 1
  3. [Dòng 192] if (typeof strTrim.length === 'number') {

== (2 điều kiện):
  1. [Dòng 115] if (type == '1') { // Thanh toán bằng thẻ tín dụng/ghi nợ
  2. [Dòng 119] } else if (type == '2') { // Thanh toán bằng thẻ ATM/STK ngân hàng

!== (2 điều kiện):
  1. [Dòng 114] const diff = (this.oldb !== b) || (this.oldType !== type)
  2. [Dòng 250] if (this.cardNumberMaxLength !== 15

================================================================================

📁 FILE 188: app-result.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/result/app-result/app-result.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 189: app-result.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/result/app-result/app-result.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 190: error.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/result/error/error.component.html
📊 Thống kê: 19 điều kiện duy nhất
   - === : 6 lần
   - == : 13 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (6 điều kiện):
  1. [Dòng 95] checkDupTran === false"
  2. [Dòng 152] isPopupSupport === 'True')
  3. [Dòng 154] isPopupSupport === 'True'"
  4. [Dòng 167] 'afs-mobile': locale === 'en'
  5. [Dòng 168] 'left-en': locale === 'en'
  6. [Dòng 171] locale === 'en'

== (13 điều kiện):
  1. [Dòng 17] errorCode == '11'"
  2. [Dòng 40] isappleerror ==false"
  3. [Dòng 43] isappleerror==true"
  4. [Dòng 53] errorCode && (errorCode == '253' || errorCode == 'overtime')
  5. [Dòng 53] errorCode == 'overtime')">
  6. [Dòng 60] errorCode == 'INVALID_CARD_LIST'"
  7. [Dòng 152] <div class="footer-button" *ngIf="(isSent == false
  8. [Dòng 154] isSent == false
  9. [Dòng 180] <div *ngIf="(isSent == false
  10. [Dòng 200] <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
  11. [Dòng 200] (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
  12. [Dòng 206] *ngIf="!(errorCode == 'overtime'
  13. [Dòng 206] !(errorCode == 'overtime' || errorCode == '253') && isBack && !invoiceNearExpire && !paymentPending

================================================================================

📁 FILE 191: error.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/result/error/error.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 192: error.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/result/error/error.component.ts
📊 Thống kê: 65 điều kiện duy nhất
   - === : 13 lần
   - == : 36 lần
   - !== : 2 lần
   - != : 14 lần
--------------------------------------------------------------------------------

=== (13 điều kiện):
  1. [Dòng 215] params.timeout === 'true') {
  2. [Dòng 224] params.kbank === 'true') {
  3. [Dòng 241] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  4. [Dòng 241] _re.body.state === 'unpaid');
  5. [Dòng 383] if (this.errorCode === 'overtime'
  6. [Dòng 383] this.errorCode === '253') {
  7. [Dòng 497] params.name === 'CUSTOMER_INTIME'
  8. [Dòng 497] params.code === '09') {
  9. [Dòng 513] params.name === 'INVALID_CARD_LIST'
  10. [Dòng 513] params.code === '10') {
  11. [Dòng 544] if (this.maxpayment === this.paymentsNum) {
  12. [Dòng 616] if (param === key) {
  13. [Dòng 668] if (this.timeLeft === 0) {

== (36 điều kiện):
  1. [Dòng 199] if (params && (params['bnpl'] == 'true')) {
  2. [Dòng 203] if (params && (params['bnpl'] == 'false')) {
  3. [Dòng 242] if (_re.body.state == 'closed')
  4. [Dòng 315] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'amigo') {
  5. [Dòng 322] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'kredivo') {
  6. [Dòng 327] if (this.paymentInformation.type == "applepay") {
  7. [Dòng 332] } else if (this.paymentInformation.type == "googlepay") {
  8. [Dòng 353] if (this.res.themes && (this.res.themes.theme == 'general'
  9. [Dòng 353] this.res.themes.theme == 'generalv2')) {
  10. [Dòng 359] params.response_code == 'overtime') {
  11. [Dòng 407] if (_re.status == '200'
  12. [Dòng 407] _re.status == '201') {
  13. [Dòng 419] if (_re2.status == '200'
  14. [Dòng 419] _re2.status == '201') {
  15. [Dòng 432] if (this.errorCode == 'overtime'
  16. [Dòng 432] this.errorCode == '253') {
  17. [Dòng 436] } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
  18. [Dòng 439] } else if (this.errorCode == 'AM1') {
  19. [Dòng 443] if (this.paymentInformation.type == 'bnpl') {
  20. [Dòng 445] if (this.paymentInformation.provider == 'amigo'
  21. [Dòng 445] this.errorCode == '2') {
  22. [Dòng 448] else if (this.paymentInformation.provider == 'kbank'
  23. [Dòng 451] else if (this.paymentInformation.provider == 'homecredit'
  24. [Dòng 454] else if (this.paymentInformation.provider == 'kredivo'
  25. [Dòng 457] else if (this.paymentInformation.provider == 'fundiin'
  26. [Dòng 467] this.res.state == 'canceled') {
  27. [Dòng 491] if (lastPayment?.state == 'pending') {
  28. [Dòng 588] if(this.locale == 'vi')
  29. [Dòng 666] if (this.isTimePause == false) {
  30. [Dòng 686] if (this.isappleerror == true) {
  31. [Dòng 773] if (response.body.state == 'not_paid') {
  32. [Dòng 775] if (response.body.payments[response.body.payments.length - 1].state == "failed") {
  33. [Dòng 801] } else if (response.body.state == 'paid') {
  34. [Dòng 807] } else if (response.body.state == 'canceled'
  35. [Dòng 807] response.body.state == 'closed'
  36. [Dòng 807] response.body.state == 'expired') {

!== (2 điều kiện):
  1. [Dòng 611] queryString = (sourceURL.indexOf("?") !== -1) ? sourceURL.split("?")[1] : "";
  2. [Dòng 612] if (queryString !== "") {

!= (14 điều kiện):
  1. [Dòng 164] if (message != ''
  2. [Dòng 164] message != null
  3. [Dòng 164] message != undefined) {
  4. [Dòng 313] } else if (this.res.payments[this.res.payments.length - 1].instrument.issuer.brand != null
  5. [Dòng 314] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id != null
  6. [Dòng 750] if (_re != null
  7. [Dòng 750] _re.links != null
  8. [Dòng 750] _re.links.merchant_return != null
  9. [Dòng 751] _re.links.merchant_return.href != null) {
  10. [Dòng 753] } else if (_re.body != null
  11. [Dòng 753] _re.body.links != null
  12. [Dòng 753] _re.body.links.merchant_return != null
  13. [Dòng 754] _re.body.links.merchant_return.href != null) {
  14. [Dòng 823] if (this.url_new_invoice != null) {

================================================================================

📁 FILE 193: support-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/result/error/support-dialog/support-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 194: support-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/result/error/support-dialog/support-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 195: queuing.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/result/queuing/queuing.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 196: queuing.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/result/queuing/queuing.component.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 62] if (this.locale == 'vi') {

================================================================================

📁 FILE 197: success.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/result/success/success.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 52] amigo_type == 'SP'"
  2. [Dòng 96] amigo_type == 'PL'"

================================================================================

📁 FILE 198: success.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/main/result/success/success.component.ts
📊 Thống kê: 9 điều kiện duy nhất
   - === : 5 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 139] params.timeout === 'true') {
  2. [Dòng 158] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  3. [Dòng 158] _re.body.state === 'unpaid');
  4. [Dòng 235] params.name === 'CUSTOMER_INTIME'
  5. [Dòng 235] params.code === '09') {

== (2 điều kiện):
  1. [Dòng 211] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
  2. [Dòng 222] this.res.themes.theme == 'general') {

!= (2 điều kiện):
  1. [Dòng 209] } else if (this.res.payments[this.res.payments.length - 1].instrument.issuer.brand != null
  2. [Dòng 210] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id != null

================================================================================

📁 FILE 199: bnpl-management.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/model/bnpl-management.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 200: fundiin-management.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/model/fundiin-management.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 201: homecredit-management.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/model/homecredit-management.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 202: kbank-management.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/model/kbank-management.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 203: auth.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/auth.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 204: cancel-tran.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/cancel-tran.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 205: change-color.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/change-color.service.ts
📊 Thống kê: 12 điều kiện duy nhất
   - === : 2 lần
   - == : 8 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 214] s = max === 0 ? 0 : d / max
  2. [Dòng 216] if (max === min) {

== (8 điều kiện):
  1. [Dòng 66] const findData = dataConfig.find((obj: any) => obj.value == color)
  2. [Dòng 131] color == this.defaultColor.color5) data = this.defaultColor
  3. [Dòng 149] if (H.length == 4) {
  4. [Dòng 153] } else if (H.length == 7) {
  5. [Dòng 170] if (delta == 0) {
  6. [Dòng 172] } else if (cmax == r) {
  7. [Dòng 174] } else if (cmax == g) {
  8. [Dòng 187] s = delta == 0 ? 0 : delta / (1 - Math.abs(2 * l - 1))

!= (2 điều kiện):
  1. [Dòng 65] color != this.defaultColor.color5) { // tìm xem màu có trong config không
  2. [Dòng 100] color != this.defaultColor.color5) {

================================================================================

📁 FILE 206: close-dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/close-dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 207: confirm.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/confirm.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 208: countdown.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/countdown.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 209: counter.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/counter/counter.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 210: counter.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/counter/counter.directive.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 211: currency-service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/currency-service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 212: data.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/data.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 139] const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
  2. [Dòng 139] item.method === method) : null;

================================================================================

📁 FILE 213: deep_link.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/deep_link.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 9] if (isIphone == true) {
  2. [Dòng 22] } else if (isAndroid == true) {

================================================================================

📁 FILE 214: dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 215: digital-wallet.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/digital-wallet.service.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 1 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 200] this.applePaySupport = element.value === 'true'

================================================================================

📁 FILE 216: focus-input.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/focus-input.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 217: format-date.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/format-date/format-date.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 218: format-date.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/format-date/format-date.directive.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0
  2. [Dòng 123] YY % 4 === 0)) {
  3. [Dòng 138] if (YYYY % 400 === 0
  4. [Dòng 138] YYYY % 4 === 0)) {

!== (2 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0 || (YY % 100 !== 0
  2. [Dòng 138] if (YYYY % 400 === 0 || (YYYY % 100 !== 0

================================================================================

📁 FILE 219: handle_bnpl_token.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/handle_bnpl_token.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 220: on-off-scroll.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/on-off-scroll.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 221: overlay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/overlay/overlay.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 1 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 2] _translate?.currentLang == 'en'"

!== (1 điều kiện):
  1. [Dòng 3] _translate?.currentLang !== 'en'"

================================================================================

📁 FILE 222: overlay.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/overlay/overlay.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 223: overlay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/overlay/overlay.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 224: payment.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/payment.service.ts
📊 Thống kê: 15 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 11 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 646] if (res.status == '200'
  2. [Dòng 646] res.status == '201') {
  3. [Dòng 657] return countPayment == maxPayment
  4. [Dòng 695] if (this.getLatestPayment().state == 'canceled')

!= (11 điều kiện):
  1. [Dòng 119] if (idInvoice != null
  2. [Dòng 119] idInvoice != 0)
  3. [Dòng 129] idInvoice != 0) {
  4. [Dòng 319] let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
  5. [Dòng 333] if (this._merchantid != null
  6. [Dòng 333] this._tranref != null
  7. [Dòng 333] this._state != null
  8. [Dòng 418] urlCancel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
  9. [Dòng 457] if (paymentId != null) {
  10. [Dòng 555] if (res?.status != 200
  11. [Dòng 555] res?.status != 201) return;

================================================================================

📁 FILE 225: bank-amount.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/pipe/bank-amount.pipe.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 11] return ((a.id == id
  2. [Dòng 11] a.code == id) && a.type.includes(type));

================================================================================

📁 FILE 226: preview-card.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/preview-card.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 227: qr.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/qr.service.ts
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 61] if (res?.state == 'canceled') {
  2. [Dòng 106] state == 'authorization_required'

!= (3 điều kiện):
  1. [Dòng 43] if (_re.status != '200'
  2. [Dòng 43] _re.status != '201') {
  3. [Dòng 52] latestPayment?.state != "authorization_required") {

================================================================================

📁 FILE 228: search-bank.service.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/search-bank.service.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 229: search-bank.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/search-bank.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 230: step-screen-mobile.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/step-screen-mobile.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 231: time-stop.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/time-stop.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 232: token-main.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/token-main.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 233: index.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/translate/index.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 234: lang-en.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/translate/lang-en.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 235: lang-vi.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/translate/lang-vi.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 236: translate.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/translate/translate.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 237: translate.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/translate/translate.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 37] if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
  2. [Dòng 38] else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';

================================================================================

📁 FILE 238: translations.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/translate/translations.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 239: vietqr.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/services/vietqr.service.ts
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 43] if (_re.status == '200'
  2. [Dòng 43] _re.status == '201') {
  3. [Dòng 58] } else if (latestPayment?.state == "failed") {
  4. [Dòng 64] if (res?.state == 'canceled') {

!= (1 điều kiện):
  1. [Dòng 55] if (latestPayment?.instrument?.type != "vietqr") {

================================================================================

📁 FILE 240: apps-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/util/apps-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 712] if (e.name == bankSwift) { // TODO: get by swift
  2. [Dòng 720] return this.apps.find(e => e.code == appCode);

================================================================================

📁 FILE 241: apps-information.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/util/apps-information.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 242: apps-infov2.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/util/apps-infov2.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 546] if (e.name == bankSwift) { // TODO: get by swift

================================================================================

📁 FILE 243: banks-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/util/banks-info.ts
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (3 điều kiện):
  1. [Dòng 1106] if (e.id == bankId) {
  2. [Dòng 1116] if (+e.id == bankId) {
  3. [Dòng 1156] if (e.swiftCode == bankSwift) {

================================================================================

📁 FILE 244: error-handler.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/util/error-handler.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 9] err?.status === 400
  2. [Dòng 10] err?.error?.name === 'INVALID_CARD_FEE'

================================================================================

📁 FILE 245: iso-ca-states.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/util/iso-ca-states.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 246: iso-us-states.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/util/iso-us-states.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 247: mobile-wallet-type.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/util/mobile-wallet-type.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 248: util.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/app/util/util.ts
📊 Thống kê: 40 điều kiện duy nhất
   - === : 16 lần
   - == : 17 lần
   - !== : 3 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (16 điều kiện):
  1. [Dòng 13] if (target.tagName === 'A'
  2. [Dòng 88] if (v.length === 2
  3. [Dòng 88] this.flag.length === 3
  4. [Dòng 88] this.flag.charAt(this.flag.length - 1) === '/') {
  5. [Dòng 92] if (v.length === 1) {
  6. [Dòng 94] } else if (v.length === 2) {
  7. [Dòng 97] v.length === 2) {
  8. [Dòng 105] if (len === 2) {
  9. [Dòng 205] if (M[1] === 'Chrome') {
  10. [Dòng 374] if (param === key) {
  11. [Dòng 536] pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
  12. [Dòng 540] target === 0
  13. [Dòng 615] if (cardTypeBank === 'bank_card_number') {
  14. [Dòng 618] } else if (cardTypeBank === 'bank_account_number') {
  15. [Dòng 668] if (event.keyCode === 8
  16. [Dòng 668] event.key === "Backspace"

== (17 điều kiện):
  1. [Dòng 38] if (temp.length == 0) {
  2. [Dòng 45] return (counter % 10 == 0);
  3. [Dòng 179] if (this.checkCount == 1) {
  4. [Dòng 191] if (results == null) {
  5. [Dòng 224] if (c.length == 3) {
  6. [Dòng 237] d = d == undefined ? '.' : d
  7. [Dòng 238] t = t == undefined ? '
  8. [Dòng 362] return results == null ? null : results[1]
  9. [Dòng 668] event.inputType == 'deleteContentBackward') {
  10. [Dòng 669] if (event.target.name == 'exp_date'
  11. [Dòng 677] event.inputType == 'insertCompositionText') {
  12. [Dòng 691] if (((_val.length == 4
  13. [Dòng 691] _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  14. [Dòng 691] _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  15. [Dòng 717] iss_date.length == 4
  16. [Dòng 717] iss_date.search('/') == -1)
  17. [Dòng 718] iss_date.length == 5))

!== (3 điều kiện):
  1. [Dòng 369] queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
  2. [Dòng 370] if (queryString !== '') {
  3. [Dòng 540] if (target !== 0

!= (4 điều kiện):
  1. [Dòng 207] if (tem != null) {
  2. [Dòng 212] if ((tem = ua.match(/version\/(\d+)/i)) != null) {
  3. [Dòng 613] if (ua.indexOf('safari') != -1
  4. [Dòng 670] if (v.length != 3) {

================================================================================

📁 FILE 249: apple.js
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/assets/script/apple.js
📊 Thống kê: 15 điều kiện duy nhất
   - === : 0 lần
   - == : 13 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (13 điều kiện):
  1. [Dòng 69] let applepayNapas = document.getElementById('applepay-napas')?.value == 'true'
  2. [Dòng 70] let applepayVisa = document.getElementById('applepay-visa')?.value == 'true'
  3. [Dòng 71] let applepayMasterCard = document.getElementById('applepay-masterCard')?.value == 'true'
  4. [Dòng 72] let applepayJCB = document.getElementById('applepay-jcb')?.value == 'true'
  5. [Dòng 76] if (applepayNapas == true) {
  6. [Dòng 80] if (applepayVisa == true) {
  7. [Dòng 84] if (applepayMasterCard == true) {
  8. [Dòng 88] if (applepayJCB == true) {
  9. [Dòng 143] if(document.getElementById('applepay-merchantAVS').value == 'true'){
  10. [Dòng 195] response.status == '400') {
  11. [Dòng 197] } else if (response.status == '500') {
  12. [Dòng 208] if (data.name == "CARD_TYPE_CAN_NOT_BE_PROCESSED") { // in case response.status == 400
  13. [Dòng 215] } else if (data.state == "approved"){ // in case response.ok

!= (2 điều kiện):
  1. [Dòng 131] if (network != "napas") return true;
  2. [Dòng 132] if (currency != "VND") return false; // napas accept VND only

================================================================================

📁 FILE 250: google-pay-intergrate.js
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/assets/script/google-pay-intergrate.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 251: google-pay-sdk.js
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/assets/script/google-pay-sdk.js
📊 Thống kê: 363 điều kiện duy nhất
   - === : 86 lần
   - == : 143 lần
   - !== : 54 lần
   - != : 80 lần
--------------------------------------------------------------------------------

=== (86 điều kiện):
  1. [Dòng 112] function"===typeof Symbol
  2. [Dòng 112] "symbol"===typeof Symbol("x")
  3. [Dòng 112] a=1===d.length
  4. [Dòng 112] "es6"===c?e[d]:null
  5. [Dòng 112] configurable:!0,writable:!0,value:b}):b!==c&&(void 0===ea[d]
  6. [Dòng 114] "function"===typeof d
  7. [Dòng 118] })};var e=function(f){this.oa=0;this.W=void 0;this.na=[];this.Hb=!1;var h=this.bb();try{f(h.resolve,h.reject)}catch(l){h.reject(l)}};e.prototype.bb=function(){function f(k){return function(q){l||(l=!0,k.call(h,q))}}var h=this,l=!1;return{resolve:f(this.Ac),reject:f(this.mb)}};e.prototype.Ac=function(f){if(f===this)this.mb(new TypeError("A Promise cannot resolve to itself"));else if(f instanceof e)this.Dc(f);else{a:switch(typeof f){case "object":var h=null!=f;break a;case "function":h=!0;break a;default:h=
  8. [Dòng 119] 2===this.oa
  9. [Dòng 120] 1)};e.prototype.qc=function(){if(this.Hb)return!1;var f=p.CustomEvent,h=p.Event,l=p.dispatchEvent;if("undefined"===typeof l)return!0;"function"===typeof f?f=new f("unhandledrejection"
  10. [Dòng 120] "function"===typeof f?f=new f("unhandledrejection",{cancelable:!0}):"function"===typeof h?f=new h("unhandledrejection",{cancelable:!0}):(f=p.document.createEvent("CustomEvent"),f.initCustomEvent("unhandledrejection",!1,!0,f))
  11. [Dòng 124] return"object"===h
  12. [Dòng 124] "function"===h}if(function(){if(!a||!Object.seal)return!1;try{var f=Object.seal({}),h=Object.seal({}),l=new a([[f,2],[h,3]]);if(2!=l.get(f)||3!=l.get(h))return!1;l.delete(f);l.set(h,4);return!l.has(f)&&4==l.get(h)}catch(k){return!1}}())return a;var d="$jscomp_hidden_"+Math.random(),e=0,g=function(f){this.va=(e+=Math.random()+1).toString()
  13. [Dòng 127] h=v(h);for(var l;!(l=h.next()).done;)l=l.value,this.set(l[0],l[1])}};c.prototype.set=function(h,l){h=0===h?0:h
  14. [Dòng 129] l===y.key)return{id:k,list:q,index:h,u:y}}return{id:k,list:q,index:-1,u:void 0}},e=function(h,l){var k=h[1];return fa(function(){if(k){for(;k.head!=h[1];)k=k.S;for(;k.next!=k.head;)return k=
  15. [Dòng 133] u("Object.is",function(a){return a?a:function(b,c){return b===c?0!==b
  16. [Dòng 133] 1/b===1/c:b!==b
  17. [Dòng 133] u("Object.is",function(a){return a?a:function(b,c){return b===c?0!==b||1/b===1/c:b!==b&&c!==c}},"es6");u("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(0>c&&(c=Math.max(c+e,0));c<e;c++){var g=d[c];if(g===b
  18. [Dòng 139] void 0===b?c=c[d]
  19. [Dòng 142] Ca("Expected object but got %s: %s.",[sa(a),a],b,Array.prototype.slice.call(arguments,2))};var Ia=function(a,b){this.Wb=a===Ga
  20. [Dòng 142] a.constructor===Ia
  21. [Dòng 142] a.bc===Ha)return a.Wb;Da("expected object of type Const
  22. [Dòng 170] Ya=["ELECTRON","MAESTRO","ELO_DEBIT"];function D(a,b){b=void 0===b?document:b
  23. [Dòng 170] b)};var $a,ab=function(){if(void 0===$a){var a=null,b=x.trustedTypes;if(b&&b.createPolicy)try{a=b.createPolicy("goog#html",{createHTML:za,createScript:za,createScriptURL:za})}catch(c){x.console&&x.console.error(c.message)}$a=a}return $a};var cb=function(a,b){if(b!==bb)throw Error("TrustedResourceUrl is not meant to be built directly");this.Pb=a};cb.prototype.toString=function(){return this.Pb+""};
  24. [Dòng 171] a.constructor===cb)return a.Pb;Da("expected object of type TrustedResourceUrl
  25. [Dòng 173] void 0)}:function(a,b){if("string"===typeof a)return"string"!==typeof b||1!=b.length?-1:a.indexOf(b
  26. [Dòng 173] a[c]===b)return c;return-1};var lb=Object.freeze||function(a){return a};var mb=function(a,b){this.name=a;this.value=b};mb.prototype.toString=function(){return this.name};var nb=new mb("OFF",Infinity),ob=new mb("WARNING",900),pb=new mb("CONFIG",700),qb=function(){this.La=0;this.clear()},rb;qb.prototype.clear=function(){this.zb=Array(this.La);this.Ab=-1;this.Gb=!1};var sb=function(a,b,c){this.reset(a
  27. [Dòng 174] var tb=function(a,b){this.level=null;this.mc=[];this.parent=(void 0===b?null:b)||null;this.children=[];this.oc={getName:function(){return a}}},ub=function(a){if(a.level)return a.level;if(a.parent)return ub(a.parent);Da("Root logger has no level set.");return nb},vb=function(a,b){for(
  28. [Dòng 175] a.constructor===G)return a.Ob;Da("expected object of type SafeUrl
  29. [Dòng 176] a.constructor===J)return a.Nb;Da("expected object of type SafeHtml
  30. [Dòng 181] "function"===typeof c
  31. [Dòng 181] -1===ic.indexOf(jc)
  32. [Dòng 183] case "static":c=1;break;case "fill":c=2}var d=void 0===a.buttonRootNode?0:3
  33. [Dòng 194] null===b)return a;for(var c in b)b.hasOwnProperty(c)&&void 0!==b[c]&&(null==b[c]?a[c]=null:null==a[c]
  34. [Dòng 194] b[c]));return a}function zc(a){var b=0,c;if(0==a.length)return b;for(c=0;c<a.length;c++){var d=a.charCodeAt(c);b=(b<<5)-b+d;b&=b}return b}function Ac(a){console.error("DEVELOPER_ERROR in "+a.aa+": "+a.errorMessage)};var Bc=function(a){Bc[" "](a);return a};Bc[" "]=function(){};var Cc=Tb&&$b&&0<$b.brands.length?!1:I("Trident")||I("MSIE"),Dc=I("Gecko")&&!(-1!=Zb().toLowerCase().indexOf("webkit")&&!I("Edge"))&&!(I("Trident")||I("MSIE"))&&!I("Edge"),Ec=-1!=Zb().toLowerCase().indexOf("webkit")&&!I("Edge");var Fc={MATH:!0,SCRIPT:!0,STYLE:!0,SVG:!0,TEMPLATE:!0},Gc=function(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}}(function(){if("undefined"===typeof document)return!1;var a=document.createElement("div"),b=document.createElement("div");b.appendChild(document.createElement("div"));a.appendChild(b);if(!a.firstChild)return!1;b=a.firstChild.firstChild;a.innerHTML=cc(dc);return!b.parentElement}),Hc=function(a,b){if(a.tagName&&Fc[a.tagName.toUpperCase()])throw Error("goog.dom.safe.setInnerHtml cannot be used to set content of "+
  35. [Dòng 195] :"===e
  36. [Dòng 195] ""===e?"https:":e}}z("javascript:"!==e,"%s is a javascript: URL",a)||(a="about:invalid#zClosurez");a=new G(a,Db)}b=b||x;c=c instanceof Ia?A(c):c||"";return void 0!==
  37. [Dòng 196] "application/xhtml+xml"===c.contentType
  38. [Dòng 196] t(a,"values").call(a));if("string"===typeof a)return a.split("");if(ta(a)){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}b=[];c=0;for(d in a)b[c++]=a[d];return b},Nc=function(a){if(a.cb
  39. [Dòng 197] "string"===typeof a){var b=[];a=a.length;for(var c=0;c<a;c++)b.push(c);return b}b=[];c=0;for(var d in a)b[c++]=d;return b}}},Oc=function(a,b,c){if(a.forEach&&"function"==typeof a.forEach)a.forEach(b,c);else if(ta(a)||"string"===typeof a)Array.prototype.forEach.call(a,b,c);else for(var d=Nc(a),e=Mc(a),g=e.length,f=0;f<g;f++)b.call(c,e[f],d&&d[f],a)};var Pc=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$"),Qc=function(a){var b=a.match(Pc);a=b[1];var c=b[3];b=b[4];var d="";a&&(d+=a+":");c&&(d=d+"//"+c,b&&(d+=":"+b));return d},Rc=function(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(0<=d){var g=a[c].substring(0,d);e=a[c].substring(d+1)}else g=a[c];b(g
  40. [Dòng 197] "string"===typeof a)Array.prototype.forEach.call(a
  41. [Dòng 203] var Vc=function(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""},Wc=function(a,b,c){return"string"===typeof a?(a=encodeURI(a).replace(b,cd),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null},cd=function(a){a=a.charCodeAt(0)
  42. [Dòng 206] m.ea=function(a){Q(this);var b=[];if("string"===typeof a)ed(this
  43. [Dòng 209] function pd(a,b,c){a=void 0===a?{
  44. [Dòng 210] g)&&!e&&!f&&("long"===d.buttonType
  45. [Dòng 210] "buy"===d.buttonType
  46. [Dòng 210] "pay"===d.buttonType
  47. [Dòng 210] "subscribe"===d.buttonType)&&(null==c
  48. [Dòng 213] 5===td()
  49. [Dòng 213] white"===e?"light":
  50. [Dòng 214] long"===f?h.fb.buy[b]:h.fb[f||"buy"][b]
  51. [Dòng 215] "fill"===a.buttonSizeMode
  52. [Dòng 219] document));var b=vd(a),c=document.createElement("button");ud(c,a);c.setAttribute("class",(-1658203989===zc(jd)?"gpay-button":"")+" gpay-card-info-container "+b);xd([c],a);var d=document.createElement("div");d.setAttribute("class",yd(a)?"gpay-card-info-animation-container new_style black":R(a)?"gpay-card-info-animation-container white":
  53. [Dòng 225] function qd(a,b,c){if(t(nd,"includes").call(nd,zc(a)))return 1;a=td();if(2===a){if("pt"!==c||void 0===b)return 1;b=v(b);for(c=b.next();!c.done;c=b.next())if(a=c.value,"CARD"===a.type)for(c=void 0,a=null==(c=a.parameters)?void 0:c.allowedCardNetworks,c=v(a),a=c.next();!a.done;a=c.next())if(t(Ya,"includes").call(Ya,a.value))return 2;return 1}return a}
  54. [Dòng 225] void 0===b)return 1;b=v(b);for(c=b.next()
  55. [Dòng 225] "CARD"===a.type)for(c=void 0
  56. [Dòng 232] "buy"===a.buttonType?c="buy long":"plain"===a.buttonType
  57. [Dòng 232] buy long":"plain"===a.buttonType
  58. [Dòng 233] 5),e=google.payments.api.EnableGpayNewButtonAsset?Sa:Ra;if(d in e.buy)return d;d!==c&&(void 0===b?0:b)&&Ac({aa:"createButton",errorMessage:'Button locale "'+a+'" is not supported, falling back to browser locale.'
  59. [Dòng 234] zd.prototype.Kb=function(a,b){null===b.error
  60. [Dòng 235] e)},function(e){Cd("REQUEST_TIMEOUT"===e.message
  61. [Dòng 241] "SANDBOX"===b
  62. [Dòng 244] void 0===window.isSecureContext?null:window.isSecureContext?null:"Google Pay APIs should be called in secure context!"}function Rd(a){if(a.environment&&!(n=t(Object,"values").call(Object,Ka),t(n,"includes")).call(n,a.environment))throw Error("Parameter environment in PaymentsClientOptions can optionally be set to PRODUCTION, otherwise it defaults to TEST.")
  63. [Dòng 253] void 0===a.style[d]
  64. [Dòng 253] d)}).length!==!!b.onPaymentDataChanged?"onPaymentDataChanged callback must be set if any of "+(c+" callback intent is set."):null}function Od(a){return a.allowedPaymentMethods&&(a=Xd(a,"CARD"))&&a.parameters?a.parameters.allowedAuthMethods:null}function Xd(a,b){for(var c=0;c<a.allowedPaymentMethods.length;c++){var d=a.allowedPaymentMethods[c];if(d.type==b)return d}return null};var $d=function(a,b){var c=Zd.transition;if(!c){var d=Jc();c=d;void 0===a.style[d]&&(d=(Ec?"Webkit":Dc?"Moz":Cc?"ms":null)+Kc(d),void 0!==a.style[d]&&(c=d));Zd.transition=c}c&&(a.style[c]=b)},Zd={};var ae=function(a,b){Array.isArray(b)||(b=[b]);z(0<b.length,"At least one Css3Property should be specified.");b=b.map(function(c){if("string"===typeof c)return c;Fa(c
  65. [Dòng 253] "number"===typeof c.duration
  66. [Dòng 253] "number"===typeof c.delay
  67. [Dòng 255] string"===typeof a.pointerType?a.pointerType:ee[a.pointerType]
  68. [Dòng 258] void 0===
  69. [Dòng 259] a.removeListener)z("change"===b
  70. [Dòng 261] "Listener can not be null.");if("function"===typeof a)return a;z(a.handleEvent
  71. [Dòng 263] S.prototype.dispatchEvent=function(a){te(this);var b=this.ib;if(b){var c=[];for(var d=1;b;b=b.ib)c.push(b),z(1E3>++d,"infinite loop")}b=this.cc;d=a.type||a;if("string"===typeof a)a=new ce(a
  72. [Dòng 315] coordination_token:void 0===c?"":
  73. [Dòng 316] hl:void 0===d?"":d};g=hb(g,{path:B("gp/p/ui/pay")});g=db(g).toString();g=ib.exec(g);b=g[3]||"";return gb(g[1]+jb("?",g[2]||"",a)+jb("#",b))},nf=function(a,b){rf(b,"all 250ms ease 0s");b.height="0px";setTimeout(function(){a.parentNode&&a.parentNode.removeChild(a)},250)},jf=function(a){var b=document.createElement("div");b.classList.add("google-payments-dialogContainer");var c=document.createElement("div");c.classList.add("iframeContainer");var d=document.createElement("iframe");d.classList.add("google-payments-dialog");
  74. [Dòng 321] this.la=5===b
  75. [Dòng 321] 4===b?new Jd(this.s):this.Z
  76. [Dòng 322] "logPaymentData"===e.data.name
  77. [Dòng 324] 5===b.l.mode
  78. [Dòng 327] 4===a.l.mode
  79. [Dòng 331] h=5===f
  80. [Dòng 331] 4===f?this.la:this.Z
  81. [Dòng 332] m.ka=function(a){a=void 0===a?{
  82. [Dòng 337] var wf=null,Cf=null,uf=function(a,b){b=void 0===b?!1:b
  83. [Dòng 338] this.o.push(1)},zf=function(a){return!0===(a.i&&a.i.disableNative)},Bf=function(a,b,c){return 2===a.mode
  84. [Dòng 338] 1===a.mode?a.mode:zf(b)?(c.push(3),2):!Cf
  85. [Dòng 340] Object.defineProperty(Y,Ff,Gf)}else Y[Ff]=Ef[Ff];Y.pa=Ef.prototype;var Z=function(a,b){a=void 0===a?{
  86. [Dòng 341] return(new r.Promise(function(c){if(b.ya)throw new Y;b.ya=c;b.Ia.loadPaymentData(a)})).then(function(c){b.ya=null;return c},function(c){c instanceof Y||(b.ya=null);throw c;})};m.ka=function(a){a=void 0===a?{

== (143 điều kiện):
  1. [Dòng 14] "undefined"==typeof c.execScript
  2. [Dòng 111] function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a
  3. [Dòng 111] (function(){var m,n,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype
  4. [Dòng 111] a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0
  5. [Dòng 111] (function(){var m,n,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){a=["object"==typeof globalThis
  6. [Dòng 111] "object"==typeof window
  7. [Dòng 111] "object"==typeof self
  8. [Dòng 111] "object"==typeof global
  9. [Dòng 111] c.Math==Math)return c}throw Error("Cannot find global object");
  10. [Dòng 112] },p=ca(this),da="function"===typeof Symbol&&"symbol"===typeof Symbol("x"),r={},ea={},t=function(a,b,c){if(!c||null!=a){c=ea[b];if(null==c)return a[b];c=a[c];return void 0!==c?c:a[b]}},u=function(a,b,c){if(b)a:{var d=a.split(".");a=1===d.length;var e=d[0];e=!a&&e in r?r:p;for(var g=0
  11. [Dòng 115] "iterator")&&a[t(r.Symbol,"iterator")];if(b)return b.call(a);if("number"==typeof a.length)return{next:aa(a)};throw Error(String(a)+"
  12. [Dòng 115] function"==typeof Object.create?Object.create:function(a){var b=
  13. [Dòng 116] "function"==typeof Object.setPrototypeOf)ja=Object.setPrototypeOf;else{var ka;a:{var la={a:!0},ma={};try{ma.__proto__=la;ka=ma.a;break a}catch(a){}ka=!1}ja=ka?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var na=ja;
  14. [Dòng 117] h(f)})}if(a)return a;b.prototype.vb=function(f){if(null==this.T){this.T=[];var h=this;this.wb(function(){h.jc()})}this.T.push(f)};var d=p.setTimeout;b.prototype.wb=function(f){d(f
  15. [Dòng 119] this.mb(l);return}"function"==typeof h?this.Ec(h,f):this.Eb(f)};e.prototype.mb=function(f){this.Vb(2,f)};e.prototype.Eb=function(f){this.Vb(1,f)};e.prototype.Vb=function(f,h){if(0!=this.oa)throw Error("Cannot settle("+f+", "+h+"): Promise already settled in state"+this.oa)
  16. [Dòng 121] this.na=null}};var g=new b;e.prototype.Dc=function(f){var h=this.bb();f.Ka(h.resolve,h.reject)};e.prototype.Ec=function(f,h){var l=this.bb();try{f.call(h,l.resolve,l.reject)}catch(k){l.reject(k)}};e.prototype.then=function(f,h){function l(E,H){return"function"==typeof E?function(oa){try{k(E(oa))}catch(V){q(V)}
  17. [Dòng 122] null==this.na?g.vb(l):this.na.push(l)
  18. [Dòng 122] 0==H
  19. [Dòng 124] 4==l.get(h)}catch(k){return!1}}())return a;var d="$jscomp_hidden_"+Math.random(),e=0,g=function(f){this.va=(e+=Math.random()+1).toString();if(f){f=v(f);for(var h
  20. [Dòng 129] "object"==k
  21. [Dòng 129] "function"==k?b.has(l)?k=b.get(l):(k=""+ ++f,b.set(l,k)):k="p_"+l
  22. [Dòng 131] "function"==typeof t(Object,"assign")?t(Object,"assign"):function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)w(d,e)&&(a[e]=d[e])}return a
  23. [Dòng 132] "iterator")&&b[t(r.Symbol,"iterator")];if("function"==typeof g){b=g.call(b);for(var f=0;!(g=b.next()).done;)e.push(c.call(d,g.value,f++))}else for(g=b.length
  24. [Dòng 134] u("String.prototype.includes",function(a){return a?a:function(b,c){if(null==this)throw new TypeError("The 'this' value for String.prototype.includes must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype.includes must not be a regular expression");return-1!==(this+"").indexOf(b
  25. [Dòng 139] return"array"==b
  26. [Dòng 139] "object"==b
  27. [Dòng 139] "number"==typeof a.length},ua=function(a){var b=typeof a
  28. [Dòng 139] return"object"==b
  29. [Dòng 139] "function"==b},va=function(a,
  30. [Dòng 172] bb)},jb=function(a,b,c){if(null==c)return b;if("string"===typeof c)return c?a+encodeURIComponent(c):"";for(var d in c)if(Object.prototype.hasOwnProperty.call(c,d)){var e=c[d];e=Array.isArray(e)?e:[e];for(var g=0;g<e.length;g++){var f=
  31. [Dòng 175] null==Wb){Ub=null;break a}Ub=Wb}var Yb=Ub&&Ub[610401301];Tb=null!=Yb?Yb:!1;function Zb(){var a=x.navigator;return a&&(a=a.userAgent)?a:""}var $b,ac=x.navigator;$b=ac?ac.userAgentData||null:null;function I(a){return-1!=Zb().indexOf(a)};var bc={},J=function(a,b){if(b!==bc)throw Error("SafeHtml is not meant to be built directly");this.Nb=a};J.prototype.toString=function(){return this.Nb.toString()};
  32. [Dòng 181] b=e):(e.Gb=g==e.La-1
  33. [Dòng 182] a);if("CANARY"==K)var b="https://ibfe-canary.corp.google.com";else b="https://pay","SANDBOX"==K?b+=".sandbox":"PREPROD"==K&&(b+="-preprod.sandbox"),b+=".google.com";mc.postMessage(a
  34. [Dòng 182] "SANDBOX"==K?b+=".sandbox":"PREPROD"==K
  35. [Dòng 182] .sandbox":"PREPROD"==K
  36. [Dòng 184] (function(){if(!F){var a=window.gpayInitParams||{};K=a.environment||"PRODUCTION";F=document.createElement("iframe");kc(F,hb(B(("CANARY"==K?"https://ibfe-canary.corp":"https://pay")+("PREPROD"==K?"-preprod.sandbox":"SANDBOX"==K?".sandbox":"")+".google.com/gp/p/ui/payframe?origin=%{windowOrigin}&mid=%{merchantId}"),{windowOrigin:window.location.origin,merchantId:a.merchantInfo&&a.merchantInfo.merchantId||""}));M({eventType:15,clientLatencyStartMs:Date.now()});sc();F.height="0";F.width="0";F.style.display=
  37. [Dòng 193] var vc="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split("");function wc(a){for(var b=Array(36),c=0,d,e=0;36>e;e++)8==e
  38. [Dòng 193] 13==e
  39. [Dòng 193] 18==e
  40. [Dòng 193] 23==e?b[e]="-":14==e?b[e]="4":(2>=c&&(c=33554432+16777216*Math.random()|0),d=c&15,c>>=4,b[e]=vc[19==e?d&3|8:d])
  41. [Dòng 193] -":14==e?b[e]="4":(2>=c&&(c=33554432+16777216*Math.random()|0),d=c&15,c>>=4,b[e]=vc[19==e?d&3|8:d])
  42. [Dòng 193] var vc="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split("");function wc(a){for(var b=Array(36),c=0,d,e=0;36>e;e++)8==e||13==e||18==e||23==e?b[e]="-":14==e?b[e]="4":(2>=c&&(c=33554432+16777216*Math.random()|0),d=c&15,c>>=4,b[e]=vc[19==e?d&3|8:d]);return b.join("")+"."+a}function xc(a){for(var b=1;b<arguments.length;b++)a=yc(a,arguments[b]);return a}
  43. [Dòng 194] Array.isArray(a[c])?a[c]=b[c]:yc(a[c],b[c]));return a}function zc(a){var b=0,c;if(0==a.length)return b;for(c=0
  44. [Dòng 196] b);if(1==d.childNodes.length)b=d.removeChild(z(d.firstChild));else for(b=c.createDocumentFragment()
  45. [Dòng 196] "function"==typeof a.ea)return a.ea();if("undefined"!==typeof r.Map
  46. [Dòng 196] "function"==typeof a.cb)return a.cb();if(!a.ea
  47. [Dòng 197] "function"==typeof a.forEach)a.forEach(b
  48. [Dòng 198] "file"==b)a.push("//"),(b=this.ha)&&a.push(Wc(b,Xc,!0)
  49. [Dòng 198] "/"==c.charAt(0)?Yc:Zc
  50. [Dòng 199] !this.M)d="/"+d;else{var e=b.M.lastIndexOf("/");-1!=e&&(d=b.M.slice(0,e+1)+d)}e=d;if(".."==e
  51. [Dòng 199] "."==e)d="";else if(-1!=e.indexOf("./")
  52. [Dòng 199] (d=b.M.slice(0,e+1)+d)}e=d;if(".."==e||"."==e)d="";else if(-1!=e.indexOf("./")||-1!=e.indexOf("/.")){d=0==e.lastIndexOf("/",0)
  53. [Dòng 199] "."==h?d
  54. [Dòng 199] f==e.length
  55. [Dòng 199] g.push(""):".."==
  56. [Dòng 200] 1==g.length
  57. [Dòng 214] "dark";var k="long"===f?h.fb.buy[b]:h.fb[f||"buy"][b];D("long"==f
  58. [Dòng 214] "buy"==f?"\n
  59. [Dòng 214] "default"==a.buttonColor
  60. [Dòng 225] a=null==(c=a.parameters)?void 0:c.allowedCardNetworks
  61. [Dòng 227] "mouseover"==c.type);var d=document.querySelector(".gpay-card-info-animation-container");null!==d&&d.classList.toggle("hover"
  62. [Dòng 227] "mouseover"==c.type)})});["mousedown","mouseup","mouseout"].map(function(b){a.addEventListener(b,function(c){a.classList.toggle("active"
  63. [Dòng 227] "mousedown"==c.type)})});["focus","blur"].map(function(b){a.addEventListener(b,function(c){a.classList.toggle("focus"
  64. [Dòng 227] "focus"==
  65. [Dòng 232] google.payments.api.EnableGpayNewButtonAsset?b+" "+c+" new_style "+rd(a.buttonLocale):b+" "+c+" "+rd(a.buttonLocale)}function R(a){return"white"==a.buttonColor}
  66. [Dòng 239] m.isReadyToPay=function(a){var b=Kd(a);return new r.Promise(function(c){(void 0!=b.hasEnrolledInstrument?b.hasEnrolledInstrument():b.canMakePayment()).then(function(d){window.sessionStorage.setItem("google.payments.api.storage.isreadytopay.result",d.toString());var e={result:d};2<=a.apiVersion&&a.existingPaymentMethodRequired&&(e.paymentMethodPresent=d);c(e)}).catch(function(){window.sessionStorage.getItem("google.payments.api.storage.isreadytopay.result")?c({result:"true"==window.sessionStorage.getItem("google.payments.api.storage.isreadytopay.result")}):
  67. [Dòng 241] "TEST"==b
  68. [Dòng 243] 1==b.length
  69. [Dòng 243] "CRYPTOGRAM_3DS"==b[0])return!0}return 1==a.allowedPaymentMethods.length&&"TOKENIZED_CARD"==a.allowedPaymentMethods[0]}function Pd(a,b){return 2<=a.apiVersion&&(a=Od(a))&&t(a,"includes").call(a,b)?!0:!1}
  70. [Dòng 243] var b=Od(a);if(b&&1==b.length&&"CRYPTOGRAM_3DS"==b[0])return!0}return 1==a.allowedPaymentMethods.length
  71. [Dòng 243] "TOKENIZED_CARD"==a.allowedPaymentMethods[0]}function Pd(a,b){return 2<=a.apiVersion
  72. [Dòng 244] a.indexOf(".google.com",b)==b
  73. [Dòng 245] 0==a.allowedPaymentMethods.length)return"for v2 allowedPaymentMethods must be set to an array containing a list of accepted payment methods";for(var b=0
  74. [Dòng 245] function Sd(a){if(!a)return"isReadyToPayRequest must be set!";if(Td(a))return"UPI not supported";if(2<=a.apiVersion){if(!("apiVersionMinor"in a))return"apiVersionMinor must be set!";if(!a.allowedPaymentMethods||!Array.isArray(a.allowedPaymentMethods)||0==a.allowedPaymentMethods.length)return"for v2 allowedPaymentMethods must be set to an array containing a list of accepted payment methods";for(var b=0;b<a.allowedPaymentMethods.length;b++){var c=a.allowedPaymentMethods[b];if("CARD"==c.type){if(!c.parameters)return"Field parameters must be setup in each allowedPaymentMethod";
  75. [Dòng 246] 0==d.length)return"allowedCardNetworks must be setup in parameters for type CARD";c=c.parameters.allowedAuthMethods;if(!c
  76. [Dòng 246] 0==c.length
  77. [Dòng 246] 0==a.allowedPaymentMethods.length
  78. [Dòng 253] d)}).length!==!!b.onPaymentDataChanged?"onPaymentDataChanged callback must be set if any of "+(c+" callback intent is set."):null}function Od(a){return a.allowedPaymentMethods&&(a=Xd(a,"CARD"))&&a.parameters?a.parameters.allowedAuthMethods:null}function Xd(a,b){for(var c=0;c<a.allowedPaymentMethods.length;c++){var d=a.allowedPaymentMethods[c];if(d.type==b)return d}return null};var $d=function(a,b){var c=Zd.transition;if(!c){var d=Jc();c=d;void 0===a.style[d]&&(d=(Ec?"Webkit":Dc?"Moz":Cc?"ms":null)+Kc(d)
  79. [Dòng 254] if(Dc){a:{try{Bc(b.nodeName);var e=!0;break a}catch(g){}e=!1}e||(b=null)}}else"mouseover"==c?b=a.fromElement:"mouseout"==c
  80. [Dòng 255] 0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||("keypress"==c?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType="string"===typeof a.pointerType?a.pointerType:ee[a.pointerType]||"";this.state=a.state;this.timeStamp=a.timeStamp;this.ua=
  81. [Dòng 256] 0==e.length
  82. [Dòng 257] 0==a.v[c].length
  83. [Dòng 258] g.listener==b
  84. [Dòng 258] g.capture==!!c
  85. [Dòng 258] g.Oa==d)return e}return-1};var ne="closure_lm_"+(1E6*Math.random()|0),oe={},pe=0,re=function(a,b,c,d,e){if(d&&d.once)qe(a,b,c,d,e);else if(Array.isArray(b))for(var g=0;g<b.length;g++)re(a,b[g],c,d,e);else c=se(c),a&&a[ge]?(d=ua(d)?!!d.capture:!!d,te(a),a.J.add(String(b),c,!1,d,e)):ue(a,b,c,!1,d,e)},ue=function(a,b,c,d,e,g){if(!b)throw Error("Invalid event type");var f=ua(e)?!!e.capture:!!e,h=ve(a);h||(a[ne]=h=new ke(a));c=h.add(b,c,d,f,g);if(!c.proxy){d=we();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)de||(e=f),void 0===
  86. [Dòng 261] 0==c.Fa
  87. [Dòng 264] f.capture==c){var h=f.listener,l=f.Oa||f.src;f.Ja&&me(a.J,f);e=!1!==h.call(l,d)&&e}}return e&&!d.defaultPrevented};
  88. [Dòng 282] ok"==a?b:null
  89. [Dòng 282] ok"==a
  90. [Dòng 282] failed"==a?Error(String(b)||""):null};function Fe(a){var b=a.indexOf("#")
  91. [Dòng 282] return-1==b?a:a.substring(0,b)}function Ge(a){return a?(/^[?#]/.test(a)?a.slice(1):a).split("&").reduce(function(b,c){var d=c.split("=");c=decodeURIComponent(d[0]||"");d=decodeURIComponent(d[1]||"");c&&(b[c]=d);return b},{}):{
  92. [Dòng 283] function He(a){var b={requestId:a.requestId,returnUrl:a.Ub,args:a.dc};void 0!==a.origin&&(b.origin=a.origin);void 0!==a.hb&&(b.originVerified=a.hb);return JSON.stringify(b)}function Ie(a,b,c){if(b.ok)c(b);else{var d;if(!(d=b.error)){d=null;if("function"==typeof a.DOMException){a=a.DOMException;try{d=new a("AbortError","AbortError")}catch(e){}}d||(d=Error("AbortError")
  93. [Dòng 286] function"==typeof a.rb?a.rb():a.rb);return a.sb},Ne=function(a){if(null==a.C)throw Error("not connected");return a.C},Qe=function(a,b){var c=null;a.tb&&"function"==typeof a.g.MessageChannel&&(c=new a.g.MessageChannel);c?(Oe(a,"start",b,[c.port2])
  94. [Dòng 286] "function"==typeof a.g.MessageChannel
  95. [Dòng 286] connect"==b?null!=a.C?
  96. [Dòng 288] Me(this)==a.source){var b=a.data;if(b&&"__ACTIVITIES__"==b.sentinel){var c=b.cmd;if(!this.j||"connect"==c||"start"==c){var d=a.origin;b=b.payload||null;null==this.C&&"start"==c&&(this.C=d);null==this.C&&a.source&&Me(this)==a.source&&(this.C=d);d==this.C&&this.ma(c,b,a)}}}};
  97. [Dòng 288] "__ACTIVITIES__"==b.sentinel){var c=b.cmd;if(!this.j||"connect"==c||"start"==c){var d=a.origin;b=b.payload||null;null==this.C&&"start"==c&&(this.C=d);null==this.C&&a.source&&Me(this)==a.source&&(this.C=d);d==this.C&&this.ma(c,b,a)}}}};
  98. [Dòng 288] "connect"==c
  99. [Dòng 288] "start"==c){var d=a.origin;b=b.payload||null;null==this.C&&"start"==c&&(this.C=d);null==this.C&&a.source&&Me(this)==a.source&&(this.C=d);d==this.C&&this.ma(c,b,a)}}}};
  100. [Dòng 288] null==this.C
  101. [Dòng 288] "start"==c
  102. [Dòng 288] Me(this)==a.source
  103. [Dòng 288] d==this.C
  104. [Dòng 289] T.prototype.ma=function(a,b,c){"connect"==a?(this.j&&(Le(this.j),this.j=null),this.tb=b&&b.acceptsChannel||!1,this.V(a,b)):"start"==a?((c=c.ports&&c.ports[0])&&Pe(this,c),this.V(a,b)):"msg"==a?null!=this.Ra
  105. [Dòng 289] this.Ra(b):"cnget"==a?(b=b.name||"",a=Re(this,b),a.port1||(c=new this.g.MessageChannel,a.port1=c.port1,a.port2=c.port2,a.Tb(a.port1)),a.port2&&(Oe(this,"cnset",{name:b},[a.port2]),a.port2=null)):"cnset"==a?(a=c.ports[0],b=Re(this,b.name),b.port1=a,b.Tb(a)):this.V(a,b)};
  106. [Dòng 292] m.ma=function(a,b){"connect"==a?(Qe(this.m,this.Ha),this.sa()):"result"==a?this.I
  107. [Dòng 292] "failed"==a?Error(b.data||""):b.data
  108. [Dòng 292] (a=b.code,b=new Ee(a,"failed"==a?Error(b.data||""):b.data,"iframe",Ne(this.m),!0,!0),Ie(this.g,b,this.I),this.I=null,Oe(this.m,"close"),this.disconnect()):"ready"==a?this.Ua
  109. [Dòng 292] (this.Ua(),this.Ua=null):"resize"==a
  110. [Dòng 293] "_"==h[0])throw Error('The only allowed targets are "_blank"
  111. [Dòng 295] requestId:a.xc,Ub:a.fa.Ub||Fe(a.g.location.href),dc:a.Ha});c=c+(-1==c.indexOf("#")?"#":"&")+encodeURIComponent("__WA__")+"="+encodeURIComponent(d)}d=a.sc;"_top"!=A(d)&&Je(a.g)&&(d=B("_top"));try{var e=Ic(c
  112. [Dòng 296] e=a.g==a.g.top
  113. [Dòng 298] "connect"==a?(Qe(this.m,this.Ha),this.sa()):"result"==a?(a=b.code,this.W(a,"failed"==a?Error(b.data||""):b.data)):"check"==a
  114. [Dòng 298] "failed"==a?Error(b.data||""):b.data)):"check"==a&&this.g.setTimeout(function(){return Ye(c)},200)};var Ze=function(a,b,c,d,e){this.g=a;this.hc=b;this.ic=c;this.C=d;this.Fc=e};
  115. [Dòng 301] g.requestId==a){var f=d.location.hash;if(f){var h=encodeURIComponent("__WA_RES__")+"=";e=-1;do if(e=f.indexOf(h,e),-1!=e){var l=0<e?f.substring(e-1,e):"";if(""==l||"?"==l||"#"==l||"&"==l){var k=f.indexOf("&",e+1);-1==k&&(k=f.length);f=f.substring(0,e)+f.substring(k+1)}else e++}while(-1!=e&&e<f.length)}var q=f;q=q||"";if(q!=
  116. [Dòng 301] e):"";if(""==l
  117. [Dòng 301] "?"==l
  118. [Dòng 301] "#"==l
  119. [Dòng 301] "&"==l){var k=f.indexOf("&",e+1);-1==k&&(k=f.length);f=f.substring(0,e)+f.substring(k+1)}else e++}while(-1!=e&&e<f.length)}var q=f;q=q||"";if(q!=
  120. [Dòng 301] -1==k
  121. [Dòng 302] H==oa)}else c=null}else c=null}catch(V){Ke(V),this.Qb(V)}c&&(this.ob[a]=c)}(a=c)&&df(a,b)};
  122. [Dòng 306] -1==["DEVELOPER_ERROR","MERCHANT_ACCOUNT_ERROR"].indexOf(c.statusCode)
  123. [Dòng 306] "AbortError"==c
  124. [Dòng 307] 599<g.status)g.onreadystatechange=null,d(Error("Unknown HTTP status "+g.status));else if(4==g.readyState)try{c(JSON.parse(g.responseText))}catch(f){d(f)}};g.onerror=function(){d(Error("Network failure"))};g.onabort=function(){d(Error("Request aborted"))};g.send(b)})};
  125. [Dòng 309] READY_TO_PAY"==q.data.isReadyToPayResponse);c(y)})):c({result:k})}}})};
  126. [Dòng 312] null==(h=g.Na())
  127. [Dòng 312] h.close()});g.onMessage(function(h){"partialPaymentDataCallback"==
  128. [Dòng 313] h.type?b.kb=Ed(h.data,b.B,b.ga,f.Qa).then(function(l){return g.message(l)}):"fullPaymentDataCallback"==h.type
  129. [Dòng 314] var ff=function(a){return"LOCAL"==a.s?"":"https://"+("PREPROD"==a.s?"pay-preprod.sandbox":"SANDBOX"==a.s?"pay.sandbox":"CANARY"==a.s?"ibfe-canary.corp":"pay")+".google.com"},hf=function(a){var b=ff(a)+"/gp/p/apis/buyflow/process"
  130. [Dòng 314] var ff=function(a){return"LOCAL"==a.s?"":"https://"+("PREPROD"==a.s?"pay-preprod.sandbox":"SANDBOX"==a.s?"pay.sandbox":"CANARY"==a.s?"ibfe-canary.corp":"pay")+".google.com"},hf=function(a){var b=ff(a)+"/gp/p/apis/buyflow/process";a.Rb&&(b+="?rk="+encodeURIComponent(a.Rb));return b},lf=function(a){var b;if(null==(b=a.i)?0:b.disableNgbf)return!1;var c;if((null==(c=a.callbackIntents)?0:c.length)
  131. [Dòng 314] +encodeURIComponent(a.Rb));return b},lf=function(a){var b;if(null==(b=a.i)?0:b.disableNgbf)return!1;var c;if((null==(c=a.callbackIntents)?0:c.length)
  132. [Dòng 314] var ff=function(a){return"LOCAL"==a.s?"":"https://"+("PREPROD"==a.s?"pay-preprod.sandbox":"SANDBOX"==a.s?"pay.sandbox":"CANARY"==a.s?"ibfe-canary.corp":"pay")+".google.com"},hf=function(a){var b=ff(a)+"/gp/p/apis/buyflow/process";a.Rb&&(b+="?rk="+encodeURIComponent(a.Rb));return b},lf=function(a){var b;if(null==(b=a.i)?0:b.disableNgbf)return!1;var c;if((null==(c=a.callbackIntents)?0:c.length)&&!google.payments.api.EnableWebNgbfForCallbackIntents||a.td)return!1;if(a.allowedPaymentMethods)for(a=v(a.allowedPaymentMethods),
  133. [Dòng 315] TIN"==a.s?"/ui/pay":ff(a)+"/gp/p/ui/pay"
  134. [Dòng 315] path}");if("CANARY"==a)g=B("https://ibfe-canary.corp.google.com/%{path
  135. [Dòng 315] path}");else if("SANDBOX"==a
  136. [Dòng 315] "PREPROD"==a)g=B("https://pay"+("PREPROD"==a?"-preprod":"")+".sandbox.google.com/%{path
  137. [Dòng 315] b=a.next();!b.done;b=a.next())if(b=b.value,b.parameters&&b.parameters.cvcRequired)return!1;return!0},of=function(a,b){a="TIN"==a.s?"/ui/pay":ff(a)+"/gp/p/ui/pay";b&&(a+="?ng=true");return a},qf=function(a,b,c,d){var e=window.location.origin,g=B("https://pay.google.com/%{path}");if("CANARY"==a)g=B("https://ibfe-canary.corp.google.com/%{path}");else if("SANDBOX"==a||"PREPROD"==a)g=B("https://pay"+("PREPROD"==a?"-preprod":"")+".sandbox.google.com/%{path}");a={origin:e,coordination_token:void 0===c?"":
  138. [Dòng 319] h;var l=new zd;h.onMessage(function(k){"partialPaymentDataCallback"==k.type?a.kb=Ed(k.data,a.B,a.ga,l.Qa).then(function(q){return h.message(q)}):"fullPaymentDataCallback"==k.type?a.jb=Dd(k.data,a.B,a.ga,l.Qa).then(function(q){return h.message(q)}):"resize"==k.type
  139. [Dòng 326] INTERNAL_ERROR"==b?1:"DEVELOPER_ERROR"==b?2:"MERCHANT_ACCOUNT_ERROR"==b?4:"UNSUPPORTED_API_VERSION"==b?5:"BUYER_CANCEL"==b?6:0):b=1;M({eventType:0,buyFlowActivityReason:c,error:b,softwareInfo:W(a),isReadyToPayRequest:d
  140. [Dòng 327] for(var g=0;g<e.allowedPaymentMethods.length;g++)"CARD"==
  141. [Dòng 328] e.allowedPaymentMethods[g].type&&(e.allowedPaymentMethods[g].parameters.allowedAuthMethods=["CRYPTOGRAM_3DS"]);c.push(8);e=a.la.isReadyToPay(e)}var f=d;Pd(b,"PAN_ONLY")&&(c.push(9),f=a.Z.isReadyToPay(b));return a.l.qa?(c.push(99),e.then(function(){return f})):e.then(function(h){return 1==(h&&h.result)?h:f})};m=X.prototype;m.prefetchPaymentData=function(a){var b=Qd()||Wd(a);b?Ac({aa:"prefetchPaymentData"
  142. [Dòng 337] "Google Inc."==window.navigator.vendor?(this.o.push(98),this.Za.paymentDataCallbacks&&this.o.push(97),a=!0):(this.o.push(36),a=!1));this.qa=a;this.Da=Df(this);this.mode=2;b?(this.o=[38]
  143. [Dòng 339] "Google Inc."==window.navigator.vendor

!== (54 điều kiện):
  1. [Dòng 14] c[d]!==Object.prototype[d]?c[d]:c[d]={
  2. [Dòng 112] return void 0!==c?c:a[b]}},u=function(a,b,c){if(b)a:{var d=a.split(".")
  3. [Dòng 112] g++){var f=d[g];if(!(f in e))break a;e=e[f]}d=d[d.length-1];c=da&&"es6"===c?e[d]:null;b=b(c);null!=b&&(a?ba(r,d,{configurable:!0,writable:!0,value:b}):b!==c
  4. [Dòng 116] function(){};b.prototype=a;return new b},ja;if(da&&"function"==typeof Object.setPrototypeOf)ja=Object.setPrototypeOf;else{var ka;a:{var la={a:!0},ma={};try{ma.__proto__=la;ka=ma.a;break a}catch(a){}ka=!1}ja=ka?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+"
  5. [Dòng 119] "undefined"!==
  6. [Dòng 124] null!==f
  7. [Dòng 129] q;!(q=k.next()).done;)q=q.value,h.call(l,q[1],q[0],this)};c.prototype[t(r.Symbol,"iterator")]=t(c.prototype,"entries");var d=function(h,l){var k=l&&typeof l;"object"==k||"function"==k?b.has(l)?k=b.get(l):(k=""+ ++f,b.set(l,k)):k="p_"+l;var q=h[0][k];if(q&&w(h[0],k))for(h=0;h<q.length;h++){var y=q[h];if(l!==l
  8. [Dòng 129] y.key!==y.key
  9. [Dòng 133] c!==c}},"es6");u("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this
  10. [Dòng 134] return-1!==(this+"").indexOf(b,c||0)}},"es6");/*
  11. [Dòng 141] void 0!==b
  12. [Dòng 142] },Ea=function(a,b,c){"number"!==typeof a
  13. [Dòng 173] return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if("string"===typeof a)return"string"!==typeof b
  14. [Dòng 175] e++){d=arguments[e];for(c in d)a[c]=d[c];for(var g=0;g<Bb.length;g++)c=Bb[g],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};var G=function(a,b){if(b!==Db)throw Error("SafeUrl is not meant to be built directly");this.Ob=a};G.prototype.toString=function(){return this.Ob.toString()};var Eb=function(a){if(a instanceof G
  15. [Dòng 175] got '"+a+"' of type "+sa(a));return"type_error:SafeUrl"},Fb;try{new URL("s://g"),Fb=!0}catch(a){Fb=!1}var Gb=Fb,Db={};new G("about:invalid#zClosurez",Db);new G("about:blank",Db);var Ob=function(a){if(!Hb.test(a))return a;-1!=a.indexOf("&")&&(a=a.replace(Ib,"&"));-1!=a.indexOf("<")&&(a=a.replace(Jb,"<"));-1!=a.indexOf(">")&&(a=a.replace(Kb,">"));-1!=a.indexOf('"')&&(a=a.replace(Lb,"""));-1!=a.indexOf("'")&&(a=a.replace(Mb,"'"));-1!=a.indexOf("\x00")&&(a=a.replace(Nb,"&#0;"));return a},Ib=/&/g,Jb=/</g,Kb=/>/g,Lb=/"/g,Mb=/'/g,Nb=/\x00/g,Hb=/[\x00&<>"']/;var Pb={},Qb=function(){if(Pb!==Pb)throw Error("SafeStyle is not meant to be built directly");this.vc=""};Qb.prototype.toString=function(){return this.vc.toString()};new Qb;var Rb={},Sb=function(){if(Rb!==Rb)throw Error("SafeStyleSheet is not meant to be built directly");this.uc=""};Sb.prototype.toString=function(){return this.uc.toString()};new Sb;var Tb,Ub;a:{for(var Vb=["CLOSURE_FLAGS"]
  16. [Dòng 194] function yc(a,b){if("object"!==typeof b
  17. [Dòng 194] void 0!==b[c]
  18. [Dòng 194] "object"!==typeof a[c]
  19. [Dòng 194] "object"!==typeof b[c]
  20. [Dòng 195] a.tagName+".");if(Gc())for(;a.lastChild;)a.removeChild(a.lastChild);a.innerHTML=cc(b)},Ic=function(a,b,c,d){if(!(a instanceof G||a instanceof G)){a=String(a);b:{var e=a;if(Gb){try{var g=new URL(e)}catch(f){e="https:";break b}e=g.protocol}else c:{g=document.createElement("a");try{g.href=e}catch(f){e=void 0;break c}e=g.protocol;e=":"===e||""===e?"https:":e}}z("javascript:"!==e
  21. [Dòng 195] return void 0!==
  22. [Dòng 196] "undefined"!==typeof Set
  23. [Dòng 196] d++)b.push(a[d]);return b}b=[];c=0;for(d in a)b[c++]=a[d];return b},Nc=function(a){if(a.cb&&"function"==typeof a.cb)return a.cb();if(!a.ea||"function"!=typeof a.ea){if("undefined"!==typeof r.Map
  24. [Dòng 197] "keys").call(a));if(!("undefined"!==typeof Set
  25. [Dòng 200] "!==a.H.toString()
  26. [Dòng 207] ""!==d[g]
  27. [Dòng 213] white"!==e
  28. [Dòng 221] "fill"!==a.buttonSizeMode?
  29. [Dòng 223] void 0!==a.allowedPaymentMethods
  30. [Dòng 225] function qd(a,b,c){if(t(nd,"includes").call(nd,zc(a)))return 1;a=td();if(2===a){if("pt"!==c
  31. [Dòng 227] null!==d
  32. [Dòng 233] d!==c
  33. [Dòng 249] if("NOT_CURRENTLY_KNOWN"!==a.transactionInfo.totalPriceStatus
  34. [Dòng 250] if(!b.transactionReferenceId)return"transactionReferenceId in allowedPaymentMethod parameters must be set!"}else return"referenceUrl in allowedPaymentMethod parameters must be set!";else return"payeeName in allowedPaymentMethod parameters must be set!";else return"payeeVpa in allowedPaymentMethod parameters must be set!";if("INR"!==a.transactionInfo.currencyCode)return"currencyCode in transactionInfo must be set to INR!";if("FINAL"!==a.transactionInfo.totalPriceStatus)return"totalPriceStatus in transactionInfo must be set to FINAL!";
  35. [Dòng 252] !b)return"paymentDataCallbacks must be set";if(t(a.callbackIntents,"includes").call(a.callbackIntents,"PAYMENT_AUTHORIZATION")!==!!b.onPaymentAuthorized)return"Both PAYMENT_AUTHORIZATION intent and onPaymentAuthorized must be set";var c=Md.slice();google.payments.api.EnableOfferCallback&&c.push("OFFER");google.payments.api.EnablePaymentMethodCallback&&c.push("PAYMENT_METHOD");return!!c.filter(function(d){return t(a.callbackIntents,"includes").call(a.callbackIntents,
  36. [Dòng 253] d)}).length!==!!b.onPaymentDataChanged?"onPaymentDataChanged callback must be set if any of "+(c+" callback intent is set."):null}function Od(a){return a.allowedPaymentMethods
  37. [Dòng 253] void 0!==a.style[d]
  38. [Dòng 254] this.currentTarget=b;if(b=a.relatedTarget){if(Dc){a:{try{Bc(b.nodeName);var e=!0;break a}catch(g){}e=!1}e||(b=null)}}else"mouseover"==c?b=a.fromElement:"mouseout"==c&&(b=a.toElement);this.relatedTarget=b;d?(this.clientX=void 0!==d.clientX?d.clientX:d.pageX
  39. [Dòng 254] this.clientY=void 0!==d.clientY?d.clientY:d.pageY
  40. [Dòng 254] void 0!==a.offsetX?a.offsetX:a.layerX
  41. [Dòng 254] void 0!==a.offsetY?a.offsetY:a.layerY
  42. [Dòng 254] this.clientX=void 0!==a.clientX?
  43. [Dòng 255] this.clientY=void 0!==a.clientY?a.clientY:a.pageY
  44. [Dòng 257] return-1<e?a[e]:null};ke.prototype.hasListener=function(a,b){var c=void 0!==a
  45. [Dòng 257] e=void 0!==b
  46. [Dòng 260] e):a&&(a=ve(a))&&(b=a.eb(b,c,d,e))&&Ae(b)},Ae=function(a){if("number"!==typeof a
  47. [Dòng 264] e=!1!==h.call(l,d)
  48. [Dòng 265] d)};S.prototype.hasListener=function(a,b){return this.J.hasListener(void 0!==a?String(a):void 0
  49. [Dòng 283] void 0!==a.origin
  50. [Dòng 283] void 0!==a.hb
  51. [Dòng 331] f!==this.l.mode
  52. [Dòng 338] var b=-1!==window.navigator.userAgent.indexOf("OPT/")
  53. [Dòng 338] c=-1!==window.navigator.userAgent.indexOf("SamsungBrowser/")
  54. [Dòng 339] if(-1!==window.navigator.userAgent.indexOf("OPR/")

!= (80 điều kiện):
  1. [Dòng 112] null!=a){c=ea[b];if(null==c)return a[b];c=a[c];return void 0!==c?c:a[b]}},u=function(a,b,c){if(b)a:{var d=a.split(".");a=1===d.length;var e=d[0];e=!a&&e in r?r:p;for(var g=0;g<d.length-1;g++){var f=d[g];if(!(f in e))break a;e=e[f]}d=d[d.length-1];c=da&&"es6"===c?e[d]:null;b=b(c);null!=b&&(a?ba(r,d,{configurable:!0,writable:!0,value:b}):b!==c&&(void 0===ea[d]&&(a=1E9*Math.random()>>>0,ea[d]=
  2. [Dòng 112] null!=b
  3. [Dòng 114] "function"!=typeof d.prototype[a]
  4. [Dòng 115] undefined"!=typeof r.Symbol
  5. [Dòng 118] (l=!0,k.call(h,q))}}var h=this,l=!1;return{resolve:f(this.Ac),reject:f(this.mb)}};e.prototype.Ac=function(f){if(f===this)this.mb(new TypeError("A Promise cannot resolve to itself"));else if(f instanceof e)this.Dc(f);else{a:switch(typeof f){case "object":var h=null!=f
  6. [Dòng 119] f)};e.prototype.Vb=function(f,h){if(0!=this.oa)throw Error("Cannot settle("+f+", "+h+"): Promise already settled in state"+this.oa);this.oa=f;this.W=h;2===this.oa&&this.Bc();this.kc()};e.prototype.Bc=function(){var f=this;d(function(){if(f.qc()){var h=p.console;"undefined"!==
  7. [Dòng 120] f.initCustomEvent("unhandledrejection",!1,!0,f));f.promise=this;f.reason=this.W;return l(f)};e.prototype.kc=function(){if(null!=this.na){for(var f=0;f<this.na.length;++f)g.vb(this.na[f]);
  8. [Dòng 124] }),l=new a([[f,2],[h,3]]);if(2!=l.get(f)
  9. [Dòng 124] 3!=l.get(h))return!1;l.delete(f);l.set(h
  10. [Dòng 126] "function"!=typeof a
  11. [Dòng 126] "function"!=typeof Object.seal)return!1;try{var h=Object.seal({x:4}),l=new a(v([[h,"s"]]));if("s"!=l.get(h)||1!=l.size||l.get({x:4})||l.set({x:4},"t")!=l||2!=l.size)return!1;var k=t(l,"entries").call(l),q=k.next();if(q.done||q.value[0]!=h||"s"!=q.value[1])return!1;q=k.next();return q.done||4!=q.value[0].x||"t"!=q.value[1]||!k.next().done?!1:!0}catch(y){return!1}}())return a;var b=new r.WeakMap,c=function(h){this[0]=
  12. [Dòng 126] x:4}),l=new a(v([[h,"s"]]));if("s"!=l.get(h)
  13. [Dòng 126] 1!=l.size
  14. [Dòng 126] l.set({x:4},"t")!=l
  15. [Dòng 126] 2!=l.size)return!1;var k=t(l
  16. [Dòng 126] q.value[0]!=h
  17. [Dòng 126] "s"!=q.value[1])return!1;q=k.next();return q.done||4!=q.value[0].x||"t"!=q.value[1]||!k.next().done?!1:!0}catch(y){return!1}}())return a;var b=new r.WeakMap,c=function(h){this[0]=
  18. [Dòng 126] 4!=q.value[0].x
  19. [Dòng 126] "t"!=q.value[1]
  20. [Dòng 129] k.head!=h[1]
  21. [Dòng 129] k.next!=k.head
  22. [Dòng 132] u("Array.from",function(a){return a?a:function(b,c,d){c=null!=c?c:function(h){return h
  23. [Dòng 139] return"object"!=b?b:a?Array.isArray(a)?"array":b:"null"},ta=function(a){var b=sa(a)
  24. [Dòng 139] null!=a
  25. [Dòng 140] -1!=Function.prototype.bind.toString().indexOf("native code")?va:wa
  26. [Dòng 173] null!=f
  27. [Dòng 173] e[g];null!=f&&(b||(b=a),b+=(b.length>a.length?"&":"")+encodeURIComponent(d)+"="+encodeURIComponent(String(f)))}}return b};var kb=Array.prototype.indexOf?function(a,b){z(null!=a.length);return Array.prototype.indexOf.call(a
  28. [Dòng 173] 1!=b.length?-1:a.indexOf(b,0)
  29. [Dòng 175] -1!=a.indexOf("&")
  30. [Dòng 175] -1!=a.indexOf("<")
  31. [Dòng 175] -1!=a.indexOf(">")
  32. [Dòng 175] -1!=a.indexOf('"')
  33. [Dòng 175] -1!=a.indexOf("'")
  34. [Dòng 175] -1!=a.indexOf("\x00")
  35. [Dòng 175] Tb=null!=Yb?Yb:!1
  36. [Dòng 175] (a=a.userAgent)?a:""}var $b,ac=x.navigator;$b=ac?ac.userAgentData||null:null;function I(a){return-1!=Zb().indexOf(a)};var bc={},J=function(a,b){if(b!==bc)throw Error("SafeHtml is not meant to be built directly")
  37. [Dòng 183] void 0!=a.buttonRadius
  38. [Dòng 183] null!=a.buttonRadius
  39. [Dòng 194] b){if("object"!==typeof b||null===b)return a;for(var c in b)b.hasOwnProperty(c)&&void 0!==b[c]&&(null==b[c]?a[c]=null:null==a[c]||"object"!==typeof a[c]||"object"!==typeof b[c]||Array.isArray(b[c])||Array.isArray(a[c])?a[c]=b[c]:yc(a[c],b[c]));return a}function zc(a){var b=0,c;if(0==a.length)return b;for(c=0;c<a.length;c++){var d=a.charCodeAt(c);b=(b<<5)-b+d;b&=b}return b}function Ac(a){console.error("DEVELOPER_ERROR in "+a.aa+": "+a.errorMessage)};var Bc=function(a){Bc[" "](a);return a};Bc[" "]=function(){};var Cc=Tb&&$b&&0<$b.brands.length?!1:I("Trident")||I("MSIE"),Dc=I("Gecko")&&!(-1!=Zb().toLowerCase().indexOf("webkit")
  40. [Dòng 194] Ec=-1!=Zb().toLowerCase().indexOf("webkit")
  41. [Dòng 196] "function"!=typeof a.ea){if("undefined"!==typeof r.Map&&a instanceof
  42. [Dòng 198] null!=c
  43. [Dòng 198] "/"!=c.charAt(0)
  44. [Dòng 199] c?(d=a.O,O(b),b.O=d):c=null!=a.j
  45. [Dòng 199] N.prototype.resolve=function(a){var b=this.clone(),c=!!a.X;c?Sc(b,a.X):c=!!a.ha;if(c){var d=a.ha;O(b);b.ha=d}else c=!!a.O;c?(d=a.O,O(b),b.O=d):c=null!=a.j;d=a.M;if(c)Tc(b,a.j);else if(c=!!a.M){if("/"!=d.charAt(0))if(this.O
  46. [Dòng 199] -1!=e
  47. [Dòng 199] -1!=e.indexOf("/.")){d=0==e.lastIndexOf("/",0);e=e.split("/");for(var g=[],f=0;f<e.length;){var h=e[f++];"."==h?d&&f==e.length&&g.push(""):".."==
  48. [Dòng 200] ""!=g[0])&&g.pop(),d&&f==e.length&&g.push("")):(g.push(h),d=!0)}d=g.join("/")}else d=e}c?(O(b),b.M=d):c=""!==a.H.toString();c?Uc(b,a.H.clone()):c=!!a.L;c&&(a=a.L,O(b),b.L=a);return b};N.prototype.clone=function(){return new N(this)};
  49. [Dòng 208] P.prototype.pb=function(a){a&&!this.F&&(Q(this),this.D=null,this.h.forEach(function(b,c){var d=c.toLowerCase();if(c!=d
  50. [Dòng 213] "short"!=h
  51. [Dòng 213] "plain"!=h
  52. [Dòng 229] c=null!=(e=C.buy[d])?e:c
  53. [Dòng 229] c=null!=(g=C.book[d])?g:c
  54. [Dòng 229] c=null!=(f=C.checkout[d])?f:c
  55. [Dòng 229] c=null!=(h=C.donate[d])?h:c
  56. [Dòng 229] c=null!=(l=C.order[d])?l:c
  57. [Dòng 229] c=null!=(k=C.pay[d])?k:c
  58. [Dòng 230] c=null!=(q=C.subscribe[d])?q:c
  59. [Dòng 231] void 0!=b.buttonRadius
  60. [Dòng 231] null!=b.buttonRadius
  61. [Dòng 233] function rd(a,b){var c=null!=navigator.language?navigator.language.substring(0,5):"en"
  62. [Dòng 239] m.isReadyToPay=function(a){var b=Kd(a);return new r.Promise(function(c){(void 0!=b.hasEnrolledInstrument?b.hasEnrolledInstrument():b.canMakePayment()).then(function(d){window.sessionStorage.setItem("google.payments.api.storage.isreadytopay.result",d.toString());var e={result:d};2<=a.apiVersion&&a.existingPaymentMethodRequired&&(e.paymentMethodPresent=d);c(e)
  63. [Dòng 256] e),b.Ja=c,a.push(b));return b};ke.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.v))return!1;var e=this.v[a];b=le(e,b,c,d);return-1<b?(je(e[b]),z(null!=e.length),Array.prototype.splice.call(e
  64. [Dòng 257] b),g;if(g=0<=e)z(null!=d.length),Array.prototype.splice.call(d
  65. [Dòng 257] g[f].type!=d
  66. [Dòng 257] g[f].capture!=b))return!0;return!1})};
  67. [Dòng 285] for(var a in this.U){var b=this.U[a];b.port1&&Le(b.port1);b.port2&&Le(b.port2)}this.U=null}};T.prototype.isConnected=function(){return null!=this.C};
  68. [Dòng 293] "_blank"!=h
  69. [Dòng 293] "_top"!=h
  70. [Dòng 295] "_top"!=A(d)
  71. [Dòng 301] -1!=e){var l=0<e?f.substring(e-1,e):"";if(""==l||"?"==l||"#"==l||"&"==l){var k=f.indexOf("&",e+1);-1==k&&(k=f.length);f=f.substring(0,e)+f.substring(k+1)}else e++}while(-1!=e
  72. [Dòng 301] $e.prototype.xa=function(a,b){var c=this.nb[a];c||(c=[],this.nb[a]=c);c.push(b);c=this.ob[a];if(!c&&this.L){try{var d=this.g,e=Ge(this.L).__WA_RES__;if(e){var g=JSON.parse(e);if(g&&g.requestId==a){var f=d.location.hash;if(f){var h=encodeURIComponent("__WA_RES__")+"=";e=-1;do if(e=f.indexOf(h,e),-1!=e){var l=0<e?f.substring(e-1,e):"";if(""==l||"?"==l||"#"==l||"&"==l){var k=f.indexOf("&",e+1);-1==k&&(k=f.length);f=f.substring(0,e)+f.substring(k+1)}else e++}while(-1!=e&&e<f.length)}var q=f;q=q||"";if(q!=
  73. [Dòng 305] U.prototype.rc=function(a){var b=this;Id(this.Fb);this.ba(a.Ga().then(function(c){if("TIN"!=b.s
  74. [Dòng 305] c.origin!=ff(b))throw Error("channel mismatch");var d=c.data;if(d.redirectEncryptedCallbackData)return L=3,gf(b
  75. [Dòng 320] a.i):b};var sf="actions.google.com amp-actions.sandbox.google.com amp-actions-staging.sandbox.google.com amp-actions-autopush.sandbox.google.com payments.developers.google.com payments.google.com".split(" "),X=function(a,b,c,d){this.gb=b;Rd(a);this.Jb=null;this.s=a.environment||"TEST";tf||(tf=-1!=sf.indexOf(window.location.hostname)
  76. [Dòng 322] window.addEventListener("message",function(e){-1!=sf.indexOf(window.location.hostname)
  77. [Dòng 332] void 0!=a.buttonVariant
  78. [Dòng 337] null!=window.navigator.userAgent.match(/Android|iPhone|iPad|iPod|BlackBerry|IEMobile/i)?(this.o.push(37),a=!1):(a=window.navigator.userAgent.match(/Chrome\/([0-9]+)\./i),"PaymentRequest"in window&&null!=a&&70<=Number(a[1])&&"Google Inc."==window.navigator.vendor?(this.o.push(98),this.Za.paymentDataCallbacks&&this.o.push(97),a=!0):(this.o.push(36),a=!1))
  79. [Dòng 339] if(-1!==window.navigator.userAgent.indexOf("OPR/")||b||c)return a.o.push(35),!1;if(a.qa)return!0;if(google.payments.api.DisablePaymentRequest&&!google.payments.api.EnablePaymentRequest)return a.o.push(3),!1;b=window.navigator.userAgent.match(/Android/i);c=window.navigator.userAgent.match(/Chrome\/([0-9]+)\./i);if(!(null!=b
  80. [Dòng 340] Ef);else for(var Ff in Ef)if("prototype"!=Ff)if(Object.defineProperties){var Gf=Object.getOwnPropertyDescriptor(Ef,Ff);Gf&&Object.defineProperty(Y,Ff,Gf)}else Y[Ff]=Ef[Ff];Y.pa=Ef.prototype;var Z=function(a

================================================================================

📁 FILE 252: qrcode.js
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/assets/script/qrcode.js
📊 Thống kê: 67 điều kiện duy nhất
   - === : 2 lần
   - == : 53 lần
   - !== : 0 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 2255] if (typeof define === 'function'
  2. [Dòng 2257] } else if (typeof exports === 'object') {

== (53 điều kiện):
  1. [Dòng 68] if (_dataCache == null) {
  2. [Dòng 85] if ( (0 <= r && r <= 6 && (c == 0
  3. [Dòng 85] c == 6) )
  4. [Dòng 86] || (0 <= c && c <= 6 && (r == 0
  5. [Dòng 86] r == 6) )
  6. [Dòng 107] if (i == 0
  7. [Dòng 122] _modules[r][6] = (r % 2 == 0);
  8. [Dòng 129] _modules[6][c] = (c % 2 == 0);
  9. [Dòng 152] if (r == -2
  10. [Dòng 152] r == 2
  11. [Dòng 152] c == -2
  12. [Dòng 152] c == 2
  13. [Dòng 153] || (r == 0
  14. [Dòng 153] c == 0) ) {
  15. [Dòng 169] ( (bits >> i) & 1) == 1);
  16. [Dòng 226] if (col == 6) col -= 1;
  17. [Dòng 232] if (_modules[row][col - c] == null) {
  18. [Dòng 237] dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
  19. [Dòng 249] if (bitIndex == -1) {
  20. [Dòng 458] margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
  21. [Dòng 498] if (typeof arguments[0] == 'object') {
  22. [Dòng 587] margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
  23. [Dòng 733] if (b == -1) throw 'eof';
  24. [Dòng 741] if (b0 == -1) break;
  25. [Dòng 767] if (typeof b == 'number') {
  26. [Dòng 768] if ( (b & 0xff) == b) {
  27. [Dòng 910] return function(i, j) { return (i + j) % 2 == 0
  28. [Dòng 912] return function(i, j) { return i % 2 == 0
  29. [Dòng 914] return function(i, j) { return j % 3 == 0
  30. [Dòng 916] return function(i, j) { return (i + j) % 3 == 0
  31. [Dòng 918] return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
  32. [Dòng 920] return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
  33. [Dòng 922] return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
  34. [Dòng 924] return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
  35. [Dòng 1011] if (r == 0
  36. [Dòng 1011] c == 0) {
  37. [Dòng 1015] if (dark == qrcode.isDark(row + r, col + c) ) {
  38. [Dòng 1036] if (count == 0
  39. [Dòng 1036] count == 4) {
  40. [Dòng 1149] if (typeof num.length == 'undefined') {
  41. [Dòng 1155] num[offset] == 0) {
  42. [Dòng 1495] if (typeof rsBlock == 'undefined') {
  43. [Dòng 1538] return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
  44. [Dòng 1543] _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
  45. [Dòng 1599] if (data.length - i == 1) {
  46. [Dòng 1601] } else if (data.length - i == 2) {
  47. [Dòng 1866] } else if (n == 62) {
  48. [Dòng 1868] } else if (n == 63) {
  49. [Dòng 1928] if (_buflen == 0) {
  50. [Dòng 1937] if (c == '=') {
  51. [Dòng 1961] } else if (c == 0x2b) {
  52. [Dòng 1963] } else if (c == 0x2f) {
  53. [Dòng 2133] if (table.size() == (1 << bitLength) ) {

!= (12 điều kiện):
  1. [Dòng 119] if (_modules[r][6] != null) {
  2. [Dòng 126] if (_modules[6][c] != null) {
  3. [Dòng 144] if (_modules[row][col] != null) {
  4. [Dòng 365] while (buffer.getLengthInBits() % 8 != 0) {
  5. [Dòng 750] if (count != numChars) {
  6. [Dòng 751] throw count + ' != ' + numChars
  7. [Dòng 878] while (data != 0) {
  8. [Dòng 1733] if (test.length != 2
  9. [Dòng 1733] ( (test[0] << 8) | test[1]) != code) {
  10. [Dòng 1894] if (_length % 3 != 0) {
  11. [Dòng 2067] if ( (data >>> length) != 0) {
  12. [Dòng 2178] return typeof _map[key] != 'undefined'

================================================================================

📁 FILE 253: environment.dev.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/environments/environment.dev.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 254: environment.mtf.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/environments/environment.mtf.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 255: environment.prod.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/environments/environment.prod.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 256: environment.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/environments/environment.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 257: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 258: karma.conf.js
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/karma.conf.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 259: main.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/main.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 260: polyfills.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/polyfills.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 261: kbank-policy.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/template/kbank-policy/kbank-policy.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 327] _locale == 'vi'"
  2. [Dòng 1407] _locale == 'en'"

================================================================================

📁 FILE 262: kbank-policy.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/template/kbank-policy/kbank-policy.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 22] if (params['locale'] == 'vn') {
  2. [Dòng 24] } else if (params['locale'] == 'en') {

================================================================================

📁 FILE 263: test.ts
📍 Đường dẫn: /root/projects/onepay/paygate-generalv2/src/test.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📋 TÓM TẮT TẤT CẢ ĐIỀU KIỆN DUY NHẤT:
====================================================================================================

=== (339 điều kiện duy nhất):
------------------------------------------------------------
1. return lang === this._translate.currentLang
2. checkDupTran === false"
3. params['code'] === '09') {
4. params.name === 'CUSTOMER_INTIME')) {
5. this.isHideBullet = count === 1
6. token)) && (token || (type === 1
7. (type == 2 && token)) && (type === 2
8. type === '2'
9. (type == 7 && token)) && (type === 7
10. d_qr) && (type === 4
11. (type === 5
12. *ngIf="token || (type === 1
13. type === 1
14. type === 2
15. type === 7
16. type === 4
17. type === 5
18. type === 3"
19. bnpl.code === 'kbank'"
20. bnpl.code === 'homepaylater'"
21. <div *ngIf="(type === 5
22. type === 5"
23. d_vrbank===true"
24. if (document.visibilityState === 'visible') {
25. if (Number(selected) === 1
26. this.type === 1) {
27. if (Number(selected) === 2
28. this.type === 2) {
29. oneBnplProvider === 1) {
30. if (this.type === 1) {
31. if (this.type === 2) {
32. this.homecreditProvider = this.bankAmountArr.find(e => e.code === 'homepaylater');
33. this.kredivoProvider = this.bankAmountArr.find(e => e.code === 'kredivo');
34. this.fundiinProvider = this.bankAmountArr.find(e => e.code === 'fundiin');
35. this.bnpls.push(this.sublist.find(e => e.name === 'KBank Pay Later'));
36. this.bnpls.push(this.sublist.find(e => e.name === 'Home PayLater'));
37. this.bnpls.push(this.sublist.find(e => e.name === 'Kredivo'));
38. this.bnpls.push(this.sublist.find(e => e.name === 'Insta'));
39. this.bnpls.push(this.sublist.find(e => e.name === 'Fundiin'));
40. this.cardInternations['number_card'] = Object.values(this.cardInternations).filter(value => value === true).length
41. this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_bnpl + this.d_mobile_wallet + this.d_vietqr_1 === 1
42. if (count === 1
43. if (offBanksArr[i] === this.lastDomescard) {
44. if (this._res.state === 'unpaid'
45. this._res.state === 'not_paid') {
46. if ('op' === auth
47. } else if ('bank' === auth
48. if (approval.method === 'REDIRECT') {
49. } else if (approval.method === 'POST_REDIRECT') {
50. if (this.timeLeftPaypal === 0) {
51. this.token)) && (this.token || (this.type === 1
52. (this.type == 2 && this.token)) && (this.type === 2
53. this.type === '2'
54. (this.type == 7 && this.token)) && (this.type === 7
55. this.d_qr) && (this.type === 4
56. (this.type === 5
57. if (GGPaySDKScript.readyState === "loaded"
58. GGPaySDKScript.readyState === "complete") {
59. return index === array.findIndex(obj => {
60. return JSON.stringify(obj) === _value
61. if (!(bnpl.status === 'disabled')) this.oneProvider = true
62. bnpl.status === 'disabled');
63. params.key === 'Backspace') {
64. if (type === 'email') {
65. if (type === 'phoneNumber') {
66. if (name === 'phoneNumber') {
67. } else if (name === 'email') {
68. if (this.id === 'city') {
69. this.priorityCity.push(this.dataSource.find(({ Id }) => Id === "01"));
70. this.priorityCity.push(this.dataSource.find(({ Id }) => Id === "79"));
71. } else if (this.id === 'district') {
72. } else if (this.id === 'ward') {
73. object.id === value ? 'select-option-active' : ''"
74. object.id === value"
75. (key === 8
76. key === 46
77. (key === 46
78. if (this.value === 'add') {
79. <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
80. [class.red_border_no_background]="c_expdate && (valueDate.length === 0
81. valueDate.trim().length === 0)"
82. if (isIE[0] === 'MSIE'
83. +isIE[1] === 10) {
84. if ((_val.value.substr(-1) === ' '
85. _val.value.length === 24) {
86. if (this.cardTypeBank === 'bank_card_number') {
87. } else if (this.cardTypeBank === 'bank_account_number') {
88. } else if (this.cardTypeBank === 'bank_username') {
89. } else if (this.cardTypeBank === 'bank_customer_code') {
90. this.cardTypeBank === 'bank_card_number'
91. if (this.cardTypeOcean === 'IB') {
92. } else if (this.cardTypeOcean === 'MB') {
93. if (_val.value.substr(0, 2) === '84') {
94. } else if (this.cardTypeOcean === 'ATM') {
95. if (this.cardTypeBank === 'bank_account_number') {
96. this.cardTypeBank === 'bank_card_number') {
97. if (this.cardTypeBank === 'bank_card_number'
98. if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
99. if (event.keyCode === 8
100. event.key === "Backspace"
101. if (v.length === 2
102. this.flag.length === 3
103. this.flag.charAt(this.flag.length - 1) === '/') {
104. if (v.length === 1) {
105. } else if (v.length === 2) {
106. v.length === 2) {
107. if (len === 2) {
108. if ((this.cardTypeBank === 'bank_account_number'
109. this.cardTypeBank === 'bank_username'
110. this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
111. this.cardTypeOcean === 'ATM')
112. || (this.cardTypeOcean === 'IB'
113. if (valIn === this._translate.instant('bank_card_number')) {
114. } else if (valIn === this._translate.instant('bank_account_number')) {
115. } else if (valIn === this._translate.instant('bank_username')) {
116. } else if (valIn === this._translate.instant('bank_customer_code')) {
117. if (_val.value === ''
118. _val.value === null
119. _val.value === undefined) {
120. if (_val.value && (this.cardTypeBank === 'bank_card_number'
121. if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
122. this.cardTypeOcean === 'MB') {
123. this.cardTypeOcean === 'IB'
124. if ((this.cardTypeBank === 'bank_card_number'
125. if (this.cardName === undefined
126. this.cardName === '') {
127. if (this.valueDate === undefined
128. this.valueDate === '') {
129. c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
130. _inExpDate.trim().length === 0)"
131. if (this.cardListTech === "op") {
132. if (this.timeLeft === 10) {
133. if (this.runTime === true) {
134. if (this.timeLeft === 0) {
135. if (this.runTime === true) this.submitCardBanking();
136. if (this.timeLeft === 1) {
137. } else if (valIn === this._translate.instant('internet_banking')) {
138. if (_val.value && (this.cardTypeBank === 'bank_card_number')) {
139. if (focusElement === 'card_name') {
140. } else if (focusElement === 'exp_date'
141. focusExpDateElement === 'card_name') {
142. if (this.cardTypeBank === 'bank_account_number'
143. filteredData.length === 0"
144. if (this.bankList?.length === 1) {
145. this.themeConfig.techcombankCard === false ? false : true
146. if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
147. filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
148. if (valOut === 'auth') {
149. if (this._b === '1'
150. this._b === '20'
151. this._b === '64') {
152. if (this._b === '36'
153. this._b === '18'
154. if (this._b === '19'
155. this._b === '16'
156. this._b === '25'
157. this._b === '33'
158. this._b === '39'
159. this._b === '9'
160. this._b === '11'
161. this._b === '17'
162. this._b === '36'
163. this._b === '44'
164. this._b === '64'
165. if (this._b === '20'
166. if (this._b === '18') {
167. return (bankId === '1'
168. bankId === '20'
169. bankId === '64');
170. return (bankId === '36'
171. bankId === '18'
172. index === self.findIndex((c) =>
173. c.code === country.code
174. const findData = this.datas.find(item => item?.code === this.codeSelected);
175. <div *ngIf="!((+!!exp_date + +!!csc + +!!card_name) === 2)" style="clear: both;" ></div>
176. if (this.themeConfig && (this.themeConfig.csc_config === false)) {
177. this._showName = (config.name === "0")? false : true;
178. this._showEmailPhone = (config.email_phone === "0")? false : true;
179. if ((v.substr(-1) === ' '
180. this.c_country = _val.value === 'default'
181. this.c_country = _val === 'default'
182. this.numberCardActive === 1) {
183. return currency === 'VND'
184. isTop = window === window.top
185. language==='en' ? 'flex-start' : ''"
186. language==='en' ? '-10px' : ''"
187. filteredData.length === 0
188. filteredDataOther.length === 0"
189. filteredDataMobile.length === 0
190. filteredDataOtherMobile.length === 0"
191. appList?.length === 1)" ></app-qr-detail>
192. this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
193. this.filteredDataOther = this.appList.filter(item => item.type === 'other');
194. this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
195. this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
196. this.appList.length === 1) {
197. if ((this.filteredDataMobile.length === 1
198. this.filteredDataOtherMobile.length === 1)
199. item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
200. filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
201. if (item.type === 'mobile_banking') {
202. this.listWalletQR.length === 1) {
203. this.listWalletQR.length === 1
204. if (!this.d_vnpayqr_wallet && !this.d_vnpayqr_deeplink && (this.listWalletQR?.length === 1
205. this.listWalletDeeplink?.length === 1)) {
206. if (item.type === 'deeplink') {
207. if (this.locale === 'en') {
208. type === 2"
209. domescardVersionNumber===1
210. type === 1"
211. if (Number(this.numberCardActive) === 1
212. type === paymentType"
213. type === 4"
214. type === 7"
215. const isSingleCardActive = Object.values(this.cardInternations).filter(value => value === true).length === 1;
216. const isSingleCardActive = Object.values(this.cardInternations).filter(value => value === true).length === 1
217. if (typeof strTrim.length === 'number') {
218. isPopupSupport === 'True')
219. isPopupSupport === 'True'"
220. 'afs-mobile': locale === 'en'
221. 'left-en': locale === 'en'
222. locale === 'en'
223. params.timeout === 'true') {
224. params.kbank === 'true') {
225. this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
226. _re.body.state === 'unpaid');
227. if (this.errorCode === 'overtime'
228. this.errorCode === '253') {
229. params.name === 'CUSTOMER_INTIME'
230. params.code === '09') {
231. params.name === 'INVALID_CARD_LIST'
232. params.code === '10') {
233. if (this.maxpayment === this.paymentsNum) {
234. if (param === key) {
235. s = max === 0 ? 0 : d / max
236. if (max === min) {
237. const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
238. item.method === method) : null;
239. this.applePaySupport = element.value === 'true'
240. if (YY % 400 === 0
241. YY % 4 === 0)) {
242. if (YYYY % 400 === 0
243. YYYY % 4 === 0)) {
244. err?.status === 400
245. err?.error?.name === 'INVALID_CARD_FEE'
246. if (target.tagName === 'A'
247. if (M[1] === 'Chrome') {
248. pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
249. target === 0
250. if (cardTypeBank === 'bank_card_number') {
251. } else if (cardTypeBank === 'bank_account_number') {
252. function"===typeof Symbol
253. "symbol"===typeof Symbol("x")
254. a=1===d.length
255. "es6"===c?e[d]:null
256. configurable:!0,writable:!0,value:b}):b!==c&&(void 0===ea[d]
257. "function"===typeof d
258. })};var e=function(f){this.oa=0;this.W=void 0;this.na=[];this.Hb=!1;var h=this.bb();try{f(h.resolve,h.reject)}catch(l){h.reject(l)}};e.prototype.bb=function(){function f(k){return function(q){l||(l=!0,k.call(h,q))}}var h=this,l=!1;return{resolve:f(this.Ac),reject:f(this.mb)}};e.prototype.Ac=function(f){if(f===this)this.mb(new TypeError("A Promise cannot resolve to itself"));else if(f instanceof e)this.Dc(f);else{a:switch(typeof f){case "object":var h=null!=f;break a;case "function":h=!0;break a;default:h=
259. 2===this.oa
260. 1)};e.prototype.qc=function(){if(this.Hb)return!1;var f=p.CustomEvent,h=p.Event,l=p.dispatchEvent;if("undefined"===typeof l)return!0;"function"===typeof f?f=new f("unhandledrejection"
261. "function"===typeof f?f=new f("unhandledrejection",{cancelable:!0}):"function"===typeof h?f=new h("unhandledrejection",{cancelable:!0}):(f=p.document.createEvent("CustomEvent"),f.initCustomEvent("unhandledrejection",!1,!0,f))
262. return"object"===h
263. "function"===h}if(function(){if(!a||!Object.seal)return!1;try{var f=Object.seal({}),h=Object.seal({}),l=new a([[f,2],[h,3]]);if(2!=l.get(f)||3!=l.get(h))return!1;l.delete(f);l.set(h,4);return!l.has(f)&&4==l.get(h)}catch(k){return!1}}())return a;var d="$jscomp_hidden_"+Math.random(),e=0,g=function(f){this.va=(e+=Math.random()+1).toString()
264. h=v(h);for(var l;!(l=h.next()).done;)l=l.value,this.set(l[0],l[1])}};c.prototype.set=function(h,l){h=0===h?0:h
265. l===y.key)return{id:k,list:q,index:h,u:y}}return{id:k,list:q,index:-1,u:void 0}},e=function(h,l){var k=h[1];return fa(function(){if(k){for(;k.head!=h[1];)k=k.S;for(;k.next!=k.head;)return k=
266. u("Object.is",function(a){return a?a:function(b,c){return b===c?0!==b
267. 1/b===1/c:b!==b
268. u("Object.is",function(a){return a?a:function(b,c){return b===c?0!==b||1/b===1/c:b!==b&&c!==c}},"es6");u("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(0>c&&(c=Math.max(c+e,0));c<e;c++){var g=d[c];if(g===b
269. void 0===b?c=c[d]
270. Ca("Expected object but got %s: %s.",[sa(a),a],b,Array.prototype.slice.call(arguments,2))};var Ia=function(a,b){this.Wb=a===Ga
271. a.constructor===Ia
272. a.bc===Ha)return a.Wb;Da("expected object of type Const
273. Ya=["ELECTRON","MAESTRO","ELO_DEBIT"];function D(a,b){b=void 0===b?document:b
274. b)};var $a,ab=function(){if(void 0===$a){var a=null,b=x.trustedTypes;if(b&&b.createPolicy)try{a=b.createPolicy("goog#html",{createHTML:za,createScript:za,createScriptURL:za})}catch(c){x.console&&x.console.error(c.message)}$a=a}return $a};var cb=function(a,b){if(b!==bb)throw Error("TrustedResourceUrl is not meant to be built directly");this.Pb=a};cb.prototype.toString=function(){return this.Pb+""};
275. a.constructor===cb)return a.Pb;Da("expected object of type TrustedResourceUrl
276. void 0)}:function(a,b){if("string"===typeof a)return"string"!==typeof b||1!=b.length?-1:a.indexOf(b
277. a[c]===b)return c;return-1};var lb=Object.freeze||function(a){return a};var mb=function(a,b){this.name=a;this.value=b};mb.prototype.toString=function(){return this.name};var nb=new mb("OFF",Infinity),ob=new mb("WARNING",900),pb=new mb("CONFIG",700),qb=function(){this.La=0;this.clear()},rb;qb.prototype.clear=function(){this.zb=Array(this.La);this.Ab=-1;this.Gb=!1};var sb=function(a,b,c){this.reset(a
278. var tb=function(a,b){this.level=null;this.mc=[];this.parent=(void 0===b?null:b)||null;this.children=[];this.oc={getName:function(){return a}}},ub=function(a){if(a.level)return a.level;if(a.parent)return ub(a.parent);Da("Root logger has no level set.");return nb},vb=function(a,b){for(
279. a.constructor===G)return a.Ob;Da("expected object of type SafeUrl
280. a.constructor===J)return a.Nb;Da("expected object of type SafeHtml
281. "function"===typeof c
282. -1===ic.indexOf(jc)
283. case "static":c=1;break;case "fill":c=2}var d=void 0===a.buttonRootNode?0:3
284. null===b)return a;for(var c in b)b.hasOwnProperty(c)&&void 0!==b[c]&&(null==b[c]?a[c]=null:null==a[c]
285. b[c]));return a}function zc(a){var b=0,c;if(0==a.length)return b;for(c=0;c<a.length;c++){var d=a.charCodeAt(c);b=(b<<5)-b+d;b&=b}return b}function Ac(a){console.error("DEVELOPER_ERROR in "+a.aa+": "+a.errorMessage)};var Bc=function(a){Bc[" "](a);return a};Bc[" "]=function(){};var Cc=Tb&&$b&&0<$b.brands.length?!1:I("Trident")||I("MSIE"),Dc=I("Gecko")&&!(-1!=Zb().toLowerCase().indexOf("webkit")&&!I("Edge"))&&!(I("Trident")||I("MSIE"))&&!I("Edge"),Ec=-1!=Zb().toLowerCase().indexOf("webkit")&&!I("Edge");var Fc={MATH:!0,SCRIPT:!0,STYLE:!0,SVG:!0,TEMPLATE:!0},Gc=function(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}}(function(){if("undefined"===typeof document)return!1;var a=document.createElement("div"),b=document.createElement("div");b.appendChild(document.createElement("div"));a.appendChild(b);if(!a.firstChild)return!1;b=a.firstChild.firstChild;a.innerHTML=cc(dc);return!b.parentElement}),Hc=function(a,b){if(a.tagName&&Fc[a.tagName.toUpperCase()])throw Error("goog.dom.safe.setInnerHtml cannot be used to set content of "+
286. :"===e
287. ""===e?"https:":e}}z("javascript:"!==e,"%s is a javascript: URL",a)||(a="about:invalid#zClosurez");a=new G(a,Db)}b=b||x;c=c instanceof Ia?A(c):c||"";return void 0!==
288. "application/xhtml+xml"===c.contentType
289. t(a,"values").call(a));if("string"===typeof a)return a.split("");if(ta(a)){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}b=[];c=0;for(d in a)b[c++]=a[d];return b},Nc=function(a){if(a.cb
290. "string"===typeof a){var b=[];a=a.length;for(var c=0;c<a;c++)b.push(c);return b}b=[];c=0;for(var d in a)b[c++]=d;return b}}},Oc=function(a,b,c){if(a.forEach&&"function"==typeof a.forEach)a.forEach(b,c);else if(ta(a)||"string"===typeof a)Array.prototype.forEach.call(a,b,c);else for(var d=Nc(a),e=Mc(a),g=e.length,f=0;f<g;f++)b.call(c,e[f],d&&d[f],a)};var Pc=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$"),Qc=function(a){var b=a.match(Pc);a=b[1];var c=b[3];b=b[4];var d="";a&&(d+=a+":");c&&(d=d+"//"+c,b&&(d+=":"+b));return d},Rc=function(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(0<=d){var g=a[c].substring(0,d);e=a[c].substring(d+1)}else g=a[c];b(g
291. "string"===typeof a)Array.prototype.forEach.call(a
292. var Vc=function(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""},Wc=function(a,b,c){return"string"===typeof a?(a=encodeURI(a).replace(b,cd),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null},cd=function(a){a=a.charCodeAt(0)
293. m.ea=function(a){Q(this);var b=[];if("string"===typeof a)ed(this
294. function pd(a,b,c){a=void 0===a?{
295. g)&&!e&&!f&&("long"===d.buttonType
296. "buy"===d.buttonType
297. "pay"===d.buttonType
298. "subscribe"===d.buttonType)&&(null==c
299. 5===td()
300. white"===e?"light":
301. long"===f?h.fb.buy[b]:h.fb[f||"buy"][b]
302. "fill"===a.buttonSizeMode
303. document));var b=vd(a),c=document.createElement("button");ud(c,a);c.setAttribute("class",(-1658203989===zc(jd)?"gpay-button":"")+" gpay-card-info-container "+b);xd([c],a);var d=document.createElement("div");d.setAttribute("class",yd(a)?"gpay-card-info-animation-container new_style black":R(a)?"gpay-card-info-animation-container white":
304. function qd(a,b,c){if(t(nd,"includes").call(nd,zc(a)))return 1;a=td();if(2===a){if("pt"!==c||void 0===b)return 1;b=v(b);for(c=b.next();!c.done;c=b.next())if(a=c.value,"CARD"===a.type)for(c=void 0,a=null==(c=a.parameters)?void 0:c.allowedCardNetworks,c=v(a),a=c.next();!a.done;a=c.next())if(t(Ya,"includes").call(Ya,a.value))return 2;return 1}return a}
305. void 0===b)return 1;b=v(b);for(c=b.next()
306. "CARD"===a.type)for(c=void 0
307. "buy"===a.buttonType?c="buy long":"plain"===a.buttonType
308. buy long":"plain"===a.buttonType
309. 5),e=google.payments.api.EnableGpayNewButtonAsset?Sa:Ra;if(d in e.buy)return d;d!==c&&(void 0===b?0:b)&&Ac({aa:"createButton",errorMessage:'Button locale "'+a+'" is not supported, falling back to browser locale.'
310. zd.prototype.Kb=function(a,b){null===b.error
311. e)},function(e){Cd("REQUEST_TIMEOUT"===e.message
312. "SANDBOX"===b
313. void 0===window.isSecureContext?null:window.isSecureContext?null:"Google Pay APIs should be called in secure context!"}function Rd(a){if(a.environment&&!(n=t(Object,"values").call(Object,Ka),t(n,"includes")).call(n,a.environment))throw Error("Parameter environment in PaymentsClientOptions can optionally be set to PRODUCTION, otherwise it defaults to TEST.")
314. void 0===a.style[d]
315. d)}).length!==!!b.onPaymentDataChanged?"onPaymentDataChanged callback must be set if any of "+(c+" callback intent is set."):null}function Od(a){return a.allowedPaymentMethods&&(a=Xd(a,"CARD"))&&a.parameters?a.parameters.allowedAuthMethods:null}function Xd(a,b){for(var c=0;c<a.allowedPaymentMethods.length;c++){var d=a.allowedPaymentMethods[c];if(d.type==b)return d}return null};var $d=function(a,b){var c=Zd.transition;if(!c){var d=Jc();c=d;void 0===a.style[d]&&(d=(Ec?"Webkit":Dc?"Moz":Cc?"ms":null)+Kc(d),void 0!==a.style[d]&&(c=d));Zd.transition=c}c&&(a.style[c]=b)},Zd={};var ae=function(a,b){Array.isArray(b)||(b=[b]);z(0<b.length,"At least one Css3Property should be specified.");b=b.map(function(c){if("string"===typeof c)return c;Fa(c
316. "number"===typeof c.duration
317. "number"===typeof c.delay
318. string"===typeof a.pointerType?a.pointerType:ee[a.pointerType]
319. void 0===
320. a.removeListener)z("change"===b
321. "Listener can not be null.");if("function"===typeof a)return a;z(a.handleEvent
322. S.prototype.dispatchEvent=function(a){te(this);var b=this.ib;if(b){var c=[];for(var d=1;b;b=b.ib)c.push(b),z(1E3>++d,"infinite loop")}b=this.cc;d=a.type||a;if("string"===typeof a)a=new ce(a
323. coordination_token:void 0===c?"":
324. hl:void 0===d?"":d};g=hb(g,{path:B("gp/p/ui/pay")});g=db(g).toString();g=ib.exec(g);b=g[3]||"";return gb(g[1]+jb("?",g[2]||"",a)+jb("#",b))},nf=function(a,b){rf(b,"all 250ms ease 0s");b.height="0px";setTimeout(function(){a.parentNode&&a.parentNode.removeChild(a)},250)},jf=function(a){var b=document.createElement("div");b.classList.add("google-payments-dialogContainer");var c=document.createElement("div");c.classList.add("iframeContainer");var d=document.createElement("iframe");d.classList.add("google-payments-dialog");
325. this.la=5===b
326. 4===b?new Jd(this.s):this.Z
327. "logPaymentData"===e.data.name
328. 5===b.l.mode
329. 4===a.l.mode
330. h=5===f
331. 4===f?this.la:this.Z
332. m.ka=function(a){a=void 0===a?{
333. var wf=null,Cf=null,uf=function(a,b){b=void 0===b?!1:b
334. this.o.push(1)},zf=function(a){return!0===(a.i&&a.i.disableNative)},Bf=function(a,b,c){return 2===a.mode
335. 1===a.mode?a.mode:zf(b)?(c.push(3),2):!Cf
336. Object.defineProperty(Y,Ff,Gf)}else Y[Ff]=Ef[Ff];Y.pa=Ef.prototype;var Z=function(a,b){a=void 0===a?{
337. return(new r.Promise(function(c){if(b.ya)throw new Y;b.ya=c;b.Ia.loadPaymentData(a)})).then(function(c){b.ya=null;return c},function(c){c instanceof Y||(b.ya=null);throw c;})};m.ka=function(a){a=void 0===a?{
338. if (typeof define === 'function'
339. } else if (typeof exports === 'object') {

== (975 điều kiện duy nhất):
------------------------------------------------------------
1. 'vi' == params['locale']) {
2. 'en' == params['locale']) {
3. <div class="d_language" [class.language_logo_full]="logoFull"  (click)="changeLanguage(_translate?.currentLang=='vi' ? 'en' : 'vi')">
4. _translate?.currentLang=='en'"
5. _translate?.currentLang=='vi'"
6. _translate?.currentLang=='en' ? 'filter:grayscale(1)' : ''"
7. _translate?.currentLang=='vi' ? 'filter:grayscale(1)' : ''"
8. if (this._paymentService.getInvoiceDetail() == null) {
9. if ((dataPassed.status == '200'
10. dataPassed.status == '201') && dataPassed.body != null) {
11. dataPassed.body.themes.logo_full == 'True') {
12. (click)="changeLanguage(_translate?.currentLang=='vi' ? 'en' : 'vi')">
13. _paymentService.getCurrentPage()=='queuing'"
14. payments[payments.length - 1].state == 'pending'
15. payments[payments.length - 1].instrument.issuer.brand.id == 'amigo') {
16. this.isThemeWhite = res.themeType == ThemeType.white
17. if (this.locale == 'en') {
18. if (name == 'MAFC')
19. if (bankId == 3
20. bankId == 61
21. bankId == 8
22. bankId == 49
23. bankId == 48
24. bankId == 10
25. bankId == 53
26. bankId == 17
27. bankId == 65
28. bankId == 23
29. bankId == 52
30. bankId == 27
31. bankId == 66
32. bankId == 9
33. bankId == 54
34. bankId == 37
35. bankId == 38
36. bankId == 39
37. bankId == 40
38. bankId == 42
39. bankId == 44
40. bankId == 72
41. bankId == 59
42. bankId == 51
43. bankId == 64
44. bankId == 58
45. bankId == 56
46. bankId == 55
47. bankId == 60
48. bankId == 68
49. bankId == 74
50. bankId == 75 //MAFC Napas
51. onePaymentMethod == true
52. token == true"
53. stepScreenMobile == 1"
54. stepScreenMobile == 2"
55. onePaymentMethod == false
56. _auth == 0
57. stepScreenMobile == 1) || d_desktop" [class.scroll-main-content]="d_mobile">
58. !token) || (type == 1
59. !token) || (type == 6
60. type == 2)"
61. !token) || (type == 3
62. !token) || (type == 5
63. method?.trim()=='International'"
64. method.trim()=='ApplePay'"
65. method.trim()=='GooglePay'"
66. method.trim()=='SamsungPay'"
67. method?.trim()=='Domestic'"
68. method?.trim()=='QR'"
69. method?.trim()=='Paypal'"
70. method?.trim()=='VietQR'
71. method?.trim()=='Bnpl'
72. stepScreenMobile == 1
73. stepScreenMobile == 2) || d_desktop || onePaymentMethod || token">
74. !token)  || (type == 2
75. !token) || (type == PaymentTypeMobile.ApplePay
76. type == PaymentTypeMobile.GooglePay
77. type == PaymentTypeMobile.SamsungPay)) && ((type == PaymentTypeMobile.ApplePay
78. type == PaymentTypeMobile.SamsungPay) || (onePaymentMethod
79. !token) || (type == 7
80. type == PaymentTypeMobile.SamsungPay)">
81. *ngIf="(type == PaymentTypeMobile.ApplePay
82. type == PaymentTypeMobile.SamsungPay) || onePaymentMethod">
83. <applepay-main *ngIf="(type == PaymentTypeMobile.ApplePay) || (onePaymentMethod
84. <googlepay-main *ngIf="(type == PaymentTypeMobile.GooglePay) || (onePaymentMethod
85. <samsungpay-main *ngIf="(type == PaymentTypeMobile.SamsungPay) || (onePaymentMethod
86. sortMethodArray[i].trim()=='Bnpl'
87. <div *ngIf="((onePaymentMethod == true
88. d_bnpl) || (onePaymentMethod == false
89. bnpl.status == 'disabled'"
90. providerType == bnpl.code"
91. bnpl.status == 'disabled'
92. d_bnpl_number == 1"
93. type==5 && providerType==bnpl.code
94. providerType==bnpl.code"
95. bnpl.code == 'kbank'
96. bnpl.code == 'insta'
97. bnpl.code == 'homepaylater'
98. bnpl.code == 'kredivo'
99. bnpl.status == 'active'
100. providerType == bnpl.code)
101. || (d_bnpl_number == 1
102. providerType == bnpl.code))
103. || (bnpl.status == 'active'
104. d_bnpl_number == 1)">
105. bnpl.code == 'insta'"
106. bnpl.code == 'kbank'"
107. bnpl.code == 'homepaylater'"
108. bnpl.code == 'kredivo'"
109. bnpl.code == 'fundiin'"
110. _auth == 1
111. [class.enable_form]="!onePaymentMethod || (onePaymentMethod && d_bnpl)" *ngIf="_auth == 1 && ((onePaymentMethod == true
112. d_bnpl_number == 1)
113. if (!isNaN(_re.status) && (_re.status == '200'
114. _re.status == '201') && _re.body != null) {
115. if (('closed' == this._res_polling.state
116. 'canceled' == this._res_polling.state
117. 'expired' == this._res_polling.state)
118. } else if ('paid' == this._res_polling.state) {
119. this._res_polling.payments == null
120. if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'authorization_required') {
121. } else if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
122. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
123. this._paymentService.getCurrentPage() == 'enter_card'
124. } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
125. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
126. } else if ('not_paid' == this._res_polling.state) {
127. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
128. if (auth == 'auth') {
129. detail.merchant.id == 'AMWAY') {
130. if (this.checkInvoiceState() == 1) {
131. _re.body?.merchant?.qr_version == '2');
132. if (domescardVersion == '2'
133. if (_re.status == '200'
134. _re.status == '201') {
135. this._res.payments[this._res.payments.length - 1].state == 'pending'
136. this._res.payments[this._res.payments.length - 1].instrument.type == 'applepay') {
137. if (this.themeConfig.default_method == 'International'
138. } else if (this.themeConfig.default_method == 'Domestic'
139. } else if (this.themeConfig.default_method == 'QR'
140. } else if (this.themeConfig.default_method == 'Paypal'
141. if (this.type == 3) this.setIntervalPaypal()
142. if (value == true) {
143. if (('closed' == this._res.state
144. 'canceled' == this._res.state
145. 'expired' == this._res.state
146. 'paid' == this._res.state)
147. if ('paid' == this._res.state
148. this.stopCounter == 'stop')) {  // Check if X is true
149. if (this.onePaymentMethod == true
150. this.d_bnpl_number == 1
151. this.d_bnpl == 1) {
152. if (this.d_amigo == true
153. this.d_insta == false
154. this.d_instaplus == false) {
155. if (this.version2 == "2") {
156. if (this.d_amigo_number == 0
157. this.d_insta_number == 1) {
158. else if (this.d_amigo_number == 0
159. this.d_instaplus_number == 1) {
160. if ((this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_bnpl + this.d_mobile_wallet + this.d_vietqr_1) == 0
161. this._auth == 0) {
162. this._res.themes.techcombankCard == false ? false : true
163. if (count == 2
164. if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
165. this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
166. if (this._res.payments[this._res.payments.length - 1].state == 'approved'
167. } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
168. || (responseCode == '1'
169. instrumentType == 'vietqr'))
170. if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required') {
171. } else if (this._res.payments[this._res.payments.length - 1].state == 'pending') {
172. } else if (this._res.payments[this._res.payments.length - 1].state == 'canceled') {
173. this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
174. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id == 'vietqr'
175. } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
176. this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
177. if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
178. } else if (idBrand == 'atm'
179. else if (idBrand == 'kbank'
180. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
181. if (this._res.payments[this._res.payments.length - 1].state == 'pending'
182. if ('paid' == this._res.state) {
183. 'canceled' == this._res.state) {
184. this._res.payments[this._res.payments.length - 1].state == 'pending') {
185. if (this.d_inter == 1) {
186. } else if (this.type == 1) {
187. if (type == 'qrv1') {
188. if (type == 'mobile') {
189. e.type == 'ewallet'
190. e.code == 'momo')) {
191. } else if (type == 'desktop') {
192. e.type == 'vnpayqr') || (regex.test(strTest)
193. if (this._res_post.state == 'approved'
194. this._res_post.state == 'failed') {
195. } else if (this._res_post.state == 'authorization_required') {
196. if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
197. } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
198. if (bankid == 2
199. bankid == 67) {
200. if (isBankOff && ((bankid == '2'
201. !this.isOffTechcombank) || (bankid == '67'
202. if (res.status == '200'
203. res.status == '201') {
204. return (this.currentMethod == 7) // PTTT riêng VietQR
205. || (this.currentMethod == 2
206. this._b == 'vietqr')    // PTTT domes vietqr
207. !this.token) || (this.type == 1
208. !this.token)  || (this.type == 2
209. !this.token) || (this.type == PaymentTypeMobile.ApplePay
210. this.type == PaymentTypeMobile.GooglePay
211. this.type == PaymentTypeMobile.SamsungPay)) && ((this.type == PaymentTypeMobile.ApplePay
212. this.type == PaymentTypeMobile.SamsungPay) || (this.onePaymentMethod
213. !this.token) || (this.type == 7
214. if (data._locale == 'en') {
215. serviceId: this.environment == 'PRODUCTION'? this.serviceIdProd : this.serviceIdTest
216. bnplDetail.method == 'SP'"
217. bnplDetail.method == 'PL'"
218. _re.code == '0'
219. packageItem.product_code == productCode) {
220. a.product_code == 'SP') {
221. a.product_code == 'PL') {
222. if (this.selectedIndex == 0
223. } else if ((this.selectedIndex == 1
224. this.payLaterSubmit) || (this.selectedIndex == 0
225. item.prepaid_percent == this.selectedPrePaid
226. item.installment_month == this.selectedPayLater) {
227. if (string == 'SP') {
228. } else if (string == 'PL') {
229. if(this._locale == 'en'){
230. if (this._res_post.state == 'failed') {
231. if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
232. bnpl.status == 'active'"
233. bnpl.status == 'disabled')" [@listAnimation]>
234. [class.bnpl-active-item]="bnpl.status == 'active'" (click)="onClick(bnpl.status == 'disabled')">
235. selectedBnpl.code == 'insta'"
236. bnpls?.length == 1"
237. selectedBnpl.code == 'kbank'"
238. selectedBnpl.code == 'homepaylater'"
239. selectedBnpl.code == 'kredivo'"
240. selectedBnpl.code == 'fundiin'"
241. _auth == 1"
242. if (this?.bnpls?.length == 1) {
243. let bnplDetail = this.bnpls.find(o => o.status == 'active'
244. detail == o.code);
245. _locale == 'vi'"
246. this.fullNameInvalidMessage = this.fundiinDetail.fullName?.length == 0 ? this._translate.instant('fill_full_name')
247. this.fundiinDetail.email?.length == 0 ? this._translate.instant('fill_email')
248. if (bnpl.status == 'disabled') {
249. value == 'add' ? 'select-option-active' : ''"
250. value == 'add'"
251. value=='add'"
252. value == 'add') || !isOnepayPolicy" [disabled]="(isInvalid()
253. value == 'add') || !isOnepayPolicy">
254. if (response.status == '200'
255. response.status == '201') {
256. this.listTokenBnpl.length == 0) {
257. if (this.value == object.id) {
258. if (name == 'email') {
259. } else if (name == 'phoneNumber') {
260. if (name == 'phoneNumber') {
261. if (name == 'fullname') {
262. if (name == 'email'
263. this._res_post.state == 'authorization_required') {
264. this._res_post.code == 'KB-02') {
265. !cmndBlur ? 'no-border' : ((kbankDetail['citizen_id'] && !(kbankDetail['citizen_id'].length == 9 || kbankDetail['citizen_id'].length == 12) || !kbankDetail['citizen_id']) &&  bnplForm.controls['citizen_id'].touched) ? 'ng-invalid ng-dirty' : ''
266. bnplForm.controls['citizen_id'].touched && kbankDetail['citizen_id'] && !(kbankDetail['citizen_id'].length == 9 || kbankDetail['citizen_id'].length == 12) && cmndBlur
267. this.value == 'add')){
268. list = this.data.customer.tokens.filter((item) => item.instrument.issuer.brand.id == 'kbank')
269. return (this.bnplForm.invalid || (this.kbankDetail['citizen_id'] && !(this.kbankDetail['citizen_id'].length == 9) && !(this.kbankDetail['citizen_id'].length == 12)));
270. if (name == 'citizen_id') {
271. if (this._locale == 'en') {
272. this.bnplDetail.fullname?.length == 0 ? this._translate.instant('kredivo_empty_fullname_message')
273. this.bnplDetail.email?.length == 0 ? this._translate.instant('kredivo_empty_email_message')
274. else if (this._res.code == '2') {
275. _re.body.payments[_re.body.payments.length - 1].reason.code == 'B4') {
276. if (this._b == 18) {//8-MB Bank;18-oceanbank
277. if (this._b == 18
278. this._b == 19) {
279. if (this._b == 19) {//19BIDV
280. } else if (this._b == 3
281. this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
282. if (this._b == 27) {
283. } else if (this._b == 12) {// 12SHB
284. } else if (this._b == 18) { //18Oceanbank-ocb
285. if (this._b == 19
286. this._b == 3
287. this._b == 27
288. this._b == 12) {
289. } else if (this._b == 18) {
290. if (this.checkBin(_val.value) && (this._b == 3
291. this._b == 27)) {
292. if (this._b == 3) {
293. this.cardTypeOcean == 'ATM') {
294. if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
295. this._b == 18)) {
296. if (this.checkBin(v) && (this._b == 3
297. event.inputType == 'deleteContentBackward') {
298. if (event.target.name == 'exp_date'
299. event.inputType == 'insertCompositionText') {
300. if (((this.valueDate.length == 4
301. this.valueDate.search('/') == -1) || this.valueDate.length == 5)
302. this.valueDate.length == 5)
303. if (temp.length == 0) {
304. return (counter % 10 == 0);
305. } else if (this._b == 19) {
306. } else if (this._b == 27) {
307. if (this._b == 12) {
308. if (this.cardTypeBank == 'bank_customer_code') {
309. } else if (this.cardTypeBank == 'bank_account_number') {
310. _formCard.exp_date.length == 5
311. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
312. this._b == 3)) {//27-pvcombank;3-TPB ;
313. if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
314. this._b == 19
315. this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
316. if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
317. if (this.cardTypeOcean == 'IB') {
318. } else if (this.cardTypeOcean == 'MB') {
319. } else if (this.cardTypeOcean == 'ATM') {
320. if (this._b == 18) {
321. if (this._b == 27
322. this._b == 18) {
323. if (err.status == 400
324. err.error['name'] == 'INVALID_INPUT_BIN') {
325. if ((cardNo.length == 16
326. if ((cardNo.length == 16 || (cardNo.length == 19
327. && ((this._b == 18
328. cardNo.length == 19) || this._b != 18)
329. if (this._b == +e.id) {
330. if (valIn == 1) {
331. } else if (valIn == 2) {
332. this._b == 3) {
333. if (this._b == 19) {
334. if(e.index == 0
335. if (cardType == this._translate.instant('internetbanking')
336. } else if (cardType == this._translate.instant('mobilebanking')
337. } else if (cardType == this._translate.instant('atm')
338. if(groupE.index == 0
339. this._b == 18))) {
340. } else if (this._b == 18
341. this.c_expdate = !(((this.valueDate.length == 4
342. this.valueDate.length == 4
343. this.valueDate.search('/') == -1)
344. this.valueDate.length == 5))
345. if(id == 1){
346. if(id == 2){
347. if(id == 3){
348. <div class="nd-form-input" [class.custome_padding]="(cardTypeBank == 'internet_banking')&&(_b == 2
349. (cardTypeBank == 'bank_card_number') && (_b == 67 || (checkTwoEnabled && techcombankCard)) && !isOffTechcombankNapas
350. (cardTypeBank == 'bank_card_number') &&(_b == 67 || (checkTwoEnabled && techcombankCard))&& ready===1 && !isOffTechcombankNapas
351. (cardTypeBank == 'internet_banking')&&(_b == 2 || (checkTwoEnabled && !techcombankCard))
352. if (this._b == 67
353. this._b == 2) {//19BIDV
354. if ((this._b == 2
355. !this.checkTwoEnabled) || (this._b == 2
356. this._b == 2
357. } else if (this._b == 2
358. if (this._b == 67) {
359. return this._b == 2
360. this._b == 67
361. _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
362. this.checkMod10(cardNo) == true
363. if (this._b != 68 || (this._b == 68
364. return ((value.length == 4
365. value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
366. value.length == 5) && parseInt(value.split('/')[0]
367. || (this._util.checkValidExpireMonthYear(value) && (this._b == 2
368. this._b == 20
369. this._b == 33
370. this._b == 39
371. this._b == 43
372. this._b == 45
373. this._b == 64
374. this._b == 68
375. this._b == 72))) //sonnh them Vietbank 72
376. this._inExpDate.length == 4
377. this._inExpDate.search('/') == -1)
378. this._inExpDate.length == 5))
379. || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 20
380. this._b == 72)));
381. if (this._b == 8) {//MB Bank
382. if (this._b == 18) {//Oceanbank
383. if (this._b == 8) {
384. if (this._b == 12) { //SHB
385. } else if (this._res.state == 'authorization_required') {
386. if (this.challengeCode == '') {
387. <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_card_number')">
388. <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_account_number')">
389. if (this._b == 12) this.isShbGroup = true;
390. return this._b == 9
391. this._b == 11
392. this._b == 16
393. this._b == 17
394. this._b == 25
395. this._b == 44
396. this._b == 57
397. this._b == 59
398. this._b == 61
399. this._b == 63
400. this._b == 69
401. if (this.cardTypeBank == 'bank_account_number') {
402. cardNo.length == 19) && cardNo.startsWith("970443") && this._util.checkMod10(cardNo) && this.checkBin(cardNo)) {
403. if(e.index == 0) this.previewCardService.setDisplayCard(true)
404. if (this._b == 2
405. this._b == 31
406. this._b == 80) {
407. if (this._b == 2) {
408. } else if (this._b == 6) {
409. } else if (this._b == 31) {
410. } else if (this._b == 80) {
411. if (this._b == 5) {//5-vib;
412. if (this._b == 5) {
413. if (this.checkBin(_val.value) && (this._b == 5)) {
414. if (this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
415. if (this.checkBin(v) && (this._b == 5)) {
416. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 5)) {//27-pvcombank;3-TPB ;
417. if (this.cardName != null && this.cardName.length > 0 && (this._b == 5)) {//3-TPB ;19-BIDV;27-pvcombank;
418. _b == 68"
419. if (this._b == 1
420. this._b == 73
421. this._b == 36
422. this._b == 55
423. this._b == 47
424. this._b == 48
425. this._b == 59) {
426. return this._b == 11
427. this._b == 72
428. this._b == 74
429. this._b == 75
430. this._b == 14
431. this._b == 15
432. this._b == 24
433. this._b == 8
434. this._b == 10
435. this._b == 22
436. this._b == 23
437. this._b == 30
438. this._b == 9) {
439. } else if(err.status == 400
440. err.error['name'] == 'INVALID_MERCHANT') { // merchant inactive
441. (cardNo.length == 19
442. (cardNo.length == 19 && (this._b == 1
443. this._b == 4
444. this._b == 59))
445. this._util.checkMod10(cardNo) == true
446. if (id == 1) {
447. if (id == 2) {
448. if (id == 3) {
449. (token || _auth==1) && _b != 16
450. (token || _auth==1) && _b != 16; else normalHeader
451. bankSelected && (token || _auth == 1) && _b != 16
452. (!token && _auth==0 && vietcombankGroupSelected) || (token && _b == 16)
453. _b == 16)">
454. _auth==0 && techcombankGroupSelected
455. _auth==0 && shbGroupSelected
456. _auth==0 && onepaynapasGroupSelected
457. _auth==0 && bankaccountGroupSelected
458. _auth==0 && vibbankGroupSelected
459. _auth==0 && vietQRSelected
460. if (type == 2) {
461. if(type == 2
462. if (e.id == '31') {
463. } else if (e.id == '80') {
464. if (!(strTest == 'card
465. if (d.b.card_list == s) {
466. if (item.b.id == '2'
467. item.b.id == '67') {
468. $event == 'true') {
469. _b: this._b == '2' ? '67' : this._b
470. || (off && !this.enabledTwoBankTech && ((bankid == '2'
471. this.isOffTechcombank) || (bankid == '67'
472. if (bankId == 1
473. bankId == 4
474. bankId == 7
475. bankId == 11
476. bankId == 14
477. bankId == 15
478. bankId == 16
479. bankId == 20
480. bankId == 22
481. bankId == 24
482. bankId == 25
483. bankId == 30
484. bankId == 33
485. bankId == 34
486. bankId == 35
487. bankId == 36
488. bankId == 41
489. bankId == 43
490. bankId == 45
491. bankId == 46
492. bankId == 47
493. bankId == 50
494. bankId == 57
495. bankId == 62
496. bankId == 63
497. bankId == 69
498. bankId == 70
499. bankId == 71
500. bankId == 73
501. bankId == 32
502. bankId == 75) {
503. } else if (bankId == 6
504. bankId == 31
505. bankId == 80) {
506. } else if (bankId == 2
507. bankId == 67) {
508. } else if (bankId == 3
509. bankId == 18
510. bankId == 19
511. bankId == 27) {
512. } else if (bankId == 5) {
513. } else if (bankId == 12) {
514. } else if (bankId == 'vietqr') {
515. this._b == '55'
516. this._b == '47'
517. this._b == '48'
518. this._b == '19'
519. this._b == '59'
520. this._b == '73'
521. this._b == '12') {
522. this._b == '3'
523. this._b == '43'
524. this._b == '45'
525. this._b == '57'
526. this._b == '61'
527. this._b == '63'
528. this._b == '67'
529. this._b == '68'
530. this._b == '69'
531. this._b == '72'
532. this._b == '74'
533. this._b == '75') {
534. this._b == '36'
535. this._b == '75') { //sonnh them 72 Vietbank
536. bankId == '55'
537. bankId == '47'
538. bankId == '48'
539. bankId == '19'
540. bankId == '59'
541. bankId == '73'
542. bankId == '5'
543. bankId == '12');
544. if (item['id'] == this._b) {
545. _b == 6"
546. this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
547. if (this._b == 6) this.htmlDesc = this._translate.instant('6_1_html_desc'); // thằng đông á bank custome lại
548. if (message == '1') {
549. return this._b == 3
550. this._b == 9
551. return this._b == 6
552. return this._b == 18
553. return this._b == 20
554. if (parseInt(b) == parseInt(v.substring(0, 6))) {
555. } else if (this._b == 2) {
556. this.token_site == 'onepay'
557. this._b == 18
558. this._b == 12
559. this._b == 5
560. this._b == 67))
561. this.c_card = !this.filteredData.some(obj=> this._b == parseInt(obj?.b?.id));
562. numberBank == 2) { // bank bảo trì
563. this._b == 67) {
564. _auth==1) && _b != 16; else noBankSelected">{{'otp_header_title' | translate}}</h3>
565. _auth==1) || (token
566. _b == 16)"
567. this._auth == 0
568. this.tokenList?.length == 0) {
569. if (this.filteredData && (this.filteredData?.length == 1
570. this.filteredData?.length == 2) && !this.tokenList?.length && this.onePaymentMethod) {
571. if ($event && ($event == 'true'
572. domescard_version == '1'"
573. domescard_version == '2'"
574. viewName == 'auth_method_ocean'"
575. (click)="selectData(viewName == 'province' ? item.label : item.name
576. item.code == codeSelected"
577. {{ viewName == 'province' ? item.label : item.name }}
578. if (this.viewName == ViewName.country) this.codeSelected = 'default'
579. if (this.viewName == ViewName.country
580. if (this.viewName == ViewName.province
581. const findInArray = this.datas.find(o => o.code == this.defaultValueByCode)
582. if (this.viewName == ViewName.province) this.filteredDatas = this.datas.filter(item => item?.label?.toLowerCase().includes(this.searchTerm.toLowerCase()));
583. (!!exp_date + !!csc + !!card_name)==2"
584. } else if (this._res_post.state == 'failed') { // lỗi mới kiểm tra sang trang lỗi
585. v.length == 15) || (v.length == 16
586. v.length == 19))
587. this._util.checkMod10(v) == true) {
588. cardNo.length == 15)
589. cardNo.length == 16)
590. cardNo.startsWith('81')) && (cardNo.length == 16
591. cardNo.length == 19))
592. this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
593. this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
594. v.length == 5) {
595. v.length == 4
596. v.length == 3)
597. _val.value.length == 4
598. _val.value.length == 3)
599. this._i_first_name.trim().length == 0
600. this._i_last_name.trim().length == 0
601. this.requireAvs = this.AVS_COUNTRIES.find(e => e.code == this._i_country_code) ? true : false;
602. this._i_csc.length == 4) ||
603. this._i_csc.length == 3)
604. country = this.AVS_COUNTRIES.find(e => e.code == res.bin_country);
605. countryCode == 'US' ? US_STATES
606. : countryCode == 'CA' ? CA_STATES
607. numberCardActive == 1}"
608. numberCardActive == 1 ? '8px' : '' }">
609. indexTab == 0"
610. if (this.numberCardActive == 1
611. cardActive == 1) this.previewCardService.setInitDataCard({ logo: this.detectLogo(), fixedLogo: true, numberCard: '', name: '', csc: ''
612. if (index == 0) {
613. if (event.data?.event_type == 'applepay_network_not_supported') {
614. if (network == 'napas'
615. <h6>{{ data?.type == 'apple_not_support' ? ('suitable_payment_device' | translate) : ('vietqr_payment_guide' |
616. data?.type == 'apple_not_support'"
617. data?.number_method == 1"
618. data?.number_method == 2"
619. data?.number_method == 3"
620. data?.number_method==1"
621. data?.number_method==2"
622. data?.number_method==3 "
623. data?.type == 'all'"
624. if (res?.body?.state == 'approved') {
625. } else if (res?.body?.state == 'authorization_required') {
626. serviceId: this.environment == 'PRODUCTION' ? this.serviceIdProd : this.serviceIdTest
627. screen=='qr'"
628. screen=='confirm_close'"
629. this.themeConfig.deeplink_status == 'Off' ? false : true
630. if (d.b.code == s) {
631. if (item.available == true) {
632. if (appcode == 'grabpay'
633. appcode == 'momo') {
634. if (type == 2
635. err.error.code == '04') {
636. e.type == 'vnpayqr') {
637. e.type == 'ewallet') {
638. e.type == 'wallet')) {
639. bank=='ShopeePay'"
640. type == 'vnpay'"
641. type == 'bankapp'"
642. type == 'both'"
643. e.type == 'deeplink') {
644. this.listWallet.length == 1
645. this.listWallet[0].code == 'momo') {
646. this.checkEWalletDeeplink.length == 0) {
647. arrayWallet.length == 0) return false;
648. if (arrayWallet[i].code == key) {
649. <qr-main-version1 *ngIf="((qr_version2 == 'qrV1'
650. version2 == 'default') || (version2 == '1')" [data]="data"
651. version2 == '2'"
652. if (banksRes?.status == 200
653. if (appRes?.status == 200
654. vietQRData?.state=='created' else state_failed"
655. vietQRData?.state=='created'"
656. if (res.body?.state == 'created') {
657. if (payment?.state == 'failed'
658. payment?.state == 'canceled'
659. payment?.state == 'expired') {
660. type == 5"
661. type == 2"
662. type == 1"
663. type == paymentType"
664. paymentType == PaymentType.ApplePay"
665. paymentType == PaymentType.GooglePay"
666. paymentType == PaymentType.SamsungPay"
667. this.paymentType == PaymentTypeMobile.ApplePay) {
668. this.paymentType == PaymentTypeMobile.GooglePay) {
669. this.paymentType == PaymentTypeMobile.SamsungPay) {
670. type == 3"
671. type == 4"
672. type == 7"
673. if (type == '1') { // Thanh toán bằng thẻ tín dụng/ghi nợ
674. } else if (type == '2') { // Thanh toán bằng thẻ ATM/STK ngân hàng
675. errorCode == '11'"
676. isappleerror ==false"
677. isappleerror==true"
678. errorCode && (errorCode == '253' || errorCode == 'overtime')
679. errorCode == 'overtime')">
680. errorCode == 'INVALID_CARD_LIST'"
681. <div class="footer-button" *ngIf="(isSent == false
682. isSent == false
683. <div *ngIf="(isSent == false
684. <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
685. (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
686. *ngIf="!(errorCode == 'overtime'
687. !(errorCode == 'overtime' || errorCode == '253') && isBack && !invoiceNearExpire && !paymentPending
688. if (params && (params['bnpl'] == 'true')) {
689. if (params && (params['bnpl'] == 'false')) {
690. if (_re.body.state == 'closed')
691. this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'amigo') {
692. this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'kredivo') {
693. if (this.paymentInformation.type == "applepay") {
694. } else if (this.paymentInformation.type == "googlepay") {
695. if (this.res.themes && (this.res.themes.theme == 'general'
696. this.res.themes.theme == 'generalv2')) {
697. params.response_code == 'overtime') {
698. if (_re2.status == '200'
699. _re2.status == '201') {
700. if (this.errorCode == 'overtime'
701. this.errorCode == '253') {
702. } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
703. } else if (this.errorCode == 'AM1') {
704. if (this.paymentInformation.type == 'bnpl') {
705. if (this.paymentInformation.provider == 'amigo'
706. this.errorCode == '2') {
707. else if (this.paymentInformation.provider == 'kbank'
708. else if (this.paymentInformation.provider == 'homecredit'
709. else if (this.paymentInformation.provider == 'kredivo'
710. else if (this.paymentInformation.provider == 'fundiin'
711. this.res.state == 'canceled') {
712. if (lastPayment?.state == 'pending') {
713. if(this.locale == 'vi')
714. if (this.isTimePause == false) {
715. if (this.isappleerror == true) {
716. if (response.body.state == 'not_paid') {
717. if (response.body.payments[response.body.payments.length - 1].state == "failed") {
718. } else if (response.body.state == 'paid') {
719. } else if (response.body.state == 'canceled'
720. response.body.state == 'closed'
721. response.body.state == 'expired') {
722. if (this.locale == 'vi') {
723. amigo_type == 'SP'"
724. amigo_type == 'PL'"
725. this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
726. this.res.themes.theme == 'general') {
727. const findData = dataConfig.find((obj: any) => obj.value == color)
728. color == this.defaultColor.color5) data = this.defaultColor
729. if (H.length == 4) {
730. } else if (H.length == 7) {
731. if (delta == 0) {
732. } else if (cmax == r) {
733. } else if (cmax == g) {
734. s = delta == 0 ? 0 : delta / (1 - Math.abs(2 * l - 1))
735. if (isIphone == true) {
736. } else if (isAndroid == true) {
737. _translate?.currentLang == 'en'"
738. return countPayment == maxPayment
739. if (this.getLatestPayment().state == 'canceled')
740. return ((a.id == id
741. a.code == id) && a.type.includes(type));
742. if (res?.state == 'canceled') {
743. state == 'authorization_required'
744. if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
745. else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';
746. } else if (latestPayment?.state == "failed") {
747. if (e.name == bankSwift) { // TODO: get by swift
748. return this.apps.find(e => e.code == appCode);
749. if (e.id == bankId) {
750. if (+e.id == bankId) {
751. if (e.swiftCode == bankSwift) {
752. if (this.checkCount == 1) {
753. if (results == null) {
754. if (c.length == 3) {
755. d = d == undefined ? '.' : d
756. t = t == undefined ? '
757. return results == null ? null : results[1]
758. if (((_val.length == 4
759. _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
760. _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
761. iss_date.length == 4
762. iss_date.search('/') == -1)
763. iss_date.length == 5))
764. let applepayNapas = document.getElementById('applepay-napas')?.value == 'true'
765. let applepayVisa = document.getElementById('applepay-visa')?.value == 'true'
766. let applepayMasterCard = document.getElementById('applepay-masterCard')?.value == 'true'
767. let applepayJCB = document.getElementById('applepay-jcb')?.value == 'true'
768. if (applepayNapas == true) {
769. if (applepayVisa == true) {
770. if (applepayMasterCard == true) {
771. if (applepayJCB == true) {
772. if(document.getElementById('applepay-merchantAVS').value == 'true'){
773. response.status == '400') {
774. } else if (response.status == '500') {
775. if (data.name == "CARD_TYPE_CAN_NOT_BE_PROCESSED") { // in case response.status == 400
776. } else if (data.state == "approved"){ // in case response.ok
777. "undefined"==typeof c.execScript
778. function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a
779. (function(){var m,n,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype
780. a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0
781. (function(){var m,n,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){a=["object"==typeof globalThis
782. "object"==typeof window
783. "object"==typeof self
784. "object"==typeof global
785. c.Math==Math)return c}throw Error("Cannot find global object");
786. },p=ca(this),da="function"===typeof Symbol&&"symbol"===typeof Symbol("x"),r={},ea={},t=function(a,b,c){if(!c||null!=a){c=ea[b];if(null==c)return a[b];c=a[c];return void 0!==c?c:a[b]}},u=function(a,b,c){if(b)a:{var d=a.split(".");a=1===d.length;var e=d[0];e=!a&&e in r?r:p;for(var g=0
787. "iterator")&&a[t(r.Symbol,"iterator")];if(b)return b.call(a);if("number"==typeof a.length)return{next:aa(a)};throw Error(String(a)+"
788. function"==typeof Object.create?Object.create:function(a){var b=
789. "function"==typeof Object.setPrototypeOf)ja=Object.setPrototypeOf;else{var ka;a:{var la={a:!0},ma={};try{ma.__proto__=la;ka=ma.a;break a}catch(a){}ka=!1}ja=ka?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var na=ja;
790. h(f)})}if(a)return a;b.prototype.vb=function(f){if(null==this.T){this.T=[];var h=this;this.wb(function(){h.jc()})}this.T.push(f)};var d=p.setTimeout;b.prototype.wb=function(f){d(f
791. this.mb(l);return}"function"==typeof h?this.Ec(h,f):this.Eb(f)};e.prototype.mb=function(f){this.Vb(2,f)};e.prototype.Eb=function(f){this.Vb(1,f)};e.prototype.Vb=function(f,h){if(0!=this.oa)throw Error("Cannot settle("+f+", "+h+"): Promise already settled in state"+this.oa)
792. this.na=null}};var g=new b;e.prototype.Dc=function(f){var h=this.bb();f.Ka(h.resolve,h.reject)};e.prototype.Ec=function(f,h){var l=this.bb();try{f.call(h,l.resolve,l.reject)}catch(k){l.reject(k)}};e.prototype.then=function(f,h){function l(E,H){return"function"==typeof E?function(oa){try{k(E(oa))}catch(V){q(V)}
793. null==this.na?g.vb(l):this.na.push(l)
794. 0==H
795. 4==l.get(h)}catch(k){return!1}}())return a;var d="$jscomp_hidden_"+Math.random(),e=0,g=function(f){this.va=(e+=Math.random()+1).toString();if(f){f=v(f);for(var h
796. "object"==k
797. "function"==k?b.has(l)?k=b.get(l):(k=""+ ++f,b.set(l,k)):k="p_"+l
798. "function"==typeof t(Object,"assign")?t(Object,"assign"):function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)w(d,e)&&(a[e]=d[e])}return a
799. "iterator")&&b[t(r.Symbol,"iterator")];if("function"==typeof g){b=g.call(b);for(var f=0;!(g=b.next()).done;)e.push(c.call(d,g.value,f++))}else for(g=b.length
800. u("String.prototype.includes",function(a){return a?a:function(b,c){if(null==this)throw new TypeError("The 'this' value for String.prototype.includes must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype.includes must not be a regular expression");return-1!==(this+"").indexOf(b
801. return"array"==b
802. "object"==b
803. "number"==typeof a.length},ua=function(a){var b=typeof a
804. return"object"==b
805. "function"==b},va=function(a,
806. bb)},jb=function(a,b,c){if(null==c)return b;if("string"===typeof c)return c?a+encodeURIComponent(c):"";for(var d in c)if(Object.prototype.hasOwnProperty.call(c,d)){var e=c[d];e=Array.isArray(e)?e:[e];for(var g=0;g<e.length;g++){var f=
807. null==Wb){Ub=null;break a}Ub=Wb}var Yb=Ub&&Ub[610401301];Tb=null!=Yb?Yb:!1;function Zb(){var a=x.navigator;return a&&(a=a.userAgent)?a:""}var $b,ac=x.navigator;$b=ac?ac.userAgentData||null:null;function I(a){return-1!=Zb().indexOf(a)};var bc={},J=function(a,b){if(b!==bc)throw Error("SafeHtml is not meant to be built directly");this.Nb=a};J.prototype.toString=function(){return this.Nb.toString()};
808. b=e):(e.Gb=g==e.La-1
809. a);if("CANARY"==K)var b="https://ibfe-canary.corp.google.com";else b="https://pay","SANDBOX"==K?b+=".sandbox":"PREPROD"==K&&(b+="-preprod.sandbox"),b+=".google.com";mc.postMessage(a
810. "SANDBOX"==K?b+=".sandbox":"PREPROD"==K
811. .sandbox":"PREPROD"==K
812. (function(){if(!F){var a=window.gpayInitParams||{};K=a.environment||"PRODUCTION";F=document.createElement("iframe");kc(F,hb(B(("CANARY"==K?"https://ibfe-canary.corp":"https://pay")+("PREPROD"==K?"-preprod.sandbox":"SANDBOX"==K?".sandbox":"")+".google.com/gp/p/ui/payframe?origin=%{windowOrigin}&mid=%{merchantId}"),{windowOrigin:window.location.origin,merchantId:a.merchantInfo&&a.merchantInfo.merchantId||""}));M({eventType:15,clientLatencyStartMs:Date.now()});sc();F.height="0";F.width="0";F.style.display=
813. var vc="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split("");function wc(a){for(var b=Array(36),c=0,d,e=0;36>e;e++)8==e
814. 13==e
815. 18==e
816. 23==e?b[e]="-":14==e?b[e]="4":(2>=c&&(c=33554432+16777216*Math.random()|0),d=c&15,c>>=4,b[e]=vc[19==e?d&3|8:d])
817. -":14==e?b[e]="4":(2>=c&&(c=33554432+16777216*Math.random()|0),d=c&15,c>>=4,b[e]=vc[19==e?d&3|8:d])
818. var vc="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split("");function wc(a){for(var b=Array(36),c=0,d,e=0;36>e;e++)8==e||13==e||18==e||23==e?b[e]="-":14==e?b[e]="4":(2>=c&&(c=33554432+16777216*Math.random()|0),d=c&15,c>>=4,b[e]=vc[19==e?d&3|8:d]);return b.join("")+"."+a}function xc(a){for(var b=1;b<arguments.length;b++)a=yc(a,arguments[b]);return a}
819. Array.isArray(a[c])?a[c]=b[c]:yc(a[c],b[c]));return a}function zc(a){var b=0,c;if(0==a.length)return b;for(c=0
820. b);if(1==d.childNodes.length)b=d.removeChild(z(d.firstChild));else for(b=c.createDocumentFragment()
821. "function"==typeof a.ea)return a.ea();if("undefined"!==typeof r.Map
822. "function"==typeof a.cb)return a.cb();if(!a.ea
823. "function"==typeof a.forEach)a.forEach(b
824. "file"==b)a.push("//"),(b=this.ha)&&a.push(Wc(b,Xc,!0)
825. "/"==c.charAt(0)?Yc:Zc
826. !this.M)d="/"+d;else{var e=b.M.lastIndexOf("/");-1!=e&&(d=b.M.slice(0,e+1)+d)}e=d;if(".."==e
827. "."==e)d="";else if(-1!=e.indexOf("./")
828. (d=b.M.slice(0,e+1)+d)}e=d;if(".."==e||"."==e)d="";else if(-1!=e.indexOf("./")||-1!=e.indexOf("/.")){d=0==e.lastIndexOf("/",0)
829. "."==h?d
830. f==e.length
831. g.push(""):".."==
832. 1==g.length
833. "dark";var k="long"===f?h.fb.buy[b]:h.fb[f||"buy"][b];D("long"==f
834. "buy"==f?"\n
835. "default"==a.buttonColor
836. a=null==(c=a.parameters)?void 0:c.allowedCardNetworks
837. "mouseover"==c.type);var d=document.querySelector(".gpay-card-info-animation-container");null!==d&&d.classList.toggle("hover"
838. "mouseover"==c.type)})});["mousedown","mouseup","mouseout"].map(function(b){a.addEventListener(b,function(c){a.classList.toggle("active"
839. "mousedown"==c.type)})});["focus","blur"].map(function(b){a.addEventListener(b,function(c){a.classList.toggle("focus"
840. "focus"==
841. google.payments.api.EnableGpayNewButtonAsset?b+" "+c+" new_style "+rd(a.buttonLocale):b+" "+c+" "+rd(a.buttonLocale)}function R(a){return"white"==a.buttonColor}
842. m.isReadyToPay=function(a){var b=Kd(a);return new r.Promise(function(c){(void 0!=b.hasEnrolledInstrument?b.hasEnrolledInstrument():b.canMakePayment()).then(function(d){window.sessionStorage.setItem("google.payments.api.storage.isreadytopay.result",d.toString());var e={result:d};2<=a.apiVersion&&a.existingPaymentMethodRequired&&(e.paymentMethodPresent=d);c(e)}).catch(function(){window.sessionStorage.getItem("google.payments.api.storage.isreadytopay.result")?c({result:"true"==window.sessionStorage.getItem("google.payments.api.storage.isreadytopay.result")}):
843. "TEST"==b
844. 1==b.length
845. "CRYPTOGRAM_3DS"==b[0])return!0}return 1==a.allowedPaymentMethods.length&&"TOKENIZED_CARD"==a.allowedPaymentMethods[0]}function Pd(a,b){return 2<=a.apiVersion&&(a=Od(a))&&t(a,"includes").call(a,b)?!0:!1}
846. var b=Od(a);if(b&&1==b.length&&"CRYPTOGRAM_3DS"==b[0])return!0}return 1==a.allowedPaymentMethods.length
847. "TOKENIZED_CARD"==a.allowedPaymentMethods[0]}function Pd(a,b){return 2<=a.apiVersion
848. a.indexOf(".google.com",b)==b
849. 0==a.allowedPaymentMethods.length)return"for v2 allowedPaymentMethods must be set to an array containing a list of accepted payment methods";for(var b=0
850. function Sd(a){if(!a)return"isReadyToPayRequest must be set!";if(Td(a))return"UPI not supported";if(2<=a.apiVersion){if(!("apiVersionMinor"in a))return"apiVersionMinor must be set!";if(!a.allowedPaymentMethods||!Array.isArray(a.allowedPaymentMethods)||0==a.allowedPaymentMethods.length)return"for v2 allowedPaymentMethods must be set to an array containing a list of accepted payment methods";for(var b=0;b<a.allowedPaymentMethods.length;b++){var c=a.allowedPaymentMethods[b];if("CARD"==c.type){if(!c.parameters)return"Field parameters must be setup in each allowedPaymentMethod";
851. 0==d.length)return"allowedCardNetworks must be setup in parameters for type CARD";c=c.parameters.allowedAuthMethods;if(!c
852. 0==c.length
853. 0==a.allowedPaymentMethods.length
854. d)}).length!==!!b.onPaymentDataChanged?"onPaymentDataChanged callback must be set if any of "+(c+" callback intent is set."):null}function Od(a){return a.allowedPaymentMethods&&(a=Xd(a,"CARD"))&&a.parameters?a.parameters.allowedAuthMethods:null}function Xd(a,b){for(var c=0;c<a.allowedPaymentMethods.length;c++){var d=a.allowedPaymentMethods[c];if(d.type==b)return d}return null};var $d=function(a,b){var c=Zd.transition;if(!c){var d=Jc();c=d;void 0===a.style[d]&&(d=(Ec?"Webkit":Dc?"Moz":Cc?"ms":null)+Kc(d)
855. if(Dc){a:{try{Bc(b.nodeName);var e=!0;break a}catch(g){}e=!1}e||(b=null)}}else"mouseover"==c?b=a.fromElement:"mouseout"==c
856. 0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||("keypress"==c?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType="string"===typeof a.pointerType?a.pointerType:ee[a.pointerType]||"";this.state=a.state;this.timeStamp=a.timeStamp;this.ua=
857. 0==e.length
858. 0==a.v[c].length
859. g.listener==b
860. g.capture==!!c
861. g.Oa==d)return e}return-1};var ne="closure_lm_"+(1E6*Math.random()|0),oe={},pe=0,re=function(a,b,c,d,e){if(d&&d.once)qe(a,b,c,d,e);else if(Array.isArray(b))for(var g=0;g<b.length;g++)re(a,b[g],c,d,e);else c=se(c),a&&a[ge]?(d=ua(d)?!!d.capture:!!d,te(a),a.J.add(String(b),c,!1,d,e)):ue(a,b,c,!1,d,e)},ue=function(a,b,c,d,e,g){if(!b)throw Error("Invalid event type");var f=ua(e)?!!e.capture:!!e,h=ve(a);h||(a[ne]=h=new ke(a));c=h.add(b,c,d,f,g);if(!c.proxy){d=we();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)de||(e=f),void 0===
862. 0==c.Fa
863. f.capture==c){var h=f.listener,l=f.Oa||f.src;f.Ja&&me(a.J,f);e=!1!==h.call(l,d)&&e}}return e&&!d.defaultPrevented};
864. ok"==a?b:null
865. ok"==a
866. failed"==a?Error(String(b)||""):null};function Fe(a){var b=a.indexOf("#")
867. return-1==b?a:a.substring(0,b)}function Ge(a){return a?(/^[?#]/.test(a)?a.slice(1):a).split("&").reduce(function(b,c){var d=c.split("=");c=decodeURIComponent(d[0]||"");d=decodeURIComponent(d[1]||"");c&&(b[c]=d);return b},{}):{
868. function He(a){var b={requestId:a.requestId,returnUrl:a.Ub,args:a.dc};void 0!==a.origin&&(b.origin=a.origin);void 0!==a.hb&&(b.originVerified=a.hb);return JSON.stringify(b)}function Ie(a,b,c){if(b.ok)c(b);else{var d;if(!(d=b.error)){d=null;if("function"==typeof a.DOMException){a=a.DOMException;try{d=new a("AbortError","AbortError")}catch(e){}}d||(d=Error("AbortError")
869. function"==typeof a.rb?a.rb():a.rb);return a.sb},Ne=function(a){if(null==a.C)throw Error("not connected");return a.C},Qe=function(a,b){var c=null;a.tb&&"function"==typeof a.g.MessageChannel&&(c=new a.g.MessageChannel);c?(Oe(a,"start",b,[c.port2])
870. "function"==typeof a.g.MessageChannel
871. connect"==b?null!=a.C?
872. Me(this)==a.source){var b=a.data;if(b&&"__ACTIVITIES__"==b.sentinel){var c=b.cmd;if(!this.j||"connect"==c||"start"==c){var d=a.origin;b=b.payload||null;null==this.C&&"start"==c&&(this.C=d);null==this.C&&a.source&&Me(this)==a.source&&(this.C=d);d==this.C&&this.ma(c,b,a)}}}};
873. "__ACTIVITIES__"==b.sentinel){var c=b.cmd;if(!this.j||"connect"==c||"start"==c){var d=a.origin;b=b.payload||null;null==this.C&&"start"==c&&(this.C=d);null==this.C&&a.source&&Me(this)==a.source&&(this.C=d);d==this.C&&this.ma(c,b,a)}}}};
874. "connect"==c
875. "start"==c){var d=a.origin;b=b.payload||null;null==this.C&&"start"==c&&(this.C=d);null==this.C&&a.source&&Me(this)==a.source&&(this.C=d);d==this.C&&this.ma(c,b,a)}}}};
876. null==this.C
877. "start"==c
878. Me(this)==a.source
879. d==this.C
880. T.prototype.ma=function(a,b,c){"connect"==a?(this.j&&(Le(this.j),this.j=null),this.tb=b&&b.acceptsChannel||!1,this.V(a,b)):"start"==a?((c=c.ports&&c.ports[0])&&Pe(this,c),this.V(a,b)):"msg"==a?null!=this.Ra
881. this.Ra(b):"cnget"==a?(b=b.name||"",a=Re(this,b),a.port1||(c=new this.g.MessageChannel,a.port1=c.port1,a.port2=c.port2,a.Tb(a.port1)),a.port2&&(Oe(this,"cnset",{name:b},[a.port2]),a.port2=null)):"cnset"==a?(a=c.ports[0],b=Re(this,b.name),b.port1=a,b.Tb(a)):this.V(a,b)};
882. m.ma=function(a,b){"connect"==a?(Qe(this.m,this.Ha),this.sa()):"result"==a?this.I
883. "failed"==a?Error(b.data||""):b.data
884. (a=b.code,b=new Ee(a,"failed"==a?Error(b.data||""):b.data,"iframe",Ne(this.m),!0,!0),Ie(this.g,b,this.I),this.I=null,Oe(this.m,"close"),this.disconnect()):"ready"==a?this.Ua
885. (this.Ua(),this.Ua=null):"resize"==a
886. "_"==h[0])throw Error('The only allowed targets are "_blank"
887. requestId:a.xc,Ub:a.fa.Ub||Fe(a.g.location.href),dc:a.Ha});c=c+(-1==c.indexOf("#")?"#":"&")+encodeURIComponent("__WA__")+"="+encodeURIComponent(d)}d=a.sc;"_top"!=A(d)&&Je(a.g)&&(d=B("_top"));try{var e=Ic(c
888. e=a.g==a.g.top
889. "connect"==a?(Qe(this.m,this.Ha),this.sa()):"result"==a?(a=b.code,this.W(a,"failed"==a?Error(b.data||""):b.data)):"check"==a
890. "failed"==a?Error(b.data||""):b.data)):"check"==a&&this.g.setTimeout(function(){return Ye(c)},200)};var Ze=function(a,b,c,d,e){this.g=a;this.hc=b;this.ic=c;this.C=d;this.Fc=e};
891. g.requestId==a){var f=d.location.hash;if(f){var h=encodeURIComponent("__WA_RES__")+"=";e=-1;do if(e=f.indexOf(h,e),-1!=e){var l=0<e?f.substring(e-1,e):"";if(""==l||"?"==l||"#"==l||"&"==l){var k=f.indexOf("&",e+1);-1==k&&(k=f.length);f=f.substring(0,e)+f.substring(k+1)}else e++}while(-1!=e&&e<f.length)}var q=f;q=q||"";if(q!=
892. e):"";if(""==l
893. "?"==l
894. "#"==l
895. "&"==l){var k=f.indexOf("&",e+1);-1==k&&(k=f.length);f=f.substring(0,e)+f.substring(k+1)}else e++}while(-1!=e&&e<f.length)}var q=f;q=q||"";if(q!=
896. -1==k
897. H==oa)}else c=null}else c=null}catch(V){Ke(V),this.Qb(V)}c&&(this.ob[a]=c)}(a=c)&&df(a,b)};
898. -1==["DEVELOPER_ERROR","MERCHANT_ACCOUNT_ERROR"].indexOf(c.statusCode)
899. "AbortError"==c
900. 599<g.status)g.onreadystatechange=null,d(Error("Unknown HTTP status "+g.status));else if(4==g.readyState)try{c(JSON.parse(g.responseText))}catch(f){d(f)}};g.onerror=function(){d(Error("Network failure"))};g.onabort=function(){d(Error("Request aborted"))};g.send(b)})};
901. READY_TO_PAY"==q.data.isReadyToPayResponse);c(y)})):c({result:k})}}})};
902. null==(h=g.Na())
903. h.close()});g.onMessage(function(h){"partialPaymentDataCallback"==
904. h.type?b.kb=Ed(h.data,b.B,b.ga,f.Qa).then(function(l){return g.message(l)}):"fullPaymentDataCallback"==h.type
905. var ff=function(a){return"LOCAL"==a.s?"":"https://"+("PREPROD"==a.s?"pay-preprod.sandbox":"SANDBOX"==a.s?"pay.sandbox":"CANARY"==a.s?"ibfe-canary.corp":"pay")+".google.com"},hf=function(a){var b=ff(a)+"/gp/p/apis/buyflow/process"
906. var ff=function(a){return"LOCAL"==a.s?"":"https://"+("PREPROD"==a.s?"pay-preprod.sandbox":"SANDBOX"==a.s?"pay.sandbox":"CANARY"==a.s?"ibfe-canary.corp":"pay")+".google.com"},hf=function(a){var b=ff(a)+"/gp/p/apis/buyflow/process";a.Rb&&(b+="?rk="+encodeURIComponent(a.Rb));return b},lf=function(a){var b;if(null==(b=a.i)?0:b.disableNgbf)return!1;var c;if((null==(c=a.callbackIntents)?0:c.length)
907. +encodeURIComponent(a.Rb));return b},lf=function(a){var b;if(null==(b=a.i)?0:b.disableNgbf)return!1;var c;if((null==(c=a.callbackIntents)?0:c.length)
908. var ff=function(a){return"LOCAL"==a.s?"":"https://"+("PREPROD"==a.s?"pay-preprod.sandbox":"SANDBOX"==a.s?"pay.sandbox":"CANARY"==a.s?"ibfe-canary.corp":"pay")+".google.com"},hf=function(a){var b=ff(a)+"/gp/p/apis/buyflow/process";a.Rb&&(b+="?rk="+encodeURIComponent(a.Rb));return b},lf=function(a){var b;if(null==(b=a.i)?0:b.disableNgbf)return!1;var c;if((null==(c=a.callbackIntents)?0:c.length)&&!google.payments.api.EnableWebNgbfForCallbackIntents||a.td)return!1;if(a.allowedPaymentMethods)for(a=v(a.allowedPaymentMethods),
909. TIN"==a.s?"/ui/pay":ff(a)+"/gp/p/ui/pay"
910. path}");if("CANARY"==a)g=B("https://ibfe-canary.corp.google.com/%{path
911. path}");else if("SANDBOX"==a
912. "PREPROD"==a)g=B("https://pay"+("PREPROD"==a?"-preprod":"")+".sandbox.google.com/%{path
913. b=a.next();!b.done;b=a.next())if(b=b.value,b.parameters&&b.parameters.cvcRequired)return!1;return!0},of=function(a,b){a="TIN"==a.s?"/ui/pay":ff(a)+"/gp/p/ui/pay";b&&(a+="?ng=true");return a},qf=function(a,b,c,d){var e=window.location.origin,g=B("https://pay.google.com/%{path}");if("CANARY"==a)g=B("https://ibfe-canary.corp.google.com/%{path}");else if("SANDBOX"==a||"PREPROD"==a)g=B("https://pay"+("PREPROD"==a?"-preprod":"")+".sandbox.google.com/%{path}");a={origin:e,coordination_token:void 0===c?"":
914. h;var l=new zd;h.onMessage(function(k){"partialPaymentDataCallback"==k.type?a.kb=Ed(k.data,a.B,a.ga,l.Qa).then(function(q){return h.message(q)}):"fullPaymentDataCallback"==k.type?a.jb=Dd(k.data,a.B,a.ga,l.Qa).then(function(q){return h.message(q)}):"resize"==k.type
915. INTERNAL_ERROR"==b?1:"DEVELOPER_ERROR"==b?2:"MERCHANT_ACCOUNT_ERROR"==b?4:"UNSUPPORTED_API_VERSION"==b?5:"BUYER_CANCEL"==b?6:0):b=1;M({eventType:0,buyFlowActivityReason:c,error:b,softwareInfo:W(a),isReadyToPayRequest:d
916. for(var g=0;g<e.allowedPaymentMethods.length;g++)"CARD"==
917. e.allowedPaymentMethods[g].type&&(e.allowedPaymentMethods[g].parameters.allowedAuthMethods=["CRYPTOGRAM_3DS"]);c.push(8);e=a.la.isReadyToPay(e)}var f=d;Pd(b,"PAN_ONLY")&&(c.push(9),f=a.Z.isReadyToPay(b));return a.l.qa?(c.push(99),e.then(function(){return f})):e.then(function(h){return 1==(h&&h.result)?h:f})};m=X.prototype;m.prefetchPaymentData=function(a){var b=Qd()||Wd(a);b?Ac({aa:"prefetchPaymentData"
918. "Google Inc."==window.navigator.vendor?(this.o.push(98),this.Za.paymentDataCallbacks&&this.o.push(97),a=!0):(this.o.push(36),a=!1));this.qa=a;this.Da=Df(this);this.mode=2;b?(this.o=[38]
919. "Google Inc."==window.navigator.vendor
920. if (_dataCache == null) {
921. if ( (0 <= r && r <= 6 && (c == 0
922. c == 6) )
923. || (0 <= c && c <= 6 && (r == 0
924. r == 6) )
925. if (i == 0
926. _modules[r][6] = (r % 2 == 0);
927. _modules[6][c] = (c % 2 == 0);
928. if (r == -2
929. r == 2
930. c == -2
931. c == 2
932. || (r == 0
933. c == 0) ) {
934. ( (bits >> i) & 1) == 1);
935. if (col == 6) col -= 1;
936. if (_modules[row][col - c] == null) {
937. dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
938. if (bitIndex == -1) {
939. margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
940. if (typeof arguments[0] == 'object') {
941. margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
942. if (b == -1) throw 'eof';
943. if (b0 == -1) break;
944. if (typeof b == 'number') {
945. if ( (b & 0xff) == b) {
946. return function(i, j) { return (i + j) % 2 == 0
947. return function(i, j) { return i % 2 == 0
948. return function(i, j) { return j % 3 == 0
949. return function(i, j) { return (i + j) % 3 == 0
950. return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
951. return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
952. return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
953. return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
954. if (r == 0
955. c == 0) {
956. if (dark == qrcode.isDark(row + r, col + c) ) {
957. if (count == 0
958. count == 4) {
959. if (typeof num.length == 'undefined') {
960. num[offset] == 0) {
961. if (typeof rsBlock == 'undefined') {
962. return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
963. _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
964. if (data.length - i == 1) {
965. } else if (data.length - i == 2) {
966. } else if (n == 62) {
967. } else if (n == 63) {
968. if (_buflen == 0) {
969. if (c == '=') {
970. } else if (c == 0x2b) {
971. } else if (c == 0x2f) {
972. if (table.size() == (1 << bitLength) ) {
973. _locale == 'en'"
974. if (params['locale'] == 'vn') {
975. } else if (params['locale'] == 'en') {

!== (108 điều kiện duy nhất):
------------------------------------------------------------
1. bnpl.code !== 'kbank'
2. bnpl.code !== 'kbank'"
3. if (this.qr_version2 !== 'qrV1'
4. this.qr_version2 !== '') {
5. this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
6. if (_val !== 3) {
7. this._util.getElementParams(this.currentUrl, 't_id') !== '') {
8. if (parseInt(oldStep) !== 1
9. bnpl.code !== 'kredivo'"
10. if (this.fundiinDetail.phoneNumber && ((this.fundiinDetail.phoneNumber.length !== 10
11. this.fundiinDetail.phoneNumber.length !== 11)
12. this.filteredData = this.dataSource.filter(i => i.Id !== "01"
13. i.Id !== "79").sort((a, b) => a.Name.localeCompare(b.Name));
14. selectedBnpl.status !== 'disabled'"
15. if ((this.bnplDetail.phoneNumber.length !== 10
16. this.bnplDetail.phoneNumber.length !== 11)
17. codeResponse.toString() !== '0') {
18. event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
19. key !== '3') {
20. cardNo.length !== 0) {
21. if (this.cardTypeBank !== 'bank_card_number') {
22. if (this.cardTypeBank !== 'bank_account_number') {
23. if (this.cardTypeBank !== 'bank_username') {
24. if (this.cardTypeBank !== 'bank_customer_code') {
25. this.lb_card_account !== this._translate.instant('ocb_account')) {
26. this.lb_card_account !== this._translate.instant('ocb_phone')) {
27. this.lb_card_account !== this._translate.instant('ocb_card')) {
28. this._b !== 18) || (this.cardTypeOcean === 'ATM'
29. let _b = this._b !== 67 ? 67 : this._b
30. if (this.cardTypeBank !== 'internet_banking') {
31. this._b !== 18)) {
32. return sentences.filter(sentence => sentence.trim() !== '');
33. this._b !== 18) || (this._b == 18)) {
34. this.bankList = this.bankList.filter(item => item.b.id !== '67');
35. if (this.tracking.hasOwnProperty(key) && ((key !== '8'
36. !this._showNameOnCard) || (key !== '9'
37. codeResponse.toString() !== '0'){
38. event.inputType !== 'deleteContentBackward') || v.length == 5) {
39. if (deviceValue !== 'default') {
40. this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
41. this._i_country_code !== 'default'
42. this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
43. this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {
44. data?.type !== 'apple_not_support'"
45. const diff = (this.oldb !== b) || (this.oldType !== type)
46. if (this.cardNumberMaxLength !== 15
47. queryString = (sourceURL.indexOf("?") !== -1) ? sourceURL.split("?")[1] : "";
48. if (queryString !== "") {
49. if (YY % 400 === 0 || (YY % 100 !== 0
50. if (YYYY % 400 === 0 || (YYYY % 100 !== 0
51. _translate?.currentLang !== 'en'"
52. queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
53. if (queryString !== '') {
54. if (target !== 0
55. c[d]!==Object.prototype[d]?c[d]:c[d]={
56. return void 0!==c?c:a[b]}},u=function(a,b,c){if(b)a:{var d=a.split(".")
57. g++){var f=d[g];if(!(f in e))break a;e=e[f]}d=d[d.length-1];c=da&&"es6"===c?e[d]:null;b=b(c);null!=b&&(a?ba(r,d,{configurable:!0,writable:!0,value:b}):b!==c
58. function(){};b.prototype=a;return new b},ja;if(da&&"function"==typeof Object.setPrototypeOf)ja=Object.setPrototypeOf;else{var ka;a:{var la={a:!0},ma={};try{ma.__proto__=la;ka=ma.a;break a}catch(a){}ka=!1}ja=ka?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+"
59. "undefined"!==
60. null!==f
61. q;!(q=k.next()).done;)q=q.value,h.call(l,q[1],q[0],this)};c.prototype[t(r.Symbol,"iterator")]=t(c.prototype,"entries");var d=function(h,l){var k=l&&typeof l;"object"==k||"function"==k?b.has(l)?k=b.get(l):(k=""+ ++f,b.set(l,k)):k="p_"+l;var q=h[0][k];if(q&&w(h[0],k))for(h=0;h<q.length;h++){var y=q[h];if(l!==l
62. y.key!==y.key
63. c!==c}},"es6");u("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this
64. return-1!==(this+"").indexOf(b,c||0)}},"es6");/*
65. void 0!==b
66. },Ea=function(a,b,c){"number"!==typeof a
67. return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if("string"===typeof a)return"string"!==typeof b
68. e++){d=arguments[e];for(c in d)a[c]=d[c];for(var g=0;g<Bb.length;g++)c=Bb[g],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};var G=function(a,b){if(b!==Db)throw Error("SafeUrl is not meant to be built directly");this.Ob=a};G.prototype.toString=function(){return this.Ob.toString()};var Eb=function(a){if(a instanceof G
69. got '"+a+"' of type "+sa(a));return"type_error:SafeUrl"},Fb;try{new URL("s://g"),Fb=!0}catch(a){Fb=!1}var Gb=Fb,Db={};new G("about:invalid#zClosurez",Db);new G("about:blank",Db);var Ob=function(a){if(!Hb.test(a))return a;-1!=a.indexOf("&")&&(a=a.replace(Ib,"&"));-1!=a.indexOf("<")&&(a=a.replace(Jb,"<"));-1!=a.indexOf(">")&&(a=a.replace(Kb,">"));-1!=a.indexOf('"')&&(a=a.replace(Lb,"""));-1!=a.indexOf("'")&&(a=a.replace(Mb,"'"));-1!=a.indexOf("\x00")&&(a=a.replace(Nb,"&#0;"));return a},Ib=/&/g,Jb=/</g,Kb=/>/g,Lb=/"/g,Mb=/'/g,Nb=/\x00/g,Hb=/[\x00&<>"']/;var Pb={},Qb=function(){if(Pb!==Pb)throw Error("SafeStyle is not meant to be built directly");this.vc=""};Qb.prototype.toString=function(){return this.vc.toString()};new Qb;var Rb={},Sb=function(){if(Rb!==Rb)throw Error("SafeStyleSheet is not meant to be built directly");this.uc=""};Sb.prototype.toString=function(){return this.uc.toString()};new Sb;var Tb,Ub;a:{for(var Vb=["CLOSURE_FLAGS"]
70. function yc(a,b){if("object"!==typeof b
71. void 0!==b[c]
72. "object"!==typeof a[c]
73. "object"!==typeof b[c]
74. a.tagName+".");if(Gc())for(;a.lastChild;)a.removeChild(a.lastChild);a.innerHTML=cc(b)},Ic=function(a,b,c,d){if(!(a instanceof G||a instanceof G)){a=String(a);b:{var e=a;if(Gb){try{var g=new URL(e)}catch(f){e="https:";break b}e=g.protocol}else c:{g=document.createElement("a");try{g.href=e}catch(f){e=void 0;break c}e=g.protocol;e=":"===e||""===e?"https:":e}}z("javascript:"!==e
75. return void 0!==
76. "undefined"!==typeof Set
77. d++)b.push(a[d]);return b}b=[];c=0;for(d in a)b[c++]=a[d];return b},Nc=function(a){if(a.cb&&"function"==typeof a.cb)return a.cb();if(!a.ea||"function"!=typeof a.ea){if("undefined"!==typeof r.Map
78. "keys").call(a));if(!("undefined"!==typeof Set
79. "!==a.H.toString()
80. ""!==d[g]
81. white"!==e
82. "fill"!==a.buttonSizeMode?
83. void 0!==a.allowedPaymentMethods
84. function qd(a,b,c){if(t(nd,"includes").call(nd,zc(a)))return 1;a=td();if(2===a){if("pt"!==c
85. null!==d
86. d!==c
87. if("NOT_CURRENTLY_KNOWN"!==a.transactionInfo.totalPriceStatus
88. if(!b.transactionReferenceId)return"transactionReferenceId in allowedPaymentMethod parameters must be set!"}else return"referenceUrl in allowedPaymentMethod parameters must be set!";else return"payeeName in allowedPaymentMethod parameters must be set!";else return"payeeVpa in allowedPaymentMethod parameters must be set!";if("INR"!==a.transactionInfo.currencyCode)return"currencyCode in transactionInfo must be set to INR!";if("FINAL"!==a.transactionInfo.totalPriceStatus)return"totalPriceStatus in transactionInfo must be set to FINAL!";
89. !b)return"paymentDataCallbacks must be set";if(t(a.callbackIntents,"includes").call(a.callbackIntents,"PAYMENT_AUTHORIZATION")!==!!b.onPaymentAuthorized)return"Both PAYMENT_AUTHORIZATION intent and onPaymentAuthorized must be set";var c=Md.slice();google.payments.api.EnableOfferCallback&&c.push("OFFER");google.payments.api.EnablePaymentMethodCallback&&c.push("PAYMENT_METHOD");return!!c.filter(function(d){return t(a.callbackIntents,"includes").call(a.callbackIntents,
90. d)}).length!==!!b.onPaymentDataChanged?"onPaymentDataChanged callback must be set if any of "+(c+" callback intent is set."):null}function Od(a){return a.allowedPaymentMethods
91. void 0!==a.style[d]
92. this.currentTarget=b;if(b=a.relatedTarget){if(Dc){a:{try{Bc(b.nodeName);var e=!0;break a}catch(g){}e=!1}e||(b=null)}}else"mouseover"==c?b=a.fromElement:"mouseout"==c&&(b=a.toElement);this.relatedTarget=b;d?(this.clientX=void 0!==d.clientX?d.clientX:d.pageX
93. this.clientY=void 0!==d.clientY?d.clientY:d.pageY
94. void 0!==a.offsetX?a.offsetX:a.layerX
95. void 0!==a.offsetY?a.offsetY:a.layerY
96. this.clientX=void 0!==a.clientX?
97. this.clientY=void 0!==a.clientY?a.clientY:a.pageY
98. return-1<e?a[e]:null};ke.prototype.hasListener=function(a,b){var c=void 0!==a
99. e=void 0!==b
100. e):a&&(a=ve(a))&&(b=a.eb(b,c,d,e))&&Ae(b)},Ae=function(a){if("number"!==typeof a
101. e=!1!==h.call(l,d)
102. d)};S.prototype.hasListener=function(a,b){return this.J.hasListener(void 0!==a?String(a):void 0
103. void 0!==a.origin
104. void 0!==a.hb
105. f!==this.l.mode
106. var b=-1!==window.navigator.userAgent.indexOf("OPT/")
107. c=-1!==window.navigator.userAgent.indexOf("SamsungBrowser/")
108. if(-1!==window.navigator.userAgent.indexOf("OPR/")

!= (321 điều kiện duy nhất):
------------------------------------------------------------
1. if (params['locale'] != null
2. } else if (params['locale'] != null
3. if (userLang != null
4. if (_idInvoice != null
5. _idInvoice != 0) {
6. dataPassed.body != null) {
7. if (this._translate.currentLang != language) {
8. if (this._idInvoice != null
9. this._idInvoice != 0) {
10. if (this._paymentService.getInvoiceDetail() != null) {
11. dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
12. _auth != 0
13. _auth != 0"
14. _auth != '1' -->
15. _auth != '1'"
16. bnpl.code != 'homepaylater'"
17. <div *ngIf="bnpl.code == 'insta' && ((selectedBnpl != bnpl) || (d_bnpl_number == 1))"
18. <div *ngIf="bnpl.code == 'homepaylater' && ((selectedBnpl != bnpl) || (d_bnpl_number == 1))"
19. this.currentMethod != selected) {
20. this._paymentService.getState() != 'error') {
21. if (this._paymentService.getCurrentPage() != 'otp') {
22. _re.body != null) {
23. this._res_polling.links != null
24. this._res_polling.links.merchant_return != null //
25. this._util.getElementParams(url_redirect, 'vpc_TxnResponseCode') != '99') {
26. } else if (this._res_polling.merchant != null
27. this._res_polling.merchant_invoice_reference != null
28. } else if (this._res_polling.payments != null
29. this._res_polling.payments[this._res_polling.payments.length - 1].instrument.brand != 'amigo') {
30. this._res_polling.links.merchant_return != null//
31. this._res_polling.payments != null
32. if (this._res_polling.payments != null
33. t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
34. this.type.toString().length != 0) {
35. if (params['locale'] != null) {
36. if ('otp' != this._paymentService.getCurrentPage()) {
37. params['b'] != ''
38. params['b'] != undefined) {
39. if (this._res.payments != null
40. this._res.payments[this._res.payments.length - 1].instrument.brand != 'amigo') {
41. if (this._idInvoice != null) {
42. const tokenListsFilter = tokenLists.filter(item => item.id != tokenData);
43. this._res.links != null
44. this._res.links.merchant_return != null
45. if (count != 1) {
46. if (this._res.merchant != null
47. this._res.merchant_invoice_reference != null) {
48. if (this._res.merchant.address_details != null) {
49. this._res.links != null//
50. } else if (this._res.payments != null
51. this._res.payments[this._res.payments.length - 1].instrument != null
52. this._res.payments[this._res.payments.length - 1].instrument.issuer != null
53. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
54. if (this.type != 7) {
55. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
56. this._res.payments[this._res.payments.length - 1].links != null
57. this._res.payments[this._res.payments.length - 1].links.cancel != null
58. this._res.payments[this._res.payments.length - 1].links.cancel.href != null
59. this._res.payments[this._res.payments.length - 1].links.update != null
60. this._res.payments[this._res.payments.length - 1].links.update.href != null) {
61. this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
62. this._res.payments[this._res.payments.length - 1].authorization != null
63. this._res.payments[this._res.payments.length - 1].authorization.links != null) {
64. this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
65. this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
66. if (this._res.payments[this._res.payments.length - 1].authorization != null
67. this._res.payments[this._res.payments.length - 1].authorization.links != null
68. auth = paramUserName != null ? paramUserName : ''
69. if (this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
70. this._res.links.merchant_return != null //
71. if (!(strInstrument != null
72. e.type != 'ewallet') || (regex.test(strTest)
73. } else if (this._res.payments != null) {
74. if (this._res_post.return_url != null) {
75. } else if (this._res_post.links != null
76. this._res_post.links.merchant_return != null
77. this._res_post.links.merchant_return.href != null) {
78. _re.body?.state != 'canceled')
79. if (res.body != null
80. res.body.links != null
81. res.body.links.merchant_return != null
82. res.body.links.merchant_return.href != null) {
83. selectedBnpl.status != 'disabled'"
84. bnplDetail.method != 'SP'"
85. bnplDetail.method != 'PL'"
86. 'total_paid': this.data?.amount ? (item.diff_amount !=0 ? this._util.formatMoney(parseFloat(item.diff_amount) + parseFloat(this.data?.amount), 0, '.', ',') : this._util.formatMoney(parseFloat(this.data?.amount), 0, '.', ',')) :0,
87. _auth != 1"
88. _locale != 'vi'"
89. document.activeElement.id!='fullName'"
90. document.activeElement.id!='phoneNumber'"
91. if ((this.fundiinDetail.phoneNumber?.length != 10
92. this.fundiinDetail.phoneNumber?.length != 11) || !this.fundiinDetail.phoneNumber.match(/^[*]{8,9
93. (this.fundiinDetail.phoneNumber?.length != 10
94. this.fundiinDetail.phoneNumber?.length != 11) ?
95. homecreditDetail['phoneNumber'].length != 10
96. homecreditDetail['phoneNumber'].length != 11)
97. this.value = (this.listTokenBnpl.length != 0) ? this.listTokenBnpl[0]['id'] : "add";
98. if ((this.homecreditDetail['phoneNumber'].length != 10
99. this.homecreditDetail['phoneNumber'].length != 11)
100. this.listTokenBnpl = this.listTokenBnpl.filter(item => item.id != result.id);
101. if (this._res_post.authorization != null
102. this._res_post.authorization.links != null
103. if (this._res_post.links != null
104. this._res_post.links.cancel != null) {
105. this._res_post.authorization.links.approval != null
106. this._res_post.authorization.links.approval.href != null) {
107. userName = paramUserName != null ? paramUserName : ''
108. document.activeElement.id!='fullname'"
109. document.activeElement.id!='email'"
110. if ((this.bnplDetail.phoneNumber?.length != 10
111. this.bnplDetail.phoneNumber?.length != 11) || !this.bnplDetail.phoneNumber.match(/^[*]{8,9
112. (this.bnplDetail.phoneNumber?.length != 10
113. this.bnplDetail.phoneNumber?.length != 11) ?
114. if (this._res.links != null
115. this._res.links.merchant_return.href != null) {
116. if (!(_formCard.otp != null
117. if (!(_formCard.password != null
118. if (ua.indexOf('safari') != -1
119. bnpl.code != 'kbank'
120. } else if (this._b != 18) {
121. if (this.htmlDesc != null
122. if (_val.value != '') {
123. this.auth_method != null) {
124. if (this.valueDate.length != 3) {
125. if (_formCard.exp_date != null
126. if (this.cardName != null
127. this._res_post.authorization.links.approval != null) {
128. this._b != 27
129. this._b != 12
130. this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
131. this._b != 18)
132. if (this._b != 18
133. this._b != 19) {
134. this._b != 18) this.previewCardService.setDisplayCard(true)
135. this._b != 18) this.previewCardService.setDisplayCard(true) // tab 0 , thẻ atm thì hiện
136. if (this._inExpDate.length != 3) {
137. let userName = _formCard.name != null ? _formCard.name : ''
138. this._b != 3))
139. if (this._b != 68
140. this._b != 2
141. this._b != 20
142. this._b != 33
143. this._b != 39
144. this._b != 43
145. this._b != 45
146. this._b != 64
147. this._b != 67
148. this._b != 68
149. this._b != 72)
150. if (this._b != 9
151. this._b != 16
152. this._b != 17
153. this._b != 25
154. this._b != 44
155. this._b != 57
156. this._b != 59
157. this._b != 61
158. this._b != 63
159. this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
160. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75'].indexOf(this._b.toString()) != -1) {
161. this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
162. if (strInstrument.substring(0, 1) != '^'
163. strInstrument.substr(strInstrument.length - 1) != '$') {
164. if (bankid != null) {
165. this.data?.merchant.id != 'OPTEST' ? this.data?.on_off_bank : ''
166. d_card_date && (_b != 3 && _b != 19 && _b != 18)
167. _b != 18)">
168. *ngIf="(_b != 18
169. (_b != 18 && _b != 6 && _b != 2)
170. _b != 2)">
171. _b != 6 && _b != 2
172. if (this.oldBankId != this._b) this.previewCardService.setResetData(true)
173. if (this.oldBankId != 0) {
174. if (this._b != 3
175. this._b != 9
176. this._b != 18
177. this._b != 19
178. this._b != 69
179. this._b != 6
180. this._b != 57) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
181. (token || _auth==1) && _b != 16; else noBankSelected
182. if (code != this.codeSelected) {
183. event.target != elementBg) {
184. cardNo != null
185. v != null
186. this.c_csc = (!(_val.value != null
187. this._i_csc != null
188. this.requireAvs = this.isAvsCountry = country != undefined
189. if (event.origin != window.origin) return; // chỉ nhận message từ OnePay
190. if (network != 'napas'
191. 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {
192. if (appcode != null) {
193. if (_re.status != '200'
194. _re.status != '201')
195. if (message != ''
196. message != null
197. message != undefined) {
198. } else if (this.res.payments[this.res.payments.length - 1].instrument.issuer.brand != null
199. this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id != null
200. if (_re != null
201. _re.links != null
202. _re.links.merchant_return != null
203. _re.links.merchant_return.href != null) {
204. } else if (_re.body != null
205. _re.body.links != null
206. _re.body.links.merchant_return != null
207. _re.body.links.merchant_return.href != null) {
208. if (this.url_new_invoice != null) {
209. color != this.defaultColor.color5) { // tìm xem màu có trong config không
210. color != this.defaultColor.color5) {
211. if (idInvoice != null
212. idInvoice != 0)
213. idInvoice != 0) {
214. let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
215. if (this._merchantid != null
216. this._tranref != null
217. this._state != null
218. urlCancel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
219. if (paymentId != null) {
220. if (res?.status != 200
221. res?.status != 201) return;
222. _re.status != '201') {
223. latestPayment?.state != "authorization_required") {
224. if (latestPayment?.instrument?.type != "vietqr") {
225. if (tem != null) {
226. if ((tem = ua.match(/version\/(\d+)/i)) != null) {
227. if (v.length != 3) {
228. if (network != "napas") return true;
229. if (currency != "VND") return false; // napas accept VND only
230. null!=a){c=ea[b];if(null==c)return a[b];c=a[c];return void 0!==c?c:a[b]}},u=function(a,b,c){if(b)a:{var d=a.split(".");a=1===d.length;var e=d[0];e=!a&&e in r?r:p;for(var g=0;g<d.length-1;g++){var f=d[g];if(!(f in e))break a;e=e[f]}d=d[d.length-1];c=da&&"es6"===c?e[d]:null;b=b(c);null!=b&&(a?ba(r,d,{configurable:!0,writable:!0,value:b}):b!==c&&(void 0===ea[d]&&(a=1E9*Math.random()>>>0,ea[d]=
231. null!=b
232. "function"!=typeof d.prototype[a]
233. undefined"!=typeof r.Symbol
234. (l=!0,k.call(h,q))}}var h=this,l=!1;return{resolve:f(this.Ac),reject:f(this.mb)}};e.prototype.Ac=function(f){if(f===this)this.mb(new TypeError("A Promise cannot resolve to itself"));else if(f instanceof e)this.Dc(f);else{a:switch(typeof f){case "object":var h=null!=f
235. f)};e.prototype.Vb=function(f,h){if(0!=this.oa)throw Error("Cannot settle("+f+", "+h+"): Promise already settled in state"+this.oa);this.oa=f;this.W=h;2===this.oa&&this.Bc();this.kc()};e.prototype.Bc=function(){var f=this;d(function(){if(f.qc()){var h=p.console;"undefined"!==
236. f.initCustomEvent("unhandledrejection",!1,!0,f));f.promise=this;f.reason=this.W;return l(f)};e.prototype.kc=function(){if(null!=this.na){for(var f=0;f<this.na.length;++f)g.vb(this.na[f]);
237. }),l=new a([[f,2],[h,3]]);if(2!=l.get(f)
238. 3!=l.get(h))return!1;l.delete(f);l.set(h
239. "function"!=typeof a
240. "function"!=typeof Object.seal)return!1;try{var h=Object.seal({x:4}),l=new a(v([[h,"s"]]));if("s"!=l.get(h)||1!=l.size||l.get({x:4})||l.set({x:4},"t")!=l||2!=l.size)return!1;var k=t(l,"entries").call(l),q=k.next();if(q.done||q.value[0]!=h||"s"!=q.value[1])return!1;q=k.next();return q.done||4!=q.value[0].x||"t"!=q.value[1]||!k.next().done?!1:!0}catch(y){return!1}}())return a;var b=new r.WeakMap,c=function(h){this[0]=
241. x:4}),l=new a(v([[h,"s"]]));if("s"!=l.get(h)
242. 1!=l.size
243. l.set({x:4},"t")!=l
244. 2!=l.size)return!1;var k=t(l
245. q.value[0]!=h
246. "s"!=q.value[1])return!1;q=k.next();return q.done||4!=q.value[0].x||"t"!=q.value[1]||!k.next().done?!1:!0}catch(y){return!1}}())return a;var b=new r.WeakMap,c=function(h){this[0]=
247. 4!=q.value[0].x
248. "t"!=q.value[1]
249. k.head!=h[1]
250. k.next!=k.head
251. u("Array.from",function(a){return a?a:function(b,c,d){c=null!=c?c:function(h){return h
252. return"object"!=b?b:a?Array.isArray(a)?"array":b:"null"},ta=function(a){var b=sa(a)
253. null!=a
254. -1!=Function.prototype.bind.toString().indexOf("native code")?va:wa
255. null!=f
256. e[g];null!=f&&(b||(b=a),b+=(b.length>a.length?"&":"")+encodeURIComponent(d)+"="+encodeURIComponent(String(f)))}}return b};var kb=Array.prototype.indexOf?function(a,b){z(null!=a.length);return Array.prototype.indexOf.call(a
257. 1!=b.length?-1:a.indexOf(b,0)
258. -1!=a.indexOf("&")
259. -1!=a.indexOf("<")
260. -1!=a.indexOf(">")
261. -1!=a.indexOf('"')
262. -1!=a.indexOf("'")
263. -1!=a.indexOf("\x00")
264. Tb=null!=Yb?Yb:!1
265. (a=a.userAgent)?a:""}var $b,ac=x.navigator;$b=ac?ac.userAgentData||null:null;function I(a){return-1!=Zb().indexOf(a)};var bc={},J=function(a,b){if(b!==bc)throw Error("SafeHtml is not meant to be built directly")
266. void 0!=a.buttonRadius
267. null!=a.buttonRadius
268. b){if("object"!==typeof b||null===b)return a;for(var c in b)b.hasOwnProperty(c)&&void 0!==b[c]&&(null==b[c]?a[c]=null:null==a[c]||"object"!==typeof a[c]||"object"!==typeof b[c]||Array.isArray(b[c])||Array.isArray(a[c])?a[c]=b[c]:yc(a[c],b[c]));return a}function zc(a){var b=0,c;if(0==a.length)return b;for(c=0;c<a.length;c++){var d=a.charCodeAt(c);b=(b<<5)-b+d;b&=b}return b}function Ac(a){console.error("DEVELOPER_ERROR in "+a.aa+": "+a.errorMessage)};var Bc=function(a){Bc[" "](a);return a};Bc[" "]=function(){};var Cc=Tb&&$b&&0<$b.brands.length?!1:I("Trident")||I("MSIE"),Dc=I("Gecko")&&!(-1!=Zb().toLowerCase().indexOf("webkit")
269. Ec=-1!=Zb().toLowerCase().indexOf("webkit")
270. "function"!=typeof a.ea){if("undefined"!==typeof r.Map&&a instanceof
271. null!=c
272. "/"!=c.charAt(0)
273. c?(d=a.O,O(b),b.O=d):c=null!=a.j
274. N.prototype.resolve=function(a){var b=this.clone(),c=!!a.X;c?Sc(b,a.X):c=!!a.ha;if(c){var d=a.ha;O(b);b.ha=d}else c=!!a.O;c?(d=a.O,O(b),b.O=d):c=null!=a.j;d=a.M;if(c)Tc(b,a.j);else if(c=!!a.M){if("/"!=d.charAt(0))if(this.O
275. -1!=e
276. -1!=e.indexOf("/.")){d=0==e.lastIndexOf("/",0);e=e.split("/");for(var g=[],f=0;f<e.length;){var h=e[f++];"."==h?d&&f==e.length&&g.push(""):".."==
277. ""!=g[0])&&g.pop(),d&&f==e.length&&g.push("")):(g.push(h),d=!0)}d=g.join("/")}else d=e}c?(O(b),b.M=d):c=""!==a.H.toString();c?Uc(b,a.H.clone()):c=!!a.L;c&&(a=a.L,O(b),b.L=a);return b};N.prototype.clone=function(){return new N(this)};
278. P.prototype.pb=function(a){a&&!this.F&&(Q(this),this.D=null,this.h.forEach(function(b,c){var d=c.toLowerCase();if(c!=d
279. "short"!=h
280. "plain"!=h
281. c=null!=(e=C.buy[d])?e:c
282. c=null!=(g=C.book[d])?g:c
283. c=null!=(f=C.checkout[d])?f:c
284. c=null!=(h=C.donate[d])?h:c
285. c=null!=(l=C.order[d])?l:c
286. c=null!=(k=C.pay[d])?k:c
287. c=null!=(q=C.subscribe[d])?q:c
288. void 0!=b.buttonRadius
289. null!=b.buttonRadius
290. function rd(a,b){var c=null!=navigator.language?navigator.language.substring(0,5):"en"
291. m.isReadyToPay=function(a){var b=Kd(a);return new r.Promise(function(c){(void 0!=b.hasEnrolledInstrument?b.hasEnrolledInstrument():b.canMakePayment()).then(function(d){window.sessionStorage.setItem("google.payments.api.storage.isreadytopay.result",d.toString());var e={result:d};2<=a.apiVersion&&a.existingPaymentMethodRequired&&(e.paymentMethodPresent=d);c(e)
292. e),b.Ja=c,a.push(b));return b};ke.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.v))return!1;var e=this.v[a];b=le(e,b,c,d);return-1<b?(je(e[b]),z(null!=e.length),Array.prototype.splice.call(e
293. b),g;if(g=0<=e)z(null!=d.length),Array.prototype.splice.call(d
294. g[f].type!=d
295. g[f].capture!=b))return!0;return!1})};
296. for(var a in this.U){var b=this.U[a];b.port1&&Le(b.port1);b.port2&&Le(b.port2)}this.U=null}};T.prototype.isConnected=function(){return null!=this.C};
297. "_blank"!=h
298. "_top"!=h
299. "_top"!=A(d)
300. -1!=e){var l=0<e?f.substring(e-1,e):"";if(""==l||"?"==l||"#"==l||"&"==l){var k=f.indexOf("&",e+1);-1==k&&(k=f.length);f=f.substring(0,e)+f.substring(k+1)}else e++}while(-1!=e
301. $e.prototype.xa=function(a,b){var c=this.nb[a];c||(c=[],this.nb[a]=c);c.push(b);c=this.ob[a];if(!c&&this.L){try{var d=this.g,e=Ge(this.L).__WA_RES__;if(e){var g=JSON.parse(e);if(g&&g.requestId==a){var f=d.location.hash;if(f){var h=encodeURIComponent("__WA_RES__")+"=";e=-1;do if(e=f.indexOf(h,e),-1!=e){var l=0<e?f.substring(e-1,e):"";if(""==l||"?"==l||"#"==l||"&"==l){var k=f.indexOf("&",e+1);-1==k&&(k=f.length);f=f.substring(0,e)+f.substring(k+1)}else e++}while(-1!=e&&e<f.length)}var q=f;q=q||"";if(q!=
302. U.prototype.rc=function(a){var b=this;Id(this.Fb);this.ba(a.Ga().then(function(c){if("TIN"!=b.s
303. c.origin!=ff(b))throw Error("channel mismatch");var d=c.data;if(d.redirectEncryptedCallbackData)return L=3,gf(b
304. a.i):b};var sf="actions.google.com amp-actions.sandbox.google.com amp-actions-staging.sandbox.google.com amp-actions-autopush.sandbox.google.com payments.developers.google.com payments.google.com".split(" "),X=function(a,b,c,d){this.gb=b;Rd(a);this.Jb=null;this.s=a.environment||"TEST";tf||(tf=-1!=sf.indexOf(window.location.hostname)
305. window.addEventListener("message",function(e){-1!=sf.indexOf(window.location.hostname)
306. void 0!=a.buttonVariant
307. null!=window.navigator.userAgent.match(/Android|iPhone|iPad|iPod|BlackBerry|IEMobile/i)?(this.o.push(37),a=!1):(a=window.navigator.userAgent.match(/Chrome\/([0-9]+)\./i),"PaymentRequest"in window&&null!=a&&70<=Number(a[1])&&"Google Inc."==window.navigator.vendor?(this.o.push(98),this.Za.paymentDataCallbacks&&this.o.push(97),a=!0):(this.o.push(36),a=!1))
308. if(-1!==window.navigator.userAgent.indexOf("OPR/")||b||c)return a.o.push(35),!1;if(a.qa)return!0;if(google.payments.api.DisablePaymentRequest&&!google.payments.api.EnablePaymentRequest)return a.o.push(3),!1;b=window.navigator.userAgent.match(/Android/i);c=window.navigator.userAgent.match(/Chrome\/([0-9]+)\./i);if(!(null!=b
309. Ef);else for(var Ff in Ef)if("prototype"!=Ff)if(Object.defineProperties){var Gf=Object.getOwnPropertyDescriptor(Ef,Ff);Gf&&Object.defineProperty(Y,Ff,Gf)}else Y[Ff]=Ef[Ff];Y.pa=Ef.prototype;var Z=function(a
310. if (_modules[r][6] != null) {
311. if (_modules[6][c] != null) {
312. if (_modules[row][col] != null) {
313. while (buffer.getLengthInBits() % 8 != 0) {
314. if (count != numChars) {
315. throw count + ' != ' + numChars
316. while (data != 0) {
317. if (test.length != 2
318. ( (test[0] << 8) | test[1]) != code) {
319. if (_length % 3 != 0) {
320. if ( (data >>> length) != 0) {
321. return typeof _map[key] != 'undefined'

