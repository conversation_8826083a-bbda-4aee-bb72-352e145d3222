====================================================================================================
TRÍCH XUẤT ĐIỀU KIỆN SO SÁNH TỪ CÁC FILE TYPESCRIPT/JAVASCRIPT/HTML
====================================================================================================
Thư mục/File nguồn: /root/projects/onepay/paygate-megags/src
Thời gian: 18:12:30 18/8/2025
Tổng số file xử lý: 200
Tổng số file bị bỏ qua: 48
Tổng số điều kiện tìm thấy: 3516

THỐNG KÊ TỔNG QUAN THEO LOẠI TOÁN TỬ:
--------------------------------------------------
Strict equality (===): 1897 lần
Loose equality (==): 676 lần
Strict inequality (!==): 673 lần
Loose inequality (!=): 270 lần
--------------------------------------------------

DANH SÁCH FILE ĐÃ XỬ LÝ:
--------------------------------------------------
1. 4en.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/4en.html
2. 4vn.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/4vn.html
3. app-routing.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/app-routing.module.ts
4. app.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/app.component.html
5. app.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/app.component.spec.ts
6. app.component.ts (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/app.component.ts
7. app.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/app.module.ts
8. counter.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/counter.directive.spec.ts
9. counter.directive.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/counter.directive.ts
10. format-carno-input.derective.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/directives/format-carno-input.derective.ts
11. uppercase-input.directive.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/directives/uppercase-input.directive.ts
12. error.component.html (7 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/error/error.component.html
13. error.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/error/error.component.spec.ts
14. error.component.ts (17 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/error/error.component.ts
15. support-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/error/support-dialog/support-dialog.html
16. support-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/error/support-dialog/support-dialog.ts
17. format-date.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/format-date.directive.spec.ts
18. format-date.directive.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/format-date.directive.ts
19. mail.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/mail/mail.component.html
20. mail.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/mail/mail.component.ts
21. main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/main.component.html
22. main.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/main.component.spec.ts
23. main.component.ts (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/main.component.ts
24. account-main.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/account-main/account-main.component.html
25. account-main.component.ts (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/account-main/account-main.component.ts
26. bank-amount-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
27. bank-amount-dialog.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
28. cancel-dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/cancel-dialog-guide-dialog.html
29. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/confirm-dialog/dialog-guide-dialog.html
30. policy-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.html
31. policy-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.ts
32. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/dialog-guide-dialog.html
33. bankaccount.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
34. bankaccount.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
35. bankaccount.component.ts (151 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
36. bank.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/model/bank.ts
37. otp-auth.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
38. otp-auth.component.ts (18 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
39. shb.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/shb/shb.component.html
40. shb.component.ts (65 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/shb/shb.component.ts
41. techcombank.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
42. techcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
43. techcombank.component.ts (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
44. vibbank.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.html
45. vibbank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.spec.ts
46. vibbank.component.ts (97 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.ts
47. vietcombank.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
48. vietcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
49. vietcombank.component.ts (91 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
50. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
51. domescard-main.component.html (69 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/domescard-main.component.html
52. domescard-main.component.ts (55 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/domescard-main.component.ts
53. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/intercard-main/dialog-guide-dialog.html
54. intercard-main.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/intercard-main/intercard-main.component.html
55. intercard-main.component.ts (59 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/intercard-main/intercard-main.component.ts
56. menu.component.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/menu.component.html
57. menu.component.ts (158 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/menu.component.ts
58. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
59. qr-dialog.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
60. qr-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
61. qr-main.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main/qr-main.component.html
62. qr-main.component.ts (25 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main/qr-main.component.ts
63. safe-html.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main/safe-html.pipe.ts
64. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/dialog/dialog-guide-dialog.html
65. qr-desktop.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.html
66. qr-desktop.component.ts (19 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.ts
67. qr-dialog.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.html
68. qr-dialog.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.ts
69. qr-guide-dialog.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.html
70. qr-guide-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.ts
71. list-bank-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.html
72. list-bank-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.ts
73. qr-main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-main.component.html
74. qr-main.component.ts (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-main.component.ts
75. qr-mobile.component.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.html
76. qr-mobile.component.ts (30 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.ts
77. safe-html.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/safe-html.pipe.ts
78. queuing.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/queuing/queuing.component.html
79. queuing.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/queuing/queuing.component.ts
80. domescard-form.component.html (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.html
81. domescard-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.ts
82. intercard-form.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.html
83. intercard-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.ts
84. deeplink.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/megags-qr/deeplink/deeplink.component.html
85. deeplink.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/megags-qr/deeplink/deeplink.component.ts
86. megags-qr.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/megags-qr/megags-qr.component.html
87. megags-qr.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/megags-qr/megags-qr.component.ts
88. qr.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/megags-qr/qr/qr.component.html
89. qr.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/megags-qr/qr/qr.component.ts
90. paypal-form.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.html
91. paypal-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.ts
92. qr-form.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/qr-form/qr-form.component.html
93. qr-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/qr-form/qr-form.component.ts
94. remove-token-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/token-main/remove-token-dialog/remove-token-dialog.html
95. dialog-guide-dialog.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/token-main/token-dialog/dialog-guide-dialog.html
96. token-main.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/token-main/token-main.component.html
97. token-main.component.ts (67 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/token-main/token-main.component.ts
98. overlay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/overlay/overlay.component.html
99. overlay.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/overlay/overlay.component.spec.ts
100. overlay.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/overlay/overlay.component.ts
101. bank-amount.pipe.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/pipe/bank-amount.pipe.ts
102. account-main.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/services/account-main.service.ts
103. close-dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/services/close-dialog.service.ts
104. data.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/services/data.service.ts
105. deep_link.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/services/deep_link.service.ts
106. dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/services/dialog.service.ts
107. fee.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/services/fee.service.ts
108. multiple_method.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/services/multiple_method.service.ts
109. payment.service.ts (13 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/services/payment.service.ts
110. qr.service.ts (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/services/qr.service.ts
111. time-stop.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/services/time-stop.service.ts
112. token-main.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/services/token-main.service.ts
113. index.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/translate/index.ts
114. lang-en.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/translate/lang-en.ts
115. lang-vi.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/translate/lang-vi.ts
116. translate.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/translate/translate.pipe.ts
117. translate.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/translate/translate.service.ts
118. translations.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/translate/translations.ts
119. apps-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/util/apps-info.ts
120. apps-information.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/util/apps-information.ts
121. banks-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/util/banks-info.ts
122. error-handler.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/util/error-handler.ts
123. util.ts (39 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/app/util/util.ts
124. qrcode.js (67 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/assets/script/qrcode.js
125. environment.prod.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/environments/environment.prod.ts
126. environment.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/environments/environment.ts
127. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/acc_bidv/index.html
128. script.js (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/acc_bidv/script.js
129. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/acc_oceanbank/index.html
130. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/acc_oceanbank/script.js
131. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/acc_pvcombank/index.html
132. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/acc_pvcombank/script.js
133. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/acc_shb/index.html
134. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/acc_shb/script.js
135. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/acc_tpbank/index.html
136. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/acc_tpbank/script.js
137. common.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/common.js
138. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/domestic_card/index.html
139. script.js (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/domestic_card/script.js
140. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/domestic_token/index.html
141. script.js (25 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/domestic_token/script.js
142. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/index.html
143. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/international_card/index.html
144. script.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/international_card/script.js
145. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/international_token/index.html
146. script.js (36 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/international_token/script.js
147. script.js (54 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/script.js
148. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.js
149. bidv.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Bidv/assets/js/bidv.js
150. bidv2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Bidv/bidv2.js
151. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Bidv/index.html
152. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.js
153. ocean.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Ocean/assets/js/ocean.js
154. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Ocean/index.html
155. ocean2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Ocean/ocean2.js
156. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
157. pvbank.js (23 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Pv_Bank/assets/js/pvbank.js
158. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Pv_Bank/index.html
159. pvbank2.js (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Pv_Bank/pvbank2.js
160. Sea2.js (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/SeaBank/Sea2.js
161. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
162. Sea.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/SeaBank/assets/js/Sea.js
163. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/SeaBank/index.html
164. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.js
165. shb.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Shb/assets/js/shb.js
166. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Shb/index.html
167. shb2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Shb/shb2.js
168. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
169. tpbank.js (23 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Tp_Bank/assets/js/tpbank.js
170. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Tp_Bank/index.html
171. tpbank2.js (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Tp_Bank/tpbank2.js
172. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.js
173. onepay.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Visa/assets/js/onepay.js
174. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Visa/index.html
175. index.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Visa/index.js
176. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
177. vpbank.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Vp_Bank/assets/js/vpbank.js
178. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Vp_Bank/index.html
179. vpbank2.js (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Vp_Bank/vpbank2.js
180. sha.js (49 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/assets/js/sha.js
181. sha256.js (28 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/assets/js/sha256.js
182. slick.js (182 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/assets/libraries/slick/slick.js
183. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.js
184. atm_b1.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_1/assets/js/atm_b1.js
185. atm_b1_2.js (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_1/atm_b1_2.js
186. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_1/index.html
187. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.js
188. atm_b2.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_2/assets/js/atm_b2.js
189. atm_b2_2.js (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_2/atm_b2_2.js
190. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_2/index.html
191. common.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/common.js
192. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.js
193. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/international_card/index.html
194. script.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/international_card/script.js
195. index.html (35 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/index.html
196. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/index.html
197. karma.conf.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/karma.conf.js
198. main.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/main.ts
199. polyfills.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/polyfills.ts
200. test.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-megags/src/test.ts

DANH SÁCH FILE BỊ BỎ QUA:
--------------------------------------------------
1. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/bootstrap.min.js
2. jquery-3.0.0.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/jquery-3.0.0.min.js
3. popper.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/popper.min.js
4. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
5. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
6. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
7. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Bidv/assets/js/jquery-3.4.1.min.js
8. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
9. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
10. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
11. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Ocean/assets/js/jquery-3.4.1.min.js
12. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
13. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
14. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
15. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Pv_Bank/assets/js/jquery-3.4.1.min.js
16. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
17. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
18. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
19. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/SeaBank/assets/js/jquery-3.4.1.min.js
20. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
21. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
22. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
23. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Shb/assets/js/jquery-3.4.1.min.js
24. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
25. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
26. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
27. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Tp_Bank/assets/js/jquery-3.4.1.min.js
28. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
29. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
30. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
31. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Visa/assets/js/jquery-3.4.1.min.js
32. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
33. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
34. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
35. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Vp_Bank/assets/js/jquery-3.4.1.min.js
36. instafeed.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/assets/js/instafeed.min.js
37. slick.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/assets/libraries/slick/slick.min.js
38. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
39. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
40. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
41. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_1/assets/js/jquery-3.4.1.min.js
42. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
43. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
44. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
45. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_2/assets/js/jquery-3.4.1.min.js
46. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
47. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
48. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js

====================================================================================================
CHI TIẾT TỪNG FILE:
====================================================================================================

📁 FILE 1: 4en.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/4en.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 2: 4vn.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/4vn.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 3: app-routing.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/app-routing.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 4: app.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/app.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 5: app.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/app.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 6: app.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/app.component.ts
📊 Thống kê: 8 điều kiện duy nhất
   - === : 1 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 97] return lang === this._translate.currentLang

== (4 điều kiện):
  1. [Dòng 67] 'vi' == params['locale']) {
  2. [Dòng 69] 'en' == params['locale']) {
  3. [Dòng 88] if (_re.status == '200'
  4. [Dòng 88] _re.status == '201') {

!= (3 điều kiện):
  1. [Dòng 67] if (params['locale'] != null
  2. [Dòng 69] } else if (params['locale'] != null
  3. [Dòng 76] if (userLang != null

================================================================================

📁 FILE 7: app.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/app.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 8: counter.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/counter.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 9: counter.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/counter.directive.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 10: format-carno-input.derective.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/directives/format-carno-input.derective.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 44] this.lastValue !== $event.target.value)) {

!= (1 điều kiện):
  1. [Dòng 23] if(value!=null){

================================================================================

📁 FILE 11: uppercase-input.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/directives/uppercase-input.directive.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 0 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 23] this.lastValue !== $event.target.value)) {

================================================================================

📁 FILE 12: error.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/error/error.component.html
📊 Thống kê: 7 điều kiện duy nhất
   - === : 1 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 18] isPopupSupport === 'True'"

== (6 điều kiện):
  1. [Dòng 15] errorCode == '11'"
  2. [Dòng 18] isSent == false
  3. [Dòng 41] <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
  4. [Dòng 41] (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
  5. [Dòng 43] <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
  6. [Dòng 43] !(errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending

================================================================================

📁 FILE 13: error.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/error/error.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 14: error.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/error/error.component.ts
📊 Thống kê: 17 điều kiện duy nhất
   - === : 6 lần
   - == : 11 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (6 điều kiện):
  1. [Dòng 105] params.timeout === 'true') {
  2. [Dòng 126] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  3. [Dòng 126] _re.body.state === 'unpaid');
  4. [Dòng 175] if (this.errorCode === 'overtime'
  5. [Dòng 175] this.errorCode === '253') {
  6. [Dòng 293] if (this.timeLeft === 0) {

== (11 điều kiện):
  1. [Dòng 147] _re.body.themes.theme == 'token') {
  2. [Dòng 153] params.response_code == 'overtime') {
  3. [Dòng 198] if (_re.status == '200'
  4. [Dòng 198] _re.status == '201') {
  5. [Dòng 211] if (_re2.status == '200'
  6. [Dòng 211] _re2.status == '201') {
  7. [Dòng 223] if (this.errorCode == 'overtime'
  8. [Dòng 223] this.errorCode == '253') {
  9. [Dòng 225] } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
  10. [Dòng 242] if (lastPayment?.state == 'pending') {
  11. [Dòng 291] if (this.isTimePause == false) {

================================================================================

📁 FILE 15: support-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/error/support-dialog/support-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 16: support-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/error/support-dialog/support-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 17: format-date.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/format-date.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 18: format-date.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/format-date.directive.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0
  2. [Dòng 123] YY % 4 === 0)) {
  3. [Dòng 138] if (YYYY % 400 === 0
  4. [Dòng 138] YYYY % 4 === 0)) {

!== (2 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0 || (YY % 100 !== 0
  2. [Dòng 138] if (YYYY % 400 === 0 || (YYYY % 100 !== 0

================================================================================

📁 FILE 19: mail.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/mail/mail.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 20: mail.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/mail/mail.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 21: main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 22: main.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/main.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 23: main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/main.component.ts
📊 Thống kê: 9 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 7 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 90] if ((dataPassed.status == '200'
  2. [Dòng 90] dataPassed.status == '201') && dataPassed.body != null) {

!= (7 điều kiện):
  1. [Dòng 81] if (this._idInvoice != null
  2. [Dòng 81] this._idInvoice != 0) {
  3. [Dòng 82] if (this._paymentService.getInvoiceDetail() != null) {
  4. [Dòng 90] dataPassed.body != null) {
  5. [Dòng 110] dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
  6. [Dòng 111] dataPassed.body.themes.border_color != true ? dataPassed.body.themes.border_color : ''
  7. [Dòng 165] if (this._translate.currentLang != language) {

================================================================================

📁 FILE 24: account-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/account-main/account-main.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 17] account_amain == '1'"

!= (1 điều kiện):
  1. [Dòng 73] notify != ''"

================================================================================

📁 FILE 25: account-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/account-main/account-main.component.ts
📊 Thống kê: 4 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 216] if (_re.status == '200'
  2. [Dòng 216] _re.status == '201') {

!== (1 điều kiện):
  1. [Dòng 101] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (1 điều kiện):
  1. [Dòng 82] if (params['locale'] != null) {

================================================================================

📁 FILE 26: bank-amount-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 27: bank-amount-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 39] if (this.locale == 'en') {
  2. [Dòng 53] if (name == 'MAFC')

================================================================================

📁 FILE 28: cancel-dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/cancel-dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 29: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/confirm-dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 30: policy-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 31: policy-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 32: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 33: bankaccount.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 43] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 58] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 58] valueDate.trim().length === 0)"

================================================================================

📁 FILE 34: bankaccount.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 35: bankaccount.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
📊 Thống kê: 151 điều kiện duy nhất
   - === : 48 lần
   - == : 69 lần
   - !== : 13 lần
   - != : 21 lần
--------------------------------------------------------------------------------

=== (48 điều kiện):
  1. [Dòng 121] if (target.tagName === 'A'
  2. [Dòng 147] if (isIE[0] === 'MSIE'
  3. [Dòng 147] +isIE[1] === 10) {
  4. [Dòng 235] if ((_val.value.substr(-1) === ' '
  5. [Dòng 235] _val.value.length === 24) {
  6. [Dòng 245] if (this.cardTypeBank === 'bank_card_number') {
  7. [Dòng 250] } else if (this.cardTypeBank === 'bank_account_number') {
  8. [Dòng 256] } else if (this.cardTypeBank === 'bank_username') {
  9. [Dòng 260] } else if (this.cardTypeBank === 'bank_customer_code') {
  10. [Dòng 266] this.cardTypeBank === 'bank_card_number'
  11. [Dòng 280] if (this.cardTypeOcean === 'IB') {
  12. [Dòng 284] } else if (this.cardTypeOcean === 'MB') {
  13. [Dòng 285] if (_val.value.substr(0, 2) === '84') {
  14. [Dòng 292] } else if (this.cardTypeOcean === 'ATM') {
  15. [Dòng 319] if (this.cardTypeBank === 'bank_account_number') {
  16. [Dòng 338] this.cardTypeBank === 'bank_card_number') {
  17. [Dòng 360] if (this.cardTypeBank === 'bank_card_number'
  18. [Dòng 360] if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  19. [Dòng 682] if (event.keyCode === 8
  20. [Dòng 682] event.key === "Backspace"
  21. [Dòng 722] if (v.length === 2
  22. [Dòng 722] this.flag.length === 3
  23. [Dòng 722] this.flag.charAt(this.flag.length - 1) === '/') {
  24. [Dòng 726] if (v.length === 1) {
  25. [Dòng 728] } else if (v.length === 2) {
  26. [Dòng 731] v.length === 2) {
  27. [Dòng 739] if (len === 2) {
  28. [Dòng 1016] if ((this.cardTypeBank === 'bank_account_number'
  29. [Dòng 1016] this.cardTypeBank === 'bank_username'
  30. [Dòng 1016] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  31. [Dòng 1021] this.cardTypeOcean === 'ATM')
  32. [Dòng 1022] || (this.cardTypeOcean === 'IB'
  33. [Dòng 1081] if (valIn === this._translate.instant('bank_card_number')) {
  34. [Dòng 1106] } else if (valIn === this._translate.instant('bank_account_number')) {
  35. [Dòng 1125] } else if (valIn === this._translate.instant('bank_username')) {
  36. [Dòng 1141] } else if (valIn === this._translate.instant('bank_customer_code')) {
  37. [Dòng 1229] if (_val.value === ''
  38. [Dòng 1229] _val.value === null
  39. [Dòng 1229] _val.value === undefined) {
  40. [Dòng 1238] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  41. [Dòng 1238] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  42. [Dòng 1245] this.cardTypeOcean === 'MB') {
  43. [Dòng 1253] this.cardTypeOcean === 'IB'
  44. [Dòng 1259] if ((this.cardTypeBank === 'bank_card_number'
  45. [Dòng 1291] if (this.cardName === undefined
  46. [Dòng 1291] this.cardName === '') {
  47. [Dòng 1299] if (this.valueDate === undefined
  48. [Dòng 1299] this.valueDate === '') {

== (69 điều kiện):
  1. [Dòng 164] if (this._b == 18
  2. [Dòng 164] this._b == 19) {
  3. [Dòng 167] if (this._b == 19) {//19BIDV
  4. [Dòng 175] } else if (this._b == 3
  5. [Dòng 175] this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  6. [Dòng 180] if (this._b == 27) {
  7. [Dòng 185] } else if (this._b == 12) {// 12SHB
  8. [Dòng 190] } else if (this._b == 18) { //18Oceanbank-ocb
  9. [Dòng 244] if (this._b == 19
  10. [Dòng 244] this._b == 3
  11. [Dòng 244] this._b == 27
  12. [Dòng 244] this._b == 12) {
  13. [Dòng 279] } else if (this._b == 18) {
  14. [Dòng 310] if (this.checkBin(_val.value) && (this._b == 3
  15. [Dòng 310] this._b == 27)) {
  16. [Dòng 315] if (this._b == 3) {
  17. [Dòng 327] this.cardTypeOcean == 'ATM') {
  18. [Dòng 340] if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  19. [Dòng 360] this._b == 18)) {
  20. [Dòng 436] if (this.checkBin(v) && (this._b == 3
  21. [Dòng 682] event.inputType == 'deleteContentBackward') {
  22. [Dòng 683] if (event.target.name == 'exp_date'
  23. [Dòng 691] event.inputType == 'insertCompositionText') {
  24. [Dòng 706] if (((this.valueDate.length == 4
  25. [Dòng 706] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  26. [Dòng 706] this.valueDate.length == 5)
  27. [Dòng 790] if (temp.length == 0) {
  28. [Dòng 797] return (counter % 10 == 0);
  29. [Dòng 817] } else if (this._b == 19) {
  30. [Dòng 819] } else if (this._b == 27) {
  31. [Dòng 824] if (this._b == 12) {
  32. [Dòng 826] if (this.cardTypeBank == 'bank_customer_code') {
  33. [Dòng 828] } else if (this.cardTypeBank == 'bank_account_number') {
  34. [Dòng 845] _formCard.exp_date.length == 5
  35. [Dòng 845] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
  36. [Dòng 845] this._b == 3)) {//27-pvcombank;3-TPB ;
  37. [Dòng 850] if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
  38. [Dòng 850] this._b == 19
  39. [Dòng 850] this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
  40. [Dòng 853] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  41. [Dòng 856] if (this.cardTypeOcean == 'IB') {
  42. [Dòng 858] } else if (this.cardTypeOcean == 'MB') {
  43. [Dòng 860] } else if (this.cardTypeOcean == 'ATM') {
  44. [Dòng 909] if (_re.status == '200'
  45. [Dòng 909] _re.status == '201') {
  46. [Dòng 914] if (this._res_post.state == 'approved'
  47. [Dòng 914] this._res_post.state == 'failed') {
  48. [Dòng 921] } else if (this._res_post.state == 'authorization_required') {
  49. [Dòng 939] if (this._b == 18) {
  50. [Dòng 944] if (this._b == 27
  51. [Dòng 944] this._b == 18) {
  52. [Dòng 1036] if ((cardNo.length == 16
  53. [Dòng 1036] if ((cardNo.length == 16 || (cardNo.length == 19
  54. [Dòng 1037] && ((this._b == 18
  55. [Dòng 1037] cardNo.length == 19) || this._b != 18)
  56. [Dòng 1050] if (this._b == +e.id) {
  57. [Dòng 1066] if (valIn == 1) {
  58. [Dòng 1068] } else if (valIn == 2) {
  59. [Dòng 1092] this._b == 3) {
  60. [Dòng 1099] if (this._b == 19) {
  61. [Dòng 1162] if (cardType == this._translate.instant('internetbanking')
  62. [Dòng 1170] } else if (cardType == this._translate.instant('mobilebanking')
  63. [Dòng 1178] } else if (cardType == this._translate.instant('atm')
  64. [Dòng 1238] this._b == 18))) {
  65. [Dòng 1245] } else if (this._b == 18
  66. [Dòng 1270] this.c_expdate = !(((this.valueDate.length == 4
  67. [Dòng 1302] this.valueDate.length == 4
  68. [Dòng 1302] this.valueDate.search('/') == -1)
  69. [Dòng 1303] this.valueDate.length == 5))

!== (13 điều kiện):
  1. [Dòng 235] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 895] key !== '3') {
  3. [Dòng 945] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 963] codeResponse.toString() !== '0') {
  5. [Dòng 1016] cardNo.length !== 0) {
  6. [Dòng 1088] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 1109] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 1130] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 1150] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 1162] this.lb_card_account !== this._translate.instant('ocb_account')) {
  11. [Dòng 1170] this.lb_card_account !== this._translate.instant('ocb_phone')) {
  12. [Dòng 1178] this.lb_card_account !== this._translate.instant('ocb_card')) {
  13. [Dòng 1259] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (21 điều kiện):
  1. [Dòng 196] } else if (this._b != 18) {
  2. [Dòng 202] if (this.htmlDesc != null
  3. [Dòng 232] if (ua.indexOf('safari') != -1
  4. [Dòng 242] if (_val.value != '') {
  5. [Dòng 328] this.auth_method != null) {
  6. [Dòng 684] if (this.valueDate.length != 3) {
  7. [Dòng 845] if (_formCard.exp_date != null
  8. [Dòng 850] if (this.cardName != null
  9. [Dòng 917] if (this._res_post.links != null
  10. [Dòng 917] this._res_post.links.merchant_return != null
  11. [Dòng 917] this._res_post.links.merchant_return.href != null) {
  12. [Dòng 925] if (this._res_post.authorization != null
  13. [Dòng 925] this._res_post.authorization.links != null
  14. [Dòng 925] this._res_post.authorization.links.approval != null) {
  15. [Dòng 932] this._res_post.links.cancel != null) {
  16. [Dòng 1036] this._b != 27
  17. [Dòng 1036] this._b != 12
  18. [Dòng 1036] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  19. [Dòng 1037] this._b != 18)
  20. [Dòng 1083] if (this._b != 18
  21. [Dòng 1083] this._b != 19) {

================================================================================

📁 FILE 36: bank.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/model/bank.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 37: otp-auth.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 38: otp-auth.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
📊 Thống kê: 18 điều kiện duy nhất
   - === : 0 lần
   - == : 11 lần
   - !== : 1 lần
   - != : 6 lần
--------------------------------------------------------------------------------

== (11 điều kiện):
  1. [Dòng 99] if (this._b == 8) {//MB Bank
  2. [Dòng 103] if (this._b == 18) {//Oceanbank
  3. [Dòng 140] if (this._b == 8) {
  4. [Dòng 145] if (this._b == 18) {
  5. [Dòng 150] if (this._b == 12) { //SHB
  6. [Dòng 172] if (_re.status == '200'
  7. [Dòng 172] _re.status == '201') {
  8. [Dòng 181] } else if (this._res.state == 'authorization_required') {
  9. [Dòng 213] if (this.challengeCode == '') {
  10. [Dòng 308] if (this._b == 12) {
  11. [Dòng 356] if (this._b == 18) {//8-MB Bank;18-oceanbank

!== (1 điều kiện):
  1. [Dòng 187] codeResponse.toString() !== '0') {

!= (6 điều kiện):
  1. [Dòng 177] if (this._res.links != null
  2. [Dòng 177] this._res.links.merchant_return != null
  3. [Dòng 177] this._res.links.merchant_return.href != null) {
  4. [Dòng 351] if (!(_formCard.otp != null
  5. [Dòng 357] if (!(_formCard.password != null
  6. [Dòng 373] if (ua.indexOf('safari') != -1

================================================================================

📁 FILE 39: shb.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/shb/shb.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 25] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 42] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  3. [Dòng 42] _inExpDate.trim().length === 0)"

== (2 điều kiện):
  1. [Dòng 22] <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_card_number')">
  2. [Dòng 66] <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_account_number')">

================================================================================

📁 FILE 40: shb.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/shb/shb.component.ts
📊 Thống kê: 65 điều kiện duy nhất
   - === : 18 lần
   - == : 31 lần
   - !== : 5 lần
   - != : 11 lần
--------------------------------------------------------------------------------

=== (18 điều kiện):
  1. [Dòng 95] if (target.tagName === 'A'
  2. [Dòng 121] if (isIE[0] === 'MSIE'
  3. [Dòng 121] +isIE[1] === 10) {
  4. [Dòng 163] if (focusElement === 'card_name') {
  5. [Dòng 165] } else if (focusElement === 'exp_date'
  6. [Dòng 186] focusExpDateElement === 'card_name') {
  7. [Dòng 399] if (this.cardTypeBank === 'bank_account_number'
  8. [Dòng 444] if (valIn === this._translate.instant('bank_card_number')) {
  9. [Dòng 450] } else if (valIn === this._translate.instant('bank_account_number')) {
  10. [Dòng 492] if (_val.value === ''
  11. [Dòng 492] _val.value === null
  12. [Dòng 492] _val.value === undefined) {
  13. [Dòng 503] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  14. [Dòng 503] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  15. [Dòng 510] this.cardTypeOcean === 'MB') {
  16. [Dòng 518] this.cardTypeOcean === 'IB'
  17. [Dòng 524] if ((this.cardTypeBank === 'bank_card_number'
  18. [Dòng 547] if (this.cardTypeOcean === 'IB') {

== (31 điều kiện):
  1. [Dòng 130] if(this._b == 12) this.isShbGroup = true;
  2. [Dòng 151] return this._b == 9
  3. [Dòng 151] this._b == 11
  4. [Dòng 151] this._b == 16
  5. [Dòng 151] this._b == 17
  6. [Dòng 151] this._b == 25
  7. [Dòng 151] this._b == 44
  8. [Dòng 152] this._b == 57
  9. [Dòng 152] this._b == 59
  10. [Dòng 152] this._b == 61
  11. [Dòng 152] this._b == 63
  12. [Dòng 152] this._b == 69
  13. [Dòng 238] if (this._b == 12
  14. [Dòng 238] this.cardTypeBank == 'bank_account_number') {
  15. [Dòng 249] _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
  16. [Dòng 295] if (_re.status == '200'
  17. [Dòng 295] _re.status == '201') {
  18. [Dòng 299] if (this._res_post.state == 'approved'
  19. [Dòng 299] this._res_post.state == 'failed') {
  20. [Dòng 305] } else if (this._res_post.state == 'authorization_required') {
  21. [Dòng 402] if ((cardNo.length == 16
  22. [Dòng 402] cardNo.length == 19) && cardNo.startsWith("970443") && this._util.checkMod10(cardNo) && this.checkBin(cardNo) ) {
  23. [Dòng 414] if (this._b == +e.id) {
  24. [Dòng 430] if (valIn == 1) {
  25. [Dòng 432] } else if (valIn == 2) {
  26. [Dòng 503] this._b == 18))) {
  27. [Dòng 510] } else if (this._b == 18
  28. [Dòng 524] this._b == 18)) {
  29. [Dòng 536] this.c_expdate = !(((this.valueDate.length == 4
  30. [Dòng 536] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  31. [Dòng 536] this.valueDate.length == 5)

!== (5 điều kiện):
  1. [Dòng 283] key !== '3') {
  2. [Dòng 325] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  3. [Dòng 343] codeResponse.toString() !== '0') {
  4. [Dòng 399] cardNo.length !== 0) {
  5. [Dòng 524] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (11 điều kiện):
  1. [Dòng 138] if (this.htmlDesc != null
  2. [Dòng 199] if (ua.indexOf('safari') != -1
  3. [Dòng 249] if (_formCard.exp_date != null
  4. [Dòng 254] if (this.cardName != null
  5. [Dòng 302] if (this._res_post.links != null
  6. [Dòng 302] this._res_post.links.merchant_return != null
  7. [Dòng 302] this._res_post.links.merchant_return.href != null) {
  8. [Dòng 308] if (this._res_post.authorization != null
  9. [Dòng 308] this._res_post.authorization.links != null
  10. [Dòng 308] this._res_post.authorization.links.approval != null) {
  11. [Dòng 315] this._res_post.links.cancel != null) {

================================================================================

📁 FILE 41: techcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 42: techcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 43: techcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
📊 Thống kê: 14 điều kiện duy nhất
   - === : 1 lần
   - == : 10 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 73] if (this.timeLeft === 0) {

== (10 điều kiện):
  1. [Dòng 64] if (this._b == 2
  2. [Dòng 64] this._b == 31) {
  3. [Dòng 103] if (this._b == 2) {
  4. [Dòng 105] } else if (this._b == 6) {
  5. [Dòng 107] } else if (this._b == 31) {
  6. [Dòng 137] if (_re.status == '200'
  7. [Dòng 137] _re.status == '201') {
  8. [Dòng 142] if (this._res_post.state == 'approved'
  9. [Dòng 142] this._res_post.state == 'failed') {
  10. [Dòng 146] } else if (this._res_post.state == 'authorization_required') {

!= (3 điều kiện):
  1. [Dòng 150] if (this._res_post.authorization != null
  2. [Dòng 150] this._res_post.authorization.links != null
  3. [Dòng 150] this._res_post.authorization.links.approval != null) {

================================================================================

📁 FILE 44: vibbank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 28] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 42] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 42] valueDate.trim().length === 0)"

================================================================================

📁 FILE 45: vibbank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 46: vibbank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.ts
📊 Thống kê: 97 điều kiện duy nhất
   - === : 37 lần
   - == : 33 lần
   - !== : 10 lần
   - != : 17 lần
--------------------------------------------------------------------------------

=== (37 điều kiện):
  1. [Dòng 108] if (target.tagName === 'A'
  2. [Dòng 135] if (isIE[0] === 'MSIE'
  3. [Dòng 135] +isIE[1] === 10) {
  4. [Dòng 166] if (this.timeLeft === 0) {
  5. [Dòng 209] if ((_val.value.substr(-1) === ' '
  6. [Dòng 209] _val.value.length === 24) {
  7. [Dòng 219] if (this.cardTypeBank === 'bank_card_number') {
  8. [Dòng 224] } else if (this.cardTypeBank === 'bank_account_number') {
  9. [Dòng 230] } else if (this.cardTypeBank === 'bank_username') {
  10. [Dòng 234] } else if (this.cardTypeBank === 'bank_customer_code') {
  11. [Dòng 255] if (this.cardTypeBank === 'bank_account_number') {
  12. [Dòng 266] this.cardTypeBank === 'bank_card_number') {
  13. [Dòng 503] if (event.keyCode === 8
  14. [Dòng 503] event.key === "Backspace"
  15. [Dòng 543] if (v.length === 2
  16. [Dòng 543] this.flag.length === 3
  17. [Dòng 543] this.flag.charAt(this.flag.length - 1) === '/') {
  18. [Dòng 547] if (v.length === 1) {
  19. [Dòng 549] } else if (v.length === 2) {
  20. [Dòng 552] v.length === 2) {
  21. [Dòng 560] if (len === 2) {
  22. [Dòng 800] if ((this.cardTypeBank === 'bank_account_number'
  23. [Dòng 800] this.cardTypeBank === 'bank_username'
  24. [Dòng 800] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  25. [Dòng 850] if (valIn === this._translate.instant('bank_card_number')) {
  26. [Dòng 869] } else if (valIn === this._translate.instant('bank_account_number')) {
  27. [Dòng 881] } else if (valIn === this._translate.instant('bank_username')) {
  28. [Dòng 892] } else if (valIn === this._translate.instant('bank_customer_code')) {
  29. [Dòng 949] if (_val.value === ''
  30. [Dòng 949] _val.value === null
  31. [Dòng 949] _val.value === undefined) {
  32. [Dòng 960] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  33. [Dòng 970] if ((this.cardTypeBank === 'bank_card_number'
  34. [Dòng 1002] if (this.cardName === undefined
  35. [Dòng 1002] this.cardName === '') {
  36. [Dòng 1010] if (this.valueDate === undefined
  37. [Dòng 1010] this.valueDate === '') {

== (33 điều kiện):
  1. [Dòng 149] if (this._b == 5) {//5-vib;
  2. [Dòng 218] if (this._b == 5) {
  3. [Dòng 252] if (this.checkBin(_val.value) && (this._b == 5)) {
  4. [Dòng 268] if (this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  5. [Dòng 330] if (this.checkBin(v) && (this._b == 5)) {
  6. [Dòng 503] event.inputType == 'deleteContentBackward') {
  7. [Dòng 504] if (event.target.name == 'exp_date'
  8. [Dòng 512] event.inputType == 'insertCompositionText') {
  9. [Dòng 527] if (((this.valueDate.length == 4
  10. [Dòng 527] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  11. [Dòng 527] this.valueDate.length == 5)
  12. [Dòng 611] if (temp.length == 0) {
  13. [Dòng 618] return (counter % 10 == 0);
  14. [Dòng 649] _formCard.exp_date.length == 5
  15. [Dòng 649] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 5)) {//27-pvcombank;3-TPB ;
  16. [Dòng 654] if (this.cardName != null && this.cardName.length > 0 && (this._b == 5)) {//3-TPB ;19-BIDV;27-pvcombank;
  17. [Dòng 698] if (_re.status == '200'
  18. [Dòng 698] _re.status == '201') {
  19. [Dòng 703] if (this._res_post.state == 'approved'
  20. [Dòng 703] this._res_post.state == 'failed') {
  21. [Dòng 710] } else if (this._res_post.state == 'authorization_required') {
  22. [Dòng 805] if ((cardNo.length == 16
  23. [Dòng 805] if ((cardNo.length == 16 || (cardNo.length == 19
  24. [Dòng 806] && ((this._b == 18
  25. [Dòng 806] cardNo.length == 19) || this._b != 18)
  26. [Dòng 819] if (this._b == +e.id) {
  27. [Dòng 835] if (valIn == 1) {
  28. [Dòng 837] } else if (valIn == 2) {
  29. [Dòng 960] this._b == 18)) {
  30. [Dòng 982] this.c_expdate = !(((this.valueDate.length == 4
  31. [Dòng 1013] this.valueDate.length == 4
  32. [Dòng 1013] this.valueDate.search('/') == -1)
  33. [Dòng 1014] this.valueDate.length == 5))

!== (10 điều kiện):
  1. [Dòng 209] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 684] key !== '3') {
  3. [Dòng 732] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 749] codeResponse.toString() !== '0') {
  5. [Dòng 800] cardNo.length !== 0) {
  6. [Dòng 857] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 872] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 886] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 899] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 970] this._b !== 18) || (this._b == 18)) {

!= (17 điều kiện):
  1. [Dòng 176] if (this.htmlDesc != null
  2. [Dòng 206] if (ua.indexOf('safari') != -1
  3. [Dòng 216] if (_val.value != '') {
  4. [Dòng 505] if (this.valueDate.length != 3) {
  5. [Dòng 649] if (_formCard.exp_date != null
  6. [Dòng 654] if (this.cardName != null
  7. [Dòng 706] if (this._res_post.links != null
  8. [Dòng 706] this._res_post.links.merchant_return != null
  9. [Dòng 706] this._res_post.links.merchant_return.href != null) {
  10. [Dòng 714] if (this._res_post.authorization != null
  11. [Dòng 714] this._res_post.authorization.links != null
  12. [Dòng 714] this._res_post.authorization.links.approval != null) {
  13. [Dòng 721] this._res_post.links.cancel != null) {
  14. [Dòng 805] this._b != 27
  15. [Dòng 805] this._b != 12
  16. [Dòng 805] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  17. [Dòng 806] this._b != 18)

================================================================================

📁 FILE 47: vietcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 31] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  2. [Dòng 31] _inExpDate.trim().length === 0)"

== (1 điều kiện):
  1. [Dòng 92] _b==68"

================================================================================

📁 FILE 48: vietcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 49: vietcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
📊 Thống kê: 91 điều kiện duy nhất
   - === : 5 lần
   - == : 58 lần
   - !== : 2 lần
   - != : 26 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 95] if (target.tagName === 'A'
  2. [Dòng 231] if (event.keyCode === 8
  3. [Dòng 231] event.key === "Backspace"
  4. [Dòng 473] if (approval.method === 'REDIRECT') {
  5. [Dòng 476] } else if (approval.method === 'POST_REDIRECT') {

== (58 điều kiện):
  1. [Dòng 135] if (this._b == 1
  2. [Dòng 135] this._b == 20
  3. [Dòng 135] this._b == 36
  4. [Dòng 135] this._b == 64
  5. [Dòng 135] this._b == 55
  6. [Dòng 135] this._b == 47
  7. [Dòng 135] this._b == 48
  8. [Dòng 135] this._b == 59) {
  9. [Dòng 149] return this._b == 9
  10. [Dòng 149] this._b == 16
  11. [Dòng 149] this._b == 17
  12. [Dòng 149] this._b == 25
  13. [Dòng 149] this._b == 44
  14. [Dòng 150] this._b == 54
  15. [Dòng 150] this._b == 57
  16. [Dòng 150] this._b == 59
  17. [Dòng 150] this._b == 61
  18. [Dòng 150] this._b == 63
  19. [Dòng 150] this._b == 69
  20. [Dòng 158] return this._b == 11
  21. [Dòng 158] this._b == 33
  22. [Dòng 158] this._b == 39
  23. [Dòng 158] this._b == 43
  24. [Dòng 158] this._b == 45
  25. [Dòng 159] this._b == 67
  26. [Dòng 159] this._b == 72
  27. [Dòng 159] this._b == 73
  28. [Dòng 159] this._b == 68
  29. [Dòng 159] this._b == 74
  30. [Dòng 159] this._b == 75
  31. [Dòng 231] event.inputType == 'deleteContentBackward') {
  32. [Dòng 232] if (event.target.name == 'exp_date'
  33. [Dòng 240] event.inputType == 'insertCompositionText') {
  34. [Dòng 363] if (this._res_post.state == 'approved'
  35. [Dòng 363] this._res_post.state == 'failed') {
  36. [Dòng 413] } else if (this._res_post.state == 'authorization_required') {
  37. [Dòng 435] this._b == 14
  38. [Dòng 435] this._b == 15
  39. [Dòng 435] this._b == 24
  40. [Dòng 435] this._b == 8
  41. [Dòng 435] this._b == 10
  42. [Dòng 435] this._b == 22
  43. [Dòng 435] this._b == 23
  44. [Dòng 435] this._b == 30
  45. [Dòng 435] this._b == 11
  46. [Dòng 435] this._b == 9) {
  47. [Dòng 513] if ((cardNo.length == 16
  48. [Dòng 514] (cardNo.length == 19
  49. [Dòng 514] (cardNo.length == 19 && (this._b == 1
  50. [Dòng 514] this._b == 4
  51. [Dòng 514] this._b == 59))
  52. [Dòng 516] this._util.checkMod10(cardNo) == true
  53. [Dòng 552] return ((value.length == 4
  54. [Dòng 552] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  55. [Dòng 552] value.length == 5) && parseInt(value.split('/')[0]
  56. [Dòng 586] this._inExpDate.length == 4
  57. [Dòng 586] this._inExpDate.search('/') == -1)
  58. [Dòng 587] this._inExpDate.length == 5))

!== (2 điều kiện):
  1. [Dòng 376] codeResponse.toString() !== '0') {
  2. [Dòng 436] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (26 điều kiện):
  1. [Dòng 140] if (this.htmlDesc != null
  2. [Dòng 168] if (ua.indexOf('safari') != -1
  3. [Dòng 233] if (this._inExpDate.length != 3) {
  4. [Dòng 313] if (this._b != 9
  5. [Dòng 313] this._b != 16
  6. [Dòng 313] this._b != 17
  7. [Dòng 313] this._b != 25
  8. [Dòng 313] this._b != 44
  9. [Dòng 313] this._b != 54
  10. [Dòng 314] this._b != 57
  11. [Dòng 314] this._b != 59
  12. [Dòng 314] this._b != 61
  13. [Dòng 314] this._b != 63
  14. [Dòng 314] this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
  15. [Dòng 327] '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72' , '73' , '74' , '75'].indexOf(this._b.toString()) != -1) {
  16. [Dòng 365] if (this._res_post.return_url != null) {
  17. [Dòng 368] if (this._res_post.links != null
  18. [Dòng 368] this._res_post.links.merchant_return != null
  19. [Dòng 368] this._res_post.links.merchant_return.href != null) {
  20. [Dòng 418] if (this._res_post.authorization != null
  21. [Dòng 418] this._res_post.authorization.links != null
  22. [Dòng 423] this._res_post.links.cancel != null) {
  23. [Dòng 429] let userName = _formCard.name != null ? _formCard.name : ''
  24. [Dòng 430] this._res_post.authorization.links.approval != null
  25. [Dòng 430] this._res_post.authorization.links.approval.href != null) {
  26. [Dòng 433] userName = paramUserName != null ? paramUserName : ''

================================================================================

📁 FILE 50: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 51: domescard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/domescard-main.component.html
📊 Thống kê: 69 điều kiện duy nhất
   - === : 1 lần
   - == : 68 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 16] filteredData.length === 0"

== (68 điều kiện):
  1. [Dòng 25] _auth==0
  2. [Dòng 25] *ngIf="(!token&&_auth==0&&(_b==1
  3. [Dòng 25] _b==4
  4. [Dòng 25] _b==7
  5. [Dòng 25] _b==8
  6. [Dòng 25] _b==9
  7. [Dòng 25] _b==10
  8. [Dòng 25] _b==11
  9. [Dòng 25] _b==14
  10. [Dòng 25] _b==15
  11. [Dòng 25] _b==16
  12. [Dòng 25] _b==17
  13. [Dòng 25] _b==20
  14. [Dòng 26] _b==22
  15. [Dòng 26] _b==23
  16. [Dòng 26] _b==24
  17. [Dòng 26] _b==25
  18. [Dòng 26] _b==30
  19. [Dòng 26] _b==33
  20. [Dòng 26] _b==34
  21. [Dòng 26] _b==35
  22. [Dòng 26] _b==36
  23. [Dòng 26] _b==37
  24. [Dòng 26] _b==38
  25. [Dòng 26] _b==39
  26. [Dòng 26] _b==40
  27. [Dòng 26] _b==41
  28. [Dòng 27] _b==42
  29. [Dòng 27] _b==43
  30. [Dòng 27] _b==44
  31. [Dòng 27] _b==45
  32. [Dòng 27] this._b==46
  33. [Dòng 27] _b==47
  34. [Dòng 27] _b==48
  35. [Dòng 27] _b==49
  36. [Dòng 27] _b==50
  37. [Dòng 27] _b==51
  38. [Dòng 27] _b==52
  39. [Dòng 27] _b==53
  40. [Dòng 27] _b==54
  41. [Dòng 27] _b==55
  42. [Dòng 27] _b==56
  43. [Dòng 27] _b==57
  44. [Dòng 27] _b==58
  45. [Dòng 27] _b==59
  46. [Dòng 27] _b==60
  47. [Dòng 28] _b==61
  48. [Dòng 28] _b==62
  49. [Dòng 28] _b==63
  50. [Dòng 28] _b==64
  51. [Dòng 28] this._b==65
  52. [Dòng 28] _b==66
  53. [Dòng 28] _b==67
  54. [Dòng 28] _b==68
  55. [Dòng 28] _b==69
  56. [Dòng 28] this._b==70
  57. [Dòng 28] this._b==71
  58. [Dòng 28] _b ==72
  59. [Dòng 28] _b ==73
  60. [Dòng 28] _b ==74
  61. [Dòng 28] _b ==75)) || (token && _b == 16)">
  62. [Dòng 28] _b == 16)">
  63. [Dòng 34] !token&&_auth==0 && shbGroupSelected
  64. [Dòng 38] !token&&_auth==0&&(_b==6 || _b==2 || _b == 31)
  65. [Dòng 38] _b == 31)">
  66. [Dòng 43] !token&&_auth==0&&(_b==3||_b==18||_b==19||_b==27)
  67. [Dòng 48] (token || _auth==1) && _b != 16
  68. [Dòng 51] !token&&_auth==0 && _b == 5

================================================================================

📁 FILE 52: domescard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/domescard-main/domescard-main.component.ts
📊 Thống kê: 55 điều kiện duy nhất
   - === : 22 lần
   - == : 26 lần
   - !== : 1 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (22 điều kiện):
  1. [Dòng 269] if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
  2. [Dòng 270] filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
  3. [Dòng 311] if (valOut === 'auth') {
  4. [Dòng 408] if (this._b === '1'
  5. [Dòng 408] this._b === '20'
  6. [Dòng 408] this._b === '64') {
  7. [Dòng 411] if (this._b === '36'
  8. [Dòng 411] this._b === '18'
  9. [Dòng 411] this._b === '19'
  10. [Dòng 414] if (this._b === '19'
  11. [Dòng 414] this._b === '16'
  12. [Dòng 414] this._b === '25'
  13. [Dòng 414] this._b === '33'
  14. [Dòng 415] this._b === '39'
  15. [Dòng 415] this._b === '9'
  16. [Dòng 415] this._b === '11'
  17. [Dòng 415] this._b === '17'
  18. [Dòng 416] this._b === '36'
  19. [Dòng 416] this._b === '44'
  20. [Dòng 417] this._b === '64'
  21. [Dòng 420] if (this._b === '20'
  22. [Dòng 423] if (this._b === '18') {

== (26 điều kiện):
  1. [Dòng 182] this._auth == 0
  2. [Dòng 182] this.tokenList.length == 0) {
  3. [Dòng 255] this.filteredData.length == 1
  4. [Dòng 292] $event == 'true') {
  5. [Dòng 352] if (bankId == 12) {
  6. [Dòng 411] this._b == '55'
  7. [Dòng 411] this._b == '47'
  8. [Dòng 411] this._b == '48'
  9. [Dòng 411] this._b == '59'
  10. [Dòng 411] this._b == '73'
  11. [Dòng 411] this._b == '12') {
  12. [Dòng 414] this._b == '3'
  13. [Dòng 415] this._b == '43'
  14. [Dòng 415] this._b == '45'
  15. [Dòng 416] this._b == '54'
  16. [Dòng 416] this._b == '57'
  17. [Dòng 417] this._b == '61'
  18. [Dòng 417] this._b == '63'
  19. [Dòng 417] this._b == '67'
  20. [Dòng 417] this._b == '68'
  21. [Dòng 417] this._b == '69'
  22. [Dòng 417] this._b == '72'
  23. [Dòng 417] this._b == '74'
  24. [Dòng 417] this._b == '75') {
  25. [Dòng 420] this._b == '36'
  26. [Dòng 440] if (item['id'] == this._b) {

!== (1 điều kiện):
  1. [Dòng 117] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (6 điều kiện):
  1. [Dòng 169] if (params['locale'] != null) {
  2. [Dòng 175] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 204] if (!(strInstrument != null
  4. [Dòng 207] if (strInstrument.substring(0, 1) != '^'
  5. [Dòng 207] strInstrument.substr(strInstrument.length - 1) != '$') {
  6. [Dòng 337] if (bankid != null) {

================================================================================

📁 FILE 53: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/intercard-main/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 54: intercard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/intercard-main/intercard-main.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 89] _showAVS!=true"

================================================================================

📁 FILE 55: intercard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/intercard-main/intercard-main.component.ts
📊 Thống kê: 59 điều kiện duy nhất
   - === : 8 lần
   - == : 31 lần
   - !== : 8 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 151] if (target.tagName === 'A'
  2. [Dòng 293] if (_formCard.country === 'default') {
  3. [Dòng 567] if (event.keyCode === 8
  4. [Dòng 567] event.key === "Backspace"
  5. [Dòng 642] if ((v.substr(-1) === ' '
  6. [Dòng 886] if (deviceValue === 'CA'
  7. [Dòng 886] deviceValue === 'US') {
  8. [Dòng 907] this.c_country = _val.value === 'default'

== (31 điều kiện):
  1. [Dòng 358] if (this._res_post.state == 'approved'
  2. [Dòng 358] this._res_post.state == 'failed') {
  3. [Dòng 384] } else if (this._res_post.state == 'failed') { // lỗi mới kiểm tra sang trang lỗi
  4. [Dòng 414] } else if (this._res_post.state == 'authorization_required') {
  5. [Dòng 415] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  6. [Dòng 427] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  7. [Dòng 486] v.length == 15) || (v.length == 16
  8. [Dòng 486] v.length == 19))
  9. [Dòng 487] this._util.checkMod10(v) == true) {
  10. [Dòng 521] cardNo.length == 15)
  11. [Dòng 523] cardNo.length == 16)
  12. [Dòng 524] cardNo.startsWith('81')) && (cardNo.length == 16
  13. [Dòng 524] cardNo.length == 19))
  14. [Dòng 567] event.inputType == 'deleteContentBackward') {
  15. [Dòng 568] if (event.target.name == 'exp_date'
  16. [Dòng 576] event.inputType == 'insertCompositionText') {
  17. [Dòng 591] if (((this.valueDate.length == 4
  18. [Dòng 591] this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  19. [Dòng 591] this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  20. [Dòng 642] v.length == 5) {
  21. [Dòng 650] v.length == 4
  22. [Dòng 654] v.length == 3)
  23. [Dòng 684] _val.value.length == 4
  24. [Dòng 688] _val.value.length == 3)
  25. [Dòng 864] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  26. [Dòng 864] this.valueDate.length == 5)
  27. [Dòng 1031] this.valueDate.length == 4
  28. [Dòng 1031] this.valueDate.search('/') == -1)
  29. [Dòng 1032] this.valueDate.length == 5))
  30. [Dòng 1045] this._i_csc.length == 4) ||
  31. [Dòng 1049] this._i_csc.length == 3)

!== (8 điều kiện):
  1. [Dòng 339] key !== '8') {
  2. [Dòng 368] codeResponse.toString() !== '0'){
  3. [Dòng 642] event.inputType !== 'deleteContentBackward') || v.length == 5) {
  4. [Dòng 883] if (deviceValue !== 'default') {
  5. [Dòng 900] this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
  6. [Dòng 1056] return this._i_country_code !== 'default'
  7. [Dòng 1113] this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
  8. [Dòng 1120] this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {

!= (12 điều kiện):
  1. [Dòng 195] if (params['locale'] != null) {
  2. [Dòng 201] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 360] if (this._res_post.return_url != null) {
  4. [Dòng 362] } else if (this._res_post.links != null
  5. [Dòng 362] this._res_post.links.merchant_return != null
  6. [Dòng 362] this._res_post.links.merchant_return.href != null) {
  7. [Dòng 468] if (ua.indexOf('safari') != -1
  8. [Dòng 521] cardNo != null
  9. [Dòng 569] if (this.valueDate.length != 3) {
  10. [Dòng 649] v != null
  11. [Dòng 683] this.c_csc = (!(_val.value != null
  12. [Dòng 1043] this._i_csc != null

================================================================================

📁 FILE 56: menu.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/menu.component.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 5 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 188] d_vrbank===true"

== (5 điều kiện):
  1. [Dòng 124] merchantId == 'OPTEST'
  2. [Dòng 145] sortMethodArray[i].trim()=='International'"
  3. [Dòng 153] sortMethodArray[i].trim()=='Domestic'"
  4. [Dòng 162] sortMethodArray[i].trim()=='QR'"
  5. [Dòng 170] sortMethodArray[i].trim()=='Paypal'"

================================================================================

📁 FILE 57: menu.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/menu.component.ts
📊 Thống kê: 158 điều kiện duy nhất
   - === : 9 lần
   - == : 87 lần
   - !== : 3 lần
   - != : 59 lần
--------------------------------------------------------------------------------

=== (9 điều kiện):
  1. [Dòng 864] this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number === 1
  2. [Dòng 924] if (this._res.state === 'unpaid'
  3. [Dòng 924] this._res.state === 'not_paid') {
  4. [Dòng 1046] if ('op' === auth
  5. [Dòng 1083] } else if ('bank' === auth
  6. [Dòng 1088] if (approval.method === 'REDIRECT') {
  7. [Dòng 1091] } else if (approval.method === 'POST_REDIRECT') {
  8. [Dòng 1325] return id === 'amex' ? '1234' : '123'
  9. [Dòng 1510] if (this.timeLeftPaypal === 0) {

== (87 điều kiện):
  1. [Dòng 200] if (el == 5) {
  2. [Dòng 202] } else if (el == 6) {
  3. [Dòng 204] } else if (el == 7) {
  4. [Dòng 206] } else if (el == 8) {
  5. [Dòng 208] } else if (el == 2) {
  6. [Dòng 210] } else if (el == 4) {
  7. [Dòng 212] } else if (el == 3) {
  8. [Dòng 248] if (!isNaN(_re.status) && (_re.status == '200'
  9. [Dòng 248] _re.status == '201') && _re.body != null) {
  10. [Dòng 253] if (('closed' == this._res_polling.state
  11. [Dòng 253] 'canceled' == this._res_polling.state
  12. [Dòng 253] 'expired' == this._res_polling.state)
  13. [Dòng 273] } else if ('paid' == this._res_polling.state) {
  14. [Dòng 279] this._res_polling.payments == null
  15. [Dòng 288] if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'authorization_required') {
  16. [Dòng 292] } else if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  17. [Dòng 299] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  18. [Dòng 301] this._paymentService.getCurrentPage() == 'enter_card') {
  19. [Dòng 304] } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
  20. [Dòng 304] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
  21. [Dòng 322] } else if ('not_paid' == this._res_polling.state) {
  22. [Dòng 334] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
  23. [Dòng 469] if (message == '1') {
  24. [Dòng 479] if (this.checkInvoiceState() == 1) {
  25. [Dòng 488] if (_re.status == '200'
  26. [Dòng 488] _re.status == '201') {
  27. [Dòng 494] this.version2 = _re.body?.merchant?.qr_version == "2"
  28. [Dòng 521] if (this.type == 5
  29. [Dòng 524] } else if (this.type == 6
  30. [Dòng 527] } else if (this.type == 2
  31. [Dòng 530] } else if (this.type == 7
  32. [Dòng 533] } else if (this.type == 8
  33. [Dòng 536] } else if (this.type == 4
  34. [Dòng 539] } else if (this.type == 3
  35. [Dòng 578] if (this.themeConfig.default_method == 'International'
  36. [Dòng 580] } else if (this.themeConfig.default_method == 'Domestic'
  37. [Dòng 582] } else if (this.themeConfig.default_method == 'QR'
  38. [Dòng 584] } else if (this.themeConfig.default_method == 'Paypal'
  39. [Dòng 644] if ('REDIRECT' == approval.method) {
  40. [Dòng 646] } else if ('POST_REDIRECT' == approval.method) {
  41. [Dòng 706] if (('closed' == this._res.state
  42. [Dòng 706] 'canceled' == this._res.state
  43. [Dòng 706] 'expired' == this._res.state
  44. [Dòng 706] 'paid' == this._res.state)
  45. [Dòng 865] if ((this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number) == 0
  46. [Dòng 889] this._auth == 0) {
  47. [Dòng 925] if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
  48. [Dòng 925] this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
  49. [Dòng 927] if (this._res.payments[this._res.payments.length - 1].state == 'approved'
  50. [Dòng 929] } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
  51. [Dòng 979] if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required') {
  52. [Dòng 985] } else if (this._res.payments[this._res.payments.length - 1].state == 'pending') {
  53. [Dòng 991] } else if (this._res.payments[this._res.payments.length - 1].state == 'canceled') {
  54. [Dòng 995] } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  55. [Dòng 995] this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
  56. [Dòng 1028] if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  57. [Dòng 1032] } else if (idBrand == 'atm'
  58. [Dòng 1111] if ('paid' == this._res.state) {
  59. [Dòng 1112] this._res.merchant.token_site == 'onepay')) {
  60. [Dòng 1155] this._res.payments == null) {
  61. [Dòng 1157] this._res.payments[this._res.payments.length - 1].state == 'pending') {
  62. [Dòng 1167] if (this._res.currencies[0] == 'USD') {
  63. [Dòng 1231] if (item.instrument.issuer.brand.id == 'atm') {
  64. [Dòng 1233] } else if (item.instrument.issuer.brand.id == 'visa'
  65. [Dòng 1233] item.instrument.issuer.brand.id == 'mastercard') {
  66. [Dòng 1234] if (item.instrument.issuer_location == 'd') {
  67. [Dòng 1239] } else if (item.instrument.issuer.brand.id == 'amex') {
  68. [Dòng 1245] } else if (item.instrument.issuer.brand.id == 'jcb') {
  69. [Dòng 1261] uniq.length == 1) {
  70. [Dòng 1389] if (type == 'qrv1') {
  71. [Dòng 1399] if (type == 'mobile') {
  72. [Dòng 1401] e.type == 'ewallet'
  73. [Dòng 1401] e.code == 'momo')) {
  74. [Dòng 1409] } else if (type == 'desktop') {
  75. [Dòng 1410] e.type == 'vnpayqr') || (regex.test(strTest)
  76. [Dòng 1454] _val == 2) {
  77. [Dòng 1481] if (_val == 2
  78. [Dòng 1483] } else if (_val == 2
  79. [Dòng 1489] _val == 2
  80. [Dòng 1498] if (this.type == 4) {
  81. [Dòng 1578] if (this._res_post.state == 'approved'
  82. [Dòng 1578] this._res_post.state == 'failed') {
  83. [Dòng 1587] } else if (this._res_post.state == 'authorization_required') {
  84. [Dòng 1588] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  85. [Dòng 1602] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  86. [Dòng 1663] filteredData.length == 1) {
  87. [Dòng 1718] if (data._locale == 'en') {

!== (3 điều kiện):
  1. [Dòng 1058] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  2. [Dòng 1445] if (_val !== 3) {
  3. [Dòng 1449] this._util.getElementParams(this.currentUrl, 't_id') !== '') {

!= (59 điều kiện):
  1. [Dòng 241] if (this._idInvoice != null
  2. [Dòng 241] this._paymentService.getState() != 'error') {
  3. [Dòng 247] if (this._paymentService.getCurrentPage() != 'otp') {
  4. [Dòng 248] _re.body != null) {
  5. [Dòng 254] this._res_polling.links != null
  6. [Dòng 254] this._res_polling.links.merchant_return != null //
  7. [Dòng 279] } else if (this._res_polling.merchant != null
  8. [Dòng 279] this._res_polling.merchant_invoice_reference != null
  9. [Dòng 281] } else if (this._res_polling.payments != null
  10. [Dòng 305] this._res_polling.links.merchant_return != null//
  11. [Dòng 325] this._res_polling.payments != null
  12. [Dòng 333] if (this._res_polling.payments != null
  13. [Dòng 337] t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
  14. [Dòng 450] this.type.toString().length != 0) {
  15. [Dòng 456] if (params['locale'] != null) {
  16. [Dòng 462] if ('otp' != this._paymentService.getCurrentPage()) {
  17. [Dòng 477] if (this._paymentService.getInvoiceDetail() != null) {
  18. [Dòng 707] this._res.links != null
  19. [Dòng 707] this._res.links.merchant_return != null
  20. [Dòng 903] if (count != 1) {
  21. [Dòng 913] if (this._res.merchant != null
  22. [Dòng 913] this._res.merchant_invoice_reference != null) {
  23. [Dòng 917] if (this._res.merchant.address_details != null) {
  24. [Dòng 925] this._res.links != null//
  25. [Dòng 974] } else if (this._res.payments != null
  26. [Dòng 996] this._res.payments[this._res.payments.length - 1].instrument != null
  27. [Dòng 996] this._res.payments[this._res.payments.length - 1].instrument.issuer != null
  28. [Dòng 997] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
  29. [Dòng 997] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
  30. [Dòng 998] this._res.payments[this._res.payments.length - 1].links != null
  31. [Dòng 998] this._res.payments[this._res.payments.length - 1].links.cancel != null
  32. [Dòng 998] this._res.payments[this._res.payments.length - 1].links.cancel.href != null
  33. [Dòng 1014] this._res.payments[this._res.payments.length - 1].links.update != null
  34. [Dòng 1014] this._res.payments[this._res.payments.length - 1].links.update.href != null) {
  35. [Dòng 1032] this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
  36. [Dòng 1033] this._res.payments[this._res.payments.length - 1].authorization != null
  37. [Dòng 1033] this._res.payments[this._res.payments.length - 1].authorization.links != null) {
  38. [Dòng 1046] this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
  39. [Dòng 1046] this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
  40. [Dòng 1049] if (this._res.payments[this._res.payments.length - 1].authorization != null
  41. [Dòng 1049] this._res.payments[this._res.payments.length - 1].authorization.links != null
  42. [Dòng 1055] auth = paramUserName != null ? paramUserName : ''
  43. [Dòng 1134] this._res.links.merchant_return != null //
  44. [Dòng 1155] } else if (this._res.merchant != null
  45. [Dòng 1155] this._res.merchant_invoice_reference != null
  46. [Dòng 1269] if (['mastercard', 'visa', 'amex', 'jcb', 'cup', 'card', 'np_card', 'atm'].indexOf(id) != -1) {
  47. [Dòng 1271] } else if (['techcombank_account', 'vib_account', 'dongabank_account', 'tpbank_account', 'bidv_account', 'pvcombank_account', 'shb_account', 'oceanbank_online_account'].indexOf(id) != -1) {
  48. [Dòng 1273] } else if (['oceanbank_mobile_account'].indexOf(id) != -1) {
  49. [Dòng 1275] } else if (['shb_customer_id'].indexOf(id) != -1) {
  50. [Dòng 1292] if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1) {
  51. [Dòng 1320] return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
  52. [Dòng 1357] if (!(strInstrument != null
  53. [Dòng 1374] if (this._translate.currentLang != language) {
  54. [Dòng 1401] e.type != 'ewallet') || (regex.test(strTest)
  55. [Dòng 1451] } else if (this._res.payments != null) {
  56. [Dòng 1580] if (this._res_post.return_url != null) {
  57. [Dòng 1582] } else if (this._res_post.links != null
  58. [Dòng 1582] this._res_post.links.merchant_return != null
  59. [Dòng 1582] this._res_post.links.merchant_return.href != null) {

================================================================================

📁 FILE 58: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 59: qr-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1] screen=='qr'"
  2. [Dòng 111] screen=='confirm_close'"

================================================================================

📁 FILE 60: qr-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 61: qr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main/qr-main.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 6] filteredData.length === 0
  2. [Dòng 6] filteredDataOther.length === 0"
  3. [Dòng 40] filteredDataMobile.length === 0
  4. [Dòng 40] filteredDataOtherMobile.length === 0"

================================================================================

📁 FILE 62: qr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main/qr-main.component.ts
📊 Thống kê: 25 điều kiện duy nhất
   - === : 11 lần
   - == : 8 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 225] this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
  2. [Dòng 226] this.filteredDataOther = this.appList.filter(item => item.type === 'other');
  3. [Dòng 227] this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
  4. [Dòng 228] this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
  5. [Dòng 233] this.appList.length === 1) {
  6. [Dòng 235] if ((this.filteredDataMobile.length === 1
  7. [Dòng 235] this.filteredDataOtherMobile.length === 1)
  8. [Dòng 260] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  9. [Dòng 261] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  10. [Dòng 267] if (item.type === 'mobile_banking') {
  11. [Dòng 612] this.appList.length === 1

== (8 điều kiện):
  1. [Dòng 152] this.themeConfig.deeplink_status == 'Off' ? false : true
  2. [Dòng 266] if (item.available == true) {
  3. [Dòng 332] if (_re.status == '200'
  4. [Dòng 332] _re.status == '201') {
  5. [Dòng 335] if (appcode == 'grabpay'
  6. [Dòng 335] appcode == 'momo') {
  7. [Dòng 338] if (type == 2
  8. [Dòng 375] err.error.code == '04') {

!= (6 điều kiện):
  1. [Dòng 170] if (params['locale'] != null) {
  2. [Dòng 176] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 202] if (!(strInstrument != null
  4. [Dòng 296] if (appcode != null) {
  5. [Dòng 587] if (_re.status != '200'
  6. [Dòng 587] _re.status != '201')

================================================================================

📁 FILE 63: safe-html.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main/safe-html.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 64: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 65: qr-desktop.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 1 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 168] listVNPayQR.length === 0"

================================================================================

📁 FILE 66: qr-desktop.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.ts
📊 Thống kê: 19 điều kiện duy nhất
   - === : 4 lần
   - == : 10 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 371] this.listWalletQR.length === 1) {
  2. [Dòng 421] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  3. [Dòng 422] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  4. [Dòng 760] this.listWalletQR.length === 1

== (10 điều kiện):
  1. [Dòng 263] e.type == 'vnpayqr') {
  2. [Dòng 270] e.type == 'ewallet') {
  3. [Dòng 320] if (_re.status == '200'
  4. [Dòng 320] _re.status == '201') {
  5. [Dòng 349] e.type == 'wallet')) {
  6. [Dòng 388] if (d.b.code == s) {
  7. [Dòng 427] if (item.available == true) {
  8. [Dòng 486] if (appcode == 'grabpay'
  9. [Dòng 486] appcode == 'momo') {
  10. [Dòng 519] err.error.code == '04') {

!= (5 điều kiện):
  1. [Dòng 225] if (params['locale'] != null) {
  2. [Dòng 253] if (!(strInstrument != null
  3. [Dòng 445] if (appcode != null) {
  4. [Dòng 734] if (_re.status != '200'
  5. [Dòng 734] _re.status != '201')

================================================================================

📁 FILE 67: qr-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1] screen=='qr'"
  2. [Dòng 122] screen=='confirm_close'"

================================================================================

📁 FILE 68: qr-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 49] 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {

================================================================================

📁 FILE 69: qr-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (3 điều kiện):
  1. [Dòng 5] type == 'vnpay'"
  2. [Dòng 6] type == 'bankapp'"
  3. [Dòng 7] type == 'both'"

================================================================================

📁 FILE 70: qr-guide-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 71: list-bank-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 72: list-bank-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 73: qr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 74: qr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-main.component.ts
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 171] this.themeConfig.deeplink_status == 'Off' ? false : true

!= (2 điều kiện):
  1. [Dòng 187] if (params['locale'] != null) {
  2. [Dòng 193] if ('otp' != this._paymentService.getCurrentPage()) {

================================================================================

📁 FILE 75: qr-mobile.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 132] _locale=='vi'"
  2. [Dòng 133] _locale=='en'"
  3. [Dòng 143] _locale == 'vi'"
  4. [Dòng 145] _locale == 'en'"

!= (2 điều kiện):
  1. [Dòng 231] qr_version2 != 'None'"
  2. [Dòng 257] qr_version2 != 'None'

================================================================================

📁 FILE 76: qr-mobile.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.ts
📊 Thống kê: 30 điều kiện duy nhất
   - === : 6 lần
   - == : 19 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (6 điều kiện):
  1. [Dòng 472] if (!this.d_vnpayqr_wallet && !this.d_vnpayqr_deeplink && (this.listWalletQR?.length === 1
  2. [Dòng 472] this.listWalletDeeplink?.length === 1)) {
  3. [Dòng 589] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  4. [Dòng 590] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  5. [Dòng 596] if (item.type === 'deeplink') {
  6. [Dòng 978] this.listWalletQR?.length === 1

== (19 điều kiện):
  1. [Dòng 279] e.type == 'deeplink') {
  2. [Dòng 290] e.type == 'ewallet'
  3. [Dòng 310] e.type == 'vnpayqr') {
  4. [Dòng 324] e.type == 'wallet')) {
  5. [Dòng 353] e.type == 'ewallet') {
  6. [Dòng 383] if (e.type == 'ewallet') {
  7. [Dòng 406] this.listWallet.length == 1
  8. [Dòng 406] this.listWallet[0].code == 'momo') {
  9. [Dòng 408] this.checkEWalletDeeplink.length == 0) {
  10. [Dòng 489] arrayWallet.length == 0) return false;
  11. [Dòng 491] if (arrayWallet[i].code == key) {
  12. [Dòng 524] if (_re.status == '200'
  13. [Dòng 524] _re.status == '201') {
  14. [Dòng 546] if (d.b.code == s) {
  15. [Dòng 595] if (item.available == true) {
  16. [Dòng 671] if (appcode == 'grabpay'
  17. [Dòng 671] appcode == 'momo') {
  18. [Dòng 674] if (type == 2
  19. [Dòng 714] err.error.code == '04') {

!= (5 điều kiện):
  1. [Dòng 232] if (params['locale'] != null) {
  2. [Dòng 260] if (!(strInstrument != null
  3. [Dòng 623] if (appcode != null) {
  4. [Dòng 948] if (_re.status != '200'
  5. [Dòng 948] _re.status != '201')

================================================================================

📁 FILE 77: safe-html.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/qr-main-version2/safe-html.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 78: queuing.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/queuing/queuing.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 79: queuing.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/queuing/queuing.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 62] if (this.locale == 'vi') {

!= (1 điều kiện):
  1. [Dòng 86] if (this.translate.currentLang != language) {

================================================================================

📁 FILE 80: domescard-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.html
📊 Thống kê: 8 điều kiện duy nhất
   - === : 4 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 1] type === 2"
  2. [Dòng 2] [ngStyle]="{'border-color': type === 2 ? this.themeConfig.border_color : border_color}"
  3. [Dòng 22] *ngIf="((type === 2
  4. [Dòng 22] type === '2'

== (2 điều kiện):
  1. [Dòng 5] uniqueTokenBank) || (type == 2
  2. [Dòng 22] type == 2)) || token">

!= (2 điều kiện):
  1. [Dòng 5] (((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token
  2. [Dòng 18] feeService['atm']['fee'] != 0"

================================================================================

📁 FILE 81: domescard-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 82: intercard-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 1] type === 1"
  2. [Dòng 2] [ngStyle]="{'border-color': type === 1 ? this.themeConfig.border_color : border_color}"

!= (1 điều kiện):
  1. [Dòng 26] feeService['visa_mastercard_d']['fee'] != 0"

================================================================================

📁 FILE 83: intercard-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 84: deeplink.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/megags-qr/deeplink/deeplink.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 85: deeplink.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/megags-qr/deeplink/deeplink.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 86: megags-qr.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/megags-qr/megags-qr.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 2] [ngStyle]="{'border-color': type === 5 ? this.themeConfig.border_color : border_color}"
  2. [Dòng 12] type === 5"

!= (1 điều kiện):
  1. [Dòng 13] feeService['qr']['fee'] != 0"

================================================================================

📁 FILE 87: megags-qr.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/megags-qr/megags-qr.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 58] this.themeConfig.deeplink_status == 'Off' ? false : true
  2. [Dòng 65] if (this.type == 5) {
  3. [Dòng 96] if (_re.status == '200'
  4. [Dòng 96] _re.status == '201') {

!= (2 điều kiện):
  1. [Dòng 61] if (params['locale'] != null) {
  2. [Dòng 79] if (appcode != null) {

================================================================================

📁 FILE 88: qr.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/megags-qr/qr/qr.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 89: qr.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/megags-qr/qr/qr.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 90: paypal-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 1] type === 3"
  2. [Dòng 2] [ngStyle]="{'border-color': type === 3 ? this.themeConfig.border_color : border_color}"

!= (1 điều kiện):
  1. [Dòng 15] feeService['pp']['fee'] != 0"

================================================================================

📁 FILE 91: paypal-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 92: qr-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/qr-form/qr-form.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 1] type === 4"
  2. [Dòng 2] [ngStyle]="{'border-color': type === 4 ? this.themeConfig.border_color : border_color}"
  3. [Dòng 19] type === 4

!= (1 điều kiện):
  1. [Dòng 17] feeService['qr']['fee'] != 0"

================================================================================

📁 FILE 93: qr-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/sorting-option/qr-form/qr-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 94: remove-token-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/token-main/remove-token-dialog/remove-token-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 95: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/token-main/token-dialog/dialog-guide-dialog.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (6 điều kiện):
  1. [Dòng 9] data['type'] == 'Visa'
  2. [Dòng 9] data['type'] == 'Master'
  3. [Dòng 9] data['type'] == 'JCB'"
  4. [Dòng 17] data['type'] == 'Visa'"
  5. [Dòng 17] data['type'] == 'Master'"
  6. [Dòng 24] data['type'] == 'Amex'"

================================================================================

📁 FILE 96: token-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/token-main/token-main.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 1 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 8] style="{{item.brand_id === 'visa' ? 'height: 14.42px

== (1 điều kiện):
  1. [Dòng 28] token_main == '1'"

!= (1 điều kiện):
  1. [Dòng 21] item['feeService']['fee'] != 0"

================================================================================

📁 FILE 97: token-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/main/menu/token-main/token-main.component.ts
📊 Thống kê: 67 điều kiện duy nhất
   - === : 6 lần
   - == : 41 lần
   - !== : 1 lần
   - != : 19 lần
--------------------------------------------------------------------------------

=== (6 điều kiện):
  1. [Dòng 78] if (event.keyCode === 13) {
  2. [Dòng 179] && ((item.brand_id === 'amex'
  3. [Dòng 195] return id === 'amex' ? '1234' : '123'
  4. [Dòng 337] if (approval.method === 'REDIRECT') {
  5. [Dòng 340] } else if (approval.method === 'POST_REDIRECT') {
  6. [Dòng 411] return this._i_token_otp != null && !isNaN(+this._i_token_otp) && ((id === 'amex'

== (41 điều kiện):
  1. [Dòng 102] if (message == '0') {
  2. [Dòng 136] item.id == element.id ? element['active'] = true : element['active'] = false
  3. [Dòng 140] item.display_cvv == true ? this.flagToken = true : this.flagToken = false
  4. [Dòng 161] if (result == 'success') {
  5. [Dòng 166] if (this.tokenList.length == 0) {
  6. [Dòng 170] } else if (result == 'error') {
  7. [Dòng 179] _val.value.trim().length == 4) || (item.brand_id != 'amex'
  8. [Dòng 179] _val.value.trim().length == 3))
  9. [Dòng 188] _val.value.length == 4) || (item.brand_id != 'amex'
  10. [Dòng 188] _val.value.length == 3)));
  11. [Dòng 194] id == 'amex' ? this.maxLength = 4 : this.maxLength = 3
  12. [Dòng 216] if (_re.body.state == 'more_info_required') {
  13. [Dòng 231] if (this._res_post.state == 'approved'
  14. [Dòng 231] this._res_post.state == 'failed') {
  15. [Dòng 238] if (this._res_post.state == 'failed') {
  16. [Dòng 254] } else if (this._res_post.state == 'authorization_required') {
  17. [Dòng 255] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  18. [Dòng 267] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  19. [Dòng 283] } else if (_re.body.state == 'authorization_required') {
  20. [Dòng 300] if (this._b == 1
  21. [Dòng 300] this._b == 14
  22. [Dòng 300] this._b == 15
  23. [Dòng 300] this._b == 24
  24. [Dòng 300] this._b == 8
  25. [Dòng 300] this._b == 10
  26. [Dòng 300] this._b == 20
  27. [Dòng 300] this._b == 22
  28. [Dòng 300] this._b == 23
  29. [Dòng 300] this._b == 30
  30. [Dòng 300] this._b == 11
  31. [Dòng 300] this._b == 17
  32. [Dòng 300] this._b == 18
  33. [Dòng 300] this._b == 27
  34. [Dòng 300] this._b == 5
  35. [Dòng 300] this._b == 12) {
  36. [Dòng 359] } else if (_re.body.state == 'failed') {
  37. [Dòng 403] if (action == 'blur') {
  38. [Dòng 411] this._i_token_otp.trim().length == 4)
  39. [Dòng 412] this._i_token_otp.trim().length == 3));
  40. [Dòng 465] if (_re.status == '200'
  41. [Dòng 465] _re.status == '201') {

!== (1 điều kiện):
  1. [Dòng 126] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (19 điều kiện):
  1. [Dòng 94] if (params['locale'] != null) {
  2. [Dòng 178] if (_val.value != null
  3. [Dòng 187] this.c_token_otp_csc = !(_val.value != null
  4. [Dòng 233] if (this._res_post.return_url != null) {
  5. [Dòng 235] } else if (this._res_post.links != null
  6. [Dòng 235] this._res_post.links.merchant_return != null
  7. [Dòng 235] this._res_post.links.merchant_return.href != null) {
  8. [Dòng 288] if (_re.body.authorization != null
  9. [Dòng 288] _re.body.authorization.links != null
  10. [Dòng 295] if (_re.body.links != null
  11. [Dòng 295] _re.body.links.cancel != null) {
  12. [Dòng 361] if (_re.body.return_url != null) {
  13. [Dòng 363] } else if (_re.body.links != null
  14. [Dòng 363] _re.body.links.merchant_return != null
  15. [Dòng 363] _re.body.links.merchant_return.href != null) {
  16. [Dòng 394] return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
  17. [Dòng 399] if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(item.brand_id) != -1
  18. [Dòng 411] return this._i_token_otp != null
  19. [Dòng 412] || (id != 'amex'

================================================================================

📁 FILE 98: overlay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/overlay/overlay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 99: overlay.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/overlay/overlay.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 100: overlay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/overlay/overlay.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 101: bank-amount.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/pipe/bank-amount.pipe.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 11] return ((a.id == id
  2. [Dòng 11] a.code == id) && a.type.includes(type));

================================================================================

📁 FILE 102: account-main.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/services/account-main.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 103: close-dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/services/close-dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 104: data.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/services/data.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 100] const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
  2. [Dòng 100] item.method === method) : null;

================================================================================

📁 FILE 105: deep_link.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/services/deep_link.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 9] if (isIphone == true) {
  2. [Dòng 22] } else if (isAndroid == true) {

================================================================================

📁 FILE 106: dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/services/dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 107: fee.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/services/fee.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 108: multiple_method.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/services/multiple_method.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 109: payment.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/services/payment.service.ts
📊 Thống kê: 13 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 11 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 595] return countPayment == maxPayment
  2. [Dòng 633] if (this.getLatestPayment().state == 'canceled')

!= (11 điều kiện):
  1. [Dòng 115] if (idInvoice != null
  2. [Dòng 115] idInvoice != 0)
  3. [Dòng 125] idInvoice != 0) {
  4. [Dòng 302] if (this._merchantid != null
  5. [Dòng 302] this._tranref != null
  6. [Dòng 302] this._state != null
  7. [Dòng 369] let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
  8. [Dòng 405] urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
  9. [Dòng 490] if (res?.status != 200
  10. [Dòng 490] res?.status != 201) return;
  11. [Dòng 581] if (paymentId != null) {

================================================================================

📁 FILE 110: qr.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/services/qr.service.ts
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 58] if (res?.state == 'canceled') {
  2. [Dòng 99] state == 'authorization_required'

!= (3 điều kiện):
  1. [Dòng 40] if (_re.status != '200'
  2. [Dòng 40] _re.status != '201') {
  3. [Dòng 49] latestPayment?.state != "authorization_required") {

================================================================================

📁 FILE 111: time-stop.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/services/time-stop.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 112: token-main.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/services/token-main.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 113: index.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/translate/index.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 114: lang-en.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/translate/lang-en.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 115: lang-vi.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/translate/lang-vi.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 116: translate.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/translate/translate.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 117: translate.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/translate/translate.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 37] if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
  2. [Dòng 38] else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';

================================================================================

📁 FILE 118: translations.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/translate/translations.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 119: apps-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/util/apps-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 549] if (e.name == bankSwift) { // TODO: get by swift
  2. [Dòng 557] return this.apps.find(e => e.code == appCode);

================================================================================

📁 FILE 120: apps-information.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/util/apps-information.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 121: banks-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/util/banks-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1013] if (+e.id == bankId) {
  2. [Dòng 1063] if (e.swiftCode == bankSwift) {

================================================================================

📁 FILE 122: error-handler.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/util/error-handler.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 9] err?.status === 400
  2. [Dòng 10] err?.error?.name === 'INVALID_CARD_FEE'

================================================================================

📁 FILE 123: util.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/app/util/util.ts
📊 Thống kê: 39 điều kiện duy nhất
   - === : 15 lần
   - == : 17 lần
   - !== : 3 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (15 điều kiện):
  1. [Dòng 54] if (v.length === 2
  2. [Dòng 54] this.flag.length === 3
  3. [Dòng 54] this.flag.charAt(this.flag.length - 1) === '/') {
  4. [Dòng 58] if (v.length === 1) {
  5. [Dòng 60] } else if (v.length === 2) {
  6. [Dòng 63] v.length === 2) {
  7. [Dòng 71] if (len === 2) {
  8. [Dòng 143] if (M[1] === 'Chrome') {
  9. [Dòng 268] if (param === key) {
  10. [Dòng 431] pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
  11. [Dòng 435] target === 0
  12. [Dòng 481] if (cardTypeBank === 'bank_card_number') {
  13. [Dòng 484] } else if (cardTypeBank === 'bank_account_number') {
  14. [Dòng 534] if (event.keyCode === 8
  15. [Dòng 534] event.key === "Backspace"

== (17 điều kiện):
  1. [Dòng 17] if (temp.length == 0) {
  2. [Dòng 24] return (counter % 10 == 0);
  3. [Dòng 117] if (this.checkCount == 1) {
  4. [Dòng 129] if (results == null) {
  5. [Dòng 162] if (c.length == 3) {
  6. [Dòng 175] d = d == undefined ? '.' : d
  7. [Dòng 176] t = t == undefined ? '
  8. [Dòng 256] return results == null ? null : results[1]
  9. [Dòng 534] event.inputType == 'deleteContentBackward') {
  10. [Dòng 535] if (event.target.name == 'exp_date'
  11. [Dòng 543] event.inputType == 'insertCompositionText') {
  12. [Dòng 557] if (((_val.length == 4
  13. [Dòng 557] _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  14. [Dòng 557] _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  15. [Dòng 583] iss_date.length == 4
  16. [Dòng 583] iss_date.search('/') == -1)
  17. [Dòng 584] iss_date.length == 5))

!== (3 điều kiện):
  1. [Dòng 263] queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
  2. [Dòng 264] if (queryString !== '') {
  3. [Dòng 435] if (target !== 0

!= (4 điều kiện):
  1. [Dòng 145] if (tem != null) {
  2. [Dòng 150] if ((tem = ua.match(/version\/(\d+)/i)) != null) {
  3. [Dòng 479] if (ua.indexOf('safari') != -1
  4. [Dòng 536] if (v.length != 3) {

================================================================================

📁 FILE 124: qrcode.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/assets/script/qrcode.js
📊 Thống kê: 67 điều kiện duy nhất
   - === : 2 lần
   - == : 53 lần
   - !== : 0 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 2255] if (typeof define === 'function'
  2. [Dòng 2257] } else if (typeof exports === 'object') {

== (53 điều kiện):
  1. [Dòng 68] if (_dataCache == null) {
  2. [Dòng 85] if ( (0 <= r && r <= 6 && (c == 0
  3. [Dòng 85] c == 6) )
  4. [Dòng 86] || (0 <= c && c <= 6 && (r == 0
  5. [Dòng 86] r == 6) )
  6. [Dòng 107] if (i == 0
  7. [Dòng 122] _modules[r][6] = (r % 2 == 0);
  8. [Dòng 129] _modules[6][c] = (c % 2 == 0);
  9. [Dòng 152] if (r == -2
  10. [Dòng 152] r == 2
  11. [Dòng 152] c == -2
  12. [Dòng 152] c == 2
  13. [Dòng 153] || (r == 0
  14. [Dòng 153] c == 0) ) {
  15. [Dòng 169] ( (bits >> i) & 1) == 1);
  16. [Dòng 226] if (col == 6) col -= 1;
  17. [Dòng 232] if (_modules[row][col - c] == null) {
  18. [Dòng 237] dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
  19. [Dòng 249] if (bitIndex == -1) {
  20. [Dòng 458] margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
  21. [Dòng 498] if (typeof arguments[0] == 'object') {
  22. [Dòng 587] margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
  23. [Dòng 733] if (b == -1) throw 'eof';
  24. [Dòng 741] if (b0 == -1) break;
  25. [Dòng 767] if (typeof b == 'number') {
  26. [Dòng 768] if ( (b & 0xff) == b) {
  27. [Dòng 910] return function(i, j) { return (i + j) % 2 == 0
  28. [Dòng 912] return function(i, j) { return i % 2 == 0
  29. [Dòng 914] return function(i, j) { return j % 3 == 0
  30. [Dòng 916] return function(i, j) { return (i + j) % 3 == 0
  31. [Dòng 918] return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
  32. [Dòng 920] return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
  33. [Dòng 922] return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
  34. [Dòng 924] return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
  35. [Dòng 1011] if (r == 0
  36. [Dòng 1011] c == 0) {
  37. [Dòng 1015] if (dark == qrcode.isDark(row + r, col + c) ) {
  38. [Dòng 1036] if (count == 0
  39. [Dòng 1036] count == 4) {
  40. [Dòng 1149] if (typeof num.length == 'undefined') {
  41. [Dòng 1155] num[offset] == 0) {
  42. [Dòng 1495] if (typeof rsBlock == 'undefined') {
  43. [Dòng 1538] return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
  44. [Dòng 1543] _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
  45. [Dòng 1599] if (data.length - i == 1) {
  46. [Dòng 1601] } else if (data.length - i == 2) {
  47. [Dòng 1866] } else if (n == 62) {
  48. [Dòng 1868] } else if (n == 63) {
  49. [Dòng 1928] if (_buflen == 0) {
  50. [Dòng 1937] if (c == '=') {
  51. [Dòng 1961] } else if (c == 0x2b) {
  52. [Dòng 1963] } else if (c == 0x2f) {
  53. [Dòng 2133] if (table.size() == (1 << bitLength) ) {

!= (12 điều kiện):
  1. [Dòng 119] if (_modules[r][6] != null) {
  2. [Dòng 126] if (_modules[6][c] != null) {
  3. [Dòng 144] if (_modules[row][col] != null) {
  4. [Dòng 365] while (buffer.getLengthInBits() % 8 != 0) {
  5. [Dòng 750] if (count != numChars) {
  6. [Dòng 751] throw count + ' != ' + numChars
  7. [Dòng 878] while (data != 0) {
  8. [Dòng 1733] if (test.length != 2
  9. [Dòng 1733] ( (test[0] << 8) | test[1]) != code) {
  10. [Dòng 1894] if (_length % 3 != 0) {
  11. [Dòng 2067] if ( (data >>> length) != 0) {
  12. [Dòng 2178] return typeof _map[key] != 'undefined'

================================================================================

📁 FILE 125: environment.prod.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/environments/environment.prod.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 126: environment.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/environments/environment.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 127: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/acc_bidv/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 128: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/acc_bidv/script.js
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 9] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 12] if (inName.value === "") return err("MISSING_FIELD"
  3. [Dòng 21] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 23] if (e !== null) {
  2. [Dòng 55] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 129: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/acc_oceanbank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 130: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/acc_oceanbank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 10] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 19] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 21] if (e !== null) {
  2. [Dòng 53] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 131: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/acc_pvcombank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 132: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/acc_pvcombank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 7] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 16] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 18] if (e !== null) {
  2. [Dòng 50] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 133: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/acc_shb/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 134: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/acc_shb/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 10] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 19] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 21] if (e !== null) {
  2. [Dòng 53] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 135: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/acc_tpbank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 136: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/acc_tpbank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 7] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 16] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 18] if (e !== null) {
  2. [Dòng 50] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 137: common.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/common.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 89] if (i % 2 === parity) d *= 2;
  2. [Dòng 93] return (sum % 10) === 0
  3. [Dòng 163] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 166] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 252] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 258] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 274] if (xhr.status === 200
  8. [Dòng 274] xhr.status === 201) {
  9. [Dòng 277] if (invoiceState === "unpaid"
  10. [Dòng 277] invoiceState === "not_paid") {
  11. [Dòng 282] if (paymentState === "authorization_required") {
  12. [Dòng 288] if (method === "REDIRECT") {
  13. [Dòng 291] } else if (method === "POST_REDIRECT") {
  14. [Dòng 330] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 141] this.oldValue !== this.value) {
  2. [Dòng 313] if (jLinks !== undefined
  3. [Dòng 313] jLinks !== null) {
  4. [Dòng 315] if (jMerchantReturn !== undefined
  5. [Dòng 315] jMerchantReturn !== null) {
  6. [Dòng 330] if (responseCode !== undefined
  7. [Dòng 330] responseCode !== null
  8. [Dòng 358] if (parentRes !== "{

================================================================================

📁 FILE 138: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/domestic_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 139: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/domestic_card/script.js
📊 Thống kê: 14 điều kiện duy nhất
   - === : 11 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 18] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 60] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 63] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 72] year === y
  5. [Dòng 74] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 106] if (inPhone.value === "") return err("MISSING_FIELD"
  7. [Dòng 136] if ("PAY" === operation) {
  8. [Dòng 525] } else if (value === "") {
  9. [Dòng 542] if (trPhone.style.display === "") {
  10. [Dòng 544] } else if (trName.style.display === "") {
  11. [Dòng 565] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 138] if (e !== null) {
  2. [Dòng 404] if (lang !== "vi") lang = "en";
  3. [Dòng 470] if (value !== "") {

================================================================================

📁 FILE 140: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/domestic_token/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 141: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/domestic_token/script.js
📊 Thống kê: 25 điều kiện duy nhất
   - === : 23 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (23 điều kiện):
  1. [Dòng 20] if ("PAY" === operation) {
  2. [Dòng 93] if (trName.style.display === "") {
  3. [Dòng 117] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  4. [Dòng 123] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  5. [Dòng 137] if (xhr.status === 200) {
  6. [Dòng 165] if (insType === "card") {
  7. [Dòng 166] if (insBrandId === "visa"
  8. [Dòng 166] insBrandId === "mastercard"
  9. [Dòng 166] insBrandId === "amex"
  10. [Dòng 166] insBrandId === "jcb"
  11. [Dòng 166] insBrandId === "cup") {
  12. [Dòng 170] } else if (insBrandId === "atm") {
  13. [Dòng 241] } else if (insType === "dongabank_account") {
  14. [Dòng 242] } else if (insType === "techcombank_account") {
  15. [Dòng 243] } else if (insType === "vib_account") {
  16. [Dòng 244] } else if (insType === "bidv_account") {
  17. [Dòng 245] } else if (insType === "tpbank_account") {
  18. [Dòng 246] } else if (insType === "shb_account") {
  19. [Dòng 247] } else if (insType === "shb_customer_id") {
  20. [Dòng 248] } else if (insType === "vpbank_account") {
  21. [Dòng 249] } else if (insType === "oceanbank_online_account") {
  22. [Dòng 250] } else if (insType === "oceanbank_mobile_account") {
  23. [Dòng 251] } else if (insType === "pvcombank_account") {

!== (2 điều kiện):
  1. [Dòng 54] if (lang !== "vi") lang = "en";
  2. [Dòng 273] if (parentRes !== "{

================================================================================

📁 FILE 142: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 143: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/international_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 144: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/international_card/script.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 13] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 23] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 26] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 38] year === y
  5. [Dòng 40] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 71] if ("PAY" === operation) {
  7. [Dòng 161] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 73] if (e !== null) {
  2. [Dòng 118] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 145: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/international_token/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 146: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/international_token/script.js
📊 Thống kê: 36 điều kiện duy nhất
   - === : 25 lần
   - == : 0 lần
   - !== : 11 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (25 điều kiện):
  1. [Dòng 32] year === y
  2. [Dòng 34] if (inCvv.value === "") {
  3. [Dòng 67] if ("PAY" === operation) {
  4. [Dòng 129] } else if (value === "") {
  5. [Dòng 190] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 196] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 209] if (xhr.status === 200) {
  8. [Dòng 245] if (insType === "card") {
  9. [Dòng 246] if (insBrandId === "visa"
  10. [Dòng 246] insBrandId === "mastercard"
  11. [Dòng 246] insBrandId === "amex"
  12. [Dòng 246] insBrandId === "jcb"
  13. [Dòng 246] insBrandId === "cup") {
  14. [Dòng 248] } else if (insBrandId === "atm") {
  15. [Dòng 319] } else if (insType === "dongabank_account") {
  16. [Dòng 320] } else if (insType === "techcombank_account") {
  17. [Dòng 321] } else if (insType === "vib_account") {
  18. [Dòng 322] } else if (insType === "bidv_account") {
  19. [Dòng 323] } else if (insType === "tpbank_account") {
  20. [Dòng 324] } else if (insType === "shb_account") {
  21. [Dòng 325] } else if (insType === "shb_customer_id") {
  22. [Dòng 326] } else if (insType === "vpbank_account") {
  23. [Dòng 327] } else if (insType === "oceanbank_online_account") {
  24. [Dòng 328] } else if (insType === "oceanbank_mobile_account") {
  25. [Dòng 329] } else if (insType === "pvcombank_account") {

!== (11 điều kiện):
  1. [Dòng 18] if (inMonth.value !== ""
  2. [Dòng 21] if (inYear.value !== ""
  3. [Dòng 23] var month = inMonth.value !== ""
  4. [Dòng 24] var year = parseInt("20" + (inYear.value !== ""
  5. [Dòng 35] if ("2D" !== order["vpc_VerType"]) return err("MISSING_FIELD"
  6. [Dòng 71] if (e !== null) {
  7. [Dòng 79] inMonth.value !== tokenInsMonth) instrument["month"] = inMonth.value;
  8. [Dòng 80] inYear.value !== tokenInsYear) instrument["year"] = inYear.value;
  9. [Dòng 81] if (inCvv.value !== "") instrument["cvv"] = inCvv.value;
  10. [Dòng 95] if (lang !== "vi") lang = "en";
  11. [Dòng 351] if (parentRes !== "{

================================================================================

📁 FILE 147: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/amway/script.js
📊 Thống kê: 54 điều kiện duy nhất
   - === : 41 lần
   - == : 1 lần
   - !== : 12 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (41 điều kiện):
  1. [Dòng 4] if ((cardno.length === 15
  2. [Dòng 4] cardno.length === 16
  3. [Dòng 4] cardno.length === 19) && isMode10(cardno) === true) {
  4. [Dòng 4] isMode10(cardno) === true) {
  5. [Dòng 23] if (i % 2 === parity) d *= 2;
  6. [Dòng 27] return (sum % 10) === 0
  7. [Dòng 48] if ("PAY" === operation) {
  8. [Dòng 69] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  9. [Dòng 75] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  10. [Dòng 91] if (xhr.status === 200
  11. [Dòng 91] xhr.status === 201) {
  12. [Dòng 94] if (invoiceState === "unpaid"
  13. [Dòng 94] invoiceState === "not_paid") {
  14. [Dòng 99] if (paymentState === "authorization_required") {
  15. [Dòng 105] if (method === "REDIRECT") {
  16. [Dòng 110] } else if (method === "POST_REDIRECT") {
  17. [Dòng 223] if (typeof queryParams[key] === "undefined") {
  18. [Dòng 226] } else if (typeof queryParams[key] === "string") {
  19. [Dòng 256] if (params["CardList"] === undefined
  20. [Dòng 256] params["CardList"] === null) return;
  21. [Dòng 302] if (xhr.status === 200) {
  22. [Dòng 312] if (insType === "card") {
  23. [Dòng 313] if (insBrandId === "visa"
  24. [Dòng 313] insBrandId === "mastercard"
  25. [Dòng 313] insBrandId === "amex"
  26. [Dòng 313] insBrandId === "jcb"
  27. [Dòng 313] insBrandId === "cup") {
  28. [Dòng 317] } else if (insBrandId === "atm") {
  29. [Dòng 318] if (insSwiftCode === "BFTVVNVX") { //Vietcombank
  30. [Dòng 322] } else if (insSwiftCode === "BIDVVNVX") { //BIDV
  31. [Dòng 329] } else if (insType === "dongabank_account") {
  32. [Dòng 331] } else if (insType === "techcombank_account") {
  33. [Dòng 333] } else if (insType === "vib_account") {
  34. [Dòng 335] } else if (insType === "bidv_account") {
  35. [Dòng 336] } else if (insType === "tpbank_account") {
  36. [Dòng 337] } else if (insType === "shb_account") {
  37. [Dòng 338] } else if (insType === "shb_customer_id") {
  38. [Dòng 339] } else if (insType === "vpbank_account") {
  39. [Dòng 340] } else if (insType === "oceanbank_online_account") {
  40. [Dòng 341] } else if (insType === "oceanbank_mobile_account") {
  41. [Dòng 342] } else if (insType === "pvcombank_account") {

== (1 điều kiện):
  1. [Dòng 270] if (cardList.length == 1) {//TOKEN, BIDV, SHB, OCEAN, PVCOM, TP Bank

!== (12 điều kiện):
  1. [Dòng 52] if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
  2. [Dòng 53] if (inNumber.style.display !== "none") instrument.number = inNumber.value;
  3. [Dòng 54] if (inDate.style.display !== "none") instrument.date = inDate.value;
  4. [Dòng 55] if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
  5. [Dòng 56] if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
  6. [Dòng 57] if (inName.style.display !== "none") instrument.name = inName.value;
  7. [Dòng 132] if (jLinks !== undefined
  8. [Dòng 132] jLinks !== null) {
  9. [Dòng 134] if (jMerchantReturn !== undefined
  10. [Dòng 134] jMerchantReturn !== null) {
  11. [Dòng 166] if (parentRes !== "{
  12. [Dòng 255] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 148: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 149: bidv.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Bidv/assets/js/bidv.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 233] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 239] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 255] if (xhr.status === 200
  8. [Dòng 255] xhr.status === 201) {
  9. [Dòng 258] if (invoiceState === "unpaid"
  10. [Dòng 258] invoiceState === "not_paid") {
  11. [Dòng 263] if (paymentState === "authorization_required") {
  12. [Dòng 269] if (method === "REDIRECT") {
  13. [Dòng 272] } else if (method === "POST_REDIRECT") {
  14. [Dòng 311] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 294] if (jLinks !== undefined
  3. [Dòng 294] jLinks !== null) {
  4. [Dòng 296] if (jMerchantReturn !== undefined
  5. [Dòng 296] jMerchantReturn !== null) {
  6. [Dòng 311] if (responseCode !== undefined
  7. [Dòng 311] responseCode !== null
  8. [Dòng 339] if (parentRes !== "{

================================================================================

📁 FILE 150: bidv2.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Bidv/bidv2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 76] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 88] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 78] if ( $('.circle_v1').css('display') == 'block'
  2. [Dòng 112] if ($('.circle_v2').css('display') == 'block'
  3. [Dòng 112] $('.circle_v3').css('display') == 'block' ) {
  4. [Dòng 302] $('.circle_v1').css('display') == 'block'

!== (3 điều kiện):
  1. [Dòng 90] if (e !== null) {
  2. [Dòng 273] if (lang !== "vi") lang = "en";
  3. [Dòng 305] if (value !== "") {

================================================================================

📁 FILE 151: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Bidv/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 152: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 153: ocean.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Ocean/assets/js/ocean.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 232] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 237] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 251] if (xhr.status === 200
  8. [Dòng 251] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 257] if (paymentState === "authorization_required") {
  12. [Dòng 261] if (method === "REDIRECT") {
  13. [Dòng 264] } else if (method === "POST_REDIRECT") {
  14. [Dòng 303] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 286] if (jLinks !== undefined
  3. [Dòng 286] jLinks !== null) {
  4. [Dòng 288] if (jMerchantReturn !== undefined
  5. [Dòng 288] jMerchantReturn !== null) {
  6. [Dòng 303] if (responseCode !== undefined
  7. [Dòng 303] responseCode !== null
  8. [Dòng 331] if (parentRes !== "{

================================================================================

📁 FILE 154: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Ocean/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 155: ocean2.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Ocean/ocean2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 72] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 92] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 74] document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM'
  2. [Dòng 116] if ( document.getElementsByClassName("select_online")[0].value == 'Easy Online Banking') {
  3. [Dòng 119] if ( document.getElementsByClassName("select_online")[0].value == 'Easy Mobile Banking') {
  4. [Dòng 304] if ( document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM') {

!== (3 điều kiện):
  1. [Dòng 94] if (e !== null) {
  2. [Dòng 276] if (lang !== "vi") lang = "en";
  3. [Dòng 306] if (value !== "") {

================================================================================

📁 FILE 156: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 157: pvbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Pv_Bank/assets/js/pvbank.js
📊 Thống kê: 23 điều kiện duy nhất
   - === : 14 lần
   - == : 1 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 239] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 261] if (xhr.status === 200
  8. [Dòng 261] xhr.status === 201) {
  9. [Dòng 264] if (invoiceState === "unpaid"
  10. [Dòng 264] invoiceState === "not_paid") {
  11. [Dòng 269] if (paymentState === "authorization_required") {
  12. [Dòng 275] if (method === "REDIRECT") {
  13. [Dòng 278] } else if (method === "POST_REDIRECT") {
  14. [Dòng 317] responseCode === "0") {

== (1 điều kiện):
  1. [Dòng 167] if ($('.circle_v1').css('display') == 'block') {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 300] if (jLinks !== undefined
  3. [Dòng 300] jLinks !== null) {
  4. [Dòng 302] if (jMerchantReturn !== undefined
  5. [Dòng 302] jMerchantReturn !== null) {
  6. [Dòng 317] if (responseCode !== undefined
  7. [Dòng 317] responseCode !== null
  8. [Dòng 345] if (parentRes !== "{

================================================================================

📁 FILE 158: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Pv_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 159: pvbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Pv_Bank/pvbank2.js
📊 Thống kê: 15 điều kiện duy nhất
   - === : 8 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 71] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 76] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 79] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 88] year === y
  5. [Dòng 90] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 100] if ("PAY" === operation) {
  7. [Dòng 347] } else if (value === "") {
  8. [Dòng 364] if (trName.style.display === "") {

== (4 điều kiện):
  1. [Dòng 73] if ($('.circle_v1').css('display') == 'block'
  2. [Dòng 75] $('.circle_v1').css('display') == 'block'
  3. [Dòng 124] if ( $('.circle_v1').css('display') == 'block') {
  4. [Dòng 129] else if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 102] if (e !== null) {
  2. [Dòng 286] if (lang !== "vi") lang = "en";
  3. [Dòng 316] if (value !== "") {

================================================================================

📁 FILE 160: Sea2.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/SeaBank/Sea2.js
📊 Thống kê: 11 điều kiện duy nhất
   - === : 8 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 23] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 31] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 34] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 43] year === y
  5. [Dòng 45] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 55] if ("PAY" === operation) {
  7. [Dòng 287] } else if (value === "") {
  8. [Dòng 304] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 57] if (e !== null) {
  2. [Dòng 233] if (lang !== "vi") lang = "en";
  3. [Dòng 263] if (value !== "") {

================================================================================

📁 FILE 161: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 162: Sea.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/SeaBank/assets/js/Sea.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 228] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 234] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 258] if (paymentState === "authorization_required") {
  12. [Dòng 264] if (method === "REDIRECT") {
  13. [Dòng 267] } else if (method === "POST_REDIRECT") {
  14. [Dòng 306] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 289] if (jLinks !== undefined
  3. [Dòng 289] jLinks !== null) {
  4. [Dòng 291] if (jMerchantReturn !== undefined
  5. [Dòng 291] jMerchantReturn !== null) {
  6. [Dòng 306] if (responseCode !== undefined
  7. [Dòng 306] responseCode !== null
  8. [Dòng 334] if (parentRes !== "{

================================================================================

📁 FILE 163: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/SeaBank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 164: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 165: shb.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Shb/assets/js/shb.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 234] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 240] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 256] if (xhr.status === 200
  8. [Dòng 256] xhr.status === 201) {
  9. [Dòng 259] if (invoiceState === "unpaid"
  10. [Dòng 259] invoiceState === "not_paid") {
  11. [Dòng 264] if (paymentState === "authorization_required") {
  12. [Dòng 270] if (method === "REDIRECT") {
  13. [Dòng 273] } else if (method === "POST_REDIRECT") {
  14. [Dòng 312] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 295] if (jLinks !== undefined
  3. [Dòng 295] jLinks !== null) {
  4. [Dòng 297] if (jMerchantReturn !== undefined
  5. [Dòng 297] jMerchantReturn !== null) {
  6. [Dòng 312] if (responseCode !== undefined
  7. [Dòng 312] responseCode !== null
  8. [Dòng 340] if (parentRes !== "{

================================================================================

📁 FILE 166: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Shb/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 167: shb2.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Shb/shb2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 73] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 85] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 74] if ($('.circle_v2').css('display') == 'block'
  2. [Dòng 110] if ($('.circle_v1').css('display') == 'block'
  3. [Dòng 114] if ( $('.circle_v3').css('display') == 'block' ) {
  4. [Dòng 299] if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 87] if (e !== null) {
  2. [Dòng 271] if (lang !== "vi") lang = "en";
  3. [Dòng 301] if (value !== "") {

================================================================================

📁 FILE 168: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 169: tpbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Tp_Bank/assets/js/tpbank.js
📊 Thống kê: 23 điều kiện duy nhất
   - === : 14 lần
   - == : 1 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 239] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 261] if (xhr.status === 200
  8. [Dòng 261] xhr.status === 201) {
  9. [Dòng 264] if (invoiceState === "unpaid"
  10. [Dòng 264] invoiceState === "not_paid") {
  11. [Dòng 269] if (paymentState === "authorization_required") {
  12. [Dòng 275] if (method === "REDIRECT") {
  13. [Dòng 278] } else if (method === "POST_REDIRECT") {
  14. [Dòng 317] responseCode === "0") {

== (1 điều kiện):
  1. [Dòng 167] if ($('.circle_v1').css('display') == 'block') {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 300] if (jLinks !== undefined
  3. [Dòng 300] jLinks !== null) {
  4. [Dòng 302] if (jMerchantReturn !== undefined
  5. [Dòng 302] jMerchantReturn !== null) {
  6. [Dòng 317] if (responseCode !== undefined
  7. [Dòng 317] responseCode !== null
  8. [Dòng 345] if (parentRes !== "{

================================================================================

📁 FILE 170: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Tp_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 171: tpbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Tp_Bank/tpbank2.js
📊 Thống kê: 15 điều kiện duy nhất
   - === : 8 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 71] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 78] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 81] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 90] year === y
  5. [Dòng 92] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 102] if ("PAY" === operation) {
  7. [Dòng 349] } else if (value === "") {
  8. [Dòng 366] if (trName.style.display === "") {

== (4 điều kiện):
  1. [Dòng 73] if ($('.circle_v1').css('display') == 'block'
  2. [Dòng 76] $('.circle_v1').css('display') == 'block'
  3. [Dòng 126] if ( $('.circle_v1').css('display') == 'block') {
  4. [Dòng 131] else if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 104] if (e !== null) {
  2. [Dòng 288] if (lang !== "vi") lang = "en";
  3. [Dòng 318] if (value !== "") {

================================================================================

📁 FILE 172: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 173: onepay.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Visa/assets/js/onepay.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 78] if (i % 2 === parity) d *= 2;
  2. [Dòng 82] return (sum % 10) === 0
  3. [Dòng 152] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 155] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 240] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 259] if (xhr.status === 200
  8. [Dòng 259] xhr.status === 201) {
  9. [Dòng 261] if (invoiceState === "unpaid"
  10. [Dòng 261] invoiceState === "not_paid") {
  11. [Dòng 265] if (paymentState === "authorization_required") {
  12. [Dòng 269] if (method === "REDIRECT") {
  13. [Dòng 272] } else if (method === "POST_REDIRECT") {
  14. [Dòng 311] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 130] this.oldValue !== this.value) {
  2. [Dòng 294] if (jLinks !== undefined
  3. [Dòng 294] jLinks !== null) {
  4. [Dòng 296] if (jMerchantReturn !== undefined
  5. [Dòng 296] jMerchantReturn !== null) {
  6. [Dòng 311] if (responseCode !== undefined
  7. [Dòng 311] responseCode !== null
  8. [Dòng 339] if (parentRes !== "{

================================================================================

📁 FILE 174: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Visa/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 175: index.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Visa/index.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 19] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 29] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 32] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 44] year === y
  5. [Dòng 46] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 77] if ("PAY" === operation) {
  7. [Dòng 168] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 79] if (e !== null) {
  2. [Dòng 125] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 176: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 177: vpbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Vp_Bank/assets/js/vpbank.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 230] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 236] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 252] if (xhr.status === 200
  8. [Dòng 252] xhr.status === 201) {
  9. [Dòng 255] if (invoiceState === "unpaid"
  10. [Dòng 255] invoiceState === "not_paid") {
  11. [Dòng 260] if (paymentState === "authorization_required") {
  12. [Dòng 266] if (method === "REDIRECT") {
  13. [Dòng 269] } else if (method === "POST_REDIRECT") {
  14. [Dòng 308] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 291] if (jLinks !== undefined
  3. [Dòng 291] jLinks !== null) {
  4. [Dòng 293] if (jMerchantReturn !== undefined
  5. [Dòng 293] jMerchantReturn !== null) {
  6. [Dòng 308] if (responseCode !== undefined
  7. [Dòng 308] responseCode !== null
  8. [Dòng 336] if (parentRes !== "{

================================================================================

📁 FILE 178: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Vp_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 179: vpbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/Vp_Bank/vpbank2.js
📊 Thống kê: 14 điều kiện duy nhất
   - === : 11 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 32] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 35] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 44] year === y
  5. [Dòng 46] if (inPhone.value === "") return err("MISSING_FIELD"
  6. [Dòng 49] if (inName.value === "") return err("MISSING_FIELD"
  7. [Dòng 59] if ("PAY" === operation) {
  8. [Dòng 292] } else if (value === "") {
  9. [Dòng 309] if (trPhone.style.display === "") {
  10. [Dòng 311] } else if (trName.style.display === "") {
  11. [Dòng 332] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 61] if (e !== null) {
  2. [Dòng 237] if (lang !== "vi") lang = "en";
  3. [Dòng 267] if (value !== "") {

================================================================================

📁 FILE 180: sha.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/assets/js/sha.js
📊 Thống kê: 49 điều kiện duy nhất
   - === : 38 lần
   - == : 0 lần
   - !== : 11 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (38 điều kiện):
  1. [Dòng 12] 1>t)throw Error("numRounds must a integer >= 1");if("SHA-1"===c)m=512,q=K,u=Z,f=160,r=function(a){return a.slice()};else if(0===c.lastIndexOf("SHA-",0))if(q=function(a,b){return L(a,b,c)
  2. [Dòng 12] c)},u=function(a,b,h,e){var k,f;if("SHA-224"===c
  3. [Dòng 12] "SHA-256"===c)k=(b+65>>>9<<4)+15,f=16;else if("SHA-384"===c
  4. [Dòng 12] "SHA-512"===c)k=(b+129>>>10<<
  5. [Dòng 13] c);if("SHA-224"===c)a=[e[0],e[1],e[2],e[3],e[4],e[5],e[6]];else if("SHA-256"===c)a=e;else if("SHA-384"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,e[4].b,e[5].a,e[5].b];else if("SHA-512"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,
  6. [Dòng 14] "SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)
  7. [Dòng 14] 0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
  8. [Dòng 14] return a},r=function(a){return a.slice()},"SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)||0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
  9. [Dòng 15] c)m=1152,f=224;else if("SHA3-256"===c)m=1088,f=256;else if("SHA3-384"===c)m=832,f=384;else if("SHA3-512"===c)m=576,f=512;else if("SHAKE128"===c)m=1344,f=-1,F=31,z=!0;else if("SHAKE256"===c)m=1088,f=-1,F=31,z=!0;else throw Error("Chosen SHA variant is not supported");u=function(a
  10. [Dòng 16] 0===64*l%e
  11. [Dòng 16] 5][l/5|0];g.push(a.b);if(32*g.length>=h)break;g.push(a.a);l+=1;0===64*l%e&&D(null,b)}return g}}else throw Error("Chosen SHA variant is not supported");d=M(a,g,x);l=A(c);this.setHMACKey=function(a,b,h){var k;if(!0===I)throw Error("HMAC key already set");if(!0===y)throw Error("Cannot set HMAC key after calling update");if(!0===z)throw Error("SHAKE is not supported for HMAC");g=(h
  12. [Dòng 17] f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
  13. [Dòng 17] )b.push(0);b[h]&=4294967040}for(a=0;a<=h;a+=1)v[a]=b[a]^909522486,w[a]=b[a]^1549556828;l=q(v,l);e=m;I=!0};this.update=function(a){var c,b,k,f=0,g=m>>>5;c=d(a,h,n);a=c.binLen;b=c.value;c=a>>>5;for(k=0;k<c;k+=g)f+m<=a&&(l=q(b.slice(k,k+g),l),f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
  14. [Dòng 18] for(g=1;g<t;g+=1)!0===z
  15. [Dòng 19] f);return k(m)};this.getHMAC=function(a,b){var k,g,d,p;if(!1===I)throw Error("Cannot call getHMAC without first setting HMAC key");d=N(b);switch(a){case "HEX":k=function(a){return O(a,f,x,d)};break;case "B64":k=function(a){return P(a,f,x,d)};break;case "BYTES":k=function(a){return Q(a,f,x)};break;case "ARRAYBUFFER":try{k=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}k=function(a){return R(a,f,x)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
  16. [Dòng 20] d=-1===b?3:0
  17. [Dòng 20] f=-1===b?3:0
  18. [Dòng 21] g=-1===b?3:0
  19. [Dòng 22] !0===c.hasOwnProperty("b64Pad")
  20. [Dòng 22] a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");
  21. [Dòng 23] u=-1===b?3:0
  22. [Dòng 23] l+=2){p=parseInt(a.substr(l,2),16);if(isNaN(p))throw Error("String of HEX type contains invalid characters");m=(l>>>1)+q;for(f=m>>>2;c.length<=f;)c.push(0);c[f]|=p<<8*(u+m%4*b)}return{value:c,binLen:4*g+d}};break;case "TEXT":c=function(c,h,d){var g,l,p=0,f,m,q,u,r,t;h=h||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(t=-1===
  23. [Dòng 24] m+=1){r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=l[m]<<8*(t+r%4*b);p+=1}else if("UTF16BE"===a
  24. [Dòng 24] "UTF16LE"===a)for(t=-1===b?2:0
  25. [Dòng 24] UTF16LE"===a
  26. [Dòng 24] 1===b
  27. [Dòng 25] !0===l
  28. [Dòng 25] !0===l&&(m=g&255,g=m<<8|g>>>8);r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=g<<8*(t+r%4*b);p+=2}return{value:h,binLen:8*p+d}};break;case "B64":c=function(a,c,d){var g=0,l,p,f,m,q,u,r,t;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");p=a.indexOf("=");a=a.replace(/\=/g
  29. [Dòng 25] t=-1===b?3:0
  30. [Dòng 26] q=-1===b?3:0
  31. [Dòng 27] m=-1===b?3:0
  32. [Dòng 32] c.b^a.b)}function A(c){var a=[],d;if("SHA-1"===c)a=[1732584193,4023233417,2562383102,271733878,3285377520];else if(0===c.lastIndexOf("SHA-",0))switch(a=
  33. [Dòng 34] new b(d[2],4271175723),new b(d[3],1595750129),new b(d[4],2917565137),new b(d[5],725511199),new b(d[6],4215389547),new b(d[7],327033209)];break;default:throw Error("Unknown SHA variant");}else if(0===c.lastIndexOf("SHA3-",0)
  34. [Dòng 34] 0===c.lastIndexOf("SHAKE",0))for(c=0
  35. [Dòng 36] a,k){var e,h,n,g,l,p,f,m,q,u,r,t,v,w,y,A,z,x,F,B,C,D,E=[],J;if("SHA-224"===k
  36. [Dòng 36] "SHA-256"===k)u=64,t=1,D=Number,v=G,w=la,y=H,A=ha,z=ja,x=da,F=fa,C=U,B=aa,J=d;else if("SHA-384"===k
  37. [Dòng 36] "SHA-512"===k)u=80,t=2,D=b,v=ma,w=na,y=oa,A=ia,z=ka,x=ea,F=ga,C=ca,B=ba,J=V;else throw Error("Unexpected error in SHA-2 implementation");k=a[0];e=a[1];h=a[2];n=a[3];g=a[4];l=a[5];p=a[6];f=a[7];for(r=0
  38. [Dòng 45] "function"===typeof define

!== (11 điều kiện):
  1. [Dòng 12] 'use strict';(function(Y){function C(c,a,b){var e=0,h=[],n=0,g,l,d,f,m,q,u,r,I=!1,v=[],w=[],t,y=!1,z=!1,x=-1;b=b||{};g=b.encoding||"UTF8";t=b.numRounds||1;if(t!==parseInt(t,10)
  2. [Dòng 18] 0!==f%32
  3. [Dòng 22] a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8
  4. [Dòng 23] }switch(c){case "HEX":c=function(a,c,d){var g=a.length,l,p,f,m,q,u;if(0!==g%2)throw Error("String of HEX type must be in byte increments");c=c||[0];d=d||0;q=d>>>3;u=-1===b?3:0;for(l=0
  5. [Dòng 24] 1!==b
  6. [Dòng 24] "UTF16LE"!==a
  7. [Dòng 25] "");if(-1!==p
  8. [Dòng 27] function(a,c,d){var g,l,p,f,m,q;c=c||[0];d=d||0;l=d>>>3;m=-1===b?3:0;q=new Uint8Array(a);for(g=0;g<a.byteLength;g+=1)f=g+l,p=f>>>2,c.length<=p&&c.push(0),c[p]|=q[g]<<8*(m+f%4*b);return{value:c,binLen:8*a.byteLength+d}};break;default:throw Error("format must be HEX, TEXT, B64, BYTES, or ARRAYBUFFER");}return c}function y(c,a){return c<<a|c>>>32-a}function S(c,a){return 32<a?(a-=32,new b(c.b<<a|c.a>>>32-a,c.a<<a|c.b>>>32-a)):0!==a?new b(c.a<<a|c.b>>>32-a,c.b<<a|c.a>>>32-a):c}function w(c,a){return c>>>
  9. [Dòng 37] a[7]);return a}function D(c,a){var d,e,h,n,g=[],l=[];if(null!==c)for(e=0
  10. [Dòng 45] define.amd?define(function(){return C}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=C),exports=C):Y.jsSHA=C})(this);
  11. [Dòng 45] return C}):"undefined"!==typeof exports?("undefined"!==typeof module

================================================================================

📁 FILE 181: sha256.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/assets/js/sha256.js
📊 Thống kê: 28 điều kiện duy nhất
   - === : 20 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (20 điều kiện):
  1. [Dòng 12] 1>u)throw Error("numRounds must a integer >= 1");if(0===c.lastIndexOf("SHA-",0))if(q=function(b,a){return A(b,a,c)
  2. [Dòng 12] c)},y=function(b,a,l,f){var g,e;if("SHA-224"===c
  3. [Dòng 12] "SHA-256"===c)g=(a+65>>>9<<4)+15,e=16;else throw Error("Unexpected error in SHA-2 implementation");for(
  4. [Dòng 13] c);if("SHA-224"===c)b=[f[0],f[1],f[2],f[3],f[4],f[5],f[6]];else if("SHA-256"===c)b=f;else throw Error("Unexpected error in SHA-2 implementation");return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
  5. [Dòng 13] "SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a
  6. [Dòng 13] return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
  7. [Dòng 14] if(!0===z)throw Error("Cannot set HMAC key after calling update");f=(g
  8. [Dòng 15] 5);g=a%h;z=!0};this.getHash=function(a,f){var d,h,k,q;if(!0===m)throw Error("Cannot call getHash after setting HMAC key");k=C(f);switch(a){case "HEX":d=function(a){return D(a,e,k)};break;case "B64":d=function(a){return E(a,e,k)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{h=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("format must be HEX, B64, BYTES, or ARRAYBUFFER");
  9. [Dòng 16] x(c));return d(q)};this.getHMAC=function(a,f){var d,k,t,u;if(!1===m)throw Error("Cannot call getHMAC without first setting HMAC key");t=C(f);switch(a){case "HEX":d=function(a){return D(a,e,t)};break;case "B64":d=function(a){return E(a,e,t)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{d=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
  10. [Dòng 18] !0===c.hasOwnProperty("b64Pad")
  11. [Dòng 20] h=(d>>>1)+q;for(e=h>>>2;b.length<=e;)b.push(0);b[e]|=k<<8*(3+h%4*-1)}return{value:b,binLen:4*f+c}};break;case "TEXT":d=function(c,b,d){var f,n,k=0,e,h,q,m,p,r;b=b||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(r=3
  12. [Dòng 21] q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=n[h]<<8*(r+p%4*-1);k+=1}else if("UTF16BE"===a
  13. [Dòng 21] "UTF16LE"===a)for(r=2
  14. [Dòng 21] UTF16LE"===a
  15. [Dòng 21] !0===n
  16. [Dòng 21] e+=1){f=c.charCodeAt(e);!0===n&&(h=f&255,f=h<<8|f>>>8);p=k+q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=f<<8*(r+p%4*-1);k+=2}return{value:b,binLen:8*k+d}};break;case "B64":d=function(a,b,c){var f=0,d,k,e,h,q,m,p;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");k=a.indexOf("=");a=a.replace(/\=/g
  17. [Dòng 25] 16)&65535)<<16|b&65535}function R(c,a,d,l,b){var g=(c&65535)+(a&65535)+(d&65535)+(l&65535)+(b&65535);return((c>>>16)+(a>>>16)+(d>>>16)+(l>>>16)+(b>>>16)+(g>>>16)&65535)<<16|g&65535}function x(c){var a=[],d;if(0===c.lastIndexOf("SHA-",0))switch(a=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428]
  18. [Dòng 26] new m,new m,new m,new m,new m,new m];break;case "SHA-512":a=[new m,new m,new m,new m,new m,new m,new m,new m];break;default:throw Error("Unknown SHA variant");}else throw Error("No SHA variants supported");return a}function A(c,a,d){var l,b,g,f,n,k,e,h,m,r,p,w,t,x,u,z,A,B,C,D,E,F,v=[],G;if("SHA-224"===d
  19. [Dòng 26] "SHA-256"===d)r=64,w=1,F=Number,t=P,x=Q,u=R,z=N,A=O,B=L,C=M,E=K,D=J,G=H;else throw Error("Unexpected error in SHA-2 implementation");d=a[0];l=a[1];b=a[2];g=a[3];f=a[4];n=a[5];k=a[6];e=a[7];for(p=
  20. [Dòng 29] "function"===typeof define

!== (8 điều kiện):
  1. [Dòng 12] 'use strict';(function(I){function w(c,a,d){var l=0,b=[],g=0,f,n,k,e,h,q,y,p,m=!1,t=[],r=[],u,z=!1;d=d||{};f=d.encoding||"UTF8";u=d.numRounds||1;if(u!==parseInt(u,10)
  2. [Dòng 18] l+=1)g[l]=c[l>>>2]>>>8*(3+l%4*-1)&255;return b}function C(c){var a={outputUpper:!1,b64Pad:"=",shakeLen:-1};c=c||{};a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");
  3. [Dòng 19] if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0;d<f;d+=2){k=parseInt(a.substr(d,2),16);if(isNaN(k))throw Error("String of HEX type contains invalid characters");
  4. [Dòng 19] if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0
  5. [Dòng 21] "UTF16LE"!==a
  6. [Dòng 22] "");if(-1!==k
  7. [Dòng 29] define.amd?define(function(){return w}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=w),exports=w):I.jsSHA=w})(this);
  8. [Dòng 29] return w}):"undefined"!==typeof exports?("undefined"!==typeof module

================================================================================

📁 FILE 182: slick.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/assets/libraries/slick/slick.js
📊 Thống kê: 182 điều kiện duy nhất
   - === : 126 lần
   - == : 5 lần
   - !== : 47 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (126 điều kiện):
  1. [Dòng 20] if (typeof define === 'function'
  2. [Dòng 206] if (typeof(index) === 'boolean') {
  3. [Dòng 215] if (typeof(index) === 'number') {
  4. [Dòng 216] if (index === 0
  5. [Dòng 216] _.$slides.length === 0) {
  6. [Dòng 224] if (addBefore === true) {
  7. [Dòng 249] if (_.options.slidesToShow === 1
  8. [Dòng 249] _.options.adaptiveHeight === true
  9. [Dòng 249] _.options.vertical === false) {
  10. [Dòng 264] if (_.options.rtl === true
  11. [Dòng 267] if (_.transformsEnabled === false) {
  12. [Dòng 268] if (_.options.vertical === false) {
  13. [Dòng 280] if (_.cssTransitions === false) {
  14. [Dòng 281] if (_.options.rtl === true) {
  15. [Dòng 355] typeof asNavFor === 'object' ) {
  16. [Dòng 371] if (_.options.fade === false) {
  17. [Dòng 414] if ( _.options.infinite === false ) {
  18. [Dòng 416] if ( _.direction === 1
  19. [Dòng 416] ( _.currentSlide + 1 ) === ( _.slideCount - 1 )) {
  20. [Dòng 420] else if ( _.direction === 0 ) {
  21. [Dòng 424] if ( _.currentSlide - 1 === 0 ) {
  22. [Dòng 442] if (_.options.arrows === true ) {
  23. [Dòng 487] if (_.options.dots === true
  24. [Dòng 524] _.$slideTrack = (_.slideCount === 0) ?
  25. [Dòng 532] if (_.options.centerMode === true
  26. [Dòng 532] _.options.swipeToSlide === true) {
  27. [Dòng 547] _.setSlideClasses(typeof _.currentSlide === 'number' ? _.currentSlide : 0);
  28. [Dòng 549] if (_.options.draggable === true) {
  29. [Dòng 602] if (_.respondTo === 'window') {
  30. [Dòng 604] } else if (_.respondTo === 'slider') {
  31. [Dòng 606] } else if (_.respondTo === 'min') {
  32. [Dòng 618] if (_.originalSettings.mobileFirst === false) {
  33. [Dòng 635] if (_.breakpointSettings[targetBreakpoint] === 'unslick') {
  34. [Dòng 641] if (initial === true) {
  35. [Dòng 705] slideOffset = indexOffset === 0 ? _.options.slidesToScroll : _.options.slidesToShow - indexOffset
  36. [Dòng 712] slideOffset = indexOffset === 0 ? _.options.slidesToScroll : indexOffset
  37. [Dòng 719] var index = event.data.index === 0 ? 0 :
  38. [Dòng 765] if (_.options.accessibility === true) {
  39. [Dòng 772] if (_.options.arrows === true
  40. [Dòng 797] if (_.options.focusOnSelect === true) {
  41. [Dòng 836] if (_.shouldClick === false) {
  42. [Dòng 1051] if (_.options.infinite === true) {
  43. [Dòng 1061] } else if (_.options.centerMode === true) {
  44. [Dòng 1094] if (_.options.vertical === true
  45. [Dòng 1094] _.options.centerMode === true) {
  46. [Dòng 1095] if (_.options.slidesToShow === 2) {
  47. [Dòng 1097] } else if (_.options.slidesToShow === 1) {
  48. [Dòng 1128] } else if (_.options.centerMode === true
  49. [Dòng 1128] _.options.infinite === true) {
  50. [Dòng 1141] if (_.options.variableWidth === true) {
  51. [Dòng 1143] _.options.infinite === false) {
  52. [Dòng 1159] if (_.options.centerMode === true) {
  53. [Dòng 1200] if (_.options.infinite === false) {
  54. [Dòng 1229] centerOffset = _.options.centerMode === true ? _.slideWidth * Math.floor(_.options.slidesToShow / 2) : 0
  55. [Dòng 1231] if (_.options.swipeToSlide === true) {
  56. [Dòng 1406] _.options.pauseOnDotsHover === true
  57. [Dòng 1498] if (event.keyCode === 37
  58. [Dòng 1498] _.options.accessibility === true) {
  59. [Dòng 1501] message: _.options.rtl === true ? 'next' :
  60. [Dòng 1504] } else if (event.keyCode === 39
  61. [Dòng 1507] message: _.options.rtl === true ? 'previous' : 'next'
  62. [Dòng 1585] if (_.options.fade === true) {
  63. [Dòng 1593] if (_.options.lazyLoad === 'anticipated') {
  64. [Dòng 1616] } else if (_.currentSlide === 0) {
  65. [Dòng 1637] if (_.options.lazyLoad === 'progressive') {
  66. [Dòng 1773] if ( _.options.adaptiveHeight === true ) {
  67. [Dòng 1864] if ( $.type(responsiveSettings) === 'array'
  68. [Dòng 1878] _.breakpoints[l] === currentBreakpoint ) {
  69. [Dòng 1969] index = removeBefore === true ? 0 : _.slideCount - 1
  70. [Dòng 1971] index = removeBefore === true ? --index : index
  71. [Dòng 1980] if (removeAll === true) {
  72. [Dòng 2050] if (_.options.vertical === false
  73. [Dòng 2050] _.options.variableWidth === false) {
  74. [Dòng 2054] } else if (_.options.variableWidth === true) {
  75. [Dòng 2062] if (_.options.variableWidth === false) _.$slideTrack.children('.slick-slide').width(_.slideWidth - offset);
  76. [Dòng 2128] if( $.type( arguments[0] ) === 'object' ) {
  77. [Dòng 2134] } else if ( $.type( arguments[0] ) === 'string' ) {
  78. [Dòng 2140] if ( arguments[0] === 'responsive'
  79. [Dòng 2140] $.type( arguments[1] ) === 'array' ) {
  80. [Dòng 2152] if ( type === 'single' ) {
  81. [Dòng 2157] } else if ( type === 'multiple' ) {
  82. [Dòng 2166] } else if ( type === 'responsive' ) {
  83. [Dòng 2181] if( _.options.responsive[l].breakpoint === value[item].breakpoint ) {
  84. [Dòng 2231] _.positionProp = _.options.vertical === true ? 'top' : 'left'
  85. [Dòng 2233] if (_.positionProp === 'top') {
  86. [Dòng 2242] if (_.options.useCSS === true) {
  87. [Dòng 2248] if ( typeof _.options.zIndex === 'number' ) {
  88. [Dòng 2261] if (bodyStyle.perspectiveProperty === undefined
  89. [Dòng 2261] bodyStyle.webkitPerspective === undefined) _.animType = false;
  90. [Dòng 2267] bodyStyle.MozPerspective === undefined) _.animType = false;
  91. [Dòng 2279] if (bodyStyle.msTransform === undefined) _.animType = false;
  92. [Dòng 2306] var evenCoef = _.options.slidesToShow % 2 === 0 ? 1 : 0
  93. [Dòng 2328] if (index === 0) {
  94. [Dòng 2334] } else if (index === _.slideCount - 1) {
  95. [Dòng 2366] indexOffset = _.options.infinite === true ? _.options.slidesToShow + index : index
  96. [Dòng 2388] if (_.options.lazyLoad === 'ondemand'
  97. [Dòng 2388] _.options.lazyLoad === 'anticipated') {
  98. [Dòng 2402] if (_.options.infinite === true
  99. [Dòng 2402] _.options.fade === false) {
  100. [Dòng 2479] if (_.animating === true
  101. [Dòng 2479] _.options.waitForAnimate === true) {
  102. [Dòng 2483] if (_.options.fade === true
  103. [Dòng 2483] _.currentSlide === index) {
  104. [Dòng 2487] if (sync === false) {
  105. [Dòng 2495] _.currentLeft = _.swipeLeft === null ? slideLeft : _.swipeLeft
  106. [Dòng 2497] if (_.options.infinite === false
  107. [Dòng 2497] _.options.centerMode === false
  108. [Dòng 2509] } else if (_.options.infinite === false
  109. [Dòng 2509] _.options.centerMode === true
  110. [Dòng 2627] return (_.options.rtl === false ? 'left' : 'right');
  111. [Dòng 2633] return (_.options.rtl === false ? 'right' : 'left');
  112. [Dòng 2635] if (_.options.verticalSwiping === true) {
  113. [Dòng 2664] if ( _.touchObject.curX === undefined ) {
  114. [Dòng 2668] if ( _.touchObject.edgeHit === true ) {
  115. [Dòng 2732] if ((_.options.swipe === false) || ('ontouchend' in document
  116. [Dòng 2732] _.options.swipe === false)) {
  117. [Dòng 2734] } else if (_.options.draggable === false
  118. [Dòng 2806] positionOffset = (_.options.rtl === false ? 1 : -1) * (_.touchObject.curX > _.touchObject.startX ? 1 : -1);
  119. [Dòng 2817] if ((_.currentSlide === 0
  120. [Dòng 2817] swipeDirection === 'right') || (_.currentSlide >= _.getDotCount()
  121. [Dòng 2817] swipeDirection === 'left')) {
  122. [Dòng 2832] _.options.touchMove === false) {
  123. [Dòng 2836] if (_.animating === true) {
  124. [Dòng 2926] if ( _.options.arrows === true
  125. [Dòng 2933] if (_.currentSlide === 0) {
  126. [Dòng 2938] _.options.centerMode === false) {

== (5 điều kiện):
  1. [Dòng 2007] x = _.positionProp == 'left' ? Math.ceil(position) + 'px' : '0px'
  2. [Dòng 2008] y = _.positionProp == 'top' ? Math.ceil(position) + 'px' : '0px'
  3. [Dòng 2368] if (_.options.slidesToShow == _.options.slidesToScroll
  4. [Dòng 3002] if (typeof opt == 'object'
  5. [Dòng 3002] typeof opt == 'undefined')

!== (47 điều kiện):
  1. [Dòng 22] } else if (typeof exports !== 'undefined') {
  2. [Dòng 155] if (typeof document.mozHidden !== 'undefined') {
  3. [Dòng 158] } else if (typeof document.webkitHidden !== 'undefined') {
  4. [Dòng 342] asNavFor !== null ) {
  5. [Dòng 355] if ( asNavFor !== null
  6. [Dòng 460] if (_.options.infinite !== true) {
  7. [Dòng 612] _.options.responsive !== null) {
  8. [Dòng 630] if (targetBreakpoint !== null) {
  9. [Dòng 631] if (_.activeBreakpoint !== null) {
  10. [Dòng 632] if (targetBreakpoint !== _.activeBreakpoint
  11. [Dòng 676] triggerBreakpoint !== false ) {
  12. [Dòng 699] unevenOffset = (_.slideCount % _.options.slidesToScroll !== 0);
  13. [Dòng 758] _.$dots !== null) {
  14. [Dòng 997] if (filter !== null) {
  15. [Dòng 1103] if (_.slideCount % _.options.slidesToScroll !== 0) {
  16. [Dòng 1314] if (_.$dots !== null) {
  17. [Dòng 1324] if (slideControlIndex !== -1) {
  18. [Dòng 1910] _.currentSlide !== 0) {
  19. [Dòng 1953] if ($(window).width() !== _.windowWidth) {
  20. [Dòng 2144] } else if ( typeof arguments[1] !== 'undefined' ) {
  21. [Dòng 2170] if( $.type( _.options.responsive ) !== 'array' ) {
  22. [Dòng 2239] if (bodyStyle.WebkitTransition !== undefined
  23. [Dòng 2240] bodyStyle.MozTransition !== undefined
  24. [Dòng 2241] bodyStyle.msTransition !== undefined) {
  25. [Dòng 2257] if (bodyStyle.OTransform !== undefined) {
  26. [Dòng 2263] if (bodyStyle.MozTransform !== undefined) {
  27. [Dòng 2269] if (bodyStyle.webkitTransform !== undefined) {
  28. [Dòng 2275] if (bodyStyle.msTransform !== undefined) {
  29. [Dòng 2281] if (bodyStyle.transform !== undefined
  30. [Dòng 2281] _.animType !== false) {
  31. [Dòng 2286] _.transformsEnabled = _.options.useTransform && (_.animType !== null
  32. [Dòng 2286] _.animType !== false);
  33. [Dòng 2500] if (dontAnimate !== true
  34. [Dòng 2567] if (dontAnimate !== true) {
  35. [Dòng 2717] if ( _.touchObject.startX !== _.touchObject.curX ) {
  36. [Dòng 2734] event.type.indexOf('mouse') !== -1) {
  37. [Dòng 2738] event.originalEvent.touches !== undefined ?
  38. [Dòng 2773] touches = event.originalEvent !== undefined ? event.originalEvent.touches : null
  39. [Dòng 2775] touches.length !== 1) {
  40. [Dòng 2781] _.touchObject.curX = touches !== undefined ? touches[0].pageX : event.clientX
  41. [Dòng 2782] _.touchObject.curY = touches !== undefined ? touches[0].pageY : event.clientY
  42. [Dòng 2801] if (event.originalEvent !== undefined
  43. [Dòng 2852] if (_.touchObject.fingerCount !== 1
  44. [Dòng 2857] event.originalEvent.touches !== undefined) {
  45. [Dòng 2861] _.touchObject.startX = _.touchObject.curX = touches !== undefined ? touches.pageX : event.clientX
  46. [Dòng 2862] _.touchObject.startY = _.touchObject.curY = touches !== undefined ? touches.pageY : event.clientY
  47. [Dòng 2872] if (_.$slidesCache !== null) {

!= (4 điều kiện):
  1. [Dòng 805] $('[draggable!=true]', _.$slideTrack).off('dragstart', _.preventDefault);
  2. [Dòng 1467] $('[draggable!=true]', _.$slideTrack).on('dragstart', _.preventDefault);
  3. [Dòng 2707] if( direction != 'vertical' ) {
  4. [Dòng 3006] if (typeof ret != 'undefined') return ret;

================================================================================

📁 FILE 183: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 184: atm_b1.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_1/assets/js/atm_b1.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 228] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 234] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 258] if (paymentState === "authorization_required") {
  12. [Dòng 264] if (method === "REDIRECT") {
  13. [Dòng 267] } else if (method === "POST_REDIRECT") {
  14. [Dòng 306] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 289] if (jLinks !== undefined
  3. [Dòng 289] jLinks !== null) {
  4. [Dòng 291] if (jMerchantReturn !== undefined
  5. [Dòng 291] jMerchantReturn !== null) {
  6. [Dòng 306] if (responseCode !== undefined
  7. [Dòng 306] responseCode !== null
  8. [Dòng 334] if (parentRes !== "{

================================================================================

📁 FILE 185: atm_b1_2.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_1/atm_b1_2.js
📊 Thống kê: 11 điều kiện duy nhất
   - === : 8 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 52] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 55] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 64] year === y
  5. [Dòng 66] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 75] if ("PAY" === operation) {
  7. [Dòng 319] } else if (value === "") {
  8. [Dòng 336] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 77] if (e !== null) {
  2. [Dòng 253] if (lang !== "vi") lang = "en";
  3. [Dòng 283] if (value !== "") {

================================================================================

📁 FILE 186: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_1/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 187: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 188: atm_b2.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_2/assets/js/atm_b2.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 231] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 236] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 252] if (invoiceState === "unpaid"
  10. [Dòng 252] invoiceState === "not_paid") {
  11. [Dòng 256] if (paymentState === "authorization_required") {
  12. [Dòng 260] if (method === "REDIRECT") {
  13. [Dòng 263] } else if (method === "POST_REDIRECT") {
  14. [Dòng 302] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 285] if (jLinks !== undefined
  3. [Dòng 285] jLinks !== null) {
  4. [Dòng 287] if (jMerchantReturn !== undefined
  5. [Dòng 287] jMerchantReturn !== null) {
  6. [Dòng 302] if (responseCode !== undefined
  7. [Dòng 302] responseCode !== null
  8. [Dòng 330] if (parentRes !== "{

================================================================================

📁 FILE 189: atm_b2_2.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_2/atm_b2_2.js
📊 Thống kê: 6 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 41] if (inName.value === "") return err("MISSING_FIELD"
  3. [Dòng 51] if ("PAY" === operation) {

!== (3 điều kiện):
  1. [Dòng 53] if (e !== null) {
  2. [Dòng 229] if (lang !== "vi") lang = "en";
  3. [Dòng 259] if (value !== "") {

================================================================================

📁 FILE 190: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/atm_bank_2/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 191: common.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/common.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 42] if (i % 2 === parity) d *= 2;
  2. [Dòng 46] return (sum % 10) === 0
  3. [Dòng 116] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 119] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 205] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 211] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 227] if (xhr.status === 200
  8. [Dòng 227] xhr.status === 201) {
  9. [Dòng 230] if (invoiceState === "unpaid"
  10. [Dòng 230] invoiceState === "not_paid") {
  11. [Dòng 235] if (paymentState === "authorization_required") {
  12. [Dòng 241] if (method === "REDIRECT") {
  13. [Dòng 244] } else if (method === "POST_REDIRECT") {
  14. [Dòng 283] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 94] this.oldValue !== this.value) {
  2. [Dòng 266] if (jLinks !== undefined
  3. [Dòng 266] jLinks !== null) {
  4. [Dòng 268] if (jMerchantReturn !== undefined
  5. [Dòng 268] jMerchantReturn !== null) {
  6. [Dòng 283] if (responseCode !== undefined
  7. [Dòng 283] responseCode !== null
  8. [Dòng 311] if (parentRes !== "{

================================================================================

📁 FILE 192: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 193: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/international_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 194: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/begodi/international_card/script.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 12] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 22] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 25] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 37] year === y
  5. [Dòng 39] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 70] if ("PAY" === operation) {
  7. [Dòng 144] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 72] if (e !== null) {
  2. [Dòng 117] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 195: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/iframe/index.html
📊 Thống kê: 35 điều kiện duy nhất
   - === : 21 lần
   - == : 0 lần
   - !== : 14 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (21 điều kiện):
  1. [Dòng 10] if ((cardno.length === 15
  2. [Dòng 10] cardno.length === 16
  3. [Dòng 10] cardno.length === 19) && isMode10(cardno) === true) {
  4. [Dòng 10] isMode10(cardno) === true) {
  5. [Dòng 29] if (i % 2 === parity) d *= 2;
  6. [Dòng 33] return (sum % 10) === 0
  7. [Dòng 54] if ("PAY" === operation) {
  8. [Dòng 75] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  9. [Dòng 81] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  10. [Dòng 97] if (xhr.status === 200
  11. [Dòng 97] xhr.status === 201) {
  12. [Dòng 100] if (invoiceState === "unpaid"
  13. [Dòng 100] invoiceState === "not_paid") {
  14. [Dòng 105] if (paymentState === "authorization_required") {
  15. [Dòng 111] if (method === "REDIRECT") {
  16. [Dòng 116] } else if (method === "POST_REDIRECT") {
  17. [Dòng 183] //Customizable =================================================================================================
  18. [Dòng 229] if (typeof queryParams[key] === "undefined") {
  19. [Dòng 232] } else if (typeof queryParams[key] === "string") {
  20. [Dòng 246] if (params["CardList"] === undefined
  21. [Dòng 246] params["CardList"] === null) return;

!== (14 điều kiện):
  1. [Dòng 40] //if (event.origin !== "http://example.com:8080") return;
  2. [Dòng 58] if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
  3. [Dòng 59] if (inNumber.style.display !== "none") instrument.number = inNumber.value;
  4. [Dòng 60] if (inDate.style.display !== "none") instrument.date = inDate.value;
  5. [Dòng 61] if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
  6. [Dòng 62] if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
  7. [Dòng 63] if (inName.style.display !== "none") instrument.name = inName.value;
  8. [Dòng 78] /*if (contentType !== my_expected_type) {
  9. [Dòng 138] if (jLinks !== undefined
  10. [Dòng 138] jLinks !== null) {
  11. [Dòng 140] if (jMerchantReturn !== undefined
  12. [Dòng 140] jMerchantReturn !== null) {
  13. [Dòng 172] if (parentRes !== "{
  14. [Dòng 245] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 196: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 197: karma.conf.js
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/karma.conf.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 198: main.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/main.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 199: polyfills.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/polyfills.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 200: test.ts
📍 Đường dẫn: /root/projects/onepay/paygate-megags/src/test.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📋 TÓM TẮT TẤT CẢ ĐIỀU KIỆN DUY NHẤT:
====================================================================================================

=== (493 điều kiện duy nhất):
------------------------------------------------------------
1. return lang === this._translate.currentLang
2. isPopupSupport === 'True'"
3. params.timeout === 'true') {
4. this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
5. _re.body.state === 'unpaid');
6. if (this.errorCode === 'overtime'
7. this.errorCode === '253') {
8. if (this.timeLeft === 0) {
9. if (YY % 400 === 0
10. YY % 4 === 0)) {
11. if (YYYY % 400 === 0
12. YYYY % 4 === 0)) {
13. <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
14. [class.red_border_no_background]="c_expdate && (valueDate.length === 0
15. valueDate.trim().length === 0)"
16. if (target.tagName === 'A'
17. if (isIE[0] === 'MSIE'
18. +isIE[1] === 10) {
19. if ((_val.value.substr(-1) === ' '
20. _val.value.length === 24) {
21. if (this.cardTypeBank === 'bank_card_number') {
22. } else if (this.cardTypeBank === 'bank_account_number') {
23. } else if (this.cardTypeBank === 'bank_username') {
24. } else if (this.cardTypeBank === 'bank_customer_code') {
25. this.cardTypeBank === 'bank_card_number'
26. if (this.cardTypeOcean === 'IB') {
27. } else if (this.cardTypeOcean === 'MB') {
28. if (_val.value.substr(0, 2) === '84') {
29. } else if (this.cardTypeOcean === 'ATM') {
30. if (this.cardTypeBank === 'bank_account_number') {
31. this.cardTypeBank === 'bank_card_number') {
32. if (this.cardTypeBank === 'bank_card_number'
33. if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
34. if (event.keyCode === 8
35. event.key === "Backspace"
36. if (v.length === 2
37. this.flag.length === 3
38. this.flag.charAt(this.flag.length - 1) === '/') {
39. if (v.length === 1) {
40. } else if (v.length === 2) {
41. v.length === 2) {
42. if (len === 2) {
43. if ((this.cardTypeBank === 'bank_account_number'
44. this.cardTypeBank === 'bank_username'
45. this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
46. this.cardTypeOcean === 'ATM')
47. || (this.cardTypeOcean === 'IB'
48. if (valIn === this._translate.instant('bank_card_number')) {
49. } else if (valIn === this._translate.instant('bank_account_number')) {
50. } else if (valIn === this._translate.instant('bank_username')) {
51. } else if (valIn === this._translate.instant('bank_customer_code')) {
52. if (_val.value === ''
53. _val.value === null
54. _val.value === undefined) {
55. if (_val.value && (this.cardTypeBank === 'bank_card_number'
56. if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
57. this.cardTypeOcean === 'MB') {
58. this.cardTypeOcean === 'IB'
59. if ((this.cardTypeBank === 'bank_card_number'
60. if (this.cardName === undefined
61. this.cardName === '') {
62. if (this.valueDate === undefined
63. this.valueDate === '') {
64. c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
65. _inExpDate.trim().length === 0)"
66. if (focusElement === 'card_name') {
67. } else if (focusElement === 'exp_date'
68. focusExpDateElement === 'card_name') {
69. if (this.cardTypeBank === 'bank_account_number'
70. if (approval.method === 'REDIRECT') {
71. } else if (approval.method === 'POST_REDIRECT') {
72. filteredData.length === 0"
73. if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
74. filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
75. if (valOut === 'auth') {
76. if (this._b === '1'
77. this._b === '20'
78. this._b === '64') {
79. if (this._b === '36'
80. this._b === '18'
81. this._b === '19'
82. if (this._b === '19'
83. this._b === '16'
84. this._b === '25'
85. this._b === '33'
86. this._b === '39'
87. this._b === '9'
88. this._b === '11'
89. this._b === '17'
90. this._b === '36'
91. this._b === '44'
92. this._b === '64'
93. if (this._b === '20'
94. if (this._b === '18') {
95. if (_formCard.country === 'default') {
96. if ((v.substr(-1) === ' '
97. if (deviceValue === 'CA'
98. deviceValue === 'US') {
99. this.c_country = _val.value === 'default'
100. d_vrbank===true"
101. this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number === 1
102. if (this._res.state === 'unpaid'
103. this._res.state === 'not_paid') {
104. if ('op' === auth
105. } else if ('bank' === auth
106. return id === 'amex' ? '1234' : '123'
107. if (this.timeLeftPaypal === 0) {
108. filteredData.length === 0
109. filteredDataOther.length === 0"
110. filteredDataMobile.length === 0
111. filteredDataOtherMobile.length === 0"
112. this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
113. this.filteredDataOther = this.appList.filter(item => item.type === 'other');
114. this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
115. this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
116. this.appList.length === 1) {
117. if ((this.filteredDataMobile.length === 1
118. this.filteredDataOtherMobile.length === 1)
119. item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
120. filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
121. if (item.type === 'mobile_banking') {
122. this.appList.length === 1
123. listVNPayQR.length === 0"
124. this.listWalletQR.length === 1) {
125. this.listWalletQR.length === 1
126. if (!this.d_vnpayqr_wallet && !this.d_vnpayqr_deeplink && (this.listWalletQR?.length === 1
127. this.listWalletDeeplink?.length === 1)) {
128. if (item.type === 'deeplink') {
129. this.listWalletQR?.length === 1
130. type === 2"
131. [ngStyle]="{'border-color': type === 2 ? this.themeConfig.border_color : border_color}"
132. *ngIf="((type === 2
133. type === '2'
134. type === 1"
135. [ngStyle]="{'border-color': type === 1 ? this.themeConfig.border_color : border_color}"
136. [ngStyle]="{'border-color': type === 5 ? this.themeConfig.border_color : border_color}"
137. type === 5"
138. type === 3"
139. [ngStyle]="{'border-color': type === 3 ? this.themeConfig.border_color : border_color}"
140. type === 4"
141. [ngStyle]="{'border-color': type === 4 ? this.themeConfig.border_color : border_color}"
142. type === 4
143. style="{{item.brand_id === 'visa' ? 'height: 14.42px
144. if (event.keyCode === 13) {
145. && ((item.brand_id === 'amex'
146. return this._i_token_otp != null && !isNaN(+this._i_token_otp) && ((id === 'amex'
147. const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
148. item.method === method) : null;
149. err?.status === 400
150. err?.error?.name === 'INVALID_CARD_FEE'
151. if (M[1] === 'Chrome') {
152. if (param === key) {
153. pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
154. target === 0
155. if (cardTypeBank === 'bank_card_number') {
156. } else if (cardTypeBank === 'bank_account_number') {
157. if (typeof define === 'function'
158. } else if (typeof exports === 'object') {
159. if (number === "") return err("MISSING_FIELD"
160. if (inName.value === "") return err("MISSING_FIELD"
161. if ("PAY" === operation) {
162. if (i % 2 === parity) d *= 2;
163. return (sum % 10) === 0
164. if (typeof queryParams[key] === "undefined") {
165. } else if (typeof queryParams[key] === "string") {
166. if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
167. } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
168. if (xhr.status === 200
169. xhr.status === 201) {
170. if (invoiceState === "unpaid"
171. invoiceState === "not_paid") {
172. if (paymentState === "authorization_required") {
173. if (method === "REDIRECT") {
174. } else if (method === "POST_REDIRECT") {
175. responseCode === "0") {
176. if (inMonth.value === "") return err("MISSING_FIELD"
177. if (inYear.value === "") return err("MISSING_FIELD"
178. year === y
179. if (inPhone.value === "") return err("MISSING_FIELD"
180. } else if (value === "") {
181. if (trPhone.style.display === "") {
182. } else if (trName.style.display === "") {
183. if (trName.style.display === "") {
184. if (xhr.status === 200) {
185. if (insType === "card") {
186. if (insBrandId === "visa"
187. insBrandId === "mastercard"
188. insBrandId === "amex"
189. insBrandId === "jcb"
190. insBrandId === "cup") {
191. } else if (insBrandId === "atm") {
192. } else if (insType === "dongabank_account") {
193. } else if (insType === "techcombank_account") {
194. } else if (insType === "vib_account") {
195. } else if (insType === "bidv_account") {
196. } else if (insType === "tpbank_account") {
197. } else if (insType === "shb_account") {
198. } else if (insType === "shb_customer_id") {
199. } else if (insType === "vpbank_account") {
200. } else if (insType === "oceanbank_online_account") {
201. } else if (insType === "oceanbank_mobile_account") {
202. } else if (insType === "pvcombank_account") {
203. if (inCvv.value === "") return err("MISSING_FIELD"
204. if (inCvv.value === "") {
205. if ((cardno.length === 15
206. cardno.length === 16
207. cardno.length === 19) && isMode10(cardno) === true) {
208. isMode10(cardno) === true) {
209. if (params["CardList"] === undefined
210. params["CardList"] === null) return;
211. if (insSwiftCode === "BFTVVNVX") { //Vietcombank
212. } else if (insSwiftCode === "BIDVVNVX") { //BIDV
213. typeof exports === 'object'
214. typeof define === 'function'
215. selector === '#') {
216. if (typeof element.getRootNode === 'function') {
217. if (typeof $ === 'undefined') {
218. version[0] === minMajor
219. version[1] === minMinor
220. if (config === 'close') {
221. if (input.type === 'radio') {
222. } else if (input.type === 'checkbox') {
223. if (this._element.tagName === 'LABEL'
224. input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
225. if (config === 'toggle') {
226. if (_button.getAttribute('aria-pressed') === 'true') {
227. if (activeIndex === index) {
228. if (this._config.pause === 'hover') {
229. if (_this3._config.pause === 'hover') {
230. var isNextDirection = direction === Direction.NEXT
231. var isPrevDirection = direction === Direction.PREV
232. activeIndex === 0
233. activeIndex === lastItemIndex
234. var delta = direction === Direction.PREV ? -1 : 1
235. return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
236. if (direction === Direction.NEXT) {
237. if (typeof config === 'object') {
238. var action = typeof config === 'string' ? config : _config.slide
239. if (typeof config === 'number') {
240. } else if (typeof action === 'string') {
241. if (typeof data[action] === 'undefined') {
242. return foundElem === element
243. if (typeof _this._config.parent === 'string') {
244. return elem.getAttribute('data-parent') === _this._config.parent
245. if (actives.length === 0) {
246. typeof config === 'object'
247. if (typeof config === 'string') {
248. if (typeof data[config] === 'undefined') {
249. if (event.currentTarget.tagName === 'A') {
250. if (usePopper === void 0) {
251. if (typeof Popper === 'undefined') {
252. if (this._config.reference === 'parent') {
253. $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
254. if (typeof this._config.offset === 'function') {
255. if (this._config.display === 'static') {
256. var _config = typeof config === 'object' ? config : null
257. if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
258. event.type === 'keyup'
259. event.type === 'click') {
260. if (event && (event.type === 'click'
261. event.which === TAB_KEYCODE) && $.contains(parent
262. if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
263. event.which === ESCAPE_KEYCODE) {
264. if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
265. event.which === SPACE_KEYCODE)) {
266. if (event.which === ESCAPE_KEYCODE) {
267. if (items.length === 0) {
268. if (event.which === ARROW_UP_KEYCODE
269. if (event.which === ARROW_DOWN_KEYCODE
270. if (this._config.backdrop === 'static') {
271. $(_this5._element).has(event.target).length === 0) {
272. if (event.which === ESCAPE_KEYCODE$1) {
273. if (this.tagName === 'A'
274. this.tagName === 'AREA') {
275. if (unsafeHtml.length === 0) {
276. typeof sanitizeFn === 'function') {
277. if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
278. if (_ret === "continue") continue;
279. if ($(this.element).css('display') === 'none') {
280. var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
281. if (prevHoverState === HoverState.OUT) {
282. if (typeof content === 'object'
283. title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
284. if (typeof this.config.offset === 'function') {
285. if (this.config.container === false) {
286. if (trigger === 'click') {
287. var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
288. var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
289. context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
290. context._hoverState === HoverState.SHOW) {
291. if (context._hoverState === HoverState.SHOW) {
292. context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
293. if (context._hoverState === HoverState.OUT) {
294. if (typeof config.delay === 'number') {
295. if (typeof config.title === 'number') {
296. if (typeof config.content === 'number') {
297. var _config = typeof config === 'object'
298. if (typeof content === 'function') {
299. this._scrollElement = element.tagName === 'BODY' ? window : element
300. var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
301. var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
302. var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
303. return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
304. return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
305. var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
306. this._element.parentNode.nodeType === Node.ELEMENT_NODE
307. var itemSelector = listElement.nodeName === 'UL'
308. listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
309. var activeElements = container && (container.nodeName === 'UL'
310. container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
311. if (active.getAttribute('role') === 'tab') {
312. if (element.getAttribute('role') === 'tab') {
313. 1>t)throw Error("numRounds must a integer >= 1");if("SHA-1"===c)m=512,q=K,u=Z,f=160,r=function(a){return a.slice()};else if(0===c.lastIndexOf("SHA-",0))if(q=function(a,b){return L(a,b,c)
314. c)},u=function(a,b,h,e){var k,f;if("SHA-224"===c
315. "SHA-256"===c)k=(b+65>>>9<<4)+15,f=16;else if("SHA-384"===c
316. "SHA-512"===c)k=(b+129>>>10<<
317. c);if("SHA-224"===c)a=[e[0],e[1],e[2],e[3],e[4],e[5],e[6]];else if("SHA-256"===c)a=e;else if("SHA-384"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,e[4].b,e[5].a,e[5].b];else if("SHA-512"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,
318. "SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)
319. 0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
320. return a},r=function(a){return a.slice()},"SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)||0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
321. c)m=1152,f=224;else if("SHA3-256"===c)m=1088,f=256;else if("SHA3-384"===c)m=832,f=384;else if("SHA3-512"===c)m=576,f=512;else if("SHAKE128"===c)m=1344,f=-1,F=31,z=!0;else if("SHAKE256"===c)m=1088,f=-1,F=31,z=!0;else throw Error("Chosen SHA variant is not supported");u=function(a
322. 0===64*l%e
323. 5][l/5|0];g.push(a.b);if(32*g.length>=h)break;g.push(a.a);l+=1;0===64*l%e&&D(null,b)}return g}}else throw Error("Chosen SHA variant is not supported");d=M(a,g,x);l=A(c);this.setHMACKey=function(a,b,h){var k;if(!0===I)throw Error("HMAC key already set");if(!0===y)throw Error("Cannot set HMAC key after calling update");if(!0===z)throw Error("SHAKE is not supported for HMAC");g=(h
324. f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
325. )b.push(0);b[h]&=4294967040}for(a=0;a<=h;a+=1)v[a]=b[a]^909522486,w[a]=b[a]^1549556828;l=q(v,l);e=m;I=!0};this.update=function(a){var c,b,k,f=0,g=m>>>5;c=d(a,h,n);a=c.binLen;b=c.value;c=a>>>5;for(k=0;k<c;k+=g)f+m<=a&&(l=q(b.slice(k,k+g),l),f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
326. for(g=1;g<t;g+=1)!0===z
327. f);return k(m)};this.getHMAC=function(a,b){var k,g,d,p;if(!1===I)throw Error("Cannot call getHMAC without first setting HMAC key");d=N(b);switch(a){case "HEX":k=function(a){return O(a,f,x,d)};break;case "B64":k=function(a){return P(a,f,x,d)};break;case "BYTES":k=function(a){return Q(a,f,x)};break;case "ARRAYBUFFER":try{k=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}k=function(a){return R(a,f,x)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
328. d=-1===b?3:0
329. f=-1===b?3:0
330. g=-1===b?3:0
331. !0===c.hasOwnProperty("b64Pad")
332. a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");
333. u=-1===b?3:0
334. l+=2){p=parseInt(a.substr(l,2),16);if(isNaN(p))throw Error("String of HEX type contains invalid characters");m=(l>>>1)+q;for(f=m>>>2;c.length<=f;)c.push(0);c[f]|=p<<8*(u+m%4*b)}return{value:c,binLen:4*g+d}};break;case "TEXT":c=function(c,h,d){var g,l,p=0,f,m,q,u,r,t;h=h||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(t=-1===
335. m+=1){r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=l[m]<<8*(t+r%4*b);p+=1}else if("UTF16BE"===a
336. "UTF16LE"===a)for(t=-1===b?2:0
337. UTF16LE"===a
338. 1===b
339. !0===l
340. !0===l&&(m=g&255,g=m<<8|g>>>8);r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=g<<8*(t+r%4*b);p+=2}return{value:h,binLen:8*p+d}};break;case "B64":c=function(a,c,d){var g=0,l,p,f,m,q,u,r,t;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");p=a.indexOf("=");a=a.replace(/\=/g
341. t=-1===b?3:0
342. q=-1===b?3:0
343. m=-1===b?3:0
344. c.b^a.b)}function A(c){var a=[],d;if("SHA-1"===c)a=[1732584193,4023233417,2562383102,271733878,3285377520];else if(0===c.lastIndexOf("SHA-",0))switch(a=
345. new b(d[2],4271175723),new b(d[3],1595750129),new b(d[4],2917565137),new b(d[5],725511199),new b(d[6],4215389547),new b(d[7],327033209)];break;default:throw Error("Unknown SHA variant");}else if(0===c.lastIndexOf("SHA3-",0)
346. 0===c.lastIndexOf("SHAKE",0))for(c=0
347. a,k){var e,h,n,g,l,p,f,m,q,u,r,t,v,w,y,A,z,x,F,B,C,D,E=[],J;if("SHA-224"===k
348. "SHA-256"===k)u=64,t=1,D=Number,v=G,w=la,y=H,A=ha,z=ja,x=da,F=fa,C=U,B=aa,J=d;else if("SHA-384"===k
349. "SHA-512"===k)u=80,t=2,D=b,v=ma,w=na,y=oa,A=ia,z=ka,x=ea,F=ga,C=ca,B=ba,J=V;else throw Error("Unexpected error in SHA-2 implementation");k=a[0];e=a[1];h=a[2];n=a[3];g=a[4];l=a[5];p=a[6];f=a[7];for(r=0
350. "function"===typeof define
351. 1>u)throw Error("numRounds must a integer >= 1");if(0===c.lastIndexOf("SHA-",0))if(q=function(b,a){return A(b,a,c)
352. c)},y=function(b,a,l,f){var g,e;if("SHA-224"===c
353. "SHA-256"===c)g=(a+65>>>9<<4)+15,e=16;else throw Error("Unexpected error in SHA-2 implementation");for(
354. c);if("SHA-224"===c)b=[f[0],f[1],f[2],f[3],f[4],f[5],f[6]];else if("SHA-256"===c)b=f;else throw Error("Unexpected error in SHA-2 implementation");return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
355. "SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a
356. return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
357. if(!0===z)throw Error("Cannot set HMAC key after calling update");f=(g
358. 5);g=a%h;z=!0};this.getHash=function(a,f){var d,h,k,q;if(!0===m)throw Error("Cannot call getHash after setting HMAC key");k=C(f);switch(a){case "HEX":d=function(a){return D(a,e,k)};break;case "B64":d=function(a){return E(a,e,k)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{h=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("format must be HEX, B64, BYTES, or ARRAYBUFFER");
359. x(c));return d(q)};this.getHMAC=function(a,f){var d,k,t,u;if(!1===m)throw Error("Cannot call getHMAC without first setting HMAC key");t=C(f);switch(a){case "HEX":d=function(a){return D(a,e,t)};break;case "B64":d=function(a){return E(a,e,t)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{d=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
360. h=(d>>>1)+q;for(e=h>>>2;b.length<=e;)b.push(0);b[e]|=k<<8*(3+h%4*-1)}return{value:b,binLen:4*f+c}};break;case "TEXT":d=function(c,b,d){var f,n,k=0,e,h,q,m,p,r;b=b||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(r=3
361. q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=n[h]<<8*(r+p%4*-1);k+=1}else if("UTF16BE"===a
362. "UTF16LE"===a)for(r=2
363. !0===n
364. e+=1){f=c.charCodeAt(e);!0===n&&(h=f&255,f=h<<8|f>>>8);p=k+q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=f<<8*(r+p%4*-1);k+=2}return{value:b,binLen:8*k+d}};break;case "B64":d=function(a,b,c){var f=0,d,k,e,h,q,m,p;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");k=a.indexOf("=");a=a.replace(/\=/g
365. 16)&65535)<<16|b&65535}function R(c,a,d,l,b){var g=(c&65535)+(a&65535)+(d&65535)+(l&65535)+(b&65535);return((c>>>16)+(a>>>16)+(d>>>16)+(l>>>16)+(b>>>16)+(g>>>16)&65535)<<16|g&65535}function x(c){var a=[],d;if(0===c.lastIndexOf("SHA-",0))switch(a=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428]
366. new m,new m,new m,new m,new m,new m];break;case "SHA-512":a=[new m,new m,new m,new m,new m,new m,new m,new m];break;default:throw Error("Unknown SHA variant");}else throw Error("No SHA variants supported");return a}function A(c,a,d){var l,b,g,f,n,k,e,h,m,r,p,w,t,x,u,z,A,B,C,D,E,F,v=[],G;if("SHA-224"===d
367. "SHA-256"===d)r=64,w=1,F=Number,t=P,x=Q,u=R,z=N,A=O,B=L,C=M,E=K,D=J,G=H;else throw Error("Unexpected error in SHA-2 implementation");d=a[0];l=a[1];b=a[2];g=a[3];f=a[4];n=a[5];k=a[6];e=a[7];for(p=
368. if (typeof(index) === 'boolean') {
369. if (typeof(index) === 'number') {
370. if (index === 0
371. _.$slides.length === 0) {
372. if (addBefore === true) {
373. if (_.options.slidesToShow === 1
374. _.options.adaptiveHeight === true
375. _.options.vertical === false) {
376. if (_.options.rtl === true
377. if (_.transformsEnabled === false) {
378. if (_.options.vertical === false) {
379. if (_.cssTransitions === false) {
380. if (_.options.rtl === true) {
381. typeof asNavFor === 'object' ) {
382. if (_.options.fade === false) {
383. if ( _.options.infinite === false ) {
384. if ( _.direction === 1
385. ( _.currentSlide + 1 ) === ( _.slideCount - 1 )) {
386. else if ( _.direction === 0 ) {
387. if ( _.currentSlide - 1 === 0 ) {
388. if (_.options.arrows === true ) {
389. if (_.options.dots === true
390. _.$slideTrack = (_.slideCount === 0) ?
391. if (_.options.centerMode === true
392. _.options.swipeToSlide === true) {
393. _.setSlideClasses(typeof _.currentSlide === 'number' ? _.currentSlide : 0);
394. if (_.options.draggable === true) {
395. if (_.respondTo === 'window') {
396. } else if (_.respondTo === 'slider') {
397. } else if (_.respondTo === 'min') {
398. if (_.originalSettings.mobileFirst === false) {
399. if (_.breakpointSettings[targetBreakpoint] === 'unslick') {
400. if (initial === true) {
401. slideOffset = indexOffset === 0 ? _.options.slidesToScroll : _.options.slidesToShow - indexOffset
402. slideOffset = indexOffset === 0 ? _.options.slidesToScroll : indexOffset
403. var index = event.data.index === 0 ? 0 :
404. if (_.options.accessibility === true) {
405. if (_.options.arrows === true
406. if (_.options.focusOnSelect === true) {
407. if (_.shouldClick === false) {
408. if (_.options.infinite === true) {
409. } else if (_.options.centerMode === true) {
410. if (_.options.vertical === true
411. _.options.centerMode === true) {
412. if (_.options.slidesToShow === 2) {
413. } else if (_.options.slidesToShow === 1) {
414. } else if (_.options.centerMode === true
415. _.options.infinite === true) {
416. if (_.options.variableWidth === true) {
417. _.options.infinite === false) {
418. if (_.options.centerMode === true) {
419. if (_.options.infinite === false) {
420. centerOffset = _.options.centerMode === true ? _.slideWidth * Math.floor(_.options.slidesToShow / 2) : 0
421. if (_.options.swipeToSlide === true) {
422. _.options.pauseOnDotsHover === true
423. if (event.keyCode === 37
424. _.options.accessibility === true) {
425. message: _.options.rtl === true ? 'next' :
426. } else if (event.keyCode === 39
427. message: _.options.rtl === true ? 'previous' : 'next'
428. if (_.options.fade === true) {
429. if (_.options.lazyLoad === 'anticipated') {
430. } else if (_.currentSlide === 0) {
431. if (_.options.lazyLoad === 'progressive') {
432. if ( _.options.adaptiveHeight === true ) {
433. if ( $.type(responsiveSettings) === 'array'
434. _.breakpoints[l] === currentBreakpoint ) {
435. index = removeBefore === true ? 0 : _.slideCount - 1
436. index = removeBefore === true ? --index : index
437. if (removeAll === true) {
438. if (_.options.vertical === false
439. _.options.variableWidth === false) {
440. } else if (_.options.variableWidth === true) {
441. if (_.options.variableWidth === false) _.$slideTrack.children('.slick-slide').width(_.slideWidth - offset);
442. if( $.type( arguments[0] ) === 'object' ) {
443. } else if ( $.type( arguments[0] ) === 'string' ) {
444. if ( arguments[0] === 'responsive'
445. $.type( arguments[1] ) === 'array' ) {
446. if ( type === 'single' ) {
447. } else if ( type === 'multiple' ) {
448. } else if ( type === 'responsive' ) {
449. if( _.options.responsive[l].breakpoint === value[item].breakpoint ) {
450. _.positionProp = _.options.vertical === true ? 'top' : 'left'
451. if (_.positionProp === 'top') {
452. if (_.options.useCSS === true) {
453. if ( typeof _.options.zIndex === 'number' ) {
454. if (bodyStyle.perspectiveProperty === undefined
455. bodyStyle.webkitPerspective === undefined) _.animType = false;
456. bodyStyle.MozPerspective === undefined) _.animType = false;
457. if (bodyStyle.msTransform === undefined) _.animType = false;
458. var evenCoef = _.options.slidesToShow % 2 === 0 ? 1 : 0
459. if (index === 0) {
460. } else if (index === _.slideCount - 1) {
461. indexOffset = _.options.infinite === true ? _.options.slidesToShow + index : index
462. if (_.options.lazyLoad === 'ondemand'
463. _.options.lazyLoad === 'anticipated') {
464. if (_.options.infinite === true
465. _.options.fade === false) {
466. if (_.animating === true
467. _.options.waitForAnimate === true) {
468. if (_.options.fade === true
469. _.currentSlide === index) {
470. if (sync === false) {
471. _.currentLeft = _.swipeLeft === null ? slideLeft : _.swipeLeft
472. if (_.options.infinite === false
473. _.options.centerMode === false
474. } else if (_.options.infinite === false
475. _.options.centerMode === true
476. return (_.options.rtl === false ? 'left' : 'right');
477. return (_.options.rtl === false ? 'right' : 'left');
478. if (_.options.verticalSwiping === true) {
479. if ( _.touchObject.curX === undefined ) {
480. if ( _.touchObject.edgeHit === true ) {
481. if ((_.options.swipe === false) || ('ontouchend' in document
482. _.options.swipe === false)) {
483. } else if (_.options.draggable === false
484. positionOffset = (_.options.rtl === false ? 1 : -1) * (_.touchObject.curX > _.touchObject.startX ? 1 : -1);
485. if ((_.currentSlide === 0
486. swipeDirection === 'right') || (_.currentSlide >= _.getDotCount()
487. swipeDirection === 'left')) {
488. _.options.touchMove === false) {
489. if (_.animating === true) {
490. if ( _.options.arrows === true
491. if (_.currentSlide === 0) {
492. _.options.centerMode === false) {
493. //Customizable =================================================================================================

== (523 điều kiện duy nhất):
------------------------------------------------------------
1. 'vi' == params['locale']) {
2. 'en' == params['locale']) {
3. if (_re.status == '200'
4. _re.status == '201') {
5. errorCode == '11'"
6. isSent == false
7. <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
8. (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
9. <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
10. !(errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
11. _re.body.themes.theme == 'token') {
12. params.response_code == 'overtime') {
13. if (_re2.status == '200'
14. _re2.status == '201') {
15. if (this.errorCode == 'overtime'
16. this.errorCode == '253') {
17. } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
18. if (lastPayment?.state == 'pending') {
19. if (this.isTimePause == false) {
20. if ((dataPassed.status == '200'
21. dataPassed.status == '201') && dataPassed.body != null) {
22. account_amain == '1'"
23. if (this.locale == 'en') {
24. if (name == 'MAFC')
25. if (this._b == 18
26. this._b == 19) {
27. if (this._b == 19) {//19BIDV
28. } else if (this._b == 3
29. this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
30. if (this._b == 27) {
31. } else if (this._b == 12) {// 12SHB
32. } else if (this._b == 18) { //18Oceanbank-ocb
33. if (this._b == 19
34. this._b == 3
35. this._b == 27
36. this._b == 12) {
37. } else if (this._b == 18) {
38. if (this.checkBin(_val.value) && (this._b == 3
39. this._b == 27)) {
40. if (this._b == 3) {
41. this.cardTypeOcean == 'ATM') {
42. if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
43. this._b == 18)) {
44. if (this.checkBin(v) && (this._b == 3
45. event.inputType == 'deleteContentBackward') {
46. if (event.target.name == 'exp_date'
47. event.inputType == 'insertCompositionText') {
48. if (((this.valueDate.length == 4
49. this.valueDate.search('/') == -1) || this.valueDate.length == 5)
50. this.valueDate.length == 5)
51. if (temp.length == 0) {
52. return (counter % 10 == 0);
53. } else if (this._b == 19) {
54. } else if (this._b == 27) {
55. if (this._b == 12) {
56. if (this.cardTypeBank == 'bank_customer_code') {
57. } else if (this.cardTypeBank == 'bank_account_number') {
58. _formCard.exp_date.length == 5
59. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
60. this._b == 3)) {//27-pvcombank;3-TPB ;
61. if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
62. this._b == 19
63. this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
64. if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
65. if (this.cardTypeOcean == 'IB') {
66. } else if (this.cardTypeOcean == 'MB') {
67. } else if (this.cardTypeOcean == 'ATM') {
68. if (this._res_post.state == 'approved'
69. this._res_post.state == 'failed') {
70. } else if (this._res_post.state == 'authorization_required') {
71. if (this._b == 18) {
72. if (this._b == 27
73. this._b == 18) {
74. if ((cardNo.length == 16
75. if ((cardNo.length == 16 || (cardNo.length == 19
76. && ((this._b == 18
77. cardNo.length == 19) || this._b != 18)
78. if (this._b == +e.id) {
79. if (valIn == 1) {
80. } else if (valIn == 2) {
81. this._b == 3) {
82. if (this._b == 19) {
83. if (cardType == this._translate.instant('internetbanking')
84. } else if (cardType == this._translate.instant('mobilebanking')
85. } else if (cardType == this._translate.instant('atm')
86. this._b == 18))) {
87. } else if (this._b == 18
88. this.c_expdate = !(((this.valueDate.length == 4
89. this.valueDate.length == 4
90. this.valueDate.search('/') == -1)
91. this.valueDate.length == 5))
92. if (this._b == 8) {//MB Bank
93. if (this._b == 18) {//Oceanbank
94. if (this._b == 8) {
95. if (this._b == 12) { //SHB
96. } else if (this._res.state == 'authorization_required') {
97. if (this.challengeCode == '') {
98. if (this._b == 18) {//8-MB Bank;18-oceanbank
99. <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_card_number')">
100. <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_account_number')">
101. if(this._b == 12) this.isShbGroup = true;
102. return this._b == 9
103. this._b == 11
104. this._b == 16
105. this._b == 17
106. this._b == 25
107. this._b == 44
108. this._b == 57
109. this._b == 59
110. this._b == 61
111. this._b == 63
112. this._b == 69
113. if (this._b == 12
114. this.cardTypeBank == 'bank_account_number') {
115. _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
116. cardNo.length == 19) && cardNo.startsWith("970443") && this._util.checkMod10(cardNo) && this.checkBin(cardNo) ) {
117. if (this._b == 2
118. this._b == 31) {
119. if (this._b == 2) {
120. } else if (this._b == 6) {
121. } else if (this._b == 31) {
122. if (this._b == 5) {//5-vib;
123. if (this._b == 5) {
124. if (this.checkBin(_val.value) && (this._b == 5)) {
125. if (this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
126. if (this.checkBin(v) && (this._b == 5)) {
127. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 5)) {//27-pvcombank;3-TPB ;
128. if (this.cardName != null && this.cardName.length > 0 && (this._b == 5)) {//3-TPB ;19-BIDV;27-pvcombank;
129. _b==68"
130. if (this._b == 1
131. this._b == 20
132. this._b == 36
133. this._b == 64
134. this._b == 55
135. this._b == 47
136. this._b == 48
137. this._b == 59) {
138. this._b == 54
139. return this._b == 11
140. this._b == 33
141. this._b == 39
142. this._b == 43
143. this._b == 45
144. this._b == 67
145. this._b == 72
146. this._b == 73
147. this._b == 68
148. this._b == 74
149. this._b == 75
150. this._b == 14
151. this._b == 15
152. this._b == 24
153. this._b == 8
154. this._b == 10
155. this._b == 22
156. this._b == 23
157. this._b == 30
158. this._b == 9) {
159. (cardNo.length == 19
160. (cardNo.length == 19 && (this._b == 1
161. this._b == 4
162. this._b == 59))
163. this._util.checkMod10(cardNo) == true
164. return ((value.length == 4
165. value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
166. value.length == 5) && parseInt(value.split('/')[0]
167. this._inExpDate.length == 4
168. this._inExpDate.search('/') == -1)
169. this._inExpDate.length == 5))
170. _auth==0
171. *ngIf="(!token&&_auth==0&&(_b==1
172. _b==4
173. _b==7
174. _b==8
175. _b==9
176. _b==10
177. _b==11
178. _b==14
179. _b==15
180. _b==16
181. _b==17
182. _b==20
183. _b==22
184. _b==23
185. _b==24
186. _b==25
187. _b==30
188. _b==33
189. _b==34
190. _b==35
191. _b==36
192. _b==37
193. _b==38
194. _b==39
195. _b==40
196. _b==41
197. _b==42
198. _b==43
199. _b==44
200. _b==45
201. this._b==46
202. _b==47
203. _b==48
204. _b==49
205. _b==50
206. _b==51
207. _b==52
208. _b==53
209. _b==54
210. _b==55
211. _b==56
212. _b==57
213. _b==58
214. _b==59
215. _b==60
216. _b==61
217. _b==62
218. _b==63
219. _b==64
220. this._b==65
221. _b==66
222. _b==67
223. _b==68
224. _b==69
225. this._b==70
226. this._b==71
227. _b ==72
228. _b ==73
229. _b ==74
230. _b ==75)) || (token && _b == 16)">
231. _b == 16)">
232. !token&&_auth==0 && shbGroupSelected
233. !token&&_auth==0&&(_b==6 || _b==2 || _b == 31)
234. _b == 31)">
235. !token&&_auth==0&&(_b==3||_b==18||_b==19||_b==27)
236. (token || _auth==1) && _b != 16
237. !token&&_auth==0 && _b == 5
238. this._auth == 0
239. this.tokenList.length == 0) {
240. this.filteredData.length == 1
241. $event == 'true') {
242. if (bankId == 12) {
243. this._b == '55'
244. this._b == '47'
245. this._b == '48'
246. this._b == '59'
247. this._b == '73'
248. this._b == '12') {
249. this._b == '3'
250. this._b == '43'
251. this._b == '45'
252. this._b == '54'
253. this._b == '57'
254. this._b == '61'
255. this._b == '63'
256. this._b == '67'
257. this._b == '68'
258. this._b == '69'
259. this._b == '72'
260. this._b == '74'
261. this._b == '75') {
262. this._b == '36'
263. if (item['id'] == this._b) {
264. } else if (this._res_post.state == 'failed') { // lỗi mới kiểm tra sang trang lỗi
265. if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
266. } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
267. v.length == 15) || (v.length == 16
268. v.length == 19))
269. this._util.checkMod10(v) == true) {
270. cardNo.length == 15)
271. cardNo.length == 16)
272. cardNo.startsWith('81')) && (cardNo.length == 16
273. cardNo.length == 19))
274. this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
275. this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
276. v.length == 5) {
277. v.length == 4
278. v.length == 3)
279. _val.value.length == 4
280. _val.value.length == 3)
281. this._i_csc.length == 4) ||
282. this._i_csc.length == 3)
283. merchantId == 'OPTEST'
284. sortMethodArray[i].trim()=='International'"
285. sortMethodArray[i].trim()=='Domestic'"
286. sortMethodArray[i].trim()=='QR'"
287. sortMethodArray[i].trim()=='Paypal'"
288. if (el == 5) {
289. } else if (el == 6) {
290. } else if (el == 7) {
291. } else if (el == 8) {
292. } else if (el == 2) {
293. } else if (el == 4) {
294. } else if (el == 3) {
295. if (!isNaN(_re.status) && (_re.status == '200'
296. _re.status == '201') && _re.body != null) {
297. if (('closed' == this._res_polling.state
298. 'canceled' == this._res_polling.state
299. 'expired' == this._res_polling.state)
300. } else if ('paid' == this._res_polling.state) {
301. this._res_polling.payments == null
302. if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'authorization_required') {
303. } else if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
304. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
305. this._paymentService.getCurrentPage() == 'enter_card') {
306. } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
307. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
308. } else if ('not_paid' == this._res_polling.state) {
309. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
310. if (message == '1') {
311. if (this.checkInvoiceState() == 1) {
312. this.version2 = _re.body?.merchant?.qr_version == "2"
313. if (this.type == 5
314. } else if (this.type == 6
315. } else if (this.type == 2
316. } else if (this.type == 7
317. } else if (this.type == 8
318. } else if (this.type == 4
319. } else if (this.type == 3
320. if (this.themeConfig.default_method == 'International'
321. } else if (this.themeConfig.default_method == 'Domestic'
322. } else if (this.themeConfig.default_method == 'QR'
323. } else if (this.themeConfig.default_method == 'Paypal'
324. if ('REDIRECT' == approval.method) {
325. } else if ('POST_REDIRECT' == approval.method) {
326. if (('closed' == this._res.state
327. 'canceled' == this._res.state
328. 'expired' == this._res.state
329. 'paid' == this._res.state)
330. if ((this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number) == 0
331. this._auth == 0) {
332. if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
333. this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
334. if (this._res.payments[this._res.payments.length - 1].state == 'approved'
335. } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
336. if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required') {
337. } else if (this._res.payments[this._res.payments.length - 1].state == 'pending') {
338. } else if (this._res.payments[this._res.payments.length - 1].state == 'canceled') {
339. } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
340. this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
341. if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
342. } else if (idBrand == 'atm'
343. if ('paid' == this._res.state) {
344. this._res.merchant.token_site == 'onepay')) {
345. this._res.payments == null) {
346. this._res.payments[this._res.payments.length - 1].state == 'pending') {
347. if (this._res.currencies[0] == 'USD') {
348. if (item.instrument.issuer.brand.id == 'atm') {
349. } else if (item.instrument.issuer.brand.id == 'visa'
350. item.instrument.issuer.brand.id == 'mastercard') {
351. if (item.instrument.issuer_location == 'd') {
352. } else if (item.instrument.issuer.brand.id == 'amex') {
353. } else if (item.instrument.issuer.brand.id == 'jcb') {
354. uniq.length == 1) {
355. if (type == 'qrv1') {
356. if (type == 'mobile') {
357. e.type == 'ewallet'
358. e.code == 'momo')) {
359. } else if (type == 'desktop') {
360. e.type == 'vnpayqr') || (regex.test(strTest)
361. _val == 2) {
362. if (_val == 2
363. } else if (_val == 2
364. _val == 2
365. if (this.type == 4) {
366. filteredData.length == 1) {
367. if (data._locale == 'en') {
368. screen=='qr'"
369. screen=='confirm_close'"
370. this.themeConfig.deeplink_status == 'Off' ? false : true
371. if (item.available == true) {
372. if (appcode == 'grabpay'
373. appcode == 'momo') {
374. if (type == 2
375. err.error.code == '04') {
376. e.type == 'vnpayqr') {
377. e.type == 'ewallet') {
378. e.type == 'wallet')) {
379. if (d.b.code == s) {
380. type == 'vnpay'"
381. type == 'bankapp'"
382. type == 'both'"
383. _locale=='vi'"
384. _locale=='en'"
385. _locale == 'vi'"
386. _locale == 'en'"
387. e.type == 'deeplink') {
388. if (e.type == 'ewallet') {
389. this.listWallet.length == 1
390. this.listWallet[0].code == 'momo') {
391. this.checkEWalletDeeplink.length == 0) {
392. arrayWallet.length == 0) return false;
393. if (arrayWallet[i].code == key) {
394. if (this.locale == 'vi') {
395. uniqueTokenBank) || (type == 2
396. type == 2)) || token">
397. if (this.type == 5) {
398. data['type'] == 'Visa'
399. data['type'] == 'Master'
400. data['type'] == 'JCB'"
401. data['type'] == 'Visa'"
402. data['type'] == 'Master'"
403. data['type'] == 'Amex'"
404. token_main == '1'"
405. if (message == '0') {
406. item.id == element.id ? element['active'] = true : element['active'] = false
407. item.display_cvv == true ? this.flagToken = true : this.flagToken = false
408. if (result == 'success') {
409. if (this.tokenList.length == 0) {
410. } else if (result == 'error') {
411. _val.value.trim().length == 4) || (item.brand_id != 'amex'
412. _val.value.trim().length == 3))
413. _val.value.length == 4) || (item.brand_id != 'amex'
414. _val.value.length == 3)));
415. id == 'amex' ? this.maxLength = 4 : this.maxLength = 3
416. if (_re.body.state == 'more_info_required') {
417. if (this._res_post.state == 'failed') {
418. } else if (_re.body.state == 'authorization_required') {
419. this._b == 18
420. this._b == 5
421. } else if (_re.body.state == 'failed') {
422. if (action == 'blur') {
423. this._i_token_otp.trim().length == 4)
424. this._i_token_otp.trim().length == 3));
425. return ((a.id == id
426. a.code == id) && a.type.includes(type));
427. if (isIphone == true) {
428. } else if (isAndroid == true) {
429. return countPayment == maxPayment
430. if (this.getLatestPayment().state == 'canceled')
431. if (res?.state == 'canceled') {
432. state == 'authorization_required'
433. if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
434. else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';
435. if (e.name == bankSwift) { // TODO: get by swift
436. return this.apps.find(e => e.code == appCode);
437. if (+e.id == bankId) {
438. if (e.swiftCode == bankSwift) {
439. if (this.checkCount == 1) {
440. if (results == null) {
441. if (c.length == 3) {
442. d = d == undefined ? '.' : d
443. t = t == undefined ? '
444. return results == null ? null : results[1]
445. if (((_val.length == 4
446. _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
447. _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
448. iss_date.length == 4
449. iss_date.search('/') == -1)
450. iss_date.length == 5))
451. if (_dataCache == null) {
452. if ( (0 <= r && r <= 6 && (c == 0
453. c == 6) )
454. || (0 <= c && c <= 6 && (r == 0
455. r == 6) )
456. if (i == 0
457. _modules[r][6] = (r % 2 == 0);
458. _modules[6][c] = (c % 2 == 0);
459. if (r == -2
460. r == 2
461. c == -2
462. c == 2
463. || (r == 0
464. c == 0) ) {
465. ( (bits >> i) & 1) == 1);
466. if (col == 6) col -= 1;
467. if (_modules[row][col - c] == null) {
468. dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
469. if (bitIndex == -1) {
470. margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
471. if (typeof arguments[0] == 'object') {
472. margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
473. if (b == -1) throw 'eof';
474. if (b0 == -1) break;
475. if (typeof b == 'number') {
476. if ( (b & 0xff) == b) {
477. return function(i, j) { return (i + j) % 2 == 0
478. return function(i, j) { return i % 2 == 0
479. return function(i, j) { return j % 3 == 0
480. return function(i, j) { return (i + j) % 3 == 0
481. return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
482. return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
483. return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
484. return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
485. if (r == 0
486. c == 0) {
487. if (dark == qrcode.isDark(row + r, col + c) ) {
488. if (count == 0
489. count == 4) {
490. if (typeof num.length == 'undefined') {
491. num[offset] == 0) {
492. if (typeof rsBlock == 'undefined') {
493. return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
494. _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
495. if (data.length - i == 1) {
496. } else if (data.length - i == 2) {
497. } else if (n == 62) {
498. } else if (n == 63) {
499. if (_buflen == 0) {
500. if (c == '=') {
501. } else if (c == 0x2b) {
502. } else if (c == 0x2f) {
503. if (table.size() == (1 << bitLength) ) {
504. if (cardList.length == 1) {//TOKEN, BIDV, SHB, OCEAN, PVCOM, TP Bank
505. if ( $('.circle_v1').css('display') == 'block'
506. if ($('.circle_v2').css('display') == 'block'
507. $('.circle_v3').css('display') == 'block' ) {
508. $('.circle_v1').css('display') == 'block'
509. document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM'
510. if ( document.getElementsByClassName("select_online")[0].value == 'Easy Online Banking') {
511. if ( document.getElementsByClassName("select_online")[0].value == 'Easy Mobile Banking') {
512. if ( document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM') {
513. if ($('.circle_v1').css('display') == 'block') {
514. if ($('.circle_v1').css('display') == 'block'
515. if ( $('.circle_v1').css('display') == 'block') {
516. else if ($('.circle_v2').css('display') == 'block') {
517. if ( $('.circle_v3').css('display') == 'block' ) {
518. if ($('.circle_v2').css('display') == 'block') {
519. x = _.positionProp == 'left' ? Math.ceil(position) + 'px' : '0px'
520. y = _.positionProp == 'top' ? Math.ceil(position) + 'px' : '0px'
521. if (_.options.slidesToShow == _.options.slidesToScroll
522. if (typeof opt == 'object'
523. typeof opt == 'undefined')

!== (156 điều kiện duy nhất):
------------------------------------------------------------
1. this.lastValue !== $event.target.value)) {
2. if (YY % 400 === 0 || (YY % 100 !== 0
3. if (YYYY % 400 === 0 || (YYYY % 100 !== 0
4. this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
5. event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
6. key !== '3') {
7. codeResponse.toString() !== '0') {
8. cardNo.length !== 0) {
9. if (this.cardTypeBank !== 'bank_card_number') {
10. if (this.cardTypeBank !== 'bank_account_number') {
11. if (this.cardTypeBank !== 'bank_username') {
12. if (this.cardTypeBank !== 'bank_customer_code') {
13. this.lb_card_account !== this._translate.instant('ocb_account')) {
14. this.lb_card_account !== this._translate.instant('ocb_phone')) {
15. this.lb_card_account !== this._translate.instant('ocb_card')) {
16. this._b !== 18) || (this.cardTypeOcean === 'ATM'
17. this._b !== 18) || (this._b == 18)) {
18. key !== '8') {
19. codeResponse.toString() !== '0'){
20. event.inputType !== 'deleteContentBackward') || v.length == 5) {
21. if (deviceValue !== 'default') {
22. this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
23. return this._i_country_code !== 'default'
24. this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
25. this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {
26. if (_val !== 3) {
27. this._util.getElementParams(this.currentUrl, 't_id') !== '') {
28. queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
29. if (queryString !== '') {
30. if (target !== 0
31. if (e !== null) {
32. if (lang !== "vi") lang = "en";
33. this.oldValue !== this.value) {
34. if (jLinks !== undefined
35. jLinks !== null) {
36. if (jMerchantReturn !== undefined
37. jMerchantReturn !== null) {
38. if (responseCode !== undefined
39. responseCode !== null
40. if (parentRes !== "{
41. if (value !== "") {
42. if (inMonth.value !== ""
43. if (inYear.value !== ""
44. var month = inMonth.value !== ""
45. var year = parseInt("20" + (inYear.value !== ""
46. if ("2D" !== order["vpc_VerType"]) return err("MISSING_FIELD"
47. inMonth.value !== tokenInsMonth) instrument["month"] = inMonth.value;
48. inYear.value !== tokenInsYear) instrument["year"] = inYear.value;
49. if (inCvv.value !== "") instrument["cvv"] = inCvv.value;
50. if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
51. if (inNumber.style.display !== "none") instrument.number = inNumber.value;
52. if (inDate.style.display !== "none") instrument.date = inDate.value;
53. if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
54. if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
55. if (inName.style.display !== "none") instrument.name = inName.value;
56. typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
57. hrefAttr !== '#' ? hrefAttr.trim() : ''
58. $(this._element).css('visibility') !== 'hidden') {
59. if (selector !== null
60. if (selector !== null) {
61. if (typeof this._config.parent.jquery !== 'undefined') {
62. if (typeof this._config.reference.jquery !== 'undefined') {
63. if (this._config.boundary !== 'scrollParent') {
64. if (this._popper !== null) {
65. event.which !== TAB_KEYCODE)) {
66. event.which !== ESCAPE_KEYCODE
67. if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
68. event.which !== ARROW_UP_KEYCODE
69. this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
70. if (document !== event.target
71. _this5._element !== event.target
72. if (event.target !== event.currentTarget) {
73. if (typeof margin !== 'undefined') {
74. if (allowedAttributeList.indexOf(attrName) !== -1) {
75. if (uriAttrs.indexOf(attrName) !== -1) {
76. var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
77. if (_this2._hoverState !== HoverState.SHOW
78. if (_this2._popper !== null) {
79. if (data.originalPlacement !== data.placement) {
80. } else if (trigger !== Trigger.MANUAL) {
81. titleType !== 'string') {
82. if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
83. if (this.constructor.Default[key] !== this.config[key]) {
84. if (tabClass !== null
85. if (tip.getAttribute('x-placement') !== null) {
86. if (typeof config.target !== 'string') {
87. if (this._scrollHeight !== scrollHeight) {
88. if (this._activeTarget !== target) {
89. var isActiveTarget = this._activeTarget !== this._targets[i]
90. 'use strict';(function(Y){function C(c,a,b){var e=0,h=[],n=0,g,l,d,f,m,q,u,r,I=!1,v=[],w=[],t,y=!1,z=!1,x=-1;b=b||{};g=b.encoding||"UTF8";t=b.numRounds||1;if(t!==parseInt(t,10)
91. 0!==f%32
92. a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8
93. }switch(c){case "HEX":c=function(a,c,d){var g=a.length,l,p,f,m,q,u;if(0!==g%2)throw Error("String of HEX type must be in byte increments");c=c||[0];d=d||0;q=d>>>3;u=-1===b?3:0;for(l=0
94. 1!==b
95. "UTF16LE"!==a
96. "");if(-1!==p
97. function(a,c,d){var g,l,p,f,m,q;c=c||[0];d=d||0;l=d>>>3;m=-1===b?3:0;q=new Uint8Array(a);for(g=0;g<a.byteLength;g+=1)f=g+l,p=f>>>2,c.length<=p&&c.push(0),c[p]|=q[g]<<8*(m+f%4*b);return{value:c,binLen:8*a.byteLength+d}};break;default:throw Error("format must be HEX, TEXT, B64, BYTES, or ARRAYBUFFER");}return c}function y(c,a){return c<<a|c>>>32-a}function S(c,a){return 32<a?(a-=32,new b(c.b<<a|c.a>>>32-a,c.a<<a|c.b>>>32-a)):0!==a?new b(c.a<<a|c.b>>>32-a,c.b<<a|c.a>>>32-a):c}function w(c,a){return c>>>
98. a[7]);return a}function D(c,a){var d,e,h,n,g=[],l=[];if(null!==c)for(e=0
99. define.amd?define(function(){return C}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=C),exports=C):Y.jsSHA=C})(this);
100. return C}):"undefined"!==typeof exports?("undefined"!==typeof module
101. 'use strict';(function(I){function w(c,a,d){var l=0,b=[],g=0,f,n,k,e,h,q,y,p,m=!1,t=[],r=[],u,z=!1;d=d||{};f=d.encoding||"UTF8";u=d.numRounds||1;if(u!==parseInt(u,10)
102. l+=1)g[l]=c[l>>>2]>>>8*(3+l%4*-1)&255;return b}function C(c){var a={outputUpper:!1,b64Pad:"=",shakeLen:-1};c=c||{};a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");
103. if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0;d<f;d+=2){k=parseInt(a.substr(d,2),16);if(isNaN(k))throw Error("String of HEX type contains invalid characters");
104. if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0
105. "");if(-1!==k
106. define.amd?define(function(){return w}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=w),exports=w):I.jsSHA=w})(this);
107. return w}):"undefined"!==typeof exports?("undefined"!==typeof module
108. } else if (typeof exports !== 'undefined') {
109. if (typeof document.mozHidden !== 'undefined') {
110. } else if (typeof document.webkitHidden !== 'undefined') {
111. asNavFor !== null ) {
112. if ( asNavFor !== null
113. if (_.options.infinite !== true) {
114. _.options.responsive !== null) {
115. if (targetBreakpoint !== null) {
116. if (_.activeBreakpoint !== null) {
117. if (targetBreakpoint !== _.activeBreakpoint
118. triggerBreakpoint !== false ) {
119. unevenOffset = (_.slideCount % _.options.slidesToScroll !== 0);
120. _.$dots !== null) {
121. if (filter !== null) {
122. if (_.slideCount % _.options.slidesToScroll !== 0) {
123. if (_.$dots !== null) {
124. if (slideControlIndex !== -1) {
125. _.currentSlide !== 0) {
126. if ($(window).width() !== _.windowWidth) {
127. } else if ( typeof arguments[1] !== 'undefined' ) {
128. if( $.type( _.options.responsive ) !== 'array' ) {
129. if (bodyStyle.WebkitTransition !== undefined
130. bodyStyle.MozTransition !== undefined
131. bodyStyle.msTransition !== undefined) {
132. if (bodyStyle.OTransform !== undefined) {
133. if (bodyStyle.MozTransform !== undefined) {
134. if (bodyStyle.webkitTransform !== undefined) {
135. if (bodyStyle.msTransform !== undefined) {
136. if (bodyStyle.transform !== undefined
137. _.animType !== false) {
138. _.transformsEnabled = _.options.useTransform && (_.animType !== null
139. _.animType !== false);
140. if (dontAnimate !== true
141. if (dontAnimate !== true) {
142. if ( _.touchObject.startX !== _.touchObject.curX ) {
143. event.type.indexOf('mouse') !== -1) {
144. event.originalEvent.touches !== undefined ?
145. touches = event.originalEvent !== undefined ? event.originalEvent.touches : null
146. touches.length !== 1) {
147. _.touchObject.curX = touches !== undefined ? touches[0].pageX : event.clientX
148. _.touchObject.curY = touches !== undefined ? touches[0].pageY : event.clientY
149. if (event.originalEvent !== undefined
150. if (_.touchObject.fingerCount !== 1
151. event.originalEvent.touches !== undefined) {
152. _.touchObject.startX = _.touchObject.curX = touches !== undefined ? touches.pageX : event.clientX
153. _.touchObject.startY = _.touchObject.curY = touches !== undefined ? touches.pageY : event.clientY
154. if (_.$slidesCache !== null) {
155. //if (event.origin !== "http://example.com:8080") return;
156. /*if (contentType !== my_expected_type) {

!= (175 điều kiện duy nhất):
------------------------------------------------------------
1. if (params['locale'] != null
2. } else if (params['locale'] != null
3. if (userLang != null
4. if(value!=null){
5. if (this._idInvoice != null
6. this._idInvoice != 0) {
7. if (this._paymentService.getInvoiceDetail() != null) {
8. dataPassed.body != null) {
9. dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
10. dataPassed.body.themes.border_color != true ? dataPassed.body.themes.border_color : ''
11. if (this._translate.currentLang != language) {
12. notify != ''"
13. if (params['locale'] != null) {
14. } else if (this._b != 18) {
15. if (this.htmlDesc != null
16. if (ua.indexOf('safari') != -1
17. if (_val.value != '') {
18. this.auth_method != null) {
19. if (this.valueDate.length != 3) {
20. if (_formCard.exp_date != null
21. if (this.cardName != null
22. if (this._res_post.links != null
23. this._res_post.links.merchant_return != null
24. this._res_post.links.merchant_return.href != null) {
25. if (this._res_post.authorization != null
26. this._res_post.authorization.links != null
27. this._res_post.authorization.links.approval != null) {
28. this._res_post.links.cancel != null) {
29. this._b != 27
30. this._b != 12
31. this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
32. this._b != 18)
33. if (this._b != 18
34. this._b != 19) {
35. if (this._res.links != null
36. this._res.links.merchant_return != null
37. this._res.links.merchant_return.href != null) {
38. if (!(_formCard.otp != null
39. if (!(_formCard.password != null
40. if (this._inExpDate.length != 3) {
41. if (this._b != 9
42. this._b != 16
43. this._b != 17
44. this._b != 25
45. this._b != 44
46. this._b != 54
47. this._b != 57
48. this._b != 59
49. this._b != 61
50. this._b != 63
51. this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
52. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72' , '73' , '74' , '75'].indexOf(this._b.toString()) != -1) {
53. if (this._res_post.return_url != null) {
54. let userName = _formCard.name != null ? _formCard.name : ''
55. this._res_post.authorization.links.approval != null
56. this._res_post.authorization.links.approval.href != null) {
57. userName = paramUserName != null ? paramUserName : ''
58. if ('otp' != this._paymentService.getCurrentPage()) {
59. if (!(strInstrument != null
60. if (strInstrument.substring(0, 1) != '^'
61. strInstrument.substr(strInstrument.length - 1) != '$') {
62. if (bankid != null) {
63. _showAVS!=true"
64. } else if (this._res_post.links != null
65. cardNo != null
66. v != null
67. this.c_csc = (!(_val.value != null
68. this._i_csc != null
69. this._paymentService.getState() != 'error') {
70. if (this._paymentService.getCurrentPage() != 'otp') {
71. _re.body != null) {
72. this._res_polling.links != null
73. this._res_polling.links.merchant_return != null //
74. } else if (this._res_polling.merchant != null
75. this._res_polling.merchant_invoice_reference != null
76. } else if (this._res_polling.payments != null
77. this._res_polling.links.merchant_return != null//
78. this._res_polling.payments != null
79. if (this._res_polling.payments != null
80. t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
81. this.type.toString().length != 0) {
82. this._res.links != null
83. if (count != 1) {
84. if (this._res.merchant != null
85. this._res.merchant_invoice_reference != null) {
86. if (this._res.merchant.address_details != null) {
87. this._res.links != null//
88. } else if (this._res.payments != null
89. this._res.payments[this._res.payments.length - 1].instrument != null
90. this._res.payments[this._res.payments.length - 1].instrument.issuer != null
91. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
92. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
93. this._res.payments[this._res.payments.length - 1].links != null
94. this._res.payments[this._res.payments.length - 1].links.cancel != null
95. this._res.payments[this._res.payments.length - 1].links.cancel.href != null
96. this._res.payments[this._res.payments.length - 1].links.update != null
97. this._res.payments[this._res.payments.length - 1].links.update.href != null) {
98. this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
99. this._res.payments[this._res.payments.length - 1].authorization != null
100. this._res.payments[this._res.payments.length - 1].authorization.links != null) {
101. this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
102. this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
103. if (this._res.payments[this._res.payments.length - 1].authorization != null
104. this._res.payments[this._res.payments.length - 1].authorization.links != null
105. auth = paramUserName != null ? paramUserName : ''
106. this._res.links.merchant_return != null //
107. } else if (this._res.merchant != null
108. this._res.merchant_invoice_reference != null
109. if (['mastercard', 'visa', 'amex', 'jcb', 'cup', 'card', 'np_card', 'atm'].indexOf(id) != -1) {
110. } else if (['techcombank_account', 'vib_account', 'dongabank_account', 'tpbank_account', 'bidv_account', 'pvcombank_account', 'shb_account', 'oceanbank_online_account'].indexOf(id) != -1) {
111. } else if (['oceanbank_mobile_account'].indexOf(id) != -1) {
112. } else if (['shb_customer_id'].indexOf(id) != -1) {
113. if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1) {
114. return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
115. e.type != 'ewallet') || (regex.test(strTest)
116. } else if (this._res.payments != null) {
117. if (appcode != null) {
118. if (_re.status != '200'
119. _re.status != '201')
120. 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {
121. qr_version2 != 'None'"
122. qr_version2 != 'None'
123. if (this.translate.currentLang != language) {
124. (((!_b && !onePaymentMethod) || (tokenList.length > 0 && !_b)) && ((type != 2 || uniqueTokenBank) || (type == 2 && !_b))) && !token
125. feeService['atm']['fee'] != 0"
126. feeService['visa_mastercard_d']['fee'] != 0"
127. feeService['qr']['fee'] != 0"
128. feeService['pp']['fee'] != 0"
129. item['feeService']['fee'] != 0"
130. if (_val.value != null
131. this.c_token_otp_csc = !(_val.value != null
132. if (_re.body.authorization != null
133. _re.body.authorization.links != null
134. if (_re.body.links != null
135. _re.body.links.cancel != null) {
136. if (_re.body.return_url != null) {
137. } else if (_re.body.links != null
138. _re.body.links.merchant_return != null
139. _re.body.links.merchant_return.href != null) {
140. if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(item.brand_id) != -1
141. return this._i_token_otp != null
142. || (id != 'amex'
143. if (idInvoice != null
144. idInvoice != 0)
145. idInvoice != 0) {
146. if (this._merchantid != null
147. this._tranref != null
148. this._state != null
149. let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
150. urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
151. if (res?.status != 200
152. res?.status != 201) return;
153. if (paymentId != null) {
154. _re.status != '201') {
155. latestPayment?.state != "authorization_required") {
156. if (tem != null) {
157. if ((tem = ua.match(/version\/(\d+)/i)) != null) {
158. if (v.length != 3) {
159. if (_modules[r][6] != null) {
160. if (_modules[6][c] != null) {
161. if (_modules[row][col] != null) {
162. while (buffer.getLengthInBits() % 8 != 0) {
163. if (count != numChars) {
164. throw count + ' != ' + numChars
165. while (data != 0) {
166. if (test.length != 2
167. ( (test[0] << 8) | test[1]) != code) {
168. if (_length % 3 != 0) {
169. if ( (data >>> length) != 0) {
170. return typeof _map[key] != 'undefined'
171. var source = arguments[i] != null ? arguments[i] : {
172. $('[draggable!=true]', _.$slideTrack).off('dragstart', _.preventDefault);
173. $('[draggable!=true]', _.$slideTrack).on('dragstart', _.preventDefault);
174. if( direction != 'vertical' ) {
175. if (typeof ret != 'undefined') return ret;

