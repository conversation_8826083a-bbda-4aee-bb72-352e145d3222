====================================================================================================
TRÍCH XUẤT ĐIỀU KIỆN SO SÁNH TỪ CÁC FILE TYPESCRIPT/JAVASCRIPT/HTML
====================================================================================================
Thư mục/File nguồn: /root/projects/onepay/paygate-general-fee-outside/src
Thời gian: 18:12:28 18/8/2025
Tổng số file xử lý: 153
Tổng số file bị bỏ qua: 48
Tổng số điều kiện tìm thấy: 3213

THỐNG KÊ TỔNG QUAN THEO LOẠI TOÁN TỬ:
--------------------------------------------------
Strict equality (===): 1807 lần
Loose equality (==): 532 lần
Strict inequality (!==): 660 lần
Loose inequality (!=): 214 lần
--------------------------------------------------

DANH SÁCH FILE ĐÃ XỬ LÝ:
--------------------------------------------------
1. 4en.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/4en.html
2. 4vn.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/4vn.html
3. app-routing.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/app-routing.module.ts
4. app.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/app.component.html
5. app.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/app.component.spec.ts
6. app.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/app.component.ts
7. app.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/app.module.ts
8. counter.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/counter.directive.spec.ts
9. counter.directive.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/counter.directive.ts
10. format-carno-input.derective.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/directives/format-carno-input.derective.ts
11. uppercase-input.directive.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/directives/uppercase-input.directive.ts
12. error.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/error/error.component.html
13. error.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/error/error.component.spec.ts
14. error.component.ts (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/error/error.component.ts
15. format-date.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/format-date.directive.spec.ts
16. format-date.directive.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/format-date.directive.ts
17. main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/main.component.html
18. main.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/main.component.spec.ts
19. main.component.ts (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/main.component.ts
20. app-result.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/app-result/app-result.component.html
21. app-result.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/app-result/app-result.component.ts
22. cancel-dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/cancel-dialog-guide-dialog.html
23. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/confirm-dialog/dialog-guide-dialog.html
24. policy-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.html
25. policy-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.ts
26. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/dialog-guide-dialog.html
27. bankaccount.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
28. bankaccount.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
29. bankaccount.component.ts (156 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
30. bank.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/model/bank.ts
31. otp-auth.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
32. otp-auth.component.ts (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
33. techcombank.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
34. techcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
35. techcombank.component.ts (16 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
36. vietcombank.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
37. vietcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
38. vietcombank.component.ts (107 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
39. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
40. domescard-main.component.html (62 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/domescard-main.component.html
41. domescard-main.component.ts (51 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/domescard-main.component.ts
42. dialog-guide-dialog.html (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/intercard-main/dialog-guide-dialog.html
43. intercard-main.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/intercard-main/intercard-main.component.html
44. intercard-main.component.ts (89 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/intercard-main/intercard-main.component.ts
45. menu.component.html (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/menu.component.html
46. menu.component.ts (133 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/menu.component.ts
47. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
48. qr-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
49. qr-dialog.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
50. qr-main.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/qr-main/qr-main.component.html
51. qr-main.component.ts (12 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/qr-main/qr-main.component.ts
52. remove-token-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/token-main/remove-token-dialog/remove-token-dialog.html
53. dialog-guide-dialog.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/token-main/token-dialog/dialog-guide-dialog.html
54. token-main.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/token-main/token-main.component.html
55. token-main.component.ts (65 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/token-main/token-main.component.ts
56. overlay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/overlay/overlay.component.html
57. overlay.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/overlay/overlay.component.spec.ts
58. overlay.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/overlay/overlay.component.ts
59. close-dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/services/close-dialog.service.ts
60. data.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/services/data.service.ts
61. dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/services/dialog.service.ts
62. fee.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/services/fee.service.ts
63. multiple_method.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/services/multiple_method.service.ts
64. payment.service.ts (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/services/payment.service.ts
65. token-main.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/services/token-main.service.ts
66. index.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/translate/index.ts
67. lang-en.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/translate/lang-en.ts
68. lang-vi.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/translate/lang-vi.ts
69. translate.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/translate/translate.pipe.ts
70. translate.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/translate/translate.service.ts
71. translations.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/translate/translations.ts
72. apps-info.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/util/apps-info.ts
73. banks-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/util/banks-info.ts
74. iso-ca-states.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/util/iso-ca-states.ts
75. iso-us-states.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/util/iso-us-states.ts
76. util.ts (25 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/util/util.ts
77. qrcode.js (67 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/assets/script/qrcode.js
78. environment.prod.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/environments/environment.prod.ts
79. environment.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/environments/environment.ts
80. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/acc_bidv/index.html
81. script.js (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/acc_bidv/script.js
82. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/acc_oceanbank/index.html
83. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/acc_oceanbank/script.js
84. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/acc_pvcombank/index.html
85. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/acc_pvcombank/script.js
86. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/acc_shb/index.html
87. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/acc_shb/script.js
88. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/acc_tpbank/index.html
89. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/acc_tpbank/script.js
90. common.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/common.js
91. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/domestic_card/index.html
92. script.js (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/domestic_card/script.js
93. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/domestic_token/index.html
94. script.js (25 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/domestic_token/script.js
95. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/index.html
96. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/international_card/index.html
97. script.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/international_card/script.js
98. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/international_token/index.html
99. script.js (36 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/international_token/script.js
100. script.js (54 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/script.js
101. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.js
102. bidv.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Bidv/assets/js/bidv.js
103. bidv2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Bidv/bidv2.js
104. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Bidv/index.html
105. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.js
106. ocean.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Ocean/assets/js/ocean.js
107. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Ocean/index.html
108. ocean2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Ocean/ocean2.js
109. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
110. pvbank.js (23 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Pv_Bank/assets/js/pvbank.js
111. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Pv_Bank/index.html
112. pvbank2.js (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Pv_Bank/pvbank2.js
113. Sea2.js (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/SeaBank/Sea2.js
114. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
115. Sea.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/SeaBank/assets/js/Sea.js
116. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/SeaBank/index.html
117. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.js
118. shb.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Shb/assets/js/shb.js
119. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Shb/index.html
120. shb2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Shb/shb2.js
121. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
122. tpbank.js (23 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Tp_Bank/assets/js/tpbank.js
123. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Tp_Bank/index.html
124. tpbank2.js (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Tp_Bank/tpbank2.js
125. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.js
126. onepay.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Visa/assets/js/onepay.js
127. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Visa/index.html
128. index.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Visa/index.js
129. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
130. vpbank.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Vp_Bank/assets/js/vpbank.js
131. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Vp_Bank/index.html
132. vpbank2.js (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Vp_Bank/vpbank2.js
133. sha.js (49 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/assets/js/sha.js
134. sha256.js (28 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/assets/js/sha256.js
135. slick.js (182 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/assets/libraries/slick/slick.js
136. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.js
137. atm_b1.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_1/assets/js/atm_b1.js
138. atm_b1_2.js (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_1/atm_b1_2.js
139. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_1/index.html
140. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.js
141. atm_b2.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_2/assets/js/atm_b2.js
142. atm_b2_2.js (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_2/atm_b2_2.js
143. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_2/index.html
144. common.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/common.js
145. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.js
146. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/international_card/index.html
147. script.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/international_card/script.js
148. index.html (35 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/index.html
149. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/index.html
150. karma.conf.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/karma.conf.js
151. main.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/main.ts
152. polyfills.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/polyfills.ts
153. test.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/test.ts

DANH SÁCH FILE BỊ BỎ QUA:
--------------------------------------------------
1. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/bootstrap.min.js
2. jquery-3.0.0.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/jquery-3.0.0.min.js
3. popper.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/popper.min.js
4. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
5. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
6. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
7. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Bidv/assets/js/jquery-3.4.1.min.js
8. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
9. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
10. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
11. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Ocean/assets/js/jquery-3.4.1.min.js
12. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
13. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
14. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
15. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Pv_Bank/assets/js/jquery-3.4.1.min.js
16. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
17. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
18. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
19. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/SeaBank/assets/js/jquery-3.4.1.min.js
20. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
21. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
22. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
23. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Shb/assets/js/jquery-3.4.1.min.js
24. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
25. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
26. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
27. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Tp_Bank/assets/js/jquery-3.4.1.min.js
28. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
29. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
30. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
31. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Visa/assets/js/jquery-3.4.1.min.js
32. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
33. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
34. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
35. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Vp_Bank/assets/js/jquery-3.4.1.min.js
36. instafeed.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/assets/js/instafeed.min.js
37. slick.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/assets/libraries/slick/slick.min.js
38. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
39. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
40. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
41. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_1/assets/js/jquery-3.4.1.min.js
42. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
43. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
44. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
45. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_2/assets/js/jquery-3.4.1.min.js
46. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
47. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
48. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js

====================================================================================================
CHI TIẾT TỪNG FILE:
====================================================================================================

📁 FILE 1: 4en.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/4en.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 2: 4vn.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/4vn.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 3: app-routing.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/app-routing.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 4: app.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/app.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 5: app.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/app.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 6: app.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/app.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 81] return lang === this._translate.currentLang

== (2 điều kiện):
  1. [Dòng 60] 'vi' == params['locale']) {
  2. [Dòng 62] 'en' == params['locale']) {

!= (3 điều kiện):
  1. [Dòng 60] if (params['locale'] != null
  2. [Dòng 62] } else if (params['locale'] != null
  3. [Dòng 69] if (userLang != null

================================================================================

📁 FILE 7: app.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/app.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 8: counter.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/counter.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 9: counter.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/counter.directive.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 10: format-carno-input.derective.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/directives/format-carno-input.derective.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 44] this.lastValue !== $event.target.value)) {

!= (1 điều kiện):
  1. [Dòng 23] if(value!=null){

================================================================================

📁 FILE 11: uppercase-input.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/directives/uppercase-input.directive.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 0 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 23] this.lastValue !== $event.target.value)) {

================================================================================

📁 FILE 12: error.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/error/error.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 24] <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
  2. [Dòng 24] (errorCode == 'overtime' || errorCode == '253')  && !invoiceNearExpire && !paymentPending
  3. [Dòng 26] <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
  4. [Dòng 26] !(errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending

================================================================================

📁 FILE 13: error.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/error/error.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 14: error.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/error/error.component.ts
📊 Thống kê: 14 điều kiện duy nhất
   - === : 5 lần
   - == : 9 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 59] params.timeout === 'true') {
  2. [Dòng 78] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  3. [Dòng 78] _re.body.state === 'unpaid');
  4. [Dòng 117] if (this.errorCode === 'overtime'
  5. [Dòng 117] this.errorCode === '253') {

== (9 điều kiện):
  1. [Dòng 98] params.response_code == 'overtime') {
  2. [Dòng 136] if (_re.status == '200'
  3. [Dòng 136] _re.status == '201') {
  4. [Dòng 149] if (_re2.status == '200'
  5. [Dòng 149] _re2.status == '201') {
  6. [Dòng 161] if (this.errorCode == 'overtime'
  7. [Dòng 161] this.errorCode == '253') {
  8. [Dòng 163] } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
  9. [Dòng 180] if (lastPayment?.state == 'pending') {

================================================================================

📁 FILE 15: format-date.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/format-date.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 16: format-date.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/format-date.directive.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0
  2. [Dòng 123] YY % 4 === 0)) {
  3. [Dòng 138] if (YYYY % 400 === 0
  4. [Dòng 138] YYYY % 4 === 0)) {

!== (2 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0 || (YY % 100 !== 0
  2. [Dòng 138] if (YYYY % 400 === 0 || (YYYY % 100 !== 0

================================================================================

📁 FILE 17: main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 18: main.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/main.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 19: main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/main.component.ts
📊 Thống kê: 9 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 7 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 91] if ((dataPassed.status == '200'
  2. [Dòng 91] dataPassed.status == '201') && dataPassed.body != null) {

!= (7 điều kiện):
  1. [Dòng 82] if (this._idInvoice != null
  2. [Dòng 82] this._idInvoice != 0) {
  3. [Dòng 83] if (this._paymentService.getInvoiceDetail() != null) {
  4. [Dòng 91] dataPassed.body != null) {
  5. [Dòng 112] dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
  6. [Dòng 113] dataPassed.body.themes.border_color != true ? dataPassed.body.themes.border_color : ''
  7. [Dòng 163] if (this._translate.currentLang != language) {

================================================================================

📁 FILE 20: app-result.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/app-result/app-result.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 21: app-result.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/app-result/app-result.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 22: cancel-dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/cancel-dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 23: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/confirm-dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 24: policy-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 25: policy-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/dialog/policy-dialog/policy-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 26: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 27: bankaccount.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 3 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 40] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 55] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 55] valueDate.trim().length === 0)"

== (1 điều kiện):
  1. [Dòng 86] token_site == 'onepay'

================================================================================

📁 FILE 28: bankaccount.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 29: bankaccount.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
📊 Thống kê: 156 điều kiện duy nhất
   - === : 47 lần
   - == : 75 lần
   - !== : 13 lần
   - != : 21 lần
--------------------------------------------------------------------------------

=== (47 điều kiện):
  1. [Dòng 120] if (isIE[0] === 'MSIE'
  2. [Dòng 120] +isIE[1] === 10) {
  3. [Dòng 210] if ((_val.value.substr(-1) === ' '
  4. [Dòng 210] _val.value.length === 24) {
  5. [Dòng 220] if (this.cardTypeBank === 'bank_card_number') {
  6. [Dòng 225] } else if (this.cardTypeBank === 'bank_account_number') {
  7. [Dòng 231] } else if (this.cardTypeBank === 'bank_username') {
  8. [Dòng 235] } else if (this.cardTypeBank === 'bank_customer_code') {
  9. [Dòng 241] this.cardTypeBank === 'bank_card_number'
  10. [Dòng 255] if (this.cardTypeOcean === 'IB') {
  11. [Dòng 259] } else if (this.cardTypeOcean === 'MB') {
  12. [Dòng 260] if (_val.value.substr(0, 2) === '84') {
  13. [Dòng 267] } else if (this.cardTypeOcean === 'ATM') {
  14. [Dòng 294] if (this.cardTypeBank === 'bank_account_number') {
  15. [Dòng 313] this.cardTypeBank === 'bank_card_number') {
  16. [Dòng 335] if (this.cardTypeBank === 'bank_card_number'
  17. [Dòng 335] if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  18. [Dòng 656] if (event.keyCode === 8
  19. [Dòng 656] event.key === "Backspace"
  20. [Dòng 696] if (v.length === 2
  21. [Dòng 696] this.flag.length === 3
  22. [Dòng 696] this.flag.charAt(this.flag.length - 1) === '/') {
  23. [Dòng 700] if (v.length === 1) {
  24. [Dòng 702] } else if (v.length === 2) {
  25. [Dòng 705] v.length === 2) {
  26. [Dòng 713] if (len === 2) {
  27. [Dòng 989] if ((this.cardTypeBank === 'bank_account_number'
  28. [Dòng 989] this.cardTypeBank === 'bank_username'
  29. [Dòng 989] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  30. [Dòng 994] this.cardTypeOcean === 'ATM')
  31. [Dòng 995] || (this.cardTypeOcean === 'IB'
  32. [Dòng 1054] if (valIn === this._translate.instant('bank_card_number')) {
  33. [Dòng 1079] } else if (valIn === this._translate.instant('bank_account_number')) {
  34. [Dòng 1098] } else if (valIn === this._translate.instant('bank_username')) {
  35. [Dòng 1114] } else if (valIn === this._translate.instant('bank_customer_code')) {
  36. [Dòng 1202] if (_val.value === ''
  37. [Dòng 1202] _val.value === null
  38. [Dòng 1202] _val.value === undefined) {
  39. [Dòng 1213] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  40. [Dòng 1213] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  41. [Dòng 1220] this.cardTypeOcean === 'MB') {
  42. [Dòng 1228] this.cardTypeOcean === 'IB'
  43. [Dòng 1233] if ((this.cardTypeBank === 'bank_card_number'
  44. [Dòng 1267] if (this.cardName === undefined
  45. [Dòng 1267] this.cardName === '') {
  46. [Dòng 1275] if (this.valueDate === undefined
  47. [Dòng 1275] this.valueDate === '') {

== (75 điều kiện):
  1. [Dòng 111] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 134] if (this._b == 18
  3. [Dòng 134] this._b == 19) {
  4. [Dòng 137] if (this._b == 19) {//19BIDV
  5. [Dòng 145] } else if (this._b == 3
  6. [Dòng 145] this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  7. [Dòng 150] if (this._b == 27) {
  8. [Dòng 155] } else if (this._b == 12) {// 12SHB
  9. [Dòng 160] } else if (this._b == 18) { //18Oceanbank-ocb
  10. [Dòng 219] if (this._b == 19
  11. [Dòng 219] this._b == 3
  12. [Dòng 219] this._b == 27
  13. [Dòng 219] this._b == 12) {
  14. [Dòng 254] } else if (this._b == 18) {
  15. [Dòng 285] if (this.checkBin(_val.value) && (this._b == 3
  16. [Dòng 285] this._b == 27)) {
  17. [Dòng 290] if (this._b == 3) {
  18. [Dòng 302] this.cardTypeOcean == 'ATM') {
  19. [Dòng 315] if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  20. [Dòng 335] this._b == 18)) {
  21. [Dòng 411] if (this.checkBin(v) && (this._b == 3
  22. [Dòng 656] event.inputType == 'deleteContentBackward') {
  23. [Dòng 657] if (event.target.name == 'exp_date'
  24. [Dòng 665] event.inputType == 'insertCompositionText') {
  25. [Dòng 680] if (((this.valueDate.length == 4
  26. [Dòng 680] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  27. [Dòng 680] this.valueDate.length == 5)
  28. [Dòng 760] if (temp.length == 0) {
  29. [Dòng 767] return (counter % 10 == 0);
  30. [Dòng 787] } else if (this._b == 19) {
  31. [Dòng 789] } else if (this._b == 27) {
  32. [Dòng 794] if (this._b == 12) {
  33. [Dòng 796] if (this.cardTypeBank == 'bank_customer_code') {
  34. [Dòng 798] } else if (this.cardTypeBank == 'bank_account_number') {
  35. [Dòng 815] _formCard.exp_date.length == 5
  36. [Dòng 815] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
  37. [Dòng 815] this._b == 3)) {//27-pvcombank;3-TPB ;
  38. [Dòng 820] if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
  39. [Dòng 820] this._b == 19
  40. [Dòng 820] this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
  41. [Dòng 823] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  42. [Dòng 826] if (this.cardTypeOcean == 'IB') {
  43. [Dòng 828] } else if (this.cardTypeOcean == 'MB') {
  44. [Dòng 830] } else if (this.cardTypeOcean == 'ATM') {
  45. [Dòng 860] this.token_site == 'onepay'
  46. [Dòng 879] if (_re.status == '200'
  47. [Dòng 879] _re.status == '201') {
  48. [Dòng 884] if (this._res_post.state == 'approved'
  49. [Dòng 884] this._res_post.state == 'failed') {
  50. [Dòng 891] } else if (this._res_post.state == 'authorization_required') {
  51. [Dòng 909] if (this._b == 18) {
  52. [Dòng 914] if (this._b == 27
  53. [Dòng 914] this._b == 18) {
  54. [Dòng 976] if (err.status == 400
  55. [Dòng 976] err.status == 500) {
  56. [Dòng 977] if (err.error && (err.error.code == 13
  57. [Dòng 977] err.error.code == '13')) {
  58. [Dòng 1009] if ((cardNo.length == 16
  59. [Dòng 1009] if ((cardNo.length == 16 || (cardNo.length == 19
  60. [Dòng 1010] && ((this._b == 18
  61. [Dòng 1010] cardNo.length == 19) || this._b != 18)
  62. [Dòng 1023] if (this._b == +e.id) {
  63. [Dòng 1039] if (valIn == 1) {
  64. [Dòng 1041] } else if (valIn == 2) {
  65. [Dòng 1065] this._b == 3) {
  66. [Dòng 1072] if (this._b == 19) {
  67. [Dòng 1135] if (cardType == this._translate.instant('internetbanking')
  68. [Dòng 1143] } else if (cardType == this._translate.instant('mobilebanking')
  69. [Dòng 1151] } else if (cardType == this._translate.instant('atm')
  70. [Dòng 1213] this._b == 18))) {
  71. [Dòng 1220] } else if (this._b == 18
  72. [Dòng 1245] this.c_expdate = !(((this.valueDate.length == 4
  73. [Dòng 1278] this.valueDate.length == 4
  74. [Dòng 1278] this.valueDate.search('/') == -1)
  75. [Dòng 1279] this.valueDate.length == 5))

!== (13 điều kiện):
  1. [Dòng 210] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 865] key !== '3') {
  3. [Dòng 915] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 933] codeResponse.toString() !== '0') {
  5. [Dòng 989] cardNo.length !== 0) {
  6. [Dòng 1061] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 1082] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 1103] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 1123] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 1135] this.lb_card_account !== this._translate.instant('ocb_account')) {
  11. [Dòng 1143] this.lb_card_account !== this._translate.instant('ocb_phone')) {
  12. [Dòng 1151] this.lb_card_account !== this._translate.instant('ocb_card')) {
  13. [Dòng 1233] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (21 điều kiện):
  1. [Dòng 165] } else if (this._b != 18) {
  2. [Dòng 171] if (this.htmlDesc != null
  3. [Dòng 207] if (ua.indexOf('safari') != -1
  4. [Dòng 217] if (_val.value != '') {
  5. [Dòng 303] this.auth_method != null) {
  6. [Dòng 658] if (this.valueDate.length != 3) {
  7. [Dòng 815] if (_formCard.exp_date != null
  8. [Dòng 820] if (this.cardName != null
  9. [Dòng 887] if (this._res_post.links != null
  10. [Dòng 887] this._res_post.links.merchant_return != null
  11. [Dòng 887] this._res_post.links.merchant_return.href != null) {
  12. [Dòng 895] if (this._res_post.authorization != null
  13. [Dòng 895] this._res_post.authorization.links != null
  14. [Dòng 895] this._res_post.authorization.links.approval != null) {
  15. [Dòng 902] this._res_post.links.cancel != null) {
  16. [Dòng 1009] this._b != 27
  17. [Dòng 1009] this._b != 12
  18. [Dòng 1009] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  19. [Dòng 1010] this._b != 18)
  20. [Dòng 1056] if (this._b != 18
  21. [Dòng 1056] this._b != 19) {

================================================================================

📁 FILE 30: bank.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/model/bank.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 31: otp-auth.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 32: otp-auth.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
📊 Thống kê: 14 điều kiện duy nhất
   - === : 0 lần
   - == : 8 lần
   - !== : 1 lần
   - != : 5 lần
--------------------------------------------------------------------------------

== (8 điều kiện):
  1. [Dòng 92] if (this._b == 8) {//MB Bank
  2. [Dòng 96] if (this._b == 18) {//Oceanbank
  3. [Dòng 136] if (this._b == 8) {
  4. [Dòng 141] if (this._b == 18) {
  5. [Dòng 165] if (_re.status == '200'
  6. [Dòng 165] _re.status == '201') {
  7. [Dòng 174] } else if (this._res.state == 'authorization_required') {
  8. [Dòng 316] if (this._b == 18) {//8-MB Bank;18-oceanbank

!== (1 điều kiện):
  1. [Dòng 180] codeResponse.toString() !== '0') {

!= (5 điều kiện):
  1. [Dòng 170] if (this._res.links != null
  2. [Dòng 170] this._res.links.merchant_return != null
  3. [Dòng 170] this._res.links.merchant_return.href != null) {
  4. [Dòng 311] if (!(_formCard.otp != null
  5. [Dòng 317] if (!(_formCard.password != null

================================================================================

📁 FILE 33: techcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 34: techcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 35: techcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
📊 Thống kê: 16 điều kiện duy nhất
   - === : 1 lần
   - == : 12 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 68] if (this.timeLeft === 0) {

== (12 điều kiện):
  1. [Dòng 59] if (this._b == 2
  2. [Dòng 59] this._b == 5
  3. [Dòng 59] this._b == 31) {
  4. [Dòng 98] if (this._b == 2) {
  5. [Dòng 100] } else if (this._b == 5) {
  6. [Dòng 102] } else if (this._b == 6) {
  7. [Dòng 104] } else if (this._b == 31) {
  8. [Dòng 134] if (_re.status == '200'
  9. [Dòng 134] _re.status == '201') {
  10. [Dòng 139] if (this._res_post.state == 'approved'
  11. [Dòng 139] this._res_post.state == 'failed') {
  12. [Dòng 143] } else if (this._res_post.state == 'authorization_required') {

!= (3 điều kiện):
  1. [Dòng 147] if (this._res_post.authorization != null
  2. [Dòng 147] this._res_post.authorization.links != null
  3. [Dòng 147] this._res_post.authorization.links.approval != null) {

================================================================================

📁 FILE 36: vietcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 27] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  2. [Dòng 27] _inExpDate.trim().length === 0)"

== (1 điều kiện):
  1. [Dòng 46] token_site == 'onepay'

================================================================================

📁 FILE 37: vietcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 38: vietcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
📊 Thống kê: 107 điều kiện duy nhất
   - === : 4 lần
   - == : 65 lần
   - !== : 2 lần
   - != : 36 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 198] if (event.keyCode === 8
  2. [Dòng 198] event.key === "Backspace"
  3. [Dòng 436] if (approval.method === 'REDIRECT') {
  4. [Dòng 439] } else if (approval.method === 'POST_REDIRECT') {

== (65 điều kiện):
  1. [Dòng 82] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 98] if (this._b == 20
  3. [Dòng 98] this._b == 33
  4. [Dòng 98] this._b == 39
  5. [Dòng 98] this._b == 43
  6. [Dòng 98] this._b == 45
  7. [Dòng 98] this._b == 67
  8. [Dòng 98] this._b == 64) {//seabank
  9. [Dòng 105] if (this._b == 1
  10. [Dòng 105] this._b == 20
  11. [Dòng 105] this._b == 36
  12. [Dòng 105] this._b == 64
  13. [Dòng 105] this._b == 55
  14. [Dòng 105] this._b == 47
  15. [Dòng 105] this._b == 48
  16. [Dòng 105] this._b == 59) {
  17. [Dòng 125] return this._b == 9
  18. [Dòng 125] this._b == 11
  19. [Dòng 125] this._b == 16
  20. [Dòng 125] this._b == 17
  21. [Dòng 125] this._b == 25
  22. [Dòng 125] this._b == 44
  23. [Dòng 126] this._b == 54
  24. [Dòng 126] this._b == 57
  25. [Dòng 126] this._b == 59
  26. [Dòng 126] this._b == 61
  27. [Dòng 126] this._b == 63
  28. [Dòng 126] this._b == 68
  29. [Dòng 126] this._b == 69
  30. [Dòng 198] event.inputType == 'deleteContentBackward') {
  31. [Dòng 199] if (event.target.name == 'exp_date'
  32. [Dòng 207] event.inputType == 'insertCompositionText') {
  33. [Dòng 314] this.token_site == 'onepay'
  34. [Dòng 328] if (this._res_post.state == 'approved'
  35. [Dòng 328] this._res_post.state == 'failed') {
  36. [Dòng 376] } else if (this._res_post.state == 'authorization_required') {
  37. [Dòng 398] this._b == 14
  38. [Dòng 398] this._b == 15
  39. [Dòng 398] this._b == 24
  40. [Dòng 398] this._b == 8
  41. [Dòng 398] this._b == 10
  42. [Dòng 398] this._b == 22
  43. [Dòng 398] this._b == 23
  44. [Dòng 398] this._b == 30
  45. [Dòng 398] this._b == 17) {
  46. [Dòng 467] if (err.status == 400
  47. [Dòng 467] err.status == 500) {
  48. [Dòng 468] if (err.error && (err.error.code == 13
  49. [Dòng 468] err.error.code == '13')) {
  50. [Dòng 481] if ((cardNo.length == 16
  51. [Dòng 482] (cardNo.length == 19
  52. [Dòng 482] (cardNo.length == 19 && (this._b == 1
  53. [Dòng 482] this._b == 4
  54. [Dòng 482] this._b == 59))
  55. [Dòng 484] this._util.checkMod10(cardNo) == true
  56. [Dòng 520] return ((value.length == 4
  57. [Dòng 520] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  58. [Dòng 520] value.length == 5) && parseInt(value.split('/')[0]
  59. [Dòng 524] || (this._util.checkValidExpireMonthYear(value) && (this._b == 20
  60. [Dòng 525] this._b == 67)))
  61. [Dòng 553] this._inExpDate.length == 4
  62. [Dòng 553] this._inExpDate.search('/') == -1)
  63. [Dòng 554] this._inExpDate.length == 5))
  64. [Dòng 556] || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 20
  65. [Dòng 556] this._b == 67)));

!== (2 điều kiện):
  1. [Dòng 341] codeResponse.toString() !== '0') {
  2. [Dòng 399] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (36 điều kiện):
  1. [Dòng 110] if (this.htmlDesc != null
  2. [Dòng 135] if (ua.indexOf('safari') != -1
  3. [Dòng 200] if (this._inExpDate.length != 3) {
  4. [Dòng 280] if (this._b != 9
  5. [Dòng 280] this._b != 11
  6. [Dòng 280] this._b != 16
  7. [Dòng 280] this._b != 17
  8. [Dòng 280] this._b != 25
  9. [Dòng 280] this._b != 36
  10. [Dòng 280] this._b != 44
  11. [Dòng 280] this._b != 54
  12. [Dòng 281] this._b != 57
  13. [Dòng 281] this._b != 59
  14. [Dòng 281] this._b != 61
  15. [Dòng 281] this._b != 63
  16. [Dòng 281] this._b != 68
  17. [Dòng 281] this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
  18. [Dòng 292] '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71'].indexOf(this._b.toString()) != -1) {
  19. [Dòng 330] if (this._res_post.return_url != null) {
  20. [Dòng 333] if (this._res_post.links != null
  21. [Dòng 333] this._res_post.links.merchant_return != null
  22. [Dòng 333] this._res_post.links.merchant_return.href != null) {
  23. [Dòng 381] if (this._res_post.authorization != null
  24. [Dòng 381] this._res_post.authorization.links != null
  25. [Dòng 386] this._res_post.links.cancel != null) {
  26. [Dòng 392] let userName = _formCard.name != null ? _formCard.name : ''
  27. [Dòng 393] this._res_post.authorization.links.approval != null
  28. [Dòng 393] this._res_post.authorization.links.approval.href != null) {
  29. [Dòng 396] userName = paramUserName != null ? paramUserName : ''
  30. [Dòng 522] this._b != 20
  31. [Dòng 522] this._b != 33
  32. [Dòng 522] this._b != 39
  33. [Dòng 523] this._b != 43
  34. [Dòng 523] this._b != 45
  35. [Dòng 523] this._b != 64
  36. [Dòng 523] this._b != 67)

================================================================================

📁 FILE 39: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 40: domescard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/domescard-main.component.html
📊 Thống kê: 62 điều kiện duy nhất
   - === : 1 lần
   - == : 61 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 15] filteredData.length === 0"

== (61 điều kiện):
  1. [Dòng 23] _auth==0
  2. [Dòng 23] *ngIf="!token&&_auth==0&&(_b==1
  3. [Dòng 23] _b==4
  4. [Dòng 23] _b==7
  5. [Dòng 23] _b==8
  6. [Dòng 23] _b==9
  7. [Dòng 23] _b==10
  8. [Dòng 23] _b==11
  9. [Dòng 23] _b==14
  10. [Dòng 23] _b==15
  11. [Dòng 23] _b==16
  12. [Dòng 23] _b==17
  13. [Dòng 23] _b==20
  14. [Dòng 24] _b==22
  15. [Dòng 24] _b==23
  16. [Dòng 24] _b==24
  17. [Dòng 24] _b==25
  18. [Dòng 24] _b==30
  19. [Dòng 24] _b==33
  20. [Dòng 24] _b==34
  21. [Dòng 24] _b==35
  22. [Dòng 24] _b==36
  23. [Dòng 24] _b==37
  24. [Dòng 24] _b==38
  25. [Dòng 24] _b==39
  26. [Dòng 24] _b==40
  27. [Dòng 24] _b==41
  28. [Dòng 25] _b==42
  29. [Dòng 25] _b==43
  30. [Dòng 25] _b==44
  31. [Dòng 25] _b==45
  32. [Dòng 25] this._b==46
  33. [Dòng 25] _b==47
  34. [Dòng 25] _b==48
  35. [Dòng 25] _b==49
  36. [Dòng 25] _b==50
  37. [Dòng 25] _b==51
  38. [Dòng 25] _b==52
  39. [Dòng 25] _b==53
  40. [Dòng 25] _b==54
  41. [Dòng 25] _b==55
  42. [Dòng 25] _b==56
  43. [Dòng 25] _b==57
  44. [Dòng 25] _b==58
  45. [Dòng 25] _b==59
  46. [Dòng 25] _b==60
  47. [Dòng 26] _b==61
  48. [Dòng 26] _b==62
  49. [Dòng 26] _b==63
  50. [Dòng 26] _b==64
  51. [Dòng 26] this._b==65
  52. [Dòng 26] _b==66
  53. [Dòng 26] _b==67
  54. [Dòng 26] _b==68
  55. [Dòng 26] _b==69
  56. [Dòng 26] this._b==70
  57. [Dòng 26] this._b==71)">
  58. [Dòng 30] _auth==0&&(_b==6 || _b==2 || _b==5 || _b == 31)
  59. [Dòng 30] _b == 31)">
  60. [Dòng 33] _auth==0&&(_b==3||_b==12||_b==18||_b==19||_b==27)
  61. [Dòng 38] token || _auth==1

================================================================================

📁 FILE 41: domescard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/domescard-main/domescard-main.component.ts
📊 Thống kê: 51 điều kiện duy nhất
   - === : 24 lần
   - == : 20 lần
   - !== : 1 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (24 điều kiện):
  1. [Dòng 217] if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
  2. [Dòng 218] filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
  3. [Dòng 258] if (valOut === 'auth') {
  4. [Dòng 325] if (this._b === '1'
  5. [Dòng 325] this._b === '20'
  6. [Dòng 325] this._b === '64') {
  7. [Dòng 328] if (this._b === '36'
  8. [Dòng 328] this._b === '18'
  9. [Dòng 328] this._b === '19'
  10. [Dòng 331] if (this._b === '19'
  11. [Dòng 331] this._b === '16'
  12. [Dòng 331] this._b === '25'
  13. [Dòng 331] this._b === '33'
  14. [Dòng 332] this._b === '39'
  15. [Dòng 332] this._b === '9'
  16. [Dòng 332] this._b === '11'
  17. [Dòng 332] this._b === '17'
  18. [Dòng 333] this._b === '36'
  19. [Dòng 333] this._b === '44'
  20. [Dòng 333] this._b === '12'
  21. [Dòng 334] this._b === '64'
  22. [Dòng 337] if (this._b === '20'
  23. [Dòng 340] if (this._b === '12'
  24. [Dòng 340] this._b === '18') {

== (20 điều kiện):
  1. [Dòng 147] this._auth == 0
  2. [Dòng 147] this.tokenList.length == 0) {
  3. [Dòng 207] this.filteredData.length == 1
  4. [Dòng 240] $event == 'true') {
  5. [Dòng 328] this._b == '55'
  6. [Dòng 328] this._b == '47'
  7. [Dòng 328] this._b == '48'
  8. [Dòng 328] this._b == '59') {
  9. [Dòng 331] this._b == '3'
  10. [Dòng 332] this._b == '43'
  11. [Dòng 332] this._b == '45'
  12. [Dòng 333] this._b == '54'
  13. [Dòng 333] this._b == '57'
  14. [Dòng 334] this._b == '59'
  15. [Dòng 334] this._b == '61'
  16. [Dòng 334] this._b == '63'
  17. [Dòng 334] this._b == '67'
  18. [Dòng 334] this._b == '68'
  19. [Dòng 334] this._b == '69') {
  20. [Dòng 337] this._b == '67') {

!== (1 điều kiện):
  1. [Dòng 98] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (6 điều kiện):
  1. [Dòng 134] if (params['locale'] != null) {
  2. [Dòng 140] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 169] if (!(strInstrument != null
  4. [Dòng 172] if (strInstrument.substring(0, 1) != '^'
  5. [Dòng 172] strInstrument.substr(strInstrument.length - 1) != '$') {
  6. [Dòng 267] if (bankid != null) {

================================================================================

📁 FILE 42: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/intercard-main/dialog-guide-dialog.html
📊 Thống kê: 8 điều kiện duy nhất
   - === : 0 lần
   - == : 8 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (8 điều kiện):
  1. [Dòng 30] data['type'] == '5'
  2. [Dòng 30] data['type'] == '7'
  3. [Dòng 30] data['type'] == '9'
  4. [Dòng 30] data['type'] == '10'"
  5. [Dòng 39] data['type'] == '7'"
  6. [Dòng 40] data['type'] == '9'"
  7. [Dòng 49] data['type'] == '6'
  8. [Dòng 49] data['type'] == '8'"

================================================================================

📁 FILE 43: intercard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/intercard-main/intercard-main.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 168] token_site == 'onepay'

!= (1 điều kiện):
  1. [Dòng 48] _showCardName!=true"

================================================================================

📁 FILE 44: intercard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/intercard-main/intercard-main.component.ts
📊 Thống kê: 89 điều kiện duy nhất
   - === : 11 lần
   - == : 54 lần
   - !== : 11 lần
   - != : 13 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 153] if (target.tagName === 'A'
  2. [Dòng 292] if (_formCard.country === 'default') {
  3. [Dòng 614] if (event.keyCode === 8
  4. [Dòng 614] event.key === "Backspace"
  5. [Dòng 711] if ((v.substr(-1) === ' '
  6. [Dòng 896] this._i_country_code === 'US') {
  7. [Dòng 934] const insertIndex = this._i_country_code === 'US' ? 5 : 3
  8. [Dòng 936] if (temp[i] === '-'
  9. [Dòng 936] temp[i] === ' ') {
  10. [Dòng 943] insertIndex === 3 ? ' ' : itemRemoved)
  11. [Dòng 988] this.c_country = _val.value === 'default'

== (54 điều kiện):
  1. [Dòng 173] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 218] regexp.test('card;;visa;USD')) && (this._type == 5
  3. [Dòng 218] this._type == 7);
  4. [Dòng 219] regexp.test('card;;mastercard;USD')) && (this._type == 5
  5. [Dòng 220] regexp.test('card;;amex;USD')) && (this._type == 6
  6. [Dòng 220] this._type == 8);
  7. [Dòng 221] regexp.test('card;;jcb;USD')) && (this._type == 9);
  8. [Dòng 222] regexp.test('card;;cup;USD')) && (this._type == 10);
  9. [Dòng 332] this.token_site == 'onepay'
  10. [Dòng 358] if (this._res_post.state == 'approved'
  11. [Dòng 358] this._res_post.state == 'failed') {
  12. [Dòng 385] } else if (this._res_post.state == 'failed') { // lỗi mới kiểm tra sang trang lỗi
  13. [Dòng 414] } else if (this._res_post.state == 'authorization_required') {
  14. [Dòng 415] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  15. [Dòng 427] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  16. [Dòng 453] if (err.status == 400
  17. [Dòng 453] err.status == 500) {
  18. [Dòng 454] if (err.error && (err.error.code == 8
  19. [Dòng 454] err.error.code == '8')) {
  20. [Dòng 455] if (this._type == 5
  21. [Dòng 455] this._type == 6) {
  22. [Dòng 457] } else if (this._type == 7
  23. [Dòng 457] this._type == 8) {
  24. [Dòng 460] } else if (err.error && (err.error.code == 13
  25. [Dòng 460] err.error.code == '13')) {
  26. [Dòng 526] v.length == 15) || (v.length == 16
  27. [Dòng 526] v.length == 19))
  28. [Dòng 527] this._util.checkMod10(v) == true) {
  29. [Dòng 565] cardNo.length == 15)
  30. [Dòng 567] cardNo.length == 16)
  31. [Dòng 568] cardNo.startsWith('81')) && (cardNo.length == 16
  32. [Dòng 568] cardNo.length == 19))
  33. [Dòng 614] event.inputType == 'deleteContentBackward') {
  34. [Dòng 615] if (event.target.name == 'exp_date'
  35. [Dòng 623] event.inputType == 'insertCompositionText') {
  36. [Dòng 638] if (((this.valueDate.length == 4
  37. [Dòng 638] this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  38. [Dòng 638] this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  39. [Dòng 711] v.length == 5) {
  40. [Dòng 719] v.length == 4
  41. [Dòng 723] v.length == 3)
  42. [Dòng 759] _val.value.length == 4
  43. [Dòng 763] _val.value.length == 3)
  44. [Dòng 950] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  45. [Dòng 950] this.valueDate.length == 5)
  46. [Dòng 977] this.requireAvs = this.AVS_COUNTRIES.find(e => e.code == this._i_country_code)? true : false;
  47. [Dòng 1019] this.valueDate.length == 4
  48. [Dòng 1019] this.valueDate.search('/') == -1)
  49. [Dòng 1020] this.valueDate.length == 5))
  50. [Dòng 1033] this._i_csc.length == 4) ||
  51. [Dòng 1037] this._i_csc.length == 3)
  52. [Dòng 1123] country = this.AVS_COUNTRIES.find(e => e.code == res.bin_country);
  53. [Dòng 1160] countryCode == 'US' ? US_STATES
  54. [Dòng 1161] : countryCode == 'CA' ? CA_STATES

!== (11 điều kiện):
  1. [Dòng 340] if (this.tracking.hasOwnProperty(key) && ((key !== '8'
  2. [Dòng 340] !this._showNameOnCard) || (key !== '9'
  3. [Dòng 368] codeResponse.toString() !== '0'){
  4. [Dòng 711] event.inputType !== 'deleteContentBackward') || v.length == 5) {
  5. [Dòng 899] this._i_country_code !== 'US') {
  6. [Dòng 942] itemRemoved !== '') {
  7. [Dòng 969] if (deviceValue !== 'default') {
  8. [Dòng 973] this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
  9. [Dòng 1050] this._i_country_code !== 'default'
  10. [Dòng 1079] this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
  11. [Dòng 1086] this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {

!= (13 điều kiện):
  1. [Dòng 202] if (params['locale'] != null) {
  2. [Dòng 208] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 360] if (this._res_post.return_url != null) {
  4. [Dòng 362] } else if (this._res_post.links != null
  5. [Dòng 362] this._res_post.links.merchant_return != null
  6. [Dòng 362] this._res_post.links.merchant_return.href != null) {
  7. [Dòng 508] if (ua.indexOf('safari') != -1
  8. [Dòng 565] cardNo != null
  9. [Dòng 616] if (this.valueDate.length != 3) {
  10. [Dòng 718] v != null
  11. [Dòng 758] this.c_csc = (!(_val.value != null
  12. [Dòng 1031] this._i_csc != null
  13. [Dòng 1125] this.requireAvs = this.isAvsCountry = country != undefined

================================================================================

📁 FILE 45: menu.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/menu.component.html
📊 Thống kê: 9 điều kiện duy nhất
   - === : 9 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (9 điều kiện):
  1. [Dòng 97] type === 7"
  2. [Dòng 98] [ngStyle]="{'border-color': type === 7 ? this.themeColor.border_color : border_color}"
  3. [Dòng 134] type === 9"
  4. [Dòng 135] [ngStyle]="{'border-color': type === 9 ? this.themeColor.border_color : border_color}"
  5. [Dòng 165] type === 10"
  6. [Dòng 166] [ngStyle]="{'border-color': type === 10 ? this.themeColor.border_color : border_color}"
  7. [Dòng 196] type === 8"
  8. [Dòng 197] [ngStyle]="{'border-color': type === 8 ? this.themeColor.border_color : border_color}"
  9. [Dòng 230] d_vrbank===true"

================================================================================

📁 FILE 46: menu.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/menu.component.ts
📊 Thống kê: 133 điều kiện duy nhất
   - === : 9 lần
   - == : 65 lần
   - !== : 3 lần
   - != : 56 lần
--------------------------------------------------------------------------------

=== (9 điều kiện):
  1. [Dòng 666] this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number === 1
  2. [Dòng 699] if (this._res.state === 'unpaid'
  3. [Dòng 699] this._res.state === 'not_paid') {
  4. [Dòng 783] if ('op' === auth
  5. [Dòng 818] } else if ('bank' === auth
  6. [Dòng 823] if (approval.method === 'REDIRECT') {
  7. [Dòng 826] } else if (approval.method === 'POST_REDIRECT') {
  8. [Dòng 1058] return id === 'amex' ? '1234' : '123'
  9. [Dòng 1203] if (this.timeLeftPaypal === 0) {

== (65 điều kiện):
  1. [Dòng 166] if (el == 5) {
  2. [Dòng 168] } else if (el == 6) {
  3. [Dòng 170] } else if (el == 7) {
  4. [Dòng 172] } else if (el == 8) {
  5. [Dòng 174] } else if (el == 2) {
  6. [Dòng 176] } else if (el == 4) {
  7. [Dòng 178] } else if (el == 3) {
  8. [Dòng 180] } else if (el == 9) {
  9. [Dòng 202] if (!isNaN(_re.status) && (_re.status == '200'
  10. [Dòng 202] _re.status == '201') && _re.body != null) {
  11. [Dòng 207] if (('closed' == this._res_polling.state
  12. [Dòng 207] 'canceled' == this._res_polling.state
  13. [Dòng 207] 'expired' == this._res_polling.state)
  14. [Dòng 227] } else if ('paid' == this._res_polling.state) {
  15. [Dòng 237] this._res_polling.payments == null) {
  16. [Dòng 239] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  17. [Dòng 241] this._paymentService.getCurrentPage() == 'enter_card') {
  18. [Dòng 244] } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
  19. [Dòng 244] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
  20. [Dòng 261] } else if ('not_paid' == this._res_polling.state) {
  21. [Dòng 273] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
  22. [Dòng 397] if (message == '1') {
  23. [Dòng 411] if (_re.status == '200'
  24. [Dòng 411] _re.status == '201') {
  25. [Dòng 433] if (this.type == 5
  26. [Dòng 436] } else if (this.type == 6
  27. [Dòng 439] } else if (this.type == 2
  28. [Dòng 442] } else if (this.type == 7
  29. [Dòng 445] } else if (this.type == 9
  30. [Dòng 448] } else if (this.type == 10
  31. [Dòng 451] } else if (this.type == 8
  32. [Dòng 454] } else if (this.type == 4
  33. [Dòng 457] } else if (this.type == 3
  34. [Dòng 672] this._auth == 0) {
  35. [Dòng 700] if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
  36. [Dòng 700] this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
  37. [Dòng 702] if (this._res.payments[this._res.payments.length - 1].state == 'approved'
  38. [Dòng 704] } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
  39. [Dòng 738] } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  40. [Dòng 738] this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
  41. [Dòng 772] } else if (idBrand == 'atm'
  42. [Dòng 846] if ('paid' == this._res.state) {
  43. [Dòng 847] this._res.merchant.token_site == 'onepay')) {
  44. [Dòng 872] if (('closed' == this._res.state
  45. [Dòng 872] 'canceled' == this._res.state
  46. [Dòng 872] 'expired' == this._res.state
  47. [Dòng 872] 'paid' == this._res.state)
  48. [Dòng 898] this._res.payments == null) {
  49. [Dòng 900] this._res.payments[this._res.payments.length - 1].state == 'pending') {
  50. [Dòng 910] if (this._res.currencies[0] == 'USD') {
  51. [Dòng 974] if (item.instrument.issuer.brand.id == 'atm') {
  52. [Dòng 976] } else if (item.instrument.issuer.brand.id == 'visa'
  53. [Dòng 976] item.instrument.issuer.brand.id == 'mastercard') {
  54. [Dòng 977] if (item.instrument.issuer_location == 'd') {
  55. [Dòng 982] } else if (item.instrument.issuer.brand.id == 'amex') {
  56. [Dòng 988] } else if (item.instrument.issuer.brand.id == 'jcb') {
  57. [Dòng 1178] this.tokenList.length == 1) {
  58. [Dòng 1179] if (_val == 2
  59. [Dòng 1191] if (this.type == 4) {
  60. [Dòng 1271] if (this._res_post.state == 'approved'
  61. [Dòng 1271] this._res_post.state == 'failed') {
  62. [Dòng 1280] } else if (this._res_post.state == 'authorization_required') {
  63. [Dòng 1281] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  64. [Dòng 1295] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  65. [Dòng 1367] if (data._locale == 'en') {

!== (3 điều kiện):
  1. [Dòng 795] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  2. [Dòng 1157] if (_val !== 3) {
  3. [Dòng 1161] this._util.getElementParams(this.currentUrl, 't_id') !== '') {

!= (56 điều kiện):
  1. [Dòng 196] if (this._idInvoice != null
  2. [Dòng 196] this._paymentService.getState() != 'error') {
  3. [Dòng 202] _re.body != null) {
  4. [Dòng 208] this._res_polling.links != null
  5. [Dòng 208] this._res_polling.links.merchant_return != null //
  6. [Dòng 237] } else if (this._res_polling.merchant != null
  7. [Dòng 237] this._res_polling.merchant_invoice_reference != null
  8. [Dòng 239] } else if (this._res_polling.payments != null
  9. [Dòng 245] this._res_polling.links.merchant_return != null//
  10. [Dòng 264] this._res_polling.payments != null
  11. [Dòng 272] if (this._res_polling.payments != null
  12. [Dòng 277] t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
  13. [Dòng 377] this.type.toString().length != 0) {
  14. [Dòng 383] if (params['locale'] != null) {
  15. [Dòng 390] if ('otp' != this._paymentService.getCurrentPage()) {
  16. [Dòng 403] if (this._paymentService.getInvoiceDetail() != null) {
  17. [Dòng 683] if (count != 1) {
  18. [Dòng 689] if (this._res.merchant != null
  19. [Dòng 689] this._res.merchant_invoice_reference != null) {
  20. [Dòng 692] if (this._res.merchant.address_details != null) {
  21. [Dòng 700] this._res.links != null//
  22. [Dòng 738] } else if (this._res.payments != null
  23. [Dòng 739] this._res.payments[this._res.payments.length - 1].instrument != null
  24. [Dòng 739] this._res.payments[this._res.payments.length - 1].instrument.issuer != null
  25. [Dòng 740] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
  26. [Dòng 740] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
  27. [Dòng 741] this._res.payments[this._res.payments.length - 1].links != null
  28. [Dòng 741] this._res.payments[this._res.payments.length - 1].links.cancel != null
  29. [Dòng 741] this._res.payments[this._res.payments.length - 1].links.cancel.href != null
  30. [Dòng 757] this._res.payments[this._res.payments.length - 1].links.update != null
  31. [Dòng 757] this._res.payments[this._res.payments.length - 1].links.update.href != null) {
  32. [Dòng 772] this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
  33. [Dòng 773] this._res.payments[this._res.payments.length - 1].authorization != null
  34. [Dòng 773] this._res.payments[this._res.payments.length - 1].authorization.links != null) {
  35. [Dòng 783] this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
  36. [Dòng 783] this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
  37. [Dòng 786] if (this._res.payments[this._res.payments.length - 1].authorization != null
  38. [Dòng 786] this._res.payments[this._res.payments.length - 1].authorization.links != null
  39. [Dòng 792] auth = paramUserName != null ? paramUserName : ''
  40. [Dòng 873] this._res.links != null
  41. [Dòng 873] this._res.links.merchant_return != null //
  42. [Dòng 898] } else if (this._res.merchant != null
  43. [Dòng 898] this._res.merchant_invoice_reference != null
  44. [Dòng 1002] if (['mastercard', 'visa', 'amex', 'jcb', 'cup', 'card', 'np_card', 'atm'].indexOf(id) != -1) {
  45. [Dòng 1004] } else if (['techcombank_account', 'vib_account', 'dongabank_account', 'tpbank_account', 'bidv_account', 'pvcombank_account', 'shb_account', 'oceanbank_online_account'].indexOf(id) != -1) {
  46. [Dòng 1006] } else if (['oceanbank_mobile_account'].indexOf(id) != -1) {
  47. [Dòng 1008] } else if (['shb_customer_id'].indexOf(id) != -1) {
  48. [Dòng 1025] if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1) {
  49. [Dòng 1053] return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
  50. [Dòng 1090] if (!(strInstrument != null
  51. [Dòng 1107] if (this._translate.currentLang != language) {
  52. [Dòng 1163] } else if (this._res.payments != null) {
  53. [Dòng 1273] if (this._res_post.return_url != null) {
  54. [Dòng 1275] } else if (this._res_post.links != null
  55. [Dòng 1275] this._res_post.links.merchant_return != null
  56. [Dòng 1275] this._res_post.links.merchant_return.href != null) {

================================================================================

📁 FILE 47: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 48: qr-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 49: qr-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 41] 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {

================================================================================

📁 FILE 50: qr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/qr-main/qr-main.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 5] filteredData.length === 0
  2. [Dòng 5] filteredDataOther.length === 0"

================================================================================

📁 FILE 51: qr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/qr-main/qr-main.component.ts
📊 Thống kê: 12 điều kiện duy nhất
   - === : 5 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 176] this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
  2. [Dòng 177] this.filteredDataOther = this.appList.filter(item => item.type === 'other');
  3. [Dòng 195] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  4. [Dòng 196] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  5. [Dòng 202] if (item.type === 'mobile_banking') {

== (3 điều kiện):
  1. [Dòng 201] if (item.available == true) {
  2. [Dòng 244] if (_re.status == '200'
  3. [Dòng 244] _re.status == '201') {

!= (4 điều kiện):
  1. [Dòng 136] if (params['locale'] != null) {
  2. [Dòng 142] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 156] if (!(strInstrument != null
  4. [Dòng 225] if (appcode != null) {

================================================================================

📁 FILE 52: remove-token-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/token-main/remove-token-dialog/remove-token-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 53: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/token-main/token-dialog/dialog-guide-dialog.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (6 điều kiện):
  1. [Dòng 9] data['type'] == 'Visa'
  2. [Dòng 9] data['type'] == 'Master'
  3. [Dòng 9] data['type'] == 'JCB'"
  4. [Dòng 17] data['type'] == 'Visa'"
  5. [Dòng 17] data['type'] == 'Master'"
  6. [Dòng 24] data['type'] == 'Amex'"

================================================================================

📁 FILE 54: token-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/token-main/token-main.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 1 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 8] style="{{item.brand_id === 'visa' ? 'height: 14.42px

== (1 điều kiện):
  1. [Dòng 21] token_main == '1'"

!= (1 điều kiện):
  1. [Dòng 16] item['feeService']['fee'] != 0"

================================================================================

📁 FILE 55: token-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/main/menu/token-main/token-main.component.ts
📊 Thống kê: 65 điều kiện duy nhất
   - === : 6 lần
   - == : 39 lần
   - !== : 1 lần
   - != : 19 lần
--------------------------------------------------------------------------------

=== (6 điều kiện):
  1. [Dòng 74] if (event.keyCode === 13) {
  2. [Dòng 174] && ((item.brand_id === 'amex'
  3. [Dòng 191] return id === 'amex' ? '1234' : '123'
  4. [Dòng 332] if (approval.method === 'REDIRECT') {
  5. [Dòng 335] } else if (approval.method === 'POST_REDIRECT') {
  6. [Dòng 406] return this._i_token_otp != null && !isNaN(+this._i_token_otp) && ((id === 'amex'

== (39 điều kiện):
  1. [Dòng 97] if (message == '0') {
  2. [Dòng 132] item.id == element.id ? element['active'] = true : element['active'] = false
  3. [Dòng 136] item.display_cvv == true ? this.flagToken = true : this.flagToken = false
  4. [Dòng 157] if (result == 'success') {
  5. [Dòng 162] if (this.tokenList.length == 0) {
  6. [Dòng 166] } else if (result == 'error') {
  7. [Dòng 174] _val.value.length == 4) || (item.brand_id != 'amex'
  8. [Dòng 174] _val.value.length == 3))
  9. [Dòng 184] _val.value.length == 3)));
  10. [Dòng 190] id == 'amex' ? this.maxLength = 4 : this.maxLength = 3
  11. [Dòng 211] if (_re.body.state == 'more_info_required') {
  12. [Dòng 226] if (this._res_post.state == 'approved'
  13. [Dòng 226] this._res_post.state == 'failed') {
  14. [Dòng 233] if (this._res_post.state == 'failed') {
  15. [Dòng 249] } else if (this._res_post.state == 'authorization_required') {
  16. [Dòng 250] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  17. [Dòng 262] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  18. [Dòng 278] } else if (_re.body.state == 'authorization_required') {
  19. [Dòng 295] if (this._b == 1
  20. [Dòng 295] this._b == 14
  21. [Dòng 295] this._b == 15
  22. [Dòng 295] this._b == 24
  23. [Dòng 295] this._b == 8
  24. [Dòng 295] this._b == 10
  25. [Dòng 295] this._b == 20
  26. [Dòng 295] this._b == 22
  27. [Dòng 295] this._b == 23
  28. [Dòng 295] this._b == 30
  29. [Dòng 295] this._b == 11
  30. [Dòng 295] this._b == 16
  31. [Dòng 295] this._b == 17
  32. [Dòng 295] this._b == 18
  33. [Dòng 295] this._b == 27) {
  34. [Dòng 354] } else if (_re.body.state == 'failed') {
  35. [Dòng 398] if (action == 'blur') {
  36. [Dòng 406] this._i_token_otp.length == 4)
  37. [Dòng 407] this._i_token_otp.length == 3));
  38. [Dòng 460] if (_re.status == '200'
  39. [Dòng 460] _re.status == '201') {

!== (1 điều kiện):
  1. [Dòng 122] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (19 điều kiện):
  1. [Dòng 90] if (params['locale'] != null) {
  2. [Dòng 173] if (_val.value != null
  3. [Dòng 183] this.c_token_otp_csc = !(_val.value != null
  4. [Dòng 228] if (this._res_post.return_url != null) {
  5. [Dòng 230] } else if (this._res_post.links != null
  6. [Dòng 230] this._res_post.links.merchant_return != null
  7. [Dòng 230] this._res_post.links.merchant_return.href != null) {
  8. [Dòng 283] if (_re.body.authorization != null
  9. [Dòng 283] _re.body.authorization.links != null
  10. [Dòng 290] if (_re.body.links != null
  11. [Dòng 290] _re.body.links.cancel != null) {
  12. [Dòng 356] if (_re.body.return_url != null) {
  13. [Dòng 358] } else if (_re.body.links != null
  14. [Dòng 358] _re.body.links.merchant_return != null
  15. [Dòng 358] _re.body.links.merchant_return.href != null) {
  16. [Dòng 389] return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
  17. [Dòng 394] if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(item.brand_id) != -1
  18. [Dòng 406] return this._i_token_otp != null
  19. [Dòng 407] || (id != 'amex'

================================================================================

📁 FILE 56: overlay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/overlay/overlay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 57: overlay.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/overlay/overlay.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 58: overlay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/overlay/overlay.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 59: close-dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/services/close-dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 60: data.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/services/data.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 63] const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
  2. [Dòng 63] item.method === method) : null;

================================================================================

📁 FILE 61: dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/services/dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 62: fee.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/services/fee.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 63: multiple_method.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/services/multiple_method.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 64: payment.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/services/payment.service.ts
📊 Thống kê: 8 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 8 lần
--------------------------------------------------------------------------------

!= (8 điều kiện):
  1. [Dòng 109] if (idInvoice != null
  2. [Dòng 109] idInvoice != 0)
  3. [Dòng 119] idInvoice != 0) {
  4. [Dòng 264] if (this._merchantid != null
  5. [Dòng 264] this._tranref != null
  6. [Dòng 264] this._state != null
  7. [Dòng 325] let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
  8. [Dòng 358] urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');

================================================================================

📁 FILE 65: token-main.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/services/token-main.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 66: index.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/translate/index.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 67: lang-en.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/translate/lang-en.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 68: lang-vi.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/translate/lang-vi.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 69: translate.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/translate/translate.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 70: translate.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/translate/translate.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 37] if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
  2. [Dòng 38] else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';

================================================================================

📁 FILE 71: translations.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/translate/translations.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 72: apps-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/util/apps-info.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 149] if (e.name == bankSwift) { // TODO: get by swift

================================================================================

📁 FILE 73: banks-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/util/banks-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 937] if (+e.id == bankId) {
  2. [Dòng 987] if (e.swiftCode == bankSwift) {

================================================================================

📁 FILE 74: iso-ca-states.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/util/iso-ca-states.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 75: iso-us-states.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/util/iso-us-states.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 76: util.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/app/util/util.ts
📊 Thống kê: 25 điều kiện duy nhất
   - === : 11 lần
   - == : 9 lần
   - !== : 3 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 54] if (v.length === 2
  2. [Dòng 54] this.flag.length === 3
  3. [Dòng 54] this.flag.charAt(this.flag.length - 1) === '/') {
  4. [Dòng 58] if (v.length === 1) {
  5. [Dòng 60] } else if (v.length === 2) {
  6. [Dòng 63] v.length === 2) {
  7. [Dòng 71] if (len === 2) {
  8. [Dòng 143] if (M[1] === 'Chrome') {
  9. [Dòng 278] if (param === key) {
  10. [Dòng 461] pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
  11. [Dòng 465] target === 0

== (9 điều kiện):
  1. [Dòng 13] if (temp.length == 0) {
  2. [Dòng 20] return (counter % 10 == 0);
  3. [Dòng 24] if (currency == 'USD') {
  4. [Dòng 117] if (this.checkCount == 1) {
  5. [Dòng 129] if (results == null) {
  6. [Dòng 162] if (c.length == 3) {
  7. [Dòng 175] d = d == undefined ? '.' : d
  8. [Dòng 176] t = t == undefined ? '
  9. [Dòng 266] return results == null ? null : results[1]

!== (3 điều kiện):
  1. [Dòng 273] queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
  2. [Dòng 274] if (queryString !== '') {
  3. [Dòng 465] if (target !== 0

!= (2 điều kiện):
  1. [Dòng 145] if (tem != null) {
  2. [Dòng 150] if ((tem = ua.match(/version\/(\d+)/i)) != null) {

================================================================================

📁 FILE 77: qrcode.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/assets/script/qrcode.js
📊 Thống kê: 67 điều kiện duy nhất
   - === : 2 lần
   - == : 53 lần
   - !== : 0 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 2255] if (typeof define === 'function'
  2. [Dòng 2257] } else if (typeof exports === 'object') {

== (53 điều kiện):
  1. [Dòng 68] if (_dataCache == null) {
  2. [Dòng 85] if ( (0 <= r && r <= 6 && (c == 0
  3. [Dòng 85] c == 6) )
  4. [Dòng 86] || (0 <= c && c <= 6 && (r == 0
  5. [Dòng 86] r == 6) )
  6. [Dòng 107] if (i == 0
  7. [Dòng 122] _modules[r][6] = (r % 2 == 0);
  8. [Dòng 129] _modules[6][c] = (c % 2 == 0);
  9. [Dòng 152] if (r == -2
  10. [Dòng 152] r == 2
  11. [Dòng 152] c == -2
  12. [Dòng 152] c == 2
  13. [Dòng 153] || (r == 0
  14. [Dòng 153] c == 0) ) {
  15. [Dòng 169] ( (bits >> i) & 1) == 1);
  16. [Dòng 226] if (col == 6) col -= 1;
  17. [Dòng 232] if (_modules[row][col - c] == null) {
  18. [Dòng 237] dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
  19. [Dòng 249] if (bitIndex == -1) {
  20. [Dòng 458] margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
  21. [Dòng 498] if (typeof arguments[0] == 'object') {
  22. [Dòng 587] margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
  23. [Dòng 733] if (b == -1) throw 'eof';
  24. [Dòng 741] if (b0 == -1) break;
  25. [Dòng 767] if (typeof b == 'number') {
  26. [Dòng 768] if ( (b & 0xff) == b) {
  27. [Dòng 910] return function(i, j) { return (i + j) % 2 == 0
  28. [Dòng 912] return function(i, j) { return i % 2 == 0
  29. [Dòng 914] return function(i, j) { return j % 3 == 0
  30. [Dòng 916] return function(i, j) { return (i + j) % 3 == 0
  31. [Dòng 918] return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
  32. [Dòng 920] return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
  33. [Dòng 922] return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
  34. [Dòng 924] return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
  35. [Dòng 1011] if (r == 0
  36. [Dòng 1011] c == 0) {
  37. [Dòng 1015] if (dark == qrcode.isDark(row + r, col + c) ) {
  38. [Dòng 1036] if (count == 0
  39. [Dòng 1036] count == 4) {
  40. [Dòng 1149] if (typeof num.length == 'undefined') {
  41. [Dòng 1155] num[offset] == 0) {
  42. [Dòng 1495] if (typeof rsBlock == 'undefined') {
  43. [Dòng 1538] return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
  44. [Dòng 1543] _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
  45. [Dòng 1599] if (data.length - i == 1) {
  46. [Dòng 1601] } else if (data.length - i == 2) {
  47. [Dòng 1866] } else if (n == 62) {
  48. [Dòng 1868] } else if (n == 63) {
  49. [Dòng 1928] if (_buflen == 0) {
  50. [Dòng 1937] if (c == '=') {
  51. [Dòng 1961] } else if (c == 0x2b) {
  52. [Dòng 1963] } else if (c == 0x2f) {
  53. [Dòng 2133] if (table.size() == (1 << bitLength) ) {

!= (12 điều kiện):
  1. [Dòng 119] if (_modules[r][6] != null) {
  2. [Dòng 126] if (_modules[6][c] != null) {
  3. [Dòng 144] if (_modules[row][col] != null) {
  4. [Dòng 365] while (buffer.getLengthInBits() % 8 != 0) {
  5. [Dòng 750] if (count != numChars) {
  6. [Dòng 751] throw count + ' != ' + numChars
  7. [Dòng 878] while (data != 0) {
  8. [Dòng 1733] if (test.length != 2
  9. [Dòng 1733] ( (test[0] << 8) | test[1]) != code) {
  10. [Dòng 1894] if (_length % 3 != 0) {
  11. [Dòng 2067] if ( (data >>> length) != 0) {
  12. [Dòng 2178] return typeof _map[key] != 'undefined'

================================================================================

📁 FILE 78: environment.prod.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/environments/environment.prod.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 79: environment.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/environments/environment.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 80: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/acc_bidv/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 81: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/acc_bidv/script.js
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 9] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 12] if (inName.value === "") return err("MISSING_FIELD"
  3. [Dòng 21] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 23] if (e !== null) {
  2. [Dòng 55] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 82: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/acc_oceanbank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 83: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/acc_oceanbank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 10] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 19] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 21] if (e !== null) {
  2. [Dòng 53] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 84: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/acc_pvcombank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 85: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/acc_pvcombank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 7] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 16] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 18] if (e !== null) {
  2. [Dòng 50] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 86: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/acc_shb/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 87: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/acc_shb/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 10] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 19] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 21] if (e !== null) {
  2. [Dòng 53] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 88: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/acc_tpbank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 89: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/acc_tpbank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 7] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 16] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 18] if (e !== null) {
  2. [Dòng 50] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 90: common.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/common.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 89] if (i % 2 === parity) d *= 2;
  2. [Dòng 93] return (sum % 10) === 0
  3. [Dòng 163] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 166] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 252] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 258] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 274] if (xhr.status === 200
  8. [Dòng 274] xhr.status === 201) {
  9. [Dòng 277] if (invoiceState === "unpaid"
  10. [Dòng 277] invoiceState === "not_paid") {
  11. [Dòng 282] if (paymentState === "authorization_required") {
  12. [Dòng 288] if (method === "REDIRECT") {
  13. [Dòng 291] } else if (method === "POST_REDIRECT") {
  14. [Dòng 330] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 141] this.oldValue !== this.value) {
  2. [Dòng 313] if (jLinks !== undefined
  3. [Dòng 313] jLinks !== null) {
  4. [Dòng 315] if (jMerchantReturn !== undefined
  5. [Dòng 315] jMerchantReturn !== null) {
  6. [Dòng 330] if (responseCode !== undefined
  7. [Dòng 330] responseCode !== null
  8. [Dòng 358] if (parentRes !== "{

================================================================================

📁 FILE 91: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/domestic_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 92: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/domestic_card/script.js
📊 Thống kê: 14 điều kiện duy nhất
   - === : 11 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 18] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 60] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 63] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 72] year === y
  5. [Dòng 74] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 106] if (inPhone.value === "") return err("MISSING_FIELD"
  7. [Dòng 136] if ("PAY" === operation) {
  8. [Dòng 525] } else if (value === "") {
  9. [Dòng 542] if (trPhone.style.display === "") {
  10. [Dòng 544] } else if (trName.style.display === "") {
  11. [Dòng 565] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 138] if (e !== null) {
  2. [Dòng 404] if (lang !== "vi") lang = "en";
  3. [Dòng 470] if (value !== "") {

================================================================================

📁 FILE 93: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/domestic_token/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 94: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/domestic_token/script.js
📊 Thống kê: 25 điều kiện duy nhất
   - === : 23 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (23 điều kiện):
  1. [Dòng 20] if ("PAY" === operation) {
  2. [Dòng 93] if (trName.style.display === "") {
  3. [Dòng 117] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  4. [Dòng 123] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  5. [Dòng 137] if (xhr.status === 200) {
  6. [Dòng 165] if (insType === "card") {
  7. [Dòng 166] if (insBrandId === "visa"
  8. [Dòng 166] insBrandId === "mastercard"
  9. [Dòng 166] insBrandId === "amex"
  10. [Dòng 166] insBrandId === "jcb"
  11. [Dòng 166] insBrandId === "cup") {
  12. [Dòng 170] } else if (insBrandId === "atm") {
  13. [Dòng 241] } else if (insType === "dongabank_account") {
  14. [Dòng 242] } else if (insType === "techcombank_account") {
  15. [Dòng 243] } else if (insType === "vib_account") {
  16. [Dòng 244] } else if (insType === "bidv_account") {
  17. [Dòng 245] } else if (insType === "tpbank_account") {
  18. [Dòng 246] } else if (insType === "shb_account") {
  19. [Dòng 247] } else if (insType === "shb_customer_id") {
  20. [Dòng 248] } else if (insType === "vpbank_account") {
  21. [Dòng 249] } else if (insType === "oceanbank_online_account") {
  22. [Dòng 250] } else if (insType === "oceanbank_mobile_account") {
  23. [Dòng 251] } else if (insType === "pvcombank_account") {

!== (2 điều kiện):
  1. [Dòng 54] if (lang !== "vi") lang = "en";
  2. [Dòng 273] if (parentRes !== "{

================================================================================

📁 FILE 95: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 96: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/international_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 97: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/international_card/script.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 13] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 23] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 26] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 38] year === y
  5. [Dòng 40] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 71] if ("PAY" === operation) {
  7. [Dòng 161] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 73] if (e !== null) {
  2. [Dòng 118] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 98: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/international_token/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 99: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/international_token/script.js
📊 Thống kê: 36 điều kiện duy nhất
   - === : 25 lần
   - == : 0 lần
   - !== : 11 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (25 điều kiện):
  1. [Dòng 32] year === y
  2. [Dòng 34] if (inCvv.value === "") {
  3. [Dòng 67] if ("PAY" === operation) {
  4. [Dòng 129] } else if (value === "") {
  5. [Dòng 190] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 196] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 209] if (xhr.status === 200) {
  8. [Dòng 245] if (insType === "card") {
  9. [Dòng 246] if (insBrandId === "visa"
  10. [Dòng 246] insBrandId === "mastercard"
  11. [Dòng 246] insBrandId === "amex"
  12. [Dòng 246] insBrandId === "jcb"
  13. [Dòng 246] insBrandId === "cup") {
  14. [Dòng 248] } else if (insBrandId === "atm") {
  15. [Dòng 319] } else if (insType === "dongabank_account") {
  16. [Dòng 320] } else if (insType === "techcombank_account") {
  17. [Dòng 321] } else if (insType === "vib_account") {
  18. [Dòng 322] } else if (insType === "bidv_account") {
  19. [Dòng 323] } else if (insType === "tpbank_account") {
  20. [Dòng 324] } else if (insType === "shb_account") {
  21. [Dòng 325] } else if (insType === "shb_customer_id") {
  22. [Dòng 326] } else if (insType === "vpbank_account") {
  23. [Dòng 327] } else if (insType === "oceanbank_online_account") {
  24. [Dòng 328] } else if (insType === "oceanbank_mobile_account") {
  25. [Dòng 329] } else if (insType === "pvcombank_account") {

!== (11 điều kiện):
  1. [Dòng 18] if (inMonth.value !== ""
  2. [Dòng 21] if (inYear.value !== ""
  3. [Dòng 23] var month = inMonth.value !== ""
  4. [Dòng 24] var year = parseInt("20" + (inYear.value !== ""
  5. [Dòng 35] if ("2D" !== order["vpc_VerType"]) return err("MISSING_FIELD"
  6. [Dòng 71] if (e !== null) {
  7. [Dòng 79] inMonth.value !== tokenInsMonth) instrument["month"] = inMonth.value;
  8. [Dòng 80] inYear.value !== tokenInsYear) instrument["year"] = inYear.value;
  9. [Dòng 81] if (inCvv.value !== "") instrument["cvv"] = inCvv.value;
  10. [Dòng 95] if (lang !== "vi") lang = "en";
  11. [Dòng 351] if (parentRes !== "{

================================================================================

📁 FILE 100: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/amway/script.js
📊 Thống kê: 54 điều kiện duy nhất
   - === : 41 lần
   - == : 1 lần
   - !== : 12 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (41 điều kiện):
  1. [Dòng 4] if ((cardno.length === 15
  2. [Dòng 4] cardno.length === 16
  3. [Dòng 4] cardno.length === 19) && isMode10(cardno) === true) {
  4. [Dòng 4] isMode10(cardno) === true) {
  5. [Dòng 23] if (i % 2 === parity) d *= 2;
  6. [Dòng 27] return (sum % 10) === 0
  7. [Dòng 48] if ("PAY" === operation) {
  8. [Dòng 69] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  9. [Dòng 75] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  10. [Dòng 91] if (xhr.status === 200
  11. [Dòng 91] xhr.status === 201) {
  12. [Dòng 94] if (invoiceState === "unpaid"
  13. [Dòng 94] invoiceState === "not_paid") {
  14. [Dòng 99] if (paymentState === "authorization_required") {
  15. [Dòng 105] if (method === "REDIRECT") {
  16. [Dòng 110] } else if (method === "POST_REDIRECT") {
  17. [Dòng 223] if (typeof queryParams[key] === "undefined") {
  18. [Dòng 226] } else if (typeof queryParams[key] === "string") {
  19. [Dòng 256] if (params["CardList"] === undefined
  20. [Dòng 256] params["CardList"] === null) return;
  21. [Dòng 302] if (xhr.status === 200) {
  22. [Dòng 312] if (insType === "card") {
  23. [Dòng 313] if (insBrandId === "visa"
  24. [Dòng 313] insBrandId === "mastercard"
  25. [Dòng 313] insBrandId === "amex"
  26. [Dòng 313] insBrandId === "jcb"
  27. [Dòng 313] insBrandId === "cup") {
  28. [Dòng 317] } else if (insBrandId === "atm") {
  29. [Dòng 318] if (insSwiftCode === "BFTVVNVX") { //Vietcombank
  30. [Dòng 322] } else if (insSwiftCode === "BIDVVNVX") { //BIDV
  31. [Dòng 329] } else if (insType === "dongabank_account") {
  32. [Dòng 331] } else if (insType === "techcombank_account") {
  33. [Dòng 333] } else if (insType === "vib_account") {
  34. [Dòng 335] } else if (insType === "bidv_account") {
  35. [Dòng 336] } else if (insType === "tpbank_account") {
  36. [Dòng 337] } else if (insType === "shb_account") {
  37. [Dòng 338] } else if (insType === "shb_customer_id") {
  38. [Dòng 339] } else if (insType === "vpbank_account") {
  39. [Dòng 340] } else if (insType === "oceanbank_online_account") {
  40. [Dòng 341] } else if (insType === "oceanbank_mobile_account") {
  41. [Dòng 342] } else if (insType === "pvcombank_account") {

== (1 điều kiện):
  1. [Dòng 270] if (cardList.length == 1) {//TOKEN, BIDV, SHB, OCEAN, PVCOM, TP Bank

!== (12 điều kiện):
  1. [Dòng 52] if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
  2. [Dòng 53] if (inNumber.style.display !== "none") instrument.number = inNumber.value;
  3. [Dòng 54] if (inDate.style.display !== "none") instrument.date = inDate.value;
  4. [Dòng 55] if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
  5. [Dòng 56] if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
  6. [Dòng 57] if (inName.style.display !== "none") instrument.name = inName.value;
  7. [Dòng 132] if (jLinks !== undefined
  8. [Dòng 132] jLinks !== null) {
  9. [Dòng 134] if (jMerchantReturn !== undefined
  10. [Dòng 134] jMerchantReturn !== null) {
  11. [Dòng 166] if (parentRes !== "{
  12. [Dòng 255] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 101: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 102: bidv.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Bidv/assets/js/bidv.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 233] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 239] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 255] if (xhr.status === 200
  8. [Dòng 255] xhr.status === 201) {
  9. [Dòng 258] if (invoiceState === "unpaid"
  10. [Dòng 258] invoiceState === "not_paid") {
  11. [Dòng 263] if (paymentState === "authorization_required") {
  12. [Dòng 269] if (method === "REDIRECT") {
  13. [Dòng 272] } else if (method === "POST_REDIRECT") {
  14. [Dòng 311] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 294] if (jLinks !== undefined
  3. [Dòng 294] jLinks !== null) {
  4. [Dòng 296] if (jMerchantReturn !== undefined
  5. [Dòng 296] jMerchantReturn !== null) {
  6. [Dòng 311] if (responseCode !== undefined
  7. [Dòng 311] responseCode !== null
  8. [Dòng 339] if (parentRes !== "{

================================================================================

📁 FILE 103: bidv2.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Bidv/bidv2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 76] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 88] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 78] if ( $('.circle_v1').css('display') == 'block'
  2. [Dòng 112] if ($('.circle_v2').css('display') == 'block'
  3. [Dòng 112] $('.circle_v3').css('display') == 'block' ) {
  4. [Dòng 302] $('.circle_v1').css('display') == 'block'

!== (3 điều kiện):
  1. [Dòng 90] if (e !== null) {
  2. [Dòng 273] if (lang !== "vi") lang = "en";
  3. [Dòng 305] if (value !== "") {

================================================================================

📁 FILE 104: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Bidv/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 105: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 106: ocean.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Ocean/assets/js/ocean.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 232] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 237] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 251] if (xhr.status === 200
  8. [Dòng 251] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 257] if (paymentState === "authorization_required") {
  12. [Dòng 261] if (method === "REDIRECT") {
  13. [Dòng 264] } else if (method === "POST_REDIRECT") {
  14. [Dòng 303] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 286] if (jLinks !== undefined
  3. [Dòng 286] jLinks !== null) {
  4. [Dòng 288] if (jMerchantReturn !== undefined
  5. [Dòng 288] jMerchantReturn !== null) {
  6. [Dòng 303] if (responseCode !== undefined
  7. [Dòng 303] responseCode !== null
  8. [Dòng 331] if (parentRes !== "{

================================================================================

📁 FILE 107: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Ocean/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 108: ocean2.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Ocean/ocean2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 72] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 92] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 74] document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM'
  2. [Dòng 116] if ( document.getElementsByClassName("select_online")[0].value == 'Easy Online Banking') {
  3. [Dòng 119] if ( document.getElementsByClassName("select_online")[0].value == 'Easy Mobile Banking') {
  4. [Dòng 304] if ( document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM') {

!== (3 điều kiện):
  1. [Dòng 94] if (e !== null) {
  2. [Dòng 276] if (lang !== "vi") lang = "en";
  3. [Dòng 306] if (value !== "") {

================================================================================

📁 FILE 109: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 110: pvbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Pv_Bank/assets/js/pvbank.js
📊 Thống kê: 23 điều kiện duy nhất
   - === : 14 lần
   - == : 1 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 239] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 261] if (xhr.status === 200
  8. [Dòng 261] xhr.status === 201) {
  9. [Dòng 264] if (invoiceState === "unpaid"
  10. [Dòng 264] invoiceState === "not_paid") {
  11. [Dòng 269] if (paymentState === "authorization_required") {
  12. [Dòng 275] if (method === "REDIRECT") {
  13. [Dòng 278] } else if (method === "POST_REDIRECT") {
  14. [Dòng 317] responseCode === "0") {

== (1 điều kiện):
  1. [Dòng 167] if ($('.circle_v1').css('display') == 'block') {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 300] if (jLinks !== undefined
  3. [Dòng 300] jLinks !== null) {
  4. [Dòng 302] if (jMerchantReturn !== undefined
  5. [Dòng 302] jMerchantReturn !== null) {
  6. [Dòng 317] if (responseCode !== undefined
  7. [Dòng 317] responseCode !== null
  8. [Dòng 345] if (parentRes !== "{

================================================================================

📁 FILE 111: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Pv_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 112: pvbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Pv_Bank/pvbank2.js
📊 Thống kê: 15 điều kiện duy nhất
   - === : 8 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 71] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 76] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 79] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 88] year === y
  5. [Dòng 90] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 100] if ("PAY" === operation) {
  7. [Dòng 347] } else if (value === "") {
  8. [Dòng 364] if (trName.style.display === "") {

== (4 điều kiện):
  1. [Dòng 73] if ($('.circle_v1').css('display') == 'block'
  2. [Dòng 75] $('.circle_v1').css('display') == 'block'
  3. [Dòng 124] if ( $('.circle_v1').css('display') == 'block') {
  4. [Dòng 129] else if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 102] if (e !== null) {
  2. [Dòng 286] if (lang !== "vi") lang = "en";
  3. [Dòng 316] if (value !== "") {

================================================================================

📁 FILE 113: Sea2.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/SeaBank/Sea2.js
📊 Thống kê: 11 điều kiện duy nhất
   - === : 8 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 23] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 31] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 34] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 43] year === y
  5. [Dòng 45] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 55] if ("PAY" === operation) {
  7. [Dòng 287] } else if (value === "") {
  8. [Dòng 304] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 57] if (e !== null) {
  2. [Dòng 233] if (lang !== "vi") lang = "en";
  3. [Dòng 263] if (value !== "") {

================================================================================

📁 FILE 114: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 115: Sea.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/SeaBank/assets/js/Sea.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 228] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 234] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 258] if (paymentState === "authorization_required") {
  12. [Dòng 264] if (method === "REDIRECT") {
  13. [Dòng 267] } else if (method === "POST_REDIRECT") {
  14. [Dòng 306] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 289] if (jLinks !== undefined
  3. [Dòng 289] jLinks !== null) {
  4. [Dòng 291] if (jMerchantReturn !== undefined
  5. [Dòng 291] jMerchantReturn !== null) {
  6. [Dòng 306] if (responseCode !== undefined
  7. [Dòng 306] responseCode !== null
  8. [Dòng 334] if (parentRes !== "{

================================================================================

📁 FILE 116: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/SeaBank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 117: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 118: shb.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Shb/assets/js/shb.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 234] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 240] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 256] if (xhr.status === 200
  8. [Dòng 256] xhr.status === 201) {
  9. [Dòng 259] if (invoiceState === "unpaid"
  10. [Dòng 259] invoiceState === "not_paid") {
  11. [Dòng 264] if (paymentState === "authorization_required") {
  12. [Dòng 270] if (method === "REDIRECT") {
  13. [Dòng 273] } else if (method === "POST_REDIRECT") {
  14. [Dòng 312] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 295] if (jLinks !== undefined
  3. [Dòng 295] jLinks !== null) {
  4. [Dòng 297] if (jMerchantReturn !== undefined
  5. [Dòng 297] jMerchantReturn !== null) {
  6. [Dòng 312] if (responseCode !== undefined
  7. [Dòng 312] responseCode !== null
  8. [Dòng 340] if (parentRes !== "{

================================================================================

📁 FILE 119: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Shb/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 120: shb2.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Shb/shb2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 73] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 85] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 74] if ($('.circle_v2').css('display') == 'block'
  2. [Dòng 110] if ($('.circle_v1').css('display') == 'block'
  3. [Dòng 114] if ( $('.circle_v3').css('display') == 'block' ) {
  4. [Dòng 299] if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 87] if (e !== null) {
  2. [Dòng 271] if (lang !== "vi") lang = "en";
  3. [Dòng 301] if (value !== "") {

================================================================================

📁 FILE 121: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 122: tpbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Tp_Bank/assets/js/tpbank.js
📊 Thống kê: 23 điều kiện duy nhất
   - === : 14 lần
   - == : 1 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 239] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 261] if (xhr.status === 200
  8. [Dòng 261] xhr.status === 201) {
  9. [Dòng 264] if (invoiceState === "unpaid"
  10. [Dòng 264] invoiceState === "not_paid") {
  11. [Dòng 269] if (paymentState === "authorization_required") {
  12. [Dòng 275] if (method === "REDIRECT") {
  13. [Dòng 278] } else if (method === "POST_REDIRECT") {
  14. [Dòng 317] responseCode === "0") {

== (1 điều kiện):
  1. [Dòng 167] if ($('.circle_v1').css('display') == 'block') {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 300] if (jLinks !== undefined
  3. [Dòng 300] jLinks !== null) {
  4. [Dòng 302] if (jMerchantReturn !== undefined
  5. [Dòng 302] jMerchantReturn !== null) {
  6. [Dòng 317] if (responseCode !== undefined
  7. [Dòng 317] responseCode !== null
  8. [Dòng 345] if (parentRes !== "{

================================================================================

📁 FILE 123: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Tp_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 124: tpbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Tp_Bank/tpbank2.js
📊 Thống kê: 15 điều kiện duy nhất
   - === : 8 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 71] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 78] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 81] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 90] year === y
  5. [Dòng 92] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 102] if ("PAY" === operation) {
  7. [Dòng 349] } else if (value === "") {
  8. [Dòng 366] if (trName.style.display === "") {

== (4 điều kiện):
  1. [Dòng 73] if ($('.circle_v1').css('display') == 'block'
  2. [Dòng 76] $('.circle_v1').css('display') == 'block'
  3. [Dòng 126] if ( $('.circle_v1').css('display') == 'block') {
  4. [Dòng 131] else if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 104] if (e !== null) {
  2. [Dòng 288] if (lang !== "vi") lang = "en";
  3. [Dòng 318] if (value !== "") {

================================================================================

📁 FILE 125: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 126: onepay.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Visa/assets/js/onepay.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 78] if (i % 2 === parity) d *= 2;
  2. [Dòng 82] return (sum % 10) === 0
  3. [Dòng 152] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 155] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 240] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 259] if (xhr.status === 200
  8. [Dòng 259] xhr.status === 201) {
  9. [Dòng 261] if (invoiceState === "unpaid"
  10. [Dòng 261] invoiceState === "not_paid") {
  11. [Dòng 265] if (paymentState === "authorization_required") {
  12. [Dòng 269] if (method === "REDIRECT") {
  13. [Dòng 272] } else if (method === "POST_REDIRECT") {
  14. [Dòng 311] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 130] this.oldValue !== this.value) {
  2. [Dòng 294] if (jLinks !== undefined
  3. [Dòng 294] jLinks !== null) {
  4. [Dòng 296] if (jMerchantReturn !== undefined
  5. [Dòng 296] jMerchantReturn !== null) {
  6. [Dòng 311] if (responseCode !== undefined
  7. [Dòng 311] responseCode !== null
  8. [Dòng 339] if (parentRes !== "{

================================================================================

📁 FILE 127: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Visa/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 128: index.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Visa/index.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 19] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 29] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 32] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 44] year === y
  5. [Dòng 46] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 77] if ("PAY" === operation) {
  7. [Dòng 168] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 79] if (e !== null) {
  2. [Dòng 125] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 129: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 130: vpbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Vp_Bank/assets/js/vpbank.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 230] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 236] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 252] if (xhr.status === 200
  8. [Dòng 252] xhr.status === 201) {
  9. [Dòng 255] if (invoiceState === "unpaid"
  10. [Dòng 255] invoiceState === "not_paid") {
  11. [Dòng 260] if (paymentState === "authorization_required") {
  12. [Dòng 266] if (method === "REDIRECT") {
  13. [Dòng 269] } else if (method === "POST_REDIRECT") {
  14. [Dòng 308] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 291] if (jLinks !== undefined
  3. [Dòng 291] jLinks !== null) {
  4. [Dòng 293] if (jMerchantReturn !== undefined
  5. [Dòng 293] jMerchantReturn !== null) {
  6. [Dòng 308] if (responseCode !== undefined
  7. [Dòng 308] responseCode !== null
  8. [Dòng 336] if (parentRes !== "{

================================================================================

📁 FILE 131: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Vp_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 132: vpbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/Vp_Bank/vpbank2.js
📊 Thống kê: 14 điều kiện duy nhất
   - === : 11 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 32] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 35] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 44] year === y
  5. [Dòng 46] if (inPhone.value === "") return err("MISSING_FIELD"
  6. [Dòng 49] if (inName.value === "") return err("MISSING_FIELD"
  7. [Dòng 59] if ("PAY" === operation) {
  8. [Dòng 292] } else if (value === "") {
  9. [Dòng 309] if (trPhone.style.display === "") {
  10. [Dòng 311] } else if (trName.style.display === "") {
  11. [Dòng 332] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 61] if (e !== null) {
  2. [Dòng 237] if (lang !== "vi") lang = "en";
  3. [Dòng 267] if (value !== "") {

================================================================================

📁 FILE 133: sha.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/assets/js/sha.js
📊 Thống kê: 49 điều kiện duy nhất
   - === : 38 lần
   - == : 0 lần
   - !== : 11 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (38 điều kiện):
  1. [Dòng 12] 1>t)throw Error("numRounds must a integer >= 1");if("SHA-1"===c)m=512,q=K,u=Z,f=160,r=function(a){return a.slice()};else if(0===c.lastIndexOf("SHA-",0))if(q=function(a,b){return L(a,b,c)
  2. [Dòng 12] c)},u=function(a,b,h,e){var k,f;if("SHA-224"===c
  3. [Dòng 12] "SHA-256"===c)k=(b+65>>>9<<4)+15,f=16;else if("SHA-384"===c
  4. [Dòng 12] "SHA-512"===c)k=(b+129>>>10<<
  5. [Dòng 13] c);if("SHA-224"===c)a=[e[0],e[1],e[2],e[3],e[4],e[5],e[6]];else if("SHA-256"===c)a=e;else if("SHA-384"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,e[4].b,e[5].a,e[5].b];else if("SHA-512"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,
  6. [Dòng 14] "SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)
  7. [Dòng 14] 0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
  8. [Dòng 14] return a},r=function(a){return a.slice()},"SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)||0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
  9. [Dòng 15] c)m=1152,f=224;else if("SHA3-256"===c)m=1088,f=256;else if("SHA3-384"===c)m=832,f=384;else if("SHA3-512"===c)m=576,f=512;else if("SHAKE128"===c)m=1344,f=-1,F=31,z=!0;else if("SHAKE256"===c)m=1088,f=-1,F=31,z=!0;else throw Error("Chosen SHA variant is not supported");u=function(a
  10. [Dòng 16] 0===64*l%e
  11. [Dòng 16] 5][l/5|0];g.push(a.b);if(32*g.length>=h)break;g.push(a.a);l+=1;0===64*l%e&&D(null,b)}return g}}else throw Error("Chosen SHA variant is not supported");d=M(a,g,x);l=A(c);this.setHMACKey=function(a,b,h){var k;if(!0===I)throw Error("HMAC key already set");if(!0===y)throw Error("Cannot set HMAC key after calling update");if(!0===z)throw Error("SHAKE is not supported for HMAC");g=(h
  12. [Dòng 17] f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
  13. [Dòng 17] )b.push(0);b[h]&=4294967040}for(a=0;a<=h;a+=1)v[a]=b[a]^909522486,w[a]=b[a]^1549556828;l=q(v,l);e=m;I=!0};this.update=function(a){var c,b,k,f=0,g=m>>>5;c=d(a,h,n);a=c.binLen;b=c.value;c=a>>>5;for(k=0;k<c;k+=g)f+m<=a&&(l=q(b.slice(k,k+g),l),f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
  14. [Dòng 18] for(g=1;g<t;g+=1)!0===z
  15. [Dòng 19] f);return k(m)};this.getHMAC=function(a,b){var k,g,d,p;if(!1===I)throw Error("Cannot call getHMAC without first setting HMAC key");d=N(b);switch(a){case "HEX":k=function(a){return O(a,f,x,d)};break;case "B64":k=function(a){return P(a,f,x,d)};break;case "BYTES":k=function(a){return Q(a,f,x)};break;case "ARRAYBUFFER":try{k=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}k=function(a){return R(a,f,x)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
  16. [Dòng 20] d=-1===b?3:0
  17. [Dòng 20] f=-1===b?3:0
  18. [Dòng 21] g=-1===b?3:0
  19. [Dòng 22] !0===c.hasOwnProperty("b64Pad")
  20. [Dòng 22] a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");
  21. [Dòng 23] u=-1===b?3:0
  22. [Dòng 23] l+=2){p=parseInt(a.substr(l,2),16);if(isNaN(p))throw Error("String of HEX type contains invalid characters");m=(l>>>1)+q;for(f=m>>>2;c.length<=f;)c.push(0);c[f]|=p<<8*(u+m%4*b)}return{value:c,binLen:4*g+d}};break;case "TEXT":c=function(c,h,d){var g,l,p=0,f,m,q,u,r,t;h=h||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(t=-1===
  23. [Dòng 24] m+=1){r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=l[m]<<8*(t+r%4*b);p+=1}else if("UTF16BE"===a
  24. [Dòng 24] "UTF16LE"===a)for(t=-1===b?2:0
  25. [Dòng 24] UTF16LE"===a
  26. [Dòng 24] 1===b
  27. [Dòng 25] !0===l
  28. [Dòng 25] !0===l&&(m=g&255,g=m<<8|g>>>8);r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=g<<8*(t+r%4*b);p+=2}return{value:h,binLen:8*p+d}};break;case "B64":c=function(a,c,d){var g=0,l,p,f,m,q,u,r,t;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");p=a.indexOf("=");a=a.replace(/\=/g
  29. [Dòng 25] t=-1===b?3:0
  30. [Dòng 26] q=-1===b?3:0
  31. [Dòng 27] m=-1===b?3:0
  32. [Dòng 32] c.b^a.b)}function A(c){var a=[],d;if("SHA-1"===c)a=[1732584193,4023233417,2562383102,271733878,3285377520];else if(0===c.lastIndexOf("SHA-",0))switch(a=
  33. [Dòng 34] new b(d[2],4271175723),new b(d[3],1595750129),new b(d[4],2917565137),new b(d[5],725511199),new b(d[6],4215389547),new b(d[7],327033209)];break;default:throw Error("Unknown SHA variant");}else if(0===c.lastIndexOf("SHA3-",0)
  34. [Dòng 34] 0===c.lastIndexOf("SHAKE",0))for(c=0
  35. [Dòng 36] a,k){var e,h,n,g,l,p,f,m,q,u,r,t,v,w,y,A,z,x,F,B,C,D,E=[],J;if("SHA-224"===k
  36. [Dòng 36] "SHA-256"===k)u=64,t=1,D=Number,v=G,w=la,y=H,A=ha,z=ja,x=da,F=fa,C=U,B=aa,J=d;else if("SHA-384"===k
  37. [Dòng 36] "SHA-512"===k)u=80,t=2,D=b,v=ma,w=na,y=oa,A=ia,z=ka,x=ea,F=ga,C=ca,B=ba,J=V;else throw Error("Unexpected error in SHA-2 implementation");k=a[0];e=a[1];h=a[2];n=a[3];g=a[4];l=a[5];p=a[6];f=a[7];for(r=0
  38. [Dòng 45] "function"===typeof define

!== (11 điều kiện):
  1. [Dòng 12] 'use strict';(function(Y){function C(c,a,b){var e=0,h=[],n=0,g,l,d,f,m,q,u,r,I=!1,v=[],w=[],t,y=!1,z=!1,x=-1;b=b||{};g=b.encoding||"UTF8";t=b.numRounds||1;if(t!==parseInt(t,10)
  2. [Dòng 18] 0!==f%32
  3. [Dòng 22] a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8
  4. [Dòng 23] }switch(c){case "HEX":c=function(a,c,d){var g=a.length,l,p,f,m,q,u;if(0!==g%2)throw Error("String of HEX type must be in byte increments");c=c||[0];d=d||0;q=d>>>3;u=-1===b?3:0;for(l=0
  5. [Dòng 24] 1!==b
  6. [Dòng 24] "UTF16LE"!==a
  7. [Dòng 25] "");if(-1!==p
  8. [Dòng 27] function(a,c,d){var g,l,p,f,m,q;c=c||[0];d=d||0;l=d>>>3;m=-1===b?3:0;q=new Uint8Array(a);for(g=0;g<a.byteLength;g+=1)f=g+l,p=f>>>2,c.length<=p&&c.push(0),c[p]|=q[g]<<8*(m+f%4*b);return{value:c,binLen:8*a.byteLength+d}};break;default:throw Error("format must be HEX, TEXT, B64, BYTES, or ARRAYBUFFER");}return c}function y(c,a){return c<<a|c>>>32-a}function S(c,a){return 32<a?(a-=32,new b(c.b<<a|c.a>>>32-a,c.a<<a|c.b>>>32-a)):0!==a?new b(c.a<<a|c.b>>>32-a,c.b<<a|c.a>>>32-a):c}function w(c,a){return c>>>
  9. [Dòng 37] a[7]);return a}function D(c,a){var d,e,h,n,g=[],l=[];if(null!==c)for(e=0
  10. [Dòng 45] define.amd?define(function(){return C}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=C),exports=C):Y.jsSHA=C})(this);
  11. [Dòng 45] return C}):"undefined"!==typeof exports?("undefined"!==typeof module

================================================================================

📁 FILE 134: sha256.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/assets/js/sha256.js
📊 Thống kê: 28 điều kiện duy nhất
   - === : 20 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (20 điều kiện):
  1. [Dòng 12] 1>u)throw Error("numRounds must a integer >= 1");if(0===c.lastIndexOf("SHA-",0))if(q=function(b,a){return A(b,a,c)
  2. [Dòng 12] c)},y=function(b,a,l,f){var g,e;if("SHA-224"===c
  3. [Dòng 12] "SHA-256"===c)g=(a+65>>>9<<4)+15,e=16;else throw Error("Unexpected error in SHA-2 implementation");for(
  4. [Dòng 13] c);if("SHA-224"===c)b=[f[0],f[1],f[2],f[3],f[4],f[5],f[6]];else if("SHA-256"===c)b=f;else throw Error("Unexpected error in SHA-2 implementation");return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
  5. [Dòng 13] "SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a
  6. [Dòng 13] return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
  7. [Dòng 14] if(!0===z)throw Error("Cannot set HMAC key after calling update");f=(g
  8. [Dòng 15] 5);g=a%h;z=!0};this.getHash=function(a,f){var d,h,k,q;if(!0===m)throw Error("Cannot call getHash after setting HMAC key");k=C(f);switch(a){case "HEX":d=function(a){return D(a,e,k)};break;case "B64":d=function(a){return E(a,e,k)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{h=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("format must be HEX, B64, BYTES, or ARRAYBUFFER");
  9. [Dòng 16] x(c));return d(q)};this.getHMAC=function(a,f){var d,k,t,u;if(!1===m)throw Error("Cannot call getHMAC without first setting HMAC key");t=C(f);switch(a){case "HEX":d=function(a){return D(a,e,t)};break;case "B64":d=function(a){return E(a,e,t)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{d=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
  10. [Dòng 18] !0===c.hasOwnProperty("b64Pad")
  11. [Dòng 20] h=(d>>>1)+q;for(e=h>>>2;b.length<=e;)b.push(0);b[e]|=k<<8*(3+h%4*-1)}return{value:b,binLen:4*f+c}};break;case "TEXT":d=function(c,b,d){var f,n,k=0,e,h,q,m,p,r;b=b||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(r=3
  12. [Dòng 21] q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=n[h]<<8*(r+p%4*-1);k+=1}else if("UTF16BE"===a
  13. [Dòng 21] "UTF16LE"===a)for(r=2
  14. [Dòng 21] UTF16LE"===a
  15. [Dòng 21] !0===n
  16. [Dòng 21] e+=1){f=c.charCodeAt(e);!0===n&&(h=f&255,f=h<<8|f>>>8);p=k+q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=f<<8*(r+p%4*-1);k+=2}return{value:b,binLen:8*k+d}};break;case "B64":d=function(a,b,c){var f=0,d,k,e,h,q,m,p;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");k=a.indexOf("=");a=a.replace(/\=/g
  17. [Dòng 25] 16)&65535)<<16|b&65535}function R(c,a,d,l,b){var g=(c&65535)+(a&65535)+(d&65535)+(l&65535)+(b&65535);return((c>>>16)+(a>>>16)+(d>>>16)+(l>>>16)+(b>>>16)+(g>>>16)&65535)<<16|g&65535}function x(c){var a=[],d;if(0===c.lastIndexOf("SHA-",0))switch(a=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428]
  18. [Dòng 26] new m,new m,new m,new m,new m,new m];break;case "SHA-512":a=[new m,new m,new m,new m,new m,new m,new m,new m];break;default:throw Error("Unknown SHA variant");}else throw Error("No SHA variants supported");return a}function A(c,a,d){var l,b,g,f,n,k,e,h,m,r,p,w,t,x,u,z,A,B,C,D,E,F,v=[],G;if("SHA-224"===d
  19. [Dòng 26] "SHA-256"===d)r=64,w=1,F=Number,t=P,x=Q,u=R,z=N,A=O,B=L,C=M,E=K,D=J,G=H;else throw Error("Unexpected error in SHA-2 implementation");d=a[0];l=a[1];b=a[2];g=a[3];f=a[4];n=a[5];k=a[6];e=a[7];for(p=
  20. [Dòng 29] "function"===typeof define

!== (8 điều kiện):
  1. [Dòng 12] 'use strict';(function(I){function w(c,a,d){var l=0,b=[],g=0,f,n,k,e,h,q,y,p,m=!1,t=[],r=[],u,z=!1;d=d||{};f=d.encoding||"UTF8";u=d.numRounds||1;if(u!==parseInt(u,10)
  2. [Dòng 18] l+=1)g[l]=c[l>>>2]>>>8*(3+l%4*-1)&255;return b}function C(c){var a={outputUpper:!1,b64Pad:"=",shakeLen:-1};c=c||{};a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");
  3. [Dòng 19] if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0;d<f;d+=2){k=parseInt(a.substr(d,2),16);if(isNaN(k))throw Error("String of HEX type contains invalid characters");
  4. [Dòng 19] if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0
  5. [Dòng 21] "UTF16LE"!==a
  6. [Dòng 22] "");if(-1!==k
  7. [Dòng 29] define.amd?define(function(){return w}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=w),exports=w):I.jsSHA=w})(this);
  8. [Dòng 29] return w}):"undefined"!==typeof exports?("undefined"!==typeof module

================================================================================

📁 FILE 135: slick.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/assets/libraries/slick/slick.js
📊 Thống kê: 182 điều kiện duy nhất
   - === : 126 lần
   - == : 5 lần
   - !== : 47 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (126 điều kiện):
  1. [Dòng 20] if (typeof define === 'function'
  2. [Dòng 206] if (typeof(index) === 'boolean') {
  3. [Dòng 215] if (typeof(index) === 'number') {
  4. [Dòng 216] if (index === 0
  5. [Dòng 216] _.$slides.length === 0) {
  6. [Dòng 224] if (addBefore === true) {
  7. [Dòng 249] if (_.options.slidesToShow === 1
  8. [Dòng 249] _.options.adaptiveHeight === true
  9. [Dòng 249] _.options.vertical === false) {
  10. [Dòng 264] if (_.options.rtl === true
  11. [Dòng 267] if (_.transformsEnabled === false) {
  12. [Dòng 268] if (_.options.vertical === false) {
  13. [Dòng 280] if (_.cssTransitions === false) {
  14. [Dòng 281] if (_.options.rtl === true) {
  15. [Dòng 355] typeof asNavFor === 'object' ) {
  16. [Dòng 371] if (_.options.fade === false) {
  17. [Dòng 414] if ( _.options.infinite === false ) {
  18. [Dòng 416] if ( _.direction === 1
  19. [Dòng 416] ( _.currentSlide + 1 ) === ( _.slideCount - 1 )) {
  20. [Dòng 420] else if ( _.direction === 0 ) {
  21. [Dòng 424] if ( _.currentSlide - 1 === 0 ) {
  22. [Dòng 442] if (_.options.arrows === true ) {
  23. [Dòng 487] if (_.options.dots === true
  24. [Dòng 524] _.$slideTrack = (_.slideCount === 0) ?
  25. [Dòng 532] if (_.options.centerMode === true
  26. [Dòng 532] _.options.swipeToSlide === true) {
  27. [Dòng 547] _.setSlideClasses(typeof _.currentSlide === 'number' ? _.currentSlide : 0);
  28. [Dòng 549] if (_.options.draggable === true) {
  29. [Dòng 602] if (_.respondTo === 'window') {
  30. [Dòng 604] } else if (_.respondTo === 'slider') {
  31. [Dòng 606] } else if (_.respondTo === 'min') {
  32. [Dòng 618] if (_.originalSettings.mobileFirst === false) {
  33. [Dòng 635] if (_.breakpointSettings[targetBreakpoint] === 'unslick') {
  34. [Dòng 641] if (initial === true) {
  35. [Dòng 705] slideOffset = indexOffset === 0 ? _.options.slidesToScroll : _.options.slidesToShow - indexOffset
  36. [Dòng 712] slideOffset = indexOffset === 0 ? _.options.slidesToScroll : indexOffset
  37. [Dòng 719] var index = event.data.index === 0 ? 0 :
  38. [Dòng 765] if (_.options.accessibility === true) {
  39. [Dòng 772] if (_.options.arrows === true
  40. [Dòng 797] if (_.options.focusOnSelect === true) {
  41. [Dòng 836] if (_.shouldClick === false) {
  42. [Dòng 1051] if (_.options.infinite === true) {
  43. [Dòng 1061] } else if (_.options.centerMode === true) {
  44. [Dòng 1094] if (_.options.vertical === true
  45. [Dòng 1094] _.options.centerMode === true) {
  46. [Dòng 1095] if (_.options.slidesToShow === 2) {
  47. [Dòng 1097] } else if (_.options.slidesToShow === 1) {
  48. [Dòng 1128] } else if (_.options.centerMode === true
  49. [Dòng 1128] _.options.infinite === true) {
  50. [Dòng 1141] if (_.options.variableWidth === true) {
  51. [Dòng 1143] _.options.infinite === false) {
  52. [Dòng 1159] if (_.options.centerMode === true) {
  53. [Dòng 1200] if (_.options.infinite === false) {
  54. [Dòng 1229] centerOffset = _.options.centerMode === true ? _.slideWidth * Math.floor(_.options.slidesToShow / 2) : 0
  55. [Dòng 1231] if (_.options.swipeToSlide === true) {
  56. [Dòng 1406] _.options.pauseOnDotsHover === true
  57. [Dòng 1498] if (event.keyCode === 37
  58. [Dòng 1498] _.options.accessibility === true) {
  59. [Dòng 1501] message: _.options.rtl === true ? 'next' :
  60. [Dòng 1504] } else if (event.keyCode === 39
  61. [Dòng 1507] message: _.options.rtl === true ? 'previous' : 'next'
  62. [Dòng 1585] if (_.options.fade === true) {
  63. [Dòng 1593] if (_.options.lazyLoad === 'anticipated') {
  64. [Dòng 1616] } else if (_.currentSlide === 0) {
  65. [Dòng 1637] if (_.options.lazyLoad === 'progressive') {
  66. [Dòng 1773] if ( _.options.adaptiveHeight === true ) {
  67. [Dòng 1864] if ( $.type(responsiveSettings) === 'array'
  68. [Dòng 1878] _.breakpoints[l] === currentBreakpoint ) {
  69. [Dòng 1969] index = removeBefore === true ? 0 : _.slideCount - 1
  70. [Dòng 1971] index = removeBefore === true ? --index : index
  71. [Dòng 1980] if (removeAll === true) {
  72. [Dòng 2050] if (_.options.vertical === false
  73. [Dòng 2050] _.options.variableWidth === false) {
  74. [Dòng 2054] } else if (_.options.variableWidth === true) {
  75. [Dòng 2062] if (_.options.variableWidth === false) _.$slideTrack.children('.slick-slide').width(_.slideWidth - offset);
  76. [Dòng 2128] if( $.type( arguments[0] ) === 'object' ) {
  77. [Dòng 2134] } else if ( $.type( arguments[0] ) === 'string' ) {
  78. [Dòng 2140] if ( arguments[0] === 'responsive'
  79. [Dòng 2140] $.type( arguments[1] ) === 'array' ) {
  80. [Dòng 2152] if ( type === 'single' ) {
  81. [Dòng 2157] } else if ( type === 'multiple' ) {
  82. [Dòng 2166] } else if ( type === 'responsive' ) {
  83. [Dòng 2181] if( _.options.responsive[l].breakpoint === value[item].breakpoint ) {
  84. [Dòng 2231] _.positionProp = _.options.vertical === true ? 'top' : 'left'
  85. [Dòng 2233] if (_.positionProp === 'top') {
  86. [Dòng 2242] if (_.options.useCSS === true) {
  87. [Dòng 2248] if ( typeof _.options.zIndex === 'number' ) {
  88. [Dòng 2261] if (bodyStyle.perspectiveProperty === undefined
  89. [Dòng 2261] bodyStyle.webkitPerspective === undefined) _.animType = false;
  90. [Dòng 2267] bodyStyle.MozPerspective === undefined) _.animType = false;
  91. [Dòng 2279] if (bodyStyle.msTransform === undefined) _.animType = false;
  92. [Dòng 2306] var evenCoef = _.options.slidesToShow % 2 === 0 ? 1 : 0
  93. [Dòng 2328] if (index === 0) {
  94. [Dòng 2334] } else if (index === _.slideCount - 1) {
  95. [Dòng 2366] indexOffset = _.options.infinite === true ? _.options.slidesToShow + index : index
  96. [Dòng 2388] if (_.options.lazyLoad === 'ondemand'
  97. [Dòng 2388] _.options.lazyLoad === 'anticipated') {
  98. [Dòng 2402] if (_.options.infinite === true
  99. [Dòng 2402] _.options.fade === false) {
  100. [Dòng 2479] if (_.animating === true
  101. [Dòng 2479] _.options.waitForAnimate === true) {
  102. [Dòng 2483] if (_.options.fade === true
  103. [Dòng 2483] _.currentSlide === index) {
  104. [Dòng 2487] if (sync === false) {
  105. [Dòng 2495] _.currentLeft = _.swipeLeft === null ? slideLeft : _.swipeLeft
  106. [Dòng 2497] if (_.options.infinite === false
  107. [Dòng 2497] _.options.centerMode === false
  108. [Dòng 2509] } else if (_.options.infinite === false
  109. [Dòng 2509] _.options.centerMode === true
  110. [Dòng 2627] return (_.options.rtl === false ? 'left' : 'right');
  111. [Dòng 2633] return (_.options.rtl === false ? 'right' : 'left');
  112. [Dòng 2635] if (_.options.verticalSwiping === true) {
  113. [Dòng 2664] if ( _.touchObject.curX === undefined ) {
  114. [Dòng 2668] if ( _.touchObject.edgeHit === true ) {
  115. [Dòng 2732] if ((_.options.swipe === false) || ('ontouchend' in document
  116. [Dòng 2732] _.options.swipe === false)) {
  117. [Dòng 2734] } else if (_.options.draggable === false
  118. [Dòng 2806] positionOffset = (_.options.rtl === false ? 1 : -1) * (_.touchObject.curX > _.touchObject.startX ? 1 : -1);
  119. [Dòng 2817] if ((_.currentSlide === 0
  120. [Dòng 2817] swipeDirection === 'right') || (_.currentSlide >= _.getDotCount()
  121. [Dòng 2817] swipeDirection === 'left')) {
  122. [Dòng 2832] _.options.touchMove === false) {
  123. [Dòng 2836] if (_.animating === true) {
  124. [Dòng 2926] if ( _.options.arrows === true
  125. [Dòng 2933] if (_.currentSlide === 0) {
  126. [Dòng 2938] _.options.centerMode === false) {

== (5 điều kiện):
  1. [Dòng 2007] x = _.positionProp == 'left' ? Math.ceil(position) + 'px' : '0px'
  2. [Dòng 2008] y = _.positionProp == 'top' ? Math.ceil(position) + 'px' : '0px'
  3. [Dòng 2368] if (_.options.slidesToShow == _.options.slidesToScroll
  4. [Dòng 3002] if (typeof opt == 'object'
  5. [Dòng 3002] typeof opt == 'undefined')

!== (47 điều kiện):
  1. [Dòng 22] } else if (typeof exports !== 'undefined') {
  2. [Dòng 155] if (typeof document.mozHidden !== 'undefined') {
  3. [Dòng 158] } else if (typeof document.webkitHidden !== 'undefined') {
  4. [Dòng 342] asNavFor !== null ) {
  5. [Dòng 355] if ( asNavFor !== null
  6. [Dòng 460] if (_.options.infinite !== true) {
  7. [Dòng 612] _.options.responsive !== null) {
  8. [Dòng 630] if (targetBreakpoint !== null) {
  9. [Dòng 631] if (_.activeBreakpoint !== null) {
  10. [Dòng 632] if (targetBreakpoint !== _.activeBreakpoint
  11. [Dòng 676] triggerBreakpoint !== false ) {
  12. [Dòng 699] unevenOffset = (_.slideCount % _.options.slidesToScroll !== 0);
  13. [Dòng 758] _.$dots !== null) {
  14. [Dòng 997] if (filter !== null) {
  15. [Dòng 1103] if (_.slideCount % _.options.slidesToScroll !== 0) {
  16. [Dòng 1314] if (_.$dots !== null) {
  17. [Dòng 1324] if (slideControlIndex !== -1) {
  18. [Dòng 1910] _.currentSlide !== 0) {
  19. [Dòng 1953] if ($(window).width() !== _.windowWidth) {
  20. [Dòng 2144] } else if ( typeof arguments[1] !== 'undefined' ) {
  21. [Dòng 2170] if( $.type( _.options.responsive ) !== 'array' ) {
  22. [Dòng 2239] if (bodyStyle.WebkitTransition !== undefined
  23. [Dòng 2240] bodyStyle.MozTransition !== undefined
  24. [Dòng 2241] bodyStyle.msTransition !== undefined) {
  25. [Dòng 2257] if (bodyStyle.OTransform !== undefined) {
  26. [Dòng 2263] if (bodyStyle.MozTransform !== undefined) {
  27. [Dòng 2269] if (bodyStyle.webkitTransform !== undefined) {
  28. [Dòng 2275] if (bodyStyle.msTransform !== undefined) {
  29. [Dòng 2281] if (bodyStyle.transform !== undefined
  30. [Dòng 2281] _.animType !== false) {
  31. [Dòng 2286] _.transformsEnabled = _.options.useTransform && (_.animType !== null
  32. [Dòng 2286] _.animType !== false);
  33. [Dòng 2500] if (dontAnimate !== true
  34. [Dòng 2567] if (dontAnimate !== true) {
  35. [Dòng 2717] if ( _.touchObject.startX !== _.touchObject.curX ) {
  36. [Dòng 2734] event.type.indexOf('mouse') !== -1) {
  37. [Dòng 2738] event.originalEvent.touches !== undefined ?
  38. [Dòng 2773] touches = event.originalEvent !== undefined ? event.originalEvent.touches : null
  39. [Dòng 2775] touches.length !== 1) {
  40. [Dòng 2781] _.touchObject.curX = touches !== undefined ? touches[0].pageX : event.clientX
  41. [Dòng 2782] _.touchObject.curY = touches !== undefined ? touches[0].pageY : event.clientY
  42. [Dòng 2801] if (event.originalEvent !== undefined
  43. [Dòng 2852] if (_.touchObject.fingerCount !== 1
  44. [Dòng 2857] event.originalEvent.touches !== undefined) {
  45. [Dòng 2861] _.touchObject.startX = _.touchObject.curX = touches !== undefined ? touches.pageX : event.clientX
  46. [Dòng 2862] _.touchObject.startY = _.touchObject.curY = touches !== undefined ? touches.pageY : event.clientY
  47. [Dòng 2872] if (_.$slidesCache !== null) {

!= (4 điều kiện):
  1. [Dòng 805] $('[draggable!=true]', _.$slideTrack).off('dragstart', _.preventDefault);
  2. [Dòng 1467] $('[draggable!=true]', _.$slideTrack).on('dragstart', _.preventDefault);
  3. [Dòng 2707] if( direction != 'vertical' ) {
  4. [Dòng 3006] if (typeof ret != 'undefined') return ret;

================================================================================

📁 FILE 136: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 137: atm_b1.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_1/assets/js/atm_b1.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 228] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 234] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 258] if (paymentState === "authorization_required") {
  12. [Dòng 264] if (method === "REDIRECT") {
  13. [Dòng 267] } else if (method === "POST_REDIRECT") {
  14. [Dòng 306] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 289] if (jLinks !== undefined
  3. [Dòng 289] jLinks !== null) {
  4. [Dòng 291] if (jMerchantReturn !== undefined
  5. [Dòng 291] jMerchantReturn !== null) {
  6. [Dòng 306] if (responseCode !== undefined
  7. [Dòng 306] responseCode !== null
  8. [Dòng 334] if (parentRes !== "{

================================================================================

📁 FILE 138: atm_b1_2.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_1/atm_b1_2.js
📊 Thống kê: 11 điều kiện duy nhất
   - === : 8 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 52] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 55] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 64] year === y
  5. [Dòng 66] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 75] if ("PAY" === operation) {
  7. [Dòng 319] } else if (value === "") {
  8. [Dòng 336] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 77] if (e !== null) {
  2. [Dòng 253] if (lang !== "vi") lang = "en";
  3. [Dòng 283] if (value !== "") {

================================================================================

📁 FILE 139: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_1/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 140: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 141: atm_b2.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_2/assets/js/atm_b2.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 231] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 236] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 252] if (invoiceState === "unpaid"
  10. [Dòng 252] invoiceState === "not_paid") {
  11. [Dòng 256] if (paymentState === "authorization_required") {
  12. [Dòng 260] if (method === "REDIRECT") {
  13. [Dòng 263] } else if (method === "POST_REDIRECT") {
  14. [Dòng 302] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 285] if (jLinks !== undefined
  3. [Dòng 285] jLinks !== null) {
  4. [Dòng 287] if (jMerchantReturn !== undefined
  5. [Dòng 287] jMerchantReturn !== null) {
  6. [Dòng 302] if (responseCode !== undefined
  7. [Dòng 302] responseCode !== null
  8. [Dòng 330] if (parentRes !== "{

================================================================================

📁 FILE 142: atm_b2_2.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_2/atm_b2_2.js
📊 Thống kê: 6 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 41] if (inName.value === "") return err("MISSING_FIELD"
  3. [Dòng 51] if ("PAY" === operation) {

!== (3 điều kiện):
  1. [Dòng 53] if (e !== null) {
  2. [Dòng 229] if (lang !== "vi") lang = "en";
  3. [Dòng 259] if (value !== "") {

================================================================================

📁 FILE 143: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/atm_bank_2/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 144: common.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/common.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 42] if (i % 2 === parity) d *= 2;
  2. [Dòng 46] return (sum % 10) === 0
  3. [Dòng 116] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 119] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 205] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 211] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 227] if (xhr.status === 200
  8. [Dòng 227] xhr.status === 201) {
  9. [Dòng 230] if (invoiceState === "unpaid"
  10. [Dòng 230] invoiceState === "not_paid") {
  11. [Dòng 235] if (paymentState === "authorization_required") {
  12. [Dòng 241] if (method === "REDIRECT") {
  13. [Dòng 244] } else if (method === "POST_REDIRECT") {
  14. [Dòng 283] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 94] this.oldValue !== this.value) {
  2. [Dòng 266] if (jLinks !== undefined
  3. [Dòng 266] jLinks !== null) {
  4. [Dòng 268] if (jMerchantReturn !== undefined
  5. [Dòng 268] jMerchantReturn !== null) {
  6. [Dòng 283] if (responseCode !== undefined
  7. [Dòng 283] responseCode !== null
  8. [Dòng 311] if (parentRes !== "{

================================================================================

📁 FILE 145: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 146: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/international_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 147: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/begodi/international_card/script.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 12] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 22] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 25] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 37] year === y
  5. [Dòng 39] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 70] if ("PAY" === operation) {
  7. [Dòng 144] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 72] if (e !== null) {
  2. [Dòng 117] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 148: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/iframe/index.html
📊 Thống kê: 35 điều kiện duy nhất
   - === : 21 lần
   - == : 0 lần
   - !== : 14 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (21 điều kiện):
  1. [Dòng 10] if ((cardno.length === 15
  2. [Dòng 10] cardno.length === 16
  3. [Dòng 10] cardno.length === 19) && isMode10(cardno) === true) {
  4. [Dòng 10] isMode10(cardno) === true) {
  5. [Dòng 29] if (i % 2 === parity) d *= 2;
  6. [Dòng 33] return (sum % 10) === 0
  7. [Dòng 54] if ("PAY" === operation) {
  8. [Dòng 75] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  9. [Dòng 81] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  10. [Dòng 97] if (xhr.status === 200
  11. [Dòng 97] xhr.status === 201) {
  12. [Dòng 100] if (invoiceState === "unpaid"
  13. [Dòng 100] invoiceState === "not_paid") {
  14. [Dòng 105] if (paymentState === "authorization_required") {
  15. [Dòng 111] if (method === "REDIRECT") {
  16. [Dòng 116] } else if (method === "POST_REDIRECT") {
  17. [Dòng 183] //Customizable =================================================================================================
  18. [Dòng 229] if (typeof queryParams[key] === "undefined") {
  19. [Dòng 232] } else if (typeof queryParams[key] === "string") {
  20. [Dòng 246] if (params["CardList"] === undefined
  21. [Dòng 246] params["CardList"] === null) return;

!== (14 điều kiện):
  1. [Dòng 40] //if (event.origin !== "http://example.com:8080") return;
  2. [Dòng 58] if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
  3. [Dòng 59] if (inNumber.style.display !== "none") instrument.number = inNumber.value;
  4. [Dòng 60] if (inDate.style.display !== "none") instrument.date = inDate.value;
  5. [Dòng 61] if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
  6. [Dòng 62] if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
  7. [Dòng 63] if (inName.style.display !== "none") instrument.name = inName.value;
  8. [Dòng 78] /*if (contentType !== my_expected_type) {
  9. [Dòng 138] if (jLinks !== undefined
  10. [Dòng 138] jLinks !== null) {
  11. [Dòng 140] if (jMerchantReturn !== undefined
  12. [Dòng 140] jMerchantReturn !== null) {
  13. [Dòng 172] if (parentRes !== "{
  14. [Dòng 245] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 149: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 150: karma.conf.js
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/karma.conf.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 151: main.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/main.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 152: polyfills.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/polyfills.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 153: test.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general-fee-outside/src/test.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📋 TÓM TẮT TẤT CẢ ĐIỀU KIỆN DUY NHẤT:
====================================================================================================

=== (469 điều kiện duy nhất):
------------------------------------------------------------
1. return lang === this._translate.currentLang
2. params.timeout === 'true') {
3. this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
4. _re.body.state === 'unpaid');
5. if (this.errorCode === 'overtime'
6. this.errorCode === '253') {
7. if (YY % 400 === 0
8. YY % 4 === 0)) {
9. if (YYYY % 400 === 0
10. YYYY % 4 === 0)) {
11. <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
12. [class.red_border_no_background]="c_expdate && (valueDate.length === 0
13. valueDate.trim().length === 0)"
14. if (isIE[0] === 'MSIE'
15. +isIE[1] === 10) {
16. if ((_val.value.substr(-1) === ' '
17. _val.value.length === 24) {
18. if (this.cardTypeBank === 'bank_card_number') {
19. } else if (this.cardTypeBank === 'bank_account_number') {
20. } else if (this.cardTypeBank === 'bank_username') {
21. } else if (this.cardTypeBank === 'bank_customer_code') {
22. this.cardTypeBank === 'bank_card_number'
23. if (this.cardTypeOcean === 'IB') {
24. } else if (this.cardTypeOcean === 'MB') {
25. if (_val.value.substr(0, 2) === '84') {
26. } else if (this.cardTypeOcean === 'ATM') {
27. if (this.cardTypeBank === 'bank_account_number') {
28. this.cardTypeBank === 'bank_card_number') {
29. if (this.cardTypeBank === 'bank_card_number'
30. if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
31. if (event.keyCode === 8
32. event.key === "Backspace"
33. if (v.length === 2
34. this.flag.length === 3
35. this.flag.charAt(this.flag.length - 1) === '/') {
36. if (v.length === 1) {
37. } else if (v.length === 2) {
38. v.length === 2) {
39. if (len === 2) {
40. if ((this.cardTypeBank === 'bank_account_number'
41. this.cardTypeBank === 'bank_username'
42. this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
43. this.cardTypeOcean === 'ATM')
44. || (this.cardTypeOcean === 'IB'
45. if (valIn === this._translate.instant('bank_card_number')) {
46. } else if (valIn === this._translate.instant('bank_account_number')) {
47. } else if (valIn === this._translate.instant('bank_username')) {
48. } else if (valIn === this._translate.instant('bank_customer_code')) {
49. if (_val.value === ''
50. _val.value === null
51. _val.value === undefined) {
52. if (_val.value && (this.cardTypeBank === 'bank_card_number'
53. if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
54. this.cardTypeOcean === 'MB') {
55. this.cardTypeOcean === 'IB'
56. if ((this.cardTypeBank === 'bank_card_number'
57. if (this.cardName === undefined
58. this.cardName === '') {
59. if (this.valueDate === undefined
60. this.valueDate === '') {
61. if (this.timeLeft === 0) {
62. c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
63. _inExpDate.trim().length === 0)"
64. if (approval.method === 'REDIRECT') {
65. } else if (approval.method === 'POST_REDIRECT') {
66. filteredData.length === 0"
67. if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
68. filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
69. if (valOut === 'auth') {
70. if (this._b === '1'
71. this._b === '20'
72. this._b === '64') {
73. if (this._b === '36'
74. this._b === '18'
75. this._b === '19'
76. if (this._b === '19'
77. this._b === '16'
78. this._b === '25'
79. this._b === '33'
80. this._b === '39'
81. this._b === '9'
82. this._b === '11'
83. this._b === '17'
84. this._b === '36'
85. this._b === '44'
86. this._b === '12'
87. this._b === '64'
88. if (this._b === '20'
89. if (this._b === '12'
90. this._b === '18') {
91. if (target.tagName === 'A'
92. if (_formCard.country === 'default') {
93. if ((v.substr(-1) === ' '
94. this._i_country_code === 'US') {
95. const insertIndex = this._i_country_code === 'US' ? 5 : 3
96. if (temp[i] === '-'
97. temp[i] === ' ') {
98. insertIndex === 3 ? ' ' : itemRemoved)
99. this.c_country = _val.value === 'default'
100. type === 7"
101. [ngStyle]="{'border-color': type === 7 ? this.themeColor.border_color : border_color}"
102. type === 9"
103. [ngStyle]="{'border-color': type === 9 ? this.themeColor.border_color : border_color}"
104. type === 10"
105. [ngStyle]="{'border-color': type === 10 ? this.themeColor.border_color : border_color}"
106. type === 8"
107. [ngStyle]="{'border-color': type === 8 ? this.themeColor.border_color : border_color}"
108. d_vrbank===true"
109. this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number === 1
110. if (this._res.state === 'unpaid'
111. this._res.state === 'not_paid') {
112. if ('op' === auth
113. } else if ('bank' === auth
114. return id === 'amex' ? '1234' : '123'
115. if (this.timeLeftPaypal === 0) {
116. filteredData.length === 0
117. filteredDataOther.length === 0"
118. this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
119. this.filteredDataOther = this.appList.filter(item => item.type === 'other');
120. item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
121. filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
122. if (item.type === 'mobile_banking') {
123. style="{{item.brand_id === 'visa' ? 'height: 14.42px
124. if (event.keyCode === 13) {
125. && ((item.brand_id === 'amex'
126. return this._i_token_otp != null && !isNaN(+this._i_token_otp) && ((id === 'amex'
127. const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
128. item.method === method) : null;
129. if (M[1] === 'Chrome') {
130. if (param === key) {
131. pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
132. target === 0
133. if (typeof define === 'function'
134. } else if (typeof exports === 'object') {
135. if (number === "") return err("MISSING_FIELD"
136. if (inName.value === "") return err("MISSING_FIELD"
137. if ("PAY" === operation) {
138. if (i % 2 === parity) d *= 2;
139. return (sum % 10) === 0
140. if (typeof queryParams[key] === "undefined") {
141. } else if (typeof queryParams[key] === "string") {
142. if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
143. } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
144. if (xhr.status === 200
145. xhr.status === 201) {
146. if (invoiceState === "unpaid"
147. invoiceState === "not_paid") {
148. if (paymentState === "authorization_required") {
149. if (method === "REDIRECT") {
150. } else if (method === "POST_REDIRECT") {
151. responseCode === "0") {
152. if (inMonth.value === "") return err("MISSING_FIELD"
153. if (inYear.value === "") return err("MISSING_FIELD"
154. year === y
155. if (inPhone.value === "") return err("MISSING_FIELD"
156. } else if (value === "") {
157. if (trPhone.style.display === "") {
158. } else if (trName.style.display === "") {
159. if (trName.style.display === "") {
160. if (xhr.status === 200) {
161. if (insType === "card") {
162. if (insBrandId === "visa"
163. insBrandId === "mastercard"
164. insBrandId === "amex"
165. insBrandId === "jcb"
166. insBrandId === "cup") {
167. } else if (insBrandId === "atm") {
168. } else if (insType === "dongabank_account") {
169. } else if (insType === "techcombank_account") {
170. } else if (insType === "vib_account") {
171. } else if (insType === "bidv_account") {
172. } else if (insType === "tpbank_account") {
173. } else if (insType === "shb_account") {
174. } else if (insType === "shb_customer_id") {
175. } else if (insType === "vpbank_account") {
176. } else if (insType === "oceanbank_online_account") {
177. } else if (insType === "oceanbank_mobile_account") {
178. } else if (insType === "pvcombank_account") {
179. if (inCvv.value === "") return err("MISSING_FIELD"
180. if (inCvv.value === "") {
181. if ((cardno.length === 15
182. cardno.length === 16
183. cardno.length === 19) && isMode10(cardno) === true) {
184. isMode10(cardno) === true) {
185. if (params["CardList"] === undefined
186. params["CardList"] === null) return;
187. if (insSwiftCode === "BFTVVNVX") { //Vietcombank
188. } else if (insSwiftCode === "BIDVVNVX") { //BIDV
189. typeof exports === 'object'
190. typeof define === 'function'
191. selector === '#') {
192. if (typeof element.getRootNode === 'function') {
193. if (typeof $ === 'undefined') {
194. version[0] === minMajor
195. version[1] === minMinor
196. if (config === 'close') {
197. if (input.type === 'radio') {
198. } else if (input.type === 'checkbox') {
199. if (this._element.tagName === 'LABEL'
200. input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
201. if (config === 'toggle') {
202. if (_button.getAttribute('aria-pressed') === 'true') {
203. if (activeIndex === index) {
204. if (this._config.pause === 'hover') {
205. if (_this3._config.pause === 'hover') {
206. var isNextDirection = direction === Direction.NEXT
207. var isPrevDirection = direction === Direction.PREV
208. activeIndex === 0
209. activeIndex === lastItemIndex
210. var delta = direction === Direction.PREV ? -1 : 1
211. return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
212. if (direction === Direction.NEXT) {
213. if (typeof config === 'object') {
214. var action = typeof config === 'string' ? config : _config.slide
215. if (typeof config === 'number') {
216. } else if (typeof action === 'string') {
217. if (typeof data[action] === 'undefined') {
218. return foundElem === element
219. if (typeof _this._config.parent === 'string') {
220. return elem.getAttribute('data-parent') === _this._config.parent
221. if (actives.length === 0) {
222. typeof config === 'object'
223. if (typeof config === 'string') {
224. if (typeof data[config] === 'undefined') {
225. if (event.currentTarget.tagName === 'A') {
226. if (usePopper === void 0) {
227. if (typeof Popper === 'undefined') {
228. if (this._config.reference === 'parent') {
229. $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
230. if (typeof this._config.offset === 'function') {
231. if (this._config.display === 'static') {
232. var _config = typeof config === 'object' ? config : null
233. if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
234. event.type === 'keyup'
235. event.type === 'click') {
236. if (event && (event.type === 'click'
237. event.which === TAB_KEYCODE) && $.contains(parent
238. if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
239. event.which === ESCAPE_KEYCODE) {
240. if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
241. event.which === SPACE_KEYCODE)) {
242. if (event.which === ESCAPE_KEYCODE) {
243. if (items.length === 0) {
244. if (event.which === ARROW_UP_KEYCODE
245. if (event.which === ARROW_DOWN_KEYCODE
246. if (this._config.backdrop === 'static') {
247. $(_this5._element).has(event.target).length === 0) {
248. if (event.which === ESCAPE_KEYCODE$1) {
249. if (this.tagName === 'A'
250. this.tagName === 'AREA') {
251. if (unsafeHtml.length === 0) {
252. typeof sanitizeFn === 'function') {
253. if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
254. if (_ret === "continue") continue;
255. if ($(this.element).css('display') === 'none') {
256. var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
257. if (prevHoverState === HoverState.OUT) {
258. if (typeof content === 'object'
259. title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
260. if (typeof this.config.offset === 'function') {
261. if (this.config.container === false) {
262. if (trigger === 'click') {
263. var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
264. var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
265. context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
266. context._hoverState === HoverState.SHOW) {
267. if (context._hoverState === HoverState.SHOW) {
268. context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
269. if (context._hoverState === HoverState.OUT) {
270. if (typeof config.delay === 'number') {
271. if (typeof config.title === 'number') {
272. if (typeof config.content === 'number') {
273. var _config = typeof config === 'object'
274. if (typeof content === 'function') {
275. this._scrollElement = element.tagName === 'BODY' ? window : element
276. var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
277. var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
278. var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
279. return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
280. return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
281. var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
282. this._element.parentNode.nodeType === Node.ELEMENT_NODE
283. var itemSelector = listElement.nodeName === 'UL'
284. listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
285. var activeElements = container && (container.nodeName === 'UL'
286. container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
287. if (active.getAttribute('role') === 'tab') {
288. if (element.getAttribute('role') === 'tab') {
289. 1>t)throw Error("numRounds must a integer >= 1");if("SHA-1"===c)m=512,q=K,u=Z,f=160,r=function(a){return a.slice()};else if(0===c.lastIndexOf("SHA-",0))if(q=function(a,b){return L(a,b,c)
290. c)},u=function(a,b,h,e){var k,f;if("SHA-224"===c
291. "SHA-256"===c)k=(b+65>>>9<<4)+15,f=16;else if("SHA-384"===c
292. "SHA-512"===c)k=(b+129>>>10<<
293. c);if("SHA-224"===c)a=[e[0],e[1],e[2],e[3],e[4],e[5],e[6]];else if("SHA-256"===c)a=e;else if("SHA-384"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,e[4].b,e[5].a,e[5].b];else if("SHA-512"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,
294. "SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)
295. 0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
296. return a},r=function(a){return a.slice()},"SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)||0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
297. c)m=1152,f=224;else if("SHA3-256"===c)m=1088,f=256;else if("SHA3-384"===c)m=832,f=384;else if("SHA3-512"===c)m=576,f=512;else if("SHAKE128"===c)m=1344,f=-1,F=31,z=!0;else if("SHAKE256"===c)m=1088,f=-1,F=31,z=!0;else throw Error("Chosen SHA variant is not supported");u=function(a
298. 0===64*l%e
299. 5][l/5|0];g.push(a.b);if(32*g.length>=h)break;g.push(a.a);l+=1;0===64*l%e&&D(null,b)}return g}}else throw Error("Chosen SHA variant is not supported");d=M(a,g,x);l=A(c);this.setHMACKey=function(a,b,h){var k;if(!0===I)throw Error("HMAC key already set");if(!0===y)throw Error("Cannot set HMAC key after calling update");if(!0===z)throw Error("SHAKE is not supported for HMAC");g=(h
300. f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
301. )b.push(0);b[h]&=4294967040}for(a=0;a<=h;a+=1)v[a]=b[a]^909522486,w[a]=b[a]^1549556828;l=q(v,l);e=m;I=!0};this.update=function(a){var c,b,k,f=0,g=m>>>5;c=d(a,h,n);a=c.binLen;b=c.value;c=a>>>5;for(k=0;k<c;k+=g)f+m<=a&&(l=q(b.slice(k,k+g),l),f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
302. for(g=1;g<t;g+=1)!0===z
303. f);return k(m)};this.getHMAC=function(a,b){var k,g,d,p;if(!1===I)throw Error("Cannot call getHMAC without first setting HMAC key");d=N(b);switch(a){case "HEX":k=function(a){return O(a,f,x,d)};break;case "B64":k=function(a){return P(a,f,x,d)};break;case "BYTES":k=function(a){return Q(a,f,x)};break;case "ARRAYBUFFER":try{k=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}k=function(a){return R(a,f,x)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
304. d=-1===b?3:0
305. f=-1===b?3:0
306. g=-1===b?3:0
307. !0===c.hasOwnProperty("b64Pad")
308. a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");
309. u=-1===b?3:0
310. l+=2){p=parseInt(a.substr(l,2),16);if(isNaN(p))throw Error("String of HEX type contains invalid characters");m=(l>>>1)+q;for(f=m>>>2;c.length<=f;)c.push(0);c[f]|=p<<8*(u+m%4*b)}return{value:c,binLen:4*g+d}};break;case "TEXT":c=function(c,h,d){var g,l,p=0,f,m,q,u,r,t;h=h||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(t=-1===
311. m+=1){r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=l[m]<<8*(t+r%4*b);p+=1}else if("UTF16BE"===a
312. "UTF16LE"===a)for(t=-1===b?2:0
313. UTF16LE"===a
314. 1===b
315. !0===l
316. !0===l&&(m=g&255,g=m<<8|g>>>8);r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=g<<8*(t+r%4*b);p+=2}return{value:h,binLen:8*p+d}};break;case "B64":c=function(a,c,d){var g=0,l,p,f,m,q,u,r,t;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");p=a.indexOf("=");a=a.replace(/\=/g
317. t=-1===b?3:0
318. q=-1===b?3:0
319. m=-1===b?3:0
320. c.b^a.b)}function A(c){var a=[],d;if("SHA-1"===c)a=[1732584193,4023233417,2562383102,271733878,3285377520];else if(0===c.lastIndexOf("SHA-",0))switch(a=
321. new b(d[2],4271175723),new b(d[3],1595750129),new b(d[4],2917565137),new b(d[5],725511199),new b(d[6],4215389547),new b(d[7],327033209)];break;default:throw Error("Unknown SHA variant");}else if(0===c.lastIndexOf("SHA3-",0)
322. 0===c.lastIndexOf("SHAKE",0))for(c=0
323. a,k){var e,h,n,g,l,p,f,m,q,u,r,t,v,w,y,A,z,x,F,B,C,D,E=[],J;if("SHA-224"===k
324. "SHA-256"===k)u=64,t=1,D=Number,v=G,w=la,y=H,A=ha,z=ja,x=da,F=fa,C=U,B=aa,J=d;else if("SHA-384"===k
325. "SHA-512"===k)u=80,t=2,D=b,v=ma,w=na,y=oa,A=ia,z=ka,x=ea,F=ga,C=ca,B=ba,J=V;else throw Error("Unexpected error in SHA-2 implementation");k=a[0];e=a[1];h=a[2];n=a[3];g=a[4];l=a[5];p=a[6];f=a[7];for(r=0
326. "function"===typeof define
327. 1>u)throw Error("numRounds must a integer >= 1");if(0===c.lastIndexOf("SHA-",0))if(q=function(b,a){return A(b,a,c)
328. c)},y=function(b,a,l,f){var g,e;if("SHA-224"===c
329. "SHA-256"===c)g=(a+65>>>9<<4)+15,e=16;else throw Error("Unexpected error in SHA-2 implementation");for(
330. c);if("SHA-224"===c)b=[f[0],f[1],f[2],f[3],f[4],f[5],f[6]];else if("SHA-256"===c)b=f;else throw Error("Unexpected error in SHA-2 implementation");return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
331. "SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a
332. return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
333. if(!0===z)throw Error("Cannot set HMAC key after calling update");f=(g
334. 5);g=a%h;z=!0};this.getHash=function(a,f){var d,h,k,q;if(!0===m)throw Error("Cannot call getHash after setting HMAC key");k=C(f);switch(a){case "HEX":d=function(a){return D(a,e,k)};break;case "B64":d=function(a){return E(a,e,k)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{h=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("format must be HEX, B64, BYTES, or ARRAYBUFFER");
335. x(c));return d(q)};this.getHMAC=function(a,f){var d,k,t,u;if(!1===m)throw Error("Cannot call getHMAC without first setting HMAC key");t=C(f);switch(a){case "HEX":d=function(a){return D(a,e,t)};break;case "B64":d=function(a){return E(a,e,t)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{d=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
336. h=(d>>>1)+q;for(e=h>>>2;b.length<=e;)b.push(0);b[e]|=k<<8*(3+h%4*-1)}return{value:b,binLen:4*f+c}};break;case "TEXT":d=function(c,b,d){var f,n,k=0,e,h,q,m,p,r;b=b||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(r=3
337. q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=n[h]<<8*(r+p%4*-1);k+=1}else if("UTF16BE"===a
338. "UTF16LE"===a)for(r=2
339. !0===n
340. e+=1){f=c.charCodeAt(e);!0===n&&(h=f&255,f=h<<8|f>>>8);p=k+q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=f<<8*(r+p%4*-1);k+=2}return{value:b,binLen:8*k+d}};break;case "B64":d=function(a,b,c){var f=0,d,k,e,h,q,m,p;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");k=a.indexOf("=");a=a.replace(/\=/g
341. 16)&65535)<<16|b&65535}function R(c,a,d,l,b){var g=(c&65535)+(a&65535)+(d&65535)+(l&65535)+(b&65535);return((c>>>16)+(a>>>16)+(d>>>16)+(l>>>16)+(b>>>16)+(g>>>16)&65535)<<16|g&65535}function x(c){var a=[],d;if(0===c.lastIndexOf("SHA-",0))switch(a=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428]
342. new m,new m,new m,new m,new m,new m];break;case "SHA-512":a=[new m,new m,new m,new m,new m,new m,new m,new m];break;default:throw Error("Unknown SHA variant");}else throw Error("No SHA variants supported");return a}function A(c,a,d){var l,b,g,f,n,k,e,h,m,r,p,w,t,x,u,z,A,B,C,D,E,F,v=[],G;if("SHA-224"===d
343. "SHA-256"===d)r=64,w=1,F=Number,t=P,x=Q,u=R,z=N,A=O,B=L,C=M,E=K,D=J,G=H;else throw Error("Unexpected error in SHA-2 implementation");d=a[0];l=a[1];b=a[2];g=a[3];f=a[4];n=a[5];k=a[6];e=a[7];for(p=
344. if (typeof(index) === 'boolean') {
345. if (typeof(index) === 'number') {
346. if (index === 0
347. _.$slides.length === 0) {
348. if (addBefore === true) {
349. if (_.options.slidesToShow === 1
350. _.options.adaptiveHeight === true
351. _.options.vertical === false) {
352. if (_.options.rtl === true
353. if (_.transformsEnabled === false) {
354. if (_.options.vertical === false) {
355. if (_.cssTransitions === false) {
356. if (_.options.rtl === true) {
357. typeof asNavFor === 'object' ) {
358. if (_.options.fade === false) {
359. if ( _.options.infinite === false ) {
360. if ( _.direction === 1
361. ( _.currentSlide + 1 ) === ( _.slideCount - 1 )) {
362. else if ( _.direction === 0 ) {
363. if ( _.currentSlide - 1 === 0 ) {
364. if (_.options.arrows === true ) {
365. if (_.options.dots === true
366. _.$slideTrack = (_.slideCount === 0) ?
367. if (_.options.centerMode === true
368. _.options.swipeToSlide === true) {
369. _.setSlideClasses(typeof _.currentSlide === 'number' ? _.currentSlide : 0);
370. if (_.options.draggable === true) {
371. if (_.respondTo === 'window') {
372. } else if (_.respondTo === 'slider') {
373. } else if (_.respondTo === 'min') {
374. if (_.originalSettings.mobileFirst === false) {
375. if (_.breakpointSettings[targetBreakpoint] === 'unslick') {
376. if (initial === true) {
377. slideOffset = indexOffset === 0 ? _.options.slidesToScroll : _.options.slidesToShow - indexOffset
378. slideOffset = indexOffset === 0 ? _.options.slidesToScroll : indexOffset
379. var index = event.data.index === 0 ? 0 :
380. if (_.options.accessibility === true) {
381. if (_.options.arrows === true
382. if (_.options.focusOnSelect === true) {
383. if (_.shouldClick === false) {
384. if (_.options.infinite === true) {
385. } else if (_.options.centerMode === true) {
386. if (_.options.vertical === true
387. _.options.centerMode === true) {
388. if (_.options.slidesToShow === 2) {
389. } else if (_.options.slidesToShow === 1) {
390. } else if (_.options.centerMode === true
391. _.options.infinite === true) {
392. if (_.options.variableWidth === true) {
393. _.options.infinite === false) {
394. if (_.options.centerMode === true) {
395. if (_.options.infinite === false) {
396. centerOffset = _.options.centerMode === true ? _.slideWidth * Math.floor(_.options.slidesToShow / 2) : 0
397. if (_.options.swipeToSlide === true) {
398. _.options.pauseOnDotsHover === true
399. if (event.keyCode === 37
400. _.options.accessibility === true) {
401. message: _.options.rtl === true ? 'next' :
402. } else if (event.keyCode === 39
403. message: _.options.rtl === true ? 'previous' : 'next'
404. if (_.options.fade === true) {
405. if (_.options.lazyLoad === 'anticipated') {
406. } else if (_.currentSlide === 0) {
407. if (_.options.lazyLoad === 'progressive') {
408. if ( _.options.adaptiveHeight === true ) {
409. if ( $.type(responsiveSettings) === 'array'
410. _.breakpoints[l] === currentBreakpoint ) {
411. index = removeBefore === true ? 0 : _.slideCount - 1
412. index = removeBefore === true ? --index : index
413. if (removeAll === true) {
414. if (_.options.vertical === false
415. _.options.variableWidth === false) {
416. } else if (_.options.variableWidth === true) {
417. if (_.options.variableWidth === false) _.$slideTrack.children('.slick-slide').width(_.slideWidth - offset);
418. if( $.type( arguments[0] ) === 'object' ) {
419. } else if ( $.type( arguments[0] ) === 'string' ) {
420. if ( arguments[0] === 'responsive'
421. $.type( arguments[1] ) === 'array' ) {
422. if ( type === 'single' ) {
423. } else if ( type === 'multiple' ) {
424. } else if ( type === 'responsive' ) {
425. if( _.options.responsive[l].breakpoint === value[item].breakpoint ) {
426. _.positionProp = _.options.vertical === true ? 'top' : 'left'
427. if (_.positionProp === 'top') {
428. if (_.options.useCSS === true) {
429. if ( typeof _.options.zIndex === 'number' ) {
430. if (bodyStyle.perspectiveProperty === undefined
431. bodyStyle.webkitPerspective === undefined) _.animType = false;
432. bodyStyle.MozPerspective === undefined) _.animType = false;
433. if (bodyStyle.msTransform === undefined) _.animType = false;
434. var evenCoef = _.options.slidesToShow % 2 === 0 ? 1 : 0
435. if (index === 0) {
436. } else if (index === _.slideCount - 1) {
437. indexOffset = _.options.infinite === true ? _.options.slidesToShow + index : index
438. if (_.options.lazyLoad === 'ondemand'
439. _.options.lazyLoad === 'anticipated') {
440. if (_.options.infinite === true
441. _.options.fade === false) {
442. if (_.animating === true
443. _.options.waitForAnimate === true) {
444. if (_.options.fade === true
445. _.currentSlide === index) {
446. if (sync === false) {
447. _.currentLeft = _.swipeLeft === null ? slideLeft : _.swipeLeft
448. if (_.options.infinite === false
449. _.options.centerMode === false
450. } else if (_.options.infinite === false
451. _.options.centerMode === true
452. return (_.options.rtl === false ? 'left' : 'right');
453. return (_.options.rtl === false ? 'right' : 'left');
454. if (_.options.verticalSwiping === true) {
455. if ( _.touchObject.curX === undefined ) {
456. if ( _.touchObject.edgeHit === true ) {
457. if ((_.options.swipe === false) || ('ontouchend' in document
458. _.options.swipe === false)) {
459. } else if (_.options.draggable === false
460. positionOffset = (_.options.rtl === false ? 1 : -1) * (_.touchObject.curX > _.touchObject.startX ? 1 : -1);
461. if ((_.currentSlide === 0
462. swipeDirection === 'right') || (_.currentSlide >= _.getDotCount()
463. swipeDirection === 'left')) {
464. _.options.touchMove === false) {
465. if (_.animating === true) {
466. if ( _.options.arrows === true
467. if (_.currentSlide === 0) {
468. _.options.centerMode === false) {
469. //Customizable =================================================================================================

== (451 điều kiện duy nhất):
------------------------------------------------------------
1. 'vi' == params['locale']) {
2. 'en' == params['locale']) {
3. <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
4. (errorCode == 'overtime' || errorCode == '253')  && !invoiceNearExpire && !paymentPending
5. <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
6. !(errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
7. params.response_code == 'overtime') {
8. if (_re.status == '200'
9. _re.status == '201') {
10. if (_re2.status == '200'
11. _re2.status == '201') {
12. if (this.errorCode == 'overtime'
13. this.errorCode == '253') {
14. } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
15. if (lastPayment?.state == 'pending') {
16. if ((dataPassed.status == '200'
17. dataPassed.status == '201') && dataPassed.body != null) {
18. token_site == 'onepay'
19. this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
20. if (this._b == 18
21. this._b == 19) {
22. if (this._b == 19) {//19BIDV
23. } else if (this._b == 3
24. this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
25. if (this._b == 27) {
26. } else if (this._b == 12) {// 12SHB
27. } else if (this._b == 18) { //18Oceanbank-ocb
28. if (this._b == 19
29. this._b == 3
30. this._b == 27
31. this._b == 12) {
32. } else if (this._b == 18) {
33. if (this.checkBin(_val.value) && (this._b == 3
34. this._b == 27)) {
35. if (this._b == 3) {
36. this.cardTypeOcean == 'ATM') {
37. if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
38. this._b == 18)) {
39. if (this.checkBin(v) && (this._b == 3
40. event.inputType == 'deleteContentBackward') {
41. if (event.target.name == 'exp_date'
42. event.inputType == 'insertCompositionText') {
43. if (((this.valueDate.length == 4
44. this.valueDate.search('/') == -1) || this.valueDate.length == 5)
45. this.valueDate.length == 5)
46. if (temp.length == 0) {
47. return (counter % 10 == 0);
48. } else if (this._b == 19) {
49. } else if (this._b == 27) {
50. if (this._b == 12) {
51. if (this.cardTypeBank == 'bank_customer_code') {
52. } else if (this.cardTypeBank == 'bank_account_number') {
53. _formCard.exp_date.length == 5
54. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
55. this._b == 3)) {//27-pvcombank;3-TPB ;
56. if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
57. this._b == 19
58. this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
59. if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
60. if (this.cardTypeOcean == 'IB') {
61. } else if (this.cardTypeOcean == 'MB') {
62. } else if (this.cardTypeOcean == 'ATM') {
63. this.token_site == 'onepay'
64. if (this._res_post.state == 'approved'
65. this._res_post.state == 'failed') {
66. } else if (this._res_post.state == 'authorization_required') {
67. if (this._b == 18) {
68. if (this._b == 27
69. this._b == 18) {
70. if (err.status == 400
71. err.status == 500) {
72. if (err.error && (err.error.code == 13
73. err.error.code == '13')) {
74. if ((cardNo.length == 16
75. if ((cardNo.length == 16 || (cardNo.length == 19
76. && ((this._b == 18
77. cardNo.length == 19) || this._b != 18)
78. if (this._b == +e.id) {
79. if (valIn == 1) {
80. } else if (valIn == 2) {
81. this._b == 3) {
82. if (this._b == 19) {
83. if (cardType == this._translate.instant('internetbanking')
84. } else if (cardType == this._translate.instant('mobilebanking')
85. } else if (cardType == this._translate.instant('atm')
86. this._b == 18))) {
87. } else if (this._b == 18
88. this.c_expdate = !(((this.valueDate.length == 4
89. this.valueDate.length == 4
90. this.valueDate.search('/') == -1)
91. this.valueDate.length == 5))
92. if (this._b == 8) {//MB Bank
93. if (this._b == 18) {//Oceanbank
94. if (this._b == 8) {
95. } else if (this._res.state == 'authorization_required') {
96. if (this._b == 18) {//8-MB Bank;18-oceanbank
97. if (this._b == 2
98. this._b == 5
99. this._b == 31) {
100. if (this._b == 2) {
101. } else if (this._b == 5) {
102. } else if (this._b == 6) {
103. } else if (this._b == 31) {
104. if (this._b == 20
105. this._b == 33
106. this._b == 39
107. this._b == 43
108. this._b == 45
109. this._b == 67
110. this._b == 64) {//seabank
111. if (this._b == 1
112. this._b == 20
113. this._b == 36
114. this._b == 64
115. this._b == 55
116. this._b == 47
117. this._b == 48
118. this._b == 59) {
119. return this._b == 9
120. this._b == 11
121. this._b == 16
122. this._b == 17
123. this._b == 25
124. this._b == 44
125. this._b == 54
126. this._b == 57
127. this._b == 59
128. this._b == 61
129. this._b == 63
130. this._b == 68
131. this._b == 69
132. this._b == 14
133. this._b == 15
134. this._b == 24
135. this._b == 8
136. this._b == 10
137. this._b == 22
138. this._b == 23
139. this._b == 30
140. this._b == 17) {
141. (cardNo.length == 19
142. (cardNo.length == 19 && (this._b == 1
143. this._b == 4
144. this._b == 59))
145. this._util.checkMod10(cardNo) == true
146. return ((value.length == 4
147. value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
148. value.length == 5) && parseInt(value.split('/')[0]
149. || (this._util.checkValidExpireMonthYear(value) && (this._b == 20
150. this._b == 67)))
151. this._inExpDate.length == 4
152. this._inExpDate.search('/') == -1)
153. this._inExpDate.length == 5))
154. || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 20
155. this._b == 67)));
156. _auth==0
157. *ngIf="!token&&_auth==0&&(_b==1
158. _b==4
159. _b==7
160. _b==8
161. _b==9
162. _b==10
163. _b==11
164. _b==14
165. _b==15
166. _b==16
167. _b==17
168. _b==20
169. _b==22
170. _b==23
171. _b==24
172. _b==25
173. _b==30
174. _b==33
175. _b==34
176. _b==35
177. _b==36
178. _b==37
179. _b==38
180. _b==39
181. _b==40
182. _b==41
183. _b==42
184. _b==43
185. _b==44
186. _b==45
187. this._b==46
188. _b==47
189. _b==48
190. _b==49
191. _b==50
192. _b==51
193. _b==52
194. _b==53
195. _b==54
196. _b==55
197. _b==56
198. _b==57
199. _b==58
200. _b==59
201. _b==60
202. _b==61
203. _b==62
204. _b==63
205. _b==64
206. this._b==65
207. _b==66
208. _b==67
209. _b==68
210. _b==69
211. this._b==70
212. this._b==71)">
213. _auth==0&&(_b==6 || _b==2 || _b==5 || _b == 31)
214. _b == 31)">
215. _auth==0&&(_b==3||_b==12||_b==18||_b==19||_b==27)
216. token || _auth==1
217. this._auth == 0
218. this.tokenList.length == 0) {
219. this.filteredData.length == 1
220. $event == 'true') {
221. this._b == '55'
222. this._b == '47'
223. this._b == '48'
224. this._b == '59') {
225. this._b == '3'
226. this._b == '43'
227. this._b == '45'
228. this._b == '54'
229. this._b == '57'
230. this._b == '59'
231. this._b == '61'
232. this._b == '63'
233. this._b == '67'
234. this._b == '68'
235. this._b == '69') {
236. this._b == '67') {
237. data['type'] == '5'
238. data['type'] == '7'
239. data['type'] == '9'
240. data['type'] == '10'"
241. data['type'] == '7'"
242. data['type'] == '9'"
243. data['type'] == '6'
244. data['type'] == '8'"
245. regexp.test('card;;visa;USD')) && (this._type == 5
246. this._type == 7);
247. regexp.test('card;;mastercard;USD')) && (this._type == 5
248. regexp.test('card;;amex;USD')) && (this._type == 6
249. this._type == 8);
250. regexp.test('card;;jcb;USD')) && (this._type == 9);
251. regexp.test('card;;cup;USD')) && (this._type == 10);
252. } else if (this._res_post.state == 'failed') { // lỗi mới kiểm tra sang trang lỗi
253. if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
254. } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
255. if (err.error && (err.error.code == 8
256. err.error.code == '8')) {
257. if (this._type == 5
258. this._type == 6) {
259. } else if (this._type == 7
260. this._type == 8) {
261. } else if (err.error && (err.error.code == 13
262. v.length == 15) || (v.length == 16
263. v.length == 19))
264. this._util.checkMod10(v) == true) {
265. cardNo.length == 15)
266. cardNo.length == 16)
267. cardNo.startsWith('81')) && (cardNo.length == 16
268. cardNo.length == 19))
269. this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
270. this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
271. v.length == 5) {
272. v.length == 4
273. v.length == 3)
274. _val.value.length == 4
275. _val.value.length == 3)
276. this.requireAvs = this.AVS_COUNTRIES.find(e => e.code == this._i_country_code)? true : false;
277. this._i_csc.length == 4) ||
278. this._i_csc.length == 3)
279. country = this.AVS_COUNTRIES.find(e => e.code == res.bin_country);
280. countryCode == 'US' ? US_STATES
281. : countryCode == 'CA' ? CA_STATES
282. if (el == 5) {
283. } else if (el == 6) {
284. } else if (el == 7) {
285. } else if (el == 8) {
286. } else if (el == 2) {
287. } else if (el == 4) {
288. } else if (el == 3) {
289. } else if (el == 9) {
290. if (!isNaN(_re.status) && (_re.status == '200'
291. _re.status == '201') && _re.body != null) {
292. if (('closed' == this._res_polling.state
293. 'canceled' == this._res_polling.state
294. 'expired' == this._res_polling.state)
295. } else if ('paid' == this._res_polling.state) {
296. this._res_polling.payments == null) {
297. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
298. this._paymentService.getCurrentPage() == 'enter_card') {
299. } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
300. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
301. } else if ('not_paid' == this._res_polling.state) {
302. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
303. if (message == '1') {
304. if (this.type == 5
305. } else if (this.type == 6
306. } else if (this.type == 2
307. } else if (this.type == 7
308. } else if (this.type == 9
309. } else if (this.type == 10
310. } else if (this.type == 8
311. } else if (this.type == 4
312. } else if (this.type == 3
313. this._auth == 0) {
314. if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
315. this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
316. if (this._res.payments[this._res.payments.length - 1].state == 'approved'
317. } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
318. } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
319. this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
320. } else if (idBrand == 'atm'
321. if ('paid' == this._res.state) {
322. this._res.merchant.token_site == 'onepay')) {
323. if (('closed' == this._res.state
324. 'canceled' == this._res.state
325. 'expired' == this._res.state
326. 'paid' == this._res.state)
327. this._res.payments == null) {
328. this._res.payments[this._res.payments.length - 1].state == 'pending') {
329. if (this._res.currencies[0] == 'USD') {
330. if (item.instrument.issuer.brand.id == 'atm') {
331. } else if (item.instrument.issuer.brand.id == 'visa'
332. item.instrument.issuer.brand.id == 'mastercard') {
333. if (item.instrument.issuer_location == 'd') {
334. } else if (item.instrument.issuer.brand.id == 'amex') {
335. } else if (item.instrument.issuer.brand.id == 'jcb') {
336. this.tokenList.length == 1) {
337. if (_val == 2
338. if (this.type == 4) {
339. if (data._locale == 'en') {
340. if (item.available == true) {
341. data['type'] == 'Visa'
342. data['type'] == 'Master'
343. data['type'] == 'JCB'"
344. data['type'] == 'Visa'"
345. data['type'] == 'Master'"
346. data['type'] == 'Amex'"
347. token_main == '1'"
348. if (message == '0') {
349. item.id == element.id ? element['active'] = true : element['active'] = false
350. item.display_cvv == true ? this.flagToken = true : this.flagToken = false
351. if (result == 'success') {
352. if (this.tokenList.length == 0) {
353. } else if (result == 'error') {
354. _val.value.length == 4) || (item.brand_id != 'amex'
355. _val.value.length == 3))
356. _val.value.length == 3)));
357. id == 'amex' ? this.maxLength = 4 : this.maxLength = 3
358. if (_re.body.state == 'more_info_required') {
359. if (this._res_post.state == 'failed') {
360. } else if (_re.body.state == 'authorization_required') {
361. this._b == 18
362. this._b == 27) {
363. } else if (_re.body.state == 'failed') {
364. if (action == 'blur') {
365. this._i_token_otp.length == 4)
366. this._i_token_otp.length == 3));
367. if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
368. else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';
369. if (e.name == bankSwift) { // TODO: get by swift
370. if (+e.id == bankId) {
371. if (e.swiftCode == bankSwift) {
372. if (currency == 'USD') {
373. if (this.checkCount == 1) {
374. if (results == null) {
375. if (c.length == 3) {
376. d = d == undefined ? '.' : d
377. t = t == undefined ? '
378. return results == null ? null : results[1]
379. if (_dataCache == null) {
380. if ( (0 <= r && r <= 6 && (c == 0
381. c == 6) )
382. || (0 <= c && c <= 6 && (r == 0
383. r == 6) )
384. if (i == 0
385. _modules[r][6] = (r % 2 == 0);
386. _modules[6][c] = (c % 2 == 0);
387. if (r == -2
388. r == 2
389. c == -2
390. c == 2
391. || (r == 0
392. c == 0) ) {
393. ( (bits >> i) & 1) == 1);
394. if (col == 6) col -= 1;
395. if (_modules[row][col - c] == null) {
396. dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
397. if (bitIndex == -1) {
398. margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
399. if (typeof arguments[0] == 'object') {
400. margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
401. if (b == -1) throw 'eof';
402. if (b0 == -1) break;
403. if (typeof b == 'number') {
404. if ( (b & 0xff) == b) {
405. return function(i, j) { return (i + j) % 2 == 0
406. return function(i, j) { return i % 2 == 0
407. return function(i, j) { return j % 3 == 0
408. return function(i, j) { return (i + j) % 3 == 0
409. return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
410. return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
411. return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
412. return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
413. if (r == 0
414. c == 0) {
415. if (dark == qrcode.isDark(row + r, col + c) ) {
416. if (count == 0
417. count == 4) {
418. if (typeof num.length == 'undefined') {
419. num[offset] == 0) {
420. if (typeof rsBlock == 'undefined') {
421. return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
422. _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
423. if (data.length - i == 1) {
424. } else if (data.length - i == 2) {
425. } else if (n == 62) {
426. } else if (n == 63) {
427. if (_buflen == 0) {
428. if (c == '=') {
429. } else if (c == 0x2b) {
430. } else if (c == 0x2f) {
431. if (table.size() == (1 << bitLength) ) {
432. if (cardList.length == 1) {//TOKEN, BIDV, SHB, OCEAN, PVCOM, TP Bank
433. if ( $('.circle_v1').css('display') == 'block'
434. if ($('.circle_v2').css('display') == 'block'
435. $('.circle_v3').css('display') == 'block' ) {
436. $('.circle_v1').css('display') == 'block'
437. document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM'
438. if ( document.getElementsByClassName("select_online")[0].value == 'Easy Online Banking') {
439. if ( document.getElementsByClassName("select_online")[0].value == 'Easy Mobile Banking') {
440. if ( document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM') {
441. if ($('.circle_v1').css('display') == 'block') {
442. if ($('.circle_v1').css('display') == 'block'
443. if ( $('.circle_v1').css('display') == 'block') {
444. else if ($('.circle_v2').css('display') == 'block') {
445. if ( $('.circle_v3').css('display') == 'block' ) {
446. if ($('.circle_v2').css('display') == 'block') {
447. x = _.positionProp == 'left' ? Math.ceil(position) + 'px' : '0px'
448. y = _.positionProp == 'top' ? Math.ceil(position) + 'px' : '0px'
449. if (_.options.slidesToShow == _.options.slidesToScroll
450. if (typeof opt == 'object'
451. typeof opt == 'undefined')

!== (158 điều kiện duy nhất):
------------------------------------------------------------
1. this.lastValue !== $event.target.value)) {
2. if (YY % 400 === 0 || (YY % 100 !== 0
3. if (YYYY % 400 === 0 || (YYYY % 100 !== 0
4. event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
5. key !== '3') {
6. this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
7. codeResponse.toString() !== '0') {
8. cardNo.length !== 0) {
9. if (this.cardTypeBank !== 'bank_card_number') {
10. if (this.cardTypeBank !== 'bank_account_number') {
11. if (this.cardTypeBank !== 'bank_username') {
12. if (this.cardTypeBank !== 'bank_customer_code') {
13. this.lb_card_account !== this._translate.instant('ocb_account')) {
14. this.lb_card_account !== this._translate.instant('ocb_phone')) {
15. this.lb_card_account !== this._translate.instant('ocb_card')) {
16. this._b !== 18) || (this.cardTypeOcean === 'ATM'
17. if (this.tracking.hasOwnProperty(key) && ((key !== '8'
18. !this._showNameOnCard) || (key !== '9'
19. codeResponse.toString() !== '0'){
20. event.inputType !== 'deleteContentBackward') || v.length == 5) {
21. this._i_country_code !== 'US') {
22. itemRemoved !== '') {
23. if (deviceValue !== 'default') {
24. this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
25. this._i_country_code !== 'default'
26. this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
27. this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {
28. if (_val !== 3) {
29. this._util.getElementParams(this.currentUrl, 't_id') !== '') {
30. queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
31. if (queryString !== '') {
32. if (target !== 0
33. if (e !== null) {
34. if (lang !== "vi") lang = "en";
35. this.oldValue !== this.value) {
36. if (jLinks !== undefined
37. jLinks !== null) {
38. if (jMerchantReturn !== undefined
39. jMerchantReturn !== null) {
40. if (responseCode !== undefined
41. responseCode !== null
42. if (parentRes !== "{
43. if (value !== "") {
44. if (inMonth.value !== ""
45. if (inYear.value !== ""
46. var month = inMonth.value !== ""
47. var year = parseInt("20" + (inYear.value !== ""
48. if ("2D" !== order["vpc_VerType"]) return err("MISSING_FIELD"
49. inMonth.value !== tokenInsMonth) instrument["month"] = inMonth.value;
50. inYear.value !== tokenInsYear) instrument["year"] = inYear.value;
51. if (inCvv.value !== "") instrument["cvv"] = inCvv.value;
52. if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
53. if (inNumber.style.display !== "none") instrument.number = inNumber.value;
54. if (inDate.style.display !== "none") instrument.date = inDate.value;
55. if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
56. if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
57. if (inName.style.display !== "none") instrument.name = inName.value;
58. typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
59. hrefAttr !== '#' ? hrefAttr.trim() : ''
60. $(this._element).css('visibility') !== 'hidden') {
61. if (selector !== null
62. if (selector !== null) {
63. if (typeof this._config.parent.jquery !== 'undefined') {
64. if (typeof this._config.reference.jquery !== 'undefined') {
65. if (this._config.boundary !== 'scrollParent') {
66. if (this._popper !== null) {
67. event.which !== TAB_KEYCODE)) {
68. event.which !== ESCAPE_KEYCODE
69. if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
70. event.which !== ARROW_UP_KEYCODE
71. this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
72. if (document !== event.target
73. _this5._element !== event.target
74. if (event.target !== event.currentTarget) {
75. if (typeof margin !== 'undefined') {
76. if (allowedAttributeList.indexOf(attrName) !== -1) {
77. if (uriAttrs.indexOf(attrName) !== -1) {
78. var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
79. if (_this2._hoverState !== HoverState.SHOW
80. if (_this2._popper !== null) {
81. if (data.originalPlacement !== data.placement) {
82. } else if (trigger !== Trigger.MANUAL) {
83. titleType !== 'string') {
84. if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
85. if (this.constructor.Default[key] !== this.config[key]) {
86. if (tabClass !== null
87. if (tip.getAttribute('x-placement') !== null) {
88. if (typeof config.target !== 'string') {
89. if (this._scrollHeight !== scrollHeight) {
90. if (this._activeTarget !== target) {
91. var isActiveTarget = this._activeTarget !== this._targets[i]
92. 'use strict';(function(Y){function C(c,a,b){var e=0,h=[],n=0,g,l,d,f,m,q,u,r,I=!1,v=[],w=[],t,y=!1,z=!1,x=-1;b=b||{};g=b.encoding||"UTF8";t=b.numRounds||1;if(t!==parseInt(t,10)
93. 0!==f%32
94. a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8
95. }switch(c){case "HEX":c=function(a,c,d){var g=a.length,l,p,f,m,q,u;if(0!==g%2)throw Error("String of HEX type must be in byte increments");c=c||[0];d=d||0;q=d>>>3;u=-1===b?3:0;for(l=0
96. 1!==b
97. "UTF16LE"!==a
98. "");if(-1!==p
99. function(a,c,d){var g,l,p,f,m,q;c=c||[0];d=d||0;l=d>>>3;m=-1===b?3:0;q=new Uint8Array(a);for(g=0;g<a.byteLength;g+=1)f=g+l,p=f>>>2,c.length<=p&&c.push(0),c[p]|=q[g]<<8*(m+f%4*b);return{value:c,binLen:8*a.byteLength+d}};break;default:throw Error("format must be HEX, TEXT, B64, BYTES, or ARRAYBUFFER");}return c}function y(c,a){return c<<a|c>>>32-a}function S(c,a){return 32<a?(a-=32,new b(c.b<<a|c.a>>>32-a,c.a<<a|c.b>>>32-a)):0!==a?new b(c.a<<a|c.b>>>32-a,c.b<<a|c.a>>>32-a):c}function w(c,a){return c>>>
100. a[7]);return a}function D(c,a){var d,e,h,n,g=[],l=[];if(null!==c)for(e=0
101. define.amd?define(function(){return C}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=C),exports=C):Y.jsSHA=C})(this);
102. return C}):"undefined"!==typeof exports?("undefined"!==typeof module
103. 'use strict';(function(I){function w(c,a,d){var l=0,b=[],g=0,f,n,k,e,h,q,y,p,m=!1,t=[],r=[],u,z=!1;d=d||{};f=d.encoding||"UTF8";u=d.numRounds||1;if(u!==parseInt(u,10)
104. l+=1)g[l]=c[l>>>2]>>>8*(3+l%4*-1)&255;return b}function C(c){var a={outputUpper:!1,b64Pad:"=",shakeLen:-1};c=c||{};a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");
105. if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0;d<f;d+=2){k=parseInt(a.substr(d,2),16);if(isNaN(k))throw Error("String of HEX type contains invalid characters");
106. if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0
107. "");if(-1!==k
108. define.amd?define(function(){return w}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=w),exports=w):I.jsSHA=w})(this);
109. return w}):"undefined"!==typeof exports?("undefined"!==typeof module
110. } else if (typeof exports !== 'undefined') {
111. if (typeof document.mozHidden !== 'undefined') {
112. } else if (typeof document.webkitHidden !== 'undefined') {
113. asNavFor !== null ) {
114. if ( asNavFor !== null
115. if (_.options.infinite !== true) {
116. _.options.responsive !== null) {
117. if (targetBreakpoint !== null) {
118. if (_.activeBreakpoint !== null) {
119. if (targetBreakpoint !== _.activeBreakpoint
120. triggerBreakpoint !== false ) {
121. unevenOffset = (_.slideCount % _.options.slidesToScroll !== 0);
122. _.$dots !== null) {
123. if (filter !== null) {
124. if (_.slideCount % _.options.slidesToScroll !== 0) {
125. if (_.$dots !== null) {
126. if (slideControlIndex !== -1) {
127. _.currentSlide !== 0) {
128. if ($(window).width() !== _.windowWidth) {
129. } else if ( typeof arguments[1] !== 'undefined' ) {
130. if( $.type( _.options.responsive ) !== 'array' ) {
131. if (bodyStyle.WebkitTransition !== undefined
132. bodyStyle.MozTransition !== undefined
133. bodyStyle.msTransition !== undefined) {
134. if (bodyStyle.OTransform !== undefined) {
135. if (bodyStyle.MozTransform !== undefined) {
136. if (bodyStyle.webkitTransform !== undefined) {
137. if (bodyStyle.msTransform !== undefined) {
138. if (bodyStyle.transform !== undefined
139. _.animType !== false) {
140. _.transformsEnabled = _.options.useTransform && (_.animType !== null
141. _.animType !== false);
142. if (dontAnimate !== true
143. if (dontAnimate !== true) {
144. if ( _.touchObject.startX !== _.touchObject.curX ) {
145. event.type.indexOf('mouse') !== -1) {
146. event.originalEvent.touches !== undefined ?
147. touches = event.originalEvent !== undefined ? event.originalEvent.touches : null
148. touches.length !== 1) {
149. _.touchObject.curX = touches !== undefined ? touches[0].pageX : event.clientX
150. _.touchObject.curY = touches !== undefined ? touches[0].pageY : event.clientY
151. if (event.originalEvent !== undefined
152. if (_.touchObject.fingerCount !== 1
153. event.originalEvent.touches !== undefined) {
154. _.touchObject.startX = _.touchObject.curX = touches !== undefined ? touches.pageX : event.clientX
155. _.touchObject.startY = _.touchObject.curY = touches !== undefined ? touches.pageY : event.clientY
156. if (_.$slidesCache !== null) {
157. //if (event.origin !== "http://example.com:8080") return;
158. /*if (contentType !== my_expected_type) {

!= (167 điều kiện duy nhất):
------------------------------------------------------------
1. if (params['locale'] != null
2. } else if (params['locale'] != null
3. if (userLang != null
4. if(value!=null){
5. if (this._idInvoice != null
6. this._idInvoice != 0) {
7. if (this._paymentService.getInvoiceDetail() != null) {
8. dataPassed.body != null) {
9. dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
10. dataPassed.body.themes.border_color != true ? dataPassed.body.themes.border_color : ''
11. if (this._translate.currentLang != language) {
12. } else if (this._b != 18) {
13. if (this.htmlDesc != null
14. if (ua.indexOf('safari') != -1
15. if (_val.value != '') {
16. this.auth_method != null) {
17. if (this.valueDate.length != 3) {
18. if (_formCard.exp_date != null
19. if (this.cardName != null
20. if (this._res_post.links != null
21. this._res_post.links.merchant_return != null
22. this._res_post.links.merchant_return.href != null) {
23. if (this._res_post.authorization != null
24. this._res_post.authorization.links != null
25. this._res_post.authorization.links.approval != null) {
26. this._res_post.links.cancel != null) {
27. this._b != 27
28. this._b != 12
29. this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
30. this._b != 18)
31. if (this._b != 18
32. this._b != 19) {
33. if (this._res.links != null
34. this._res.links.merchant_return != null
35. this._res.links.merchant_return.href != null) {
36. if (!(_formCard.otp != null
37. if (!(_formCard.password != null
38. if (this._inExpDate.length != 3) {
39. if (this._b != 9
40. this._b != 11
41. this._b != 16
42. this._b != 17
43. this._b != 25
44. this._b != 36
45. this._b != 44
46. this._b != 54
47. this._b != 57
48. this._b != 59
49. this._b != 61
50. this._b != 63
51. this._b != 68
52. this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
53. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71'].indexOf(this._b.toString()) != -1) {
54. if (this._res_post.return_url != null) {
55. let userName = _formCard.name != null ? _formCard.name : ''
56. this._res_post.authorization.links.approval != null
57. this._res_post.authorization.links.approval.href != null) {
58. userName = paramUserName != null ? paramUserName : ''
59. this._b != 20
60. this._b != 33
61. this._b != 39
62. this._b != 43
63. this._b != 45
64. this._b != 64
65. this._b != 67)
66. if (params['locale'] != null) {
67. if ('otp' != this._paymentService.getCurrentPage()) {
68. if (!(strInstrument != null
69. if (strInstrument.substring(0, 1) != '^'
70. strInstrument.substr(strInstrument.length - 1) != '$') {
71. if (bankid != null) {
72. _showCardName!=true"
73. } else if (this._res_post.links != null
74. cardNo != null
75. v != null
76. this.c_csc = (!(_val.value != null
77. this._i_csc != null
78. this.requireAvs = this.isAvsCountry = country != undefined
79. this._paymentService.getState() != 'error') {
80. _re.body != null) {
81. this._res_polling.links != null
82. this._res_polling.links.merchant_return != null //
83. } else if (this._res_polling.merchant != null
84. this._res_polling.merchant_invoice_reference != null
85. } else if (this._res_polling.payments != null
86. this._res_polling.links.merchant_return != null//
87. this._res_polling.payments != null
88. if (this._res_polling.payments != null
89. t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
90. this.type.toString().length != 0) {
91. if (count != 1) {
92. if (this._res.merchant != null
93. this._res.merchant_invoice_reference != null) {
94. if (this._res.merchant.address_details != null) {
95. this._res.links != null//
96. } else if (this._res.payments != null
97. this._res.payments[this._res.payments.length - 1].instrument != null
98. this._res.payments[this._res.payments.length - 1].instrument.issuer != null
99. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
100. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
101. this._res.payments[this._res.payments.length - 1].links != null
102. this._res.payments[this._res.payments.length - 1].links.cancel != null
103. this._res.payments[this._res.payments.length - 1].links.cancel.href != null
104. this._res.payments[this._res.payments.length - 1].links.update != null
105. this._res.payments[this._res.payments.length - 1].links.update.href != null) {
106. this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
107. this._res.payments[this._res.payments.length - 1].authorization != null
108. this._res.payments[this._res.payments.length - 1].authorization.links != null) {
109. this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
110. this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
111. if (this._res.payments[this._res.payments.length - 1].authorization != null
112. this._res.payments[this._res.payments.length - 1].authorization.links != null
113. auth = paramUserName != null ? paramUserName : ''
114. this._res.links != null
115. this._res.links.merchant_return != null //
116. } else if (this._res.merchant != null
117. this._res.merchant_invoice_reference != null
118. if (['mastercard', 'visa', 'amex', 'jcb', 'cup', 'card', 'np_card', 'atm'].indexOf(id) != -1) {
119. } else if (['techcombank_account', 'vib_account', 'dongabank_account', 'tpbank_account', 'bidv_account', 'pvcombank_account', 'shb_account', 'oceanbank_online_account'].indexOf(id) != -1) {
120. } else if (['oceanbank_mobile_account'].indexOf(id) != -1) {
121. } else if (['shb_customer_id'].indexOf(id) != -1) {
122. if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1) {
123. return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
124. } else if (this._res.payments != null) {
125. 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {
126. if (appcode != null) {
127. item['feeService']['fee'] != 0"
128. if (_val.value != null
129. this.c_token_otp_csc = !(_val.value != null
130. if (_re.body.authorization != null
131. _re.body.authorization.links != null
132. if (_re.body.links != null
133. _re.body.links.cancel != null) {
134. if (_re.body.return_url != null) {
135. } else if (_re.body.links != null
136. _re.body.links.merchant_return != null
137. _re.body.links.merchant_return.href != null) {
138. if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(item.brand_id) != -1
139. return this._i_token_otp != null
140. || (id != 'amex'
141. if (idInvoice != null
142. idInvoice != 0)
143. idInvoice != 0) {
144. if (this._merchantid != null
145. this._tranref != null
146. this._state != null
147. let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
148. urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
149. if (tem != null) {
150. if ((tem = ua.match(/version\/(\d+)/i)) != null) {
151. if (_modules[r][6] != null) {
152. if (_modules[6][c] != null) {
153. if (_modules[row][col] != null) {
154. while (buffer.getLengthInBits() % 8 != 0) {
155. if (count != numChars) {
156. throw count + ' != ' + numChars
157. while (data != 0) {
158. if (test.length != 2
159. ( (test[0] << 8) | test[1]) != code) {
160. if (_length % 3 != 0) {
161. if ( (data >>> length) != 0) {
162. return typeof _map[key] != 'undefined'
163. var source = arguments[i] != null ? arguments[i] : {
164. $('[draggable!=true]', _.$slideTrack).off('dragstart', _.preventDefault);
165. $('[draggable!=true]', _.$slideTrack).on('dragstart', _.preventDefault);
166. if( direction != 'vertical' ) {
167. if (typeof ret != 'undefined') return ret;

