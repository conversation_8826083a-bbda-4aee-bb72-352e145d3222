====================================================================================================
TRÍCH XUẤT ĐIỀU KIỆN SO SÁNH TỪ CÁC FILE TYPESCRIPT/JAVASCRIPT/HTML
====================================================================================================
Thư mục/File nguồn: /root/projects/onepay/paygate-enetviet/src
Thời gian: 18:12:28 18/8/2025
Tổng số file xử lý: 116
Tổng số file bị bỏ qua: 0
Tổng số điều kiện tìm thấy: 1233

THỐNG KÊ TỔNG QUAN THEO LOẠI TOÁN TỬ:
--------------------------------------------------
Strict equality (===): 231 lần
Loose equality (==): 685 lần
Strict inequality (!==): 51 lần
Loose inequality (!=): 266 lần
--------------------------------------------------

DANH SÁCH FILE ĐÃ XỬ LÝ:
--------------------------------------------------
1. 4en.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/4en.html
2. 4vn.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/4vn.html
3. app-routing.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/app-routing.module.ts
4. app.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/app.component.html
5. app.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/app.component.spec.ts
6. app.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/app.component.ts
7. app.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/app.module.ts
8. counter.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/counter.directive.spec.ts
9. counter.directive.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/counter.directive.ts
10. format-carno-input.derective.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/directives/format-carno-input.derective.ts
11. uppercase-input.directive.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/directives/uppercase-input.directive.ts
12. error.component.html (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/error/error.component.html
13. error.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/error/error.component.spec.ts
14. error.component.ts (28 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/error/error.component.ts
15. support-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/error/support-dialog/support-dialog.html
16. support-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/error/support-dialog/support-dialog.ts
17. format-date.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/format-date.directive.spec.ts
18. format-date.directive.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/format-date.directive.ts
19. main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/main.component.html
20. main.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/main.component.spec.ts
21. main.component.ts (16 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/main.component.ts
22. app-result.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/app-result/app-result.component.html
23. app-result.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/app-result/app-result.component.ts
24. bank-amount-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
25. bank-amount-dialog.component.ts (34 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
26. cancel-dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/cancel-dialog-guide-dialog.html
27. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/confirm-dialog/dialog-guide-dialog.html
28. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/dialog-guide-dialog.html
29. bankaccount.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
30. bankaccount.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
31. bankaccount.component.ts (153 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
32. bank.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/model/bank.ts
33. otp-auth.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
34. otp-auth.component.ts (18 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
35. shb.component.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/shb/shb.component.html
36. shb.component.ts (66 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/shb/shb.component.ts
37. techcombank.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
38. techcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
39. techcombank.component.ts (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
40. vibbank.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.html
41. vibbank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.spec.ts
42. vibbank.component.ts (99 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.ts
43. vietcombank.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
44. vietcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
45. vietcombank.component.ts (109 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
46. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
47. off-bank-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
48. off-bank-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
49. policy-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/dialog/policy-dialog/policy-dialog/policy-dialog.component.html
50. policy-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/dialog/policy-dialog/policy-dialog/policy-dialog.component.ts
51. domescard-main.component.html (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/domescard-main.component.html
52. domescard-main.component.ts (125 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/domescard-main.component.ts
53. dialog-guide-dialog.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/intercard-main/dialog-guide-dialog.html
54. intercard-main.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/intercard-main/intercard-main.component.html
55. intercard-main.component.ts (66 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/intercard-main/intercard-main.component.ts
56. menu.component.html (30 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/menu.component.html
57. menu.component.ts (150 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/menu.component.ts
58. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
59. qr-dialog.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
60. qr-dialog.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
61. qr-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/qr-main/qr-guide-dialog/qr-guide-dialog.html
62. qr-guide-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/qr-main/qr-guide-dialog/qr-guide-dialog.ts
63. qr-main.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/qr-main/qr-main.component.html
64. qr-main.component.ts (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/qr-main/qr-main.component.ts
65. safe-html.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/qr-main/safe-html.pipe.ts
66. queuing.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/queuing/queuing.component.html
67. queuing.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/queuing/queuing.component.ts
68. token-expired-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/token-expired-dialog/token-expired-dialog.component.html
69. token-expired-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/token-expired-dialog/token-expired-dialog.component.ts
70. remove-token-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/token-main/remove-token-dialog/remove-token-dialog.html
71. dialog-guide-dialog.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/token-main/token-dialog/dialog-guide-dialog.html
72. token-main.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/token-main/token-main.component.html
73. token-main.component.ts (70 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/token-main/token-main.component.ts
74. vietqr-guide-mobile.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/vietqr-main/vietqr-guide/mobile/vietqr-guide-mobile.html
75. vietqr-guide-mobile.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/vietqr-main/vietqr-guide/mobile/vietqr-guide-mobile.ts
76. vietqr-guide.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/vietqr-main/vietqr-guide/vietqr-guide.component.html
77. vietqr-guide.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/vietqr-main/vietqr-guide/vietqr-guide.component.ts
78. vietqr-listbank.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/vietqr-main/vietqr-listbank/vietqr-listbank.component.html
79. vietqr-listbank.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/vietqr-main/vietqr-listbank/vietqr-listbank.component.ts
80. vietqr-main.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/vietqr-main/vietqr-main.component.html
81. vietqr-main.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/vietqr-main/vietqr-main.component.ts
82. overlay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/overlay/overlay.component.html
83. overlay.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/overlay/overlay.component.spec.ts
84. overlay.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/overlay/overlay.component.ts
85. bank-amount.pipe.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/pipe/bank-amount.pipe.ts
86. safe-html.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/pipe/safe-html.pipe.ts
87. close-dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/services/close-dialog.service.ts
88. data.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/services/data.service.ts
89. deep_link.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/services/deep_link.service.ts
90. dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/services/dialog.service.ts
91. fee.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/services/fee.service.ts
92. multiple_method.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/services/multiple_method.service.ts
93. payment.service.ts (16 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/services/payment.service.ts
94. qr.service.ts (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/services/qr.service.ts
95. token-main.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/services/token-main.service.ts
96. vietqr.service.ts (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/services/vietqr.service.ts
97. index.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/translate/index.ts
98. lang-en.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/translate/lang-en.ts
99. lang-vi.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/translate/lang-vi.ts
100. translate.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/translate/translate.pipe.ts
101. translate.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/translate/translate.service.ts
102. translations.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/translate/translations.ts
103. apps-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/util/apps-info.ts
104. banks-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/util/banks-info.ts
105. error-handler.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/util/error-handler.ts
106. util.ts (40 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/util/util.ts
107. qrcode.js (67 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/assets/script/qrcode.js
108. environment.dev.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/environments/environment.dev.ts
109. environment.mtf.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/environments/environment.mtf.ts
110. environment.prod.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/environments/environment.prod.ts
111. environment.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/environments/environment.ts
112. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/index.html
113. karma.conf.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/karma.conf.js
114. main.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/main.ts
115. polyfills.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/polyfills.ts
116. test.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-enetviet/src/test.ts

====================================================================================================
CHI TIẾT TỪNG FILE:
====================================================================================================

📁 FILE 1: 4en.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/4en.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 2: 4vn.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/4vn.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 3: app-routing.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/app-routing.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 4: app.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/app.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 5: app.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/app.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 6: app.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/app.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 81] return lang === this._translate.currentLang

== (2 điều kiện):
  1. [Dòng 60] 'vi' == params['locale']) {
  2. [Dòng 62] 'en' == params['locale']) {

!= (3 điều kiện):
  1. [Dòng 60] if (params['locale'] != null
  2. [Dòng 62] } else if (params['locale'] != null
  3. [Dòng 69] if (userLang != null

================================================================================

📁 FILE 7: app.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/app.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 8: counter.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/counter.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 9: counter.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/counter.directive.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 10: format-carno-input.derective.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/directives/format-carno-input.derective.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 44] this.lastValue !== $event.target.value)) {

!= (1 điều kiện):
  1. [Dòng 23] if(value!=null){

================================================================================

📁 FILE 11: uppercase-input.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/directives/uppercase-input.directive.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 0 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 23] this.lastValue !== $event.target.value)) {

================================================================================

📁 FILE 12: error.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/error/error.component.html
📊 Thống kê: 11 điều kiện duy nhất
   - === : 3 lần
   - == : 8 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 72] isPopupSupport === 'True') || (rePayment
  2. [Dòng 73] isPopupSupport === 'True'"
  3. [Dòng 79] isPopupSupport === 'True')">

== (8 điều kiện):
  1. [Dòng 3] errorCode == '11'"
  2. [Dòng 72] <div class="footer-button" *ngIf="((isSent == false
  3. [Dòng 73] isSent == false
  4. [Dòng 79] <div class="payment_again" *ngIf="rePayment && !timeOut" [class.select_only]="!(isSent == false
  5. [Dòng 96] <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
  6. [Dòng 96] (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
  7. [Dòng 98] <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
  8. [Dòng 98] !(errorCode == 'overtime' || errorCode == '253') && isBack && !invoiceNearExpire && !paymentPending

================================================================================

📁 FILE 13: error.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/error/error.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 14: error.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/error/error.component.ts
📊 Thống kê: 28 điều kiện duy nhất
   - === : 8 lần
   - == : 12 lần
   - !== : 0 lần
   - != : 8 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 134] params.timeout === 'true') {
  2. [Dòng 153] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  3. [Dòng 153] _re.body.state === 'unpaid');
  4. [Dòng 234] if (this.errorCode === 'overtime'
  5. [Dòng 234] this.errorCode === '253') {
  6. [Dòng 315] params.name === 'CUSTOMER_INTIME'
  7. [Dòng 315] params.code === '09') {
  8. [Dòng 361] if (this.timeLeft === 0) {

== (12 điều kiện):
  1. [Dòng 208] this.res.themes.theme == 'enetviet') {
  2. [Dòng 214] params.response_code == 'overtime') {
  3. [Dòng 257] if (_re.status == '200'
  4. [Dòng 257] _re.status == '201') {
  5. [Dòng 270] if (_re2.status == '200'
  6. [Dòng 270] _re2.status == '201') {
  7. [Dòng 283] if (this.errorCode == 'overtime'
  8. [Dòng 283] this.errorCode == '253') {
  9. [Dòng 286] } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
  10. [Dòng 291] this.res.state == 'canceled') {
  11. [Dòng 309] if (lastPayment?.state == 'pending') {
  12. [Dòng 359] if (this.isTimePause == false) {

!= (8 điều kiện):
  1. [Dòng 422] if (_re != null
  2. [Dòng 422] _re.links != null
  3. [Dòng 422] _re.links.merchant_return != null
  4. [Dòng 423] _re.links.merchant_return.href != null) {
  5. [Dòng 425] } else if (_re.body != null
  6. [Dòng 425] _re.body.links != null
  7. [Dòng 425] _re.body.links.merchant_return != null
  8. [Dòng 426] _re.body.links.merchant_return.href != null) {

================================================================================

📁 FILE 15: support-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/error/support-dialog/support-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 16: support-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/error/support-dialog/support-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 17: format-date.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/format-date.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 18: format-date.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/format-date.directive.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0
  2. [Dòng 123] YY % 4 === 0)) {
  3. [Dòng 138] if (YYYY % 400 === 0
  4. [Dòng 138] YYYY % 4 === 0)) {

!== (2 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0 || (YY % 100 !== 0
  2. [Dòng 138] if (YYYY % 400 === 0 || (YYYY % 100 !== 0

================================================================================

📁 FILE 19: main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 20: main.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/main.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 21: main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/main.component.ts
📊 Thống kê: 16 điều kiện duy nhất
   - === : 0 lần
   - == : 10 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

== (10 điều kiện):
  1. [Dòng 95] if ((dataPassed.status == '200'
  2. [Dòng 95] dataPassed.status == '201') && dataPassed.body != null) {
  3. [Dòng 99] dataPassed.body.themes.logo_full == 'True') {
  4. [Dòng 124] if (this.type == 5
  5. [Dòng 127] } else if (this.type == 6
  6. [Dòng 130] } else if (this.type == 2
  7. [Dòng 133] } else if (this.type == 7
  8. [Dòng 136] } else if (this.type == 8
  9. [Dòng 139] } else if (this.type == 4
  10. [Dòng 142] } else if (this.type == 3

!= (6 điều kiện):
  1. [Dòng 86] if (this._idInvoice != null
  2. [Dòng 86] this._idInvoice != 0) {
  3. [Dòng 87] if (this._paymentService.getInvoiceDetail() != null) {
  4. [Dòng 95] dataPassed.body != null) {
  5. [Dòng 155] dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
  6. [Dòng 216] if (this._translate.currentLang != language) {

================================================================================

📁 FILE 22: app-result.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/app-result/app-result.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 23: app-result.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/app-result/app-result.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 24: bank-amount-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 25: bank-amount-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
📊 Thống kê: 34 điều kiện duy nhất
   - === : 0 lần
   - == : 34 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (34 điều kiện):
  1. [Dòng 40] if (this.locale == 'en') {
  2. [Dòng 53] if (bankId == 3
  3. [Dòng 53] bankId == 61
  4. [Dòng 54] bankId == 8
  5. [Dòng 54] bankId == 49
  6. [Dòng 55] bankId == 48
  7. [Dòng 56] bankId == 10
  8. [Dòng 56] bankId == 53
  9. [Dòng 57] bankId == 17
  10. [Dòng 57] bankId == 65
  11. [Dòng 58] bankId == 23
  12. [Dòng 58] bankId == 52
  13. [Dòng 59] bankId == 27
  14. [Dòng 59] bankId == 66
  15. [Dòng 60] bankId == 9
  16. [Dòng 60] bankId == 54
  17. [Dòng 61] bankId == 37
  18. [Dòng 62] bankId == 38
  19. [Dòng 63] bankId == 39
  20. [Dòng 64] bankId == 40
  21. [Dòng 65] bankId == 42
  22. [Dòng 66] bankId == 44
  23. [Dòng 67] bankId == 72
  24. [Dòng 68] bankId == 59
  25. [Dòng 71] bankId == 51
  26. [Dòng 72] bankId == 64
  27. [Dòng 73] bankId == 58
  28. [Dòng 74] bankId == 56
  29. [Dòng 77] bankId == 55
  30. [Dòng 78] bankId == 60
  31. [Dòng 79] bankId == 68
  32. [Dòng 80] bankId == 73
  33. [Dòng 81] bankId == 74
  34. [Dòng 82] bankId == 75 //Keb Hana

================================================================================

📁 FILE 26: cancel-dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/cancel-dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 27: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/confirm-dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 28: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 29: bankaccount.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 3 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 40] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 55] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 55] valueDate.trim().length === 0)"

== (1 điều kiện):
  1. [Dòng 91] token_site == 'onepay'

================================================================================

📁 FILE 30: bankaccount.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 31: bankaccount.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
📊 Thống kê: 153 điều kiện duy nhất
   - === : 48 lần
   - == : 71 lần
   - !== : 13 lần
   - != : 21 lần
--------------------------------------------------------------------------------

=== (48 điều kiện):
  1. [Dòng 114] if (target.tagName === 'A'
  2. [Dòng 142] if (isIE[0] === 'MSIE'
  3. [Dòng 142] +isIE[1] === 10) {
  4. [Dòng 232] if ((_val.value.substr(-1) === ' '
  5. [Dòng 232] _val.value.length === 24) {
  6. [Dòng 242] if (this.cardTypeBank === 'bank_card_number') {
  7. [Dòng 247] } else if (this.cardTypeBank === 'bank_account_number') {
  8. [Dòng 253] } else if (this.cardTypeBank === 'bank_username') {
  9. [Dòng 257] } else if (this.cardTypeBank === 'bank_customer_code') {
  10. [Dòng 263] this.cardTypeBank === 'bank_card_number'
  11. [Dòng 277] if (this.cardTypeOcean === 'IB') {
  12. [Dòng 281] } else if (this.cardTypeOcean === 'MB') {
  13. [Dòng 282] if (_val.value.substr(0, 2) === '84') {
  14. [Dòng 289] } else if (this.cardTypeOcean === 'ATM') {
  15. [Dòng 316] if (this.cardTypeBank === 'bank_account_number') {
  16. [Dòng 335] this.cardTypeBank === 'bank_card_number') {
  17. [Dòng 357] if (this.cardTypeBank === 'bank_card_number'
  18. [Dòng 357] if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  19. [Dòng 678] if (event.keyCode === 8
  20. [Dòng 678] event.key === "Backspace"
  21. [Dòng 718] if (v.length === 2
  22. [Dòng 718] this.flag.length === 3
  23. [Dòng 718] this.flag.charAt(this.flag.length - 1) === '/') {
  24. [Dòng 722] if (v.length === 1) {
  25. [Dòng 724] } else if (v.length === 2) {
  26. [Dòng 727] v.length === 2) {
  27. [Dòng 735] if (len === 2) {
  28. [Dòng 1007] if ((this.cardTypeBank === 'bank_account_number'
  29. [Dòng 1007] this.cardTypeBank === 'bank_username'
  30. [Dòng 1007] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  31. [Dòng 1012] this.cardTypeOcean === 'ATM')
  32. [Dòng 1013] || (this.cardTypeOcean === 'IB'
  33. [Dòng 1072] if (valIn === this._translate.instant('bank_card_number')) {
  34. [Dòng 1097] } else if (valIn === this._translate.instant('bank_account_number')) {
  35. [Dòng 1116] } else if (valIn === this._translate.instant('bank_username')) {
  36. [Dòng 1132] } else if (valIn === this._translate.instant('bank_customer_code')) {
  37. [Dòng 1220] if (_val.value === ''
  38. [Dòng 1220] _val.value === null
  39. [Dòng 1220] _val.value === undefined) {
  40. [Dòng 1229] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  41. [Dòng 1229] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  42. [Dòng 1236] this.cardTypeOcean === 'MB') {
  43. [Dòng 1244] this.cardTypeOcean === 'IB'
  44. [Dòng 1250] if ((this.cardTypeBank === 'bank_card_number'
  45. [Dòng 1282] if (this.cardName === undefined
  46. [Dòng 1282] this.cardName === '') {
  47. [Dòng 1290] if (this.valueDate === undefined
  48. [Dòng 1290] this.valueDate === '') {

== (71 điều kiện):
  1. [Dòng 133] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 156] if (this._b == 18
  3. [Dòng 156] this._b == 19) {
  4. [Dòng 159] if (this._b == 19) {//19BIDV
  5. [Dòng 167] } else if (this._b == 3
  6. [Dòng 167] this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  7. [Dòng 172] if (this._b == 27) {
  8. [Dòng 177] } else if (this._b == 12) {// 12SHB
  9. [Dòng 182] } else if (this._b == 18) { //18Oceanbank-ocb
  10. [Dòng 241] if (this._b == 19
  11. [Dòng 241] this._b == 3
  12. [Dòng 241] this._b == 27
  13. [Dòng 241] this._b == 12) {
  14. [Dòng 276] } else if (this._b == 18) {
  15. [Dòng 307] if (this.checkBin(_val.value) && (this._b == 3
  16. [Dòng 307] this._b == 27)) {
  17. [Dòng 312] if (this._b == 3) {
  18. [Dòng 324] this.cardTypeOcean == 'ATM') {
  19. [Dòng 337] if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  20. [Dòng 357] this._b == 18)) {
  21. [Dòng 433] if (this.checkBin(v) && (this._b == 3
  22. [Dòng 678] event.inputType == 'deleteContentBackward') {
  23. [Dòng 679] if (event.target.name == 'exp_date'
  24. [Dòng 687] event.inputType == 'insertCompositionText') {
  25. [Dòng 702] if (((this.valueDate.length == 4
  26. [Dòng 702] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  27. [Dòng 702] this.valueDate.length == 5)
  28. [Dòng 782] if (temp.length == 0) {
  29. [Dòng 789] return (counter % 10 == 0);
  30. [Dòng 809] } else if (this._b == 19) {
  31. [Dòng 811] } else if (this._b == 27) {
  32. [Dòng 816] if (this._b == 12) {
  33. [Dòng 818] if (this.cardTypeBank == 'bank_customer_code') {
  34. [Dòng 820] } else if (this.cardTypeBank == 'bank_account_number') {
  35. [Dòng 837] _formCard.exp_date.length == 5
  36. [Dòng 837] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
  37. [Dòng 837] this._b == 3)) {//27-pvcombank;3-TPB ;
  38. [Dòng 842] if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
  39. [Dòng 842] this._b == 19
  40. [Dòng 842] this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
  41. [Dòng 845] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  42. [Dòng 848] if (this.cardTypeOcean == 'IB') {
  43. [Dòng 850] } else if (this.cardTypeOcean == 'MB') {
  44. [Dòng 852] } else if (this.cardTypeOcean == 'ATM') {
  45. [Dòng 882] this.token_site == 'onepay'
  46. [Dòng 901] if (_re.status == '200'
  47. [Dòng 901] _re.status == '201') {
  48. [Dòng 906] if (this._res_post.state == 'approved'
  49. [Dòng 906] this._res_post.state == 'failed') {
  50. [Dòng 913] } else if (this._res_post.state == 'authorization_required') {
  51. [Dòng 931] if (this._b == 18) {
  52. [Dòng 936] if (this._b == 27
  53. [Dòng 936] this._b == 18) {
  54. [Dòng 1027] if ((cardNo.length == 16
  55. [Dòng 1027] if ((cardNo.length == 16 || (cardNo.length == 19
  56. [Dòng 1028] && ((this._b == 18
  57. [Dòng 1028] cardNo.length == 19) || this._b != 18)
  58. [Dòng 1041] if (this._b == +e.id) {
  59. [Dòng 1057] if (valIn == 1) {
  60. [Dòng 1059] } else if (valIn == 2) {
  61. [Dòng 1083] this._b == 3) {
  62. [Dòng 1090] if (this._b == 19) {
  63. [Dòng 1153] if (cardType == this._translate.instant('internetbanking')
  64. [Dòng 1161] } else if (cardType == this._translate.instant('mobilebanking')
  65. [Dòng 1169] } else if (cardType == this._translate.instant('atm')
  66. [Dòng 1229] this._b == 18))) {
  67. [Dòng 1236] } else if (this._b == 18
  68. [Dòng 1261] this.c_expdate = !(((this.valueDate.length == 4
  69. [Dòng 1293] this.valueDate.length == 4
  70. [Dòng 1293] this.valueDate.search('/') == -1)
  71. [Dòng 1294] this.valueDate.length == 5))

!== (13 điều kiện):
  1. [Dòng 232] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 887] key !== '3') {
  3. [Dòng 937] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 955] codeResponse.toString() !== '0') {
  5. [Dòng 1007] cardNo.length !== 0) {
  6. [Dòng 1079] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 1100] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 1121] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 1141] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 1153] this.lb_card_account !== this._translate.instant('ocb_account')) {
  11. [Dòng 1161] this.lb_card_account !== this._translate.instant('ocb_phone')) {
  12. [Dòng 1169] this.lb_card_account !== this._translate.instant('ocb_card')) {
  13. [Dòng 1250] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (21 điều kiện):
  1. [Dòng 187] } else if (this._b != 18) {
  2. [Dòng 193] if (this.htmlDesc != null
  3. [Dòng 229] if (ua.indexOf('safari') != -1
  4. [Dòng 239] if (_val.value != '') {
  5. [Dòng 325] this.auth_method != null) {
  6. [Dòng 680] if (this.valueDate.length != 3) {
  7. [Dòng 837] if (_formCard.exp_date != null
  8. [Dòng 842] if (this.cardName != null
  9. [Dòng 909] if (this._res_post.links != null
  10. [Dòng 909] this._res_post.links.merchant_return != null
  11. [Dòng 909] this._res_post.links.merchant_return.href != null) {
  12. [Dòng 917] if (this._res_post.authorization != null
  13. [Dòng 917] this._res_post.authorization.links != null
  14. [Dòng 917] this._res_post.authorization.links.approval != null) {
  15. [Dòng 924] this._res_post.links.cancel != null) {
  16. [Dòng 1027] this._b != 27
  17. [Dòng 1027] this._b != 12
  18. [Dòng 1027] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  19. [Dòng 1028] this._b != 18)
  20. [Dòng 1074] if (this._b != 18
  21. [Dòng 1074] this._b != 19) {

================================================================================

📁 FILE 32: bank.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/model/bank.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 33: otp-auth.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 34: otp-auth.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
📊 Thống kê: 18 điều kiện duy nhất
   - === : 0 lần
   - == : 11 lần
   - !== : 1 lần
   - != : 6 lần
--------------------------------------------------------------------------------

== (11 điều kiện):
  1. [Dòng 95] if (this._b == 8) {//MB Bank
  2. [Dòng 99] if (this._b == 18) {//Oceanbank
  3. [Dòng 140] if (this._b == 8) {
  4. [Dòng 145] if (this._b == 18) {
  5. [Dòng 150] if (this._b == 12) { //SHB
  6. [Dòng 171] if (_re.status == '200'
  7. [Dòng 171] _re.status == '201') {
  8. [Dòng 180] } else if (this._res.state == 'authorization_required') {
  9. [Dòng 212] if (this.challengeCode == '') {
  10. [Dòng 304] if (this._b == 12) {
  11. [Dòng 353] if (this._b == 18) {//8-MB Bank;18-oceanbank

!== (1 điều kiện):
  1. [Dòng 186] codeResponse.toString() !== '0') {

!= (6 điều kiện):
  1. [Dòng 176] if (this._res.links != null
  2. [Dòng 176] this._res.links.merchant_return != null
  3. [Dòng 176] this._res.links.merchant_return.href != null) {
  4. [Dòng 348] if (!(_formCard.otp != null
  5. [Dòng 354] if (!(_formCard.password != null
  6. [Dòng 370] if (ua.indexOf('safari') != -1

================================================================================

📁 FILE 35: shb.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/shb/shb.component.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 3 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 25] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 42] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  3. [Dòng 42] _inExpDate.trim().length === 0)"

== (3 điều kiện):
  1. [Dòng 22] <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_card_number')">
  2. [Dòng 66] <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_account_number')">
  3. [Dòng 94] token_site == 'onepay'

================================================================================

📁 FILE 36: shb.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/shb/shb.component.ts
📊 Thống kê: 66 điều kiện duy nhất
   - === : 18 lần
   - == : 32 lần
   - !== : 5 lần
   - != : 11 lần
--------------------------------------------------------------------------------

=== (18 điều kiện):
  1. [Dòng 98] if (target.tagName === 'A'
  2. [Dòng 126] if (isIE[0] === 'MSIE'
  3. [Dòng 126] +isIE[1] === 10) {
  4. [Dòng 168] if (focusElement === 'card_name') {
  5. [Dòng 170] } else if (focusElement === 'exp_date'
  6. [Dòng 191] focusExpDateElement === 'card_name') {
  7. [Dòng 410] if (this.cardTypeBank === 'bank_account_number'
  8. [Dòng 455] if (valIn === this._translate.instant('bank_card_number')) {
  9. [Dòng 461] } else if (valIn === this._translate.instant('bank_account_number')) {
  10. [Dòng 503] if (_val.value === ''
  11. [Dòng 503] _val.value === null
  12. [Dòng 503] _val.value === undefined) {
  13. [Dòng 514] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  14. [Dòng 514] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  15. [Dòng 521] this.cardTypeOcean === 'MB') {
  16. [Dòng 529] this.cardTypeOcean === 'IB'
  17. [Dòng 535] if ((this.cardTypeBank === 'bank_card_number'
  18. [Dòng 558] if (this.cardTypeOcean === 'IB') {

== (32 điều kiện):
  1. [Dòng 117] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 135] if(this._b == 12) this.isShbGroup = true;
  3. [Dòng 156] return this._b == 9
  4. [Dòng 156] this._b == 11
  5. [Dòng 156] this._b == 16
  6. [Dòng 156] this._b == 17
  7. [Dòng 156] this._b == 25
  8. [Dòng 156] this._b == 44
  9. [Dòng 157] this._b == 57
  10. [Dòng 157] this._b == 59
  11. [Dòng 157] this._b == 61
  12. [Dòng 157] this._b == 63
  13. [Dòng 157] this._b == 69
  14. [Dòng 239] if (this.cardTypeBank == 'bank_account_number') {
  15. [Dòng 254] _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
  16. [Dòng 286] this.token_site == 'onepay'
  17. [Dòng 303] if (_re.status == '200'
  18. [Dòng 303] _re.status == '201') {
  19. [Dòng 307] if (this._res_post.state == 'approved'
  20. [Dòng 307] this._res_post.state == 'failed') {
  21. [Dòng 313] } else if (this._res_post.state == 'authorization_required') {
  22. [Dòng 413] if ((cardNo.length == 16
  23. [Dòng 413] cardNo.length == 19) && cardNo.startsWith("970443") && this._util.checkMod10(cardNo) && this.checkBin(cardNo) ) {
  24. [Dòng 425] if (this._b == +e.id) {
  25. [Dòng 441] if (valIn == 1) {
  26. [Dòng 443] } else if (valIn == 2) {
  27. [Dòng 514] this._b == 18))) {
  28. [Dòng 521] } else if (this._b == 18
  29. [Dòng 535] this._b == 18)) {
  30. [Dòng 547] this.c_expdate = !(((this.valueDate.length == 4
  31. [Dòng 547] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  32. [Dòng 547] this.valueDate.length == 5)

!== (5 điều kiện):
  1. [Dòng 291] key !== '3') {
  2. [Dòng 333] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  3. [Dòng 351] codeResponse.toString() !== '0') {
  4. [Dòng 410] cardNo.length !== 0) {
  5. [Dòng 535] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (11 điều kiện):
  1. [Dòng 143] if (this.htmlDesc != null
  2. [Dòng 204] if (ua.indexOf('safari') != -1
  3. [Dòng 254] if (_formCard.exp_date != null
  4. [Dòng 259] if (this.cardName != null
  5. [Dòng 310] if (this._res_post.links != null
  6. [Dòng 310] this._res_post.links.merchant_return != null
  7. [Dòng 310] this._res_post.links.merchant_return.href != null) {
  8. [Dòng 316] if (this._res_post.authorization != null
  9. [Dòng 316] this._res_post.authorization.links != null
  10. [Dòng 316] this._res_post.authorization.links.approval != null) {
  11. [Dòng 323] this._res_post.links.cancel != null) {

================================================================================

📁 FILE 37: techcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 38: techcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 39: techcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
📊 Thống kê: 14 điều kiện duy nhất
   - === : 1 lần
   - == : 10 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 69] if (this.timeLeft === 0) {

== (10 điều kiện):
  1. [Dòng 60] if (this._b == 2
  2. [Dòng 60] this._b == 31) {
  3. [Dòng 99] if (this._b == 2) {
  4. [Dòng 101] } else if (this._b == 6) {
  5. [Dòng 103] } else if (this._b == 31) {
  6. [Dòng 133] if (_re.status == '200'
  7. [Dòng 133] _re.status == '201') {
  8. [Dòng 138] if (this._res_post.state == 'approved'
  9. [Dòng 138] this._res_post.state == 'failed') {
  10. [Dòng 142] } else if (this._res_post.state == 'authorization_required') {

!= (3 điều kiện):
  1. [Dòng 146] if (this._res_post.authorization != null
  2. [Dòng 146] this._res_post.authorization.links != null
  3. [Dòng 146] this._res_post.authorization.links.approval != null) {

================================================================================

📁 FILE 40: vibbank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 3 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 28] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 42] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 42] valueDate.trim().length === 0)"

== (1 điều kiện):
  1. [Dòng 68] token_site == 'onepay'

================================================================================

📁 FILE 41: vibbank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 42: vibbank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.ts
📊 Thống kê: 99 điều kiện duy nhất
   - === : 37 lần
   - == : 35 lần
   - !== : 10 lần
   - != : 17 lần
--------------------------------------------------------------------------------

=== (37 điều kiện):
  1. [Dòng 117] if (target.tagName === 'A'
  2. [Dòng 146] if (isIE[0] === 'MSIE'
  3. [Dòng 146] +isIE[1] === 10) {
  4. [Dòng 177] if (this.timeLeft === 0) {
  5. [Dòng 227] if ((_val.value.substr(-1) === ' '
  6. [Dòng 227] _val.value.length === 24) {
  7. [Dòng 237] if (this.cardTypeBank === 'bank_card_number') {
  8. [Dòng 242] } else if (this.cardTypeBank === 'bank_account_number') {
  9. [Dòng 248] } else if (this.cardTypeBank === 'bank_username') {
  10. [Dòng 252] } else if (this.cardTypeBank === 'bank_customer_code') {
  11. [Dòng 273] if (this.cardTypeBank === 'bank_account_number') {
  12. [Dòng 284] this.cardTypeBank === 'bank_card_number') {
  13. [Dòng 524] if (event.keyCode === 8
  14. [Dòng 524] event.key === "Backspace"
  15. [Dòng 564] if (v.length === 2
  16. [Dòng 564] this.flag.length === 3
  17. [Dòng 564] this.flag.charAt(this.flag.length - 1) === '/') {
  18. [Dòng 568] if (v.length === 1) {
  19. [Dòng 570] } else if (v.length === 2) {
  20. [Dòng 573] v.length === 2) {
  21. [Dòng 581] if (len === 2) {
  22. [Dòng 823] if ((this.cardTypeBank === 'bank_account_number'
  23. [Dòng 823] this.cardTypeBank === 'bank_username'
  24. [Dòng 823] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  25. [Dòng 873] if (valIn === this._translate.instant('bank_card_number')) {
  26. [Dòng 892] } else if (valIn === this._translate.instant('bank_account_number')) {
  27. [Dòng 904] } else if (valIn === this._translate.instant('bank_username')) {
  28. [Dòng 915] } else if (valIn === this._translate.instant('bank_customer_code')) {
  29. [Dòng 972] if (_val.value === ''
  30. [Dòng 972] _val.value === null
  31. [Dòng 972] _val.value === undefined) {
  32. [Dòng 983] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  33. [Dòng 993] if ((this.cardTypeBank === 'bank_card_number'
  34. [Dòng 1025] if (this.cardName === undefined
  35. [Dòng 1025] this.cardName === '') {
  36. [Dòng 1033] if (this.valueDate === undefined
  37. [Dòng 1033] this.valueDate === '') {

== (35 điều kiện):
  1. [Dòng 137] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 160] if (this._b == 5) {//5-vib;
  3. [Dòng 236] if (this._b == 5) {
  4. [Dòng 270] if (this.checkBin(_val.value) && (this._b == 5)) {
  5. [Dòng 286] if (this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  6. [Dòng 348] if (this.checkBin(v) && (this._b == 5)) {
  7. [Dòng 524] event.inputType == 'deleteContentBackward') {
  8. [Dòng 525] if (event.target.name == 'exp_date'
  9. [Dòng 533] event.inputType == 'insertCompositionText') {
  10. [Dòng 548] if (((this.valueDate.length == 4
  11. [Dòng 548] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  12. [Dòng 548] this.valueDate.length == 5)
  13. [Dòng 628] if (temp.length == 0) {
  14. [Dòng 635] return (counter % 10 == 0);
  15. [Dòng 666] _formCard.exp_date.length == 5
  16. [Dòng 666] this._b == 5) {//5 vib ;
  17. [Dòng 671] this._b == 5) {//5vib;
  18. [Dòng 700] this.token_site == 'onepay'
  19. [Dòng 719] if (_re.status == '200'
  20. [Dòng 719] _re.status == '201') {
  21. [Dòng 724] if (this._res_post.state == 'approved'
  22. [Dòng 724] this._res_post.state == 'failed') {
  23. [Dòng 731] } else if (this._res_post.state == 'authorization_required') {
  24. [Dòng 828] if ((cardNo.length == 16
  25. [Dòng 828] if ((cardNo.length == 16 || (cardNo.length == 19
  26. [Dòng 829] && ((this._b == 18
  27. [Dòng 829] cardNo.length == 19) || this._b != 18)
  28. [Dòng 842] if (this._b == +e.id) {
  29. [Dòng 858] if (valIn == 1) {
  30. [Dòng 860] } else if (valIn == 2) {
  31. [Dòng 983] this._b == 18)) {
  32. [Dòng 1005] this.c_expdate = !(((this.valueDate.length == 4
  33. [Dòng 1036] this.valueDate.length == 4
  34. [Dòng 1036] this.valueDate.search('/') == -1)
  35. [Dòng 1037] this.valueDate.length == 5))

!== (10 điều kiện):
  1. [Dòng 227] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 705] key !== '3') {
  3. [Dòng 753] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 770] codeResponse.toString() !== '0') {
  5. [Dòng 823] cardNo.length !== 0) {
  6. [Dòng 880] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 895] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 909] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 922] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 993] this._b !== 18) || (this._b == 18)) {

!= (17 điều kiện):
  1. [Dòng 187] if (this.htmlDesc != null
  2. [Dòng 224] if (ua.indexOf('safari') != -1
  3. [Dòng 234] if (_val.value != '') {
  4. [Dòng 526] if (this.valueDate.length != 3) {
  5. [Dòng 666] if (_formCard.exp_date != null
  6. [Dòng 671] if (this.cardName != null
  7. [Dòng 727] if (this._res_post.links != null
  8. [Dòng 727] this._res_post.links.merchant_return != null
  9. [Dòng 727] this._res_post.links.merchant_return.href != null) {
  10. [Dòng 735] if (this._res_post.authorization != null
  11. [Dòng 735] this._res_post.authorization.links != null
  12. [Dòng 735] this._res_post.authorization.links.approval != null) {
  13. [Dòng 742] this._res_post.links.cancel != null) {
  14. [Dòng 828] this._b != 27
  15. [Dòng 828] this._b != 12
  16. [Dòng 828] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  17. [Dòng 829] this._b != 18)

================================================================================

📁 FILE 43: vietcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 27] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  2. [Dòng 27] _inExpDate.trim().length === 0)"

== (2 điều kiện):
  1. [Dòng 53] token_site == 'onepay'
  2. [Dòng 63] _b == 68"

================================================================================

📁 FILE 44: vietcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 45: vietcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
📊 Thống kê: 109 điều kiện duy nhất
   - === : 5 lần
   - == : 63 lần
   - !== : 2 lần
   - != : 39 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 87] if (target.tagName === 'A'
  2. [Dòng 223] if (event.keyCode === 8
  3. [Dòng 223] event.key === "Backspace"
  4. [Dòng 463] if (approval.method === 'REDIRECT') {
  5. [Dòng 466] } else if (approval.method === 'POST_REDIRECT') {

== (63 điều kiện):
  1. [Dòng 107] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 123] if (this._b == 11
  3. [Dòng 123] this._b == 20
  4. [Dòng 123] this._b == 33
  5. [Dòng 123] this._b == 39
  6. [Dòng 123] this._b == 43
  7. [Dòng 123] this._b == 45
  8. [Dòng 123] this._b == 67
  9. [Dòng 123] this._b == 64
  10. [Dòng 123] this._b == 68
  11. [Dòng 123] this._b == 72
  12. [Dòng 123] this._b == 73
  13. [Dòng 123] this._b == 36
  14. [Dòng 123] this._b == 74
  15. [Dòng 123] this._b == 75) {//seabank
  16. [Dòng 130] if (this._b == 1
  17. [Dòng 130] this._b == 55
  18. [Dòng 130] this._b == 47
  19. [Dòng 130] this._b == 48
  20. [Dòng 130] this._b == 59) {
  21. [Dòng 150] return this._b == 9
  22. [Dòng 150] this._b == 16
  23. [Dòng 150] this._b == 17
  24. [Dòng 150] this._b == 25
  25. [Dòng 150] this._b == 44
  26. [Dòng 151] this._b == 57
  27. [Dòng 151] this._b == 59
  28. [Dòng 151] this._b == 61
  29. [Dòng 151] this._b == 63
  30. [Dòng 151] this._b == 69
  31. [Dòng 223] event.inputType == 'deleteContentBackward') {
  32. [Dòng 224] if (event.target.name == 'exp_date'
  33. [Dòng 232] event.inputType == 'insertCompositionText') {
  34. [Dòng 341] this.token_site == 'onepay'
  35. [Dòng 355] if (this._res_post.state == 'approved'
  36. [Dòng 355] this._res_post.state == 'failed') {
  37. [Dòng 403] } else if (this._res_post.state == 'authorization_required') {
  38. [Dòng 425] this._b == 14
  39. [Dòng 425] this._b == 15
  40. [Dòng 425] this._b == 24
  41. [Dòng 425] this._b == 8
  42. [Dòng 425] this._b == 10
  43. [Dòng 425] this._b == 22
  44. [Dòng 425] this._b == 23
  45. [Dòng 425] this._b == 30
  46. [Dòng 425] this._b == 11
  47. [Dòng 425] this._b == 9) {
  48. [Dòng 503] if ((cardNo.length == 16
  49. [Dòng 504] (cardNo.length == 19
  50. [Dòng 504] (cardNo.length == 19 && (this._b == 1
  51. [Dòng 504] this._b == 4
  52. [Dòng 504] this._b == 59))
  53. [Dòng 506] this._util.checkMod10(cardNo) == true
  54. [Dòng 542] return ((value.length == 4
  55. [Dòng 542] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  56. [Dòng 542] value.length == 5) && parseInt(value.split('/')[0]
  57. [Dòng 546] || (this._util.checkValidExpireMonthYear(value) && (this._b == 11
  58. [Dòng 547] this._b == 75)))
  59. [Dòng 578] this._inExpDate.length == 4
  60. [Dòng 578] this._inExpDate.search('/') == -1)
  61. [Dòng 579] this._inExpDate.length == 5))
  62. [Dòng 582] || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 11
  63. [Dòng 583] this._b == 75)));

!== (2 điều kiện):
  1. [Dòng 368] codeResponse.toString() !== '0') {
  2. [Dòng 426] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (39 điều kiện):
  1. [Dòng 135] if (this.htmlDesc != null
  2. [Dòng 160] if (ua.indexOf('safari') != -1
  3. [Dòng 225] if (this._inExpDate.length != 3) {
  4. [Dòng 305] if (this._b != 9
  5. [Dòng 305] this._b != 16
  6. [Dòng 305] this._b != 17
  7. [Dòng 305] this._b != 25
  8. [Dòng 305] this._b != 44
  9. [Dòng 306] this._b != 57
  10. [Dòng 306] this._b != 59
  11. [Dòng 306] this._b != 61
  12. [Dòng 306] this._b != 63
  13. [Dòng 306] this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
  14. [Dòng 319] '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73','74','75'].indexOf(this._b.toString()) != -1) {
  15. [Dòng 357] if (this._res_post.return_url != null) {
  16. [Dòng 360] if (this._res_post.links != null
  17. [Dòng 360] this._res_post.links.merchant_return != null
  18. [Dòng 360] this._res_post.links.merchant_return.href != null) {
  19. [Dòng 408] if (this._res_post.authorization != null
  20. [Dòng 408] this._res_post.authorization.links != null
  21. [Dòng 413] this._res_post.links.cancel != null) {
  22. [Dòng 419] let userName = _formCard.name != null ? _formCard.name : ''
  23. [Dòng 420] this._res_post.authorization.links.approval != null
  24. [Dòng 420] this._res_post.authorization.links.approval.href != null) {
  25. [Dòng 423] userName = paramUserName != null ? paramUserName : ''
  26. [Dòng 544] this._b != 11
  27. [Dòng 544] this._b != 20
  28. [Dòng 544] this._b != 33
  29. [Dòng 544] this._b != 39
  30. [Dòng 545] this._b != 43
  31. [Dòng 545] this._b != 45
  32. [Dòng 545] this._b != 64
  33. [Dòng 545] this._b != 67
  34. [Dòng 545] this._b != 68
  35. [Dòng 545] this._b != 72
  36. [Dòng 545] this._b != 73
  37. [Dòng 545] this._b != 36
  38. [Dòng 545] this._b != 74
  39. [Dòng 545] this._b != 75)

================================================================================

📁 FILE 46: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 47: off-bank-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 48: off-bank-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 49: policy-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/dialog/policy-dialog/policy-dialog/policy-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 50: policy-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/dialog/policy-dialog/policy-dialog/policy-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 51: domescard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/domescard-main.component.html
📊 Thống kê: 8 điều kiện duy nhất
   - === : 1 lần
   - == : 7 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 16] filteredData.length === 0"

== (7 điều kiện):
  1. [Dòng 24] ((!token && _auth==0 && vietcombankGroupSelected) || (token && _b==16))
  2. [Dòng 24] _b==16))">
  3. [Dòng 29] !token && _auth==0 && techcombankGroupSelected
  4. [Dòng 33] !token && _auth==0 && bankaccountGroupSelected
  5. [Dòng 38] !token && _auth==0 && vibbankGroupSelected
  6. [Dòng 45] !token && _auth==0 && shbGroupSelected
  7. [Dòng 50] (token || _auth==1) && _b != 16

================================================================================

📁 FILE 52: domescard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/domescard-main/domescard-main.component.ts
📊 Thống kê: 125 điều kiện duy nhất
   - === : 21 lần
   - == : 96 lần
   - !== : 1 lần
   - != : 7 lần
--------------------------------------------------------------------------------

=== (21 điều kiện):
  1. [Dòng 257] if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
  2. [Dòng 258] filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
  3. [Dòng 299] if (valOut === 'auth') {
  4. [Dòng 452] if (this._b === '1'
  5. [Dòng 452] this._b === '20'
  6. [Dòng 452] this._b === '64') {
  7. [Dòng 455] if (this._b === '36'
  8. [Dòng 455] this._b === '18'
  9. [Dòng 455] this._b === '19'
  10. [Dòng 458] if (this._b === '19'
  11. [Dòng 458] this._b === '16'
  12. [Dòng 458] this._b === '25'
  13. [Dòng 458] this._b === '33'
  14. [Dòng 459] this._b === '39'
  15. [Dòng 459] this._b === '11'
  16. [Dòng 459] this._b === '17'
  17. [Dòng 460] this._b === '36'
  18. [Dòng 460] this._b === '44'
  19. [Dòng 461] this._b === '64'
  20. [Dòng 464] if (this._b === '20'
  21. [Dòng 467] if (this._b === '18') {

== (96 điều kiện):
  1. [Dòng 171] this._auth == 0
  2. [Dòng 171] this.tokenList.length == 0) {
  3. [Dòng 246] this.filteredData.length == 1
  4. [Dòng 280] $event == 'true') {
  5. [Dòng 370] if (bankId==1
  6. [Dòng 370] bankId==4
  7. [Dòng 370] bankId==7
  8. [Dòng 370] bankId==8
  9. [Dòng 370] bankId==9
  10. [Dòng 370] bankId==10
  11. [Dòng 370] bankId==11
  12. [Dòng 370] bankId==14
  13. [Dòng 370] bankId==15
  14. [Dòng 371] bankId==16
  15. [Dòng 371] bankId==17
  16. [Dòng 371] bankId==20
  17. [Dòng 371] bankId==22
  18. [Dòng 371] bankId==23
  19. [Dòng 371] bankId==24
  20. [Dòng 371] bankId==25
  21. [Dòng 371] bankId==30
  22. [Dòng 371] bankId==33
  23. [Dòng 372] bankId==34
  24. [Dòng 372] bankId==35
  25. [Dòng 372] bankId==36
  26. [Dòng 372] bankId==37
  27. [Dòng 372] bankId==38
  28. [Dòng 372] bankId==39
  29. [Dòng 372] bankId==40
  30. [Dòng 372] bankId==41
  31. [Dòng 372] bankId==42
  32. [Dòng 373] bankId==43
  33. [Dòng 373] bankId==44
  34. [Dòng 373] bankId==45
  35. [Dòng 373] bankId==46
  36. [Dòng 373] bankId==47
  37. [Dòng 373] bankId==48
  38. [Dòng 373] bankId==49
  39. [Dòng 373] bankId==50
  40. [Dòng 373] bankId==51
  41. [Dòng 374] bankId==52
  42. [Dòng 374] bankId==53
  43. [Dòng 374] bankId==54
  44. [Dòng 374] bankId==55
  45. [Dòng 374] bankId==56
  46. [Dòng 374] bankId==57
  47. [Dòng 374] bankId==58
  48. [Dòng 374] bankId==59
  49. [Dòng 374] bankId==60
  50. [Dòng 375] bankId==61
  51. [Dòng 375] bankId==62
  52. [Dòng 375] bankId==63
  53. [Dòng 375] bankId==64
  54. [Dòng 375] bankId==65
  55. [Dòng 375] bankId==66
  56. [Dòng 375] bankId==67
  57. [Dòng 375] bankId==68
  58. [Dòng 375] bankId==69
  59. [Dòng 375] bankId==70
  60. [Dòng 376] bankId==71
  61. [Dòng 376] bankId==72
  62. [Dòng 376] bankId==73
  63. [Dòng 376] bankId==32
  64. [Dòng 376] bankId==74
  65. [Dòng 376] bankId==75) {
  66. [Dòng 378] } else if (bankId == 6
  67. [Dòng 378] bankId == 31
  68. [Dòng 378] bankId == 80
  69. [Dòng 378] bankId == 2) {
  70. [Dòng 380] } else if (bankId==3
  71. [Dòng 380] bankId==18
  72. [Dòng 380] bankId==19
  73. [Dòng 380] bankId==27) {
  74. [Dòng 382] } else if (bankId==5) {
  75. [Dòng 384] } else if (bankId==12) {
  76. [Dòng 455] this._b == '55'
  77. [Dòng 455] this._b == '47'
  78. [Dòng 455] this._b == '48'
  79. [Dòng 455] this._b == '59'
  80. [Dòng 455] this._b == '73'
  81. [Dòng 455] this._b == '12') {
  82. [Dòng 458] this._b == '3'
  83. [Dòng 459] this._b == '43'
  84. [Dòng 459] this._b == '45'
  85. [Dòng 460] this._b == '57'
  86. [Dòng 461] this._b == '61'
  87. [Dòng 461] this._b == '63'
  88. [Dòng 461] this._b == '67'
  89. [Dòng 461] this._b == '68'
  90. [Dòng 461] this._b == '69'
  91. [Dòng 461] this._b == '72'
  92. [Dòng 461] this._b == '9'
  93. [Dòng 461] this._b == '74'
  94. [Dòng 461] this._b == '75') {
  95. [Dòng 464] this._b == '36'
  96. [Dòng 484] if (item['id'] == this._b) {

!== (1 điều kiện):
  1. [Dòng 119] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (7 điều kiện):
  1. [Dòng 157] if (params['locale'] != null) {
  2. [Dòng 163] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 167] this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
  4. [Dòng 194] if (!(strInstrument != null
  5. [Dòng 197] if (strInstrument.substring(0, 1) != '^'
  6. [Dòng 197] strInstrument.substr(strInstrument.length - 1) != '$') {
  7. [Dòng 348] if (bankid != null) {

================================================================================

📁 FILE 53: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/intercard-main/dialog-guide-dialog.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 30] data['type'] == '5'
  2. [Dòng 30] data['type'] == '7'"
  3. [Dòng 45] data['type'] == '6'
  4. [Dòng 45] data['type'] == '8'"

================================================================================

📁 FILE 54: intercard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/intercard-main/intercard-main.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 119] token_site == 'onepay'

!= (1 điều kiện):
  1. [Dòng 6] _showAVS!=true"

================================================================================

📁 FILE 55: intercard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/intercard-main/intercard-main.component.ts
📊 Thống kê: 66 điều kiện duy nhất
   - === : 8 lần
   - == : 38 lần
   - !== : 8 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 127] if (target.tagName === 'A'
  2. [Dòng 261] if (_formCard.country === 'default') {
  3. [Dòng 537] if (event.keyCode === 8
  4. [Dòng 537] event.key === "Backspace"
  5. [Dòng 612] if ((v.substr(-1) === ' '
  6. [Dòng 769] if (deviceValue === 'CA'
  7. [Dòng 769] deviceValue === 'US') {
  8. [Dòng 790] this.c_country = _val.value === 'default'

== (38 điều kiện):
  1. [Dòng 147] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 190] regexp.test('card;;visa;USD')) && (this._type == 5
  3. [Dòng 190] this._type == 7);
  4. [Dòng 191] regexp.test('card;;mastercard;USD')) && (this._type == 5
  5. [Dòng 192] regexp.test('card;;amex;USD')) && (this._type == 6
  6. [Dòng 192] this._type == 8);
  7. [Dòng 292] this.token_site == 'onepay'
  8. [Dòng 318] if (this._res_post.state == 'approved'
  9. [Dòng 318] this._res_post.state == 'failed') {
  10. [Dòng 344] } else if(this._res_post.state == 'failed') { // lỗi mới kiểm tra sang trang lỗi
  11. [Dòng 374] } else if (this._res_post.state == 'authorization_required') {
  12. [Dòng 375] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  13. [Dòng 387] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  14. [Dòng 446] v.length == 15) || (v.length == 16
  15. [Dòng 446] v.length == 19))
  16. [Dòng 447] this._util.checkMod10(v) == true) {
  17. [Dòng 491] cardNo.length == 15)
  18. [Dòng 493] cardNo.length == 16)
  19. [Dòng 494] cardNo.startsWith('81')) && (cardNo.length == 16
  20. [Dòng 494] cardNo.length == 19))
  21. [Dòng 537] event.inputType == 'deleteContentBackward') {
  22. [Dòng 538] if (event.target.name == 'exp_date'
  23. [Dòng 546] event.inputType == 'insertCompositionText') {
  24. [Dòng 561] if (((this.valueDate.length == 4
  25. [Dòng 561] this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  26. [Dòng 561] this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  27. [Dòng 612] v.length == 5) {
  28. [Dòng 620] v.length == 4
  29. [Dòng 624] v.length == 3)
  30. [Dòng 647] _val.value.length == 4
  31. [Dòng 651] _val.value.length == 3)
  32. [Dòng 747] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  33. [Dòng 747] this.valueDate.length == 5)
  34. [Dòng 820] this.valueDate.length == 4
  35. [Dòng 820] this.valueDate.search('/') == -1)
  36. [Dòng 821] this.valueDate.length == 5))
  37. [Dòng 834] this._i_csc.length == 4) ||
  38. [Dòng 838] this._i_csc.length == 3)

!== (8 điều kiện):
  1. [Dòng 300] key !== '8') {
  2. [Dòng 328] codeResponse.toString() !== '0'){
  3. [Dòng 612] event.inputType !== 'deleteContentBackward') || v.length == 5) {
  4. [Dòng 766] if (deviceValue !== 'default') {
  5. [Dòng 783] this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
  6. [Dòng 845] return this._i_country_code !== 'default'
  7. [Dòng 878] this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
  8. [Dòng 885] this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {

!= (12 điều kiện):
  1. [Dòng 174] if (params['locale'] != null) {
  2. [Dòng 180] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 320] if (this._res_post.return_url != null) {
  4. [Dòng 322] } else if (this._res_post.links != null
  5. [Dòng 322] this._res_post.links.merchant_return != null
  6. [Dòng 322] this._res_post.links.merchant_return.href != null) {
  7. [Dòng 428] if (ua.indexOf('safari') != -1
  8. [Dòng 491] cardNo != null
  9. [Dòng 539] if (this.valueDate.length != 3) {
  10. [Dòng 619] v != null
  11. [Dòng 646] this.c_csc = (!(_val.value != null
  12. [Dòng 832] this._i_csc != null

================================================================================

📁 FILE 56: menu.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/menu.component.html
📊 Thống kê: 30 điều kiện duy nhất
   - === : 15 lần
   - == : 8 lần
   - !== : 0 lần
   - != : 7 lần
--------------------------------------------------------------------------------

=== (15 điều kiện):
  1. [Dòng 120] type === 2"
  2. [Dòng 122] [ngStyle]="{'border-color': type === 2 ? this.themeColor.border_color : border_color}"
  3. [Dòng 149] <div *ngIf="(type === 2
  4. [Dòng 149] type === '2'
  5. [Dòng 162] type === 4"
  6. [Dòng 163] [ngStyle]="{'border-color': type === 4 ? this.themeColor.border_color : border_color}"
  7. [Dòng 200] type === 9"
  8. [Dòng 201] [ngStyle]="{'border-color': (type === 9
  9. [Dòng 236] [ngStyle]="{'border-color': type === 5 ? this.themeColor.border_color : border_color}"
  10. [Dòng 260] type === 5"
  11. [Dòng 279] type === 6"
  12. [Dòng 280] [ngStyle]="{'border-color': type === 6 ? this.themeColor.border_color : border_color}"
  13. [Dòng 321] type === 3"
  14. [Dòng 322] [ngStyle]="{'border-color': type === 3 ? this.themeColor.border_color : border_color}"
  15. [Dòng 364] d_vrbank===true"

== (8 điều kiện):
  1. [Dòng 123] sortMethodArray[i].trim()=='Domestic'"
  2. [Dòng 126] tokenList.length == 1)">
  3. [Dòng 131] tokenList.length == 1"
  4. [Dòng 165] sortMethodArray[i].trim()=='QR'"
  5. [Dòng 203] sortMethodArray[i].trim()=='VietQR'"
  6. [Dòng 235] type == 5"
  7. [Dòng 238] sortMethodArray[i].trim()=='International')">
  8. [Dòng 324] sortMethodArray[i].trim()=='Paypal'"

!= (7 điều kiện):
  1. [Dòng 146] feeService['atm']['fee'] != 0) || atm != 0"></span>
  2. [Dòng 146] (feeService['atm'] && feeService['atm']['fee'] != 0) || atm != 0
  3. [Dòng 185] feeService['qr']['fee'] != 0"
  4. [Dòng 221] feeService['vietqr']['fee'] != 0"
  5. [Dòng 262] feeService['visa_mastercard_d']['fee'] != 0"
  6. [Dòng 304] feeService['amex_d']['fee'] != 0"
  7. [Dòng 341] feeService['pp']['fee'] != 0"

================================================================================

📁 FILE 57: menu.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/menu.component.ts
📊 Thống kê: 150 điều kiện duy nhất
   - === : 9 lần
   - == : 77 lần
   - !== : 3 lần
   - != : 61 lần
--------------------------------------------------------------------------------

=== (9 điều kiện):
  1. [Dòng 765] this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_vietqr_1 === 1
  2. [Dòng 801] if (this._res.state === 'unpaid'
  3. [Dòng 801] this._res.state === 'not_paid') {
  4. [Dòng 925] if ('op' === auth
  5. [Dòng 962] } else if ('bank' === auth
  6. [Dòng 967] if (approval.method === 'REDIRECT') {
  7. [Dòng 970] } else if (approval.method === 'POST_REDIRECT') {
  8. [Dòng 1203] return id === 'amex' ? '1234' : '123'
  9. [Dòng 1357] if (this.timeLeftPaypal === 0) {

== (77 điều kiện):
  1. [Dòng 193] if (el == 5) {
  2. [Dòng 195] } else if (el == 6) {
  3. [Dòng 197] } else if (el == 7) {
  4. [Dòng 199] } else if (el == 8) {
  5. [Dòng 201] } else if (el == 2) {
  6. [Dòng 203] } else if (el == 4) {
  7. [Dòng 205] } else if (el == 3) {
  8. [Dòng 207] } else if (el == 9) {
  9. [Dòng 228] if (!isNaN(_re.status) && (_re.status == '200'
  10. [Dòng 228] _re.status == '201') && _re.body != null) {
  11. [Dòng 233] if (('closed' == this._res_polling.state
  12. [Dòng 233] 'canceled' == this._res_polling.state
  13. [Dòng 233] 'expired' == this._res_polling.state)
  14. [Dòng 253] } else if ('paid' == this._res_polling.state) {
  15. [Dòng 263] this._res_polling.payments == null) {
  16. [Dòng 272] if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'authorization_required') {
  17. [Dòng 276] } else if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  18. [Dòng 283] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  19. [Dòng 285] this._paymentService.getCurrentPage() == 'enter_card') {
  20. [Dòng 288] } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
  21. [Dòng 288] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
  22. [Dòng 305] } else if ('not_paid' == this._res_polling.state) {
  23. [Dòng 317] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
  24. [Dòng 448] if (message == '1') {
  25. [Dòng 456] if (this.checkInvoiceState() == 1) {
  26. [Dòng 470] if (_re.status == '200'
  27. [Dòng 470] _re.status == '201') {
  28. [Dòng 506] if (this.type == 5
  29. [Dòng 509] } else if (this.type == 6
  30. [Dòng 512] } else if (this.type == 2
  31. [Dòng 515] } else if (this.type == 7
  32. [Dòng 518] } else if (this.type == 8
  33. [Dòng 521] } else if (this.type == 4
  34. [Dòng 524] } else if (this.type == 3
  35. [Dòng 527] } else if (this.type == 9
  36. [Dòng 614] if (value == true) {
  37. [Dòng 628] if (('closed' == this._res.state
  38. [Dòng 628] 'canceled' == this._res.state
  39. [Dòng 628] 'expired' == this._res.state
  40. [Dòng 628] 'paid' == this._res.state)
  41. [Dòng 632] if ('paid' == this._res.state
  42. [Dòng 774] this._auth == 0) {
  43. [Dòng 802] if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
  44. [Dòng 802] this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
  45. [Dòng 804] if (this._res.payments[this._res.payments.length - 1].state == 'approved'
  46. [Dòng 806] } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
  47. [Dòng 820] || (responseCode == '1'
  48. [Dòng 820] instrumentType == 'vietqr'))
  49. [Dòng 854] if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required') {
  50. [Dòng 860] } else if (this._res.payments[this._res.payments.length - 1].state == 'pending') {
  51. [Dòng 866] } else if (this._res.payments[this._res.payments.length - 1].state == 'canceled') {
  52. [Dòng 870] this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  53. [Dòng 872] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id == 'vietqr'
  54. [Dòng 879] } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  55. [Dòng 879] this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
  56. [Dòng 913] } else if (idBrand == 'atm'
  57. [Dòng 990] if ('paid' == this._res.state) {
  58. [Dòng 991] this._res.merchant.token_site == 'onepay')) {
  59. [Dòng 1042] this._res.payments == null) {
  60. [Dòng 1044] this._res.payments[this._res.payments.length - 1].state == 'pending') {
  61. [Dòng 1054] if (this._res.currencies[0] == 'USD') {
  62. [Dòng 1119] if (item.instrument.issuer.brand.id == 'atm') {
  63. [Dòng 1121] } else if (item.instrument.issuer.brand.id == 'visa'
  64. [Dòng 1121] item.instrument.issuer.brand.id == 'mastercard') {
  65. [Dòng 1122] if (item.instrument.issuer_location == 'd') {
  66. [Dòng 1127] } else if (item.instrument.issuer.brand.id == 'amex') {
  67. [Dòng 1133] } else if (item.instrument.issuer.brand.id == 'jcb') {
  68. [Dòng 1332] this.tokenList.length == 1) {
  69. [Dòng 1333] if (_val == 2
  70. [Dòng 1345] if (this.type == 4) {
  71. [Dòng 1367] return (this.currentMethod == 9) // PTTT riêng VietQR
  72. [Dòng 1429] if (this._res_post.state == 'approved'
  73. [Dòng 1429] this._res_post.state == 'failed') {
  74. [Dòng 1438] } else if (this._res_post.state == 'authorization_required') {
  75. [Dòng 1439] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  76. [Dòng 1453] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  77. [Dòng 1529] if (data._locale == 'en') {

!== (3 điều kiện):
  1. [Dòng 937] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  2. [Dòng 1311] if (_val !== 3) {
  3. [Dòng 1315] this._util.getElementParams(this.currentUrl, 't_id') !== '') {

!= (61 điều kiện):
  1. [Dòng 221] if (this._idInvoice != null
  2. [Dòng 221] this._paymentService.getState() != 'error') {
  3. [Dòng 227] if (this._paymentService.getCurrentPage() != 'otp') {
  4. [Dòng 228] _re.body != null) {
  5. [Dòng 234] this._res_polling.links != null
  6. [Dòng 234] this._res_polling.links.merchant_return != null //
  7. [Dòng 263] } else if (this._res_polling.merchant != null
  8. [Dòng 263] this._res_polling.merchant_invoice_reference != null
  9. [Dòng 265] } else if (this._res_polling.payments != null
  10. [Dòng 289] this._res_polling.links.merchant_return != null//
  11. [Dòng 308] this._res_polling.payments != null
  12. [Dòng 316] if (this._res_polling.payments != null
  13. [Dòng 321] t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
  14. [Dòng 426] this.type.toString().length != 0) {
  15. [Dòng 433] if (params['locale'] != null) {
  16. [Dòng 441] if ('otp' != this._paymentService.getCurrentPage()) {
  17. [Dòng 453] if (this._paymentService.getInvoiceDetail() != null) {
  18. [Dòng 629] this._res.links != null
  19. [Dòng 629] this._res.links.merchant_return != null
  20. [Dòng 785] if (count != 1) {
  21. [Dòng 791] if (this._res.merchant != null
  22. [Dòng 791] this._res.merchant_invoice_reference != null) {
  23. [Dòng 794] if (this._res.merchant.address_details != null) {
  24. [Dòng 802] this._res.links != null//
  25. [Dòng 848] } else if (this._res.payments != null
  26. [Dòng 871] this._res.payments[this._res.payments.length - 1].instrument != null
  27. [Dòng 871] this._res.payments[this._res.payments.length - 1].instrument.issuer != null
  28. [Dòng 872] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
  29. [Dòng 876] if (this.type != 9) {
  30. [Dòng 881] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
  31. [Dòng 882] this._res.payments[this._res.payments.length - 1].links != null
  32. [Dòng 882] this._res.payments[this._res.payments.length - 1].links.cancel != null
  33. [Dòng 882] this._res.payments[this._res.payments.length - 1].links.cancel.href != null
  34. [Dòng 898] this._res.payments[this._res.payments.length - 1].links.update != null
  35. [Dòng 898] this._res.payments[this._res.payments.length - 1].links.update.href != null) {
  36. [Dòng 913] this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
  37. [Dòng 914] this._res.payments[this._res.payments.length - 1].authorization != null
  38. [Dòng 914] this._res.payments[this._res.payments.length - 1].authorization.links != null) {
  39. [Dòng 925] this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
  40. [Dòng 925] this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
  41. [Dòng 928] if (this._res.payments[this._res.payments.length - 1].authorization != null
  42. [Dòng 928] this._res.payments[this._res.payments.length - 1].authorization.links != null
  43. [Dòng 934] auth = paramUserName != null ? paramUserName : ''
  44. [Dòng 1017] this._res.links.merchant_return != null //
  45. [Dòng 1042] } else if (this._res.merchant != null
  46. [Dòng 1042] this._res.merchant_invoice_reference != null
  47. [Dòng 1147] if (['mastercard', 'visa', 'amex', 'jcb', 'cup', 'card', 'np_card', 'atm'].indexOf(id) != -1) {
  48. [Dòng 1149] } else if (['techcombank_account', 'vib_account', 'dongabank_account', 'tpbank_account', 'bidv_account', 'pvcombank_account', 'shb_account', 'oceanbank_online_account'].indexOf(id) != -1) {
  49. [Dòng 1151] } else if (['oceanbank_mobile_account'].indexOf(id) != -1) {
  50. [Dòng 1153] } else if (['shb_customer_id'].indexOf(id) != -1) {
  51. [Dòng 1170] if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1) {
  52. [Dòng 1198] return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
  53. [Dòng 1235] if (!(strInstrument != null
  54. [Dòng 1252] if (this._translate.currentLang != language) {
  55. [Dòng 1291] this.currentMethod != selected) {
  56. [Dòng 1317] } else if (this._res.payments != null) {
  57. [Dòng 1431] if (this._res_post.return_url != null) {
  58. [Dòng 1433] } else if (this._res_post.links != null
  59. [Dòng 1433] this._res_post.links.merchant_return != null
  60. [Dòng 1433] this._res_post.links.merchant_return.href != null) {
  61. [Dòng 1492] _re.body?.state != 'canceled')

================================================================================

📁 FILE 58: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 59: qr-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 2] screen=='qr'"
  2. [Dòng 123] screen=='confirm_close'"

================================================================================

📁 FILE 60: qr-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 51] 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {

================================================================================

📁 FILE 61: qr-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/qr-main/qr-guide-dialog/qr-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 62: qr-guide-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/qr-main/qr-guide-dialog/qr-guide-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 63: qr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/qr-main/qr-main.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 6] filteredData.length === 0
  2. [Dòng 6] filteredDataOther.length === 0"
  3. [Dòng 45] filteredDataMobile.length === 0
  4. [Dòng 45] filteredDataOtherMobile.length === 0"

================================================================================

📁 FILE 64: qr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/qr-main/qr-main.component.ts
📊 Thống kê: 22 điều kiện duy nhất
   - === : 8 lần
   - == : 8 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 217] this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
  2. [Dòng 218] this.filteredDataOther = this.appList.filter(item => item.type === 'other');
  3. [Dòng 219] this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
  4. [Dòng 220] this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
  5. [Dòng 241] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  6. [Dòng 242] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  7. [Dòng 248] if (item.type === 'mobile_banking') {
  8. [Dòng 550] this.appList.length === 1

== (8 điều kiện):
  1. [Dòng 144] this.themeColor.deeplink_status == 'Off' ? false : true
  2. [Dòng 247] if (item.available == true) {
  3. [Dòng 316] if (_re.status == '200'
  4. [Dòng 316] _re.status == '201') {
  5. [Dòng 318] if (appcode == 'grabpay'
  6. [Dòng 318] appcode == 'momo') {
  7. [Dòng 321] if (type == 2
  8. [Dòng 360] err.error.code == '04') {

!= (6 điều kiện):
  1. [Dòng 162] if (params['locale'] != null) {
  2. [Dòng 168] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 194] if (!(strInstrument != null
  4. [Dòng 278] if (appcode != null) {
  5. [Dòng 523] if (_re.status != '200'
  6. [Dòng 523] _re.status != '201')

================================================================================

📁 FILE 65: safe-html.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/qr-main/safe-html.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 66: queuing.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/queuing/queuing.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 67: queuing.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/queuing/queuing.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 62] if (this.locale == 'vi') {

!= (1 điều kiện):
  1. [Dòng 86] if (this.translate.currentLang != language) {

================================================================================

📁 FILE 68: token-expired-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/token-expired-dialog/token-expired-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 69: token-expired-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/token-expired-dialog/token-expired-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 70: remove-token-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/token-main/remove-token-dialog/remove-token-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 71: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/token-main/token-dialog/dialog-guide-dialog.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (6 điều kiện):
  1. [Dòng 9] data['type'] == 'Visa'
  2. [Dòng 9] data['type'] == 'Master'
  3. [Dòng 9] data['type'] == 'JCB'"
  4. [Dòng 17] data['type'] == 'Visa'"
  5. [Dòng 17] data['type'] == 'Master'"
  6. [Dòng 24] data['type'] == 'Amex'"

================================================================================

📁 FILE 72: token-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/token-main/token-main.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 1 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 8] item.brand_id === 'visa' ? 'height: 14.42px

== (1 điều kiện):
  1. [Dòng 27] token_main == '1'"

!= (2 điều kiện):
  1. [Dòng 20] item['feeService']['fee'] != 0
  2. [Dòng 22] item['feeService']['fee'] != 0"

================================================================================

📁 FILE 73: token-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/token-main/token-main.component.ts
📊 Thống kê: 70 điều kiện duy nhất
   - === : 7 lần
   - == : 41 lần
   - !== : 1 lần
   - != : 21 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 92] if (target.tagName === 'A'
  2. [Dòng 111] if (event.keyCode === 13) {
  3. [Dòng 284] && ((item.brand_id === 'amex'
  4. [Dòng 301] return id === 'amex' ? '1234' : '123'
  5. [Dòng 466] if (approval.method === 'REDIRECT') {
  6. [Dòng 469] } else if (approval.method === 'POST_REDIRECT') {
  7. [Dòng 541] return this._i_token_otp != null && !isNaN(+this._i_token_otp) && ((id === 'amex'

== (41 điều kiện):
  1. [Dòng 151] if (message == '0') {
  2. [Dòng 221] item.id == element.id ? element['active'] = true : element['active'] = false
  3. [Dòng 225] item.display_cvv == true ? this.flagToken = true : this.flagToken = false
  4. [Dòng 267] if (result == 'success') {
  5. [Dòng 272] if (this.tokenList.length == 0) {
  6. [Dòng 276] } else if (result == 'error') {
  7. [Dòng 284] _val.value.length == 4) || (item.brand_id != 'amex'
  8. [Dòng 284] _val.value.length == 3))
  9. [Dòng 294] _val.value.length == 3)));
  10. [Dòng 300] id == 'amex' ? this.maxLength = 4 : this.maxLength = 3
  11. [Dòng 345] if (_re.body.state == 'more_info_required') {
  12. [Dòng 360] if (this._res_post.state == 'approved'
  13. [Dòng 360] this._res_post.state == 'failed') {
  14. [Dòng 367] if (this._res_post.state == 'failed') {
  15. [Dòng 383] } else if (this._res_post.state == 'authorization_required') {
  16. [Dòng 384] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  17. [Dòng 396] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  18. [Dòng 412] } else if (_re.body.state == 'authorization_required') {
  19. [Dòng 429] if (this._b == 1
  20. [Dòng 429] this._b == 14
  21. [Dòng 429] this._b == 15
  22. [Dòng 429] this._b == 24
  23. [Dòng 429] this._b == 8
  24. [Dòng 429] this._b == 10
  25. [Dòng 429] this._b == 20
  26. [Dòng 429] this._b == 22
  27. [Dòng 429] this._b == 23
  28. [Dòng 429] this._b == 30
  29. [Dòng 429] this._b == 11
  30. [Dòng 429] this._b == 17
  31. [Dòng 429] this._b == 18
  32. [Dòng 429] this._b == 27
  33. [Dòng 429] this._b == 5
  34. [Dòng 429] this._b == 12
  35. [Dòng 429] this._b == 9) {
  36. [Dòng 488] } else if (_re.body.state == 'failed') {
  37. [Dòng 533] if (action == 'blur') {
  38. [Dòng 541] this._i_token_otp.length == 4)
  39. [Dòng 542] this._i_token_otp.length == 3));
  40. [Dòng 595] if (_re.status == '200'
  41. [Dòng 595] _re.status == '201') {

!== (1 điều kiện):
  1. [Dòng 211] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (21 điều kiện):
  1. [Dòng 112] this._res.merchant.id != 'OPTEST' ? this._res.on_off_bank : ''
  2. [Dòng 137] if (params['locale'] != null) {
  3. [Dòng 194] if (this.currentMethod != 9) {
  4. [Dòng 283] if (_val.value != null
  5. [Dòng 293] this.c_token_otp_csc = !(_val.value != null
  6. [Dòng 362] if (this._res_post.return_url != null) {
  7. [Dòng 364] } else if (this._res_post.links != null
  8. [Dòng 364] this._res_post.links.merchant_return != null
  9. [Dòng 364] this._res_post.links.merchant_return.href != null) {
  10. [Dòng 417] if (_re.body.authorization != null
  11. [Dòng 417] _re.body.authorization.links != null
  12. [Dòng 424] if (_re.body.links != null
  13. [Dòng 424] _re.body.links.cancel != null) {
  14. [Dòng 490] if (_re.body.return_url != null) {
  15. [Dòng 492] } else if (_re.body.links != null
  16. [Dòng 492] _re.body.links.merchant_return != null
  17. [Dòng 492] _re.body.links.merchant_return.href != null) {
  18. [Dòng 524] return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
  19. [Dòng 529] if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(item.brand_id) != -1
  20. [Dòng 541] return this._i_token_otp != null
  21. [Dòng 542] || (id != 'amex'

================================================================================

📁 FILE 74: vietqr-guide-mobile.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/vietqr-main/vietqr-guide/mobile/vietqr-guide-mobile.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 75: vietqr-guide-mobile.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/vietqr-main/vietqr-guide/mobile/vietqr-guide-mobile.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 76: vietqr-guide.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/vietqr-main/vietqr-guide/vietqr-guide.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 77: vietqr-guide.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/vietqr-main/vietqr-guide/vietqr-guide.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 78: vietqr-listbank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/vietqr-main/vietqr-listbank/vietqr-listbank.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 79: vietqr-listbank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/vietqr-main/vietqr-listbank/vietqr-listbank.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 36] if (banksRes?.status == 200
  2. [Dòng 39] if (appRes?.status == 200

================================================================================

📁 FILE 80: vietqr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/vietqr-main/vietqr-main.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 15] vietQRData?.state=='created' else state_failed"

================================================================================

📁 FILE 81: vietqr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/main/menu/vietqr-main/vietqr-main.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (6 điều kiện):
  1. [Dòng 96] if (res.status == '200'
  2. [Dòng 96] res.status == '201') {
  3. [Dòng 101] if (res.body?.state == 'created'){
  4. [Dòng 125] if (payment?.state == 'failed'
  5. [Dòng 125] payment?.state == 'canceled'
  6. [Dòng 125] payment?.state == 'expired') {

================================================================================

📁 FILE 82: overlay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/overlay/overlay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 83: overlay.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/overlay/overlay.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 84: overlay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/overlay/overlay.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 85: bank-amount.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/pipe/bank-amount.pipe.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 11] return ((a.id == id
  2. [Dòng 11] a.code == id) && a.type.includes(type));

================================================================================

📁 FILE 86: safe-html.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/pipe/safe-html.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 87: close-dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/services/close-dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 88: data.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/services/data.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 79] const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
  2. [Dòng 79] item.method === method) : null;

================================================================================

📁 FILE 89: deep_link.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/services/deep_link.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 9] if (isIphone == true) {
  2. [Dòng 22] } else if (isAndroid == true) {

================================================================================

📁 FILE 90: dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/services/dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 91: fee.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/services/fee.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 92: multiple_method.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/services/multiple_method.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 93: payment.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/services/payment.service.ts
📊 Thống kê: 16 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 12 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 606] if (res.status == '200'
  2. [Dòng 606] res.status == '201') {
  3. [Dòng 617] return countPayment == maxPayment
  4. [Dòng 653] if (this.getLatestPayment().state == 'canceled')

!= (12 điều kiện):
  1. [Dòng 112] if (idInvoice != null
  2. [Dòng 112] idInvoice != 0)
  3. [Dòng 122] idInvoice != 0) {
  4. [Dòng 307] if (this._merchantid != null
  5. [Dòng 307] this._tranref != null
  6. [Dòng 307] this._state != null
  7. [Dòng 378] let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
  8. [Dòng 421] urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
  9. [Dòng 443] if (paymentId != null) {
  10. [Dòng 446] if (fee != null) {
  11. [Dòng 543] if (res?.status != 200
  12. [Dòng 543] res?.status != 201) return;

================================================================================

📁 FILE 94: qr.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/services/qr.service.ts
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 59] if (res?.state == 'canceled') {
  2. [Dòng 100] state == 'authorization_required'

!= (3 điều kiện):
  1. [Dòng 41] if (_re.status != '200'
  2. [Dòng 41] _re.status != '201') {
  3. [Dòng 50] latestPayment?.state != "authorization_required") {

================================================================================

📁 FILE 95: token-main.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/services/token-main.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 96: vietqr.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/services/vietqr.service.ts
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 43] if (_re.status == '200'
  2. [Dòng 43] _re.status == '201') {
  3. [Dòng 58] } else if (latestPayment?.state == "failed") {
  4. [Dòng 64] if (res?.state == 'canceled') {

!= (1 điều kiện):
  1. [Dòng 55] if (latestPayment?.instrument?.type != "vietqr") {

================================================================================

📁 FILE 97: index.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/translate/index.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 98: lang-en.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/translate/lang-en.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 99: lang-vi.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/translate/lang-vi.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 100: translate.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/translate/translate.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 101: translate.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/translate/translate.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 37] if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
  2. [Dòng 38] else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';

================================================================================

📁 FILE 102: translations.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/translate/translations.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 103: apps-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/util/apps-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 550] if (e.name == bankSwift) { // TODO: get by swift
  2. [Dòng 558] return this.apps.find(e => e.code == appCode);

================================================================================

📁 FILE 104: banks-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/util/banks-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1012] if (+e.id == bankId) {
  2. [Dòng 1062] if (e.swiftCode == bankSwift) {

================================================================================

📁 FILE 105: error-handler.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/util/error-handler.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 9] err?.status === 400
  2. [Dòng 10] err?.error?.name === 'INVALID_CARD_FEE'

================================================================================

📁 FILE 106: util.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/app/util/util.ts
📊 Thống kê: 40 điều kiện duy nhất
   - === : 15 lần
   - == : 18 lần
   - !== : 3 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (15 điều kiện):
  1. [Dòng 56] if (v.length === 2
  2. [Dòng 56] this.flag.length === 3
  3. [Dòng 56] this.flag.charAt(this.flag.length - 1) === '/') {
  4. [Dòng 60] if (v.length === 1) {
  5. [Dòng 62] } else if (v.length === 2) {
  6. [Dòng 65] v.length === 2) {
  7. [Dòng 73] if (len === 2) {
  8. [Dòng 145] if (M[1] === 'Chrome') {
  9. [Dòng 270] if (param === key) {
  10. [Dòng 393] pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
  11. [Dòng 397] target === 0
  12. [Dòng 475] if (cardTypeBank === 'bank_card_number') {
  13. [Dòng 478] } else if (cardTypeBank === 'bank_account_number') {
  14. [Dòng 528] if (event.keyCode === 8
  15. [Dòng 528] event.key === "Backspace"

== (18 điều kiện):
  1. [Dòng 15] if (temp.length == 0) {
  2. [Dòng 22] return (counter % 10 == 0);
  3. [Dòng 26] if (currency == 'USD') {
  4. [Dòng 119] if (this.checkCount == 1) {
  5. [Dòng 131] if (results == null) {
  6. [Dòng 164] if (c.length == 3) {
  7. [Dòng 177] d = d == undefined ? '.' : d
  8. [Dòng 178] t = t == undefined ? '
  9. [Dòng 258] return results == null ? null : results[1]
  10. [Dòng 528] event.inputType == 'deleteContentBackward') {
  11. [Dòng 529] if (event.target.name == 'exp_date'
  12. [Dòng 537] event.inputType == 'insertCompositionText') {
  13. [Dòng 551] if (((_val.length == 4
  14. [Dòng 551] _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  15. [Dòng 551] _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  16. [Dòng 577] iss_date.length == 4
  17. [Dòng 577] iss_date.search('/') == -1)
  18. [Dòng 578] iss_date.length == 5))

!== (3 điều kiện):
  1. [Dòng 265] queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
  2. [Dòng 266] if (queryString !== '') {
  3. [Dòng 397] if (target !== 0

!= (4 điều kiện):
  1. [Dòng 147] if (tem != null) {
  2. [Dòng 152] if ((tem = ua.match(/version\/(\d+)/i)) != null) {
  3. [Dòng 473] if (ua.indexOf('safari') != -1
  4. [Dòng 530] if (v.length != 3) {

================================================================================

📁 FILE 107: qrcode.js
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/assets/script/qrcode.js
📊 Thống kê: 67 điều kiện duy nhất
   - === : 2 lần
   - == : 53 lần
   - !== : 0 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 2255] if (typeof define === 'function'
  2. [Dòng 2257] } else if (typeof exports === 'object') {

== (53 điều kiện):
  1. [Dòng 68] if (_dataCache == null) {
  2. [Dòng 85] if ( (0 <= r && r <= 6 && (c == 0
  3. [Dòng 85] c == 6) )
  4. [Dòng 86] || (0 <= c && c <= 6 && (r == 0
  5. [Dòng 86] r == 6) )
  6. [Dòng 107] if (i == 0
  7. [Dòng 122] _modules[r][6] = (r % 2 == 0);
  8. [Dòng 129] _modules[6][c] = (c % 2 == 0);
  9. [Dòng 152] if (r == -2
  10. [Dòng 152] r == 2
  11. [Dòng 152] c == -2
  12. [Dòng 152] c == 2
  13. [Dòng 153] || (r == 0
  14. [Dòng 153] c == 0) ) {
  15. [Dòng 169] ( (bits >> i) & 1) == 1);
  16. [Dòng 226] if (col == 6) col -= 1;
  17. [Dòng 232] if (_modules[row][col - c] == null) {
  18. [Dòng 237] dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
  19. [Dòng 249] if (bitIndex == -1) {
  20. [Dòng 458] margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
  21. [Dòng 498] if (typeof arguments[0] == 'object') {
  22. [Dòng 587] margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
  23. [Dòng 733] if (b == -1) throw 'eof';
  24. [Dòng 741] if (b0 == -1) break;
  25. [Dòng 767] if (typeof b == 'number') {
  26. [Dòng 768] if ( (b & 0xff) == b) {
  27. [Dòng 910] return function(i, j) { return (i + j) % 2 == 0
  28. [Dòng 912] return function(i, j) { return i % 2 == 0
  29. [Dòng 914] return function(i, j) { return j % 3 == 0
  30. [Dòng 916] return function(i, j) { return (i + j) % 3 == 0
  31. [Dòng 918] return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
  32. [Dòng 920] return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
  33. [Dòng 922] return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
  34. [Dòng 924] return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
  35. [Dòng 1011] if (r == 0
  36. [Dòng 1011] c == 0) {
  37. [Dòng 1015] if (dark == qrcode.isDark(row + r, col + c) ) {
  38. [Dòng 1036] if (count == 0
  39. [Dòng 1036] count == 4) {
  40. [Dòng 1149] if (typeof num.length == 'undefined') {
  41. [Dòng 1155] num[offset] == 0) {
  42. [Dòng 1495] if (typeof rsBlock == 'undefined') {
  43. [Dòng 1538] return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
  44. [Dòng 1543] _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
  45. [Dòng 1599] if (data.length - i == 1) {
  46. [Dòng 1601] } else if (data.length - i == 2) {
  47. [Dòng 1866] } else if (n == 62) {
  48. [Dòng 1868] } else if (n == 63) {
  49. [Dòng 1928] if (_buflen == 0) {
  50. [Dòng 1937] if (c == '=') {
  51. [Dòng 1961] } else if (c == 0x2b) {
  52. [Dòng 1963] } else if (c == 0x2f) {
  53. [Dòng 2133] if (table.size() == (1 << bitLength) ) {

!= (12 điều kiện):
  1. [Dòng 119] if (_modules[r][6] != null) {
  2. [Dòng 126] if (_modules[6][c] != null) {
  3. [Dòng 144] if (_modules[row][col] != null) {
  4. [Dòng 365] while (buffer.getLengthInBits() % 8 != 0) {
  5. [Dòng 750] if (count != numChars) {
  6. [Dòng 751] throw count + ' != ' + numChars
  7. [Dòng 878] while (data != 0) {
  8. [Dòng 1733] if (test.length != 2
  9. [Dòng 1733] ( (test[0] << 8) | test[1]) != code) {
  10. [Dòng 1894] if (_length % 3 != 0) {
  11. [Dòng 2067] if ( (data >>> length) != 0) {
  12. [Dòng 2178] return typeof _map[key] != 'undefined'

================================================================================

📁 FILE 108: environment.dev.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/environments/environment.dev.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 109: environment.mtf.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/environments/environment.mtf.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 110: environment.prod.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/environments/environment.prod.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 111: environment.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/environments/environment.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 112: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 113: karma.conf.js
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/karma.conf.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 114: main.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/main.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 115: polyfills.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/polyfills.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 116: test.ts
📍 Đường dẫn: /root/projects/onepay/paygate-enetviet/src/test.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📋 TÓM TẮT TẤT CẢ ĐIỀU KIỆN DUY NHẤT:
====================================================================================================

=== (152 điều kiện duy nhất):
------------------------------------------------------------
1. return lang === this._translate.currentLang
2. isPopupSupport === 'True') || (rePayment
3. isPopupSupport === 'True'"
4. isPopupSupport === 'True')">
5. params.timeout === 'true') {
6. this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
7. _re.body.state === 'unpaid');
8. if (this.errorCode === 'overtime'
9. this.errorCode === '253') {
10. params.name === 'CUSTOMER_INTIME'
11. params.code === '09') {
12. if (this.timeLeft === 0) {
13. if (YY % 400 === 0
14. YY % 4 === 0)) {
15. if (YYYY % 400 === 0
16. YYYY % 4 === 0)) {
17. <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
18. [class.red_border_no_background]="c_expdate && (valueDate.length === 0
19. valueDate.trim().length === 0)"
20. if (target.tagName === 'A'
21. if (isIE[0] === 'MSIE'
22. +isIE[1] === 10) {
23. if ((_val.value.substr(-1) === ' '
24. _val.value.length === 24) {
25. if (this.cardTypeBank === 'bank_card_number') {
26. } else if (this.cardTypeBank === 'bank_account_number') {
27. } else if (this.cardTypeBank === 'bank_username') {
28. } else if (this.cardTypeBank === 'bank_customer_code') {
29. this.cardTypeBank === 'bank_card_number'
30. if (this.cardTypeOcean === 'IB') {
31. } else if (this.cardTypeOcean === 'MB') {
32. if (_val.value.substr(0, 2) === '84') {
33. } else if (this.cardTypeOcean === 'ATM') {
34. if (this.cardTypeBank === 'bank_account_number') {
35. this.cardTypeBank === 'bank_card_number') {
36. if (this.cardTypeBank === 'bank_card_number'
37. if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
38. if (event.keyCode === 8
39. event.key === "Backspace"
40. if (v.length === 2
41. this.flag.length === 3
42. this.flag.charAt(this.flag.length - 1) === '/') {
43. if (v.length === 1) {
44. } else if (v.length === 2) {
45. v.length === 2) {
46. if (len === 2) {
47. if ((this.cardTypeBank === 'bank_account_number'
48. this.cardTypeBank === 'bank_username'
49. this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
50. this.cardTypeOcean === 'ATM')
51. || (this.cardTypeOcean === 'IB'
52. if (valIn === this._translate.instant('bank_card_number')) {
53. } else if (valIn === this._translate.instant('bank_account_number')) {
54. } else if (valIn === this._translate.instant('bank_username')) {
55. } else if (valIn === this._translate.instant('bank_customer_code')) {
56. if (_val.value === ''
57. _val.value === null
58. _val.value === undefined) {
59. if (_val.value && (this.cardTypeBank === 'bank_card_number'
60. if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
61. this.cardTypeOcean === 'MB') {
62. this.cardTypeOcean === 'IB'
63. if ((this.cardTypeBank === 'bank_card_number'
64. if (this.cardName === undefined
65. this.cardName === '') {
66. if (this.valueDate === undefined
67. this.valueDate === '') {
68. c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
69. _inExpDate.trim().length === 0)"
70. if (focusElement === 'card_name') {
71. } else if (focusElement === 'exp_date'
72. focusExpDateElement === 'card_name') {
73. if (this.cardTypeBank === 'bank_account_number'
74. if (approval.method === 'REDIRECT') {
75. } else if (approval.method === 'POST_REDIRECT') {
76. filteredData.length === 0"
77. if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
78. filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
79. if (valOut === 'auth') {
80. if (this._b === '1'
81. this._b === '20'
82. this._b === '64') {
83. if (this._b === '36'
84. this._b === '18'
85. this._b === '19'
86. if (this._b === '19'
87. this._b === '16'
88. this._b === '25'
89. this._b === '33'
90. this._b === '39'
91. this._b === '11'
92. this._b === '17'
93. this._b === '36'
94. this._b === '44'
95. this._b === '64'
96. if (this._b === '20'
97. if (this._b === '18') {
98. if (_formCard.country === 'default') {
99. if ((v.substr(-1) === ' '
100. if (deviceValue === 'CA'
101. deviceValue === 'US') {
102. this.c_country = _val.value === 'default'
103. type === 2"
104. [ngStyle]="{'border-color': type === 2 ? this.themeColor.border_color : border_color}"
105. <div *ngIf="(type === 2
106. type === '2'
107. type === 4"
108. [ngStyle]="{'border-color': type === 4 ? this.themeColor.border_color : border_color}"
109. type === 9"
110. [ngStyle]="{'border-color': (type === 9
111. [ngStyle]="{'border-color': type === 5 ? this.themeColor.border_color : border_color}"
112. type === 5"
113. type === 6"
114. [ngStyle]="{'border-color': type === 6 ? this.themeColor.border_color : border_color}"
115. type === 3"
116. [ngStyle]="{'border-color': type === 3 ? this.themeColor.border_color : border_color}"
117. d_vrbank===true"
118. this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_vietqr_1 === 1
119. if (this._res.state === 'unpaid'
120. this._res.state === 'not_paid') {
121. if ('op' === auth
122. } else if ('bank' === auth
123. return id === 'amex' ? '1234' : '123'
124. if (this.timeLeftPaypal === 0) {
125. filteredData.length === 0
126. filteredDataOther.length === 0"
127. filteredDataMobile.length === 0
128. filteredDataOtherMobile.length === 0"
129. this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
130. this.filteredDataOther = this.appList.filter(item => item.type === 'other');
131. this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
132. this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
133. item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
134. filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
135. if (item.type === 'mobile_banking') {
136. this.appList.length === 1
137. item.brand_id === 'visa' ? 'height: 14.42px
138. if (event.keyCode === 13) {
139. && ((item.brand_id === 'amex'
140. return this._i_token_otp != null && !isNaN(+this._i_token_otp) && ((id === 'amex'
141. const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
142. item.method === method) : null;
143. err?.status === 400
144. err?.error?.name === 'INVALID_CARD_FEE'
145. if (M[1] === 'Chrome') {
146. if (param === key) {
147. pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
148. target === 0
149. if (cardTypeBank === 'bank_card_number') {
150. } else if (cardTypeBank === 'bank_account_number') {
151. if (typeof define === 'function'
152. } else if (typeof exports === 'object') {

== (544 điều kiện duy nhất):
------------------------------------------------------------
1. 'vi' == params['locale']) {
2. 'en' == params['locale']) {
3. errorCode == '11'"
4. <div class="footer-button" *ngIf="((isSent == false
5. isSent == false
6. <div class="payment_again" *ngIf="rePayment && !timeOut" [class.select_only]="!(isSent == false
7. <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
8. (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
9. <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
10. !(errorCode == 'overtime' || errorCode == '253') && isBack && !invoiceNearExpire && !paymentPending
11. this.res.themes.theme == 'enetviet') {
12. params.response_code == 'overtime') {
13. if (_re.status == '200'
14. _re.status == '201') {
15. if (_re2.status == '200'
16. _re2.status == '201') {
17. if (this.errorCode == 'overtime'
18. this.errorCode == '253') {
19. } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
20. this.res.state == 'canceled') {
21. if (lastPayment?.state == 'pending') {
22. if (this.isTimePause == false) {
23. if ((dataPassed.status == '200'
24. dataPassed.status == '201') && dataPassed.body != null) {
25. dataPassed.body.themes.logo_full == 'True') {
26. if (this.type == 5
27. } else if (this.type == 6
28. } else if (this.type == 2
29. } else if (this.type == 7
30. } else if (this.type == 8
31. } else if (this.type == 4
32. } else if (this.type == 3
33. if (this.locale == 'en') {
34. if (bankId == 3
35. bankId == 61
36. bankId == 8
37. bankId == 49
38. bankId == 48
39. bankId == 10
40. bankId == 53
41. bankId == 17
42. bankId == 65
43. bankId == 23
44. bankId == 52
45. bankId == 27
46. bankId == 66
47. bankId == 9
48. bankId == 54
49. bankId == 37
50. bankId == 38
51. bankId == 39
52. bankId == 40
53. bankId == 42
54. bankId == 44
55. bankId == 72
56. bankId == 59
57. bankId == 51
58. bankId == 64
59. bankId == 58
60. bankId == 56
61. bankId == 55
62. bankId == 60
63. bankId == 68
64. bankId == 73
65. bankId == 74
66. bankId == 75 //Keb Hana
67. token_site == 'onepay'
68. this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
69. if (this._b == 18
70. this._b == 19) {
71. if (this._b == 19) {//19BIDV
72. } else if (this._b == 3
73. this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
74. if (this._b == 27) {
75. } else if (this._b == 12) {// 12SHB
76. } else if (this._b == 18) { //18Oceanbank-ocb
77. if (this._b == 19
78. this._b == 3
79. this._b == 27
80. this._b == 12) {
81. } else if (this._b == 18) {
82. if (this.checkBin(_val.value) && (this._b == 3
83. this._b == 27)) {
84. if (this._b == 3) {
85. this.cardTypeOcean == 'ATM') {
86. if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
87. this._b == 18)) {
88. if (this.checkBin(v) && (this._b == 3
89. event.inputType == 'deleteContentBackward') {
90. if (event.target.name == 'exp_date'
91. event.inputType == 'insertCompositionText') {
92. if (((this.valueDate.length == 4
93. this.valueDate.search('/') == -1) || this.valueDate.length == 5)
94. this.valueDate.length == 5)
95. if (temp.length == 0) {
96. return (counter % 10 == 0);
97. } else if (this._b == 19) {
98. } else if (this._b == 27) {
99. if (this._b == 12) {
100. if (this.cardTypeBank == 'bank_customer_code') {
101. } else if (this.cardTypeBank == 'bank_account_number') {
102. _formCard.exp_date.length == 5
103. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
104. this._b == 3)) {//27-pvcombank;3-TPB ;
105. if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
106. this._b == 19
107. this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
108. if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
109. if (this.cardTypeOcean == 'IB') {
110. } else if (this.cardTypeOcean == 'MB') {
111. } else if (this.cardTypeOcean == 'ATM') {
112. this.token_site == 'onepay'
113. if (this._res_post.state == 'approved'
114. this._res_post.state == 'failed') {
115. } else if (this._res_post.state == 'authorization_required') {
116. if (this._b == 18) {
117. if (this._b == 27
118. this._b == 18) {
119. if ((cardNo.length == 16
120. if ((cardNo.length == 16 || (cardNo.length == 19
121. && ((this._b == 18
122. cardNo.length == 19) || this._b != 18)
123. if (this._b == +e.id) {
124. if (valIn == 1) {
125. } else if (valIn == 2) {
126. this._b == 3) {
127. if (this._b == 19) {
128. if (cardType == this._translate.instant('internetbanking')
129. } else if (cardType == this._translate.instant('mobilebanking')
130. } else if (cardType == this._translate.instant('atm')
131. this._b == 18))) {
132. } else if (this._b == 18
133. this.c_expdate = !(((this.valueDate.length == 4
134. this.valueDate.length == 4
135. this.valueDate.search('/') == -1)
136. this.valueDate.length == 5))
137. if (this._b == 8) {//MB Bank
138. if (this._b == 18) {//Oceanbank
139. if (this._b == 8) {
140. if (this._b == 12) { //SHB
141. } else if (this._res.state == 'authorization_required') {
142. if (this.challengeCode == '') {
143. if (this._b == 18) {//8-MB Bank;18-oceanbank
144. <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_card_number')">
145. <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_account_number')">
146. if(this._b == 12) this.isShbGroup = true;
147. return this._b == 9
148. this._b == 11
149. this._b == 16
150. this._b == 17
151. this._b == 25
152. this._b == 44
153. this._b == 57
154. this._b == 59
155. this._b == 61
156. this._b == 63
157. this._b == 69
158. if (this.cardTypeBank == 'bank_account_number') {
159. _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
160. cardNo.length == 19) && cardNo.startsWith("970443") && this._util.checkMod10(cardNo) && this.checkBin(cardNo) ) {
161. if (this._b == 2
162. this._b == 31) {
163. if (this._b == 2) {
164. } else if (this._b == 6) {
165. } else if (this._b == 31) {
166. if (this._b == 5) {//5-vib;
167. if (this._b == 5) {
168. if (this.checkBin(_val.value) && (this._b == 5)) {
169. if (this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
170. if (this.checkBin(v) && (this._b == 5)) {
171. this._b == 5) {//5 vib ;
172. this._b == 5) {//5vib;
173. _b == 68"
174. if (this._b == 11
175. this._b == 20
176. this._b == 33
177. this._b == 39
178. this._b == 43
179. this._b == 45
180. this._b == 67
181. this._b == 64
182. this._b == 68
183. this._b == 72
184. this._b == 73
185. this._b == 36
186. this._b == 74
187. this._b == 75) {//seabank
188. if (this._b == 1
189. this._b == 55
190. this._b == 47
191. this._b == 48
192. this._b == 59) {
193. this._b == 14
194. this._b == 15
195. this._b == 24
196. this._b == 8
197. this._b == 10
198. this._b == 22
199. this._b == 23
200. this._b == 30
201. this._b == 9) {
202. (cardNo.length == 19
203. (cardNo.length == 19 && (this._b == 1
204. this._b == 4
205. this._b == 59))
206. this._util.checkMod10(cardNo) == true
207. return ((value.length == 4
208. value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
209. value.length == 5) && parseInt(value.split('/')[0]
210. || (this._util.checkValidExpireMonthYear(value) && (this._b == 11
211. this._b == 75)))
212. this._inExpDate.length == 4
213. this._inExpDate.search('/') == -1)
214. this._inExpDate.length == 5))
215. || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 11
216. this._b == 75)));
217. ((!token && _auth==0 && vietcombankGroupSelected) || (token && _b==16))
218. _b==16))">
219. !token && _auth==0 && techcombankGroupSelected
220. !token && _auth==0 && bankaccountGroupSelected
221. !token && _auth==0 && vibbankGroupSelected
222. !token && _auth==0 && shbGroupSelected
223. (token || _auth==1) && _b != 16
224. this._auth == 0
225. this.tokenList.length == 0) {
226. this.filteredData.length == 1
227. $event == 'true') {
228. if (bankId==1
229. bankId==4
230. bankId==7
231. bankId==8
232. bankId==9
233. bankId==10
234. bankId==11
235. bankId==14
236. bankId==15
237. bankId==16
238. bankId==17
239. bankId==20
240. bankId==22
241. bankId==23
242. bankId==24
243. bankId==25
244. bankId==30
245. bankId==33
246. bankId==34
247. bankId==35
248. bankId==36
249. bankId==37
250. bankId==38
251. bankId==39
252. bankId==40
253. bankId==41
254. bankId==42
255. bankId==43
256. bankId==44
257. bankId==45
258. bankId==46
259. bankId==47
260. bankId==48
261. bankId==49
262. bankId==50
263. bankId==51
264. bankId==52
265. bankId==53
266. bankId==54
267. bankId==55
268. bankId==56
269. bankId==57
270. bankId==58
271. bankId==59
272. bankId==60
273. bankId==61
274. bankId==62
275. bankId==63
276. bankId==64
277. bankId==65
278. bankId==66
279. bankId==67
280. bankId==68
281. bankId==69
282. bankId==70
283. bankId==71
284. bankId==72
285. bankId==73
286. bankId==32
287. bankId==74
288. bankId==75) {
289. } else if (bankId == 6
290. bankId == 31
291. bankId == 80
292. bankId == 2) {
293. } else if (bankId==3
294. bankId==18
295. bankId==19
296. bankId==27) {
297. } else if (bankId==5) {
298. } else if (bankId==12) {
299. this._b == '55'
300. this._b == '47'
301. this._b == '48'
302. this._b == '59'
303. this._b == '73'
304. this._b == '12') {
305. this._b == '3'
306. this._b == '43'
307. this._b == '45'
308. this._b == '57'
309. this._b == '61'
310. this._b == '63'
311. this._b == '67'
312. this._b == '68'
313. this._b == '69'
314. this._b == '72'
315. this._b == '9'
316. this._b == '74'
317. this._b == '75') {
318. this._b == '36'
319. if (item['id'] == this._b) {
320. data['type'] == '5'
321. data['type'] == '7'"
322. data['type'] == '6'
323. data['type'] == '8'"
324. regexp.test('card;;visa;USD')) && (this._type == 5
325. this._type == 7);
326. regexp.test('card;;mastercard;USD')) && (this._type == 5
327. regexp.test('card;;amex;USD')) && (this._type == 6
328. this._type == 8);
329. } else if(this._res_post.state == 'failed') { // lỗi mới kiểm tra sang trang lỗi
330. if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
331. } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
332. v.length == 15) || (v.length == 16
333. v.length == 19))
334. this._util.checkMod10(v) == true) {
335. cardNo.length == 15)
336. cardNo.length == 16)
337. cardNo.startsWith('81')) && (cardNo.length == 16
338. cardNo.length == 19))
339. this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
340. this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
341. v.length == 5) {
342. v.length == 4
343. v.length == 3)
344. _val.value.length == 4
345. _val.value.length == 3)
346. this._i_csc.length == 4) ||
347. this._i_csc.length == 3)
348. sortMethodArray[i].trim()=='Domestic'"
349. tokenList.length == 1)">
350. tokenList.length == 1"
351. sortMethodArray[i].trim()=='QR'"
352. sortMethodArray[i].trim()=='VietQR'"
353. type == 5"
354. sortMethodArray[i].trim()=='International')">
355. sortMethodArray[i].trim()=='Paypal'"
356. if (el == 5) {
357. } else if (el == 6) {
358. } else if (el == 7) {
359. } else if (el == 8) {
360. } else if (el == 2) {
361. } else if (el == 4) {
362. } else if (el == 3) {
363. } else if (el == 9) {
364. if (!isNaN(_re.status) && (_re.status == '200'
365. _re.status == '201') && _re.body != null) {
366. if (('closed' == this._res_polling.state
367. 'canceled' == this._res_polling.state
368. 'expired' == this._res_polling.state)
369. } else if ('paid' == this._res_polling.state) {
370. this._res_polling.payments == null) {
371. if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'authorization_required') {
372. } else if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
373. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
374. this._paymentService.getCurrentPage() == 'enter_card') {
375. } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
376. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
377. } else if ('not_paid' == this._res_polling.state) {
378. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
379. if (message == '1') {
380. if (this.checkInvoiceState() == 1) {
381. } else if (this.type == 9
382. if (value == true) {
383. if (('closed' == this._res.state
384. 'canceled' == this._res.state
385. 'expired' == this._res.state
386. 'paid' == this._res.state)
387. if ('paid' == this._res.state
388. this._auth == 0) {
389. if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
390. this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
391. if (this._res.payments[this._res.payments.length - 1].state == 'approved'
392. } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
393. || (responseCode == '1'
394. instrumentType == 'vietqr'))
395. if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required') {
396. } else if (this._res.payments[this._res.payments.length - 1].state == 'pending') {
397. } else if (this._res.payments[this._res.payments.length - 1].state == 'canceled') {
398. this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
399. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id == 'vietqr'
400. } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
401. this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
402. } else if (idBrand == 'atm'
403. if ('paid' == this._res.state) {
404. this._res.merchant.token_site == 'onepay')) {
405. this._res.payments == null) {
406. this._res.payments[this._res.payments.length - 1].state == 'pending') {
407. if (this._res.currencies[0] == 'USD') {
408. if (item.instrument.issuer.brand.id == 'atm') {
409. } else if (item.instrument.issuer.brand.id == 'visa'
410. item.instrument.issuer.brand.id == 'mastercard') {
411. if (item.instrument.issuer_location == 'd') {
412. } else if (item.instrument.issuer.brand.id == 'amex') {
413. } else if (item.instrument.issuer.brand.id == 'jcb') {
414. this.tokenList.length == 1) {
415. if (_val == 2
416. if (this.type == 4) {
417. return (this.currentMethod == 9) // PTTT riêng VietQR
418. if (data._locale == 'en') {
419. screen=='qr'"
420. screen=='confirm_close'"
421. this.themeColor.deeplink_status == 'Off' ? false : true
422. if (item.available == true) {
423. if (appcode == 'grabpay'
424. appcode == 'momo') {
425. if (type == 2
426. err.error.code == '04') {
427. if (this.locale == 'vi') {
428. data['type'] == 'Visa'
429. data['type'] == 'Master'
430. data['type'] == 'JCB'"
431. data['type'] == 'Visa'"
432. data['type'] == 'Master'"
433. data['type'] == 'Amex'"
434. token_main == '1'"
435. if (message == '0') {
436. item.id == element.id ? element['active'] = true : element['active'] = false
437. item.display_cvv == true ? this.flagToken = true : this.flagToken = false
438. if (result == 'success') {
439. if (this.tokenList.length == 0) {
440. } else if (result == 'error') {
441. _val.value.length == 4) || (item.brand_id != 'amex'
442. _val.value.length == 3))
443. _val.value.length == 3)));
444. id == 'amex' ? this.maxLength = 4 : this.maxLength = 3
445. if (_re.body.state == 'more_info_required') {
446. if (this._res_post.state == 'failed') {
447. } else if (_re.body.state == 'authorization_required') {
448. this._b == 18
449. this._b == 5
450. this._b == 12
451. } else if (_re.body.state == 'failed') {
452. if (action == 'blur') {
453. this._i_token_otp.length == 4)
454. this._i_token_otp.length == 3));
455. if (banksRes?.status == 200
456. if (appRes?.status == 200
457. vietQRData?.state=='created' else state_failed"
458. if (res.status == '200'
459. res.status == '201') {
460. if (res.body?.state == 'created'){
461. if (payment?.state == 'failed'
462. payment?.state == 'canceled'
463. payment?.state == 'expired') {
464. return ((a.id == id
465. a.code == id) && a.type.includes(type));
466. if (isIphone == true) {
467. } else if (isAndroid == true) {
468. return countPayment == maxPayment
469. if (this.getLatestPayment().state == 'canceled')
470. if (res?.state == 'canceled') {
471. state == 'authorization_required'
472. } else if (latestPayment?.state == "failed") {
473. if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
474. else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';
475. if (e.name == bankSwift) { // TODO: get by swift
476. return this.apps.find(e => e.code == appCode);
477. if (+e.id == bankId) {
478. if (e.swiftCode == bankSwift) {
479. if (currency == 'USD') {
480. if (this.checkCount == 1) {
481. if (results == null) {
482. if (c.length == 3) {
483. d = d == undefined ? '.' : d
484. t = t == undefined ? '
485. return results == null ? null : results[1]
486. if (((_val.length == 4
487. _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
488. _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
489. iss_date.length == 4
490. iss_date.search('/') == -1)
491. iss_date.length == 5))
492. if (_dataCache == null) {
493. if ( (0 <= r && r <= 6 && (c == 0
494. c == 6) )
495. || (0 <= c && c <= 6 && (r == 0
496. r == 6) )
497. if (i == 0
498. _modules[r][6] = (r % 2 == 0);
499. _modules[6][c] = (c % 2 == 0);
500. if (r == -2
501. r == 2
502. c == -2
503. c == 2
504. || (r == 0
505. c == 0) ) {
506. ( (bits >> i) & 1) == 1);
507. if (col == 6) col -= 1;
508. if (_modules[row][col - c] == null) {
509. dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
510. if (bitIndex == -1) {
511. margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
512. if (typeof arguments[0] == 'object') {
513. margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
514. if (b == -1) throw 'eof';
515. if (b0 == -1) break;
516. if (typeof b == 'number') {
517. if ( (b & 0xff) == b) {
518. return function(i, j) { return (i + j) % 2 == 0
519. return function(i, j) { return i % 2 == 0
520. return function(i, j) { return j % 3 == 0
521. return function(i, j) { return (i + j) % 3 == 0
522. return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
523. return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
524. return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
525. return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
526. if (r == 0
527. c == 0) {
528. if (dark == qrcode.isDark(row + r, col + c) ) {
529. if (count == 0
530. count == 4) {
531. if (typeof num.length == 'undefined') {
532. num[offset] == 0) {
533. if (typeof rsBlock == 'undefined') {
534. return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
535. _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
536. if (data.length - i == 1) {
537. } else if (data.length - i == 2) {
538. } else if (n == 62) {
539. } else if (n == 63) {
540. if (_buflen == 0) {
541. if (c == '=') {
542. } else if (c == 0x2b) {
543. } else if (c == 0x2f) {
544. if (table.size() == (1 << bitLength) ) {

!== (30 điều kiện duy nhất):
------------------------------------------------------------
1. this.lastValue !== $event.target.value)) {
2. if (YY % 400 === 0 || (YY % 100 !== 0
3. if (YYYY % 400 === 0 || (YYYY % 100 !== 0
4. event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
5. key !== '3') {
6. this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
7. codeResponse.toString() !== '0') {
8. cardNo.length !== 0) {
9. if (this.cardTypeBank !== 'bank_card_number') {
10. if (this.cardTypeBank !== 'bank_account_number') {
11. if (this.cardTypeBank !== 'bank_username') {
12. if (this.cardTypeBank !== 'bank_customer_code') {
13. this.lb_card_account !== this._translate.instant('ocb_account')) {
14. this.lb_card_account !== this._translate.instant('ocb_phone')) {
15. this.lb_card_account !== this._translate.instant('ocb_card')) {
16. this._b !== 18) || (this.cardTypeOcean === 'ATM'
17. this._b !== 18) || (this._b == 18)) {
18. key !== '8') {
19. codeResponse.toString() !== '0'){
20. event.inputType !== 'deleteContentBackward') || v.length == 5) {
21. if (deviceValue !== 'default') {
22. this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
23. return this._i_country_code !== 'default'
24. this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
25. this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {
26. if (_val !== 3) {
27. this._util.getElementParams(this.currentUrl, 't_id') !== '') {
28. queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
29. if (queryString !== '') {
30. if (target !== 0

!= (195 điều kiện duy nhất):
------------------------------------------------------------
1. if (params['locale'] != null
2. } else if (params['locale'] != null
3. if (userLang != null
4. if(value!=null){
5. if (_re != null
6. _re.links != null
7. _re.links.merchant_return != null
8. _re.links.merchant_return.href != null) {
9. } else if (_re.body != null
10. _re.body.links != null
11. _re.body.links.merchant_return != null
12. _re.body.links.merchant_return.href != null) {
13. if (this._idInvoice != null
14. this._idInvoice != 0) {
15. if (this._paymentService.getInvoiceDetail() != null) {
16. dataPassed.body != null) {
17. dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
18. if (this._translate.currentLang != language) {
19. } else if (this._b != 18) {
20. if (this.htmlDesc != null
21. if (ua.indexOf('safari') != -1
22. if (_val.value != '') {
23. this.auth_method != null) {
24. if (this.valueDate.length != 3) {
25. if (_formCard.exp_date != null
26. if (this.cardName != null
27. if (this._res_post.links != null
28. this._res_post.links.merchant_return != null
29. this._res_post.links.merchant_return.href != null) {
30. if (this._res_post.authorization != null
31. this._res_post.authorization.links != null
32. this._res_post.authorization.links.approval != null) {
33. this._res_post.links.cancel != null) {
34. this._b != 27
35. this._b != 12
36. this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
37. this._b != 18)
38. if (this._b != 18
39. this._b != 19) {
40. if (this._res.links != null
41. this._res.links.merchant_return != null
42. this._res.links.merchant_return.href != null) {
43. if (!(_formCard.otp != null
44. if (!(_formCard.password != null
45. if (this._inExpDate.length != 3) {
46. if (this._b != 9
47. this._b != 16
48. this._b != 17
49. this._b != 25
50. this._b != 44
51. this._b != 57
52. this._b != 59
53. this._b != 61
54. this._b != 63
55. this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
56. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73','74','75'].indexOf(this._b.toString()) != -1) {
57. if (this._res_post.return_url != null) {
58. let userName = _formCard.name != null ? _formCard.name : ''
59. this._res_post.authorization.links.approval != null
60. this._res_post.authorization.links.approval.href != null) {
61. userName = paramUserName != null ? paramUserName : ''
62. this._b != 11
63. this._b != 20
64. this._b != 33
65. this._b != 39
66. this._b != 43
67. this._b != 45
68. this._b != 64
69. this._b != 67
70. this._b != 68
71. this._b != 72
72. this._b != 73
73. this._b != 36
74. this._b != 74
75. this._b != 75)
76. if (params['locale'] != null) {
77. if ('otp' != this._paymentService.getCurrentPage()) {
78. this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
79. if (!(strInstrument != null
80. if (strInstrument.substring(0, 1) != '^'
81. strInstrument.substr(strInstrument.length - 1) != '$') {
82. if (bankid != null) {
83. _showAVS!=true"
84. } else if (this._res_post.links != null
85. cardNo != null
86. v != null
87. this.c_csc = (!(_val.value != null
88. this._i_csc != null
89. feeService['atm']['fee'] != 0) || atm != 0"></span>
90. (feeService['atm'] && feeService['atm']['fee'] != 0) || atm != 0
91. feeService['qr']['fee'] != 0"
92. feeService['vietqr']['fee'] != 0"
93. feeService['visa_mastercard_d']['fee'] != 0"
94. feeService['amex_d']['fee'] != 0"
95. feeService['pp']['fee'] != 0"
96. this._paymentService.getState() != 'error') {
97. if (this._paymentService.getCurrentPage() != 'otp') {
98. _re.body != null) {
99. this._res_polling.links != null
100. this._res_polling.links.merchant_return != null //
101. } else if (this._res_polling.merchant != null
102. this._res_polling.merchant_invoice_reference != null
103. } else if (this._res_polling.payments != null
104. this._res_polling.links.merchant_return != null//
105. this._res_polling.payments != null
106. if (this._res_polling.payments != null
107. t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
108. this.type.toString().length != 0) {
109. this._res.links != null
110. if (count != 1) {
111. if (this._res.merchant != null
112. this._res.merchant_invoice_reference != null) {
113. if (this._res.merchant.address_details != null) {
114. this._res.links != null//
115. } else if (this._res.payments != null
116. this._res.payments[this._res.payments.length - 1].instrument != null
117. this._res.payments[this._res.payments.length - 1].instrument.issuer != null
118. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
119. if (this.type != 9) {
120. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
121. this._res.payments[this._res.payments.length - 1].links != null
122. this._res.payments[this._res.payments.length - 1].links.cancel != null
123. this._res.payments[this._res.payments.length - 1].links.cancel.href != null
124. this._res.payments[this._res.payments.length - 1].links.update != null
125. this._res.payments[this._res.payments.length - 1].links.update.href != null) {
126. this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
127. this._res.payments[this._res.payments.length - 1].authorization != null
128. this._res.payments[this._res.payments.length - 1].authorization.links != null) {
129. this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
130. this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
131. if (this._res.payments[this._res.payments.length - 1].authorization != null
132. this._res.payments[this._res.payments.length - 1].authorization.links != null
133. auth = paramUserName != null ? paramUserName : ''
134. this._res.links.merchant_return != null //
135. } else if (this._res.merchant != null
136. this._res.merchant_invoice_reference != null
137. if (['mastercard', 'visa', 'amex', 'jcb', 'cup', 'card', 'np_card', 'atm'].indexOf(id) != -1) {
138. } else if (['techcombank_account', 'vib_account', 'dongabank_account', 'tpbank_account', 'bidv_account', 'pvcombank_account', 'shb_account', 'oceanbank_online_account'].indexOf(id) != -1) {
139. } else if (['oceanbank_mobile_account'].indexOf(id) != -1) {
140. } else if (['shb_customer_id'].indexOf(id) != -1) {
141. if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1) {
142. return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
143. this.currentMethod != selected) {
144. } else if (this._res.payments != null) {
145. _re.body?.state != 'canceled')
146. 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {
147. if (appcode != null) {
148. if (_re.status != '200'
149. _re.status != '201')
150. if (this.translate.currentLang != language) {
151. item['feeService']['fee'] != 0
152. item['feeService']['fee'] != 0"
153. this._res.merchant.id != 'OPTEST' ? this._res.on_off_bank : ''
154. if (this.currentMethod != 9) {
155. if (_val.value != null
156. this.c_token_otp_csc = !(_val.value != null
157. if (_re.body.authorization != null
158. _re.body.authorization.links != null
159. if (_re.body.links != null
160. _re.body.links.cancel != null) {
161. if (_re.body.return_url != null) {
162. } else if (_re.body.links != null
163. if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(item.brand_id) != -1
164. return this._i_token_otp != null
165. || (id != 'amex'
166. if (idInvoice != null
167. idInvoice != 0)
168. idInvoice != 0) {
169. if (this._merchantid != null
170. this._tranref != null
171. this._state != null
172. let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
173. urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
174. if (paymentId != null) {
175. if (fee != null) {
176. if (res?.status != 200
177. res?.status != 201) return;
178. _re.status != '201') {
179. latestPayment?.state != "authorization_required") {
180. if (latestPayment?.instrument?.type != "vietqr") {
181. if (tem != null) {
182. if ((tem = ua.match(/version\/(\d+)/i)) != null) {
183. if (v.length != 3) {
184. if (_modules[r][6] != null) {
185. if (_modules[6][c] != null) {
186. if (_modules[row][col] != null) {
187. while (buffer.getLengthInBits() % 8 != 0) {
188. if (count != numChars) {
189. throw count + ' != ' + numChars
190. while (data != 0) {
191. if (test.length != 2
192. ( (test[0] << 8) | test[1]) != code) {
193. if (_length % 3 != 0) {
194. if ( (data >>> length) != 0) {
195. return typeof _map[key] != 'undefined'

