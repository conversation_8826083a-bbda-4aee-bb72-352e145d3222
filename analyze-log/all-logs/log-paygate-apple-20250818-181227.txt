====================================================================================================
TRÍCH XUẤT ĐIỀU KIỆN SO SÁNH TỪ CÁC FILE TYPESCRIPT/JAVASCRIPT/HTML
====================================================================================================
Thư mục/File nguồn: /root/projects/onepay/paygate-apple/src
Thời gian: 18:12:27 18/8/2025
Tổng số file xử lý: 134
Tổng số file bị bỏ qua: 0
Tổng số điều kiện tìm thấy: 1541

THỐNG KÊ TỔNG QUAN THEO LOẠI TOÁN TỬ:
--------------------------------------------------
Strict equality (===): 276 lần
Loose equality (==): 875 lần
Strict inequality (!==): 62 lần
Loose inequality (!=): 328 lần
--------------------------------------------------

DANH SÁCH FILE ĐÃ XỬ LÝ:
--------------------------------------------------
1. 4en.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/4en.html
2. 4vn.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/4vn.html
3. app-routing.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/app-routing.module.ts
4. app.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/app.component.html
5. app.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/app.component.spec.ts
6. app.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/app.component.ts
7. app.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/app.module.ts
8. counter.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/counter.directive.spec.ts
9. counter.directive.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/counter.directive.ts
10. format-carno-input.derective.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/directives/format-carno-input.derective.ts
11. uppercase-input.directive.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/directives/uppercase-input.directive.ts
12. error.component.html (17 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/error/error.component.html
13. error.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/error/error.component.spec.ts
14. error.component.ts (39 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/error/error.component.ts
15. support-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/error/support-dialog/support-dialog.html
16. support-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/error/support-dialog/support-dialog.ts
17. format-date.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/format-date.directive.spec.ts
18. format-date.directive.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/format-date.directive.ts
19. main.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/main.component.html
20. main.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/main.component.spec.ts
21. main.component.ts (13 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/main.component.ts
22. app-result.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/app-result/app-result.component.html
23. app-result.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/app-result/app-result.component.ts
24. bank-amount-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
25. bank-amount-dialog.component.ts (31 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
26. amigo-form.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/amigo/amigo-form.component.html
27. amigo-form.component.ts (30 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/amigo/amigo-form.component.ts
28. bnpl-main.component.html (16 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/bnpl-main.component.html
29. bnpl-main.component.ts (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/bnpl-main.component.ts
30. circle-process-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/dialog/circle-process-dialog.html
31. dialog-delete-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/homecredit/dialog-delete-dialog.html
32. dialog-policy.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/homecredit/dialog-policy/dialog-policy.component.html
33. dialog-policy.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/homecredit/dialog-policy/dialog-policy.component.spec.ts
34. dialog-policy.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/homecredit/dialog-policy/dialog-policy.component.ts
35. homecredit-form.component.html (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/homecredit/homecredit-form.component.html
36. homecredit-form.component.ts (35 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/homecredit/homecredit-form.component.ts
37. dialog-delete-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/kbank/dialog-delete-dialog.html
38. kbank-form.component.html (7 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/kbank/kbank-form.component.html
39. kbank-form.component.ts (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/kbank/kbank-form.component.ts
40. kredivo-form.component.html (10 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/kredivo/kredivo-form.component.html
41. kredivo-form.component.ts (21 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/kredivo/kredivo-form.component.ts
42. otp-auth-bnpl.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/otp-auth-bnpl/otp-auth-bnpl.component.html
43. otp-auth-bnpl.component.ts (16 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/otp-auth-bnpl/otp-auth-bnpl.component.ts
44. process-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/process-dialog/process-dialog.html
45. process-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/process-dialog/process-dialog.ts
46. select-bnpl.html (10 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/select-bnpl-dialog/select-bnpl.html
47. select-bnpl.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/select-bnpl-dialog/select-bnpl.ts
48. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/dialog-guide-dialog.html
49. bankaccount.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
50. bankaccount.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
51. bankaccount.component.ts (156 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
52. bank.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/model/bank.ts
53. onepay-napas.component.html (7 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.html
54. onepay-napas.component.ts (105 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.ts
55. otp-auth.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
56. otp-auth.component.ts (28 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
57. shb.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/shb/shb.component.html
58. shb.component.ts (69 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/shb/shb.component.ts
59. techcombank.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
60. techcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
61. techcombank.component.ts (17 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
62. vibbank.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.html
63. vibbank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.spec.ts
64. vibbank.component.ts (102 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.ts
65. vietcombank.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
66. vietcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
67. vietcombank.component.ts (111 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
68. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
69. off-bank-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
70. off-bank-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
71. policy-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/dialog/policy-dialog/policy-dialog.component.html
72. policy-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/dialog/policy-dialog/policy-dialog.component.ts
73. domescard-main.component.html (66 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/domescard-main.component.html
74. domescard-main.component.ts (141 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/domescard-main.component.ts
75. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/intercard-main/dialog-guide-dialog.html
76. intercard-main-form.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/intercard-main/form/intercard-main-form.component.html
77. intercard-main-form.component.ts (74 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/intercard-main/form/intercard-main-form.component.ts
78. intercard-main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/intercard-main/intercard-main.component.html
79. intercard-main.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/intercard-main/intercard-main.component.ts
80. menu.component.html (10 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/menu.component.html
81. menu.component.ts (144 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/menu.component.ts
82. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
83. qr-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
84. qr-dialog.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
85. qr-main.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/qr-main/qr-main.component.html
86. qr-main.component.ts (27 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/qr-main/qr-main.component.ts
87. safe-html.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/qr-main/safe-html.pipe.ts
88. bnpl-form.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/sorting-option/bnpl-form/bnpl-form.component.html
89. bnpl-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/sorting-option/bnpl-form/bnpl-form.component.ts
90. domescard-form.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.html
91. domescard-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.ts
92. intercard-form.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.html
93. intercard-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.ts
94. paypal-form.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.html
95. paypal-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.ts
96. qr-form.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/sorting-option/qr-form/qr-form.component.html
97. qr-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/sorting-option/qr-form/qr-form.component.ts
98. bnpl-management.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/model/bnpl-management.ts
99. homecredit-management.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/model/homecredit-management.ts
100. kbank-management.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/model/kbank-management.ts
101. overlay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/overlay/overlay.component.html
102. overlay.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/overlay/overlay.component.spec.ts
103. overlay.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/overlay/overlay.component.ts
104. bank-amount.pipe.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/pipe/bank-amount.pipe.ts
105. auth.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/services/auth.service.ts
106. close-dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/services/close-dialog.service.ts
107. data.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/services/data.service.ts
108. deep_link.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/services/deep_link.service.ts
109. dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/services/dialog.service.ts
110. handle_bnpl_token.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/services/handle_bnpl_token.service.ts
111. multiple_method.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/services/multiple_method.service.ts
112. payment.service.ts (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/services/payment.service.ts
113. success.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/success/success.component.html
114. success.component.ts (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/success/success.component.ts
115. index.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/translate/index.ts
116. lang-en.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/translate/lang-en.ts
117. lang-vi.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/translate/lang-vi.ts
118. translate.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/translate/translate.pipe.ts
119. translate.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/translate/translate.service.ts
120. translations.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/translate/translations.ts
121. apps-info.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/util/apps-info.ts
122. banks-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/util/banks-info.ts
123. iso-ca-states.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/util/iso-ca-states.ts
124. iso-us-states.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/util/iso-us-states.ts
125. util.ts (40 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/app/util/util.ts
126. qrcode.js (67 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/assets/script/qrcode.js
127. environment.dev.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/environments/environment.dev.ts
128. environment.mtf.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/environments/environment.mtf.ts
129. environment.prod.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/environments/environment.prod.ts
130. environment.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/environments/environment.ts
131. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/index.html
132. main.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/main.ts
133. polyfills.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/polyfills.ts
134. test.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-apple/src/test.ts

====================================================================================================
CHI TIẾT TỪNG FILE:
====================================================================================================

📁 FILE 1: 4en.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/4en.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 2: 4vn.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/4vn.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 3: app-routing.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/app-routing.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 4: app.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/app.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 5: app.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/app.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 6: app.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/app.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 77] return lang === this._translate.currentLang

== (2 điều kiện):
  1. [Dòng 57] 'vi' == params['locale']) {
  2. [Dòng 59] 'en' == params['locale']) {

!= (3 điều kiện):
  1. [Dòng 57] if (params['locale'] != null
  2. [Dòng 59] } else if (params['locale'] != null
  3. [Dòng 65] if (userLang != null

================================================================================

📁 FILE 7: app.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/app.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 8: counter.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/counter.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 9: counter.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/counter.directive.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 10: format-carno-input.derective.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/directives/format-carno-input.derective.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 44] this.lastValue !== $event.target.value)) {

!= (1 điều kiện):
  1. [Dòng 23] if(value!=null){

================================================================================

📁 FILE 11: uppercase-input.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/directives/uppercase-input.directive.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 0 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 23] this.lastValue !== $event.target.value)) {

================================================================================

📁 FILE 12: error.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/error/error.component.html
📊 Thống kê: 17 điều kiện duy nhất
   - === : 4 lần
   - == : 11 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 58] checkDupTran === false"
  2. [Dòng 102] isPopupSupport === 'True') || (rePayment
  3. [Dòng 103] isPopupSupport === 'True'"
  4. [Dòng 109] isPopupSupport === 'True')">

== (11 điều kiện):
  1. [Dòng 3] errorCode == '11'"
  2. [Dòng 29] errorCode && (errorCode == '253' || errorCode == 'overtime')
  3. [Dòng 29] errorCode == 'overtime')">
  4. [Dòng 102] <div class="footer-button" *ngIf="(isSent == false
  5. [Dòng 103] isSent == false
  6. [Dòng 109] <div class="payment_again" *ngIf="rePayment && !timeOut" [class.select_only]="!(isSent == false
  7. [Dòng 115] <div class="cancel_transaction" *ngIf="checkDupTran" [class.select_only]="!(isSent == false
  8. [Dòng 128] errorCode == 'overtime'
  9. [Dòng 128] errorCode == '253'"
  10. [Dòng 130] <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
  11. [Dòng 130] !(errorCode == 'overtime' || errorCode == '253') && isBack

!= (2 điều kiện):
  1. [Dòng 21] errorCode != '253'
  2. [Dòng 21] errorCode != 'overtime'"

================================================================================

📁 FILE 13: error.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/error/error.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 14: error.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/error/error.component.ts
📊 Thống kê: 39 điều kiện duy nhất
   - === : 9 lần
   - == : 23 lần
   - !== : 0 lần
   - != : 7 lần
--------------------------------------------------------------------------------

=== (9 điều kiện):
  1. [Dòng 163] params.timeout === 'true') {
  2. [Dòng 172] params.kbank === 'true') {
  3. [Dòng 189] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  4. [Dòng 189] _re.body.state === 'unpaid');
  5. [Dòng 275] if (this.errorCode === 'overtime'
  6. [Dòng 275] this.errorCode === '253') {
  7. [Dòng 368] params.name === 'CUSTOMER_INTIME'
  8. [Dòng 368] params.code === '09') {
  9. [Dòng 449] if (this.timeLeft === 0) {

== (23 điều kiện):
  1. [Dòng 147] if (params && (params['bnpl'] == 'true')) {
  2. [Dòng 151] if (params && (params['bnpl'] == 'false')) {
  3. [Dòng 197] if (this.res.currencies[0] == 'USD') {
  4. [Dòng 225] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'amigo') {
  5. [Dòng 232] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'kredivo') {
  6. [Dòng 249] this.res.themes.theme == 'general') {
  7. [Dòng 255] params.response_code == 'overtime') {
  8. [Dòng 298] if (_re.status == '200'
  9. [Dòng 298] _re.status == '201') {
  10. [Dòng 311] if (_re2.status == '200'
  11. [Dòng 311] _re2.status == '201') {
  12. [Dòng 328] if (this.errorCode == 'overtime'
  13. [Dòng 328] this.errorCode == '253') {
  14. [Dòng 333] if (this.paymentInformation.type == 'bnpl') {
  15. [Dòng 335] if (this.paymentInformation.provider == 'amigo'
  16. [Dòng 335] this.errorCode == '2') {
  17. [Dòng 338] else if (this.paymentInformation.provider == 'kbank'
  18. [Dòng 341] else if (this.paymentInformation.provider == 'homecredit'
  19. [Dòng 344] else if (this.paymentInformation.provider == 'kredivo'
  20. [Dòng 354] this.res.state == 'canceled') {
  21. [Dòng 447] if (this.isTimePause == false) {
  22. [Dòng 527] if (this.url_new_invoice != null && (_re.status == 201
  23. [Dòng 527] _re.status == 200)) {

!= (7 điều kiện):
  1. [Dòng 223] } else if (this.res.payments[this.res.payments.length - 1].instrument.issuer.brand != null
  2. [Dòng 224] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id != null
  3. [Dòng 511] if (_re.body != null
  4. [Dòng 511] _re.body.links != null
  5. [Dòng 511] _re.body.links.merchant_return != null
  6. [Dòng 512] _re.body.links.merchant_return.href != null) {
  7. [Dòng 527] if (this.url_new_invoice != null

================================================================================

📁 FILE 15: support-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/error/support-dialog/support-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 16: support-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/error/support-dialog/support-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 17: format-date.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/format-date.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 18: format-date.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/format-date.directive.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0
  2. [Dòng 123] YY % 4 === 0)) {
  3. [Dòng 138] if (YYYY % 400 === 0
  4. [Dòng 138] YYYY % 4 === 0)) {

!== (2 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0 || (YY % 100 !== 0
  2. [Dòng 138] if (YYYY % 400 === 0 || (YYYY % 100 !== 0

================================================================================

📁 FILE 19: main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/main.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 1 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 28] checkDupTran === false"

================================================================================

📁 FILE 20: main.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/main.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 21: main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/main.component.ts
📊 Thống kê: 13 điều kiện duy nhất
   - === : 2 lần
   - == : 5 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 123] params['code'] === '09') {
  2. [Dòng 137] params.name === 'CUSTOMER_INTIME')) {

== (5 điều kiện):
  1. [Dòng 90] if ((dataPassed.status == '200'
  2. [Dòng 90] dataPassed.status == '201') && dataPassed.body != null) {
  3. [Dòng 94] dataPassed.body.themes.logo_full == 'True') {
  4. [Dòng 114] payments[payments.length - 1].state == 'pending'
  5. [Dòng 116] payments[payments.length - 1].instrument.issuer.brand.id == 'amigo') {

!= (6 điều kiện):
  1. [Dòng 81] if (this._idInvoice != null
  2. [Dòng 81] this._idInvoice != 0) {
  3. [Dòng 82] if (this._paymentService.getInvoiceDetail() != null) {
  4. [Dòng 90] dataPassed.body != null) {
  5. [Dòng 108] dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
  6. [Dòng 173] if (this._translate.currentLang != language) {

================================================================================

📁 FILE 22: app-result.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/app-result/app-result.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 23: app-result.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/app-result/app-result.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 24: bank-amount-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 25: bank-amount-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
📊 Thống kê: 31 điều kiện duy nhất
   - === : 0 lần
   - == : 31 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (31 điều kiện):
  1. [Dòng 40] if (this.locale == 'en') {
  2. [Dòng 54] if (bankId == 3
  3. [Dòng 54] bankId == 61
  4. [Dòng 55] bankId == 8
  5. [Dòng 55] bankId == 49
  6. [Dòng 56] bankId == 48
  7. [Dòng 57] bankId == 10
  8. [Dòng 57] bankId == 53
  9. [Dòng 58] bankId == 17
  10. [Dòng 58] bankId == 65
  11. [Dòng 59] bankId == 23
  12. [Dòng 59] bankId == 52
  13. [Dòng 60] bankId == 27
  14. [Dòng 60] bankId == 66
  15. [Dòng 61] bankId == 9
  16. [Dòng 61] bankId == 54
  17. [Dòng 62] bankId == 37
  18. [Dòng 63] bankId == 38
  19. [Dòng 64] bankId == 39
  20. [Dòng 65] bankId == 40
  21. [Dòng 66] bankId == 42
  22. [Dòng 67] bankId == 44
  23. [Dòng 68] bankId == 72
  24. [Dòng 69] bankId == 59
  25. [Dòng 72] bankId == 51
  26. [Dòng 73] bankId == 64
  27. [Dòng 74] bankId == 58
  28. [Dòng 75] bankId == 56
  29. [Dòng 78] bankId == 55
  30. [Dòng 79] bankId == 60
  31. [Dòng 80] bankId == 68 //Eximbank Napas

================================================================================

📁 FILE 26: amigo-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/amigo/amigo-form.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 31] bnplDetail.method == 'SP'"
  2. [Dòng 49] bnplDetail.method == 'PL'"

!= (2 điều kiện):
  1. [Dòng 45] bnplDetail.method != 'SP'"
  2. [Dòng 63] bnplDetail.method != 'PL'"

================================================================================

📁 FILE 27: amigo-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/amigo/amigo-form.component.ts
📊 Thống kê: 30 điều kiện duy nhất
   - === : 2 lần
   - == : 23 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 177] return index === array.findIndex(obj => {
  2. [Dòng 178] return JSON.stringify(obj) === _value

== (23 điều kiện):
  1. [Dòng 77] _re.code == '0'
  2. [Dòng 108] packageItem.product_code == productCode) {
  3. [Dòng 126] a.product_code == 'SP') {
  4. [Dòng 139] a.product_code == 'PL') {
  5. [Dòng 188] if (this.selectedIndex == 0
  6. [Dòng 192] } else if ((this.selectedIndex == 1
  7. [Dòng 192] this.payLaterSubmit) || (this.selectedIndex == 0
  8. [Dòng 195] item.prepaid_percent == this.selectedPrePaid
  9. [Dòng 195] item.installment_month == this.selectedPayLater) {
  10. [Dòng 228] if (string == 'SP') {
  11. [Dòng 231] } else if (string == 'PL') {
  12. [Dòng 274] if(this._locale == 'en'){
  13. [Dòng 284] if (this._res_post.state == 'approved'
  14. [Dòng 284] this._res_post.state == 'failed') {
  15. [Dòng 290] if (this._res_post.state == 'failed') {
  16. [Dòng 294] && (_re.body.payments[_re.body.payments.length - 1].reason.code == '8'
  17. [Dòng 295] _re.body.payments[_re.body.payments.length - 1].reason.code == '9'
  18. [Dòng 296] _re.body.payments[_re.body.payments.length - 1].reason.code == '12'
  19. [Dòng 297] _re.body.payments[_re.body.payments.length - 1].reason.code == '13'
  20. [Dòng 298] _re.body.payments[_re.body.payments.length - 1].reason.code == '24'
  21. [Dòng 299] _re.body.payments[_re.body.payments.length - 1].reason.code == '25')) {
  22. [Dòng 320] } else if (this._res_post.state == 'authorization_required') {
  23. [Dòng 321] if ('REDIRECT' == this._res_post.authorization.links.approval.method) {

!= (5 điều kiện):
  1. [Dòng 215] 'total_paid': this.data?.amount ? (item.diff_amount !=0 ? this._util.formatMoney(parseFloat(item.diff_amount) + parseFloat(this.data?.amount), 0, '.', ',') : this._util.formatMoney(parseFloat(this.data?.amount), 0, '.', ',')) :0,
  2. [Dòng 285] if (this._res_post.return_url != null) {
  3. [Dòng 287] } else if (this._res_post.links != null
  4. [Dòng 287] this._res_post.links.merchant_return != null
  5. [Dòng 287] this._res_post.links.merchant_return.href != null) {

================================================================================

📁 FILE 28: bnpl-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/bnpl-main.component.html
📊 Thống kê: 16 điều kiện duy nhất
   - === : 1 lần
   - == : 13 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 24] bnpl.code === 'kbank'"

== (13 điều kiện):
  1. [Dòng 21] bnpl.status == 'disabled'"
  2. [Dòng 22] bnpl.status == 'active'"
  3. [Dòng 22] [class.bnpl-active-item]="bnpl.status == 'active'" (click)="onClick(bnpl.status == 'disabled')">
  4. [Dòng 28] bnpl.code == 'kbank'
  5. [Dòng 31] bnpl.code == 'insta'"
  6. [Dòng 35] bnpl.code == 'homepaylater'"
  7. [Dòng 36] bnpl.status == 'disabled'
  8. [Dòng 44] bnpl.code == 'kredivo'
  9. [Dòng 59] selectedBnpl.code == 'insta'"
  10. [Dòng 65] selectedBnpl.code == 'kbank'"
  11. [Dòng 71] selectedBnpl.code == 'homepaylater'"
  12. [Dòng 75] selectedBnpl.code == 'kredivo'"
  13. [Dòng 140] _auth == 1"

!== (1 điều kiện):
  1. [Dòng 27] bnpl.code !== 'kbank'"

!= (1 điều kiện):
  1. [Dòng 1] _auth != 1"

================================================================================

📁 FILE 29: bnpl-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/bnpl-main.component.ts
📊 Thống kê: 8 điều kiện duy nhất
   - === : 6 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (6 điều kiện):
  1. [Dòng 124] this.bnpls.push(this.sublist.find(e => e.name === 'KBank Pay Later'));
  2. [Dòng 127] this.bnpls.push(this.sublist.find(e => e.name === 'Home PayLater'));
  3. [Dòng 130] this.bnpls.push(this.sublist.find(e => e.name === 'Kredivo'));
  4. [Dòng 133] this.bnpls.push(this.sublist.find(e => e.name === 'Insta'));
  5. [Dòng 148] return index === array.findIndex(obj => {
  6. [Dòng 149] return JSON.stringify(obj) === _value

== (2 điều kiện):
  1. [Dòng 157] item.prepaid_percent == this.selectedPrePaid
  2. [Dòng 157] item.installment_month == this.selectedPayLater) {

================================================================================

📁 FILE 30: circle-process-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/dialog/circle-process-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 31: dialog-delete-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/homecredit/dialog-delete-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 32: dialog-policy.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/homecredit/dialog-policy/dialog-policy.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 33: dialog-policy.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/homecredit/dialog-policy/dialog-policy.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 34: dialog-policy.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/homecredit/dialog-policy/dialog-policy.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 35: homecredit-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/homecredit/homecredit-form.component.html
📊 Thống kê: 8 điều kiện duy nhất
   - === : 2 lần
   - == : 5 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 150] object.id === value ? 'select-option-active' : ''"
  2. [Dòng 152] object.id === value"

== (5 điều kiện):
  1. [Dòng 163] value == 'add' ? 'select-option-active' : ''"
  2. [Dòng 164] value == 'add'"
  3. [Dòng 171] value=='add'"
  4. [Dòng 286] value == 'add') || !onepayChecked.value" [disabled]="(isInvalid()
  5. [Dòng 286] value == 'add') || !onepayChecked.value">

!= (1 điều kiện):
  1. [Dòng 209] && ((homecreidtDetail['phoneNumber'].length != 10) || !this.homecreidtDetail['phoneNumber'].startsWith('0'))

================================================================================

📁 FILE 36: homecredit-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/homecredit/homecredit-form.component.ts
📊 Thống kê: 35 điều kiện duy nhất
   - === : 1 lần
   - == : 20 lần
   - !== : 1 lần
   - != : 13 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 260] if (this.value === 'add') {

== (20 điều kiện):
  1. [Dòng 148] if (response.status == '200'
  2. [Dòng 148] response.status == '201') {
  3. [Dòng 151] this.listTokenBnpl.length == 0) {
  4. [Dòng 154] if (this.value == object.id) {
  5. [Dòng 221] if (name == 'email') {
  6. [Dòng 224] if (name == 'phoneNumber') {
  7. [Dòng 227] if (name == 'fullname') {
  8. [Dòng 287] if (this._res_post.state == 'approved'
  9. [Dòng 287] this._res_post.state == 'failed') {
  10. [Dòng 293] if (this._res_post.state == 'failed') {
  11. [Dòng 297] && (_re.body.payments[_re.body.payments.length - 1].reason.code == '8'
  12. [Dòng 298] _re.body.payments[_re.body.payments.length - 1].reason.code == '9'
  13. [Dòng 299] _re.body.payments[_re.body.payments.length - 1].reason.code == '12'
  14. [Dòng 300] _re.body.payments[_re.body.payments.length - 1].reason.code == '13'
  15. [Dòng 301] _re.body.payments[_re.body.payments.length - 1].reason.code == '24'
  16. [Dòng 302] _re.body.payments[_re.body.payments.length - 1].reason.code == '25')) {
  17. [Dòng 323] } else if (this._res_post.state == 'authorization_required') {
  18. [Dòng 324] if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  19. [Dòng 385] this._res_post.state == 'authorization_required') {
  20. [Dòng 455] this._res_post.code == 'KB-02') {

!== (1 điều kiện):
  1. [Dòng 407] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (13 điều kiện):
  1. [Dòng 98] this.value = (this.listTokenBnpl.length != 0) ? this.listTokenBnpl[0]['id'] : "add";
  2. [Dòng 150] this.listTokenBnpl = this.listTokenBnpl.filter(item => item.id != result.id);
  3. [Dòng 288] if (this._res_post.return_url != null) {
  4. [Dòng 290] } else if (this._res_post.links != null
  5. [Dòng 290] this._res_post.links.merchant_return != null
  6. [Dòng 290] this._res_post.links.merchant_return.href != null) {
  7. [Dòng 390] if (this._res_post.authorization != null
  8. [Dòng 390] this._res_post.authorization.links != null
  9. [Dòng 395] if (this._res_post.links != null
  10. [Dòng 395] this._res_post.links.cancel != null) {
  11. [Dòng 402] this._res_post.authorization.links.approval != null
  12. [Dòng 402] this._res_post.authorization.links.approval.href != null) {
  13. [Dòng 405] userName = paramUserName != null ? paramUserName : ''

================================================================================

📁 FILE 37: dialog-delete-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/kbank/dialog-delete-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 38: kbank-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/kbank/kbank-form.component.html
📊 Thống kê: 7 điều kiện duy nhất
   - === : 2 lần
   - == : 5 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 92] object.id === value ? 'select-option-active' : ''"
  2. [Dòng 94] object.id === value"

== (5 điều kiện):
  1. [Dòng 104] value == 'add' ? 'select-option-active' : ''"
  2. [Dòng 105] value == 'add'"
  3. [Dòng 112] value=='add'"
  4. [Dòng 154] !cmndBlur ? 'no-border' : ((kbankDetail['citizen_id'] && !(kbankDetail['citizen_id'].length == 9 || kbankDetail['citizen_id'].length == 12) || !kbankDetail['citizen_id']) &&  bnplForm.controls['citizen_id'].touched) ? 'ng-invalid ng-dirty' : ''
  5. [Dòng 159] bnplForm.controls['citizen_id'].touched && kbankDetail['citizen_id'] && !(kbankDetail['citizen_id'].length == 9 || kbankDetail['citizen_id'].length == 12) && cmndBlur

================================================================================

📁 FILE 39: kbank-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/kbank/kbank-form.component.ts
📊 Thống kê: 22 điều kiện duy nhất
   - === : 0 lần
   - == : 13 lần
   - !== : 1 lần
   - != : 8 lần
--------------------------------------------------------------------------------

== (13 điều kiện):
  1. [Dòng 75] this.value == 'add')){
  2. [Dòng 111] list = this.data.customer.tokens.filter((item) => item.instrument.issuer.brand.id == 'kbank')
  3. [Dòng 153] if (response.status == '200'
  4. [Dòng 153] response.status == '201') {
  5. [Dòng 161] this.listTokenBnpl.length == 0) {
  6. [Dòng 164] if (this.value == object.id) {
  7. [Dòng 184] return (this.bnplForm.invalid || ( this.kbankDetail['citizen_id'] && !(this.kbankDetail['citizen_id'].length == 9) && !(this.kbankDetail['citizen_id'].length == 12)));
  8. [Dòng 224] if (name == 'citizen_id') {
  9. [Dòng 227] if (name == 'phoneNumber') {
  10. [Dòng 230] if (name == 'fullname') {
  11. [Dòng 269] this._res_post.state == 'authorization_required') {
  12. [Dòng 337] this._res_post.code == 'KB-02') {
  13. [Dòng 355] this._res_post.state == 'failed') {

!== (1 điều kiện):
  1. [Dòng 291] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (8 điều kiện):
  1. [Dòng 159] this.listTokenBnpl = this.listTokenBnpl.filter(item => item.id != result.id);
  2. [Dòng 274] if (this._res_post.authorization != null
  3. [Dòng 274] this._res_post.authorization.links != null
  4. [Dòng 279] if (this._res_post.links != null
  5. [Dòng 279] this._res_post.links.cancel != null) {
  6. [Dòng 286] this._res_post.authorization.links.approval != null
  7. [Dòng 286] this._res_post.authorization.links.approval.href != null) {
  8. [Dòng 289] userName = paramUserName != null ? paramUserName : ''

================================================================================

📁 FILE 40: kredivo-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/kredivo/kredivo-form.component.html
📊 Thống kê: 10 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 8 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 17] plan.type=='1'"
  2. [Dòng 46] plan.applicable=='premium_users'"

!= (8 điều kiện):
  1. [Dòng 22] plan.type!='1'"
  2. [Dòng 33] {{plan.type!='1'
  3. [Dòng 33] plan.interest_rate!='0%'? ('/' && ('month' | translate)) : ''}}
  4. [Dòng 41] plan.processing_fee_rate!='0%'? ('/' && ('month' | translate)) : ''}}
  5. [Dòng 45] plan.applicable!='premium_users'"
  6. [Dòng 74] document.activeElement.id!='fullname'"
  7. [Dòng 90] document.activeElement.id!='phoneNumber'"
  8. [Dòng 107] document.activeElement.id!='email'"

================================================================================

📁 FILE 41: kredivo-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/kredivo/kredivo-form.component.ts
📊 Thống kê: 21 điều kiện duy nhất
   - === : 0 lần
   - == : 16 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

== (16 điều kiện):
  1. [Dòng 122] if(this._locale == 'en'){
  2. [Dòng 132] if (this._res_post.state == 'approved'
  3. [Dòng 132] this._res_post.state == 'failed') {
  4. [Dòng 138] if (this._res_post.state == 'failed') {
  5. [Dòng 142] && (_re.body.payments[_re.body.payments.length - 1].reason.code == '8'
  6. [Dòng 143] _re.body.payments[_re.body.payments.length - 1].reason.code == '9'
  7. [Dòng 144] _re.body.payments[_re.body.payments.length - 1].reason.code == '12'
  8. [Dòng 145] _re.body.payments[_re.body.payments.length - 1].reason.code == '13'
  9. [Dòng 146] _re.body.payments[_re.body.payments.length - 1].reason.code == '24'
  10. [Dòng 147] _re.body.payments[_re.body.payments.length - 1].reason.code == '25')) {
  11. [Dòng 168] } else if (this._res_post.state == 'authorization_required') {
  12. [Dòng 169] if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  13. [Dòng 224] "applicable": (type=="6"
  14. [Dòng 224] type=="12")? "premium_users" : "basic_or_premium_users"
  15. [Dòng 259] this.bnplDetail.fullname?.length == 0 ? this._translate.instant('kredivo_empty_fullname_message')
  16. [Dòng 277] this.bnplDetail.email?.length == 0 ? this._translate.instant('kredivo_empty_email_message')

!= (5 điều kiện):
  1. [Dòng 133] if (this._res_post.return_url != null) {
  2. [Dòng 135] } else if (this._res_post.links != null
  3. [Dòng 135] this._res_post.links.merchant_return != null
  4. [Dòng 135] this._res_post.links.merchant_return.href != null) {
  5. [Dòng 268] this.bnplDetail.phoneNumber?.length != 10 ?

================================================================================

📁 FILE 42: otp-auth-bnpl.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/otp-auth-bnpl/otp-auth-bnpl.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 43: otp-auth-bnpl.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/otp-auth-bnpl/otp-auth-bnpl.component.ts
📊 Thống kê: 16 điều kiện duy nhất
   - === : 0 lần
   - == : 5 lần
   - !== : 1 lần
   - != : 10 lần
--------------------------------------------------------------------------------

== (5 điều kiện):
  1. [Dòng 156] if (_re.status == '200'
  2. [Dòng 156] _re.status == '201') {
  3. [Dòng 167] else if (this._res.code == '2') {
  4. [Dòng 209] _re.body.payments[_re.body.payments.length - 1].reason.code == 'B4') {
  5. [Dòng 333] if (this._b == 18) {//8-MB Bank;18-oceanbank

!== (1 điều kiện):
  1. [Dòng 187] codeResponse.toString() !== '0') {

!= (10 điều kiện):
  1. [Dòng 161] if (this._res.links != null
  2. [Dòng 161] this._res.links.merchant_return != null
  3. [Dòng 161] this._res.links.merchant_return.href != null) {
  4. [Dòng 304] if (this._res_post != null
  5. [Dòng 304] this._res_post.links != null
  6. [Dòng 304] this._res_post.links.merchant_return != null
  7. [Dòng 304] this._res_post.links.merchant_return.href != null) {
  8. [Dòng 328] if (!(_formCard.otp != null
  9. [Dòng 334] if (!(_formCard.password != null
  10. [Dòng 350] if (ua.indexOf('safari') != -1

================================================================================

📁 FILE 44: process-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/process-dialog/process-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 45: process-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/process-dialog/process-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 46: select-bnpl.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/select-bnpl-dialog/select-bnpl.html
📊 Thống kê: 10 điều kiện duy nhất
   - === : 0 lần
   - == : 9 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (9 điều kiện):
  1. [Dòng 7] bnpl.status == 'disabled'"
  2. [Dòng 8] bnpl.status == 'active'"
  3. [Dòng 15] bnpl.code == 'kbank'"
  4. [Dòng 20] bnpl.code == 'kbank'
  5. [Dòng 23] bnpl.status == 'disabled'
  6. [Dòng 23] bnpl.code == 'insta'"
  7. [Dòng 27] bnpl.status == 'active'
  8. [Dòng 30] bnpl.code == 'homepaylater'
  9. [Dòng 33] bnpl.code == 'kredivo'

!= (1 điều kiện):
  1. [Dòng 18] bnpl.code != 'kbank'"

================================================================================

📁 FILE 47: select-bnpl.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/bnpl-main/select-bnpl-dialog/select-bnpl.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 28] if (bnpl.status == 'disabled') {

================================================================================

📁 FILE 48: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 49: bankaccount.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 39] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 53] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 53] valueDate.trim().length === 0)"

================================================================================

📁 FILE 50: bankaccount.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 51: bankaccount.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
📊 Thống kê: 156 điều kiện duy nhất
   - === : 47 lần
   - == : 75 lần
   - !== : 13 lần
   - != : 21 lần
--------------------------------------------------------------------------------

=== (47 điều kiện):
  1. [Dòng 113] if (isIE[0] === 'MSIE'
  2. [Dòng 113] +isIE[1] === 10) {
  3. [Dòng 203] if ((_val.value.substr(-1) === ' '
  4. [Dòng 203] _val.value.length === 24) {
  5. [Dòng 213] if (this.cardTypeBank === 'bank_card_number') {
  6. [Dòng 218] } else if (this.cardTypeBank === 'bank_account_number') {
  7. [Dòng 224] } else if (this.cardTypeBank === 'bank_username') {
  8. [Dòng 228] } else if (this.cardTypeBank === 'bank_customer_code') {
  9. [Dòng 234] this.cardTypeBank === 'bank_card_number'
  10. [Dòng 248] if (this.cardTypeOcean === 'IB') {
  11. [Dòng 252] } else if (this.cardTypeOcean === 'MB') {
  12. [Dòng 253] if (_val.value.substr(0, 2) === '84') {
  13. [Dòng 260] } else if (this.cardTypeOcean === 'ATM') {
  14. [Dòng 287] if (this.cardTypeBank === 'bank_account_number') {
  15. [Dòng 306] this.cardTypeBank === 'bank_card_number') {
  16. [Dòng 328] if (this.cardTypeBank === 'bank_card_number'
  17. [Dòng 328] if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  18. [Dòng 649] if (event.keyCode === 8
  19. [Dòng 649] event.key === "Backspace"
  20. [Dòng 689] if (v.length === 2
  21. [Dòng 689] this.flag.length === 3
  22. [Dòng 689] this.flag.charAt(this.flag.length - 1) === '/') {
  23. [Dòng 693] if (v.length === 1) {
  24. [Dòng 695] } else if (v.length === 2) {
  25. [Dòng 698] v.length === 2) {
  26. [Dòng 706] if (len === 2) {
  27. [Dòng 974] if ((this.cardTypeBank === 'bank_account_number'
  28. [Dòng 974] this.cardTypeBank === 'bank_username'
  29. [Dòng 974] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  30. [Dòng 979] this.cardTypeOcean === 'ATM')
  31. [Dòng 980] || (this.cardTypeOcean === 'IB'
  32. [Dòng 1039] if (valIn === this._translate.instant('bank_card_number')) {
  33. [Dòng 1064] } else if (valIn === this._translate.instant('bank_account_number')) {
  34. [Dòng 1083] } else if (valIn === this._translate.instant('bank_username')) {
  35. [Dòng 1099] } else if (valIn === this._translate.instant('bank_customer_code')) {
  36. [Dòng 1188] if (_val.value === ''
  37. [Dòng 1188] _val.value === null
  38. [Dòng 1188] _val.value === undefined) {
  39. [Dòng 1199] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  40. [Dòng 1199] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  41. [Dòng 1206] this.cardTypeOcean === 'MB') {
  42. [Dòng 1214] this.cardTypeOcean === 'IB'
  43. [Dòng 1220] if ((this.cardTypeBank === 'bank_card_number'
  44. [Dòng 1254] if (this.cardName === undefined
  45. [Dòng 1254] this.cardName === '') {
  46. [Dòng 1262] if (this.valueDate === undefined
  47. [Dòng 1262] this.valueDate === '') {

== (75 điều kiện):
  1. [Dòng 127] if (this._b == 18
  2. [Dòng 127] this._b == 19) {
  3. [Dòng 130] if (this._b == 19) {//19BIDV
  4. [Dòng 138] } else if (this._b == 3
  5. [Dòng 138] this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  6. [Dòng 143] if (this._b == 27) {
  7. [Dòng 148] } else if (this._b == 12) {// 12SHB
  8. [Dòng 153] } else if (this._b == 18) { //18Oceanbank-ocb
  9. [Dòng 212] if (this._b == 19
  10. [Dòng 212] this._b == 3
  11. [Dòng 212] this._b == 27
  12. [Dòng 212] this._b == 12) {
  13. [Dòng 247] } else if (this._b == 18) {
  14. [Dòng 278] if (this.checkBin(_val.value) && (this._b == 3
  15. [Dòng 278] this._b == 27)) {
  16. [Dòng 283] if (this._b == 3) {
  17. [Dòng 295] this.cardTypeOcean == 'ATM') {
  18. [Dòng 308] if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  19. [Dòng 328] this._b == 18)) {
  20. [Dòng 404] if (this.checkBin(v) && (this._b == 3
  21. [Dòng 649] event.inputType == 'deleteContentBackward') {
  22. [Dòng 650] if (event.target.name == 'exp_date'
  23. [Dòng 658] event.inputType == 'insertCompositionText') {
  24. [Dòng 673] if (((this.valueDate.length == 4
  25. [Dòng 673] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  26. [Dòng 673] this.valueDate.length == 5)
  27. [Dòng 753] if (temp.length == 0) {
  28. [Dòng 760] return (counter % 10 == 0);
  29. [Dòng 780] } else if (this._b == 19) {
  30. [Dòng 782] } else if (this._b == 27) {
  31. [Dòng 787] if (this._b == 12) {
  32. [Dòng 789] if (this.cardTypeBank == 'bank_customer_code') {
  33. [Dòng 791] } else if (this.cardTypeBank == 'bank_account_number') {
  34. [Dòng 808] _formCard.exp_date.length == 5
  35. [Dòng 808] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
  36. [Dòng 808] this._b == 3)) {//27-pvcombank;3-TPB ;
  37. [Dòng 813] if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
  38. [Dòng 813] this._b == 19
  39. [Dòng 813] this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
  40. [Dòng 816] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  41. [Dòng 819] if (this.cardTypeOcean == 'IB') {
  42. [Dòng 821] } else if (this.cardTypeOcean == 'MB') {
  43. [Dòng 823] } else if (this.cardTypeOcean == 'ATM') {
  44. [Dòng 868] if (_re.status == '200'
  45. [Dòng 868] _re.status == '201') {
  46. [Dòng 873] if (this._res_post.state == 'approved'
  47. [Dòng 873] this._res_post.state == 'failed') {
  48. [Dòng 880] } else if (this._res_post.state == 'authorization_required') {
  49. [Dòng 898] if (this._b == 18) {
  50. [Dòng 903] if (this._b == 27
  51. [Dòng 903] this._b == 18) {
  52. [Dòng 944] && (_re.body.payments[_re.body.payments.length - 1].reason.code == '8'
  53. [Dòng 945] _re.body.payments[_re.body.payments.length - 1].reason.code == '9'
  54. [Dòng 946] _re.body.payments[_re.body.payments.length - 1].reason.code == '12'
  55. [Dòng 947] _re.body.payments[_re.body.payments.length - 1].reason.code == '13'
  56. [Dòng 948] _re.body.payments[_re.body.payments.length - 1].reason.code == '24'
  57. [Dòng 949] _re.body.payments[_re.body.payments.length - 1].reason.code == '25')) {
  58. [Dòng 994] if ((cardNo.length == 16
  59. [Dòng 994] if ((cardNo.length == 16 || (cardNo.length == 19
  60. [Dòng 995] && ((this._b == 18
  61. [Dòng 995] cardNo.length == 19) || this._b != 18)
  62. [Dòng 1008] if (this._b == +e.id) {
  63. [Dòng 1024] if (valIn == 1) {
  64. [Dòng 1026] } else if (valIn == 2) {
  65. [Dòng 1050] this._b == 3) {
  66. [Dòng 1057] if (this._b == 19) {
  67. [Dòng 1121] if (cardType == this._translate.instant('internetbanking')
  68. [Dòng 1129] } else if (cardType == this._translate.instant('mobilebanking')
  69. [Dòng 1137] } else if (cardType == this._translate.instant('atm')
  70. [Dòng 1199] this._b == 18))) {
  71. [Dòng 1206] } else if (this._b == 18
  72. [Dòng 1232] this.c_expdate = !(((this.valueDate.length == 4
  73. [Dòng 1265] this.valueDate.length == 4
  74. [Dòng 1265] this.valueDate.search('/') == -1)
  75. [Dòng 1266] this.valueDate.length == 5))

!== (13 điều kiện):
  1. [Dòng 203] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 854] key !== '3') {
  3. [Dòng 904] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 922] codeResponse.toString() !== '0') {
  5. [Dòng 974] cardNo.length !== 0) {
  6. [Dòng 1046] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 1067] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 1088] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 1108] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 1121] this.lb_card_account !== this._translate.instant('ocb_account')) {
  11. [Dòng 1129] this.lb_card_account !== this._translate.instant('ocb_phone')) {
  12. [Dòng 1137] this.lb_card_account !== this._translate.instant('ocb_card')) {
  13. [Dòng 1220] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (21 điều kiện):
  1. [Dòng 159] } else if (this._b != 18) {
  2. [Dòng 165] if (this.htmlDesc != null
  3. [Dòng 200] if (ua.indexOf('safari') != -1
  4. [Dòng 210] if (_val.value != '') {
  5. [Dòng 296] this.auth_method != null) {
  6. [Dòng 651] if (this.valueDate.length != 3) {
  7. [Dòng 808] if (_formCard.exp_date != null
  8. [Dòng 813] if (this.cardName != null
  9. [Dòng 876] if (this._res_post.links != null
  10. [Dòng 876] this._res_post.links.merchant_return != null
  11. [Dòng 876] this._res_post.links.merchant_return.href != null) {
  12. [Dòng 884] if (this._res_post.authorization != null
  13. [Dòng 884] this._res_post.authorization.links != null
  14. [Dòng 884] this._res_post.authorization.links.approval != null) {
  15. [Dòng 891] this._res_post.links.cancel != null) {
  16. [Dòng 994] this._b != 27
  17. [Dòng 994] this._b != 12
  18. [Dòng 994] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  19. [Dòng 995] this._b != 18)
  20. [Dòng 1041] if (this._b != 18
  21. [Dòng 1041] this._b != 19) {

================================================================================

📁 FILE 52: bank.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/model/bank.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 53: onepay-napas.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.html
📊 Thống kê: 7 điều kiện duy nhất
   - === : 4 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 26] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 41] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  3. [Dòng 41] _inExpDate.trim().length === 0)"
  4. [Dòng 65] ready===1"

== (3 điều kiện):
  1. [Dòng 23] <div [ngStyle]="{'margin-top': !checkTwoEnabled ? '15px' : '0px' }" *ngIf="(cardTypeBank == 'bank_card_number') &&(_b == 67
  2. [Dòng 65] (cardTypeBank == 'bank_card_number') &&(_b == 67 || checkTwoEnabled)&& ready===1
  3. [Dòng 90] <div [ngStyle]="{'margin-top': !checkTwoEnabled ? '15px' : '0px' }" class="nd-bank-card-two" *ngIf="(cardTypeBank == 'internet_banking')&&(_b == 2

================================================================================

📁 FILE 54: onepay-napas.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.ts
📊 Thống kê: 105 điều kiện duy nhất
   - === : 20 lần
   - == : 49 lần
   - !== : 4 lần
   - != : 32 lần
--------------------------------------------------------------------------------

=== (20 điều kiện):
  1. [Dòng 108] if (isIE[0] === 'MSIE'
  2. [Dòng 108] +isIE[1] === 10) {
  3. [Dòng 132] if (this.timeLeft === 10) {
  4. [Dòng 136] if (this.runTime === true) {
  5. [Dòng 142] if (this.timeLeft === 0) {
  6. [Dòng 144] if (this.runTime === true) this.submitCardBanking();
  7. [Dòng 332] if (event.keyCode === 8
  8. [Dòng 332] event.key === "Backspace"
  9. [Dòng 541] if (approval.method === 'REDIRECT') {
  10. [Dòng 544] } else if (approval.method === 'POST_REDIRECT') {
  11. [Dòng 618] if (valIn === this._translate.instant('bank_card_number')) {
  12. [Dòng 620] if (this.timeLeft === 1) {
  13. [Dòng 637] } else if (valIn === this._translate.instant('internet_banking')) {
  14. [Dòng 717] if (_val.value === ''
  15. [Dòng 717] _val.value === null
  16. [Dòng 717] _val.value === undefined) {
  17. [Dòng 728] if (_val.value && (this.cardTypeBank === 'bank_card_number')) {
  18. [Dòng 738] if ((this.cardTypeBank === 'bank_card_number'
  19. [Dòng 776] if (this.cardName === undefined
  20. [Dòng 776] this.cardName === '') {

== (49 điều kiện):
  1. [Dòng 124] if (this._b == 67
  2. [Dòng 124] this._b == 2) {//19BIDV
  3. [Dòng 130] if(this._b == 2
  4. [Dòng 154] if(this._b == 67 ){
  5. [Dòng 197] if (_re.status == '200'
  6. [Dòng 197] _re.status == '201') {
  7. [Dòng 202] if (this._res_post.state == 'approved'
  8. [Dòng 202] this._res_post.state == 'failed') {
  9. [Dòng 206] } else if (this._res_post.state == 'authorization_required') {
  10. [Dòng 317] return this._b == 2
  11. [Dòng 317] this._b == 67
  12. [Dòng 332] event.inputType == 'deleteContentBackward') {
  13. [Dòng 333] if (event.target.name == 'exp_date'
  14. [Dòng 341] event.inputType == 'insertCompositionText') {
  15. [Dòng 403] if (temp.length == 0) {
  16. [Dòng 410] return (counter % 10 == 0);
  17. [Dòng 426] _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
  18. [Dòng 501] && (_re.body.payments[_re.body.payments.length - 1].reason.code == '8'
  19. [Dòng 502] _re.body.payments[_re.body.payments.length - 1].reason.code == '9'
  20. [Dòng 503] _re.body.payments[_re.body.payments.length - 1].reason.code == '12'
  21. [Dòng 504] _re.body.payments[_re.body.payments.length - 1].reason.code == '13'
  22. [Dòng 505] _re.body.payments[_re.body.payments.length - 1].reason.code == '24'
  23. [Dòng 506] _re.body.payments[_re.body.payments.length - 1].reason.code == '25')) {
  24. [Dòng 581] if ((cardNo.length == 16
  25. [Dòng 581] if ((cardNo.length == 16 || (cardNo.length == 19
  26. [Dòng 582] this.checkMod10(cardNo) == true
  27. [Dòng 595] if (this._b == +e.id) {
  28. [Dòng 671] if (this._b == 19) {
  29. [Dòng 675] if (this._b == 27
  30. [Dòng 675] this._b == 3) {
  31. [Dòng 750] if (this._b != 68 || (this._b == 68
  32. [Dòng 759] return ((value.length == 4
  33. [Dòng 759] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  34. [Dòng 759] value.length == 5) && parseInt(value.split('/')[0]
  35. [Dòng 763] || (this._util.checkValidExpireMonthYear(value) && (this._b == 2
  36. [Dòng 763] this._b == 20
  37. [Dòng 763] this._b == 33
  38. [Dòng 764] this._b == 39
  39. [Dòng 764] this._b == 43
  40. [Dòng 764] this._b == 45
  41. [Dòng 764] this._b == 64
  42. [Dòng 764] this._b == 68
  43. [Dòng 764] this._b == 72))) //sonnh them Vietbank 72
  44. [Dòng 785] this._inExpDate.length == 4
  45. [Dòng 785] this._inExpDate.search('/') == -1)
  46. [Dòng 786] this._inExpDate.length == 5))
  47. [Dòng 788] || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 20
  48. [Dòng 788] this._b == 2
  49. [Dòng 788] this._b == 72)));

!== (4 điều kiện):
  1. [Dòng 221] codeResponse.toString() !== '0') {
  2. [Dòng 579] let _b = this._b !== 67 ? 67 : this._b
  3. [Dòng 664] if (this.cardTypeBank !== 'internet_banking') {
  4. [Dòng 738] this._b !== 18)) {

!= (32 điều kiện):
  1. [Dòng 149] if (this.htmlDesc != null
  2. [Dòng 210] if (this._res_post.authorization != null
  3. [Dòng 210] this._res_post.authorization.links != null
  4. [Dòng 210] this._res_post.authorization.links.approval != null) {
  5. [Dòng 269] if (ua.indexOf('safari') != -1
  6. [Dòng 334] if (this._inExpDate.length != 3) {
  7. [Dòng 426] if (_formCard.exp_date != null
  8. [Dòng 431] if (this.cardName != null
  9. [Dòng 468] if (this._res_post.return_url != null) {
  10. [Dòng 471] if (this._res_post.links != null
  11. [Dòng 471] this._res_post.links.merchant_return != null
  12. [Dòng 471] this._res_post.links.merchant_return.href != null) {
  13. [Dòng 526] this._res_post.links.cancel != null) {
  14. [Dòng 531] let userName = _formCard.name != null ? _formCard.name : ''
  15. [Dòng 532] this._res_post.authorization.links.approval != null
  16. [Dòng 532] this._res_post.authorization.links.approval.href != null) {
  17. [Dòng 535] userName = paramUserName != null ? paramUserName : ''
  18. [Dòng 581] this._b != 27
  19. [Dòng 581] this._b != 12
  20. [Dòng 581] this._b != 3))
  21. [Dòng 750] if (this._b != 68
  22. [Dòng 761] this._b != 2
  23. [Dòng 761] this._b != 20
  24. [Dòng 761] this._b != 33
  25. [Dòng 761] this._b != 39
  26. [Dòng 762] this._b != 43
  27. [Dòng 762] this._b != 45
  28. [Dòng 762] this._b != 64
  29. [Dòng 762] this._b != 67
  30. [Dòng 762] this._b != 68
  31. [Dòng 762] this._b != 72)
  32. [Dòng 787] this._b != 72 )

================================================================================

📁 FILE 55: otp-auth.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 56: otp-auth.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
📊 Thống kê: 28 điều kiện duy nhất
   - === : 0 lần
   - == : 17 lần
   - !== : 1 lần
   - != : 10 lần
--------------------------------------------------------------------------------

== (17 điều kiện):
  1. [Dòng 99] if (this._b == 8) {//MB Bank
  2. [Dòng 103] if (this._b == 18) {//Oceanbank
  3. [Dòng 146] if (this._b == 8) {
  4. [Dòng 151] if (this._b == 18) {
  5. [Dòng 156] if (this._b == 12) { //SHB
  6. [Dòng 177] if (_re.status == '200'
  7. [Dòng 177] _re.status == '201') {
  8. [Dòng 186] } else if (this._res.state == 'authorization_required') {
  9. [Dòng 211] && (_re.body.payments[_re.body.payments.length - 1].reason.code == '8'
  10. [Dòng 212] _re.body.payments[_re.body.payments.length - 1].reason.code == '9'
  11. [Dòng 213] _re.body.payments[_re.body.payments.length - 1].reason.code == '12'
  12. [Dòng 214] _re.body.payments[_re.body.payments.length - 1].reason.code == '13'
  13. [Dòng 215] _re.body.payments[_re.body.payments.length - 1].reason.code == '24'
  14. [Dòng 216] _re.body.payments[_re.body.payments.length - 1].reason.code == '25'
  15. [Dòng 220] if (this.challengeCode == '') {
  16. [Dòng 313] if (this._b == 12) {
  17. [Dòng 368] if (this._b == 18) {//8-MB Bank;18-oceanbank

!== (1 điều kiện):
  1. [Dòng 192] codeResponse.toString() !== '0') {

!= (10 điều kiện):
  1. [Dòng 182] if (this._res.links != null
  2. [Dòng 182] this._res.links.merchant_return != null
  3. [Dòng 182] this._res.links.merchant_return.href != null) {
  4. [Dòng 338] if (this._res_post != null
  5. [Dòng 338] this._res_post.links != null
  6. [Dòng 338] this._res_post.links.merchant_return != null
  7. [Dòng 338] this._res_post.links.merchant_return.href != null) {
  8. [Dòng 363] if (!(_formCard.otp != null
  9. [Dòng 369] if (!(_formCard.password != null
  10. [Dòng 385] if (ua.indexOf('safari') != -1

================================================================================

📁 FILE 57: shb.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/shb/shb.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 25] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 42] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  3. [Dòng 42] _inExpDate.trim().length === 0)"

== (2 điều kiện):
  1. [Dòng 22] <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_card_number')">
  2. [Dòng 66] <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_account_number')">

================================================================================

📁 FILE 58: shb.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/shb/shb.component.ts
📊 Thống kê: 69 điều kiện duy nhất
   - === : 17 lần
   - == : 36 lần
   - !== : 5 lần
   - != : 11 lần
--------------------------------------------------------------------------------

=== (17 điều kiện):
  1. [Dòng 101] if (isIE[0] === 'MSIE'
  2. [Dòng 101] +isIE[1] === 10) {
  3. [Dòng 143] if (focusElement === 'card_name') {
  4. [Dòng 145] } else if (focusElement === 'exp_date'
  5. [Dòng 166] focusExpDateElement === 'card_name') {
  6. [Dòng 375] if (this.cardTypeBank === 'bank_account_number'
  7. [Dòng 420] if (valIn === this._translate.instant('bank_card_number')) {
  8. [Dòng 426] } else if (valIn === this._translate.instant('bank_account_number')) {
  9. [Dòng 468] if (_val.value === ''
  10. [Dòng 468] _val.value === null
  11. [Dòng 468] _val.value === undefined) {
  12. [Dòng 479] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  13. [Dòng 479] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  14. [Dòng 486] this.cardTypeOcean === 'MB') {
  15. [Dòng 494] this.cardTypeOcean === 'IB'
  16. [Dòng 500] if ((this.cardTypeBank === 'bank_card_number'
  17. [Dòng 523] if (this.cardTypeOcean === 'IB') {

== (36 điều kiện):
  1. [Dòng 110] if(this._b == 12) this.isShbGroup = true;
  2. [Dòng 131] return this._b == 9
  3. [Dòng 131] this._b == 11
  4. [Dòng 131] this._b == 16
  5. [Dòng 131] this._b == 17
  6. [Dòng 131] this._b == 25
  7. [Dòng 131] this._b == 44
  8. [Dòng 132] this._b == 57
  9. [Dòng 132] this._b == 59
  10. [Dòng 132] this._b == 61
  11. [Dòng 132] this._b == 63
  12. [Dòng 132] this._b == 69
  13. [Dòng 214] if (this.cardTypeBank == 'bank_account_number') {
  14. [Dòng 225] _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
  15. [Dòng 271] if (_re.status == '200'
  16. [Dòng 271] _re.status == '201') {
  17. [Dòng 275] if (this._res_post.state == 'approved'
  18. [Dòng 275] this._res_post.state == 'failed') {
  19. [Dòng 281] } else if (this._res_post.state == 'authorization_required') {
  20. [Dòng 341] && (_re.body.payments[_re.body.payments.length - 1].reason.code == '8'
  21. [Dòng 342] _re.body.payments[_re.body.payments.length - 1].reason.code == '9'
  22. [Dòng 343] _re.body.payments[_re.body.payments.length - 1].reason.code == '12'
  23. [Dòng 344] _re.body.payments[_re.body.payments.length - 1].reason.code == '13'
  24. [Dòng 345] _re.body.payments[_re.body.payments.length - 1].reason.code == '24'
  25. [Dòng 346] _re.body.payments[_re.body.payments.length - 1].reason.code == '25')) {
  26. [Dòng 378] if ((cardNo.length == 16
  27. [Dòng 378] cardNo.length == 19) && cardNo.startsWith("970443") && this._util.checkMod10(cardNo) && this.checkBin(cardNo) ) {
  28. [Dòng 390] if (this._b == +e.id) {
  29. [Dòng 406] if (valIn == 1) {
  30. [Dòng 408] } else if (valIn == 2) {
  31. [Dòng 479] this._b == 18))) {
  32. [Dòng 486] } else if (this._b == 18
  33. [Dòng 500] this._b == 18)) {
  34. [Dòng 512] this.c_expdate = !(((this.valueDate.length == 4
  35. [Dòng 512] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  36. [Dòng 512] this.valueDate.length == 5)

!== (5 điều kiện):
  1. [Dòng 259] key !== '3') {
  2. [Dòng 301] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  3. [Dòng 319] codeResponse.toString() !== '0') {
  4. [Dòng 375] cardNo.length !== 0) {
  5. [Dòng 500] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (11 điều kiện):
  1. [Dòng 118] if (this.htmlDesc != null
  2. [Dòng 179] if (ua.indexOf('safari') != -1
  3. [Dòng 225] if (_formCard.exp_date != null
  4. [Dòng 230] if (this.cardName != null
  5. [Dòng 278] if (this._res_post.links != null
  6. [Dòng 278] this._res_post.links.merchant_return != null
  7. [Dòng 278] this._res_post.links.merchant_return.href != null) {
  8. [Dòng 284] if (this._res_post.authorization != null
  9. [Dòng 284] this._res_post.authorization.links != null
  10. [Dòng 284] this._res_post.authorization.links.approval != null) {
  11. [Dòng 291] this._res_post.links.cancel != null) {

================================================================================

📁 FILE 59: techcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 60: techcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 61: techcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
📊 Thống kê: 17 điều kiện duy nhất
   - === : 1 lần
   - == : 12 lần
   - !== : 1 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 64] if (this.timeLeft === 0) {

== (12 điều kiện):
  1. [Dòng 55] if (this._b == 2
  2. [Dòng 55] this._b == 31
  3. [Dòng 55] this._b == 80) {
  4. [Dòng 92] if (this._b == 2) {
  5. [Dòng 94] } else if (this._b == 6) {
  6. [Dòng 96] } else if (this._b == 31) {
  7. [Dòng 98] } else if (this._b == 80) {
  8. [Dòng 128] if (_re.status == '200'
  9. [Dòng 128] _re.status == '201') {
  10. [Dòng 133] if (this._res_post.state == 'approved'
  11. [Dòng 133] this._res_post.state == 'failed') {
  12. [Dòng 137] } else if (this._res_post.state == 'authorization_required') {

!== (1 điều kiện):
  1. [Dòng 152] codeResponse.toString() !== '0') {

!= (3 điều kiện):
  1. [Dòng 141] if (this._res_post.authorization != null
  2. [Dòng 141] this._res_post.authorization.links != null
  3. [Dòng 141] this._res_post.authorization.links.approval != null) {

================================================================================

📁 FILE 62: vibbank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 27] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 41] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 41] valueDate.trim().length === 0)"

================================================================================

📁 FILE 63: vibbank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 64: vibbank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.ts
📊 Thống kê: 102 điều kiện duy nhất
   - === : 36 lần
   - == : 39 lần
   - !== : 10 lần
   - != : 17 lần
--------------------------------------------------------------------------------

=== (36 điều kiện):
  1. [Dòng 116] if (isIE[0] === 'MSIE'
  2. [Dòng 116] +isIE[1] === 10) {
  3. [Dòng 147] if (this.timeLeft === 0) {
  4. [Dòng 196] if ((_val.value.substr(-1) === ' '
  5. [Dòng 196] _val.value.length === 24) {
  6. [Dòng 206] if (this.cardTypeBank === 'bank_card_number') {
  7. [Dòng 211] } else if (this.cardTypeBank === 'bank_account_number') {
  8. [Dòng 217] } else if (this.cardTypeBank === 'bank_username') {
  9. [Dòng 221] } else if (this.cardTypeBank === 'bank_customer_code') {
  10. [Dòng 242] if (this.cardTypeBank === 'bank_account_number') {
  11. [Dòng 253] this.cardTypeBank === 'bank_card_number') {
  12. [Dòng 490] if (event.keyCode === 8
  13. [Dòng 490] event.key === "Backspace"
  14. [Dòng 530] if (v.length === 2
  15. [Dòng 530] this.flag.length === 3
  16. [Dòng 530] this.flag.charAt(this.flag.length - 1) === '/') {
  17. [Dòng 534] if (v.length === 1) {
  18. [Dòng 536] } else if (v.length === 2) {
  19. [Dòng 539] v.length === 2) {
  20. [Dòng 547] if (len === 2) {
  21. [Dòng 782] if ((this.cardTypeBank === 'bank_account_number'
  22. [Dòng 782] this.cardTypeBank === 'bank_username'
  23. [Dòng 782] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  24. [Dòng 832] if (valIn === this._translate.instant('bank_card_number')) {
  25. [Dòng 851] } else if (valIn === this._translate.instant('bank_account_number')) {
  26. [Dòng 863] } else if (valIn === this._translate.instant('bank_username')) {
  27. [Dòng 874] } else if (valIn === this._translate.instant('bank_customer_code')) {
  28. [Dòng 931] if (_val.value === ''
  29. [Dòng 931] _val.value === null
  30. [Dòng 931] _val.value === undefined) {
  31. [Dòng 942] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  32. [Dòng 952] if ((this.cardTypeBank === 'bank_card_number'
  33. [Dòng 984] if (this.cardName === undefined
  34. [Dòng 984] this.cardName === '') {
  35. [Dòng 992] if (this.valueDate === undefined
  36. [Dòng 992] this.valueDate === '') {

== (39 điều kiện):
  1. [Dòng 130] if (this._b == 5) {//5-vib;
  2. [Dòng 205] if (this._b == 5) {
  3. [Dòng 239] if (this.checkBin(_val.value) && (this._b == 5)) {
  4. [Dòng 255] if (this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  5. [Dòng 317] if (this.checkBin(v) && (this._b == 5)) {
  6. [Dòng 490] event.inputType == 'deleteContentBackward') {
  7. [Dòng 491] if (event.target.name == 'exp_date'
  8. [Dòng 499] event.inputType == 'insertCompositionText') {
  9. [Dòng 514] if (((this.valueDate.length == 4
  10. [Dòng 514] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  11. [Dòng 514] this.valueDate.length == 5)
  12. [Dòng 594] if (temp.length == 0) {
  13. [Dòng 601] return (counter % 10 == 0);
  14. [Dòng 632] _formCard.exp_date.length == 5
  15. [Dòng 632] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 5)) {//27-pvcombank;3-TPB ;
  16. [Dòng 637] if (this.cardName != null && this.cardName.length > 0 && (this._b == 5)) {//3-TPB ;19-BIDV;27-pvcombank;
  17. [Dòng 681] if (_re.status == '200'
  18. [Dòng 681] _re.status == '201') {
  19. [Dòng 686] if (this._res_post.state == 'approved'
  20. [Dòng 686] this._res_post.state == 'failed') {
  21. [Dòng 693] } else if (this._res_post.state == 'authorization_required') {
  22. [Dòng 754] && (_re.body.payments[_re.body.payments.length - 1].reason.code == '8'
  23. [Dòng 755] _re.body.payments[_re.body.payments.length - 1].reason.code == '9'
  24. [Dòng 756] _re.body.payments[_re.body.payments.length - 1].reason.code == '12'
  25. [Dòng 757] _re.body.payments[_re.body.payments.length - 1].reason.code == '13'
  26. [Dòng 758] _re.body.payments[_re.body.payments.length - 1].reason.code == '24'
  27. [Dòng 759] _re.body.payments[_re.body.payments.length - 1].reason.code == '25')) {
  28. [Dòng 787] if ((cardNo.length == 16
  29. [Dòng 787] if ((cardNo.length == 16 || (cardNo.length == 19
  30. [Dòng 788] && ((this._b == 18
  31. [Dòng 788] cardNo.length == 19) || this._b != 18)
  32. [Dòng 801] if (this._b == +e.id) {
  33. [Dòng 817] if (valIn == 1) {
  34. [Dòng 819] } else if (valIn == 2) {
  35. [Dòng 942] this._b == 18)) {
  36. [Dòng 964] this.c_expdate = !(((this.valueDate.length == 4
  37. [Dòng 995] this.valueDate.length == 4
  38. [Dòng 995] this.valueDate.search('/') == -1)
  39. [Dòng 996] this.valueDate.length == 5))

!== (10 điều kiện):
  1. [Dòng 196] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 667] key !== '3') {
  3. [Dòng 715] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 732] codeResponse.toString() !== '0') {
  5. [Dòng 782] cardNo.length !== 0) {
  6. [Dòng 839] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 854] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 868] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 881] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 952] this._b !== 18) || (this._b == 18)) {

!= (17 điều kiện):
  1. [Dòng 157] if (this.htmlDesc != null
  2. [Dòng 193] if (ua.indexOf('safari') != -1
  3. [Dòng 203] if (_val.value != '') {
  4. [Dòng 492] if (this.valueDate.length != 3) {
  5. [Dòng 632] if (_formCard.exp_date != null
  6. [Dòng 637] if (this.cardName != null
  7. [Dòng 689] if (this._res_post.links != null
  8. [Dòng 689] this._res_post.links.merchant_return != null
  9. [Dòng 689] this._res_post.links.merchant_return.href != null) {
  10. [Dòng 697] if (this._res_post.authorization != null
  11. [Dòng 697] this._res_post.authorization.links != null
  12. [Dòng 697] this._res_post.authorization.links.approval != null) {
  13. [Dòng 704] this._res_post.links.cancel != null) {
  14. [Dòng 787] this._b != 27
  15. [Dòng 787] this._b != 12
  16. [Dòng 787] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  17. [Dòng 788] this._b != 18)

================================================================================

📁 FILE 65: vietcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 28] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  2. [Dòng 28] _inExpDate.trim().length === 0)"

== (1 điều kiện):
  1. [Dòng 58] _b == 68"

================================================================================

📁 FILE 66: vietcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 67: vietcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
📊 Thống kê: 111 điều kiện duy nhất
   - === : 4 lần
   - == : 67 lần
   - !== : 2 lần
   - != : 38 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 193] if (event.keyCode === 8
  2. [Dòng 193] event.key === "Backspace"
  3. [Dòng 428] if (approval.method === 'REDIRECT') {
  4. [Dòng 431] } else if (approval.method === 'POST_REDIRECT') {

== (67 điều kiện):
  1. [Dòng 94] if (this._b == 11
  2. [Dòng 94] this._b == 20
  3. [Dòng 94] this._b == 73
  4. [Dòng 94] this._b == 33
  5. [Dòng 94] this._b == 39
  6. [Dòng 94] this._b == 43
  7. [Dòng 94] this._b == 45
  8. [Dòng 94] this._b == 67
  9. [Dòng 94] this._b == 64
  10. [Dòng 94] this._b == 68
  11. [Dòng 94] this._b == 72
  12. [Dòng 94] this._b == 36) {//seabank
  13. [Dòng 101] if (this._b == 1
  14. [Dòng 101] this._b == 36
  15. [Dòng 101] this._b == 55
  16. [Dòng 101] this._b == 47
  17. [Dòng 101] this._b == 48
  18. [Dòng 101] this._b == 59) {
  19. [Dòng 120] return this._b == 9
  20. [Dòng 120] this._b == 16
  21. [Dòng 120] this._b == 17
  22. [Dòng 120] this._b == 25
  23. [Dòng 120] this._b == 44
  24. [Dòng 121] this._b == 54
  25. [Dòng 121] this._b == 57
  26. [Dòng 121] this._b == 59
  27. [Dòng 121] this._b == 61
  28. [Dòng 121] this._b == 63
  29. [Dòng 121] this._b == 69
  30. [Dòng 193] event.inputType == 'deleteContentBackward') {
  31. [Dòng 194] if (event.target.name == 'exp_date'
  32. [Dòng 202] event.inputType == 'insertCompositionText') {
  33. [Dòng 317] if (this._res_post.state == 'approved'
  34. [Dòng 317] this._res_post.state == 'failed') {
  35. [Dòng 352] && (_re.body.payments[_re.body.payments.length - 1].reason.code == '8'
  36. [Dòng 353] _re.body.payments[_re.body.payments.length - 1].reason.code == '9'
  37. [Dòng 354] _re.body.payments[_re.body.payments.length - 1].reason.code == '12'
  38. [Dòng 355] _re.body.payments[_re.body.payments.length - 1].reason.code == '13'
  39. [Dòng 356] _re.body.payments[_re.body.payments.length - 1].reason.code == '24'
  40. [Dòng 357] _re.body.payments[_re.body.payments.length - 1].reason.code == '25')) {
  41. [Dòng 367] } else if (this._res_post.state == 'authorization_required') {
  42. [Dòng 389] this._b == 14
  43. [Dòng 389] this._b == 15
  44. [Dòng 389] this._b == 24
  45. [Dòng 389] this._b == 8
  46. [Dòng 389] this._b == 10
  47. [Dòng 389] this._b == 22
  48. [Dòng 389] this._b == 23
  49. [Dòng 389] this._b == 30
  50. [Dòng 389] this._b == 11
  51. [Dòng 389] this._b == 17) {
  52. [Dòng 469] if ((cardNo.length == 16
  53. [Dòng 470] (cardNo.length == 19
  54. [Dòng 470] (cardNo.length == 19 && (this._b == 1
  55. [Dòng 470] this._b == 4
  56. [Dòng 470] this._b == 59))
  57. [Dòng 472] this._util.checkMod10(cardNo) == true
  58. [Dòng 508] return ((value.length == 4
  59. [Dòng 508] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  60. [Dòng 508] value.length == 5) && parseInt(value.split('/')[0]
  61. [Dòng 512] || (this._util.checkValidExpireMonthYear(value) && (this._b == 11
  62. [Dòng 513] this._b == 36))) //sonnh them Vietbank 72
  63. [Dòng 544] this._inExpDate.length == 4
  64. [Dòng 544] this._inExpDate.search('/') == -1)
  65. [Dòng 545] this._inExpDate.length == 5))
  66. [Dòng 547] || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 11
  67. [Dòng 547] this._b == 36)));

!== (2 điều kiện):
  1. [Dòng 330] codeResponse.toString() !== '0') {
  2. [Dòng 390] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (38 điều kiện):
  1. [Dòng 106] if (this.htmlDesc != null
  2. [Dòng 130] if (ua.indexOf('safari') != -1
  3. [Dòng 195] if (this._inExpDate.length != 3) {
  4. [Dòng 271] if (this._b != 9
  5. [Dòng 271] this._b != 16
  6. [Dòng 271] this._b != 17
  7. [Dòng 271] this._b != 25
  8. [Dòng 271] this._b != 44
  9. [Dòng 271] this._b != 54
  10. [Dòng 272] this._b != 57
  11. [Dòng 272] this._b != 59
  12. [Dòng 272] this._b != 61
  13. [Dòng 272] this._b != 63
  14. [Dòng 272] this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
  15. [Dòng 285] '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73'].indexOf(this._b.toString()) != -1) {// duongpxt 19255: Them vietbank napas id 72
  16. [Dòng 319] if (this._res_post.return_url != null) {
  17. [Dòng 322] if (this._res_post.links != null
  18. [Dòng 322] this._res_post.links.merchant_return != null
  19. [Dòng 322] this._res_post.links.merchant_return.href != null) {
  20. [Dòng 372] if (this._res_post.authorization != null
  21. [Dòng 372] this._res_post.authorization.links != null
  22. [Dòng 377] this._res_post.links.cancel != null) {
  23. [Dòng 383] let userName = _formCard.name != null ? _formCard.name : ''
  24. [Dòng 384] this._res_post.authorization.links.approval != null
  25. [Dòng 384] this._res_post.authorization.links.approval.href != null) {
  26. [Dòng 387] userName = paramUserName != null ? paramUserName : ''
  27. [Dòng 510] this._b != 11
  28. [Dòng 510] this._b != 20
  29. [Dòng 510] this._b != 33
  30. [Dòng 510] this._b != 39
  31. [Dòng 511] this._b != 43
  32. [Dòng 511] this._b != 45
  33. [Dòng 511] this._b != 64
  34. [Dòng 511] this._b != 67
  35. [Dòng 511] this._b != 68
  36. [Dòng 511] this._b != 72
  37. [Dòng 511] this._b != 73
  38. [Dòng 511] this._b != 36)

================================================================================

📁 FILE 68: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 69: off-bank-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 70: off-bank-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 71: policy-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/dialog/policy-dialog/policy-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 72: policy-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/dialog/policy-dialog/policy-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 73: domescard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/domescard-main.component.html
📊 Thống kê: 66 điều kiện duy nhất
   - === : 1 lần
   - == : 65 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 17] filteredData.length === 0"

== (65 điều kiện):
  1. [Dòng 26] _auth==0
  2. [Dòng 26] *ngIf="(!token&&_auth==0&&(_b==1
  3. [Dòng 26] _b==4
  4. [Dòng 26] _b==7
  5. [Dòng 26] _b==8
  6. [Dòng 26] _b==9
  7. [Dòng 26] _b==10
  8. [Dòng 26] _b==11
  9. [Dòng 26] _b==14
  10. [Dòng 26] _b==15
  11. [Dòng 26] _b==16
  12. [Dòng 26] _b==17
  13. [Dòng 26] _b==20
  14. [Dòng 27] _b==22
  15. [Dòng 27] _b==23
  16. [Dòng 27] _b==24
  17. [Dòng 27] _b==25
  18. [Dòng 27] _b==30
  19. [Dòng 27] _b==33
  20. [Dòng 27] _b==34
  21. [Dòng 27] _b==35
  22. [Dòng 27] _b==36
  23. [Dòng 27] _b==37
  24. [Dòng 27] _b==38
  25. [Dòng 27] _b==39
  26. [Dòng 27] _b==40
  27. [Dòng 27] _b==41
  28. [Dòng 28] _b==42
  29. [Dòng 28] _b==43
  30. [Dòng 28] _b==44
  31. [Dòng 28] _b==45
  32. [Dòng 28] this._b==46
  33. [Dòng 28] _b==47
  34. [Dòng 28] _b==48
  35. [Dòng 28] _b==49
  36. [Dòng 28] _b==50
  37. [Dòng 28] _b==51
  38. [Dòng 28] _b==52
  39. [Dòng 28] _b==53
  40. [Dòng 28] _b==54
  41. [Dòng 28] _b==55
  42. [Dòng 28] _b==56
  43. [Dòng 28] _b==57
  44. [Dòng 28] _b==58
  45. [Dòng 28] _b==59
  46. [Dòng 28] _b==60
  47. [Dòng 29] _b==61
  48. [Dòng 29] _b==62
  49. [Dòng 29] _b==63
  50. [Dòng 29] _b==64
  51. [Dòng 29] this._b==65
  52. [Dòng 29] _b==66
  53. [Dòng 29] _b==68
  54. [Dòng 29] _b==69
  55. [Dòng 29] this._b==70
  56. [Dòng 29] this._b==71
  57. [Dòng 29] this._b==72
  58. [Dòng 29] _b==73)) || (token && _b == 16)">
  59. [Dòng 29] _b == 16)">
  60. [Dòng 33] _auth==0 && techcombankGroupSelected
  61. [Dòng 37] _auth==0 && shbGroupSelected
  62. [Dòng 42] _auth==0 && onepaynapasGroupSelected
  63. [Dòng 48] _auth==0 && bankaccountGroupSelected
  64. [Dòng 52] _auth==0 && vibbankGroupSelected
  65. [Dòng 56] (token || _auth==1) && _b != 16

================================================================================

📁 FILE 74: domescard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/domescard-main/domescard-main.component.ts
📊 Thống kê: 141 điều kiện duy nhất
   - === : 26 lần
   - == : 106 lần
   - !== : 2 lần
   - != : 7 lần
--------------------------------------------------------------------------------

=== (26 điều kiện):
  1. [Dòng 283] if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
  2. [Dòng 284] filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
  3. [Dòng 328] if (valOut === 'auth') {
  4. [Dòng 471] if (this._b === '1'
  5. [Dòng 471] this._b === '20'
  6. [Dòng 471] this._b === '64' ) {
  7. [Dòng 474] if (this._b === '36'
  8. [Dòng 474] this._b === '18'
  9. [Dòng 477] if (this._b === '19'
  10. [Dòng 477] this._b === '16'
  11. [Dòng 477] this._b === '25'
  12. [Dòng 477] this._b === '33'
  13. [Dòng 478] this._b === '39'
  14. [Dòng 478] this._b === '9'
  15. [Dòng 478] this._b === '11'
  16. [Dòng 478] this._b === '17'
  17. [Dòng 479] this._b === '36'
  18. [Dòng 479] this._b === '44'
  19. [Dòng 480] this._b === '64'
  20. [Dòng 483] if (this._b === '20'
  21. [Dòng 486] if (this._b === '18') {
  22. [Dòng 492] return (bankId === '1'
  23. [Dòng 492] bankId === '20'
  24. [Dòng 492] bankId === '64');
  25. [Dòng 496] return (bankId === '36'
  26. [Dòng 496] bankId === '18'

== (106 điều kiện):
  1. [Dòng 167] this._auth == 0) {
  2. [Dòng 220] if(e.id == '31'){
  3. [Dòng 222] } else if(e.id =='80'){
  4. [Dòng 244] if (d.b.card_list == s) {
  5. [Dòng 265] if(item.b.id == '2'
  6. [Dòng 265] item.b.id =='67'){
  7. [Dòng 306] $event == 'true') {
  8. [Dòng 313] _b: this._b=='2'? '67':this._b
  9. [Dòng 399] if (bankId==1
  10. [Dòng 399] bankId==4
  11. [Dòng 399] bankId==7
  12. [Dòng 399] bankId==8
  13. [Dòng 399] bankId==9
  14. [Dòng 399] bankId==10
  15. [Dòng 399] bankId==11
  16. [Dòng 399] bankId==14
  17. [Dòng 399] bankId==15
  18. [Dòng 400] bankId==16
  19. [Dòng 400] bankId==17
  20. [Dòng 400] bankId==20
  21. [Dòng 400] bankId==22
  22. [Dòng 400] bankId==23
  23. [Dòng 400] bankId==24
  24. [Dòng 400] bankId==25
  25. [Dòng 400] bankId==30
  26. [Dòng 400] bankId==33
  27. [Dòng 401] bankId==34
  28. [Dòng 401] bankId==35
  29. [Dòng 401] bankId==36
  30. [Dòng 401] bankId==37
  31. [Dòng 401] bankId==38
  32. [Dòng 401] bankId==39
  33. [Dòng 401] bankId==40
  34. [Dòng 401] bankId==41
  35. [Dòng 401] bankId==42
  36. [Dòng 402] bankId==43
  37. [Dòng 402] bankId==44
  38. [Dòng 402] bankId==45
  39. [Dòng 402] bankId==46
  40. [Dòng 402] bankId==47
  41. [Dòng 402] bankId==48
  42. [Dòng 402] bankId==49
  43. [Dòng 402] bankId==50
  44. [Dòng 402] bankId==51
  45. [Dòng 403] bankId==52
  46. [Dòng 403] bankId==53
  47. [Dòng 403] bankId==54
  48. [Dòng 403] bankId==55
  49. [Dòng 403] bankId==56
  50. [Dòng 403] bankId==57
  51. [Dòng 403] bankId==58
  52. [Dòng 403] bankId==59
  53. [Dòng 403] bankId==60
  54. [Dòng 404] bankId==61
  55. [Dòng 404] bankId==62
  56. [Dòng 404] bankId==63
  57. [Dòng 404] bankId==64
  58. [Dòng 404] bankId==65
  59. [Dòng 404] bankId==66
  60. [Dòng 404] bankId==68
  61. [Dòng 404] bankId==69
  62. [Dòng 404] bankId==70
  63. [Dòng 405] bankId==71
  64. [Dòng 405] bankId==72
  65. [Dòng 405] bankId==73
  66. [Dòng 405] bankId==32) {
  67. [Dòng 407] } else if (bankId == 6
  68. [Dòng 407] bankId == 31
  69. [Dòng 407] bankId == 80) {
  70. [Dòng 409] } else if (bankId == 2
  71. [Dòng 409] bankId == 67) {
  72. [Dòng 411] } else if (bankId==3
  73. [Dòng 411] bankId==18
  74. [Dòng 411] bankId==19
  75. [Dòng 411] bankId==27) {
  76. [Dòng 413] } else if (bankId==5) {
  77. [Dòng 415] } else if (bankId == 12) {
  78. [Dòng 474] this._b == '55'
  79. [Dòng 474] this._b == '47'
  80. [Dòng 474] this._b == '48'
  81. [Dòng 474] this._b == '19'
  82. [Dòng 474] this._b == '59'
  83. [Dòng 474] this._b == '73'
  84. [Dòng 474] this._b == '12') {
  85. [Dòng 477] this._b == '3'
  86. [Dòng 478] this._b == '43'
  87. [Dòng 478] this._b == '45'
  88. [Dòng 479] this._b == '54'
  89. [Dòng 479] this._b == '57'
  90. [Dòng 480] this._b == '61'
  91. [Dòng 480] this._b == '63'
  92. [Dòng 480] this._b == '67'
  93. [Dòng 480] this._b == '68'
  94. [Dòng 480] this._b == '69'
  95. [Dòng 480] this._b == '72') {
  96. [Dòng 483] this._b == '72'
  97. [Dòng 483] this._b == '36') { //sonnh them 72 Vietbank
  98. [Dòng 496] bankId == '55'
  99. [Dòng 496] bankId == '47'
  100. [Dòng 496] bankId == '48'
  101. [Dòng 496] bankId == '19'
  102. [Dòng 496] bankId == '59'
  103. [Dòng 496] bankId == '73'
  104. [Dòng 496] bankId == '5'
  105. [Dòng 496] bankId == '12');
  106. [Dòng 511] if (item['id'] == this._b) {

!== (2 điều kiện):
  1. [Dòng 115] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  2. [Dòng 270] this.bankList = this.bankList.filter( item => item.b.id !== '67');

!= (7 điều kiện):
  1. [Dòng 153] if (params['locale'] != null) {
  2. [Dòng 159] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 163] this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
  4. [Dòng 189] if (!(strInstrument != null
  5. [Dòng 192] if (strInstrument.substring(0, 1) != '^'
  6. [Dòng 192] strInstrument.substr(strInstrument.length - 1) != '$') {
  7. [Dòng 377] if (bankid != null) {

================================================================================

📁 FILE 75: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/intercard-main/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 76: intercard-main-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/intercard-main/form/intercard-main-form.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 45] _showNameOnCard!=true"

================================================================================

📁 FILE 77: intercard-main-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/intercard-main/form/intercard-main-form.component.ts
📊 Thống kê: 74 điều kiện duy nhất
   - === : 10 lần
   - == : 41 lần
   - !== : 10 lần
   - != : 13 lần
--------------------------------------------------------------------------------

=== (10 điều kiện):
  1. [Dòng 240] if (_formCard.country === 'default') {
  2. [Dòng 488] if (event.keyCode === 8
  3. [Dòng 488] event.key === "Backspace"
  4. [Dòng 563] if ((v.substr(-1) === ' '
  5. [Dòng 742] this._i_country_code === 'US') {
  6. [Dòng 779] const insertIndex = this._i_country_code === 'US' ? 5 : 3
  7. [Dòng 781] if (temp[i] === '-'
  8. [Dòng 781] temp[i] === ' ') {
  9. [Dòng 788] insertIndex === 3 ? ' ' : itemRemoved)
  10. [Dòng 837] this.c_country = _val.value === 'default'

== (41 điều kiện):
  1. [Dòng 294] if (this._res_post.state == 'approved'
  2. [Dòng 294] this._res_post.state == 'failed') {
  3. [Dòng 301] if (this._res_post.state == 'failed') {
  4. [Dòng 305] && (_re.body.payments[_re.body.payments.length - 1].reason.code == '8'
  5. [Dòng 306] _re.body.payments[_re.body.payments.length - 1].reason.code == '9'
  6. [Dòng 307] _re.body.payments[_re.body.payments.length - 1].reason.code == '12'
  7. [Dòng 308] _re.body.payments[_re.body.payments.length - 1].reason.code == '13'
  8. [Dòng 309] _re.body.payments[_re.body.payments.length - 1].reason.code == '24'
  9. [Dòng 310] _re.body.payments[_re.body.payments.length - 1].reason.code == '25')) {
  10. [Dòng 331] } else if (this._res_post.state == 'authorization_required') {
  11. [Dòng 332] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  12. [Dòng 344] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  13. [Dòng 390] v.length == 15) || (v.length == 16
  14. [Dòng 390] v.length == 19))
  15. [Dòng 391] this._util.checkMod10(v) == true) {
  16. [Dòng 439] cardNo.length == 15)
  17. [Dòng 441] cardNo.length == 16)
  18. [Dòng 442] cardNo.startsWith('81')) && (cardNo.length == 16
  19. [Dòng 442] cardNo.length == 19))
  20. [Dòng 488] event.inputType == 'deleteContentBackward') {
  21. [Dòng 489] if (event.target.name == 'exp_date'
  22. [Dòng 497] event.inputType == 'insertCompositionText') {
  23. [Dòng 512] if (((this.valueDate.length == 4
  24. [Dòng 512] this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  25. [Dòng 512] this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  26. [Dòng 563] v.length == 5) {
  27. [Dòng 571] v.length == 4
  28. [Dòng 575] v.length == 3)
  29. [Dòng 601] _val.value.length == 4
  30. [Dòng 605] _val.value.length == 3)
  31. [Dòng 795] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  32. [Dòng 795] this.valueDate.length == 5)
  33. [Dòng 822] this.requireAvs = this.AVS_COUNTRIES.find(e => e.code == this._i_country_code)? true : false;
  34. [Dòng 873] this.valueDate.length == 4
  35. [Dòng 873] this.valueDate.search('/') == -1)
  36. [Dòng 874] this.valueDate.length == 5))
  37. [Dòng 887] this._i_csc.length == 4) ||
  38. [Dòng 891] this._i_csc.length == 3)
  39. [Dòng 964] country = this.AVS_COUNTRIES.find(e => e.code == res.bin_country);
  40. [Dòng 985] countryCode == 'US' ? US_STATES
  41. [Dòng 986] : countryCode == 'CA' ? CA_STATES

!== (10 điều kiện):
  1. [Dòng 276] if (this.tracking.hasOwnProperty(key) && ((key !== '8'
  2. [Dòng 276] !this._showNameOnCard) || (key !== '9'
  3. [Dòng 563] event.inputType !== 'deleteContentBackward') || v.length == 5) {
  4. [Dòng 745] this._i_country_code !== 'US') {
  5. [Dòng 787] itemRemoved !== '') {
  6. [Dòng 814] if (deviceValue !== 'default') {
  7. [Dòng 818] this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
  8. [Dòng 899] this._i_country_code !== 'default'
  9. [Dòng 924] this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
  10. [Dòng 931] this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {

!= (13 điều kiện):
  1. [Dòng 157] if (params['locale'] != null) {
  2. [Dòng 163] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 296] if (this._res_post.return_url != null) {
  4. [Dòng 298] } else if (this._res_post.links != null
  5. [Dòng 298] this._res_post.links.merchant_return != null
  6. [Dòng 298] this._res_post.links.merchant_return.href != null) {
  7. [Dòng 372] if (ua.indexOf('safari') != -1
  8. [Dòng 439] cardNo != null
  9. [Dòng 490] if (this.valueDate.length != 3) {
  10. [Dòng 570] v != null
  11. [Dòng 600] this.c_csc = (!(_val.value != null
  12. [Dòng 885] this._i_csc != null
  13. [Dòng 966] this.requireAvs = this.isAvsCountry = country != undefined

================================================================================

📁 FILE 78: intercard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/intercard-main/intercard-main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 79: intercard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/intercard-main/intercard-main.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 79] if (_re.status == '200'
  2. [Dòng 79] _re.status == '201') {

================================================================================

📁 FILE 80: menu.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/menu.component.html
📊 Thống kê: 10 điều kiện duy nhất
   - === : 1 lần
   - == : 9 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 156] d_vrbank===true"

== (9 điều kiện):
  1. [Dòng 61] !token) || (type == 1
  2. [Dòng 79] type == 2)"
  3. [Dòng 88] !token) || (type == 3
  4. [Dòng 99] !token) || (type == 5
  5. [Dòng 112] sortMethodArray[i].trim()=='International'"
  6. [Dòng 119] sortMethodArray[i].trim()=='Domestic'"
  7. [Dòng 127] sortMethodArray[i].trim()=='QR'"
  8. [Dòng 134] sortMethodArray[i].trim()=='Paypal'"
  9. [Dòng 141] sortMethodArray[i].trim()=='Bnpl'

================================================================================

📁 FILE 81: menu.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/menu.component.ts
📊 Thống kê: 144 điều kiện duy nhất
   - === : 11 lần
   - == : 67 lần
   - !== : 3 lần
   - != : 63 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 193] if (document.visibilityState === 'visible') {
  2. [Dòng 682] this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_bnpl === 1
  3. [Dòng 703] if (count === 1
  4. [Dòng 706] if (offBanksArr[i] === this.lastDomescard)
  5. [Dòng 723] if (this._res.state === 'unpaid'
  6. [Dòng 723] this._res.state === 'not_paid') {
  7. [Dòng 820] if ('op' === auth
  8. [Dòng 861] } else if ('bank' === auth
  9. [Dòng 866] if (approval.method === 'REDIRECT') {
  10. [Dòng 869] } else if (approval.method === 'POST_REDIRECT') {
  11. [Dòng 1210] if (this.timeLeftPaypal === 0) {

== (67 điều kiện):
  1. [Dòng 200] if (el == 1) {
  2. [Dòng 202] } else if (el == 2) {
  3. [Dòng 204] } else if (el == 4) {
  4. [Dòng 206] } else if (el == 3) {
  5. [Dòng 235] if (!isNaN(_re.status) && (_re.status == '200'
  6. [Dòng 235] _re.status == '201') && _re.body != null) {
  7. [Dòng 240] if (('closed' == this._res_polling.state
  8. [Dòng 240] 'canceled' == this._res_polling.state
  9. [Dòng 240] 'expired' == this._res_polling.state)
  10. [Dòng 260] } else if ('paid' == this._res_polling.state) {
  11. [Dòng 272] this._res_polling.payments == null
  12. [Dòng 274] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  13. [Dòng 276] this._paymentService.getCurrentPage() == 'enter_card'
  14. [Dòng 279] } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
  15. [Dòng 279] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
  16. [Dòng 296] } else if ('not_paid' == this._res_polling.state) {
  17. [Dòng 308] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
  18. [Dòng 315] if (!((this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '8'
  19. [Dòng 316] this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '9'
  20. [Dòng 317] this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '12'
  21. [Dòng 318] this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '13'
  22. [Dòng 319] this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '24'
  23. [Dòng 320] this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '25'))) {
  24. [Dòng 418] if (auth == 'auth') {
  25. [Dòng 420] detail.merchant.id == 'AMWAY') {
  26. [Dòng 453] if (_re.status == '200'
  27. [Dòng 453] _re.status == '201') {
  28. [Dòng 472] if (this.themeConfig.default_method == 'International'
  29. [Dòng 474] } else if (this.themeConfig.default_method == 'Domestic'
  30. [Dòng 476] } else if (this.themeConfig.default_method == 'QR'
  31. [Dòng 478] } else if (this.themeConfig.default_method == 'Paypal'
  32. [Dòng 688] this._auth == 0) {
  33. [Dòng 724] if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
  34. [Dòng 724] this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
  35. [Dòng 726] if (this._res.payments[this._res.payments.length - 1].state == 'approved'
  36. [Dòng 732] } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
  37. [Dòng 741] if (response_code == '8'
  38. [Dòng 741] response_code == '9'
  39. [Dòng 741] response_code == '12'
  40. [Dòng 741] response_code == '13'
  41. [Dòng 741] response_code == '24'
  42. [Dòng 741] response_code == '25') {
  43. [Dòng 768] } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  44. [Dòng 768] this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
  45. [Dòng 804] if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  46. [Dòng 808] } else if (idBrand == 'atm'
  47. [Dòng 886] else if (idBrand == 'kbank'
  48. [Dòng 987] this._res.payments[this._res.payments.length - 1].state == 'pending'
  49. [Dòng 989] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
  50. [Dòng 1008] if ('paid' == this._res.state) {
  51. [Dòng 1032] if (('closed' == this._res.state
  52. [Dòng 1032] 'canceled' == this._res.state
  53. [Dòng 1032] 'expired' == this._res.state
  54. [Dòng 1032] 'paid' == this._res.state)
  55. [Dòng 1035] if ('paid' == this._res.state
  56. [Dòng 1035] 'canceled' == this._res.state) {
  57. [Dòng 1054] this._res.payments == null) {
  58. [Dòng 1056] this._res.payments[this._res.payments.length - 1].state == 'pending') {
  59. [Dòng 1066] if (this._res.currencies[0] == 'USD') {
  60. [Dòng 1262] if (this._res_post.state == 'approved'
  61. [Dòng 1262] this._res_post.state == 'failed') {
  62. [Dòng 1271] } else if (this._res_post.state == 'authorization_required') {
  63. [Dòng 1272] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  64. [Dòng 1286] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  65. [Dòng 1395] if (res.status == '200'
  66. [Dòng 1395] res.status == '201') {
  67. [Dòng 1429] if (data._locale == 'en') {

!== (3 điều kiện):
  1. [Dòng 836] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  2. [Dòng 1176] if (_val !== 3) {
  3. [Dòng 1180] this._util.getElementParams(this.currentUrl, 't_id') !== '') {

!= (63 điều kiện):
  1. [Dòng 228] if (this._idInvoice != null
  2. [Dòng 228] this._paymentService.getState() != 'error') {
  3. [Dòng 234] if (this._paymentService.getCurrentPage() != 'otp') {
  4. [Dòng 235] _re.body != null) {
  5. [Dòng 241] this._res_polling.links != null
  6. [Dòng 241] this._res_polling.links.merchant_return != null //
  7. [Dòng 244] this._util.getElementParams(url_redirect, 'vpc_TxnResponseCode') != '99') {
  8. [Dòng 272] } else if (this._res_polling.merchant != null
  9. [Dòng 272] this._res_polling.merchant_invoice_reference != null
  10. [Dòng 274] } else if (this._res_polling.payments != null
  11. [Dòng 276] this._res_polling.payments[this._res_polling.payments.length - 1].instrument.brand != 'amigo') {
  12. [Dòng 280] this._res_polling.links.merchant_return != null//
  13. [Dòng 299] this._res_polling.payments != null
  14. [Dòng 307] if (this._res_polling.payments != null
  15. [Dòng 311] t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
  16. [Dòng 427] this.type.toString().length != 0) {
  17. [Dòng 433] if (params['locale'] != null) {
  18. [Dòng 440] if ('otp' != this._paymentService.getCurrentPage()) {
  19. [Dòng 444] if (this._paymentService.getInvoiceDetail() != null) {
  20. [Dòng 520] if (this._idInvoice != null) {
  21. [Dòng 544] const tokenListsFilter = tokenLists.filter(item => item.id != tokenData);
  22. [Dòng 699] if (count != 1) {
  23. [Dòng 713] if (this._res.merchant != null
  24. [Dòng 713] this._res.merchant_invoice_reference != null) {
  25. [Dòng 716] if (this._res.merchant.address_details != null) {
  26. [Dòng 724] this._res.links != null//
  27. [Dòng 768] } else if (this._res.payments != null
  28. [Dòng 769] this._res.payments[this._res.payments.length - 1].instrument != null
  29. [Dòng 769] this._res.payments[this._res.payments.length - 1].instrument.issuer != null
  30. [Dòng 770] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
  31. [Dòng 770] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
  32. [Dòng 771] this._res.payments[this._res.payments.length - 1].links != null
  33. [Dòng 771] this._res.payments[this._res.payments.length - 1].links.cancel != null
  34. [Dòng 771] this._res.payments[this._res.payments.length - 1].links.cancel.href != null
  35. [Dòng 789] this._res.payments[this._res.payments.length - 1].links.update != null
  36. [Dòng 789] this._res.payments[this._res.payments.length - 1].links.update.href != null) {
  37. [Dòng 808] this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
  38. [Dòng 809] this._res.payments[this._res.payments.length - 1].authorization != null
  39. [Dòng 809] this._res.payments[this._res.payments.length - 1].authorization.links != null) {
  40. [Dòng 820] this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
  41. [Dòng 820] this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
  42. [Dòng 823] if (this._res.payments[this._res.payments.length - 1].authorization != null
  43. [Dòng 823] this._res.payments[this._res.payments.length - 1].authorization.links != null
  44. [Dòng 829] auth = paramUserName != null ? paramUserName : ''
  45. [Dòng 898] if (this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
  46. [Dòng 1033] this._res.links != null
  47. [Dòng 1033] this._res.links.merchant_return != null //
  48. [Dòng 1054] } else if (this._res.merchant != null
  49. [Dòng 1054] this._res.merchant_invoice_reference != null
  50. [Dòng 1058] this._res.payments[this._res.payments.length - 1].instrument.brand != 'amigo') {
  51. [Dòng 1117] if (!(strInstrument != null
  52. [Dòng 1134] if (this._translate.currentLang != language) {
  53. [Dòng 1182] } else if (this._res.payments != null) {
  54. [Dòng 1264] if (this._res_post.return_url != null) {
  55. [Dòng 1266] } else if (this._res_post.links != null
  56. [Dòng 1266] this._res_post.links.merchant_return != null
  57. [Dòng 1266] this._res_post.links.merchant_return.href != null) {
  58. [Dòng 1328] if (this._res_post != null
  59. [Dòng 1328] this._res_post.links != null
  60. [Dòng 1396] if (res.body != null
  61. [Dòng 1396] res.body.links != null
  62. [Dòng 1396] res.body.links.merchant_return != null
  63. [Dòng 1397] res.body.links.merchant_return.href != null) {

================================================================================

📁 FILE 82: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 83: qr-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 84: qr-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 42] 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {

================================================================================

📁 FILE 85: qr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/qr-main/qr-main.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 6] filteredData.length === 0
  2. [Dòng 6] filteredDataOther.length === 0"
  3. [Dòng 42] filteredDataMobile.length === 0
  4. [Dòng 42] filteredDataOtherMobile.length === 0"

================================================================================

📁 FILE 86: qr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/qr-main/qr-main.component.ts
📊 Thống kê: 27 điều kiện duy nhất
   - === : 10 lần
   - == : 9 lần
   - !== : 0 lần
   - != : 8 lần
--------------------------------------------------------------------------------

=== (10 điều kiện):
  1. [Dòng 198] this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
  2. [Dòng 199] this.filteredDataOther = this.appList.filter(item => item.type === 'other');
  3. [Dòng 200] this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
  4. [Dòng 201] this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
  5. [Dòng 227] this.appList.length === 1) {
  6. [Dòng 229] if ((this.filteredDataMobile.length === 1
  7. [Dòng 229] this.filteredDataOtherMobile.length === 1)
  8. [Dòng 284] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  9. [Dòng 285] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  10. [Dòng 291] if (item.type === 'mobile_banking') {

== (9 điều kiện):
  1. [Dòng 138] this.themeConfig.deeplink_status == 'Off' ? false : true
  2. [Dòng 241] if (d.b.code == s) {
  3. [Dòng 290] if (item.available == true) {
  4. [Dòng 356] if (_re.status == '200'
  5. [Dòng 356] _re.status == '201') {
  6. [Dòng 359] if (appcode == 'grabpay'
  7. [Dòng 359] appcode == 'momo') {
  8. [Dòng 362] if (type == 2
  9. [Dòng 401] err.error.code == '04') {

!= (8 điều kiện):
  1. [Dòng 155] if (params['locale'] != null) {
  2. [Dòng 161] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 175] if (!(strInstrument != null
  4. [Dòng 320] if (appcode != null) {
  5. [Dòng 574] if (this._res_post != null
  6. [Dòng 574] this._res_post.links != null
  7. [Dòng 574] this._res_post.links.merchant_return != null
  8. [Dòng 575] this._res_post.links.merchant_return.href != null) {

================================================================================

📁 FILE 87: safe-html.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/qr-main/safe-html.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 88: bnpl-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/sorting-option/bnpl-form/bnpl-form.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 3 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 1] type === 5"
  2. [Dòng 2] [ngStyle]="{'border-color': (type === 5
  3. [Dòng 16] type === 5

== (1 điều kiện):
  1. [Dòng 4] !token) || (type == 5

================================================================================

📁 FILE 89: bnpl-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/sorting-option/bnpl-form/bnpl-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 90: domescard-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 4 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 1] type === 2"
  2. [Dòng 3] [ngStyle]="{'border-color': (type === 2
  3. [Dòng 14] type === 2
  4. [Dòng 14] type === '2'

== (1 điều kiện):
  1. [Dòng 4] !token)  || (type == 2

================================================================================

📁 FILE 91: domescard-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 92: intercard-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 3 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 1] type === 1"
  2. [Dòng 2] [ngStyle]="{'border-color': (type === 1
  3. [Dòng 19] type === 1

== (1 điều kiện):
  1. [Dòng 4] !token) || (type == 1

================================================================================

📁 FILE 93: intercard-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 94: paypal-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 1] type === 3"
  2. [Dòng 3] [ngStyle]="{'border-color': (type === 3

================================================================================

📁 FILE 95: paypal-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 96: qr-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/sorting-option/qr-form/qr-form.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 1] type === 4"
  2. [Dòng 2] [ngStyle]="{'border-color': (type === 4
  3. [Dòng 13] type === 4

================================================================================

📁 FILE 97: qr-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/main/menu/sorting-option/qr-form/qr-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 98: bnpl-management.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/model/bnpl-management.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 99: homecredit-management.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/model/homecredit-management.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 100: kbank-management.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/model/kbank-management.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 101: overlay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/overlay/overlay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 102: overlay.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/overlay/overlay.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 103: overlay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/overlay/overlay.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 104: bank-amount.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/pipe/bank-amount.pipe.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 11] return ((a.id == id
  2. [Dòng 11] a.code == id) && a.type.includes(type));

================================================================================

📁 FILE 105: auth.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/services/auth.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 106: close-dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/services/close-dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 107: data.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/services/data.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 108: deep_link.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/services/deep_link.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 9] if (isIphone == true) {
  2. [Dòng 22] } else if (isAndroid == true) {

================================================================================

📁 FILE 109: dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/services/dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 110: handle_bnpl_token.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/services/handle_bnpl_token.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 111: multiple_method.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/services/multiple_method.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 112: payment.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/services/payment.service.ts
📊 Thống kê: 8 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 8 lần
--------------------------------------------------------------------------------

!= (8 điều kiện):
  1. [Dòng 109] if (idInvoice != null
  2. [Dòng 109] idInvoice != 0)
  3. [Dòng 119] idInvoice != 0) {
  4. [Dòng 260] let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
  5. [Dòng 274] if (this._merchantid != null
  6. [Dòng 274] this._tranref != null
  7. [Dòng 274] this._state != null
  8. [Dòng 349] urlCancel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');

================================================================================

📁 FILE 113: success.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/success/success.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 52] amigo_type == 'SP'"
  2. [Dòng 96] amigo_type == 'PL'"

================================================================================

📁 FILE 114: success.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/success/success.component.ts
📊 Thống kê: 14 điều kiện duy nhất
   - === : 5 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 137] params.timeout === 'true') {
  2. [Dòng 156] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  3. [Dòng 156] _re.body.state === 'unpaid');
  4. [Dòng 219] params.name === 'CUSTOMER_INTIME'
  5. [Dòng 219] params.code === '09') {

== (3 điều kiện):
  1. [Dòng 167] if (this.res.currencies[0] == 'USD') {
  2. [Dòng 195] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
  3. [Dòng 206] this.res.themes.theme == 'general') {

!= (6 điều kiện):
  1. [Dòng 193] } else if (this.res.payments[this.res.payments.length - 1].instrument.issuer.brand != null
  2. [Dòng 194] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id != null
  3. [Dòng 312] if (this.res != null
  4. [Dòng 312] this.res.links != null
  5. [Dòng 312] this.res.links.merchant_return != null
  6. [Dòng 313] this.res.links.merchant_return.href != null) {

================================================================================

📁 FILE 115: index.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/translate/index.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 116: lang-en.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/translate/lang-en.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 117: lang-vi.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/translate/lang-vi.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 118: translate.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/translate/translate.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 119: translate.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/translate/translate.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 37] if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
  2. [Dòng 38] else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';

================================================================================

📁 FILE 120: translations.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/translate/translations.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 121: apps-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/util/apps-info.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 456] if (e.name == bankSwift) { // TODO: get by swift

================================================================================

📁 FILE 122: banks-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/util/banks-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1065] if (+e.id == bankId) {
  2. [Dòng 1115] if (e.swiftCode == bankSwift) {

================================================================================

📁 FILE 123: iso-ca-states.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/util/iso-ca-states.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 124: iso-us-states.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/util/iso-us-states.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 125: util.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/app/util/util.ts
📊 Thống kê: 40 điều kiện duy nhất
   - === : 16 lần
   - == : 17 lần
   - !== : 3 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (16 điều kiện):
  1. [Dòng 14] if (target.tagName === 'A'
  2. [Dòng 73] if (v.length === 2
  3. [Dòng 73] this.flag.length === 3
  4. [Dòng 73] this.flag.charAt(this.flag.length - 1) === '/') {
  5. [Dòng 77] if (v.length === 1) {
  6. [Dòng 79] } else if (v.length === 2) {
  7. [Dòng 82] v.length === 2) {
  8. [Dòng 90] if (len === 2) {
  9. [Dòng 190] if (M[1] === 'Chrome') {
  10. [Dòng 360] if (param === key) {
  11. [Dòng 576] pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
  12. [Dòng 580] target === 0
  13. [Dòng 656] if (cardTypeBank === 'bank_card_number') {
  14. [Dòng 659] } else if (cardTypeBank === 'bank_account_number') {
  15. [Dòng 709] if (event.keyCode === 8
  16. [Dòng 709] event.key === "Backspace"

== (17 điều kiện):
  1. [Dòng 40] if (temp.length == 0) {
  2. [Dòng 47] return (counter % 10 == 0);
  3. [Dòng 164] if (this.checkCount == 1) {
  4. [Dòng 176] if (results == null) {
  5. [Dòng 209] if (c.length == 3) {
  6. [Dòng 222] d = d == undefined ? '.' : d
  7. [Dòng 223] t = t == undefined ? '
  8. [Dòng 348] return results == null ? null : results[1]
  9. [Dòng 709] event.inputType == 'deleteContentBackward') {
  10. [Dòng 710] if (event.target.name == 'exp_date'
  11. [Dòng 718] event.inputType == 'insertCompositionText') {
  12. [Dòng 732] if (((_val.length == 4
  13. [Dòng 732] _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  14. [Dòng 732] _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  15. [Dòng 758] iss_date.length == 4
  16. [Dòng 758] iss_date.search('/') == -1)
  17. [Dòng 759] iss_date.length == 5))

!== (3 điều kiện):
  1. [Dòng 355] queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
  2. [Dòng 356] if (queryString !== '') {
  3. [Dòng 580] if (target !== 0

!= (4 điều kiện):
  1. [Dòng 192] if (tem != null) {
  2. [Dòng 197] if ((tem = ua.match(/version\/(\d+)/i)) != null) {
  3. [Dòng 654] if (ua.indexOf('safari') != -1
  4. [Dòng 711] if (v.length != 3) {

================================================================================

📁 FILE 126: qrcode.js
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/assets/script/qrcode.js
📊 Thống kê: 67 điều kiện duy nhất
   - === : 2 lần
   - == : 53 lần
   - !== : 0 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 2255] if (typeof define === 'function'
  2. [Dòng 2257] } else if (typeof exports === 'object') {

== (53 điều kiện):
  1. [Dòng 68] if (_dataCache == null) {
  2. [Dòng 85] if ( (0 <= r && r <= 6 && (c == 0
  3. [Dòng 85] c == 6) )
  4. [Dòng 86] || (0 <= c && c <= 6 && (r == 0
  5. [Dòng 86] r == 6) )
  6. [Dòng 107] if (i == 0
  7. [Dòng 122] _modules[r][6] = (r % 2 == 0);
  8. [Dòng 129] _modules[6][c] = (c % 2 == 0);
  9. [Dòng 152] if (r == -2
  10. [Dòng 152] r == 2
  11. [Dòng 152] c == -2
  12. [Dòng 152] c == 2
  13. [Dòng 153] || (r == 0
  14. [Dòng 153] c == 0) ) {
  15. [Dòng 169] ( (bits >> i) & 1) == 1);
  16. [Dòng 226] if (col == 6) col -= 1;
  17. [Dòng 232] if (_modules[row][col - c] == null) {
  18. [Dòng 237] dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
  19. [Dòng 249] if (bitIndex == -1) {
  20. [Dòng 458] margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
  21. [Dòng 498] if (typeof arguments[0] == 'object') {
  22. [Dòng 587] margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
  23. [Dòng 733] if (b == -1) throw 'eof';
  24. [Dòng 741] if (b0 == -1) break;
  25. [Dòng 767] if (typeof b == 'number') {
  26. [Dòng 768] if ( (b & 0xff) == b) {
  27. [Dòng 910] return function(i, j) { return (i + j) % 2 == 0
  28. [Dòng 912] return function(i, j) { return i % 2 == 0
  29. [Dòng 914] return function(i, j) { return j % 3 == 0
  30. [Dòng 916] return function(i, j) { return (i + j) % 3 == 0
  31. [Dòng 918] return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
  32. [Dòng 920] return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
  33. [Dòng 922] return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
  34. [Dòng 924] return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
  35. [Dòng 1011] if (r == 0
  36. [Dòng 1011] c == 0) {
  37. [Dòng 1015] if (dark == qrcode.isDark(row + r, col + c) ) {
  38. [Dòng 1036] if (count == 0
  39. [Dòng 1036] count == 4) {
  40. [Dòng 1149] if (typeof num.length == 'undefined') {
  41. [Dòng 1155] num[offset] == 0) {
  42. [Dòng 1495] if (typeof rsBlock == 'undefined') {
  43. [Dòng 1538] return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
  44. [Dòng 1543] _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
  45. [Dòng 1599] if (data.length - i == 1) {
  46. [Dòng 1601] } else if (data.length - i == 2) {
  47. [Dòng 1866] } else if (n == 62) {
  48. [Dòng 1868] } else if (n == 63) {
  49. [Dòng 1928] if (_buflen == 0) {
  50. [Dòng 1937] if (c == '=') {
  51. [Dòng 1961] } else if (c == 0x2b) {
  52. [Dòng 1963] } else if (c == 0x2f) {
  53. [Dòng 2133] if (table.size() == (1 << bitLength) ) {

!= (12 điều kiện):
  1. [Dòng 119] if (_modules[r][6] != null) {
  2. [Dòng 126] if (_modules[6][c] != null) {
  3. [Dòng 144] if (_modules[row][col] != null) {
  4. [Dòng 365] while (buffer.getLengthInBits() % 8 != 0) {
  5. [Dòng 750] if (count != numChars) {
  6. [Dòng 751] throw count + ' != ' + numChars
  7. [Dòng 878] while (data != 0) {
  8. [Dòng 1733] if (test.length != 2
  9. [Dòng 1733] ( (test[0] << 8) | test[1]) != code) {
  10. [Dòng 1894] if (_length % 3 != 0) {
  11. [Dòng 2067] if ( (data >>> length) != 0) {
  12. [Dòng 2178] return typeof _map[key] != 'undefined'

================================================================================

📁 FILE 127: environment.dev.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/environments/environment.dev.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 128: environment.mtf.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/environments/environment.mtf.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 129: environment.prod.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/environments/environment.prod.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 130: environment.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/environments/environment.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 131: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 132: main.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/main.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 133: polyfills.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/polyfills.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 134: test.ts
📍 Đường dẫn: /root/projects/onepay/paygate-apple/src/test.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📋 TÓM TẮT TẤT CẢ ĐIỀU KIỆN DUY NHẤT:
====================================================================================================

=== (178 điều kiện duy nhất):
------------------------------------------------------------
1. return lang === this._translate.currentLang
2. checkDupTran === false"
3. isPopupSupport === 'True') || (rePayment
4. isPopupSupport === 'True'"
5. isPopupSupport === 'True')">
6. params.timeout === 'true') {
7. params.kbank === 'true') {
8. this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
9. _re.body.state === 'unpaid');
10. if (this.errorCode === 'overtime'
11. this.errorCode === '253') {
12. params.name === 'CUSTOMER_INTIME'
13. params.code === '09') {
14. if (this.timeLeft === 0) {
15. if (YY % 400 === 0
16. YY % 4 === 0)) {
17. if (YYYY % 400 === 0
18. YYYY % 4 === 0)) {
19. params['code'] === '09') {
20. params.name === 'CUSTOMER_INTIME')) {
21. return index === array.findIndex(obj => {
22. return JSON.stringify(obj) === _value
23. bnpl.code === 'kbank'"
24. this.bnpls.push(this.sublist.find(e => e.name === 'KBank Pay Later'));
25. this.bnpls.push(this.sublist.find(e => e.name === 'Home PayLater'));
26. this.bnpls.push(this.sublist.find(e => e.name === 'Kredivo'));
27. this.bnpls.push(this.sublist.find(e => e.name === 'Insta'));
28. object.id === value ? 'select-option-active' : ''"
29. object.id === value"
30. if (this.value === 'add') {
31. <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
32. [class.red_border_no_background]="c_expdate && (valueDate.length === 0
33. valueDate.trim().length === 0)"
34. if (isIE[0] === 'MSIE'
35. +isIE[1] === 10) {
36. if ((_val.value.substr(-1) === ' '
37. _val.value.length === 24) {
38. if (this.cardTypeBank === 'bank_card_number') {
39. } else if (this.cardTypeBank === 'bank_account_number') {
40. } else if (this.cardTypeBank === 'bank_username') {
41. } else if (this.cardTypeBank === 'bank_customer_code') {
42. this.cardTypeBank === 'bank_card_number'
43. if (this.cardTypeOcean === 'IB') {
44. } else if (this.cardTypeOcean === 'MB') {
45. if (_val.value.substr(0, 2) === '84') {
46. } else if (this.cardTypeOcean === 'ATM') {
47. if (this.cardTypeBank === 'bank_account_number') {
48. this.cardTypeBank === 'bank_card_number') {
49. if (this.cardTypeBank === 'bank_card_number'
50. if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
51. if (event.keyCode === 8
52. event.key === "Backspace"
53. if (v.length === 2
54. this.flag.length === 3
55. this.flag.charAt(this.flag.length - 1) === '/') {
56. if (v.length === 1) {
57. } else if (v.length === 2) {
58. v.length === 2) {
59. if (len === 2) {
60. if ((this.cardTypeBank === 'bank_account_number'
61. this.cardTypeBank === 'bank_username'
62. this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
63. this.cardTypeOcean === 'ATM')
64. || (this.cardTypeOcean === 'IB'
65. if (valIn === this._translate.instant('bank_card_number')) {
66. } else if (valIn === this._translate.instant('bank_account_number')) {
67. } else if (valIn === this._translate.instant('bank_username')) {
68. } else if (valIn === this._translate.instant('bank_customer_code')) {
69. if (_val.value === ''
70. _val.value === null
71. _val.value === undefined) {
72. if (_val.value && (this.cardTypeBank === 'bank_card_number'
73. if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
74. this.cardTypeOcean === 'MB') {
75. this.cardTypeOcean === 'IB'
76. if ((this.cardTypeBank === 'bank_card_number'
77. if (this.cardName === undefined
78. this.cardName === '') {
79. if (this.valueDate === undefined
80. this.valueDate === '') {
81. c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
82. _inExpDate.trim().length === 0)"
83. ready===1"
84. if (this.timeLeft === 10) {
85. if (this.runTime === true) {
86. if (this.runTime === true) this.submitCardBanking();
87. if (approval.method === 'REDIRECT') {
88. } else if (approval.method === 'POST_REDIRECT') {
89. if (this.timeLeft === 1) {
90. } else if (valIn === this._translate.instant('internet_banking')) {
91. if (_val.value && (this.cardTypeBank === 'bank_card_number')) {
92. if (focusElement === 'card_name') {
93. } else if (focusElement === 'exp_date'
94. focusExpDateElement === 'card_name') {
95. if (this.cardTypeBank === 'bank_account_number'
96. filteredData.length === 0"
97. if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
98. filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
99. if (valOut === 'auth') {
100. if (this._b === '1'
101. this._b === '20'
102. this._b === '64' ) {
103. if (this._b === '36'
104. this._b === '18'
105. if (this._b === '19'
106. this._b === '16'
107. this._b === '25'
108. this._b === '33'
109. this._b === '39'
110. this._b === '9'
111. this._b === '11'
112. this._b === '17'
113. this._b === '36'
114. this._b === '44'
115. this._b === '64'
116. if (this._b === '20'
117. if (this._b === '18') {
118. return (bankId === '1'
119. bankId === '20'
120. bankId === '64');
121. return (bankId === '36'
122. bankId === '18'
123. if (_formCard.country === 'default') {
124. if ((v.substr(-1) === ' '
125. this._i_country_code === 'US') {
126. const insertIndex = this._i_country_code === 'US' ? 5 : 3
127. if (temp[i] === '-'
128. temp[i] === ' ') {
129. insertIndex === 3 ? ' ' : itemRemoved)
130. this.c_country = _val.value === 'default'
131. d_vrbank===true"
132. if (document.visibilityState === 'visible') {
133. this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_bnpl === 1
134. if (count === 1
135. if (offBanksArr[i] === this.lastDomescard)
136. if (this._res.state === 'unpaid'
137. this._res.state === 'not_paid') {
138. if ('op' === auth
139. } else if ('bank' === auth
140. if (this.timeLeftPaypal === 0) {
141. filteredData.length === 0
142. filteredDataOther.length === 0"
143. filteredDataMobile.length === 0
144. filteredDataOtherMobile.length === 0"
145. this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
146. this.filteredDataOther = this.appList.filter(item => item.type === 'other');
147. this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
148. this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
149. this.appList.length === 1) {
150. if ((this.filteredDataMobile.length === 1
151. this.filteredDataOtherMobile.length === 1)
152. item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
153. filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
154. if (item.type === 'mobile_banking') {
155. type === 5"
156. [ngStyle]="{'border-color': (type === 5
157. type === 5
158. type === 2"
159. [ngStyle]="{'border-color': (type === 2
160. type === 2
161. type === '2'
162. type === 1"
163. [ngStyle]="{'border-color': (type === 1
164. type === 1
165. type === 3"
166. [ngStyle]="{'border-color': (type === 3
167. type === 4"
168. [ngStyle]="{'border-color': (type === 4
169. type === 4
170. if (target.tagName === 'A'
171. if (M[1] === 'Chrome') {
172. if (param === key) {
173. pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
174. target === 0
175. if (cardTypeBank === 'bank_card_number') {
176. } else if (cardTypeBank === 'bank_account_number') {
177. if (typeof define === 'function'
178. } else if (typeof exports === 'object') {

== (650 điều kiện duy nhất):
------------------------------------------------------------
1. 'vi' == params['locale']) {
2. 'en' == params['locale']) {
3. errorCode == '11'"
4. errorCode && (errorCode == '253' || errorCode == 'overtime')
5. errorCode == 'overtime')">
6. <div class="footer-button" *ngIf="(isSent == false
7. isSent == false
8. <div class="payment_again" *ngIf="rePayment && !timeOut" [class.select_only]="!(isSent == false
9. <div class="cancel_transaction" *ngIf="checkDupTran" [class.select_only]="!(isSent == false
10. errorCode == 'overtime'
11. errorCode == '253'"
12. <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
13. !(errorCode == 'overtime' || errorCode == '253') && isBack
14. if (params && (params['bnpl'] == 'true')) {
15. if (params && (params['bnpl'] == 'false')) {
16. if (this.res.currencies[0] == 'USD') {
17. this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'amigo') {
18. this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'kredivo') {
19. this.res.themes.theme == 'general') {
20. params.response_code == 'overtime') {
21. if (_re.status == '200'
22. _re.status == '201') {
23. if (_re2.status == '200'
24. _re2.status == '201') {
25. if (this.errorCode == 'overtime'
26. this.errorCode == '253') {
27. if (this.paymentInformation.type == 'bnpl') {
28. if (this.paymentInformation.provider == 'amigo'
29. this.errorCode == '2') {
30. else if (this.paymentInformation.provider == 'kbank'
31. else if (this.paymentInformation.provider == 'homecredit'
32. else if (this.paymentInformation.provider == 'kredivo'
33. this.res.state == 'canceled') {
34. if (this.isTimePause == false) {
35. if (this.url_new_invoice != null && (_re.status == 201
36. _re.status == 200)) {
37. if ((dataPassed.status == '200'
38. dataPassed.status == '201') && dataPassed.body != null) {
39. dataPassed.body.themes.logo_full == 'True') {
40. payments[payments.length - 1].state == 'pending'
41. payments[payments.length - 1].instrument.issuer.brand.id == 'amigo') {
42. if (this.locale == 'en') {
43. if (bankId == 3
44. bankId == 61
45. bankId == 8
46. bankId == 49
47. bankId == 48
48. bankId == 10
49. bankId == 53
50. bankId == 17
51. bankId == 65
52. bankId == 23
53. bankId == 52
54. bankId == 27
55. bankId == 66
56. bankId == 9
57. bankId == 54
58. bankId == 37
59. bankId == 38
60. bankId == 39
61. bankId == 40
62. bankId == 42
63. bankId == 44
64. bankId == 72
65. bankId == 59
66. bankId == 51
67. bankId == 64
68. bankId == 58
69. bankId == 56
70. bankId == 55
71. bankId == 60
72. bankId == 68 //Eximbank Napas
73. bnplDetail.method == 'SP'"
74. bnplDetail.method == 'PL'"
75. _re.code == '0'
76. packageItem.product_code == productCode) {
77. a.product_code == 'SP') {
78. a.product_code == 'PL') {
79. if (this.selectedIndex == 0
80. } else if ((this.selectedIndex == 1
81. this.payLaterSubmit) || (this.selectedIndex == 0
82. item.prepaid_percent == this.selectedPrePaid
83. item.installment_month == this.selectedPayLater) {
84. if (string == 'SP') {
85. } else if (string == 'PL') {
86. if(this._locale == 'en'){
87. if (this._res_post.state == 'approved'
88. this._res_post.state == 'failed') {
89. if (this._res_post.state == 'failed') {
90. && (_re.body.payments[_re.body.payments.length - 1].reason.code == '8'
91. _re.body.payments[_re.body.payments.length - 1].reason.code == '9'
92. _re.body.payments[_re.body.payments.length - 1].reason.code == '12'
93. _re.body.payments[_re.body.payments.length - 1].reason.code == '13'
94. _re.body.payments[_re.body.payments.length - 1].reason.code == '24'
95. _re.body.payments[_re.body.payments.length - 1].reason.code == '25')) {
96. } else if (this._res_post.state == 'authorization_required') {
97. if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
98. bnpl.status == 'disabled'"
99. bnpl.status == 'active'"
100. [class.bnpl-active-item]="bnpl.status == 'active'" (click)="onClick(bnpl.status == 'disabled')">
101. bnpl.code == 'kbank'
102. bnpl.code == 'insta'"
103. bnpl.code == 'homepaylater'"
104. bnpl.status == 'disabled'
105. bnpl.code == 'kredivo'
106. selectedBnpl.code == 'insta'"
107. selectedBnpl.code == 'kbank'"
108. selectedBnpl.code == 'homepaylater'"
109. selectedBnpl.code == 'kredivo'"
110. _auth == 1"
111. value == 'add' ? 'select-option-active' : ''"
112. value == 'add'"
113. value=='add'"
114. value == 'add') || !onepayChecked.value" [disabled]="(isInvalid()
115. value == 'add') || !onepayChecked.value">
116. if (response.status == '200'
117. response.status == '201') {
118. this.listTokenBnpl.length == 0) {
119. if (this.value == object.id) {
120. if (name == 'email') {
121. if (name == 'phoneNumber') {
122. if (name == 'fullname') {
123. this._res_post.state == 'authorization_required') {
124. this._res_post.code == 'KB-02') {
125. !cmndBlur ? 'no-border' : ((kbankDetail['citizen_id'] && !(kbankDetail['citizen_id'].length == 9 || kbankDetail['citizen_id'].length == 12) || !kbankDetail['citizen_id']) &&  bnplForm.controls['citizen_id'].touched) ? 'ng-invalid ng-dirty' : ''
126. bnplForm.controls['citizen_id'].touched && kbankDetail['citizen_id'] && !(kbankDetail['citizen_id'].length == 9 || kbankDetail['citizen_id'].length == 12) && cmndBlur
127. this.value == 'add')){
128. list = this.data.customer.tokens.filter((item) => item.instrument.issuer.brand.id == 'kbank')
129. return (this.bnplForm.invalid || ( this.kbankDetail['citizen_id'] && !(this.kbankDetail['citizen_id'].length == 9) && !(this.kbankDetail['citizen_id'].length == 12)));
130. if (name == 'citizen_id') {
131. plan.type=='1'"
132. plan.applicable=='premium_users'"
133. "applicable": (type=="6"
134. type=="12")? "premium_users" : "basic_or_premium_users"
135. this.bnplDetail.fullname?.length == 0 ? this._translate.instant('kredivo_empty_fullname_message')
136. this.bnplDetail.email?.length == 0 ? this._translate.instant('kredivo_empty_email_message')
137. else if (this._res.code == '2') {
138. _re.body.payments[_re.body.payments.length - 1].reason.code == 'B4') {
139. if (this._b == 18) {//8-MB Bank;18-oceanbank
140. bnpl.code == 'kbank'"
141. bnpl.status == 'active'
142. bnpl.code == 'homepaylater'
143. if (bnpl.status == 'disabled') {
144. if (this._b == 18
145. this._b == 19) {
146. if (this._b == 19) {//19BIDV
147. } else if (this._b == 3
148. this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
149. if (this._b == 27) {
150. } else if (this._b == 12) {// 12SHB
151. } else if (this._b == 18) { //18Oceanbank-ocb
152. if (this._b == 19
153. this._b == 3
154. this._b == 27
155. this._b == 12) {
156. } else if (this._b == 18) {
157. if (this.checkBin(_val.value) && (this._b == 3
158. this._b == 27)) {
159. if (this._b == 3) {
160. this.cardTypeOcean == 'ATM') {
161. if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
162. this._b == 18)) {
163. if (this.checkBin(v) && (this._b == 3
164. event.inputType == 'deleteContentBackward') {
165. if (event.target.name == 'exp_date'
166. event.inputType == 'insertCompositionText') {
167. if (((this.valueDate.length == 4
168. this.valueDate.search('/') == -1) || this.valueDate.length == 5)
169. this.valueDate.length == 5)
170. if (temp.length == 0) {
171. return (counter % 10 == 0);
172. } else if (this._b == 19) {
173. } else if (this._b == 27) {
174. if (this._b == 12) {
175. if (this.cardTypeBank == 'bank_customer_code') {
176. } else if (this.cardTypeBank == 'bank_account_number') {
177. _formCard.exp_date.length == 5
178. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
179. this._b == 3)) {//27-pvcombank;3-TPB ;
180. if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
181. this._b == 19
182. this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
183. if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
184. if (this.cardTypeOcean == 'IB') {
185. } else if (this.cardTypeOcean == 'MB') {
186. } else if (this.cardTypeOcean == 'ATM') {
187. if (this._b == 18) {
188. if (this._b == 27
189. this._b == 18) {
190. if ((cardNo.length == 16
191. if ((cardNo.length == 16 || (cardNo.length == 19
192. && ((this._b == 18
193. cardNo.length == 19) || this._b != 18)
194. if (this._b == +e.id) {
195. if (valIn == 1) {
196. } else if (valIn == 2) {
197. this._b == 3) {
198. if (this._b == 19) {
199. if (cardType == this._translate.instant('internetbanking')
200. } else if (cardType == this._translate.instant('mobilebanking')
201. } else if (cardType == this._translate.instant('atm')
202. this._b == 18))) {
203. } else if (this._b == 18
204. this.c_expdate = !(((this.valueDate.length == 4
205. this.valueDate.length == 4
206. this.valueDate.search('/') == -1)
207. this.valueDate.length == 5))
208. <div [ngStyle]="{'margin-top': !checkTwoEnabled ? '15px' : '0px' }" *ngIf="(cardTypeBank == 'bank_card_number') &&(_b == 67
209. (cardTypeBank == 'bank_card_number') &&(_b == 67 || checkTwoEnabled)&& ready===1
210. <div [ngStyle]="{'margin-top': !checkTwoEnabled ? '15px' : '0px' }" class="nd-bank-card-two" *ngIf="(cardTypeBank == 'internet_banking')&&(_b == 2
211. if (this._b == 67
212. this._b == 2) {//19BIDV
213. if(this._b == 2
214. if(this._b == 67 ){
215. return this._b == 2
216. this._b == 67
217. _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
218. this.checkMod10(cardNo) == true
219. if (this._b != 68 || (this._b == 68
220. return ((value.length == 4
221. value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
222. value.length == 5) && parseInt(value.split('/')[0]
223. || (this._util.checkValidExpireMonthYear(value) && (this._b == 2
224. this._b == 20
225. this._b == 33
226. this._b == 39
227. this._b == 43
228. this._b == 45
229. this._b == 64
230. this._b == 68
231. this._b == 72))) //sonnh them Vietbank 72
232. this._inExpDate.length == 4
233. this._inExpDate.search('/') == -1)
234. this._inExpDate.length == 5))
235. || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 20
236. this._b == 2
237. this._b == 72)));
238. if (this._b == 8) {//MB Bank
239. if (this._b == 18) {//Oceanbank
240. if (this._b == 8) {
241. if (this._b == 12) { //SHB
242. } else if (this._res.state == 'authorization_required') {
243. _re.body.payments[_re.body.payments.length - 1].reason.code == '25'
244. if (this.challengeCode == '') {
245. <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_card_number')">
246. <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_account_number')">
247. if(this._b == 12) this.isShbGroup = true;
248. return this._b == 9
249. this._b == 11
250. this._b == 16
251. this._b == 17
252. this._b == 25
253. this._b == 44
254. this._b == 57
255. this._b == 59
256. this._b == 61
257. this._b == 63
258. this._b == 69
259. if (this.cardTypeBank == 'bank_account_number') {
260. cardNo.length == 19) && cardNo.startsWith("970443") && this._util.checkMod10(cardNo) && this.checkBin(cardNo) ) {
261. if (this._b == 2
262. this._b == 31
263. this._b == 80) {
264. if (this._b == 2) {
265. } else if (this._b == 6) {
266. } else if (this._b == 31) {
267. } else if (this._b == 80) {
268. if (this._b == 5) {//5-vib;
269. if (this._b == 5) {
270. if (this.checkBin(_val.value) && (this._b == 5)) {
271. if (this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
272. if (this.checkBin(v) && (this._b == 5)) {
273. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 5)) {//27-pvcombank;3-TPB ;
274. if (this.cardName != null && this.cardName.length > 0 && (this._b == 5)) {//3-TPB ;19-BIDV;27-pvcombank;
275. _b == 68"
276. if (this._b == 11
277. this._b == 73
278. this._b == 72
279. this._b == 36) {//seabank
280. if (this._b == 1
281. this._b == 36
282. this._b == 55
283. this._b == 47
284. this._b == 48
285. this._b == 59) {
286. this._b == 54
287. this._b == 14
288. this._b == 15
289. this._b == 24
290. this._b == 8
291. this._b == 10
292. this._b == 22
293. this._b == 23
294. this._b == 30
295. this._b == 17) {
296. (cardNo.length == 19
297. (cardNo.length == 19 && (this._b == 1
298. this._b == 4
299. this._b == 59))
300. this._util.checkMod10(cardNo) == true
301. || (this._util.checkValidExpireMonthYear(value) && (this._b == 11
302. this._b == 36))) //sonnh them Vietbank 72
303. || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 11
304. this._b == 36)));
305. _auth==0
306. *ngIf="(!token&&_auth==0&&(_b==1
307. _b==4
308. _b==7
309. _b==8
310. _b==9
311. _b==10
312. _b==11
313. _b==14
314. _b==15
315. _b==16
316. _b==17
317. _b==20
318. _b==22
319. _b==23
320. _b==24
321. _b==25
322. _b==30
323. _b==33
324. _b==34
325. _b==35
326. _b==36
327. _b==37
328. _b==38
329. _b==39
330. _b==40
331. _b==41
332. _b==42
333. _b==43
334. _b==44
335. _b==45
336. this._b==46
337. _b==47
338. _b==48
339. _b==49
340. _b==50
341. _b==51
342. _b==52
343. _b==53
344. _b==54
345. _b==55
346. _b==56
347. _b==57
348. _b==58
349. _b==59
350. _b==60
351. _b==61
352. _b==62
353. _b==63
354. _b==64
355. this._b==65
356. _b==66
357. _b==68
358. _b==69
359. this._b==70
360. this._b==71
361. this._b==72
362. _b==73)) || (token && _b == 16)">
363. _b == 16)">
364. _auth==0 && techcombankGroupSelected
365. _auth==0 && shbGroupSelected
366. _auth==0 && onepaynapasGroupSelected
367. _auth==0 && bankaccountGroupSelected
368. _auth==0 && vibbankGroupSelected
369. (token || _auth==1) && _b != 16
370. this._auth == 0) {
371. if(e.id == '31'){
372. } else if(e.id =='80'){
373. if (d.b.card_list == s) {
374. if(item.b.id == '2'
375. item.b.id =='67'){
376. $event == 'true') {
377. _b: this._b=='2'? '67':this._b
378. if (bankId==1
379. bankId==4
380. bankId==7
381. bankId==8
382. bankId==9
383. bankId==10
384. bankId==11
385. bankId==14
386. bankId==15
387. bankId==16
388. bankId==17
389. bankId==20
390. bankId==22
391. bankId==23
392. bankId==24
393. bankId==25
394. bankId==30
395. bankId==33
396. bankId==34
397. bankId==35
398. bankId==36
399. bankId==37
400. bankId==38
401. bankId==39
402. bankId==40
403. bankId==41
404. bankId==42
405. bankId==43
406. bankId==44
407. bankId==45
408. bankId==46
409. bankId==47
410. bankId==48
411. bankId==49
412. bankId==50
413. bankId==51
414. bankId==52
415. bankId==53
416. bankId==54
417. bankId==55
418. bankId==56
419. bankId==57
420. bankId==58
421. bankId==59
422. bankId==60
423. bankId==61
424. bankId==62
425. bankId==63
426. bankId==64
427. bankId==65
428. bankId==66
429. bankId==68
430. bankId==69
431. bankId==70
432. bankId==71
433. bankId==72
434. bankId==73
435. bankId==32) {
436. } else if (bankId == 6
437. bankId == 31
438. bankId == 80) {
439. } else if (bankId == 2
440. bankId == 67) {
441. } else if (bankId==3
442. bankId==18
443. bankId==19
444. bankId==27) {
445. } else if (bankId==5) {
446. } else if (bankId == 12) {
447. this._b == '55'
448. this._b == '47'
449. this._b == '48'
450. this._b == '19'
451. this._b == '59'
452. this._b == '73'
453. this._b == '12') {
454. this._b == '3'
455. this._b == '43'
456. this._b == '45'
457. this._b == '54'
458. this._b == '57'
459. this._b == '61'
460. this._b == '63'
461. this._b == '67'
462. this._b == '68'
463. this._b == '69'
464. this._b == '72') {
465. this._b == '72'
466. this._b == '36') { //sonnh them 72 Vietbank
467. bankId == '55'
468. bankId == '47'
469. bankId == '48'
470. bankId == '19'
471. bankId == '59'
472. bankId == '73'
473. bankId == '5'
474. bankId == '12');
475. if (item['id'] == this._b) {
476. if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
477. } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
478. v.length == 15) || (v.length == 16
479. v.length == 19))
480. this._util.checkMod10(v) == true) {
481. cardNo.length == 15)
482. cardNo.length == 16)
483. cardNo.startsWith('81')) && (cardNo.length == 16
484. cardNo.length == 19))
485. this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
486. this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
487. v.length == 5) {
488. v.length == 4
489. v.length == 3)
490. _val.value.length == 4
491. _val.value.length == 3)
492. this.requireAvs = this.AVS_COUNTRIES.find(e => e.code == this._i_country_code)? true : false;
493. this._i_csc.length == 4) ||
494. this._i_csc.length == 3)
495. country = this.AVS_COUNTRIES.find(e => e.code == res.bin_country);
496. countryCode == 'US' ? US_STATES
497. : countryCode == 'CA' ? CA_STATES
498. !token) || (type == 1
499. type == 2)"
500. !token) || (type == 3
501. !token) || (type == 5
502. sortMethodArray[i].trim()=='International'"
503. sortMethodArray[i].trim()=='Domestic'"
504. sortMethodArray[i].trim()=='QR'"
505. sortMethodArray[i].trim()=='Paypal'"
506. sortMethodArray[i].trim()=='Bnpl'
507. if (el == 1) {
508. } else if (el == 2) {
509. } else if (el == 4) {
510. } else if (el == 3) {
511. if (!isNaN(_re.status) && (_re.status == '200'
512. _re.status == '201') && _re.body != null) {
513. if (('closed' == this._res_polling.state
514. 'canceled' == this._res_polling.state
515. 'expired' == this._res_polling.state)
516. } else if ('paid' == this._res_polling.state) {
517. this._res_polling.payments == null
518. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
519. this._paymentService.getCurrentPage() == 'enter_card'
520. } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
521. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
522. } else if ('not_paid' == this._res_polling.state) {
523. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
524. if (!((this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '8'
525. this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '9'
526. this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '12'
527. this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '13'
528. this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '24'
529. this._res_polling.body.payments[this._res_polling.body.payments.length - 1].reason.code == '25'))) {
530. if (auth == 'auth') {
531. detail.merchant.id == 'AMWAY') {
532. if (this.themeConfig.default_method == 'International'
533. } else if (this.themeConfig.default_method == 'Domestic'
534. } else if (this.themeConfig.default_method == 'QR'
535. } else if (this.themeConfig.default_method == 'Paypal'
536. if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
537. this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
538. if (this._res.payments[this._res.payments.length - 1].state == 'approved'
539. } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
540. if (response_code == '8'
541. response_code == '9'
542. response_code == '12'
543. response_code == '13'
544. response_code == '24'
545. response_code == '25') {
546. } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
547. this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
548. if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
549. } else if (idBrand == 'atm'
550. else if (idBrand == 'kbank'
551. this._res.payments[this._res.payments.length - 1].state == 'pending'
552. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
553. if ('paid' == this._res.state) {
554. if (('closed' == this._res.state
555. 'canceled' == this._res.state
556. 'expired' == this._res.state
557. 'paid' == this._res.state)
558. if ('paid' == this._res.state
559. 'canceled' == this._res.state) {
560. this._res.payments == null) {
561. this._res.payments[this._res.payments.length - 1].state == 'pending') {
562. if (this._res.currencies[0] == 'USD') {
563. if (res.status == '200'
564. res.status == '201') {
565. if (data._locale == 'en') {
566. this.themeConfig.deeplink_status == 'Off' ? false : true
567. if (d.b.code == s) {
568. if (item.available == true) {
569. if (appcode == 'grabpay'
570. appcode == 'momo') {
571. if (type == 2
572. err.error.code == '04') {
573. !token)  || (type == 2
574. return ((a.id == id
575. a.code == id) && a.type.includes(type));
576. if (isIphone == true) {
577. } else if (isAndroid == true) {
578. amigo_type == 'SP'"
579. amigo_type == 'PL'"
580. this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
581. if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
582. else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';
583. if (e.name == bankSwift) { // TODO: get by swift
584. if (+e.id == bankId) {
585. if (e.swiftCode == bankSwift) {
586. if (this.checkCount == 1) {
587. if (results == null) {
588. if (c.length == 3) {
589. d = d == undefined ? '.' : d
590. t = t == undefined ? '
591. return results == null ? null : results[1]
592. if (((_val.length == 4
593. _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
594. _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
595. iss_date.length == 4
596. iss_date.search('/') == -1)
597. iss_date.length == 5))
598. if (_dataCache == null) {
599. if ( (0 <= r && r <= 6 && (c == 0
600. c == 6) )
601. || (0 <= c && c <= 6 && (r == 0
602. r == 6) )
603. if (i == 0
604. _modules[r][6] = (r % 2 == 0);
605. _modules[6][c] = (c % 2 == 0);
606. if (r == -2
607. r == 2
608. c == -2
609. c == 2
610. || (r == 0
611. c == 0) ) {
612. ( (bits >> i) & 1) == 1);
613. if (col == 6) col -= 1;
614. if (_modules[row][col - c] == null) {
615. dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
616. if (bitIndex == -1) {
617. margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
618. if (typeof arguments[0] == 'object') {
619. margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
620. if (b == -1) throw 'eof';
621. if (b0 == -1) break;
622. if (typeof b == 'number') {
623. if ( (b & 0xff) == b) {
624. return function(i, j) { return (i + j) % 2 == 0
625. return function(i, j) { return i % 2 == 0
626. return function(i, j) { return j % 3 == 0
627. return function(i, j) { return (i + j) % 3 == 0
628. return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
629. return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
630. return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
631. return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
632. if (r == 0
633. c == 0) {
634. if (dark == qrcode.isDark(row + r, col + c) ) {
635. if (count == 0
636. count == 4) {
637. if (typeof num.length == 'undefined') {
638. num[offset] == 0) {
639. if (typeof rsBlock == 'undefined') {
640. return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
641. _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
642. if (data.length - i == 1) {
643. } else if (data.length - i == 2) {
644. } else if (n == 62) {
645. } else if (n == 63) {
646. if (_buflen == 0) {
647. if (c == '=') {
648. } else if (c == 0x2b) {
649. } else if (c == 0x2f) {
650. if (table.size() == (1 << bitLength) ) {

!== (37 điều kiện duy nhất):
------------------------------------------------------------
1. this.lastValue !== $event.target.value)) {
2. if (YY % 400 === 0 || (YY % 100 !== 0
3. if (YYYY % 400 === 0 || (YYYY % 100 !== 0
4. bnpl.code !== 'kbank'"
5. this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
6. codeResponse.toString() !== '0') {
7. event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
8. key !== '3') {
9. cardNo.length !== 0) {
10. if (this.cardTypeBank !== 'bank_card_number') {
11. if (this.cardTypeBank !== 'bank_account_number') {
12. if (this.cardTypeBank !== 'bank_username') {
13. if (this.cardTypeBank !== 'bank_customer_code') {
14. this.lb_card_account !== this._translate.instant('ocb_account')) {
15. this.lb_card_account !== this._translate.instant('ocb_phone')) {
16. this.lb_card_account !== this._translate.instant('ocb_card')) {
17. this._b !== 18) || (this.cardTypeOcean === 'ATM'
18. let _b = this._b !== 67 ? 67 : this._b
19. if (this.cardTypeBank !== 'internet_banking') {
20. this._b !== 18)) {
21. this._b !== 18) || (this._b == 18)) {
22. this.bankList = this.bankList.filter( item => item.b.id !== '67');
23. if (this.tracking.hasOwnProperty(key) && ((key !== '8'
24. !this._showNameOnCard) || (key !== '9'
25. event.inputType !== 'deleteContentBackward') || v.length == 5) {
26. this._i_country_code !== 'US') {
27. itemRemoved !== '') {
28. if (deviceValue !== 'default') {
29. this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
30. this._i_country_code !== 'default'
31. this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
32. this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {
33. if (_val !== 3) {
34. this._util.getElementParams(this.currentUrl, 't_id') !== '') {
35. queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
36. if (queryString !== '') {
37. if (target !== 0

!= (193 điều kiện duy nhất):
------------------------------------------------------------
1. if (params['locale'] != null
2. } else if (params['locale'] != null
3. if (userLang != null
4. if(value!=null){
5. errorCode != '253'
6. errorCode != 'overtime'"
7. } else if (this.res.payments[this.res.payments.length - 1].instrument.issuer.brand != null
8. this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id != null
9. if (_re.body != null
10. _re.body.links != null
11. _re.body.links.merchant_return != null
12. _re.body.links.merchant_return.href != null) {
13. if (this.url_new_invoice != null
14. if (this._idInvoice != null
15. this._idInvoice != 0) {
16. if (this._paymentService.getInvoiceDetail() != null) {
17. dataPassed.body != null) {
18. dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
19. if (this._translate.currentLang != language) {
20. bnplDetail.method != 'SP'"
21. bnplDetail.method != 'PL'"
22. 'total_paid': this.data?.amount ? (item.diff_amount !=0 ? this._util.formatMoney(parseFloat(item.diff_amount) + parseFloat(this.data?.amount), 0, '.', ',') : this._util.formatMoney(parseFloat(this.data?.amount), 0, '.', ',')) :0,
23. if (this._res_post.return_url != null) {
24. } else if (this._res_post.links != null
25. this._res_post.links.merchant_return != null
26. this._res_post.links.merchant_return.href != null) {
27. _auth != 1"
28. && ((homecreidtDetail['phoneNumber'].length != 10) || !this.homecreidtDetail['phoneNumber'].startsWith('0'))
29. this.value = (this.listTokenBnpl.length != 0) ? this.listTokenBnpl[0]['id'] : "add";
30. this.listTokenBnpl = this.listTokenBnpl.filter(item => item.id != result.id);
31. if (this._res_post.authorization != null
32. this._res_post.authorization.links != null
33. if (this._res_post.links != null
34. this._res_post.links.cancel != null) {
35. this._res_post.authorization.links.approval != null
36. this._res_post.authorization.links.approval.href != null) {
37. userName = paramUserName != null ? paramUserName : ''
38. plan.type!='1'"
39. {{plan.type!='1'
40. plan.interest_rate!='0%'? ('/' && ('month' | translate)) : ''}}
41. plan.processing_fee_rate!='0%'? ('/' && ('month' | translate)) : ''}}
42. plan.applicable!='premium_users'"
43. document.activeElement.id!='fullname'"
44. document.activeElement.id!='phoneNumber'"
45. document.activeElement.id!='email'"
46. this.bnplDetail.phoneNumber?.length != 10 ?
47. if (this._res.links != null
48. this._res.links.merchant_return != null
49. this._res.links.merchant_return.href != null) {
50. if (this._res_post != null
51. this._res_post.links != null
52. if (!(_formCard.otp != null
53. if (!(_formCard.password != null
54. if (ua.indexOf('safari') != -1
55. bnpl.code != 'kbank'"
56. } else if (this._b != 18) {
57. if (this.htmlDesc != null
58. if (_val.value != '') {
59. this.auth_method != null) {
60. if (this.valueDate.length != 3) {
61. if (_formCard.exp_date != null
62. if (this.cardName != null
63. this._res_post.authorization.links.approval != null) {
64. this._b != 27
65. this._b != 12
66. this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
67. this._b != 18)
68. if (this._b != 18
69. this._b != 19) {
70. if (this._inExpDate.length != 3) {
71. let userName = _formCard.name != null ? _formCard.name : ''
72. this._b != 3))
73. if (this._b != 68
74. this._b != 2
75. this._b != 20
76. this._b != 33
77. this._b != 39
78. this._b != 43
79. this._b != 45
80. this._b != 64
81. this._b != 67
82. this._b != 68
83. this._b != 72)
84. this._b != 72 )
85. if (this._b != 9
86. this._b != 16
87. this._b != 17
88. this._b != 25
89. this._b != 44
90. this._b != 54
91. this._b != 57
92. this._b != 59
93. this._b != 61
94. this._b != 63
95. this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
96. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73'].indexOf(this._b.toString()) != -1) {// duongpxt 19255: Them vietbank napas id 72
97. this._b != 11
98. this._b != 72
99. this._b != 73
100. this._b != 36)
101. if (params['locale'] != null) {
102. if ('otp' != this._paymentService.getCurrentPage()) {
103. this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
104. if (!(strInstrument != null
105. if (strInstrument.substring(0, 1) != '^'
106. strInstrument.substr(strInstrument.length - 1) != '$') {
107. if (bankid != null) {
108. _showNameOnCard!=true"
109. cardNo != null
110. v != null
111. this.c_csc = (!(_val.value != null
112. this._i_csc != null
113. this.requireAvs = this.isAvsCountry = country != undefined
114. this._paymentService.getState() != 'error') {
115. if (this._paymentService.getCurrentPage() != 'otp') {
116. _re.body != null) {
117. this._res_polling.links != null
118. this._res_polling.links.merchant_return != null //
119. this._util.getElementParams(url_redirect, 'vpc_TxnResponseCode') != '99') {
120. } else if (this._res_polling.merchant != null
121. this._res_polling.merchant_invoice_reference != null
122. } else if (this._res_polling.payments != null
123. this._res_polling.payments[this._res_polling.payments.length - 1].instrument.brand != 'amigo') {
124. this._res_polling.links.merchant_return != null//
125. this._res_polling.payments != null
126. if (this._res_polling.payments != null
127. t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
128. this.type.toString().length != 0) {
129. if (this._idInvoice != null) {
130. const tokenListsFilter = tokenLists.filter(item => item.id != tokenData);
131. if (count != 1) {
132. if (this._res.merchant != null
133. this._res.merchant_invoice_reference != null) {
134. if (this._res.merchant.address_details != null) {
135. this._res.links != null//
136. } else if (this._res.payments != null
137. this._res.payments[this._res.payments.length - 1].instrument != null
138. this._res.payments[this._res.payments.length - 1].instrument.issuer != null
139. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
140. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
141. this._res.payments[this._res.payments.length - 1].links != null
142. this._res.payments[this._res.payments.length - 1].links.cancel != null
143. this._res.payments[this._res.payments.length - 1].links.cancel.href != null
144. this._res.payments[this._res.payments.length - 1].links.update != null
145. this._res.payments[this._res.payments.length - 1].links.update.href != null) {
146. this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
147. this._res.payments[this._res.payments.length - 1].authorization != null
148. this._res.payments[this._res.payments.length - 1].authorization.links != null) {
149. this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
150. this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
151. if (this._res.payments[this._res.payments.length - 1].authorization != null
152. this._res.payments[this._res.payments.length - 1].authorization.links != null
153. auth = paramUserName != null ? paramUserName : ''
154. if (this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
155. this._res.links != null
156. this._res.links.merchant_return != null //
157. } else if (this._res.merchant != null
158. this._res.merchant_invoice_reference != null
159. this._res.payments[this._res.payments.length - 1].instrument.brand != 'amigo') {
160. } else if (this._res.payments != null) {
161. if (res.body != null
162. res.body.links != null
163. res.body.links.merchant_return != null
164. res.body.links.merchant_return.href != null) {
165. 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {
166. if (appcode != null) {
167. if (idInvoice != null
168. idInvoice != 0)
169. idInvoice != 0) {
170. let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
171. if (this._merchantid != null
172. this._tranref != null
173. this._state != null
174. urlCancel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
175. if (this.res != null
176. this.res.links != null
177. this.res.links.merchant_return != null
178. this.res.links.merchant_return.href != null) {
179. if (tem != null) {
180. if ((tem = ua.match(/version\/(\d+)/i)) != null) {
181. if (v.length != 3) {
182. if (_modules[r][6] != null) {
183. if (_modules[6][c] != null) {
184. if (_modules[row][col] != null) {
185. while (buffer.getLengthInBits() % 8 != 0) {
186. if (count != numChars) {
187. throw count + ' != ' + numChars
188. while (data != 0) {
189. if (test.length != 2
190. ( (test[0] << 8) | test[1]) != code) {
191. if (_length % 3 != 0) {
192. if ( (data >>> length) != 0) {
193. return typeof _map[key] != 'undefined'

