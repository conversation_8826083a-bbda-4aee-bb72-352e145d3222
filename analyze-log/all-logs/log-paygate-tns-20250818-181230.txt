====================================================================================================
TRÍCH XUẤT ĐIỀU KIỆN SO SÁNH TỪ CÁC FILE TYPESCRIPT/JAVASCRIPT/HTML
====================================================================================================
Thư mục/File nguồn: /root/projects/onepay/paygate-tns/src
Thời gian: 18:12:30 18/8/2025
Tổng số file xử lý: 120
Tổng số file bị bỏ qua: 0
Tổng số điều kiện tìm thấy: 1258

THỐNG KÊ TỔNG QUAN THEO LOẠI TOÁN TỬ:
--------------------------------------------------
Strict equality (===): 244 lần
Loose equality (==): 706 lần
Strict inequality (!==): 51 lần
Loose inequality (!=): 257 lần
--------------------------------------------------

DANH SÁCH FILE ĐÃ XỬ LÝ:
--------------------------------------------------
1. 4en.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/4en.html
2. 4vn.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/4vn.html
3. app-routing.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/app-routing.module.ts
4. app.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/app.component.html
5. app.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/app.component.spec.ts
6. app.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/app.component.ts
7. app.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/app.module.ts
8. counter.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/counter.directive.spec.ts
9. counter.directive.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/counter.directive.ts
10. format-carno-input.derective.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/directives/format-carno-input.derective.ts
11. uppercase-input.directive.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/directives/uppercase-input.directive.ts
12. error.component.html (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/error/error.component.html
13. error.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/error/error.component.spec.ts
14. error.component.ts (24 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/error/error.component.ts
15. support-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/error/support-dialog/support-dialog.html
16. support-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/error/support-dialog/support-dialog.ts
17. format-date.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/format-date.directive.spec.ts
18. format-date.directive.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/format-date.directive.ts
19. main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/main.component.html
20. main.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/main.component.spec.ts
21. main.component.ts (16 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/main.component.ts
22. app-result.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/app-result/app-result.component.html
23. app-result.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/app-result/app-result.component.ts
24. bank-amount-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
25. bank-amount-dialog.component.ts (34 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
26. cancel-dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/cancel-dialog-guide-dialog.html
27. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/confirm-dialog/dialog-guide-dialog.html
28. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/dialog-guide-dialog.html
29. bankaccount.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
30. bankaccount.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
31. bankaccount.component.ts (153 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
32. bank.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/model/bank.ts
33. otp-auth.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
34. otp-auth.component.ts (18 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
35. shb.component.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/shb/shb.component.html
36. shb.component.ts (67 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/shb/shb.component.ts
37. techcombank.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
38. techcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
39. techcombank.component.ts (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
40. vibbank.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.html
41. vibbank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.spec.ts
42. vibbank.component.ts (99 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.ts
43. vietcombank.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
44. vietcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
45. vietcombank.component.ts (91 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
46. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
47. off-bank-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
48. off-bank-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
49. domescard-main.component.html (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/domescard-main.component.html
50. domescard-main.component.ts (125 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/domescard-main.component.ts
51. dialog-guide-dialog.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/intercard-main/dialog-guide-dialog.html
52. intercard-main.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/intercard-main/intercard-main.component.html
53. intercard-main.component.ts (66 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/intercard-main/intercard-main.component.ts
54. menu.component.html (27 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/menu.component.html
55. menu.component.ts (155 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/menu.component.ts
56. policy-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/policy-dialog/policy-dialog.component.html
57. policy-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/policy-dialog/policy-dialog.component.ts
58. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
59. qr-dialog.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
60. qr-dialog.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
61. qr-main.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main/qr-main.component.html
62. qr-main.component.ts (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main/qr-main.component.ts
63. safe-html.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main/safe-html.pipe.ts
64. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/dialog/dialog-guide-dialog.html
65. qr-desktop.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.html
66. qr-desktop.component.ts (19 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.ts
67. qr-dialog.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.html
68. qr-dialog.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.ts
69. qr-guide-dialog.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.html
70. qr-guide-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.ts
71. list-bank-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.html
72. list-bank-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.ts
73. qr-main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-main.component.html
74. qr-main.component.ts (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-main.component.ts
75. qr-mobile.component.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.html
76. qr-mobile.component.ts (30 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.ts
77. safe-html.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/safe-html.pipe.ts
78. queuing.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/queuing/queuing.component.html
79. queuing.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/queuing/queuing.component.ts
80. token-expired-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/token-expired-dialog/token-expired-dialog.component.html
81. token-expired-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/token-expired-dialog/token-expired-dialog.component.ts
82. remove-token-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/token-main/remove-token-dialog/remove-token-dialog.html
83. dialog-guide-dialog.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/token-main/token-dialog/dialog-guide-dialog.html
84. token-main.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/token-main/token-main.component.html
85. token-main.component.ts (65 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/token-main/token-main.component.ts
86. overlay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/overlay/overlay.component.html
87. overlay.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/overlay/overlay.component.spec.ts
88. overlay.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/overlay/overlay.component.ts
89. bank-amount.pipe.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/pipe/bank-amount.pipe.ts
90. close-dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/services/close-dialog.service.ts
91. data.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/services/data.service.ts
92. deep_link.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/services/deep_link.service.ts
93. dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/services/dialog.service.ts
94. fee.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/services/fee.service.ts
95. multiple_method.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/services/multiple_method.service.ts
96. payment.service.ts (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/services/payment.service.ts
97. qr.service.ts (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/services/qr.service.ts
98. time-stop.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/services/time-stop.service.ts
99. token-main.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/services/token-main.service.ts
100. index.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/translate/index.ts
101. lang-en.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/translate/lang-en.ts
102. lang-vi.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/translate/lang-vi.ts
103. translate.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/translate/translate.pipe.ts
104. translate.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/translate/translate.service.ts
105. translations.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/translate/translations.ts
106. apps-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/util/apps-info.ts
107. apps-information.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/util/apps-information.ts
108. banks-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/util/banks-info.ts
109. error-handler.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/util/error-handler.ts
110. util.ts (40 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/app/util/util.ts
111. qrcode.js (67 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/assets/script/qrcode.js
112. environment.dev.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/environments/environment.dev.ts
113. environment.mtf.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/environments/environment.mtf.ts
114. environment.prod.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/environments/environment.prod.ts
115. environment.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/environments/environment.ts
116. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/index.html
117. karma.conf.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/karma.conf.js
118. main.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/main.ts
119. polyfills.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/polyfills.ts
120. test.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-tns/src/test.ts

====================================================================================================
CHI TIẾT TỪNG FILE:
====================================================================================================

📁 FILE 1: 4en.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/4en.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 2: 4vn.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/4vn.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 3: app-routing.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/app-routing.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 4: app.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/app.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 5: app.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/app.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 6: app.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/app.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 76] return lang === this._translate.currentLang

== (2 điều kiện):
  1. [Dòng 55] 'vi' == params['locale']) {
  2. [Dòng 57] 'en' == params['locale']) {

!= (3 điều kiện):
  1. [Dòng 55] if (params['locale'] != null
  2. [Dòng 57] } else if (params['locale'] != null
  3. [Dòng 64] if (userLang != null

================================================================================

📁 FILE 7: app.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/app.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 8: counter.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/counter.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 9: counter.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/counter.directive.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 10: format-carno-input.derective.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/directives/format-carno-input.derective.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 44] this.lastValue !== $event.target.value)) {

!= (1 điều kiện):
  1. [Dòng 23] if(value!=null){

================================================================================

📁 FILE 11: uppercase-input.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/directives/uppercase-input.directive.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 0 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 23] this.lastValue !== $event.target.value)) {

================================================================================

📁 FILE 12: error.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/error/error.component.html
📊 Thống kê: 11 điều kiện duy nhất
   - === : 3 lần
   - == : 8 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 77] isPopupSupport === 'True') || (rePayment
  2. [Dòng 78] isPopupSupport === 'True'"
  3. [Dòng 87] isPopupSupport === 'True')">

== (8 điều kiện):
  1. [Dòng 3] errorCode == '11'"
  2. [Dòng 77] *ngIf="((isSent == false
  3. [Dòng 78] isSent == false
  4. [Dòng 87] [class.select_only]="!(isSent == false
  5. [Dòng 104] <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
  6. [Dòng 104] (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
  7. [Dòng 106] <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
  8. [Dòng 106] !(errorCode == 'overtime' || errorCode == '253') && isBack && !invoiceNearExpire && !paymentPending

================================================================================

📁 FILE 13: error.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/error/error.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 14: error.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/error/error.component.ts
📊 Thống kê: 24 điều kiện duy nhất
   - === : 8 lần
   - == : 12 lần
   - !== : 0 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 138] params.timeout === 'true') {
  2. [Dòng 158] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  3. [Dòng 158] _re.body.state === 'unpaid');
  4. [Dòng 239] if (this.errorCode === 'overtime'
  5. [Dòng 239] this.errorCode === '253') {
  6. [Dòng 320] params.name === 'CUSTOMER_INTIME'
  7. [Dòng 320] params.code === '09') {
  8. [Dòng 366] if (this.timeLeft === 0) {

== (12 điều kiện):
  1. [Dòng 213] this.res.themes.theme == 'tns') {
  2. [Dòng 219] params.response_code == 'overtime') {
  3. [Dòng 262] if (_re.status == '200'
  4. [Dòng 262] _re.status == '201') {
  5. [Dòng 275] if (_re2.status == '200'
  6. [Dòng 275] _re2.status == '201') {
  7. [Dòng 288] if (this.errorCode == 'overtime'
  8. [Dòng 288] this.errorCode == '253') {
  9. [Dòng 291] } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
  10. [Dòng 296] this.res.state == 'canceled') {
  11. [Dòng 314] if (lastPayment?.state == 'pending') {
  12. [Dòng 364] if (this.isTimePause == false) {

!= (4 điều kiện):
  1. [Dòng 427] if (this.res != null
  2. [Dòng 427] this.res.links != null
  3. [Dòng 427] this.res.links.merchant_return != null
  4. [Dòng 428] this.res.links.merchant_return.href != null) {

================================================================================

📁 FILE 15: support-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/error/support-dialog/support-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 16: support-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/error/support-dialog/support-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 17: format-date.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/format-date.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 18: format-date.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/format-date.directive.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0
  2. [Dòng 123] YY % 4 === 0)) {
  3. [Dòng 138] if (YYYY % 400 === 0
  4. [Dòng 138] YYYY % 4 === 0)) {

!== (2 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0 || (YY % 100 !== 0
  2. [Dòng 138] if (YYYY % 400 === 0 || (YYYY % 100 !== 0

================================================================================

📁 FILE 19: main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 20: main.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/main.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 21: main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/main.component.ts
📊 Thống kê: 16 điều kiện duy nhất
   - === : 0 lần
   - == : 10 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

== (10 điều kiện):
  1. [Dòng 95] if ((dataPassed.status == '200'
  2. [Dòng 95] dataPassed.status == '201') && dataPassed.body != null) {
  3. [Dòng 99] dataPassed.body.themes.logo_full == 'True') {
  4. [Dòng 124] if (this.type == 5
  5. [Dòng 127] } else if (this.type == 6
  6. [Dòng 130] } else if (this.type == 2
  7. [Dòng 133] } else if (this.type == 7
  8. [Dòng 136] } else if (this.type == 8
  9. [Dòng 139] } else if (this.type == 4
  10. [Dòng 142] } else if (this.type == 3

!= (6 điều kiện):
  1. [Dòng 86] if (this._idInvoice != null
  2. [Dòng 86] this._idInvoice != 0) {
  3. [Dòng 87] if (this._paymentService.getInvoiceDetail() != null) {
  4. [Dòng 95] dataPassed.body != null) {
  5. [Dòng 155] dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
  6. [Dòng 216] if (this._translate.currentLang != language) {

================================================================================

📁 FILE 22: app-result.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/app-result/app-result.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 23: app-result.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/app-result/app-result.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 24: bank-amount-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 25: bank-amount-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
📊 Thống kê: 34 điều kiện duy nhất
   - === : 0 lần
   - == : 34 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (34 điều kiện):
  1. [Dòng 41] if (this.locale == 'en') {
  2. [Dòng 55] if (name == 'MAFC')
  3. [Dòng 61] if (bankId == 3
  4. [Dòng 61] bankId == 61
  5. [Dòng 62] bankId == 8
  6. [Dòng 62] bankId == 49
  7. [Dòng 63] bankId == 48
  8. [Dòng 64] bankId == 10
  9. [Dòng 64] bankId == 53
  10. [Dòng 65] bankId == 17
  11. [Dòng 65] bankId == 65
  12. [Dòng 66] bankId == 23
  13. [Dòng 66] bankId == 52
  14. [Dòng 67] bankId == 27
  15. [Dòng 67] bankId == 66
  16. [Dòng 68] bankId == 9
  17. [Dòng 68] bankId == 54
  18. [Dòng 69] bankId == 37
  19. [Dòng 70] bankId == 38
  20. [Dòng 71] bankId == 39
  21. [Dòng 72] bankId == 40
  22. [Dòng 73] bankId == 42
  23. [Dòng 74] bankId == 44
  24. [Dòng 75] bankId == 72
  25. [Dòng 76] bankId == 59
  26. [Dòng 79] bankId == 51
  27. [Dòng 80] bankId == 64
  28. [Dòng 81] bankId == 58
  29. [Dòng 82] bankId == 56
  30. [Dòng 85] bankId == 55
  31. [Dòng 86] bankId == 60
  32. [Dòng 87] bankId == 68
  33. [Dòng 88] bankId == 74
  34. [Dòng 89] bankId == 75 //LEB HANA Napas

================================================================================

📁 FILE 26: cancel-dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/cancel-dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 27: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/confirm-dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 28: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 29: bankaccount.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 3 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 40] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 55] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 55] valueDate.trim().length === 0)"

== (1 điều kiện):
  1. [Dòng 91] token_site == 'onepay'

================================================================================

📁 FILE 30: bankaccount.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 31: bankaccount.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
📊 Thống kê: 153 điều kiện duy nhất
   - === : 48 lần
   - == : 71 lần
   - !== : 13 lần
   - != : 21 lần
--------------------------------------------------------------------------------

=== (48 điều kiện):
  1. [Dòng 114] if (target.tagName === 'A'
  2. [Dòng 142] if (isIE[0] === 'MSIE'
  3. [Dòng 142] +isIE[1] === 10) {
  4. [Dòng 226] if ((_val.value.substr(-1) === ' '
  5. [Dòng 226] _val.value.length === 24) {
  6. [Dòng 236] if (this.cardTypeBank === 'bank_card_number') {
  7. [Dòng 241] } else if (this.cardTypeBank === 'bank_account_number') {
  8. [Dòng 247] } else if (this.cardTypeBank === 'bank_username') {
  9. [Dòng 251] } else if (this.cardTypeBank === 'bank_customer_code') {
  10. [Dòng 257] this.cardTypeBank === 'bank_card_number'
  11. [Dòng 271] if (this.cardTypeOcean === 'IB') {
  12. [Dòng 275] } else if (this.cardTypeOcean === 'MB') {
  13. [Dòng 276] if (_val.value.substr(0, 2) === '84') {
  14. [Dòng 283] } else if (this.cardTypeOcean === 'ATM') {
  15. [Dòng 310] if (this.cardTypeBank === 'bank_account_number') {
  16. [Dòng 329] this.cardTypeBank === 'bank_card_number') {
  17. [Dòng 351] if (this.cardTypeBank === 'bank_card_number'
  18. [Dòng 351] if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  19. [Dòng 672] if (event.keyCode === 8
  20. [Dòng 672] event.key === "Backspace"
  21. [Dòng 712] if (v.length === 2
  22. [Dòng 712] this.flag.length === 3
  23. [Dòng 712] this.flag.charAt(this.flag.length - 1) === '/') {
  24. [Dòng 716] if (v.length === 1) {
  25. [Dòng 718] } else if (v.length === 2) {
  26. [Dòng 721] v.length === 2) {
  27. [Dòng 729] if (len === 2) {
  28. [Dòng 1005] if ((this.cardTypeBank === 'bank_account_number'
  29. [Dòng 1005] this.cardTypeBank === 'bank_username'
  30. [Dòng 1005] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  31. [Dòng 1010] this.cardTypeOcean === 'ATM')
  32. [Dòng 1011] || (this.cardTypeOcean === 'IB'
  33. [Dòng 1070] if (valIn === this._translate.instant('bank_card_number')) {
  34. [Dòng 1095] } else if (valIn === this._translate.instant('bank_account_number')) {
  35. [Dòng 1114] } else if (valIn === this._translate.instant('bank_username')) {
  36. [Dòng 1130] } else if (valIn === this._translate.instant('bank_customer_code')) {
  37. [Dòng 1218] if (_val.value === ''
  38. [Dòng 1218] _val.value === null
  39. [Dòng 1218] _val.value === undefined) {
  40. [Dòng 1227] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  41. [Dòng 1227] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  42. [Dòng 1234] this.cardTypeOcean === 'MB') {
  43. [Dòng 1242] this.cardTypeOcean === 'IB'
  44. [Dòng 1248] if ((this.cardTypeBank === 'bank_card_number'
  45. [Dòng 1280] if (this.cardName === undefined
  46. [Dòng 1280] this.cardName === '') {
  47. [Dòng 1288] if (this.valueDate === undefined
  48. [Dòng 1288] this.valueDate === '') {

== (71 điều kiện):
  1. [Dòng 133] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 156] if (this._b == 18
  3. [Dòng 156] this._b == 19) {
  4. [Dòng 159] if (this._b == 19) {//19BIDV
  5. [Dòng 167] } else if (this._b == 3
  6. [Dòng 167] this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  7. [Dòng 172] if (this._b == 27) {
  8. [Dòng 177] } else if (this._b == 12) {// 12SHB
  9. [Dòng 182] } else if (this._b == 18) { //18Oceanbank-ocb
  10. [Dòng 235] if (this._b == 19
  11. [Dòng 235] this._b == 3
  12. [Dòng 235] this._b == 27
  13. [Dòng 235] this._b == 12) {
  14. [Dòng 270] } else if (this._b == 18) {
  15. [Dòng 301] if (this.checkBin(_val.value) && (this._b == 3
  16. [Dòng 301] this._b == 27)) {
  17. [Dòng 306] if (this._b == 3) {
  18. [Dòng 318] this.cardTypeOcean == 'ATM') {
  19. [Dòng 331] if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  20. [Dòng 351] this._b == 18)) {
  21. [Dòng 427] if (this.checkBin(v) && (this._b == 3
  22. [Dòng 672] event.inputType == 'deleteContentBackward') {
  23. [Dòng 673] if (event.target.name == 'exp_date'
  24. [Dòng 681] event.inputType == 'insertCompositionText') {
  25. [Dòng 696] if (((this.valueDate.length == 4
  26. [Dòng 696] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  27. [Dòng 696] this.valueDate.length == 5)
  28. [Dòng 780] if (temp.length == 0) {
  29. [Dòng 787] return (counter % 10 == 0);
  30. [Dòng 807] } else if (this._b == 19) {
  31. [Dòng 809] } else if (this._b == 27) {
  32. [Dòng 814] if (this._b == 12) {
  33. [Dòng 816] if (this.cardTypeBank == 'bank_customer_code') {
  34. [Dòng 818] } else if (this.cardTypeBank == 'bank_account_number') {
  35. [Dòng 835] _formCard.exp_date.length == 5
  36. [Dòng 835] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
  37. [Dòng 835] this._b == 3)) {//27-pvcombank;3-TPB ;
  38. [Dòng 840] if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
  39. [Dòng 840] this._b == 19
  40. [Dòng 840] this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
  41. [Dòng 843] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  42. [Dòng 846] if (this.cardTypeOcean == 'IB') {
  43. [Dòng 848] } else if (this.cardTypeOcean == 'MB') {
  44. [Dòng 850] } else if (this.cardTypeOcean == 'ATM') {
  45. [Dòng 880] this.token_site == 'onepay'
  46. [Dòng 899] if (_re.status == '200'
  47. [Dòng 899] _re.status == '201') {
  48. [Dòng 904] if (this._res_post.state == 'approved'
  49. [Dòng 904] this._res_post.state == 'failed') {
  50. [Dòng 911] } else if (this._res_post.state == 'authorization_required') {
  51. [Dòng 929] if (this._b == 18) {
  52. [Dòng 934] if (this._b == 27
  53. [Dòng 934] this._b == 18) {
  54. [Dòng 1025] if ((cardNo.length == 16
  55. [Dòng 1025] if ((cardNo.length == 16 || (cardNo.length == 19
  56. [Dòng 1026] && ((this._b == 18
  57. [Dòng 1026] cardNo.length == 19) || this._b != 18)
  58. [Dòng 1039] if (this._b == +e.id) {
  59. [Dòng 1055] if (valIn == 1) {
  60. [Dòng 1057] } else if (valIn == 2) {
  61. [Dòng 1081] this._b == 3) {
  62. [Dòng 1088] if (this._b == 19) {
  63. [Dòng 1151] if (cardType == this._translate.instant('internetbanking')
  64. [Dòng 1159] } else if (cardType == this._translate.instant('mobilebanking')
  65. [Dòng 1167] } else if (cardType == this._translate.instant('atm')
  66. [Dòng 1227] this._b == 18))) {
  67. [Dòng 1234] } else if (this._b == 18
  68. [Dòng 1259] this.c_expdate = !(((this.valueDate.length == 4
  69. [Dòng 1291] this.valueDate.length == 4
  70. [Dòng 1291] this.valueDate.search('/') == -1)
  71. [Dòng 1292] this.valueDate.length == 5))

!== (13 điều kiện):
  1. [Dòng 226] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 885] key !== '3') {
  3. [Dòng 935] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 953] codeResponse.toString() !== '0') {
  5. [Dòng 1005] cardNo.length !== 0) {
  6. [Dòng 1077] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 1098] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 1119] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 1139] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 1151] this.lb_card_account !== this._translate.instant('ocb_account')) {
  11. [Dòng 1159] this.lb_card_account !== this._translate.instant('ocb_phone')) {
  12. [Dòng 1167] this.lb_card_account !== this._translate.instant('ocb_card')) {
  13. [Dòng 1248] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (21 điều kiện):
  1. [Dòng 187] } else if (this._b != 18) {
  2. [Dòng 193] if (this.htmlDesc != null
  3. [Dòng 223] if (ua.indexOf('safari') != -1
  4. [Dòng 233] if (_val.value != '') {
  5. [Dòng 319] this.auth_method != null) {
  6. [Dòng 674] if (this.valueDate.length != 3) {
  7. [Dòng 835] if (_formCard.exp_date != null
  8. [Dòng 840] if (this.cardName != null
  9. [Dòng 907] if (this._res_post.links != null
  10. [Dòng 907] this._res_post.links.merchant_return != null
  11. [Dòng 907] this._res_post.links.merchant_return.href != null) {
  12. [Dòng 915] if (this._res_post.authorization != null
  13. [Dòng 915] this._res_post.authorization.links != null
  14. [Dòng 915] this._res_post.authorization.links.approval != null) {
  15. [Dòng 922] this._res_post.links.cancel != null) {
  16. [Dòng 1025] this._b != 27
  17. [Dòng 1025] this._b != 12
  18. [Dòng 1025] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  19. [Dòng 1026] this._b != 18)
  20. [Dòng 1072] if (this._b != 18
  21. [Dòng 1072] this._b != 19) {

================================================================================

📁 FILE 32: bank.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/model/bank.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 33: otp-auth.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 34: otp-auth.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
📊 Thống kê: 18 điều kiện duy nhất
   - === : 0 lần
   - == : 11 lần
   - !== : 1 lần
   - != : 6 lần
--------------------------------------------------------------------------------

== (11 điều kiện):
  1. [Dòng 95] if (this._b == 8) {//MB Bank
  2. [Dòng 99] if (this._b == 18) {//Oceanbank
  3. [Dòng 135] if (this._b == 8) {
  4. [Dòng 140] if (this._b == 18) {
  5. [Dòng 145] if (this._b == 12) { //SHB
  6. [Dòng 166] if (_re.status == '200'
  7. [Dòng 166] _re.status == '201') {
  8. [Dòng 175] } else if (this._res.state == 'authorization_required') {
  9. [Dòng 205] if (this.challengeCode == '') {
  10. [Dòng 297] if (this._b == 12) {
  11. [Dòng 348] if (this._b == 18) {//8-MB Bank;18-oceanbank

!== (1 điều kiện):
  1. [Dòng 181] codeResponse.toString() !== '0') {

!= (6 điều kiện):
  1. [Dòng 171] if (this._res.links != null
  2. [Dòng 171] this._res.links.merchant_return != null
  3. [Dòng 171] this._res.links.merchant_return.href != null) {
  4. [Dòng 343] if (!(_formCard.otp != null
  5. [Dòng 349] if (!(_formCard.password != null
  6. [Dòng 365] if (ua.indexOf('safari') != -1

================================================================================

📁 FILE 35: shb.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/shb/shb.component.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 3 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 25] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 42] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  3. [Dòng 42] _inExpDate.trim().length === 0)"

== (3 điều kiện):
  1. [Dòng 22] <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_card_number')">
  2. [Dòng 66] <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_account_number')">
  3. [Dòng 93] token_site == 'onepay'

================================================================================

📁 FILE 36: shb.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/shb/shb.component.ts
📊 Thống kê: 67 điều kiện duy nhất
   - === : 18 lần
   - == : 33 lần
   - !== : 5 lần
   - != : 11 lần
--------------------------------------------------------------------------------

=== (18 điều kiện):
  1. [Dòng 96] if (target.tagName === 'A'
  2. [Dòng 125] if (isIE[0] === 'MSIE'
  3. [Dòng 125] +isIE[1] === 10) {
  4. [Dòng 167] if (focusElement === 'card_name') {
  5. [Dòng 169] } else if (focusElement === 'exp_date'
  6. [Dòng 190] focusExpDateElement === 'card_name') {
  7. [Dòng 411] if (this.cardTypeBank === 'bank_account_number'
  8. [Dòng 456] if (valIn === this._translate.instant('bank_card_number')) {
  9. [Dòng 462] } else if (valIn === this._translate.instant('bank_account_number')) {
  10. [Dòng 504] if (_val.value === ''
  11. [Dòng 504] _val.value === null
  12. [Dòng 504] _val.value === undefined) {
  13. [Dòng 515] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  14. [Dòng 515] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  15. [Dòng 522] this.cardTypeOcean === 'MB') {
  16. [Dòng 530] this.cardTypeOcean === 'IB'
  17. [Dòng 536] if ((this.cardTypeBank === 'bank_card_number'
  18. [Dòng 559] if (this.cardTypeOcean === 'IB') {

== (33 điều kiện):
  1. [Dòng 116] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 134] if (this._b == 12) this.isShbGroup = true;
  3. [Dòng 155] return this._b == 9
  4. [Dòng 155] this._b == 11
  5. [Dòng 155] this._b == 16
  6. [Dòng 155] this._b == 17
  7. [Dòng 155] this._b == 25
  8. [Dòng 155] this._b == 44
  9. [Dòng 156] this._b == 57
  10. [Dòng 156] this._b == 59
  11. [Dòng 156] this._b == 61
  12. [Dòng 156] this._b == 63
  13. [Dòng 156] this._b == 69
  14. [Dòng 242] if (this._b == 12
  15. [Dòng 242] this.cardTypeBank == 'bank_account_number') {
  16. [Dòng 253] _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
  17. [Dòng 285] this.token_site == 'onepay'
  18. [Dòng 302] if (_re.status == '200'
  19. [Dòng 302] _re.status == '201') {
  20. [Dòng 306] if (this._res_post.state == 'approved'
  21. [Dòng 306] this._res_post.state == 'failed') {
  22. [Dòng 312] } else if (this._res_post.state == 'authorization_required') {
  23. [Dòng 414] if ((cardNo.length == 16
  24. [Dòng 414] cardNo.length == 19) && cardNo.startsWith("970443") && this._util.checkMod10(cardNo) && this.checkBin(cardNo)) {
  25. [Dòng 426] if (this._b == +e.id) {
  26. [Dòng 442] if (valIn == 1) {
  27. [Dòng 444] } else if (valIn == 2) {
  28. [Dòng 515] this._b == 18))) {
  29. [Dòng 522] } else if (this._b == 18
  30. [Dòng 536] this._b == 18)) {
  31. [Dòng 548] this.c_expdate = !(((this.valueDate.length == 4
  32. [Dòng 548] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  33. [Dòng 548] this.valueDate.length == 5)

!== (5 điều kiện):
  1. [Dòng 290] key !== '3') {
  2. [Dòng 332] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  3. [Dòng 350] codeResponse.toString() !== '0') {
  4. [Dòng 411] cardNo.length !== 0) {
  5. [Dòng 536] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (11 điều kiện):
  1. [Dòng 142] if (this.htmlDesc != null
  2. [Dòng 203] if (ua.indexOf('safari') != -1
  3. [Dòng 253] if (_formCard.exp_date != null
  4. [Dòng 258] if (this.cardName != null
  5. [Dòng 309] if (this._res_post.links != null
  6. [Dòng 309] this._res_post.links.merchant_return != null
  7. [Dòng 309] this._res_post.links.merchant_return.href != null) {
  8. [Dòng 315] if (this._res_post.authorization != null
  9. [Dòng 315] this._res_post.authorization.links != null
  10. [Dòng 315] this._res_post.authorization.links.approval != null) {
  11. [Dòng 322] this._res_post.links.cancel != null) {

================================================================================

📁 FILE 37: techcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 38: techcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 39: techcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
📊 Thống kê: 14 điều kiện duy nhất
   - === : 1 lần
   - == : 10 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 69] if (this.timeLeft === 0) {

== (10 điều kiện):
  1. [Dòng 60] if (this._b == 2
  2. [Dòng 60] this._b == 31) {
  3. [Dòng 99] if (this._b == 2) {
  4. [Dòng 101] } else if (this._b == 6) {
  5. [Dòng 103] } else if (this._b == 31) {
  6. [Dòng 133] if (_re.status == '200'
  7. [Dòng 133] _re.status == '201') {
  8. [Dòng 138] if (this._res_post.state == 'approved'
  9. [Dòng 138] this._res_post.state == 'failed') {
  10. [Dòng 142] } else if (this._res_post.state == 'authorization_required') {

!= (3 điều kiện):
  1. [Dòng 146] if (this._res_post.authorization != null
  2. [Dòng 146] this._res_post.authorization.links != null
  3. [Dòng 146] this._res_post.authorization.links.approval != null) {

================================================================================

📁 FILE 40: vibbank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 3 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 28] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 42] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 42] valueDate.trim().length === 0)"

== (1 điều kiện):
  1. [Dòng 68] token_site == 'onepay'

================================================================================

📁 FILE 41: vibbank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 42: vibbank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.ts
📊 Thống kê: 99 điều kiện duy nhất
   - === : 37 lần
   - == : 35 lần
   - !== : 10 lần
   - != : 17 lần
--------------------------------------------------------------------------------

=== (37 điều kiện):
  1. [Dòng 116] if (target.tagName === 'A'
  2. [Dòng 145] if (isIE[0] === 'MSIE'
  3. [Dòng 145] +isIE[1] === 10) {
  4. [Dòng 176] if (this.timeLeft === 0) {
  5. [Dòng 220] if ((_val.value.substr(-1) === ' '
  6. [Dòng 220] _val.value.length === 24) {
  7. [Dòng 230] if (this.cardTypeBank === 'bank_card_number') {
  8. [Dòng 235] } else if (this.cardTypeBank === 'bank_account_number') {
  9. [Dòng 241] } else if (this.cardTypeBank === 'bank_username') {
  10. [Dòng 245] } else if (this.cardTypeBank === 'bank_customer_code') {
  11. [Dòng 266] if (this.cardTypeBank === 'bank_account_number') {
  12. [Dòng 277] this.cardTypeBank === 'bank_card_number') {
  13. [Dòng 517] if (event.keyCode === 8
  14. [Dòng 517] event.key === "Backspace"
  15. [Dòng 557] if (v.length === 2
  16. [Dòng 557] this.flag.length === 3
  17. [Dòng 557] this.flag.charAt(this.flag.length - 1) === '/') {
  18. [Dòng 561] if (v.length === 1) {
  19. [Dòng 563] } else if (v.length === 2) {
  20. [Dòng 566] v.length === 2) {
  21. [Dòng 574] if (len === 2) {
  22. [Dòng 822] if ((this.cardTypeBank === 'bank_account_number'
  23. [Dòng 822] this.cardTypeBank === 'bank_username'
  24. [Dòng 822] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  25. [Dòng 872] if (valIn === this._translate.instant('bank_card_number')) {
  26. [Dòng 891] } else if (valIn === this._translate.instant('bank_account_number')) {
  27. [Dòng 903] } else if (valIn === this._translate.instant('bank_username')) {
  28. [Dòng 914] } else if (valIn === this._translate.instant('bank_customer_code')) {
  29. [Dòng 971] if (_val.value === ''
  30. [Dòng 971] _val.value === null
  31. [Dòng 971] _val.value === undefined) {
  32. [Dòng 982] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  33. [Dòng 992] if ((this.cardTypeBank === 'bank_card_number'
  34. [Dòng 1024] if (this.cardName === undefined
  35. [Dòng 1024] this.cardName === '') {
  36. [Dòng 1032] if (this.valueDate === undefined
  37. [Dòng 1032] this.valueDate === '') {

== (35 điều kiện):
  1. [Dòng 136] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 159] if (this._b == 5) {//5-vib;
  3. [Dòng 229] if (this._b == 5) {
  4. [Dòng 263] if (this.checkBin(_val.value) && (this._b == 5)) {
  5. [Dòng 279] if (this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  6. [Dòng 341] if (this.checkBin(v) && (this._b == 5)) {
  7. [Dòng 517] event.inputType == 'deleteContentBackward') {
  8. [Dòng 518] if (event.target.name == 'exp_date'
  9. [Dòng 526] event.inputType == 'insertCompositionText') {
  10. [Dòng 541] if (((this.valueDate.length == 4
  11. [Dòng 541] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  12. [Dòng 541] this.valueDate.length == 5)
  13. [Dòng 625] if (temp.length == 0) {
  14. [Dòng 632] return (counter % 10 == 0);
  15. [Dòng 663] _formCard.exp_date.length == 5
  16. [Dòng 663] this._b == 5) {//5 vib ;
  17. [Dòng 668] this._b == 5) {//5vib;
  18. [Dòng 697] this.token_site == 'onepay'
  19. [Dòng 716] if (_re.status == '200'
  20. [Dòng 716] _re.status == '201') {
  21. [Dòng 721] if (this._res_post.state == 'approved'
  22. [Dòng 721] this._res_post.state == 'failed') {
  23. [Dòng 728] } else if (this._res_post.state == 'authorization_required') {
  24. [Dòng 827] if ((cardNo.length == 16
  25. [Dòng 827] if ((cardNo.length == 16 || (cardNo.length == 19
  26. [Dòng 828] && ((this._b == 18
  27. [Dòng 828] cardNo.length == 19) || this._b != 18)
  28. [Dòng 841] if (this._b == +e.id) {
  29. [Dòng 857] if (valIn == 1) {
  30. [Dòng 859] } else if (valIn == 2) {
  31. [Dòng 982] this._b == 18)) {
  32. [Dòng 1004] this.c_expdate = !(((this.valueDate.length == 4
  33. [Dòng 1035] this.valueDate.length == 4
  34. [Dòng 1035] this.valueDate.search('/') == -1)
  35. [Dòng 1036] this.valueDate.length == 5))

!== (10 điều kiện):
  1. [Dòng 220] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 702] key !== '3') {
  3. [Dòng 750] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 767] codeResponse.toString() !== '0') {
  5. [Dòng 822] cardNo.length !== 0) {
  6. [Dòng 879] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 894] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 908] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 921] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 992] this._b !== 18) || (this._b == 18)) {

!= (17 điều kiện):
  1. [Dòng 186] if (this.htmlDesc != null
  2. [Dòng 217] if (ua.indexOf('safari') != -1
  3. [Dòng 227] if (_val.value != '') {
  4. [Dòng 519] if (this.valueDate.length != 3) {
  5. [Dòng 663] if (_formCard.exp_date != null
  6. [Dòng 668] if (this.cardName != null
  7. [Dòng 724] if (this._res_post.links != null
  8. [Dòng 724] this._res_post.links.merchant_return != null
  9. [Dòng 724] this._res_post.links.merchant_return.href != null) {
  10. [Dòng 732] if (this._res_post.authorization != null
  11. [Dòng 732] this._res_post.authorization.links != null
  12. [Dòng 732] this._res_post.authorization.links.approval != null) {
  13. [Dòng 739] this._res_post.links.cancel != null) {
  14. [Dòng 827] this._b != 27
  15. [Dòng 827] this._b != 12
  16. [Dòng 827] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  17. [Dòng 828] this._b != 18)

================================================================================

📁 FILE 43: vietcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 27] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  2. [Dòng 27] _inExpDate.trim().length === 0)"

== (2 điều kiện):
  1. [Dòng 53] token_site == 'onepay'
  2. [Dòng 63] _b == 68"

================================================================================

📁 FILE 44: vietcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 45: vietcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
📊 Thống kê: 91 điều kiện duy nhất
   - === : 5 lần
   - == : 59 lần
   - !== : 2 lần
   - != : 25 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 87] if (target.tagName === 'A'
  2. [Dòng 225] if (event.keyCode === 8
  3. [Dòng 225] event.key === "Backspace"
  4. [Dòng 468] if (approval.method === 'REDIRECT') {
  5. [Dòng 471] } else if (approval.method === 'POST_REDIRECT') {

== (59 điều kiện):
  1. [Dòng 107] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 130] if (this._b == 1
  3. [Dòng 130] this._b == 20
  4. [Dòng 130] this._b == 36
  5. [Dòng 130] this._b == 64
  6. [Dòng 130] this._b == 55
  7. [Dòng 130] this._b == 47
  8. [Dòng 130] this._b == 48
  9. [Dòng 130] this._b == 59) {
  10. [Dòng 144] return this._b == 9
  11. [Dòng 144] this._b == 16
  12. [Dòng 144] this._b == 17
  13. [Dòng 144] this._b == 25
  14. [Dòng 144] this._b == 44
  15. [Dòng 145] this._b == 57
  16. [Dòng 145] this._b == 59
  17. [Dòng 145] this._b == 61
  18. [Dòng 145] this._b == 63
  19. [Dòng 145] this._b == 69
  20. [Dòng 149] return this._b == 11
  21. [Dòng 149] this._b == 33
  22. [Dòng 149] this._b == 39
  23. [Dòng 149] this._b == 43
  24. [Dòng 149] this._b == 45
  25. [Dòng 149] this._b == 67
  26. [Dòng 149] this._b == 68
  27. [Dòng 149] this._b == 72
  28. [Dòng 149] this._b == 73
  29. [Dòng 149] this._b == 74
  30. [Dòng 149] this._b == 75
  31. [Dòng 225] event.inputType == 'deleteContentBackward') {
  32. [Dòng 226] if (event.target.name == 'exp_date'
  33. [Dòng 234] event.inputType == 'insertCompositionText') {
  34. [Dòng 343] this.token_site == 'onepay'
  35. [Dòng 357] if (this._res_post.state == 'approved'
  36. [Dòng 357] this._res_post.state == 'failed') {
  37. [Dòng 408] } else if (this._res_post.state == 'authorization_required') {
  38. [Dòng 430] this._b == 14
  39. [Dòng 430] this._b == 15
  40. [Dòng 430] this._b == 24
  41. [Dòng 430] this._b == 8
  42. [Dòng 430] this._b == 10
  43. [Dòng 430] this._b == 22
  44. [Dòng 430] this._b == 23
  45. [Dòng 430] this._b == 30
  46. [Dòng 430] this._b == 11
  47. [Dòng 430] this._b == 9) {
  48. [Dòng 508] if ((cardNo.length == 16
  49. [Dòng 509] (cardNo.length == 19
  50. [Dòng 509] (cardNo.length == 19 && (this._b == 1
  51. [Dòng 509] this._b == 4
  52. [Dòng 509] this._b == 59))
  53. [Dòng 511] this._util.checkMod10(cardNo) == true
  54. [Dòng 547] return ((value.length == 4
  55. [Dòng 547] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  56. [Dòng 547] value.length == 5) && parseInt(value.split('/')[0]
  57. [Dòng 581] this._inExpDate.length == 4
  58. [Dòng 581] this._inExpDate.search('/') == -1)
  59. [Dòng 582] this._inExpDate.length == 5))

!== (2 điều kiện):
  1. [Dòng 370] codeResponse.toString() !== '0') {
  2. [Dòng 431] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (25 điều kiện):
  1. [Dòng 135] if (this.htmlDesc != null
  2. [Dòng 162] if (ua.indexOf('safari') != -1
  3. [Dòng 227] if (this._inExpDate.length != 3) {
  4. [Dòng 307] if (this._b != 9
  5. [Dòng 307] this._b != 16
  6. [Dòng 307] this._b != 17
  7. [Dòng 307] this._b != 25
  8. [Dòng 307] this._b != 44
  9. [Dòng 308] this._b != 57
  10. [Dòng 308] this._b != 59
  11. [Dòng 308] this._b != 61
  12. [Dòng 308] this._b != 63
  13. [Dòng 308] this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
  14. [Dòng 321] '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75'].indexOf(this._b.toString()) != -1) {
  15. [Dòng 359] if (this._res_post.return_url != null) {
  16. [Dòng 362] if (this._res_post.links != null
  17. [Dòng 362] this._res_post.links.merchant_return != null
  18. [Dòng 362] this._res_post.links.merchant_return.href != null) {
  19. [Dòng 413] if (this._res_post.authorization != null
  20. [Dòng 413] this._res_post.authorization.links != null
  21. [Dòng 418] this._res_post.links.cancel != null) {
  22. [Dòng 424] let userName = _formCard.name != null ? _formCard.name : ''
  23. [Dòng 425] this._res_post.authorization.links.approval != null
  24. [Dòng 425] this._res_post.authorization.links.approval.href != null) {
  25. [Dòng 428] userName = paramUserName != null ? paramUserName : ''

================================================================================

📁 FILE 46: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 47: off-bank-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 48: off-bank-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 49: domescard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/domescard-main.component.html
📊 Thống kê: 8 điều kiện duy nhất
   - === : 1 lần
   - == : 7 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 15] filteredData.length === 0"

== (7 điều kiện):
  1. [Dòng 23] ((!token && _auth==0 && vietcombankGroupSelected) || (token && _b==16))
  2. [Dòng 23] _b==16))">
  3. [Dòng 27] !token && _auth==0 && techcombankGroupSelected
  4. [Dòng 30] !token && _auth==0 && bankaccountGroupSelected
  5. [Dòng 35] !token && _auth==0 && vibbankGroupSelected
  6. [Dòng 42] !token && _auth==0 && shbGroupSelected
  7. [Dòng 48] (token || _auth==1) && _b != 16

================================================================================

📁 FILE 50: domescard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/domescard-main/domescard-main.component.ts
📊 Thống kê: 125 điều kiện duy nhất
   - === : 21 lần
   - == : 96 lần
   - !== : 1 lần
   - != : 7 lần
--------------------------------------------------------------------------------

=== (21 điều kiện):
  1. [Dòng 257] if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
  2. [Dòng 258] filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
  3. [Dòng 299] if (valOut === 'auth') {
  4. [Dòng 438] if (this._b === '1'
  5. [Dòng 438] this._b === '20'
  6. [Dòng 438] this._b === '64') {
  7. [Dòng 441] if (this._b === '36'
  8. [Dòng 441] this._b === '18'
  9. [Dòng 441] this._b === '19'
  10. [Dòng 444] if (this._b === '19'
  11. [Dòng 444] this._b === '16'
  12. [Dòng 444] this._b === '25'
  13. [Dòng 444] this._b === '33'
  14. [Dòng 445] this._b === '39'
  15. [Dòng 445] this._b === '11'
  16. [Dòng 445] this._b === '17'
  17. [Dòng 446] this._b === '36'
  18. [Dòng 446] this._b === '44'
  19. [Dòng 447] this._b === '64'
  20. [Dòng 450] if (this._b === '20'
  21. [Dòng 453] if (this._b === '18') {

== (96 điều kiện):
  1. [Dòng 171] this._auth == 0
  2. [Dòng 171] this.tokenList.length == 0) {
  3. [Dòng 246] this.filteredData.length == 1
  4. [Dòng 280] $event == 'true') {
  5. [Dòng 369] if (bankId == 1
  6. [Dòng 369] bankId == 4
  7. [Dòng 369] bankId == 7
  8. [Dòng 369] bankId == 8
  9. [Dòng 369] bankId == 9
  10. [Dòng 369] bankId == 10
  11. [Dòng 369] bankId == 11
  12. [Dòng 369] bankId == 14
  13. [Dòng 369] bankId == 15
  14. [Dòng 370] bankId == 16
  15. [Dòng 370] bankId == 17
  16. [Dòng 370] bankId == 20
  17. [Dòng 370] bankId == 22
  18. [Dòng 370] bankId == 23
  19. [Dòng 370] bankId == 24
  20. [Dòng 370] bankId == 25
  21. [Dòng 370] bankId == 30
  22. [Dòng 370] bankId == 33
  23. [Dòng 371] bankId == 34
  24. [Dòng 371] bankId == 35
  25. [Dòng 371] bankId == 36
  26. [Dòng 371] bankId == 37
  27. [Dòng 371] bankId == 38
  28. [Dòng 371] bankId == 39
  29. [Dòng 371] bankId == 40
  30. [Dòng 371] bankId == 41
  31. [Dòng 371] bankId == 42
  32. [Dòng 372] bankId == 43
  33. [Dòng 372] bankId == 44
  34. [Dòng 372] bankId == 45
  35. [Dòng 372] bankId == 46
  36. [Dòng 372] bankId == 47
  37. [Dòng 372] bankId == 48
  38. [Dòng 372] bankId == 49
  39. [Dòng 372] bankId == 50
  40. [Dòng 372] bankId == 51
  41. [Dòng 373] bankId == 52
  42. [Dòng 373] bankId == 53
  43. [Dòng 373] bankId == 54
  44. [Dòng 373] bankId == 55
  45. [Dòng 373] bankId == 56
  46. [Dòng 373] bankId == 57
  47. [Dòng 373] bankId == 58
  48. [Dòng 373] bankId == 59
  49. [Dòng 373] bankId == 60
  50. [Dòng 374] bankId == 61
  51. [Dòng 374] bankId == 62
  52. [Dòng 374] bankId == 63
  53. [Dòng 374] bankId == 64
  54. [Dòng 374] bankId == 65
  55. [Dòng 374] bankId == 66
  56. [Dòng 374] bankId == 67
  57. [Dòng 374] bankId == 68
  58. [Dòng 374] bankId == 69
  59. [Dòng 374] bankId == 70
  60. [Dòng 375] bankId == 71
  61. [Dòng 375] bankId == 72
  62. [Dòng 375] bankId == 73
  63. [Dòng 375] bankId == 32
  64. [Dòng 375] bankId == 74
  65. [Dòng 375] bankId == 75) {
  66. [Dòng 377] } else if (bankId == 6
  67. [Dòng 377] bankId == 31
  68. [Dòng 377] bankId == 80
  69. [Dòng 377] bankId == 2) {
  70. [Dòng 379] } else if (bankId == 3
  71. [Dòng 379] bankId == 18
  72. [Dòng 379] bankId == 19
  73. [Dòng 379] bankId == 27) {
  74. [Dòng 381] } else if (bankId == 5) {
  75. [Dòng 383] } else if (bankId == 12) {
  76. [Dòng 441] this._b == '55'
  77. [Dòng 441] this._b == '47'
  78. [Dòng 441] this._b == '48'
  79. [Dòng 441] this._b == '59'
  80. [Dòng 441] this._b == '73'
  81. [Dòng 441] this._b == '12') {
  82. [Dòng 444] this._b == '3'
  83. [Dòng 445] this._b == '43'
  84. [Dòng 445] this._b == '45'
  85. [Dòng 446] this._b == '57'
  86. [Dòng 447] this._b == '61'
  87. [Dòng 447] this._b == '63'
  88. [Dòng 447] this._b == '67'
  89. [Dòng 447] this._b == '68'
  90. [Dòng 447] this._b == '69'
  91. [Dòng 447] this._b == '72'
  92. [Dòng 447] this._b == '9'
  93. [Dòng 447] this._b == '74'
  94. [Dòng 447] this._b == '75') {
  95. [Dòng 450] this._b == '36'
  96. [Dòng 470] if (item['id'] == this._b) {

!== (1 điều kiện):
  1. [Dòng 119] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (7 điều kiện):
  1. [Dòng 157] if (params['locale'] != null) {
  2. [Dòng 163] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 167] this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
  4. [Dòng 194] if (!(strInstrument != null
  5. [Dòng 197] if (strInstrument.substring(0, 1) != '^'
  6. [Dòng 197] strInstrument.substr(strInstrument.length - 1) != '$') {
  7. [Dòng 348] if (bankid != null) {

================================================================================

📁 FILE 51: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/intercard-main/dialog-guide-dialog.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 30] data['type'] == '5'
  2. [Dòng 30] data['type'] == '7'"
  3. [Dòng 45] data['type'] == '6'
  4. [Dòng 45] data['type'] == '8'"

================================================================================

📁 FILE 52: intercard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/intercard-main/intercard-main.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 157] token_site == 'onepay'

!= (1 điều kiện):
  1. [Dòng 6] _showAVS!=true"

================================================================================

📁 FILE 53: intercard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/intercard-main/intercard-main.component.ts
📊 Thống kê: 66 điều kiện duy nhất
   - === : 8 lần
   - == : 38 lần
   - !== : 8 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 146] if (target.tagName === 'A'
  2. [Dòng 291] if (_formCard.country === 'default') {
  3. [Dòng 574] if (event.keyCode === 8
  4. [Dòng 574] event.key === "Backspace"
  5. [Dòng 649] if ((v.substr(-1) === ' '
  6. [Dòng 891] if (deviceValue === 'CA'
  7. [Dòng 891] deviceValue === 'US') {
  8. [Dòng 912] this.c_country = _val.value === 'default'

== (38 điều kiện):
  1. [Dòng 191] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 209] regexp.test('card;;visa;USD')) && (this._type == 5
  3. [Dòng 209] this._type == 7);
  4. [Dòng 210] regexp.test('card;;mastercard;USD')) && (this._type == 5
  5. [Dòng 211] regexp.test('card;;amex;USD')) && (this._type == 6
  6. [Dòng 211] this._type == 8);
  7. [Dòng 329] this.token_site == 'onepay'
  8. [Dòng 355] if (this._res_post.state == 'approved'
  9. [Dòng 355] this._res_post.state == 'failed') {
  10. [Dòng 381] } else if (this._res_post.state == 'failed') { // lỗi mới kiểm tra sang trang lỗi
  11. [Dòng 411] } else if (this._res_post.state == 'authorization_required') {
  12. [Dòng 412] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  13. [Dòng 424] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  14. [Dòng 483] v.length == 15) || (v.length == 16
  15. [Dòng 483] v.length == 19))
  16. [Dòng 484] this._util.checkMod10(v) == true) {
  17. [Dòng 528] cardNo.length == 15)
  18. [Dòng 530] cardNo.length == 16)
  19. [Dòng 531] cardNo.startsWith('81')) && (cardNo.length == 16
  20. [Dòng 531] cardNo.length == 19))
  21. [Dòng 574] event.inputType == 'deleteContentBackward') {
  22. [Dòng 575] if (event.target.name == 'exp_date'
  23. [Dòng 583] event.inputType == 'insertCompositionText') {
  24. [Dòng 598] if (((this.valueDate.length == 4
  25. [Dòng 598] this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  26. [Dòng 598] this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  27. [Dòng 649] v.length == 5) {
  28. [Dòng 657] v.length == 4
  29. [Dòng 661] v.length == 3)
  30. [Dòng 687] _val.value.length == 4
  31. [Dòng 691] _val.value.length == 3)
  32. [Dòng 869] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  33. [Dòng 869] this.valueDate.length == 5)
  34. [Dòng 959] this.valueDate.length == 4
  35. [Dòng 959] this.valueDate.search('/') == -1)
  36. [Dòng 960] this.valueDate.length == 5))
  37. [Dòng 973] this._i_csc.length == 4) ||
  38. [Dòng 977] this._i_csc.length == 3)

!== (8 điều kiện):
  1. [Dòng 337] key !== '8') {
  2. [Dòng 365] codeResponse.toString() !== '0'){
  3. [Dòng 649] event.inputType !== 'deleteContentBackward') || v.length == 5) {
  4. [Dòng 888] if (deviceValue !== 'default') {
  5. [Dòng 905] this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
  6. [Dòng 984] return this._i_country_code !== 'default'
  7. [Dòng 1017] this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
  8. [Dòng 1024] this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {

!= (12 điều kiện):
  1. [Dòng 193] if (params['locale'] != null) {
  2. [Dòng 199] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 357] if (this._res_post.return_url != null) {
  4. [Dòng 359] } else if (this._res_post.links != null
  5. [Dòng 359] this._res_post.links.merchant_return != null
  6. [Dòng 359] this._res_post.links.merchant_return.href != null) {
  7. [Dòng 465] if (ua.indexOf('safari') != -1
  8. [Dòng 528] cardNo != null
  9. [Dòng 576] if (this.valueDate.length != 3) {
  10. [Dòng 656] v != null
  11. [Dòng 686] this.c_csc = (!(_val.value != null
  12. [Dòng 971] this._i_csc != null

================================================================================

📁 FILE 54: menu.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/menu.component.html
📊 Thống kê: 27 điều kiện duy nhất
   - === : 17 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 7 lần
--------------------------------------------------------------------------------

=== (17 điều kiện):
  1. [Dòng 109] [ngStyle]="{'border-color': type === 5 ? this.themeColor.border_color : border_color}"
  2. [Dòng 133] type === 5"
  3. [Dòng 150] type === 6"
  4. [Dòng 151] [ngStyle]="{'border-color': type === 6 ? this.themeColor.border_color : border_color}"
  5. [Dòng 188] type === 2"
  6. [Dòng 190] [ngStyle]="{'border-color': type === 2 ? this.themeColor.border_color : border_color}"
  7. [Dòng 216] <div *ngIf="(type === 2
  8. [Dòng 216] type === '2'
  9. [Dòng 226] type === 7"
  10. [Dòng 227] [ngStyle]="{'border-color': type === 7 ? this.themeColor.border_color : border_color}"
  11. [Dòng 267] type === 8"
  12. [Dòng 268] [ngStyle]="{'border-color': type === 8 ? this.themeColor.border_color : border_color}"
  13. [Dòng 304] type === 4"
  14. [Dòng 305] [ngStyle]="{'border-color': type === 4 ? this.themeColor.border_color : border_color}"
  15. [Dòng 343] type === 3"
  16. [Dòng 344] [ngStyle]="{'border-color': type === 3 ? this.themeColor.border_color : border_color}"
  17. [Dòng 382] d_vrbank===true"

== (3 điều kiện):
  1. [Dòng 108] type == 5"
  2. [Dòng 194] tokenList.length == 1)">
  3. [Dòng 199] tokenList.length == 1"

!= (7 điều kiện):
  1. [Dòng 135] feeService['visa_mastercard_d']['fee'] != 0"
  2. [Dòng 174] feeService['amex_d']['fee'] != 0"
  3. [Dòng 213] feeService['atm']['fee'] != 0"
  4. [Dòng 251] feeService['visa_mastercard_i']['fee'] != 0"
  5. [Dòng 289] feeService['amex_i']['fee'] != 0"
  6. [Dòng 325] feeService['qr']['fee'] != 0"
  7. [Dòng 362] feeService['pp']['fee'] != 0"

================================================================================

📁 FILE 55: menu.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/menu.component.ts
📊 Thống kê: 155 điều kiện duy nhất
   - === : 9 lần
   - == : 85 lần
   - !== : 3 lần
   - != : 58 lần
--------------------------------------------------------------------------------

=== (9 điều kiện):
  1. [Dòng 812] this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number === 1
  2. [Dòng 867] if (this._res.state === 'unpaid'
  3. [Dòng 867] this._res.state === 'not_paid') {
  4. [Dòng 981] if ('op' === auth
  5. [Dòng 1018] } else if ('bank' === auth
  6. [Dòng 1023] if (approval.method === 'REDIRECT') {
  7. [Dòng 1026] } else if (approval.method === 'POST_REDIRECT') {
  8. [Dòng 1283] return id === 'amex' ? '1234' : '123'
  9. [Dòng 1445] if (this.timeLeftPaypal === 0) {

== (85 điều kiện):
  1. [Dòng 207] if (el == 5) {
  2. [Dòng 209] } else if (el == 6) {
  3. [Dòng 211] } else if (el == 7) {
  4. [Dòng 213] } else if (el == 8) {
  5. [Dòng 215] } else if (el == 2) {
  6. [Dòng 217] } else if (el == 4) {
  7. [Dòng 219] } else if (el == 3) {
  8. [Dòng 256] if (!isNaN(_re.status) && (_re.status == '200'
  9. [Dòng 256] _re.status == '201') && _re.body != null) {
  10. [Dòng 261] if (('closed' == this._res_polling.state
  11. [Dòng 261] 'canceled' == this._res_polling.state
  12. [Dòng 261] 'expired' == this._res_polling.state)
  13. [Dòng 281] } else if ('paid' == this._res_polling.state) {
  14. [Dòng 291] this._res_polling.payments == null) {
  15. [Dòng 300] if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'authorization_required') {
  16. [Dòng 304] } else if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  17. [Dòng 311] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  18. [Dòng 313] this._paymentService.getCurrentPage() == 'enter_card') {
  19. [Dòng 316] } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
  20. [Dòng 316] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
  21. [Dòng 333] } else if ('not_paid' == this._res_polling.state) {
  22. [Dòng 345] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
  23. [Dòng 489] if (message == '1') {
  24. [Dòng 498] if (this.checkInvoiceState() == 1) {
  25. [Dòng 509] this.version2 = _re.body?.merchant?.qr_version == "2"
  26. [Dòng 517] if (_re.status == '200'
  27. [Dòng 517] _re.status == '201') {
  28. [Dòng 542] if (this.type == 5
  29. [Dòng 545] } else if (this.type == 6
  30. [Dòng 548] } else if (this.type == 2
  31. [Dòng 551] } else if (this.type == 7
  32. [Dòng 554] } else if (this.type == 8
  33. [Dòng 557] } else if (this.type == 4
  34. [Dòng 560] } else if (this.type == 3
  35. [Dòng 598] if (this.themeConfig.default_method == 'International'
  36. [Dòng 600] } else if (this.themeConfig.default_method == 'Domestic'
  37. [Dòng 602] } else if (this.themeConfig.default_method == 'QR'
  38. [Dòng 604] } else if (this.themeConfig.default_method == 'Paypal'
  39. [Dòng 667] if (('closed' == this._res.state
  40. [Dòng 667] 'canceled' == this._res.state
  41. [Dòng 667] 'expired' == this._res.state
  42. [Dòng 667] 'paid' == this._res.state)
  43. [Dòng 671] if ('paid' == this._res.state
  44. [Dòng 816] if ((this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number) == 0
  45. [Dòng 840] this._auth == 0) {
  46. [Dòng 868] if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
  47. [Dòng 868] this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
  48. [Dòng 870] if (this._res.payments[this._res.payments.length - 1].state == 'approved'
  49. [Dòng 872] } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
  50. [Dòng 919] if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required') {
  51. [Dòng 925] } else if (this._res.payments[this._res.payments.length - 1].state == 'pending') {
  52. [Dòng 931] } else if (this._res.payments[this._res.payments.length - 1].state == 'canceled') {
  53. [Dòng 935] } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  54. [Dòng 935] this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
  55. [Dòng 969] } else if (idBrand == 'atm'
  56. [Dòng 1046] if ('paid' == this._res.state) {
  57. [Dòng 1047] this._res.merchant.token_site == 'onepay')) {
  58. [Dòng 1098] this._res.payments == null) {
  59. [Dòng 1100] this._res.payments[this._res.payments.length - 1].state == 'pending') {
  60. [Dòng 1110] if (this._res.currencies[0] == 'USD') {
  61. [Dòng 1178] if (item.instrument.issuer.brand.id == 'atm') {
  62. [Dòng 1180] } else if (item.instrument.issuer.brand.id == 'visa'
  63. [Dòng 1180] item.instrument.issuer.brand.id == 'mastercard') {
  64. [Dòng 1181] if (item.instrument.issuer_location == 'd') {
  65. [Dòng 1186] } else if (item.instrument.issuer.brand.id == 'amex') {
  66. [Dòng 1192] } else if (item.instrument.issuer.brand.id == 'jcb') {
  67. [Dòng 1256] return merchant.token_cvv == true
  68. [Dòng 1261] return (merchant.token_name == true)
  69. [Dòng 1267] return (merchant.token_email == true)
  70. [Dòng 1273] return (merchant.token_phone == true)
  71. [Dòng 1347] if (type == 'qrv1') {
  72. [Dòng 1357] if (type == 'mobile') {
  73. [Dòng 1359] e.type == 'ewallet'
  74. [Dòng 1359] e.code == 'momo')) {
  75. [Dòng 1367] } else if (type == 'desktop') {
  76. [Dòng 1368] e.type == 'vnpayqr') || (regex.test(strTest)
  77. [Dòng 1420] this.tokenList.length == 1) {
  78. [Dòng 1421] if (_val == 2
  79. [Dòng 1433] if (this.type == 4) {
  80. [Dòng 1513] if (this._res_post.state == 'approved'
  81. [Dòng 1513] this._res_post.state == 'failed') {
  82. [Dòng 1522] } else if (this._res_post.state == 'authorization_required') {
  83. [Dòng 1523] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  84. [Dòng 1537] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  85. [Dòng 1610] if (data._locale == 'en') {

!== (3 điều kiện):
  1. [Dòng 993] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  2. [Dòng 1399] if (_val !== 3) {
  3. [Dòng 1403] this._util.getElementParams(this.currentUrl, 't_id') !== '') {

!= (58 điều kiện):
  1. [Dòng 249] if (this._idInvoice != null
  2. [Dòng 249] this._paymentService.getState() != 'error') {
  3. [Dòng 255] if (this._paymentService.getCurrentPage() != 'otp') {
  4. [Dòng 256] _re.body != null) {
  5. [Dòng 262] this._res_polling.links != null
  6. [Dòng 262] this._res_polling.links.merchant_return != null //
  7. [Dòng 291] } else if (this._res_polling.merchant != null
  8. [Dòng 291] this._res_polling.merchant_invoice_reference != null
  9. [Dòng 293] } else if (this._res_polling.payments != null
  10. [Dòng 317] this._res_polling.links.merchant_return != null//
  11. [Dòng 336] this._res_polling.payments != null
  12. [Dòng 344] if (this._res_polling.payments != null
  13. [Dòng 349] t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
  14. [Dòng 468] this.type.toString().length != 0) {
  15. [Dòng 474] if (params['locale'] != null) {
  16. [Dòng 482] if ('otp' != this._paymentService.getCurrentPage()) {
  17. [Dòng 495] if (this._paymentService.getInvoiceDetail() != null) {
  18. [Dòng 668] this._res.links != null
  19. [Dòng 668] this._res.links.merchant_return != null
  20. [Dòng 851] if (count != 1) {
  21. [Dòng 857] if (this._res.merchant != null
  22. [Dòng 857] this._res.merchant_invoice_reference != null) {
  23. [Dòng 860] if (this._res.merchant.address_details != null) {
  24. [Dòng 868] this._res.links != null//
  25. [Dòng 914] } else if (this._res.payments != null
  26. [Dòng 936] this._res.payments[this._res.payments.length - 1].instrument != null
  27. [Dòng 936] this._res.payments[this._res.payments.length - 1].instrument.issuer != null
  28. [Dòng 937] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
  29. [Dòng 937] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
  30. [Dòng 938] this._res.payments[this._res.payments.length - 1].links != null
  31. [Dòng 938] this._res.payments[this._res.payments.length - 1].links.cancel != null
  32. [Dòng 938] this._res.payments[this._res.payments.length - 1].links.cancel.href != null
  33. [Dòng 954] this._res.payments[this._res.payments.length - 1].links.update != null
  34. [Dòng 954] this._res.payments[this._res.payments.length - 1].links.update.href != null) {
  35. [Dòng 969] this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
  36. [Dòng 970] this._res.payments[this._res.payments.length - 1].authorization != null
  37. [Dòng 970] this._res.payments[this._res.payments.length - 1].authorization.links != null) {
  38. [Dòng 981] this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
  39. [Dòng 981] this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
  40. [Dòng 984] if (this._res.payments[this._res.payments.length - 1].authorization != null
  41. [Dòng 984] this._res.payments[this._res.payments.length - 1].authorization.links != null
  42. [Dòng 990] auth = paramUserName != null ? paramUserName : ''
  43. [Dòng 1073] this._res.links.merchant_return != null //
  44. [Dòng 1098] } else if (this._res.merchant != null
  45. [Dòng 1098] this._res.merchant_invoice_reference != null
  46. [Dòng 1206] if (['mastercard', 'visa', 'amex', 'jcb', 'cup', 'card', 'np_card', 'atm'].indexOf(id) != -1) {
  47. [Dòng 1208] } else if (['techcombank_account', 'vib_account', 'dongabank_account', 'tpbank_account', 'bidv_account', 'pvcombank_account', 'shb_account', 'oceanbank_online_account'].indexOf(id) != -1) {
  48. [Dòng 1210] } else if (['oceanbank_mobile_account'].indexOf(id) != -1) {
  49. [Dòng 1212] } else if (['shb_customer_id'].indexOf(id) != -1) {
  50. [Dòng 1229] if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1) {
  51. [Dòng 1315] if (!(strInstrument != null
  52. [Dòng 1332] if (this._translate.currentLang != language) {
  53. [Dòng 1359] e.type != 'ewallet') || (regex.test(strTest)
  54. [Dòng 1405] } else if (this._res.payments != null) {
  55. [Dòng 1515] if (this._res_post.return_url != null) {
  56. [Dòng 1517] } else if (this._res_post.links != null
  57. [Dòng 1517] this._res_post.links.merchant_return != null
  58. [Dòng 1517] this._res_post.links.merchant_return.href != null) {

================================================================================

📁 FILE 56: policy-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/policy-dialog/policy-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 57: policy-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/policy-dialog/policy-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 58: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 59: qr-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 2] screen=='qr'"
  2. [Dòng 112] screen=='confirm_close'"

================================================================================

📁 FILE 60: qr-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 46] 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {

================================================================================

📁 FILE 61: qr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main/qr-main.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 6] filteredData.length === 0
  2. [Dòng 6] filteredDataOther.length === 0"
  3. [Dòng 40] filteredDataMobile.length === 0
  4. [Dòng 40] filteredDataOtherMobile.length === 0"

================================================================================

📁 FILE 62: qr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main/qr-main.component.ts
📊 Thống kê: 22 điều kiện duy nhất
   - === : 8 lần
   - == : 8 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 211] this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
  2. [Dòng 212] this.filteredDataOther = this.appList.filter(item => item.type === 'other');
  3. [Dòng 213] this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
  4. [Dòng 214] this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
  5. [Dòng 235] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  6. [Dòng 236] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  7. [Dòng 242] if (item.type === 'mobile_banking') {
  8. [Dòng 523] this.appList.length === 1

== (8 điều kiện):
  1. [Dòng 138] this.themeColor.deeplink_status == 'Off' ? false : true
  2. [Dòng 241] if (item.available == true) {
  3. [Dòng 308] if (_re.status == '200'
  4. [Dòng 308] _re.status == '201') {
  5. [Dòng 310] if (appcode == 'grabpay'
  6. [Dòng 310] appcode == 'momo') {
  7. [Dòng 313] if (type == 2
  8. [Dòng 350] err.error.code == '04') {

!= (6 điều kiện):
  1. [Dòng 156] if (params['locale'] != null) {
  2. [Dòng 162] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 188] if (!(strInstrument != null
  4. [Dòng 272] if (appcode != null) {
  5. [Dòng 499] if (_re.status != '200'
  6. [Dòng 499] _re.status != '201')

================================================================================

📁 FILE 63: safe-html.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main/safe-html.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 64: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 65: qr-desktop.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 1 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 168] listVNPayQR.length === 0"

================================================================================

📁 FILE 66: qr-desktop.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.ts
📊 Thống kê: 19 điều kiện duy nhất
   - === : 4 lần
   - == : 10 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 368] this.listWalletQR.length === 1) {
  2. [Dòng 418] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  3. [Dòng 419] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  4. [Dòng 758] this.listWalletQR.length === 1

== (10 điều kiện):
  1. [Dòng 267] e.type == 'vnpayqr') {
  2. [Dòng 274] e.type == 'ewallet') {
  3. [Dòng 317] if (_re.status == '200'
  4. [Dòng 317] _re.status == '201') {
  5. [Dòng 346] e.type == 'wallet')) {
  6. [Dòng 385] if (d.b.code == s) {
  7. [Dòng 424] if (item.available == true) {
  8. [Dòng 485] if (appcode == 'grabpay'
  9. [Dòng 485] appcode == 'momo') {
  10. [Dòng 518] err.error.code == '04') {

!= (5 điều kiện):
  1. [Dòng 228] if (params['locale'] != null) {
  2. [Dòng 257] if (!(strInstrument != null
  3. [Dòng 443] if (appcode != null) {
  4. [Dòng 734] if (_re.status != '200'
  5. [Dòng 734] _re.status != '201')

================================================================================

📁 FILE 67: qr-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1] screen=='qr'"
  2. [Dòng 122] screen=='confirm_close'"

================================================================================

📁 FILE 68: qr-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 50] 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {

================================================================================

📁 FILE 69: qr-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (3 điều kiện):
  1. [Dòng 5] type == 'vnpay'"
  2. [Dòng 6] type == 'bankapp'"
  3. [Dòng 7] type == 'both'"

================================================================================

📁 FILE 70: qr-guide-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 71: list-bank-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 72: list-bank-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 73: qr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 74: qr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-main.component.ts
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 195] this.themeConfig.deeplink_status == 'Off' ? false : true

!= (2 điều kiện):
  1. [Dòng 212] if (params['locale'] != null) {
  2. [Dòng 218] if ('otp' != this._paymentService.getCurrentPage()) {

================================================================================

📁 FILE 75: qr-mobile.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 134] _locale=='vi'"
  2. [Dòng 135] _locale=='en'"
  3. [Dòng 145] _locale == 'vi'"
  4. [Dòng 147] _locale == 'en'"

!= (2 điều kiện):
  1. [Dòng 234] qr_version2 != 'None'"
  2. [Dòng 260] qr_version2 != 'None'

================================================================================

📁 FILE 76: qr-mobile.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.ts
📊 Thống kê: 30 điều kiện duy nhất
   - === : 6 lần
   - == : 19 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (6 điều kiện):
  1. [Dòng 475] if (!this.d_vnpayqr_wallet && !this.d_vnpayqr_deeplink && (this.listWalletQR?.length === 1
  2. [Dòng 475] this.listWalletDeeplink?.length === 1)) {
  3. [Dòng 594] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  4. [Dòng 595] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  5. [Dòng 601] if (item.type === 'deeplink') {
  6. [Dòng 974] this.listWalletQR?.length === 1

== (19 điều kiện):
  1. [Dòng 283] e.type == 'deeplink') {
  2. [Dòng 294] e.type == 'ewallet'
  3. [Dòng 314] e.type == 'vnpayqr') {
  4. [Dòng 328] e.type == 'wallet')) {
  5. [Dòng 357] e.type == 'ewallet') {
  6. [Dòng 387] if (e.type == 'ewallet') {
  7. [Dòng 410] this.listWallet.length == 1
  8. [Dòng 410] this.listWallet[0].code == 'momo') {
  9. [Dòng 412] this.checkEWalletDeeplink.length == 0) {
  10. [Dòng 493] arrayWallet.length == 0) return false;
  11. [Dòng 495] if (arrayWallet[i].code == key) {
  12. [Dòng 529] if (_re.status == '200'
  13. [Dòng 529] _re.status == '201') {
  14. [Dòng 551] if (d.b.code == s) {
  15. [Dòng 600] if (item.available == true) {
  16. [Dòng 678] if (appcode == 'grabpay'
  17. [Dòng 678] appcode == 'momo') {
  18. [Dòng 681] if (type == 2
  19. [Dòng 721] err.error.code == '04') {

!= (5 điều kiện):
  1. [Dòng 235] if (params['locale'] != null) {
  2. [Dòng 264] if (!(strInstrument != null
  3. [Dòng 629] if (appcode != null) {
  4. [Dòng 946] if (_re.status != '200'
  5. [Dòng 946] _re.status != '201')

================================================================================

📁 FILE 77: safe-html.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/qr-main-version2/safe-html.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 78: queuing.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/queuing/queuing.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 79: queuing.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/queuing/queuing.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 62] if (this.locale == 'vi') {

!= (1 điều kiện):
  1. [Dòng 86] if (this.translate.currentLang != language) {

================================================================================

📁 FILE 80: token-expired-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/token-expired-dialog/token-expired-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 81: token-expired-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/token-expired-dialog/token-expired-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 82: remove-token-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/token-main/remove-token-dialog/remove-token-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 83: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/token-main/token-dialog/dialog-guide-dialog.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (6 điều kiện):
  1. [Dòng 9] data['type'] == 'Visa'
  2. [Dòng 9] data['type'] == 'Master'
  3. [Dòng 9] data['type'] == 'JCB'"
  4. [Dòng 17] data['type'] == 'Visa'"
  5. [Dòng 17] data['type'] == 'Master'"
  6. [Dòng 24] data['type'] == 'Amex'"

================================================================================

📁 FILE 84: token-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/token-main/token-main.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 1 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 8] item.brand_id === 'visa' ? 'height: 14.42px

== (1 điều kiện):
  1. [Dòng 27] token_main == '1'"

!= (2 điều kiện):
  1. [Dòng 20] item['feeService']['fee'] != 0
  2. [Dòng 22] item['feeService']['fee'] != 0"

================================================================================

📁 FILE 85: token-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/main/menu/token-main/token-main.component.ts
📊 Thống kê: 65 điều kiện duy nhất
   - === : 7 lần
   - == : 38 lần
   - !== : 1 lần
   - != : 19 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 98] if (event.keyCode === 13) {
  2. [Dòng 117] if (target.tagName === 'A'
  3. [Dòng 280] && ((token.brand_id === 'amex'
  4. [Dòng 363] return id === 'amex' ? '1234' : '123'
  5. [Dòng 535] if (approval.method === 'REDIRECT') {
  6. [Dòng 538] } else if (approval.method === 'POST_REDIRECT') {
  7. [Dòng 600] return csc != null && !isNaN(+csc) && ((brand_id === 'amex'

== (38 điều kiện):
  1. [Dòng 157] if (message == '0') {
  2. [Dòng 217] item.id == element.id ? element['active'] = true : element['active'] = false
  3. [Dòng 263] if (result == 'success') {
  4. [Dòng 268] if (this.tokenList.length == 0) {
  5. [Dòng 272] } else if (result == 'error') {
  6. [Dòng 280] _val.value.length == 4) || (token.brand_id != 'amex'
  7. [Dòng 280] _val.value.length == 3))
  8. [Dòng 362] id == 'amex' ? this.maxLength = 4 : this.maxLength = 3
  9. [Dòng 407] if (_re.body.state == 'more_info_required') {
  10. [Dòng 429] if (this._res_post.state == 'approved'
  11. [Dòng 429] this._res_post.state == 'failed') {
  12. [Dòng 436] if (this._res_post.state == 'failed') {
  13. [Dòng 452] } else if (this._res_post.state == 'authorization_required') {
  14. [Dòng 453] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  15. [Dòng 465] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  16. [Dòng 481] } else if (_re.body.state == 'authorization_required') {
  17. [Dòng 498] if (this._b == 1
  18. [Dòng 498] this._b == 14
  19. [Dòng 498] this._b == 15
  20. [Dòng 498] this._b == 24
  21. [Dòng 498] this._b == 8
  22. [Dòng 498] this._b == 10
  23. [Dòng 498] this._b == 20
  24. [Dòng 498] this._b == 22
  25. [Dòng 498] this._b == 23
  26. [Dòng 498] this._b == 30
  27. [Dòng 498] this._b == 11
  28. [Dòng 498] this._b == 17
  29. [Dòng 498] this._b == 18
  30. [Dòng 498] this._b == 27
  31. [Dòng 498] this._b == 5
  32. [Dòng 498] this._b == 12
  33. [Dòng 498] this._b == 9) {
  34. [Dòng 557] } else if (_re.body.state == 'failed') {
  35. [Dòng 600] csc.trim().length == 4)
  36. [Dòng 601] csc.trim().length == 3));
  37. [Dòng 678] if (_re.status == '200'
  38. [Dòng 678] _re.status == '201') {

!== (1 điều kiện):
  1. [Dòng 207] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (19 điều kiện):
  1. [Dòng 99] this._res.merchant.id != 'OPTEST' ? this._res.on_off_bank : ''
  2. [Dòng 143] if (params['locale'] != null) {
  3. [Dòng 279] if (_val.value != null
  4. [Dòng 338] if (ua.indexOf('safari') != -1
  5. [Dòng 431] if (this._res_post.return_url != null) {
  6. [Dòng 433] } else if (this._res_post.links != null
  7. [Dòng 433] this._res_post.links.merchant_return != null
  8. [Dòng 433] this._res_post.links.merchant_return.href != null) {
  9. [Dòng 486] if (_re.body.authorization != null
  10. [Dòng 486] _re.body.authorization.links != null
  11. [Dòng 493] if (_re.body.links != null
  12. [Dòng 493] _re.body.links.cancel != null) {
  13. [Dòng 559] if (_re.body.return_url != null) {
  14. [Dòng 561] } else if (_re.body.links != null
  15. [Dòng 561] _re.body.links.merchant_return != null
  16. [Dòng 561] _re.body.links.merchant_return.href != null) {
  17. [Dòng 600] return csc != null
  18. [Dòng 601] || (brand_id != 'amex'
  19. [Dòng 611] return this._i_name != ''

================================================================================

📁 FILE 86: overlay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/overlay/overlay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 87: overlay.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/overlay/overlay.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 88: overlay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/overlay/overlay.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 89: bank-amount.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/pipe/bank-amount.pipe.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 11] return ((a.id == id
  2. [Dòng 11] a.code == id) && a.type.includes(type));

================================================================================

📁 FILE 90: close-dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/services/close-dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 91: data.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/services/data.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 86] const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
  2. [Dòng 86] item.method === method) : null;

================================================================================

📁 FILE 92: deep_link.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/services/deep_link.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 9] if (isIphone == true) {
  2. [Dòng 22] } else if (isAndroid == true) {

================================================================================

📁 FILE 93: dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/services/dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 94: fee.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/services/fee.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 95: multiple_method.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/services/multiple_method.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 96: payment.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/services/payment.service.ts
📊 Thống kê: 14 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 12 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 590] return countPayment == maxPayment
  2. [Dòng 626] if (this.getLatestPayment().state == 'canceled')

!= (12 điều kiện):
  1. [Dòng 111] if (idInvoice != null
  2. [Dòng 111] idInvoice != 0)
  3. [Dòng 121] idInvoice != 0) {
  4. [Dòng 298] if (this._merchantid != null
  5. [Dòng 298] this._tranref != null
  6. [Dòng 298] this._state != null
  7. [Dòng 369] let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
  8. [Dòng 405] urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
  9. [Dòng 427] if (paymentId != null) {
  10. [Dòng 430] if (fee != null) {
  11. [Dòng 527] if (res?.status != 200
  12. [Dòng 527] res?.status != 201) return;

================================================================================

📁 FILE 97: qr.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/services/qr.service.ts
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 58] if (res?.state == 'canceled') {
  2. [Dòng 99] state == 'authorization_required'

!= (3 điều kiện):
  1. [Dòng 40] if (_re.status != '200'
  2. [Dòng 40] _re.status != '201') {
  3. [Dòng 49] latestPayment?.state != "authorization_required") {

================================================================================

📁 FILE 98: time-stop.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/services/time-stop.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 99: token-main.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/services/token-main.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 100: index.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/translate/index.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 101: lang-en.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/translate/lang-en.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 102: lang-vi.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/translate/lang-vi.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 103: translate.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/translate/translate.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 104: translate.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/translate/translate.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 37] if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
  2. [Dòng 38] else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';

================================================================================

📁 FILE 105: translations.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/translate/translations.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 106: apps-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/util/apps-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 549] if (e.name == bankSwift) { // TODO: get by swift
  2. [Dòng 557] return this.apps.find(e => e.code == appCode);

================================================================================

📁 FILE 107: apps-information.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/util/apps-information.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 108: banks-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/util/banks-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1013] if (+e.id == bankId) {
  2. [Dòng 1063] if (e.swiftCode == bankSwift) {

================================================================================

📁 FILE 109: error-handler.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/util/error-handler.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 9] err?.status === 400
  2. [Dòng 10] err?.error?.name === 'INVALID_CARD_FEE'

================================================================================

📁 FILE 110: util.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/app/util/util.ts
📊 Thống kê: 40 điều kiện duy nhất
   - === : 15 lần
   - == : 18 lần
   - !== : 3 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (15 điều kiện):
  1. [Dòng 56] if (v.length === 2
  2. [Dòng 56] this.flag.length === 3
  3. [Dòng 56] this.flag.charAt(this.flag.length - 1) === '/') {
  4. [Dòng 60] if (v.length === 1) {
  5. [Dòng 62] } else if (v.length === 2) {
  6. [Dòng 65] v.length === 2) {
  7. [Dòng 73] if (len === 2) {
  8. [Dòng 145] if (M[1] === 'Chrome') {
  9. [Dòng 270] if (param === key) {
  10. [Dòng 393] pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
  11. [Dòng 397] target === 0
  12. [Dòng 474] if (cardTypeBank === 'bank_card_number') {
  13. [Dòng 477] } else if (cardTypeBank === 'bank_account_number') {
  14. [Dòng 527] if (event.keyCode === 8
  15. [Dòng 527] event.key === "Backspace"

== (18 điều kiện):
  1. [Dòng 15] if (temp.length == 0) {
  2. [Dòng 22] return (counter % 10 == 0);
  3. [Dòng 26] if (currency == 'USD') {
  4. [Dòng 119] if (this.checkCount == 1) {
  5. [Dòng 131] if (results == null) {
  6. [Dòng 164] if (c.length == 3) {
  7. [Dòng 177] d = d == undefined ? '.' : d
  8. [Dòng 178] t = t == undefined ? '
  9. [Dòng 258] return results == null ? null : results[1]
  10. [Dòng 527] event.inputType == 'deleteContentBackward') {
  11. [Dòng 528] if (event.target.name == 'exp_date'
  12. [Dòng 536] event.inputType == 'insertCompositionText') {
  13. [Dòng 550] if (((_val.length == 4
  14. [Dòng 550] _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  15. [Dòng 550] _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  16. [Dòng 576] iss_date.length == 4
  17. [Dòng 576] iss_date.search('/') == -1)
  18. [Dòng 577] iss_date.length == 5))

!== (3 điều kiện):
  1. [Dòng 265] queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
  2. [Dòng 266] if (queryString !== '') {
  3. [Dòng 397] if (target !== 0

!= (4 điều kiện):
  1. [Dòng 147] if (tem != null) {
  2. [Dòng 152] if ((tem = ua.match(/version\/(\d+)/i)) != null) {
  3. [Dòng 472] if (ua.indexOf('safari') != -1
  4. [Dòng 529] if (v.length != 3) {

================================================================================

📁 FILE 111: qrcode.js
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/assets/script/qrcode.js
📊 Thống kê: 67 điều kiện duy nhất
   - === : 2 lần
   - == : 53 lần
   - !== : 0 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 2255] if (typeof define === 'function'
  2. [Dòng 2257] } else if (typeof exports === 'object') {

== (53 điều kiện):
  1. [Dòng 68] if (_dataCache == null) {
  2. [Dòng 85] if ( (0 <= r && r <= 6 && (c == 0
  3. [Dòng 85] c == 6) )
  4. [Dòng 86] || (0 <= c && c <= 6 && (r == 0
  5. [Dòng 86] r == 6) )
  6. [Dòng 107] if (i == 0
  7. [Dòng 122] _modules[r][6] = (r % 2 == 0);
  8. [Dòng 129] _modules[6][c] = (c % 2 == 0);
  9. [Dòng 152] if (r == -2
  10. [Dòng 152] r == 2
  11. [Dòng 152] c == -2
  12. [Dòng 152] c == 2
  13. [Dòng 153] || (r == 0
  14. [Dòng 153] c == 0) ) {
  15. [Dòng 169] ( (bits >> i) & 1) == 1);
  16. [Dòng 226] if (col == 6) col -= 1;
  17. [Dòng 232] if (_modules[row][col - c] == null) {
  18. [Dòng 237] dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
  19. [Dòng 249] if (bitIndex == -1) {
  20. [Dòng 458] margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
  21. [Dòng 498] if (typeof arguments[0] == 'object') {
  22. [Dòng 587] margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
  23. [Dòng 733] if (b == -1) throw 'eof';
  24. [Dòng 741] if (b0 == -1) break;
  25. [Dòng 767] if (typeof b == 'number') {
  26. [Dòng 768] if ( (b & 0xff) == b) {
  27. [Dòng 910] return function(i, j) { return (i + j) % 2 == 0
  28. [Dòng 912] return function(i, j) { return i % 2 == 0
  29. [Dòng 914] return function(i, j) { return j % 3 == 0
  30. [Dòng 916] return function(i, j) { return (i + j) % 3 == 0
  31. [Dòng 918] return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
  32. [Dòng 920] return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
  33. [Dòng 922] return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
  34. [Dòng 924] return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
  35. [Dòng 1011] if (r == 0
  36. [Dòng 1011] c == 0) {
  37. [Dòng 1015] if (dark == qrcode.isDark(row + r, col + c) ) {
  38. [Dòng 1036] if (count == 0
  39. [Dòng 1036] count == 4) {
  40. [Dòng 1149] if (typeof num.length == 'undefined') {
  41. [Dòng 1155] num[offset] == 0) {
  42. [Dòng 1495] if (typeof rsBlock == 'undefined') {
  43. [Dòng 1538] return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
  44. [Dòng 1543] _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
  45. [Dòng 1599] if (data.length - i == 1) {
  46. [Dòng 1601] } else if (data.length - i == 2) {
  47. [Dòng 1866] } else if (n == 62) {
  48. [Dòng 1868] } else if (n == 63) {
  49. [Dòng 1928] if (_buflen == 0) {
  50. [Dòng 1937] if (c == '=') {
  51. [Dòng 1961] } else if (c == 0x2b) {
  52. [Dòng 1963] } else if (c == 0x2f) {
  53. [Dòng 2133] if (table.size() == (1 << bitLength) ) {

!= (12 điều kiện):
  1. [Dòng 119] if (_modules[r][6] != null) {
  2. [Dòng 126] if (_modules[6][c] != null) {
  3. [Dòng 144] if (_modules[row][col] != null) {
  4. [Dòng 365] while (buffer.getLengthInBits() % 8 != 0) {
  5. [Dòng 750] if (count != numChars) {
  6. [Dòng 751] throw count + ' != ' + numChars
  7. [Dòng 878] while (data != 0) {
  8. [Dòng 1733] if (test.length != 2
  9. [Dòng 1733] ( (test[0] << 8) | test[1]) != code) {
  10. [Dòng 1894] if (_length % 3 != 0) {
  11. [Dòng 2067] if ( (data >>> length) != 0) {
  12. [Dòng 2178] return typeof _map[key] != 'undefined'

================================================================================

📁 FILE 112: environment.dev.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/environments/environment.dev.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 113: environment.mtf.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/environments/environment.mtf.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 114: environment.prod.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/environments/environment.prod.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 115: environment.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/environments/environment.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 116: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 117: karma.conf.js
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/karma.conf.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 118: main.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/main.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 119: polyfills.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/polyfills.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 120: test.ts
📍 Đường dẫn: /root/projects/onepay/paygate-tns/src/test.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📋 TÓM TẮT TẤT CẢ ĐIỀU KIỆN DUY NHẤT:
====================================================================================================

=== (161 điều kiện duy nhất):
------------------------------------------------------------
1. return lang === this._translate.currentLang
2. isPopupSupport === 'True') || (rePayment
3. isPopupSupport === 'True'"
4. isPopupSupport === 'True')">
5. params.timeout === 'true') {
6. this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
7. _re.body.state === 'unpaid');
8. if (this.errorCode === 'overtime'
9. this.errorCode === '253') {
10. params.name === 'CUSTOMER_INTIME'
11. params.code === '09') {
12. if (this.timeLeft === 0) {
13. if (YY % 400 === 0
14. YY % 4 === 0)) {
15. if (YYYY % 400 === 0
16. YYYY % 4 === 0)) {
17. <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
18. [class.red_border_no_background]="c_expdate && (valueDate.length === 0
19. valueDate.trim().length === 0)"
20. if (target.tagName === 'A'
21. if (isIE[0] === 'MSIE'
22. +isIE[1] === 10) {
23. if ((_val.value.substr(-1) === ' '
24. _val.value.length === 24) {
25. if (this.cardTypeBank === 'bank_card_number') {
26. } else if (this.cardTypeBank === 'bank_account_number') {
27. } else if (this.cardTypeBank === 'bank_username') {
28. } else if (this.cardTypeBank === 'bank_customer_code') {
29. this.cardTypeBank === 'bank_card_number'
30. if (this.cardTypeOcean === 'IB') {
31. } else if (this.cardTypeOcean === 'MB') {
32. if (_val.value.substr(0, 2) === '84') {
33. } else if (this.cardTypeOcean === 'ATM') {
34. if (this.cardTypeBank === 'bank_account_number') {
35. this.cardTypeBank === 'bank_card_number') {
36. if (this.cardTypeBank === 'bank_card_number'
37. if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
38. if (event.keyCode === 8
39. event.key === "Backspace"
40. if (v.length === 2
41. this.flag.length === 3
42. this.flag.charAt(this.flag.length - 1) === '/') {
43. if (v.length === 1) {
44. } else if (v.length === 2) {
45. v.length === 2) {
46. if (len === 2) {
47. if ((this.cardTypeBank === 'bank_account_number'
48. this.cardTypeBank === 'bank_username'
49. this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
50. this.cardTypeOcean === 'ATM')
51. || (this.cardTypeOcean === 'IB'
52. if (valIn === this._translate.instant('bank_card_number')) {
53. } else if (valIn === this._translate.instant('bank_account_number')) {
54. } else if (valIn === this._translate.instant('bank_username')) {
55. } else if (valIn === this._translate.instant('bank_customer_code')) {
56. if (_val.value === ''
57. _val.value === null
58. _val.value === undefined) {
59. if (_val.value && (this.cardTypeBank === 'bank_card_number'
60. if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
61. this.cardTypeOcean === 'MB') {
62. this.cardTypeOcean === 'IB'
63. if ((this.cardTypeBank === 'bank_card_number'
64. if (this.cardName === undefined
65. this.cardName === '') {
66. if (this.valueDate === undefined
67. this.valueDate === '') {
68. c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
69. _inExpDate.trim().length === 0)"
70. if (focusElement === 'card_name') {
71. } else if (focusElement === 'exp_date'
72. focusExpDateElement === 'card_name') {
73. if (this.cardTypeBank === 'bank_account_number'
74. if (approval.method === 'REDIRECT') {
75. } else if (approval.method === 'POST_REDIRECT') {
76. filteredData.length === 0"
77. if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
78. filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
79. if (valOut === 'auth') {
80. if (this._b === '1'
81. this._b === '20'
82. this._b === '64') {
83. if (this._b === '36'
84. this._b === '18'
85. this._b === '19'
86. if (this._b === '19'
87. this._b === '16'
88. this._b === '25'
89. this._b === '33'
90. this._b === '39'
91. this._b === '11'
92. this._b === '17'
93. this._b === '36'
94. this._b === '44'
95. this._b === '64'
96. if (this._b === '20'
97. if (this._b === '18') {
98. if (_formCard.country === 'default') {
99. if ((v.substr(-1) === ' '
100. if (deviceValue === 'CA'
101. deviceValue === 'US') {
102. this.c_country = _val.value === 'default'
103. [ngStyle]="{'border-color': type === 5 ? this.themeColor.border_color : border_color}"
104. type === 5"
105. type === 6"
106. [ngStyle]="{'border-color': type === 6 ? this.themeColor.border_color : border_color}"
107. type === 2"
108. [ngStyle]="{'border-color': type === 2 ? this.themeColor.border_color : border_color}"
109. <div *ngIf="(type === 2
110. type === '2'
111. type === 7"
112. [ngStyle]="{'border-color': type === 7 ? this.themeColor.border_color : border_color}"
113. type === 8"
114. [ngStyle]="{'border-color': type === 8 ? this.themeColor.border_color : border_color}"
115. type === 4"
116. [ngStyle]="{'border-color': type === 4 ? this.themeColor.border_color : border_color}"
117. type === 3"
118. [ngStyle]="{'border-color': type === 3 ? this.themeColor.border_color : border_color}"
119. d_vrbank===true"
120. this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number === 1
121. if (this._res.state === 'unpaid'
122. this._res.state === 'not_paid') {
123. if ('op' === auth
124. } else if ('bank' === auth
125. return id === 'amex' ? '1234' : '123'
126. if (this.timeLeftPaypal === 0) {
127. filteredData.length === 0
128. filteredDataOther.length === 0"
129. filteredDataMobile.length === 0
130. filteredDataOtherMobile.length === 0"
131. this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
132. this.filteredDataOther = this.appList.filter(item => item.type === 'other');
133. this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
134. this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
135. item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
136. filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
137. if (item.type === 'mobile_banking') {
138. this.appList.length === 1
139. listVNPayQR.length === 0"
140. this.listWalletQR.length === 1) {
141. this.listWalletQR.length === 1
142. if (!this.d_vnpayqr_wallet && !this.d_vnpayqr_deeplink && (this.listWalletQR?.length === 1
143. this.listWalletDeeplink?.length === 1)) {
144. if (item.type === 'deeplink') {
145. this.listWalletQR?.length === 1
146. item.brand_id === 'visa' ? 'height: 14.42px
147. if (event.keyCode === 13) {
148. && ((token.brand_id === 'amex'
149. return csc != null && !isNaN(+csc) && ((brand_id === 'amex'
150. const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
151. item.method === method) : null;
152. err?.status === 400
153. err?.error?.name === 'INVALID_CARD_FEE'
154. if (M[1] === 'Chrome') {
155. if (param === key) {
156. pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
157. target === 0
158. if (cardTypeBank === 'bank_card_number') {
159. } else if (cardTypeBank === 'bank_account_number') {
160. if (typeof define === 'function'
161. } else if (typeof exports === 'object') {

== (521 điều kiện duy nhất):
------------------------------------------------------------
1. 'vi' == params['locale']) {
2. 'en' == params['locale']) {
3. errorCode == '11'"
4. *ngIf="((isSent == false
5. isSent == false
6. [class.select_only]="!(isSent == false
7. <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
8. (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
9. <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
10. !(errorCode == 'overtime' || errorCode == '253') && isBack && !invoiceNearExpire && !paymentPending
11. this.res.themes.theme == 'tns') {
12. params.response_code == 'overtime') {
13. if (_re.status == '200'
14. _re.status == '201') {
15. if (_re2.status == '200'
16. _re2.status == '201') {
17. if (this.errorCode == 'overtime'
18. this.errorCode == '253') {
19. } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
20. this.res.state == 'canceled') {
21. if (lastPayment?.state == 'pending') {
22. if (this.isTimePause == false) {
23. if ((dataPassed.status == '200'
24. dataPassed.status == '201') && dataPassed.body != null) {
25. dataPassed.body.themes.logo_full == 'True') {
26. if (this.type == 5
27. } else if (this.type == 6
28. } else if (this.type == 2
29. } else if (this.type == 7
30. } else if (this.type == 8
31. } else if (this.type == 4
32. } else if (this.type == 3
33. if (this.locale == 'en') {
34. if (name == 'MAFC')
35. if (bankId == 3
36. bankId == 61
37. bankId == 8
38. bankId == 49
39. bankId == 48
40. bankId == 10
41. bankId == 53
42. bankId == 17
43. bankId == 65
44. bankId == 23
45. bankId == 52
46. bankId == 27
47. bankId == 66
48. bankId == 9
49. bankId == 54
50. bankId == 37
51. bankId == 38
52. bankId == 39
53. bankId == 40
54. bankId == 42
55. bankId == 44
56. bankId == 72
57. bankId == 59
58. bankId == 51
59. bankId == 64
60. bankId == 58
61. bankId == 56
62. bankId == 55
63. bankId == 60
64. bankId == 68
65. bankId == 74
66. bankId == 75 //LEB HANA Napas
67. token_site == 'onepay'
68. this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
69. if (this._b == 18
70. this._b == 19) {
71. if (this._b == 19) {//19BIDV
72. } else if (this._b == 3
73. this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
74. if (this._b == 27) {
75. } else if (this._b == 12) {// 12SHB
76. } else if (this._b == 18) { //18Oceanbank-ocb
77. if (this._b == 19
78. this._b == 3
79. this._b == 27
80. this._b == 12) {
81. } else if (this._b == 18) {
82. if (this.checkBin(_val.value) && (this._b == 3
83. this._b == 27)) {
84. if (this._b == 3) {
85. this.cardTypeOcean == 'ATM') {
86. if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
87. this._b == 18)) {
88. if (this.checkBin(v) && (this._b == 3
89. event.inputType == 'deleteContentBackward') {
90. if (event.target.name == 'exp_date'
91. event.inputType == 'insertCompositionText') {
92. if (((this.valueDate.length == 4
93. this.valueDate.search('/') == -1) || this.valueDate.length == 5)
94. this.valueDate.length == 5)
95. if (temp.length == 0) {
96. return (counter % 10 == 0);
97. } else if (this._b == 19) {
98. } else if (this._b == 27) {
99. if (this._b == 12) {
100. if (this.cardTypeBank == 'bank_customer_code') {
101. } else if (this.cardTypeBank == 'bank_account_number') {
102. _formCard.exp_date.length == 5
103. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
104. this._b == 3)) {//27-pvcombank;3-TPB ;
105. if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
106. this._b == 19
107. this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
108. if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
109. if (this.cardTypeOcean == 'IB') {
110. } else if (this.cardTypeOcean == 'MB') {
111. } else if (this.cardTypeOcean == 'ATM') {
112. this.token_site == 'onepay'
113. if (this._res_post.state == 'approved'
114. this._res_post.state == 'failed') {
115. } else if (this._res_post.state == 'authorization_required') {
116. if (this._b == 18) {
117. if (this._b == 27
118. this._b == 18) {
119. if ((cardNo.length == 16
120. if ((cardNo.length == 16 || (cardNo.length == 19
121. && ((this._b == 18
122. cardNo.length == 19) || this._b != 18)
123. if (this._b == +e.id) {
124. if (valIn == 1) {
125. } else if (valIn == 2) {
126. this._b == 3) {
127. if (this._b == 19) {
128. if (cardType == this._translate.instant('internetbanking')
129. } else if (cardType == this._translate.instant('mobilebanking')
130. } else if (cardType == this._translate.instant('atm')
131. this._b == 18))) {
132. } else if (this._b == 18
133. this.c_expdate = !(((this.valueDate.length == 4
134. this.valueDate.length == 4
135. this.valueDate.search('/') == -1)
136. this.valueDate.length == 5))
137. if (this._b == 8) {//MB Bank
138. if (this._b == 18) {//Oceanbank
139. if (this._b == 8) {
140. if (this._b == 12) { //SHB
141. } else if (this._res.state == 'authorization_required') {
142. if (this.challengeCode == '') {
143. if (this._b == 18) {//8-MB Bank;18-oceanbank
144. <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_card_number')">
145. <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_account_number')">
146. if (this._b == 12) this.isShbGroup = true;
147. return this._b == 9
148. this._b == 11
149. this._b == 16
150. this._b == 17
151. this._b == 25
152. this._b == 44
153. this._b == 57
154. this._b == 59
155. this._b == 61
156. this._b == 63
157. this._b == 69
158. if (this._b == 12
159. this.cardTypeBank == 'bank_account_number') {
160. _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
161. cardNo.length == 19) && cardNo.startsWith("970443") && this._util.checkMod10(cardNo) && this.checkBin(cardNo)) {
162. if (this._b == 2
163. this._b == 31) {
164. if (this._b == 2) {
165. } else if (this._b == 6) {
166. } else if (this._b == 31) {
167. if (this._b == 5) {//5-vib;
168. if (this._b == 5) {
169. if (this.checkBin(_val.value) && (this._b == 5)) {
170. if (this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
171. if (this.checkBin(v) && (this._b == 5)) {
172. this._b == 5) {//5 vib ;
173. this._b == 5) {//5vib;
174. _b == 68"
175. if (this._b == 1
176. this._b == 20
177. this._b == 36
178. this._b == 64
179. this._b == 55
180. this._b == 47
181. this._b == 48
182. this._b == 59) {
183. return this._b == 11
184. this._b == 33
185. this._b == 39
186. this._b == 43
187. this._b == 45
188. this._b == 67
189. this._b == 68
190. this._b == 72
191. this._b == 73
192. this._b == 74
193. this._b == 75
194. this._b == 14
195. this._b == 15
196. this._b == 24
197. this._b == 8
198. this._b == 10
199. this._b == 22
200. this._b == 23
201. this._b == 30
202. this._b == 9) {
203. (cardNo.length == 19
204. (cardNo.length == 19 && (this._b == 1
205. this._b == 4
206. this._b == 59))
207. this._util.checkMod10(cardNo) == true
208. return ((value.length == 4
209. value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
210. value.length == 5) && parseInt(value.split('/')[0]
211. this._inExpDate.length == 4
212. this._inExpDate.search('/') == -1)
213. this._inExpDate.length == 5))
214. ((!token && _auth==0 && vietcombankGroupSelected) || (token && _b==16))
215. _b==16))">
216. !token && _auth==0 && techcombankGroupSelected
217. !token && _auth==0 && bankaccountGroupSelected
218. !token && _auth==0 && vibbankGroupSelected
219. !token && _auth==0 && shbGroupSelected
220. (token || _auth==1) && _b != 16
221. this._auth == 0
222. this.tokenList.length == 0) {
223. this.filteredData.length == 1
224. $event == 'true') {
225. if (bankId == 1
226. bankId == 4
227. bankId == 7
228. bankId == 11
229. bankId == 14
230. bankId == 15
231. bankId == 16
232. bankId == 20
233. bankId == 22
234. bankId == 24
235. bankId == 25
236. bankId == 30
237. bankId == 33
238. bankId == 34
239. bankId == 35
240. bankId == 36
241. bankId == 41
242. bankId == 43
243. bankId == 45
244. bankId == 46
245. bankId == 47
246. bankId == 50
247. bankId == 57
248. bankId == 62
249. bankId == 63
250. bankId == 67
251. bankId == 69
252. bankId == 70
253. bankId == 71
254. bankId == 73
255. bankId == 32
256. bankId == 75) {
257. } else if (bankId == 6
258. bankId == 31
259. bankId == 80
260. bankId == 2) {
261. } else if (bankId == 3
262. bankId == 18
263. bankId == 19
264. bankId == 27) {
265. } else if (bankId == 5) {
266. } else if (bankId == 12) {
267. this._b == '55'
268. this._b == '47'
269. this._b == '48'
270. this._b == '59'
271. this._b == '73'
272. this._b == '12') {
273. this._b == '3'
274. this._b == '43'
275. this._b == '45'
276. this._b == '57'
277. this._b == '61'
278. this._b == '63'
279. this._b == '67'
280. this._b == '68'
281. this._b == '69'
282. this._b == '72'
283. this._b == '9'
284. this._b == '74'
285. this._b == '75') {
286. this._b == '36'
287. if (item['id'] == this._b) {
288. data['type'] == '5'
289. data['type'] == '7'"
290. data['type'] == '6'
291. data['type'] == '8'"
292. regexp.test('card;;visa;USD')) && (this._type == 5
293. this._type == 7);
294. regexp.test('card;;mastercard;USD')) && (this._type == 5
295. regexp.test('card;;amex;USD')) && (this._type == 6
296. this._type == 8);
297. } else if (this._res_post.state == 'failed') { // lỗi mới kiểm tra sang trang lỗi
298. if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
299. } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
300. v.length == 15) || (v.length == 16
301. v.length == 19))
302. this._util.checkMod10(v) == true) {
303. cardNo.length == 15)
304. cardNo.length == 16)
305. cardNo.startsWith('81')) && (cardNo.length == 16
306. cardNo.length == 19))
307. this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
308. this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
309. v.length == 5) {
310. v.length == 4
311. v.length == 3)
312. _val.value.length == 4
313. _val.value.length == 3)
314. this._i_csc.length == 4) ||
315. this._i_csc.length == 3)
316. type == 5"
317. tokenList.length == 1)">
318. tokenList.length == 1"
319. if (el == 5) {
320. } else if (el == 6) {
321. } else if (el == 7) {
322. } else if (el == 8) {
323. } else if (el == 2) {
324. } else if (el == 4) {
325. } else if (el == 3) {
326. if (!isNaN(_re.status) && (_re.status == '200'
327. _re.status == '201') && _re.body != null) {
328. if (('closed' == this._res_polling.state
329. 'canceled' == this._res_polling.state
330. 'expired' == this._res_polling.state)
331. } else if ('paid' == this._res_polling.state) {
332. this._res_polling.payments == null) {
333. if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'authorization_required') {
334. } else if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
335. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
336. this._paymentService.getCurrentPage() == 'enter_card') {
337. } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
338. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
339. } else if ('not_paid' == this._res_polling.state) {
340. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
341. if (message == '1') {
342. if (this.checkInvoiceState() == 1) {
343. this.version2 = _re.body?.merchant?.qr_version == "2"
344. if (this.themeConfig.default_method == 'International'
345. } else if (this.themeConfig.default_method == 'Domestic'
346. } else if (this.themeConfig.default_method == 'QR'
347. } else if (this.themeConfig.default_method == 'Paypal'
348. if (('closed' == this._res.state
349. 'canceled' == this._res.state
350. 'expired' == this._res.state
351. 'paid' == this._res.state)
352. if ('paid' == this._res.state
353. if ((this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number) == 0
354. this._auth == 0) {
355. if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
356. this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
357. if (this._res.payments[this._res.payments.length - 1].state == 'approved'
358. } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
359. if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required') {
360. } else if (this._res.payments[this._res.payments.length - 1].state == 'pending') {
361. } else if (this._res.payments[this._res.payments.length - 1].state == 'canceled') {
362. } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
363. this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
364. } else if (idBrand == 'atm'
365. if ('paid' == this._res.state) {
366. this._res.merchant.token_site == 'onepay')) {
367. this._res.payments == null) {
368. this._res.payments[this._res.payments.length - 1].state == 'pending') {
369. if (this._res.currencies[0] == 'USD') {
370. if (item.instrument.issuer.brand.id == 'atm') {
371. } else if (item.instrument.issuer.brand.id == 'visa'
372. item.instrument.issuer.brand.id == 'mastercard') {
373. if (item.instrument.issuer_location == 'd') {
374. } else if (item.instrument.issuer.brand.id == 'amex') {
375. } else if (item.instrument.issuer.brand.id == 'jcb') {
376. return merchant.token_cvv == true
377. return (merchant.token_name == true)
378. return (merchant.token_email == true)
379. return (merchant.token_phone == true)
380. if (type == 'qrv1') {
381. if (type == 'mobile') {
382. e.type == 'ewallet'
383. e.code == 'momo')) {
384. } else if (type == 'desktop') {
385. e.type == 'vnpayqr') || (regex.test(strTest)
386. this.tokenList.length == 1) {
387. if (_val == 2
388. if (this.type == 4) {
389. if (data._locale == 'en') {
390. screen=='qr'"
391. screen=='confirm_close'"
392. this.themeColor.deeplink_status == 'Off' ? false : true
393. if (item.available == true) {
394. if (appcode == 'grabpay'
395. appcode == 'momo') {
396. if (type == 2
397. err.error.code == '04') {
398. e.type == 'vnpayqr') {
399. e.type == 'ewallet') {
400. e.type == 'wallet')) {
401. if (d.b.code == s) {
402. type == 'vnpay'"
403. type == 'bankapp'"
404. type == 'both'"
405. this.themeConfig.deeplink_status == 'Off' ? false : true
406. _locale=='vi'"
407. _locale=='en'"
408. _locale == 'vi'"
409. _locale == 'en'"
410. e.type == 'deeplink') {
411. if (e.type == 'ewallet') {
412. this.listWallet.length == 1
413. this.listWallet[0].code == 'momo') {
414. this.checkEWalletDeeplink.length == 0) {
415. arrayWallet.length == 0) return false;
416. if (arrayWallet[i].code == key) {
417. if (this.locale == 'vi') {
418. data['type'] == 'Visa'
419. data['type'] == 'Master'
420. data['type'] == 'JCB'"
421. data['type'] == 'Visa'"
422. data['type'] == 'Master'"
423. data['type'] == 'Amex'"
424. token_main == '1'"
425. if (message == '0') {
426. item.id == element.id ? element['active'] = true : element['active'] = false
427. if (result == 'success') {
428. if (this.tokenList.length == 0) {
429. } else if (result == 'error') {
430. _val.value.length == 4) || (token.brand_id != 'amex'
431. _val.value.length == 3))
432. id == 'amex' ? this.maxLength = 4 : this.maxLength = 3
433. if (_re.body.state == 'more_info_required') {
434. if (this._res_post.state == 'failed') {
435. } else if (_re.body.state == 'authorization_required') {
436. this._b == 18
437. this._b == 5
438. this._b == 12
439. } else if (_re.body.state == 'failed') {
440. csc.trim().length == 4)
441. csc.trim().length == 3));
442. return ((a.id == id
443. a.code == id) && a.type.includes(type));
444. if (isIphone == true) {
445. } else if (isAndroid == true) {
446. return countPayment == maxPayment
447. if (this.getLatestPayment().state == 'canceled')
448. if (res?.state == 'canceled') {
449. state == 'authorization_required'
450. if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
451. else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';
452. if (e.name == bankSwift) { // TODO: get by swift
453. return this.apps.find(e => e.code == appCode);
454. if (+e.id == bankId) {
455. if (e.swiftCode == bankSwift) {
456. if (currency == 'USD') {
457. if (this.checkCount == 1) {
458. if (results == null) {
459. if (c.length == 3) {
460. d = d == undefined ? '.' : d
461. t = t == undefined ? '
462. return results == null ? null : results[1]
463. if (((_val.length == 4
464. _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
465. _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
466. iss_date.length == 4
467. iss_date.search('/') == -1)
468. iss_date.length == 5))
469. if (_dataCache == null) {
470. if ( (0 <= r && r <= 6 && (c == 0
471. c == 6) )
472. || (0 <= c && c <= 6 && (r == 0
473. r == 6) )
474. if (i == 0
475. _modules[r][6] = (r % 2 == 0);
476. _modules[6][c] = (c % 2 == 0);
477. if (r == -2
478. r == 2
479. c == -2
480. c == 2
481. || (r == 0
482. c == 0) ) {
483. ( (bits >> i) & 1) == 1);
484. if (col == 6) col -= 1;
485. if (_modules[row][col - c] == null) {
486. dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
487. if (bitIndex == -1) {
488. margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
489. if (typeof arguments[0] == 'object') {
490. margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
491. if (b == -1) throw 'eof';
492. if (b0 == -1) break;
493. if (typeof b == 'number') {
494. if ( (b & 0xff) == b) {
495. return function(i, j) { return (i + j) % 2 == 0
496. return function(i, j) { return i % 2 == 0
497. return function(i, j) { return j % 3 == 0
498. return function(i, j) { return (i + j) % 3 == 0
499. return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
500. return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
501. return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
502. return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
503. if (r == 0
504. c == 0) {
505. if (dark == qrcode.isDark(row + r, col + c) ) {
506. if (count == 0
507. count == 4) {
508. if (typeof num.length == 'undefined') {
509. num[offset] == 0) {
510. if (typeof rsBlock == 'undefined') {
511. return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
512. _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
513. if (data.length - i == 1) {
514. } else if (data.length - i == 2) {
515. } else if (n == 62) {
516. } else if (n == 63) {
517. if (_buflen == 0) {
518. if (c == '=') {
519. } else if (c == 0x2b) {
520. } else if (c == 0x2f) {
521. if (table.size() == (1 << bitLength) ) {

!== (30 điều kiện duy nhất):
------------------------------------------------------------
1. this.lastValue !== $event.target.value)) {
2. if (YY % 400 === 0 || (YY % 100 !== 0
3. if (YYYY % 400 === 0 || (YYYY % 100 !== 0
4. event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
5. key !== '3') {
6. this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
7. codeResponse.toString() !== '0') {
8. cardNo.length !== 0) {
9. if (this.cardTypeBank !== 'bank_card_number') {
10. if (this.cardTypeBank !== 'bank_account_number') {
11. if (this.cardTypeBank !== 'bank_username') {
12. if (this.cardTypeBank !== 'bank_customer_code') {
13. this.lb_card_account !== this._translate.instant('ocb_account')) {
14. this.lb_card_account !== this._translate.instant('ocb_phone')) {
15. this.lb_card_account !== this._translate.instant('ocb_card')) {
16. this._b !== 18) || (this.cardTypeOcean === 'ATM'
17. this._b !== 18) || (this._b == 18)) {
18. key !== '8') {
19. codeResponse.toString() !== '0'){
20. event.inputType !== 'deleteContentBackward') || v.length == 5) {
21. if (deviceValue !== 'default') {
22. this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
23. return this._i_country_code !== 'default'
24. this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
25. this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {
26. if (_val !== 3) {
27. this._util.getElementParams(this.currentUrl, 't_id') !== '') {
28. queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
29. if (queryString !== '') {
30. if (target !== 0

!= (175 điều kiện duy nhất):
------------------------------------------------------------
1. if (params['locale'] != null
2. } else if (params['locale'] != null
3. if (userLang != null
4. if(value!=null){
5. if (this.res != null
6. this.res.links != null
7. this.res.links.merchant_return != null
8. this.res.links.merchant_return.href != null) {
9. if (this._idInvoice != null
10. this._idInvoice != 0) {
11. if (this._paymentService.getInvoiceDetail() != null) {
12. dataPassed.body != null) {
13. dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
14. if (this._translate.currentLang != language) {
15. } else if (this._b != 18) {
16. if (this.htmlDesc != null
17. if (ua.indexOf('safari') != -1
18. if (_val.value != '') {
19. this.auth_method != null) {
20. if (this.valueDate.length != 3) {
21. if (_formCard.exp_date != null
22. if (this.cardName != null
23. if (this._res_post.links != null
24. this._res_post.links.merchant_return != null
25. this._res_post.links.merchant_return.href != null) {
26. if (this._res_post.authorization != null
27. this._res_post.authorization.links != null
28. this._res_post.authorization.links.approval != null) {
29. this._res_post.links.cancel != null) {
30. this._b != 27
31. this._b != 12
32. this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
33. this._b != 18)
34. if (this._b != 18
35. this._b != 19) {
36. if (this._res.links != null
37. this._res.links.merchant_return != null
38. this._res.links.merchant_return.href != null) {
39. if (!(_formCard.otp != null
40. if (!(_formCard.password != null
41. if (this._inExpDate.length != 3) {
42. if (this._b != 9
43. this._b != 16
44. this._b != 17
45. this._b != 25
46. this._b != 44
47. this._b != 57
48. this._b != 59
49. this._b != 61
50. this._b != 63
51. this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
52. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75'].indexOf(this._b.toString()) != -1) {
53. if (this._res_post.return_url != null) {
54. let userName = _formCard.name != null ? _formCard.name : ''
55. this._res_post.authorization.links.approval != null
56. this._res_post.authorization.links.approval.href != null) {
57. userName = paramUserName != null ? paramUserName : ''
58. if (params['locale'] != null) {
59. if ('otp' != this._paymentService.getCurrentPage()) {
60. this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
61. if (!(strInstrument != null
62. if (strInstrument.substring(0, 1) != '^'
63. strInstrument.substr(strInstrument.length - 1) != '$') {
64. if (bankid != null) {
65. _showAVS!=true"
66. } else if (this._res_post.links != null
67. cardNo != null
68. v != null
69. this.c_csc = (!(_val.value != null
70. this._i_csc != null
71. feeService['visa_mastercard_d']['fee'] != 0"
72. feeService['amex_d']['fee'] != 0"
73. feeService['atm']['fee'] != 0"
74. feeService['visa_mastercard_i']['fee'] != 0"
75. feeService['amex_i']['fee'] != 0"
76. feeService['qr']['fee'] != 0"
77. feeService['pp']['fee'] != 0"
78. this._paymentService.getState() != 'error') {
79. if (this._paymentService.getCurrentPage() != 'otp') {
80. _re.body != null) {
81. this._res_polling.links != null
82. this._res_polling.links.merchant_return != null //
83. } else if (this._res_polling.merchant != null
84. this._res_polling.merchant_invoice_reference != null
85. } else if (this._res_polling.payments != null
86. this._res_polling.links.merchant_return != null//
87. this._res_polling.payments != null
88. if (this._res_polling.payments != null
89. t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
90. this.type.toString().length != 0) {
91. this._res.links != null
92. if (count != 1) {
93. if (this._res.merchant != null
94. this._res.merchant_invoice_reference != null) {
95. if (this._res.merchant.address_details != null) {
96. this._res.links != null//
97. } else if (this._res.payments != null
98. this._res.payments[this._res.payments.length - 1].instrument != null
99. this._res.payments[this._res.payments.length - 1].instrument.issuer != null
100. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
101. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
102. this._res.payments[this._res.payments.length - 1].links != null
103. this._res.payments[this._res.payments.length - 1].links.cancel != null
104. this._res.payments[this._res.payments.length - 1].links.cancel.href != null
105. this._res.payments[this._res.payments.length - 1].links.update != null
106. this._res.payments[this._res.payments.length - 1].links.update.href != null) {
107. this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
108. this._res.payments[this._res.payments.length - 1].authorization != null
109. this._res.payments[this._res.payments.length - 1].authorization.links != null) {
110. this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
111. this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
112. if (this._res.payments[this._res.payments.length - 1].authorization != null
113. this._res.payments[this._res.payments.length - 1].authorization.links != null
114. auth = paramUserName != null ? paramUserName : ''
115. this._res.links.merchant_return != null //
116. } else if (this._res.merchant != null
117. this._res.merchant_invoice_reference != null
118. if (['mastercard', 'visa', 'amex', 'jcb', 'cup', 'card', 'np_card', 'atm'].indexOf(id) != -1) {
119. } else if (['techcombank_account', 'vib_account', 'dongabank_account', 'tpbank_account', 'bidv_account', 'pvcombank_account', 'shb_account', 'oceanbank_online_account'].indexOf(id) != -1) {
120. } else if (['oceanbank_mobile_account'].indexOf(id) != -1) {
121. } else if (['shb_customer_id'].indexOf(id) != -1) {
122. if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1) {
123. e.type != 'ewallet') || (regex.test(strTest)
124. } else if (this._res.payments != null) {
125. 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {
126. if (appcode != null) {
127. if (_re.status != '200'
128. _re.status != '201')
129. qr_version2 != 'None'"
130. qr_version2 != 'None'
131. if (this.translate.currentLang != language) {
132. item['feeService']['fee'] != 0
133. item['feeService']['fee'] != 0"
134. this._res.merchant.id != 'OPTEST' ? this._res.on_off_bank : ''
135. if (_val.value != null
136. if (_re.body.authorization != null
137. _re.body.authorization.links != null
138. if (_re.body.links != null
139. _re.body.links.cancel != null) {
140. if (_re.body.return_url != null) {
141. } else if (_re.body.links != null
142. _re.body.links.merchant_return != null
143. _re.body.links.merchant_return.href != null) {
144. return csc != null
145. || (brand_id != 'amex'
146. return this._i_name != ''
147. if (idInvoice != null
148. idInvoice != 0)
149. idInvoice != 0) {
150. if (this._merchantid != null
151. this._tranref != null
152. this._state != null
153. let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
154. urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
155. if (paymentId != null) {
156. if (fee != null) {
157. if (res?.status != 200
158. res?.status != 201) return;
159. _re.status != '201') {
160. latestPayment?.state != "authorization_required") {
161. if (tem != null) {
162. if ((tem = ua.match(/version\/(\d+)/i)) != null) {
163. if (v.length != 3) {
164. if (_modules[r][6] != null) {
165. if (_modules[6][c] != null) {
166. if (_modules[row][col] != null) {
167. while (buffer.getLengthInBits() % 8 != 0) {
168. if (count != numChars) {
169. throw count + ' != ' + numChars
170. while (data != 0) {
171. if (test.length != 2
172. ( (test[0] << 8) | test[1]) != code) {
173. if (_length % 3 != 0) {
174. if ( (data >>> length) != 0) {
175. return typeof _map[key] != 'undefined'

