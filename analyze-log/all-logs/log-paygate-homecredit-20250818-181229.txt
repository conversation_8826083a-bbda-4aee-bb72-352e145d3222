====================================================================================================
TRÍCH XUẤT ĐIỀU KIỆN SO SÁNH TỪ CÁC FILE TYPESCRIPT/JAVASCRIPT/HTML
====================================================================================================
Thư mục/File nguồn: /root/projects/onepay/paygate-homecredit/src
Thời gian: 18:12:29 18/8/2025
Tổng số file xử lý: 95
Tổng số file bị bỏ qua: 0
Tổng số điều kiện tìm thấy: 1218

THỐNG KÊ TỔNG QUAN THEO LOẠI TOÁN TỬ:
--------------------------------------------------
Strict equality (===): 207 lần
Loose equality (==): 682 lần
Strict inequality (!==): 40 lần
Loose inequality (!=): 289 lần
--------------------------------------------------

DANH SÁCH FILE ĐÃ XỬ LÝ:
--------------------------------------------------
1. 4en.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/4en.html
2. 4vn.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/4vn.html
3. app-routing.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/app-routing.module.ts
4. app.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/app.component.html
5. app.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/app.component.spec.ts
6. app.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/app.component.ts
7. app.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/app.module.ts
8. anbinhbank.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/anbinhbank/anbinhbank.component.html
9. anbinhbank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/anbinhbank/anbinhbank.component.spec.ts
10. anbinhbank.component.ts (26 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/anbinhbank/anbinhbank.component.ts
11. bankaccount.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/bankaccount/bankaccount.component.html
12. bankaccount.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/bankaccount/bankaccount.component.spec.ts
13. bankaccount.component.ts (154 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/bankaccount/bankaccount.component.ts
14. policy-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/dialog/policy-dialog/policy-dialog.component.html
15. policy-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/dialog/policy-dialog/policy-dialog.component.ts
16. bank.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/model/bank.ts
17. onepay-napas.component.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/onepay-napas/onepay-napas.component.html
18. onepay-napas.component.ts (103 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/onepay-napas/onepay-napas.component.ts
19. otp-auth.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/otp-auth/otp-auth.component.html
20. otp-auth.component.ts (21 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/otp-auth/otp-auth.component.ts
21. shb.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/shb/shb.component.html
22. shb.component.ts (68 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/shb/shb.component.ts
23. techcombank.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/techcombank/techcombank.component.html
24. techcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/techcombank/techcombank.component.spec.ts
25. techcombank.component.ts (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/techcombank/techcombank.component.ts
26. vibbank.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/vibbank/vibbank.component.html
27. vibbank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/vibbank/vibbank.component.spec.ts
28. vibbank.component.ts (102 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/vibbank/vibbank.component.ts
29. vietcombank.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/vietcombank/vietcombank.component.html
30. vietcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/vietcombank/vietcombank.component.spec.ts
31. vietcombank.component.ts (92 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/vietcombank/vietcombank.component.ts
32. counter.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/counter.directive.spec.ts
33. counter.directive.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/counter.directive.ts
34. csc.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/csc/csc.component.html
35. csc.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/csc/csc.component.spec.ts
36. csc.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/csc/csc.component.ts
37. format-carno-input.derective.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/directives/format-carno-input.derective.ts
38. uppercase-input.directive.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/directives/uppercase-input.directive.ts
39. domestic.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/domestic/domestic.component.html
40. domestic.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/domestic/domestic.component.spec.ts
41. domestic.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/domestic/domestic.component.ts
42. error.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/error/error.component.html
43. error.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/error/error.component.spec.ts
44. error.component.ts (18 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/error/error.component.ts
45. support-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/error/support-dialog/support-dialog.html
46. support-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/error/support-dialog/support-dialog.ts
47. format-date.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/format-date.directive.spec.ts
48. format-date.directive.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/format-date.directive.ts
49. main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/main/main.component.html
50. main.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/main/main.component.spec.ts
51. main.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/main/main.component.ts
52. mpay-bank.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/mpay-bank/mpay-bank.component.html
53. mpay-bank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/mpay-bank/mpay-bank.component.spec.ts
54. mpay-bank.component.ts (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/mpay-bank/mpay-bank.component.ts
55. overlay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/overlay/overlay.component.html
56. overlay.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/overlay/overlay.component.spec.ts
57. overlay.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/overlay/overlay.component.ts
58. payment.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/payment/payment.component.html
59. payment.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/payment/payment.component.spec.ts
60. payment.component.ts (179 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/payment/payment.component.ts
61. bank-amount.pipe.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/pipe/bank-amount.pipe.ts
62. bank-amount-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/select-bank/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
63. bank-amount-dialog.component.ts (34 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/select-bank/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
64. condition-dialog.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/select-bank/condition-dialog/condition-dialog.html
65. condition-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/select-bank/condition-dialog/condition-dialog.ts
66. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/select-bank/dialog/dialog-guide-dialog.html
67. off-bank-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/select-bank/dialog/off-bank-dialog/off-bank-dialog.html
68. off-bank-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/select-bank/dialog/off-bank-dialog/off-bank-dialog.ts
69. select-bank.component.html (10 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/select-bank/select-bank.component.html
70. select-bank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/select-bank/select-bank.component.spec.ts
71. select-bank.component.ts (211 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/select-bank/select-bank.component.ts
72. cancel.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/services/cancel.service.ts
73. data.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/services/data.service.ts
74. dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/services/dialog.service.ts
75. payment.service.ts (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/services/payment.service.ts
76. index.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/translate/index.ts
77. lang-en.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/translate/lang-en.ts
78. lang-vi.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/translate/lang-vi.ts
79. translate.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/translate/translate.pipe.ts
80. translate.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/translate/translate.service.ts
81. translations.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/translate/translations.ts
82. banks-fee.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/util/banks-fee.ts
83. banks-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/util/banks-info.ts
84. installment-utils.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/util/installment-utils.ts
85. util.ts (36 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/util/util.ts
86. qrcode.js (67 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/assets/script/qrcode.js
87. environment.dev.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/environments/environment.dev.ts
88. environment.mtf.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/environments/environment.mtf.ts
89. environment.prod.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/environments/environment.prod.ts
90. environment.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/environments/environment.ts
91. index.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/index.html
92. karma.conf.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/karma.conf.js
93. main.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/main.ts
94. polyfills.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/polyfills.ts
95. test.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-homecredit/src/test.ts

====================================================================================================
CHI TIẾT TỪNG FILE:
====================================================================================================

📁 FILE 1: 4en.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/4en.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 2: 4vn.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/4vn.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 3: app-routing.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/app-routing.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 4: app.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/app.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 5: app.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/app.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 6: app.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/app.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 76] return lang === this._translate.currentLang

== (2 điều kiện):
  1. [Dòng 57] 'vi'==params['locale']){
  2. [Dòng 59] 'en'==params['locale']){

!= (3 điều kiện):
  1. [Dòng 57] if(params['locale']!=null
  2. [Dòng 59] } else if(params['locale']!=null
  3. [Dòng 66] if(userLang!=null

================================================================================

📁 FILE 7: app.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/app.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 8: anbinhbank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/anbinhbank/anbinhbank.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 9: anbinhbank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/anbinhbank/anbinhbank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 10: anbinhbank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/anbinhbank/anbinhbank.component.ts
📊 Thống kê: 26 điều kiện duy nhất
   - === : 0 lần
   - == : 17 lần
   - !== : 0 lần
   - != : 9 lần
--------------------------------------------------------------------------------

== (17 điều kiện):
  1. [Dòng 45] if ((_val.value.length == 16
  2. [Dòng 45] _val.value.length == 19) && this.checkMod10(_val.value) == true) {
  3. [Dòng 45] this.checkMod10(_val.value) == true) {
  4. [Dòng 54] if ((_val.value.length == 4
  5. [Dòng 54] _val.value.search('/') == -1) || _val.value.length == 5) {
  6. [Dòng 54] _val.value.length == 5) {
  7. [Dòng 66] if (temp.length == 0) {
  8. [Dòng 73] return (counter % 10 == 0);
  9. [Dòng 141] if (_re.status == '200'
  10. [Dòng 141] _re.status == '201') {
  11. [Dòng 146] if (this._res.state == 'approved'
  12. [Dòng 146] this._res.state == 'failed') {
  13. [Dòng 150] } else if (this._res.state == 'authorization_required') {
  14. [Dòng 184] if (!((_formCard.card_number.length == 16
  15. [Dòng 184] _formCard.card_number.length == 19) && this.checkMod10(_formCard.card_number) == true && _formCard.card_number.startsWith('970425'))) {
  16. [Dòng 184] this.checkMod10(_formCard.card_number) == true
  17. [Dòng 190] if (expdate.length == 5) {

!= (9 điều kiện):
  1. [Dòng 34] if (params['locale'] != null) {
  2. [Dòng 84] if (this.current != 'card') {
  3. [Dòng 93] if (this.current != 'date') {
  4. [Dòng 102] if (this.current != 'name') {
  5. [Dòng 154] if (this._res.authorization != null
  6. [Dòng 154] this._res.authorization.links != null
  7. [Dòng 157] if (this._res.links != null
  8. [Dòng 157] this._res.links.cancel != null) {
  9. [Dòng 202] if (!(_formCard.name != null

================================================================================

📁 FILE 11: bankaccount.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/bankaccount/bankaccount.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 39] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number" class="form-control "
  2. [Dòng 55] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 55] valueDate.trim().length === 0)"

================================================================================

📁 FILE 12: bankaccount.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/bankaccount/bankaccount.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 13: bankaccount.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/bankaccount/bankaccount.component.ts
📊 Thống kê: 154 điều kiện duy nhất
   - === : 48 lần
   - == : 71 lần
   - !== : 12 lần
   - != : 23 lần
--------------------------------------------------------------------------------

=== (48 điều kiện):
  1. [Dòng 99] if (target.tagName === 'A'
  2. [Dòng 123] if (isIE[0] === 'MSIE'
  3. [Dòng 123] +isIE[1] === 10) {
  4. [Dòng 220] if ((_val.value.substr(-1) === ' '
  5. [Dòng 220] _val.value.length === 24) {
  6. [Dòng 230] if (this.cardTypeBank === 'bank_card_number') {
  7. [Dòng 235] } else if (this.cardTypeBank === 'bank_account_number') {
  8. [Dòng 241] } else if (this.cardTypeBank === 'bank_username') {
  9. [Dòng 245] } else if (this.cardTypeBank === 'bank_customer_code') {
  10. [Dòng 251] this.cardTypeBank === 'bank_card_number'
  11. [Dòng 265] if (this.cardTypeOcean === 'IB') {
  12. [Dòng 269] } else if (this.cardTypeOcean === 'MB') {
  13. [Dòng 270] if (_val.value.substr(0, 2) === '84') {
  14. [Dòng 277] } else if (this.cardTypeOcean === 'ATM') {
  15. [Dòng 304] if (this.cardTypeBank === 'bank_account_number') {
  16. [Dòng 323] this.cardTypeBank === 'bank_card_number') {
  17. [Dòng 345] if (this.cardTypeBank === 'bank_card_number'
  18. [Dòng 345] if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  19. [Dòng 672] if (event.keyCode === 8
  20. [Dòng 672] event.key === "Backspace"
  21. [Dòng 712] if (v.length === 2
  22. [Dòng 712] this.flag.length === 3
  23. [Dòng 712] this.flag.charAt(this.flag.length - 1) === '/') {
  24. [Dòng 716] if (v.length === 1) {
  25. [Dòng 718] } else if (v.length === 2) {
  26. [Dòng 721] v.length === 2) {
  27. [Dòng 729] if (len === 2) {
  28. [Dòng 1023] if ((this.cardTypeBank === 'bank_account_number'
  29. [Dòng 1023] this.cardTypeBank === 'bank_username'
  30. [Dòng 1023] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  31. [Dòng 1028] this.cardTypeOcean === 'ATM')
  32. [Dòng 1029] || (this.cardTypeOcean === 'IB'
  33. [Dòng 1085] if (valIn === this._translate.instant('bank_card_number')) {
  34. [Dòng 1113] } else if (valIn === this._translate.instant('bank_account_number')) {
  35. [Dòng 1135] } else if (valIn === this._translate.instant('bank_username')) {
  36. [Dòng 1154] } else if (valIn === this._translate.instant('bank_customer_code')) {
  37. [Dòng 1234] if (_val.value === ''
  38. [Dòng 1234] _val.value === null
  39. [Dòng 1234] _val.value === undefined) {
  40. [Dòng 1259] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  41. [Dòng 1259] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  42. [Dòng 1266] this.cardTypeOcean === 'MB') {
  43. [Dòng 1274] this.cardTypeOcean === 'IB'
  44. [Dòng 1280] if ((this.cardTypeBank === 'bank_card_number'
  45. [Dòng 1312] if (this.cardName === undefined
  46. [Dòng 1312] this.cardName === '') {
  47. [Dòng 1320] if (this.valueDate === undefined
  48. [Dòng 1320] this.valueDate === '') {

== (71 điều kiện):
  1. [Dòng 137] if (this._b == 18
  2. [Dòng 137] this._b == 19) {
  3. [Dòng 140] if (this._b == 19) {//19BIDV
  4. [Dòng 148] } else if (this._b == 3
  5. [Dòng 148] this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  6. [Dòng 153] if (this._b == 27) {
  7. [Dòng 158] } else if (this._b == 12) {// 12SHB
  8. [Dòng 163] } else if (this._b == 18) { //18Oceanbank-ocb
  9. [Dòng 229] if (this._b == 19
  10. [Dòng 229] this._b == 3
  11. [Dòng 229] this._b == 27
  12. [Dòng 229] this._b == 12) {
  13. [Dòng 264] } else if (this._b == 18) {
  14. [Dòng 295] if (this.checkBin(_val.value) && (this._b == 3
  15. [Dòng 295] this._b == 27)) {
  16. [Dòng 300] if (this._b == 3) {
  17. [Dòng 312] this.cardTypeOcean == 'ATM') {
  18. [Dòng 325] if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  19. [Dòng 345] this._b == 18)) {
  20. [Dòng 420] if (this.checkBin(v) && (this._b == 3
  21. [Dòng 672] event.inputType == 'deleteContentBackward') {
  22. [Dòng 673] if (event.target.name == 'exp_date'
  23. [Dòng 681] event.inputType == 'insertCompositionText') {
  24. [Dòng 696] if (((this.valueDate.length == 4
  25. [Dòng 696] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  26. [Dòng 696] this.valueDate.length == 5)
  27. [Dòng 781] if (temp.length == 0) {
  28. [Dòng 788] return (counter % 10 == 0);
  29. [Dòng 808] } else if (this._b == 19) {
  30. [Dòng 810] } else if (this._b == 27) {
  31. [Dòng 815] if (this._b == 12) {
  32. [Dòng 817] if (this.cardTypeBank == 'bank_customer_code') {
  33. [Dòng 819] } else if (this.cardTypeBank == 'bank_account_number') {
  34. [Dòng 836] _formCard.exp_date.length == 5
  35. [Dòng 836] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
  36. [Dòng 836] this._b == 3)) {//27-pvcombank;3-TPB ;
  37. [Dòng 841] if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
  38. [Dòng 841] this._b == 19
  39. [Dòng 841] this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
  40. [Dòng 844] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  41. [Dòng 847] if (this.cardTypeOcean == 'IB') {
  42. [Dòng 849] } else if (this.cardTypeOcean == 'MB') {
  43. [Dòng 851] } else if (this.cardTypeOcean == 'ATM') {
  44. [Dòng 894] if (_re.status == '200'
  45. [Dòng 894] _re.status == '201') {
  46. [Dòng 899] if (this._res_post.state == 'approved'
  47. [Dòng 899] this._res_post.state == 'failed') {
  48. [Dòng 906] } else if (this._res_post.state == 'authorization_required') {
  49. [Dòng 924] if (this._b == 18) {
  50. [Dòng 929] if (this._b == 27
  51. [Dòng 929] this._b == 18) {
  52. [Dòng 1004] if (err.status == 400
  53. [Dòng 1004] err.error['name'] == 'INVALID_INPUT_BIN') {
  54. [Dòng 1043] if ((cardNo.length == 16
  55. [Dòng 1043] if ((cardNo.length == 16 || (cardNo.length == 19
  56. [Dòng 1044] && ((this._b == 18
  57. [Dòng 1044] cardNo.length == 19) || this._b != 18)
  58. [Dòng 1057] if (this._b == +e.id) {
  59. [Dòng 1073] if (valIn == 1) {
  60. [Dòng 1075] } else if (valIn == 2) {
  61. [Dòng 1099] this._b == 3) {
  62. [Dòng 1106] if (this._b == 19) {
  63. [Dòng 1173] if (cardType == this._translate.instant('internetbanking')
  64. [Dòng 1180] } else if (cardType == this._translate.instant('mobilebanking')
  65. [Dòng 1187] } else if (cardType == this._translate.instant('atm')
  66. [Dòng 1259] this._b == 18))) {
  67. [Dòng 1266] } else if (this._b == 18
  68. [Dòng 1291] this.c_expdate = !(((this.valueDate.length == 4
  69. [Dòng 1323] this.valueDate.length == 4
  70. [Dòng 1323] this.valueDate.search('/') == -1)
  71. [Dòng 1324] this.valueDate.length == 5))

!== (12 điều kiện):
  1. [Dòng 220] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 882] key !== '3') {
  3. [Dòng 953] codeResponse.toString() !== '0') {
  4. [Dòng 1023] cardNo.length !== 0) {
  5. [Dòng 1092] if (this.cardTypeBank !== 'bank_card_number') {
  6. [Dòng 1116] if (this.cardTypeBank !== 'bank_account_number') {
  7. [Dòng 1140] if (this.cardTypeBank !== 'bank_username') {
  8. [Dòng 1159] if (this.cardTypeBank !== 'bank_customer_code') {
  9. [Dòng 1173] this.lb_card_account !== this._translate.instant('ocb_account')) {
  10. [Dòng 1180] this.lb_card_account !== this._translate.instant('ocb_phone')) {
  11. [Dòng 1187] this.lb_card_account !== this._translate.instant('ocb_card')) {
  12. [Dòng 1280] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (23 điều kiện):
  1. [Dòng 168] } else if (this._b != 18) {
  2. [Dòng 174] if (this.htmlDesc != null
  3. [Dòng 195] if (this._res_post != null
  4. [Dòng 195] this._res_post.links != null
  5. [Dòng 195] this._res_post.links.merchant_return != null
  6. [Dòng 195] this._res_post.links.merchant_return.href != null) {
  7. [Dòng 217] if (ua.indexOf('safari') != -1
  8. [Dòng 227] if (_val.value != '') {
  9. [Dòng 313] this.auth_method != null) {
  10. [Dòng 674] if (this.valueDate.length != 3) {
  11. [Dòng 836] if (_formCard.exp_date != null
  12. [Dòng 841] if (this.cardName != null
  13. [Dòng 902] if (this._res_post.links != null
  14. [Dòng 910] if (this._res_post.authorization != null
  15. [Dòng 910] this._res_post.authorization.links != null
  16. [Dòng 910] this._res_post.authorization.links.approval != null) {
  17. [Dòng 917] this._res_post.links.cancel != null) {
  18. [Dòng 1043] this._b != 27
  19. [Dòng 1043] this._b != 12
  20. [Dòng 1043] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  21. [Dòng 1044] this._b != 18)
  22. [Dòng 1087] if (this._b != 18
  23. [Dòng 1087] this._b != 19) {

================================================================================

📁 FILE 14: policy-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/dialog/policy-dialog/policy-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 15: policy-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/dialog/policy-dialog/policy-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 16: bank.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/model/bank.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 17: onepay-napas.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/onepay-napas/onepay-napas.component.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 3 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 26] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 41] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  3. [Dòng 41] _inExpDate.trim().length === 0)"

== (3 điều kiện):
  1. [Dòng 23] <div [ngStyle]="{'margin-top': !(checkTwoEnabled && !isOffTechcombankNapas && !isOffTechcombank)  ? '15px' : '0px' }" *ngIf="(cardTypeBank == 'bank_card_number') &&(_b == 67
  2. [Dòng 65] (cardTypeBank == 'bank_card_number') &&(_b == 67 || checkTwoEnabled)&& ready===1 && !isOffTechcombankNapas
  3. [Dòng 90] <div [ngStyle]="{'margin-top': !(checkTwoEnabled && !isOffTechcombankNapas && !isOffTechcombank)  ? '15px' : '0px' }" class="nd-bank-card-two" *ngIf="(cardTypeBank == 'internet_banking')&&(_b == 2

================================================================================

📁 FILE 18: onepay-napas.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/onepay-napas/onepay-napas.component.ts
📊 Thống kê: 103 điều kiện duy nhất
   - === : 21 lần
   - == : 47 lần
   - !== : 4 lần
   - != : 31 lần
--------------------------------------------------------------------------------

=== (21 điều kiện):
  1. [Dòng 100] if (target.tagName === 'A'
  2. [Dòng 127] if (isIE[0] === 'MSIE'
  3. [Dòng 127] +isIE[1] === 10) {
  4. [Dòng 151] if (this.timeLeft === 10) {
  5. [Dòng 155] if (this.runTime === true) {
  6. [Dòng 161] if (this.timeLeft === 0) {
  7. [Dòng 163] if (this.runTime === true) this.submitCardBanking();
  8. [Dòng 342] if (event.keyCode === 8
  9. [Dòng 342] event.key === "Backspace"
  10. [Dòng 554] if (approval.method === 'REDIRECT') {
  11. [Dòng 557] } else if (approval.method === 'POST_REDIRECT') {
  12. [Dòng 636] if (valIn === this._translate.instant('bank_card_number')) {
  13. [Dòng 638] if (this.timeLeft === 1) {
  14. [Dòng 655] } else if (valIn === this._translate.instant('internet_banking')) {
  15. [Dòng 733] if (_val.value === ''
  16. [Dòng 733] _val.value === null
  17. [Dòng 733] _val.value === undefined) {
  18. [Dòng 744] if (_val.value && (this.cardTypeBank === 'bank_card_number')) {
  19. [Dòng 754] if ((this.cardTypeBank === 'bank_card_number'
  20. [Dòng 792] if (this.cardName === undefined
  21. [Dòng 792] this.cardName === '') {

== (47 điều kiện):
  1. [Dòng 143] if (this._b == 67
  2. [Dòng 143] this._b == 2) {//19BIDV
  3. [Dòng 149] if((this._b == 2
  4. [Dòng 149] !this.checkTwoEnabled) || (this._b == 2
  5. [Dòng 172] } else if ((this._b == 2
  6. [Dòng 172] this._b == 67) {
  7. [Dòng 208] if (_re.status == '200'
  8. [Dòng 208] _re.status == '201') {
  9. [Dòng 213] if (this._res_post.state == 'approved'
  10. [Dòng 213] this._res_post.state == 'failed') {
  11. [Dòng 217] } else if (this._res_post.state == 'authorization_required') {
  12. [Dòng 327] return this._b == 2
  13. [Dòng 327] this._b == 67
  14. [Dòng 342] event.inputType == 'deleteContentBackward') {
  15. [Dòng 343] if (event.target.name == 'exp_date'
  16. [Dòng 351] event.inputType == 'insertCompositionText') {
  17. [Dòng 417] if (temp.length == 0) {
  18. [Dòng 424] return (counter % 10 == 0);
  19. [Dòng 440] _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
  20. [Dòng 585] if (err.status == 400
  21. [Dòng 585] err.error['name'] == 'INVALID_INPUT_BIN') {
  22. [Dòng 599] if ((cardNo.length == 16
  23. [Dòng 599] if ((cardNo.length == 16 || (cardNo.length == 19
  24. [Dòng 600] this.checkMod10(cardNo) == true
  25. [Dòng 613] if (this._b == +e.id) {
  26. [Dòng 689] if (this._b == 19) {
  27. [Dòng 693] if (this._b == 27
  28. [Dòng 693] this._b == 3) {
  29. [Dòng 766] if (this._b != 68 || (this._b == 68
  30. [Dòng 775] return ((value.length == 4
  31. [Dòng 775] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  32. [Dòng 775] value.length == 5) && parseInt(value.split('/')[0]
  33. [Dòng 779] || (this._util.checkValidExpireMonthYear(value) && (this._b == 2
  34. [Dòng 779] this._b == 20
  35. [Dòng 779] this._b == 33
  36. [Dòng 780] this._b == 39
  37. [Dòng 780] this._b == 43
  38. [Dòng 780] this._b == 45
  39. [Dòng 780] this._b == 64
  40. [Dòng 780] this._b == 68
  41. [Dòng 780] this._b == 72))) //sonnh them Vietbank 72
  42. [Dòng 801] this._inExpDate.length == 4
  43. [Dòng 801] this._inExpDate.search('/') == -1)
  44. [Dòng 802] this._inExpDate.length == 5))
  45. [Dòng 804] || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 20
  46. [Dòng 804] this._b == 2
  47. [Dòng 804] this._b == 72)));

!== (4 điều kiện):
  1. [Dòng 232] codeResponse.toString() !== '0') {
  2. [Dòng 597] let _b = this._b !== 67 ? 67 : this._b
  3. [Dòng 682] if (this.cardTypeBank !== 'internet_banking') {
  4. [Dòng 754] this._b !== 18)) {

!= (31 điều kiện):
  1. [Dòng 168] if (this.htmlDesc != null
  2. [Dòng 221] if (this._res_post.authorization != null
  3. [Dòng 221] this._res_post.authorization.links != null
  4. [Dòng 221] this._res_post.authorization.links.approval != null) {
  5. [Dòng 279] if (ua.indexOf('safari') != -1
  6. [Dòng 344] if (this._inExpDate.length != 3) {
  7. [Dòng 440] if (_formCard.exp_date != null
  8. [Dòng 445] if (this.cardName != null
  9. [Dòng 482] if (this._res_post.return_url != null) {
  10. [Dòng 485] if (this._res_post.links != null
  11. [Dòng 485] this._res_post.links.merchant_return != null
  12. [Dòng 485] this._res_post.links.merchant_return.href != null) {
  13. [Dòng 539] this._res_post.links.cancel != null) {
  14. [Dòng 544] let userName = _formCard.name != null ? _formCard.name : ''
  15. [Dòng 545] this._res_post.authorization.links.approval != null
  16. [Dòng 545] this._res_post.authorization.links.approval.href != null) {
  17. [Dòng 548] userName = paramUserName != null ? paramUserName : ''
  18. [Dòng 599] this._b != 27
  19. [Dòng 599] this._b != 12
  20. [Dòng 599] this._b != 3))
  21. [Dòng 766] if (this._b != 68
  22. [Dòng 777] this._b != 2
  23. [Dòng 777] this._b != 20
  24. [Dòng 777] this._b != 33
  25. [Dòng 777] this._b != 39
  26. [Dòng 778] this._b != 43
  27. [Dòng 778] this._b != 45
  28. [Dòng 778] this._b != 64
  29. [Dòng 778] this._b != 67
  30. [Dòng 778] this._b != 68
  31. [Dòng 778] this._b != 72)

================================================================================

📁 FILE 19: otp-auth.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/otp-auth/otp-auth.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 20: otp-auth.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/otp-auth/otp-auth.component.ts
📊 Thống kê: 21 điều kiện duy nhất
   - === : 0 lần
   - == : 10 lần
   - !== : 1 lần
   - != : 10 lần
--------------------------------------------------------------------------------

== (10 điều kiện):
  1. [Dòng 77] if (this._b == 8) {//MB Bank
  2. [Dòng 81] if (this._b == 18) {//Oceanbank
  3. [Dòng 108] if (this._b == 8) {
  4. [Dòng 113] if (this._b == 18) {
  5. [Dòng 118] if (this._b == 12) { //SHB
  6. [Dòng 138] if (_re.status == '200'
  7. [Dòng 138] _re.status == '201') {
  8. [Dòng 147] } else if (this._res.state == 'authorization_required') {
  9. [Dòng 199] if (this._b == 12) {
  10. [Dòng 248] if (this._b == 18) {//8-MB Bank;18-oceanbank

!== (1 điều kiện):
  1. [Dòng 153] codeResponse.toString() !== '0') {

!= (10 điều kiện):
  1. [Dòng 143] if (this._res.links != null
  2. [Dòng 143] this._res.links.merchant_return != null
  3. [Dòng 143] this._res.links.merchant_return.href != null) {
  4. [Dòng 220] if (this._res_post != null
  5. [Dòng 220] this._res_post.links != null
  6. [Dòng 220] this._res_post.links.merchant_return != null
  7. [Dòng 220] this._res_post.links.merchant_return.href != null) {
  8. [Dòng 243] if (!(_formCard.otp != null
  9. [Dòng 249] if (!(_formCard.password != null
  10. [Dòng 265] if (ua.indexOf('safari') != -1

================================================================================

📁 FILE 21: shb.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/shb/shb.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 25] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 42] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  3. [Dòng 42] _inExpDate.trim().length === 0)"

== (2 điều kiện):
  1. [Dòng 22] <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_card_number')">
  2. [Dòng 66] <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_account_number')">

================================================================================

📁 FILE 22: shb.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/shb/shb.component.ts
📊 Thống kê: 68 điều kiện duy nhất
   - === : 18 lần
   - == : 34 lần
   - !== : 5 lần
   - != : 11 lần
--------------------------------------------------------------------------------

=== (18 điều kiện):
  1. [Dòng 94] if (target.tagName === 'A'
  2. [Dòng 118] if (isIE[0] === 'MSIE'
  3. [Dòng 118] +isIE[1] === 10) {
  4. [Dòng 160] if (focusElement === 'card_name') {
  5. [Dòng 162] } else if (focusElement === 'exp_date'
  6. [Dòng 183] focusExpDateElement === 'card_name') {
  7. [Dòng 400] if (this.cardTypeBank === 'bank_account_number'
  8. [Dòng 445] if (valIn === this._translate.instant('bank_card_number')) {
  9. [Dòng 451] } else if (valIn === this._translate.instant('bank_account_number')) {
  10. [Dòng 493] if (_val.value === ''
  11. [Dòng 493] _val.value === null
  12. [Dòng 493] _val.value === undefined) {
  13. [Dòng 504] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  14. [Dòng 504] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  15. [Dòng 511] this.cardTypeOcean === 'MB') {
  16. [Dòng 519] this.cardTypeOcean === 'IB'
  17. [Dòng 525] if ((this.cardTypeBank === 'bank_card_number'
  18. [Dòng 548] if (this.cardTypeOcean === 'IB') {

== (34 điều kiện):
  1. [Dòng 127] if(this._b == 12) this.isShbGroup = true;
  2. [Dòng 148] return this._b == 9
  3. [Dòng 148] this._b == 11
  4. [Dòng 148] this._b == 16
  5. [Dòng 148] this._b == 17
  6. [Dòng 148] this._b == 25
  7. [Dòng 148] this._b == 44
  8. [Dòng 149] this._b == 57
  9. [Dòng 149] this._b == 59
  10. [Dòng 149] this._b == 61
  11. [Dòng 149] this._b == 63
  12. [Dòng 149] this._b == 69
  13. [Dòng 235] if (this._b == 12
  14. [Dòng 235] this.cardTypeBank == 'bank_account_number') {
  15. [Dòng 246] _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
  16. [Dòng 292] if (_re.status == '200'
  17. [Dòng 292] _re.status == '201') {
  18. [Dòng 296] if (this._res_post.state == 'approved'
  19. [Dòng 296] this._res_post.state == 'failed') {
  20. [Dòng 302] } else if (this._res_post.state == 'authorization_required') {
  21. [Dòng 362] && (_re.body.payments[_re.body.payments.length - 1].reason.code == '25')) {
  22. [Dòng 387] if (err.status == 400
  23. [Dòng 387] err.error['name'] == 'INVALID_INPUT_BIN') {
  24. [Dòng 403] if ((cardNo.length == 16
  25. [Dòng 403] cardNo.length == 19) && cardNo.startsWith("970443") && this._util.checkMod10(cardNo) && this.checkBin(cardNo) ) {
  26. [Dòng 415] if (this._b == +e.id) {
  27. [Dòng 431] if (valIn == 1) {
  28. [Dòng 433] } else if (valIn == 2) {
  29. [Dòng 504] this._b == 18))) {
  30. [Dòng 511] } else if (this._b == 18
  31. [Dòng 525] this._b == 18)) {
  32. [Dòng 537] this.c_expdate = !(((this.valueDate.length == 4
  33. [Dòng 537] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  34. [Dòng 537] this.valueDate.length == 5)

!== (5 điều kiện):
  1. [Dòng 280] key !== '3') {
  2. [Dòng 322] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  3. [Dòng 340] codeResponse.toString() !== '0') {
  4. [Dòng 400] cardNo.length !== 0) {
  5. [Dòng 525] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (11 điều kiện):
  1. [Dòng 135] if (this.htmlDesc != null
  2. [Dòng 196] if (ua.indexOf('safari') != -1
  3. [Dòng 246] if (_formCard.exp_date != null
  4. [Dòng 251] if (this.cardName != null
  5. [Dòng 299] if (this._res_post.links != null
  6. [Dòng 299] this._res_post.links.merchant_return != null
  7. [Dòng 299] this._res_post.links.merchant_return.href != null) {
  8. [Dòng 305] if (this._res_post.authorization != null
  9. [Dòng 305] this._res_post.authorization.links != null
  10. [Dòng 305] this._res_post.authorization.links.approval != null) {
  11. [Dòng 312] this._res_post.links.cancel != null) {

================================================================================

📁 FILE 23: techcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/techcombank/techcombank.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 24: techcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/techcombank/techcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 25: techcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/techcombank/techcombank.component.ts
📊 Thống kê: 15 điều kiện duy nhất
   - === : 1 lần
   - == : 11 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 64] if (this.timeLeft === 0) {

== (11 điều kiện):
  1. [Dòng 55] if (this._b == 2
  2. [Dòng 55] this._b == 5
  3. [Dòng 55] this._b == 31) {
  4. [Dòng 84] if (this._b == 2) {
  5. [Dòng 86] } else if (this._b == 6) {
  6. [Dòng 88] } else if (this._b == 31) {
  7. [Dòng 117] if (_re.status == '200'
  8. [Dòng 117] _re.status == '201') {
  9. [Dòng 122] if (this._res_post.state == 'approved'
  10. [Dòng 122] this._res_post.state == 'failed') {
  11. [Dòng 126] } else if (this._res_post.state == 'authorization_required') {

!= (3 điều kiện):
  1. [Dòng 130] if (this._res_post.authorization != null
  2. [Dòng 130] this._res_post.authorization.links != null
  3. [Dòng 130] this._res_post.authorization.links.approval != null) {

================================================================================

📁 FILE 26: vibbank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/vibbank/vibbank.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 47] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number" class="form-control "
  2. [Dòng 63] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 63] valueDate.trim().length === 0)"

== (2 điều kiện):
  1. [Dòng 1] transactionFlow=='internet_banking'"
  2. [Dòng 23] transactionFlow=='bank_account'"

================================================================================

📁 FILE 27: vibbank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/vibbank/vibbank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 28: vibbank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/vibbank/vibbank.component.ts
📊 Thống kê: 102 điều kiện duy nhất
   - === : 30 lần
   - == : 43 lần
   - !== : 10 lần
   - != : 19 lần
--------------------------------------------------------------------------------

=== (30 điều kiện):
  1. [Dòng 107] if (target.tagName === 'A'
  2. [Dòng 131] if (isIE[0] === 'MSIE'
  3. [Dòng 131] +isIE[1] === 10) {
  4. [Dòng 167] if (this.timeLeft === 0) {
  5. [Dòng 220] if ((_val.value.substr(-1) === ' '
  6. [Dòng 220] _val.value.length === 24) {
  7. [Dòng 230] if (this.cardTypeBank === 'bank_card_number') {
  8. [Dòng 235] } else if (this.cardTypeBank === 'bank_account_number') {
  9. [Dòng 243] this.cardTypeBank === 'bank_card_number'
  10. [Dòng 270] if (this.cardTypeBank === 'bank_account_number') {
  11. [Dòng 279] this.cardTypeBank === 'bank_card_number') {
  12. [Dòng 518] if (event.keyCode === 8
  13. [Dòng 518] event.key === "Backspace"
  14. [Dòng 631] this.cardTypeBank === 'bank_card_number')) {
  15. [Dòng 862] if ((this.cardTypeBank === 'bank_account_number') && cardNo.length !== 0) {
  16. [Dòng 907] if (valIn === this._translate.instant('bank_card_number')) {
  17. [Dòng 925] } else if (valIn === this._translate.instant('bank_account_number')) {
  18. [Dòng 1012] if (_val.value === ''
  19. [Dòng 1012] _val.value === null
  20. [Dòng 1012] _val.value === undefined) {
  21. [Dòng 1037] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  22. [Dòng 1037] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  23. [Dòng 1044] this.cardTypeOcean === 'MB') {
  24. [Dòng 1052] this.cardTypeOcean === 'IB'
  25. [Dòng 1058] if ((this.cardTypeBank === 'bank_card_number'
  26. [Dòng 1079] if (this.cardTypeOcean === 'IB') {
  27. [Dòng 1090] if (this.cardName === undefined
  28. [Dòng 1090] this.cardName === '') {
  29. [Dòng 1098] if (this.valueDate === undefined
  30. [Dòng 1098] this.valueDate === '') {

== (43 điều kiện):
  1. [Dòng 148] if (this._b == 5) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  2. [Dòng 161] if (this.transactionFlow=='internet_banking') {
  3. [Dòng 229] if (this._b ==5) {
  4. [Dòng 243] if (this._b == 5
  5. [Dòng 267] if (this.checkBin(_val.value) && (this._b == 5)) {
  6. [Dòng 281] if (this._b == 5) {//** check đúng số thẻ 5-vib
  7. [Dòng 315] if (this._b == 5) {
  8. [Dòng 346] if (this.checkBin(v) && (this._b == 5)) {
  9. [Dòng 518] event.inputType == 'deleteContentBackward') {
  10. [Dòng 519] if (event.target.name == 'exp_date'
  11. [Dòng 527] event.inputType == 'insertCompositionText') {
  12. [Dòng 542] if (((this.valueDate.length == 4
  13. [Dòng 542] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  14. [Dòng 542] this.valueDate.length == 5)
  15. [Dòng 591] if (temp.length == 0) {
  16. [Dòng 598] return (counter % 10 == 0);
  17. [Dòng 631] _formCard.exp_date.length == 5
  18. [Dòng 631] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 5
  19. [Dòng 636] if (this.cardName != null && this.cardName.length > 0 && (this._b == 5)) {
  20. [Dòng 678] if (_re.status == '200'
  21. [Dòng 678] _re.status == '201') {
  22. [Dòng 683] if (this._res_post.state == 'approved'
  23. [Dòng 683] this._res_post.state == 'failed') {
  24. [Dòng 690] } else if (this._res_post.state == 'authorization_required') {
  25. [Dòng 782] if (err.status == 400
  26. [Dòng 782] err.error['name'] == 'INVALID_INPUT_BIN') {
  27. [Dòng 866] if ((cardNo.length == 16
  28. [Dòng 866] if ((cardNo.length == 16 || (cardNo.length == 19
  29. [Dòng 867] && ((this._b == 18
  30. [Dòng 867] cardNo.length == 19) || this._b != 18)
  31. [Dòng 880] if (this._b == +e.id) {
  32. [Dòng 896] if (valIn == 1) {
  33. [Dòng 898] } else if (valIn == 2) {
  34. [Dòng 950] if (cardType == this._translate.instant('internetbanking')
  35. [Dòng 957] } else if (cardType == this._translate.instant('mobilebanking')
  36. [Dòng 964] } else if (cardType == this._translate.instant('atm')
  37. [Dòng 1037] this._b == 18))) {
  38. [Dòng 1044] } else if (this._b == 18
  39. [Dòng 1058] this._b == 18)) {
  40. [Dòng 1069] this.c_expdate = !(((this.valueDate.length == 4
  41. [Dòng 1101] this.valueDate.length == 4
  42. [Dòng 1101] this.valueDate.search('/') == -1)
  43. [Dòng 1102] this.valueDate.length == 5))

!== (10 điều kiện):
  1. [Dòng 220] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 666] key !== '3') {
  3. [Dòng 732] codeResponse.toString() !== '0') {
  4. [Dòng 862] cardNo.length !== 0) {
  5. [Dòng 912] if (this.cardTypeBank !== 'bank_card_number') {
  6. [Dòng 928] if (this.cardTypeBank !== 'bank_account_number') {
  7. [Dòng 950] this.lb_card_account !== this._translate.instant('ocb_account')) {
  8. [Dòng 957] this.lb_card_account !== this._translate.instant('ocb_phone')) {
  9. [Dòng 964] this.lb_card_account !== this._translate.instant('ocb_card')) {
  10. [Dòng 1058] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (19 điều kiện):
  1. [Dòng 175] if (this.htmlDesc != null
  2. [Dòng 196] if (this._res_post != null
  3. [Dòng 196] this._res_post.links != null
  4. [Dòng 196] this._res_post.links.merchant_return != null
  5. [Dòng 196] this._res_post.links.merchant_return.href != null) {
  6. [Dòng 217] if (ua.indexOf('safari') != -1
  7. [Dòng 227] if (_val.value != '') {
  8. [Dòng 520] if (this.valueDate.length != 3) {
  9. [Dòng 631] if (_formCard.exp_date != null
  10. [Dòng 636] if (this.cardName != null
  11. [Dòng 686] if (this._res_post.links != null
  12. [Dòng 694] if (this._res_post.authorization != null
  13. [Dòng 694] this._res_post.authorization.links != null
  14. [Dòng 694] this._res_post.authorization.links.approval != null) {
  15. [Dòng 701] this._res_post.links.cancel != null) {
  16. [Dòng 866] this._b != 27
  17. [Dòng 866] this._b != 12
  18. [Dòng 866] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  19. [Dòng 867] this._b != 18)

================================================================================

📁 FILE 29: vietcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/vietcombank/vietcombank.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 23] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  2. [Dòng 23] _inExpDate.trim().length === 0)"

== (1 điều kiện):
  1. [Dòng 50] _b==68"

================================================================================

📁 FILE 30: vietcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/vietcombank/vietcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 31: vietcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/banks/vietcombank/vietcombank.component.ts
📊 Thống kê: 92 điều kiện duy nhất
   - === : 5 lần
   - == : 59 lần
   - !== : 1 lần
   - != : 27 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 72] if (target.tagName === 'A'
  2. [Dòng 224] if (event.keyCode === 8
  3. [Dòng 224] event.key === "Backspace"
  4. [Dòng 440] if (approval.method === 'REDIRECT') {
  5. [Dòng 443] } else if (approval.method === 'POST_REDIRECT') {

== (59 điều kiện):
  1. [Dòng 110] if (this._b == 1
  2. [Dòng 110] this._b == 20
  3. [Dòng 110] this._b == 36
  4. [Dòng 110] this._b == 64
  5. [Dòng 110] this._b == 55
  6. [Dòng 110] this._b == 47
  7. [Dòng 110] this._b == 48) {
  8. [Dòng 142] this._b == 9
  9. [Dòng 142] this._b == 16
  10. [Dòng 142] this._b == 17
  11. [Dòng 142] this._b == 25
  12. [Dòng 142] this._b == 44
  13. [Dòng 143] this._b == 57
  14. [Dòng 143] this._b == 59
  15. [Dòng 143] this._b == 61
  16. [Dòng 143] this._b == 63
  17. [Dòng 143] this._b == 69
  18. [Dòng 151] return this._b == 11
  19. [Dòng 151] this._b == 33
  20. [Dòng 151] this._b == 39
  21. [Dòng 151] this._b == 43
  22. [Dòng 151] this._b == 45
  23. [Dòng 152] this._b == 67
  24. [Dòng 152] this._b == 72
  25. [Dòng 152] this._b == 73
  26. [Dòng 152] this._b == 68
  27. [Dòng 152] this._b == 74
  28. [Dòng 152] this._b == 75
  29. [Dòng 224] event.inputType == 'deleteContentBackward') {
  30. [Dòng 225] if (event.target.name == 'exp_date'
  31. [Dòng 233] event.inputType == 'insertCompositionText') {
  32. [Dòng 294] return ((value.length == 4
  33. [Dòng 294] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  34. [Dòng 294] value.length == 5) && parseInt(value.split('/')[0]
  35. [Dòng 353] if (this._res_post.state == 'approved'
  36. [Dòng 353] this._res_post.state == 'failed') {
  37. [Dòng 409] } else if (this._res_post.state == 'authorization_required') {
  38. [Dòng 431] this._b == 14
  39. [Dòng 431] this._b == 15
  40. [Dòng 431] this._b == 24
  41. [Dòng 431] this._b == 8
  42. [Dòng 431] this._b == 10
  43. [Dòng 431] this._b == 22
  44. [Dòng 431] this._b == 23
  45. [Dòng 431] this._b == 30
  46. [Dòng 431] this._b == 11
  47. [Dòng 431] this._b == 9) {
  48. [Dòng 473] if (err.status == 400
  49. [Dòng 473] err.error['name'] == 'INVALID_INPUT_BIN') {
  50. [Dòng 488] if ((cardNo.length == 16
  51. [Dòng 489] (cardNo.length == 19
  52. [Dòng 489] (cardNo.length == 19 && (this._b == 1
  53. [Dòng 489] this._b == 4
  54. [Dòng 490] this._b == 48
  55. [Dòng 490] this._b == 59))
  56. [Dòng 492] this._util.checkMod10(cardNo) == true
  57. [Dòng 555] this._inExpDate.length == 4
  58. [Dòng 555] this._inExpDate.search('/') == -1)
  59. [Dòng 556] this._inExpDate.length == 5))

!== (1 điều kiện):
  1. [Dòng 367] codeResponse.toString() !== '0') {

!= (27 điều kiện):
  1. [Dòng 115] if (this.htmlDesc != null
  2. [Dòng 129] if (this._res_post != null
  3. [Dòng 129] this._res_post.links != null
  4. [Dòng 129] this._res_post.links.merchant_return != null
  5. [Dòng 129] this._res_post.links.merchant_return.href != null) {
  6. [Dòng 161] if (ua.indexOf('safari') != -1
  7. [Dòng 226] if (this._inExpDate.length != 3) {
  8. [Dòng 309] if ( this._b != 9
  9. [Dòng 309] this._b != 16
  10. [Dòng 309] this._b != 17
  11. [Dòng 309] this._b != 25
  12. [Dòng 309] this._b != 44
  13. [Dòng 310] this._b != 57
  14. [Dòng 310] this._b != 59
  15. [Dòng 310] this._b != 61
  16. [Dòng 310] this._b != 63
  17. [Dòng 310] this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
  18. [Dòng 323] '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72' , '73', '74', '75'].indexOf(this._b.toString()) != -1) {
  19. [Dòng 355] if (this._res_post.return_url != null) {
  20. [Dòng 358] if (this._res_post.links != null
  21. [Dòng 414] if (this._res_post.authorization != null
  22. [Dòng 414] this._res_post.authorization.links != null
  23. [Dòng 419] this._res_post.links.cancel != null) {
  24. [Dòng 425] let userName = _formCard.name != null ? _formCard.name : ''
  25. [Dòng 426] this._res_post.authorization.links.approval != null
  26. [Dòng 426] this._res_post.authorization.links.approval.href != null) {
  27. [Dòng 429] userName = paramUserName != null ? paramUserName : ''

================================================================================

📁 FILE 32: counter.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/counter.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 33: counter.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/counter.directive.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 34: csc.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/csc/csc.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 35: csc.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/csc/csc.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 36: csc.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/csc/csc.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 37: format-carno-input.derective.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/directives/format-carno-input.derective.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 44] this.lastValue !== $event.target.value)) {

!= (1 điều kiện):
  1. [Dòng 23] if(value!=null){

================================================================================

📁 FILE 38: uppercase-input.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/directives/uppercase-input.directive.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 0 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 23] this.lastValue !== $event.target.value)) {

================================================================================

📁 FILE 39: domestic.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/domestic/domestic.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 40: domestic.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/domestic/domestic.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 41: domestic.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/domestic/domestic.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 42: error.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/error/error.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 1 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 55] isPopupSupport === 'True'"

== (3 điều kiện):
  1. [Dòng 11] errorCode == '11'"
  2. [Dòng 55] isSent == false
  3. [Dòng 68] timeLeft == 'Infinity'"

!= (1 điều kiện):
  1. [Dòng 60] timeLeft != 'Infinity'"

================================================================================

📁 FILE 43: error.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/error/error.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 44: error.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/error/error.component.ts
📊 Thống kê: 18 điều kiện duy nhất
   - === : 7 lần
   - == : 7 lần
   - !== : 0 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 87] params.timeout === 'true') {
  2. [Dòng 102] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  3. [Dòng 102] _re.body.state === 'unpaid');
  4. [Dòng 167] if (this.errorCode === 'overtime'
  5. [Dòng 167] this.errorCode === '253') {
  6. [Dòng 196] if (this.errorCode === 'overtime') {
  7. [Dòng 256] if (this.timeLeft === 0) {

== (7 điều kiện):
  1. [Dòng 110] if (_re.body.currencies[0] == 'USD') {
  2. [Dòng 144] _re.body.themes.theme == 'homecredit'){
  3. [Dòng 171] if (_re.status == '200'
  4. [Dòng 171] _re.status == '201') {
  5. [Dòng 184] if (_re2.status == '200'
  6. [Dòng 184] _re2.status == '201') {
  7. [Dòng 254] if(this.isTimePause == false) {

!= (4 điều kiện):
  1. [Dòng 302] if (_re.body != null
  2. [Dòng 302] _re.body.links != null
  3. [Dòng 302] _re.body.links.merchant_return != null
  4. [Dòng 303] _re.body.links.merchant_return.href != null) {

================================================================================

📁 FILE 45: support-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/error/support-dialog/support-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 46: support-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/error/support-dialog/support-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 47: format-date.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/format-date.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 48: format-date.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/format-date.directive.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0
  2. [Dòng 123] YY % 4 === 0)) {
  3. [Dòng 138] if (YYYY % 400 === 0
  4. [Dòng 138] YYYY % 4 === 0)) {

!== (2 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0 || (YY % 100 !== 0
  2. [Dòng 138] if (YYYY % 400 === 0 || (YYYY % 100 !== 0

================================================================================

📁 FILE 49: main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/main/main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 50: main.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/main/main.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 51: main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/main/main.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 4 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 55] if ((dataPassed.status == '200'
  2. [Dòng 55] dataPassed.status == '201') && dataPassed.body != null) {

!= (4 điều kiện):
  1. [Dòng 46] if (this._idInvoice != null
  2. [Dòng 46] this._idInvoice != 0) {
  3. [Dòng 47] if (this._paymentService.getInvoiceDetail() != null) {
  4. [Dòng 55] dataPassed.body != null) {

================================================================================

📁 FILE 52: mpay-bank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/mpay-bank/mpay-bank.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 53: mpay-bank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/mpay-bank/mpay-bank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 54: mpay-bank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/mpay-bank/mpay-bank.component.ts
📊 Thống kê: 4 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 28] (this._cookie.get('d_mpayvn')+"")=="true"
  2. [Dòng 29] (this._cookie.get('d_bidvpayplus')+"")=="true"
  3. [Dòng 30] (this._cookie.get('d_myvib')+"")=="true"
  4. [Dòng 31] (this._cookie.get('d_momo')+"")=="true"

================================================================================

📁 FILE 55: overlay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/overlay/overlay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 56: overlay.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/overlay/overlay.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 57: overlay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/overlay/overlay.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 58: payment.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/payment/payment.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 178] _auth==0&&(_b==1||_b==4||_b==7||_b==8||_b==9||_b==10||_b==11||_b==15||_b==16||_b==17||_b==20||_b==22||_b==23||_b==24||_b==25||_b==30)
  2. [Dòng 218] _auth==0&&(_b==2||_b==5||_b==6)
  3. [Dòng 221] _auth==0&&(_b==3||_b==12||_b==14||_b==18||_b==19||_b==27)
  4. [Dòng 224] _auth==1"

!= (1 điều kiện):
  1. [Dòng 147] <img src="assets/img/banklogo/{{cbtest.value!=null?cbtest.value:combobankValue}}_logo.png" style="height:25px;margin-right: 10px;"/>{{cbtest.value!=null?getBankName(cbtest.value):getBankName(combobankValue)}}

================================================================================

📁 FILE 59: payment.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/payment/payment.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 60: payment.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/payment/payment.component.ts
📊 Thống kê: 179 điều kiện duy nhất
   - === : 8 lần
   - == : 105 lần
   - !== : 0 lần
   - != : 66 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 702] if (valid.indexOf(temp) === -1) bNum = false;
  2. [Dòng 1152] if (v.length === 2
  3. [Dòng 1152] this.flag.length === 3
  4. [Dòng 1152] this.flag.charAt(this.flag.length - 1) === '/') {
  5. [Dòng 1156] if (v.length === 1) {
  6. [Dòng 1158] } else if (v.length === 2) {
  7. [Dòng 1161] v.length === 2) {
  8. [Dòng 1169] if (len === 2) {

== (105 điều kiện):
  1. [Dòng 137] if (bankId == 24) return true;
  2. [Dòng 170] if (!isNaN(_re.status) && (_re.status == '200'
  3. [Dòng 170] _re.status == '201') && _re.body != null) {
  4. [Dòng 175] if (('closed' == this._res_polling.state
  5. [Dòng 175] 'canceled' == this._res_polling.state
  6. [Dòng 175] 'expired' == this._res_polling.state
  7. [Dòng 175] 'paid' == this._res_polling.state)
  8. [Dòng 183] this._res_polling.payments == null) {
  9. [Dòng 189] this._res_polling.payments[0].state == 'pending') {
  10. [Dòng 195] this._paymentService.getCurrentPage() == 'enter_card') {
  11. [Dòng 202] } else if (this._res_polling.payments != null && (this._res_polling.payments[0].state == 'approved'
  12. [Dòng 202] this._res_polling.payments[0].state == 'failed')
  13. [Dòng 288] if (count == null
  14. [Dòng 288] count == '') {
  15. [Dòng 314] if (_re.status == '200'
  16. [Dòng 314] _re.status == '201') {
  17. [Dòng 391] if (('closed' == this._res.state
  18. [Dòng 391] 'canceled' == this._res.state
  19. [Dòng 391] 'expired' == this._res.state
  20. [Dòng 391] 'paid' == this._res.state)
  21. [Dòng 398] this._res.payments == null) {
  22. [Dòng 402] this._res.payments[0].state == 'pending') {
  23. [Dòng 410] } else if (this._res.payments != null && (this._res.payments[0].state == 'approved'
  24. [Dòng 410] this._res.payments[0].state == 'failed')
  25. [Dòng 429] if (this._res.currencies[0] == 'USD') numfix = 2;
  26. [Dòng 517] if (this._res_post.state == 'approved'
  27. [Dòng 517] this._res_post.state == 'failed') {
  28. [Dòng 521] } else if (this._res_post.state == 'authorization_required') {
  29. [Dòng 581] if (x == 1) {
  30. [Dòng 585] else if (x == 2) {
  31. [Dòng 589] else if (x == 3) {
  32. [Dòng 615] v.length == 15) || (v.length == 16
  33. [Dòng 615] v.length == 19))
  34. [Dòng 616] this._util.checkMod10(v) == true) {
  35. [Dòng 647] if (((_val.value.length == 4
  36. [Dòng 647] _val.value.search('/') == -1) || _val.value.length == 5) && this._util.checkValidIssueMonthYear(_val.value)) {
  37. [Dòng 647] _val.value.length == 5) && this._util.checkValidIssueMonthYear(_val.value)) {
  38. [Dòng 661] _val.value.length == 4) ||
  39. [Dòng 665] _val.value.length == 3)
  40. [Dòng 709] if (len == 0) { /* nothing, field is blank */
  41. [Dòng 748] if ((iTotal % 10) == 0) {
  42. [Dòng 804] cardNo.length == 15)
  43. [Dòng 806] cardNo.length == 16)
  44. [Dòng 807] || (cardNo.startsWith('6') && (cardNo.length == 16
  45. [Dòng 807] cardNo.length == 19))
  46. [Dòng 809] this._util.checkMod10(cardNo) == true
  47. [Dòng 829] if (expdate.length == 5) {
  48. [Dòng 861] _formCard.csc.length == 4) ||
  49. [Dòng 865] _formCard.csc.length == 3)
  50. [Dòng 940] if ('otp_page' == valOut) {
  51. [Dòng 943] if ('qr_timeout' == valOut) {
  52. [Dòng 1004] if (e.id == "1") { this.d_1 = true; this.b_1 = e; }
  53. [Dòng 1005] if (e.id == "2") { this.d_2 = true; this.b_2 = e; }
  54. [Dòng 1006] if (e.id == "3") { this.d_3 = true; this.b_3 = e; }
  55. [Dòng 1007] if (e.id == "4") { this.d_4 = true; this.b_4 = e; }
  56. [Dòng 1008] if (e.id == "5") { this.d_5 = true; this.b_5 = e; }
  57. [Dòng 1009] if (e.id == "6") { this.d_6 = true; this.b_6 = e; }
  58. [Dòng 1010] if (e.id == "7") { this.d_7 = true; this.b_7 = e; }
  59. [Dòng 1011] if (e.id == "8") { this.d_8 = true; this.b_8 = e; }
  60. [Dòng 1012] if (e.id == "9") { this.d_9 = true; this.b_9 = e; }
  61. [Dòng 1013] if (e.id == "10") { this.d_10 = true; this.b_10 = e; }
  62. [Dòng 1014] if (e.id == "11") { this.d_11 = true; this.b_11 = e; }
  63. [Dòng 1015] if (e.id == "12") { this.d_12 = true; this.b_12 = e; }
  64. [Dòng 1016] if (e.id == "14") { this.d_14 = true; this.b_14 = e; }
  65. [Dòng 1017] if (e.id == "15") { this.d_15 = true; this.b_15 = e; }
  66. [Dòng 1018] if (e.id == "16") { this.d_16 = true; this.b_16 = e; }
  67. [Dòng 1019] if (e.id == "17") { this.d_17 = true; this.b_17 = e; }
  68. [Dòng 1020] if (e.id == "18") { this.d_18 = true; this.b_18 = e; }
  69. [Dòng 1021] if (e.id == "19") { this.d_19 = true; this.b_19 = e; }
  70. [Dòng 1022] if (e.id == "20") { this.d_20 = true; this.b_20 = e; }
  71. [Dòng 1023] if (e.id == "22") { this.d_22 = true; this.b_22 = e; }
  72. [Dòng 1024] if (e.id == "23") { this.d_23 = true; this.b_23 = e; }
  73. [Dòng 1025] if (e.id == "24") { this.d_24 = true; this.b_24 = e; }
  74. [Dòng 1026] if (e.id == "25") { this.d_25 = true; this.b_25 = e; }
  75. [Dòng 1027] if (e.id == "27") { this.d_27 = true; this.b_27 = e; }
  76. [Dòng 1028] if (e.id == "30") { this.d_30 = true; this.b_30 = e; }
  77. [Dòng 1039] if (valOut == 'auth') {
  78. [Dòng 1069] if (this._auth == 0
  79. [Dòng 1069] if (this._auth == 0 && (this._b == 1
  80. [Dòng 1069] this._b == 4
  81. [Dòng 1069] this._b == 7
  82. [Dòng 1069] this._b == 8
  83. [Dòng 1069] this._b == 9
  84. [Dòng 1069] this._b == 10
  85. [Dòng 1070] this._b == 11
  86. [Dòng 1070] this._b == 15
  87. [Dòng 1070] this._b == 16
  88. [Dòng 1070] this._b == 17
  89. [Dòng 1070] this._b == 20
  90. [Dòng 1070] this._b == 22
  91. [Dòng 1071] this._b == 23
  92. [Dòng 1071] this._b == 24
  93. [Dòng 1071] this._b == 25
  94. [Dòng 1071] this._b == 30)) {
  95. [Dòng 1083] if (this._b == 20) {//seabank
  96. [Dòng 1086] } else if (this._b == 9
  97. [Dòng 1086] this._b == 25) {//9vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  98. [Dòng 1292] if (this._b == 15
  99. [Dòng 1292] this._b == 30
  100. [Dòng 1292] this._b == 9) {
  101. [Dòng 1302] if (this._b == 1
  102. [Dòng 1302] this._b == 25) {
  103. [Dòng 1354] this._b == 20)) {
  104. [Dòng 1380] if ((cardNo.length == 16
  105. [Dòng 1380] cardNo.length == 19) && this._util.checkMod10(cardNo) == true && regex.test(cardNo)

!= (66 điều kiện):
  1. [Dòng 165] if (this._idInvoice != null) {
  2. [Dòng 170] _re.body != null) {
  3. [Dòng 176] this._res_polling.links != null
  4. [Dòng 176] this._res_polling.links.merchant_return != null //
  5. [Dòng 183] } else if (this._res_polling.merchant != null
  6. [Dòng 183] this._res_polling.merchant_invoice_reference != null
  7. [Dòng 189] } else if (this._res_polling.payments != null
  8. [Dòng 203] this._res_polling.links.merchant_return != null//
  9. [Dòng 297] if (params['locale'] != null) {
  10. [Dòng 306] if (this._paymentService.getInvoiceDetail() != null) {
  11. [Dòng 337] this.d_vrbank = (this._res.qr_data != null
  12. [Dòng 369] if (this._res.merchant != null
  13. [Dòng 369] this._res.merchant_invoice_reference != null) {
  14. [Dòng 372] if (this._res.merchant.address_details != null) {
  15. [Dòng 377] if (this._res.billing != null
  16. [Dòng 377] this._res.billing.address != null) {
  17. [Dòng 378] if (this._res.billing.address.city != null) this._i_city = this._res.billing.address.city;
  18. [Dòng 379] if (this._res.billing.address.line1 != null) this._i_address = this._res.billing.address.line1;
  19. [Dòng 380] if (this._res.billing.address.postal_code != null) this._i_postal_code = this._res.billing.address.postal_code;
  20. [Dòng 381] if (this._res.billing.address.state != null) this._i_state = this._res.billing.address.state;
  21. [Dòng 382] if (this._res.billing.address.country_code != null) {
  22. [Dòng 392] this._res.links != null
  23. [Dòng 392] this._res.links.merchant_return != null //
  24. [Dòng 398] } else if (this._res.merchant != null
  25. [Dòng 398] this._res.merchant_invoice_reference != null
  26. [Dòng 402] } else if (this._res.payments != null
  27. [Dòng 411] this._res.links.merchant_return != null//
  28. [Dòng 600] if (imgCard != null) imgCard.src = imgCardSrc;
  29. [Dòng 660] _val.value != null
  30. [Dòng 771] if (this._res_post != null
  31. [Dòng 771] this._res_post.links != null
  32. [Dòng 771] this._res_post.links.merchant_return != null
  33. [Dòng 771] this._res_post.links.merchant_return.href != null){
  34. [Dòng 860] _formCard.csc != null
  35. [Dòng 873] if (!(!isNaN(_formCard.country) != null
  36. [Dòng 878] if (!(_formCard.address != null
  37. [Dòng 883] if (!(_formCard.province != null
  38. [Dòng 897] if (this.current != 'card') {
  39. [Dòng 910] if (this.current != 'date') {
  40. [Dòng 924] if (this.current != 'csc') {
  41. [Dòng 977] if (bankid != null) {
  42. [Dòng 995] if (!(strInstrument != null
  43. [Dòng 1097] if (this.htmlDesc != null
  44. [Dòng 1114] if (this._b != 9
  45. [Dòng 1114] this._b != 11
  46. [Dòng 1114] this._b != 16
  47. [Dòng 1114] this._b != 17
  48. [Dòng 1114] this._b != 25)
  49. [Dòng 1190] if (this.current_f1 != 'card') {
  50. [Dòng 1199] if (this.current_f1 != 'date') {
  51. [Dòng 1208] if (this.current_f1 != 'name') {
  52. [Dòng 1228] this._b != 25) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
  53. [Dòng 1266] if (this._res_post.return_url != null)
  54. [Dòng 1268] if (this._res_post.links != null
  55. [Dòng 1268] this._res_post.links.merchant_return.href != null) {
  56. [Dòng 1275] if (this._res_post.authorization != null
  57. [Dòng 1275] this._res_post.authorization.links != null
  58. [Dòng 1275] this._res_post.authorization.links.approval != null)
  59. [Dòng 1279] this._res_post.links.cancel != null)
  60. [Dòng 1283] var userName = _formCard.name != null ? _formCard.name : ''
  61. [Dòng 1284] this._res_post.authorization.links.approval != null
  62. [Dòng 1284] this._res_post.authorization.links.approval.href != null) {
  63. [Dòng 1289] userName = myParam != null ? myParam : ''
  64. [Dòng 1333] this._b != 25) {//9vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  65. [Dòng 1354] this._b != 20) || ((dCardExp.getTime() - dCurrent.getTime()) < 0
  66. [Dòng 1366] if (!(_formCard.name != null

================================================================================

📁 FILE 61: bank-amount.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/pipe/bank-amount.pipe.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 11] return ((a.id == id
  2. [Dòng 11] a.code == id) && a.type.includes(type));

================================================================================

📁 FILE 62: bank-amount-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/select-bank/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 63: bank-amount-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/select-bank/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
📊 Thống kê: 34 điều kiện duy nhất
   - === : 0 lần
   - == : 34 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (34 điều kiện):
  1. [Dòng 41] if (this.locale == 'en') {
  2. [Dòng 55] if (name == 'MAFC')
  3. [Dòng 61] if (bankId == 3
  4. [Dòng 61] bankId == 61
  5. [Dòng 62] bankId == 8
  6. [Dòng 62] bankId == 49
  7. [Dòng 63] bankId == 48
  8. [Dòng 64] bankId == 10
  9. [Dòng 64] bankId == 53
  10. [Dòng 65] bankId == 17
  11. [Dòng 65] bankId == 65
  12. [Dòng 66] bankId == 23
  13. [Dòng 66] bankId == 52
  14. [Dòng 67] bankId == 27
  15. [Dòng 67] bankId == 66
  16. [Dòng 68] bankId == 9
  17. [Dòng 68] bankId == 54
  18. [Dòng 69] bankId == 37
  19. [Dòng 70] bankId == 38
  20. [Dòng 71] bankId == 39
  21. [Dòng 72] bankId == 40
  22. [Dòng 73] bankId == 42
  23. [Dòng 74] bankId == 44
  24. [Dòng 75] bankId == 72
  25. [Dòng 76] bankId == 59
  26. [Dòng 79] bankId == 51
  27. [Dòng 80] bankId == 64
  28. [Dòng 81] bankId == 58
  29. [Dòng 82] bankId == 56
  30. [Dòng 85] bankId == 55
  31. [Dòng 86] bankId == 60
  32. [Dòng 87] bankId == 68
  33. [Dòng 88] bankId == 74
  34. [Dòng 89] bankId == 75

================================================================================

📁 FILE 64: condition-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/select-bank/condition-dialog/condition-dialog.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 22] confirm == false"

================================================================================

📁 FILE 65: condition-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/select-bank/condition-dialog/condition-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 66: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/select-bank/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 67: off-bank-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/select-bank/dialog/off-bank-dialog/off-bank-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 68: off-bank-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/select-bank/dialog/off-bank-dialog/off-bank-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 69: select-bank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/select-bank/select-bank.component.html
📊 Thống kê: 10 điều kiện duy nhất
   - === : 2 lần
   - == : 8 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 52] filteredData.length === 0"
  2. [Dòng 99] d_vrbank===true"

== (8 điều kiện):
  1. [Dòng 60] (!token&&_auth==0 && vietcombankGroupSelected) || (token && _b == 16)
  2. [Dòng 60] _b == 16)">
  3. [Dòng 64] _auth==0 && techcombankGroupSelected
  4. [Dòng 68] _auth==0 && shbGroupSelected
  5. [Dòng 73] _auth==0 && onepaynapasGroupSelected
  6. [Dòng 81] _auth==0 && bankaccountGroupSelected
  7. [Dòng 87] _auth==0 && vibbankGroupSelected
  8. [Dòng 92] _auth==1 && _b != 16

================================================================================

📁 FILE 70: select-bank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/select-bank/select-bank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 71: select-bank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/select-bank/select-bank.component.ts
📊 Thống kê: 211 điều kiện duy nhất
   - === : 29 lần
   - == : 134 lần
   - !== : 1 lần
   - != : 47 lần
--------------------------------------------------------------------------------

=== (29 điều kiện):
  1. [Dòng 438] if (count === 1) {
  2. [Dòng 445] if (offBanksArr[i] === this.lastDomescard) {
  3. [Dòng 463] if (this._res.state === 'unpaid'
  4. [Dòng 463] this._res.state === 'not_paid') {
  5. [Dòng 530] if (approval.method === 'REDIRECT') {
  6. [Dòng 533] } else if (approval.method === 'POST_REDIRECT') {
  7. [Dòng 559] this._res.merchant.max_payment === this._res.payments.length
  8. [Dòng 720] if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
  9. [Dòng 721] filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
  10. [Dòng 765] if (valOut === 'auth') {
  11. [Dòng 848] if (result === true) {
  12. [Dòng 967] if (this._b === '1'
  13. [Dòng 967] this._b === '20'
  14. [Dòng 967] this._b === '64') {
  15. [Dòng 970] if (this._b === '36'
  16. [Dòng 970] this._b === '18'
  17. [Dòng 970] this._b === '73'
  18. [Dòng 973] if (this._b === '19'
  19. [Dòng 973] this._b === '16'
  20. [Dòng 973] this._b === '25'
  21. [Dòng 973] this._b === '33'
  22. [Dòng 974] this._b === '39'
  23. [Dòng 974] this._b === '11'
  24. [Dòng 974] this._b === '17'
  25. [Dòng 975] this._b === '36'
  26. [Dòng 975] this._b === '44'
  27. [Dòng 976] this._b === '64'
  28. [Dòng 979] if (this._b === '20'
  29. [Dòng 982] if (this._b === '18') {

== (134 điều kiện):
  1. [Dòng 170] if (!isNaN(_re.status) && (_re.status == '200'
  2. [Dòng 170] _re.status == '201') && _re.body != null) {
  3. [Dòng 176] if (('closed' == this._res_polling.state
  4. [Dòng 176] 'canceled' == this._res_polling.state
  5. [Dòng 176] 'expired' == this._res_polling.state)
  6. [Dòng 194] } else if ('paid' == this._res_polling.state) {
  7. [Dòng 200] this._res_polling.payments == null) {
  8. [Dòng 202] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  9. [Dòng 208] } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
  10. [Dòng 208] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
  11. [Dòng 323] if (_re.status == '200'
  12. [Dòng 323] _re.status == '201') {
  13. [Dòng 417] this._auth == 0) {
  14. [Dòng 429] if (count == 2
  15. [Dòng 464] if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
  16. [Dòng 464] this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
  17. [Dòng 466] if (this._res.payments[this._res.payments.length - 1].state == 'approved'
  18. [Dòng 468] } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
  19. [Dòng 486] } else if (this._res.payments != null && (this._res.payments[0].state == 'authorization_required'
  20. [Dòng 486] this._res.payments[0].state == 'more_info_required')
  21. [Dòng 502] if (idBrand == 'atm'
  22. [Dòng 509] if ('op' == auth
  23. [Dòng 525] } else if ('bank' == auth
  24. [Dòng 550] this._res.payments[this._res.payments.length - 1].state == 'pending'
  25. [Dòng 567] if ('paid' == this._res.state) {
  26. [Dòng 585] if (('closed' == this._res.state
  27. [Dòng 585] 'canceled' == this._res.state
  28. [Dòng 585] 'expired' == this._res.state
  29. [Dòng 585] 'paid' == this._res.state)
  30. [Dòng 605] this._res.payments == null) {
  31. [Dòng 607] this._res.payments[this._res.payments.length - 1].state == 'pending') {
  32. [Dòng 618] if (this._res.currencies[0] == 'USD') {
  33. [Dòng 702] if (item.b.id == '2'
  34. [Dòng 702] item.b.id == '67') {
  35. [Dòng 743] $event == 'true') {
  36. [Dòng 750] _b: this._b == '2' ? '67' : this._b
  37. [Dòng 771] if (bankid == 2
  38. [Dòng 771] bankid == 67) {
  39. [Dòng 774] || (off && !this.enabledTwoBankTech && ((bankid == '2'
  40. [Dòng 774] this.isOffTechcombank) || (bankid == '67'
  41. [Dòng 827] bankid == 31) {
  42. [Dòng 895] if (bankId == 1
  43. [Dòng 895] bankId == 4
  44. [Dòng 895] bankId == 7
  45. [Dòng 895] bankId == 8
  46. [Dòng 895] bankId == 9
  47. [Dòng 895] bankId == 10
  48. [Dòng 895] bankId == 11
  49. [Dòng 895] bankId == 14
  50. [Dòng 895] bankId == 15
  51. [Dòng 896] bankId == 16
  52. [Dòng 896] bankId == 17
  53. [Dòng 896] bankId == 20
  54. [Dòng 896] bankId == 22
  55. [Dòng 896] bankId == 23
  56. [Dòng 896] bankId == 24
  57. [Dòng 896] bankId == 25
  58. [Dòng 896] bankId == 30
  59. [Dòng 896] bankId == 33
  60. [Dòng 897] bankId == 34
  61. [Dòng 897] bankId == 35
  62. [Dòng 897] bankId == 36
  63. [Dòng 897] bankId == 37
  64. [Dòng 897] bankId == 38
  65. [Dòng 897] bankId == 39
  66. [Dòng 897] bankId == 40
  67. [Dòng 897] bankId == 41
  68. [Dòng 897] bankId == 42
  69. [Dòng 898] bankId == 43
  70. [Dòng 898] bankId == 44
  71. [Dòng 898] bankId == 45
  72. [Dòng 898] bankId == 46
  73. [Dòng 898] bankId == 47
  74. [Dòng 898] bankId == 48
  75. [Dòng 898] bankId == 49
  76. [Dòng 898] bankId == 50
  77. [Dòng 898] bankId == 51
  78. [Dòng 899] bankId == 52
  79. [Dòng 899] bankId == 53
  80. [Dòng 899] bankId == 54
  81. [Dòng 899] bankId == 55
  82. [Dòng 899] bankId == 56
  83. [Dòng 899] bankId == 57
  84. [Dòng 899] bankId == 58
  85. [Dòng 899] bankId == 59
  86. [Dòng 899] bankId == 60
  87. [Dòng 900] bankId == 61
  88. [Dòng 900] bankId == 62
  89. [Dòng 900] bankId == 63
  90. [Dòng 900] bankId == 64
  91. [Dòng 900] bankId == 65
  92. [Dòng 900] bankId == 66
  93. [Dòng 900] bankId == 68
  94. [Dòng 900] bankId == 69
  95. [Dòng 900] bankId == 70
  96. [Dòng 901] bankId == 71
  97. [Dòng 901] bankId == 72
  98. [Dòng 901] bankId == 73
  99. [Dòng 901] bankId == 32
  100. [Dòng 901] bankId == 74
  101. [Dòng 901] bankId == 75) {
  102. [Dòng 903] } else if (bankId == 6
  103. [Dòng 903] bankId == 31
  104. [Dòng 903] bankId == 80) {
  105. [Dòng 905] } else if (bankId == 2
  106. [Dòng 905] bankId == 67) {
  107. [Dòng 907] } else if (bankId == 3
  108. [Dòng 907] bankId == 18
  109. [Dòng 907] bankId == 19
  110. [Dòng 907] bankId == 27) {
  111. [Dòng 909] } else if (bankId == 5) {
  112. [Dòng 911] } else if (bankId == 12) {
  113. [Dòng 970] this._b == '55'
  114. [Dòng 970] this._b == '47'
  115. [Dòng 970] this._b == '48'
  116. [Dòng 970] this._b == '59'
  117. [Dòng 970] this._b == '19'
  118. [Dòng 970] this._b == '12') {
  119. [Dòng 973] this._b == '3'
  120. [Dòng 974] this._b == '43'
  121. [Dòng 974] this._b == '45'
  122. [Dòng 974] this._b == '73'
  123. [Dòng 975] this._b == '57'
  124. [Dòng 976] this._b == '61'
  125. [Dòng 976] this._b == '63'
  126. [Dòng 976] this._b == '67'
  127. [Dòng 976] this._b == '68'
  128. [Dòng 976] this._b == '69'
  129. [Dòng 976] this._b == '72'
  130. [Dòng 976] this._b == '9'
  131. [Dòng 976] this._b == '74'
  132. [Dòng 976] this._b == '75') {
  133. [Dòng 979] this._b == '36'
  134. [Dòng 999] if (item['id'] == this._b) {

!== (1 điều kiện):
  1. [Dòng 707] this.bankList = this.bankList.filter(item => item.b.id !== '67');

!= (47 điều kiện):
  1. [Dòng 164] if (this._idInvoice != null
  2. [Dòng 164] this._paymentService.getState() != 'error') {
  3. [Dòng 170] _re.body != null) {
  4. [Dòng 177] this._res_polling.links != null
  5. [Dòng 177] this._res_polling.links.merchant_return != null //
  6. [Dòng 200] } else if (this._res_polling.merchant != null
  7. [Dòng 200] this._res_polling.merchant_invoice_reference != null
  8. [Dòng 202] } else if (this._res_polling.payments != null
  9. [Dòng 211] if(this._res_polling.links != null
  10. [Dòng 211] this._res_polling.links.merchant_return != null){
  11. [Dòng 302] if (params['locale'] != null) {
  12. [Dòng 310] if ('otp' != this._paymentService.getCurrentPage()) {
  13. [Dòng 315] if (this._paymentService.getInvoiceDetail() != null) {
  14. [Dòng 325] this._res.merchant.id != 'OPTEST' ? this._res.on_off_bank : ''
  15. [Dòng 434] if (count != 1) {
  16. [Dòng 459] if (this._res.merchant != null
  17. [Dòng 459] this._res.merchant_invoice_reference != null) {
  18. [Dòng 464] this._res.links != null//
  19. [Dòng 486] } else if (this._res.payments != null
  20. [Dòng 487] this._res.payments[0].instrument != null
  21. [Dòng 487] this._res.payments[0].instrument.issuer != null
  22. [Dòng 488] this._res.payments[0].instrument.issuer.brand != null
  23. [Dòng 488] this._res.payments[0].instrument.issuer.brand.id != null
  24. [Dòng 489] this._res.payments[0].links != null
  25. [Dòng 489] this._res.payments[0].links.cancel != null
  26. [Dòng 489] this._res.payments[0].links.cancel.href != null
  27. [Dòng 502] this._res.payments[0].instrument.issuer.swift_code != null
  28. [Dòng 503] this._res.payments[0].authorization != null
  29. [Dòng 503] this._res.payments[0].authorization.links != null) {
  30. [Dòng 509] this._res.payments[0].authorization.links.approval != null
  31. [Dòng 509] this._res.payments[0].authorization.links.approval.href != null) {
  32. [Dòng 512] if (this._res.payments[0].authorization != null
  33. [Dòng 512] this._res.payments[0].authorization.links != null
  34. [Dòng 518] auth = paramUserName != null ? paramUserName : ''
  35. [Dòng 586] this._res.links != null
  36. [Dòng 586] this._res.links.merchant_return != null //
  37. [Dòng 605] } else if (this._res.merchant != null
  38. [Dòng 605] this._res.merchant_invoice_reference != null
  39. [Dòng 655] if (!(strInstrument != null
  40. [Dòng 658] if (strInstrument.substring(0, 1) != '^'
  41. [Dòng 658] strInstrument.substr(strInstrument.length - 1) != '$') {
  42. [Dòng 833] bankid != 31) {
  43. [Dòng 851] if (bankid != null) {
  44. [Dòng 876] if (this._res_post != null
  45. [Dòng 876] this._res_post.links != null
  46. [Dòng 876] this._res_post.links.merchant_return != null
  47. [Dòng 876] this._res_post.links.merchant_return.href != null) {

================================================================================

📁 FILE 72: cancel.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/services/cancel.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 73: data.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/services/data.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 49] const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
  2. [Dòng 49] item.method === method) : null;

================================================================================

📁 FILE 74: dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/services/dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 75: payment.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/services/payment.service.ts
📊 Thống kê: 11 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 10 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 263] this._state == 'completed') return of([]);

!= (10 điều kiện):
  1. [Dòng 105] if (idInvoice != null
  2. [Dòng 105] idInvoice != 0)
  3. [Dòng 233] if (this._merchantid != null
  4. [Dòng 233] this._tranref != null
  5. [Dòng 233] this._state != null
  6. [Dòng 263] if (this._state != null
  7. [Dòng 272] } else if (idInvoice != null
  8. [Dòng 272] idInvoice != 0) {
  9. [Dòng 348] let urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
  10. [Dòng 353] urlCalcel = this.getUrlCancel() + (idInvoice != null ? idInvoice : '');

================================================================================

📁 FILE 76: index.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/translate/index.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 77: lang-en.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/translate/lang-en.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 78: lang-vi.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/translate/lang-vi.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 79: translate.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/translate/translate.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 80: translate.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/translate/translate.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 33] if(this.currentLang=='en') curr_lang='LANG_EN_NAME';
  2. [Dòng 34] else if(this.currentLang=='vi') curr_lang='LANG_VI_NAME';

================================================================================

📁 FILE 81: translations.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/translate/translations.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 82: banks-fee.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/util/banks-fee.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 83: banks-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/util/banks-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1194] if (+e.id == bankId) {
  2. [Dòng 1244] if (e.swiftCode == bankSwift) {

================================================================================

📁 FILE 84: installment-utils.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/util/installment-utils.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 85: util.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/app/util/util.ts
📊 Thống kê: 36 điều kiện duy nhất
   - === : 13 lần
   - == : 17 lần
   - !== : 2 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (13 điều kiện):
  1. [Dòng 47] if (param === key) {
  2. [Dòng 94] if (v.length === 2
  3. [Dòng 94] this.flag.length === 3
  4. [Dòng 94] this.flag.charAt(this.flag.length - 1) === '/') {
  5. [Dòng 98] if (v.length === 1) {
  6. [Dòng 100] } else if (v.length === 2) {
  7. [Dòng 103] v.length === 2) {
  8. [Dòng 111] if (len === 2) {
  9. [Dòng 183] if (M[1] === 'Chrome') {
  10. [Dòng 495] if (cardTypeBank === 'bank_card_number') {
  11. [Dòng 498] } else if (cardTypeBank === 'bank_account_number') {
  12. [Dòng 548] if (event.keyCode === 8
  13. [Dòng 548] event.key === "Backspace"

== (17 điều kiện):
  1. [Dòng 15] if (c.length == 3) {
  2. [Dòng 31] return results == null ? null : results[1]
  3. [Dòng 61] if (temp.length == 0) {
  4. [Dòng 68] return (counter % 10 == 0);
  5. [Dòng 157] if (this.checkCount == 1) {
  6. [Dòng 169] if (results == null) {
  7. [Dòng 200] d = d == undefined ? '.' : d
  8. [Dòng 201] t = t == undefined ? '
  9. [Dòng 548] event.inputType == 'deleteContentBackward') {
  10. [Dòng 549] if (event.target.name == 'exp_date'
  11. [Dòng 557] event.inputType == 'insertCompositionText') {
  12. [Dòng 571] if (((_val.length == 4
  13. [Dòng 571] _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  14. [Dòng 571] _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  15. [Dòng 597] iss_date.length == 4
  16. [Dòng 597] iss_date.search('/') == -1)
  17. [Dòng 598] iss_date.length == 5))

!== (2 điều kiện):
  1. [Dòng 42] queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
  2. [Dòng 43] if (queryString !== '') {

!= (4 điều kiện):
  1. [Dòng 185] if (tem != null) {
  2. [Dòng 190] if ((tem = ua.match(/version\/(\d+)/i)) != null) {
  3. [Dòng 493] if (ua.indexOf('safari') != -1
  4. [Dòng 550] if (v.length != 3) {

================================================================================

📁 FILE 86: qrcode.js
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/assets/script/qrcode.js
📊 Thống kê: 67 điều kiện duy nhất
   - === : 2 lần
   - == : 53 lần
   - !== : 0 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 2255] if (typeof define === 'function'
  2. [Dòng 2257] } else if (typeof exports === 'object') {

== (53 điều kiện):
  1. [Dòng 68] if (_dataCache == null) {
  2. [Dòng 85] if ( (0 <= r && r <= 6 && (c == 0
  3. [Dòng 85] c == 6) )
  4. [Dòng 86] || (0 <= c && c <= 6 && (r == 0
  5. [Dòng 86] r == 6) )
  6. [Dòng 107] if (i == 0
  7. [Dòng 122] _modules[r][6] = (r % 2 == 0);
  8. [Dòng 129] _modules[6][c] = (c % 2 == 0);
  9. [Dòng 152] if (r == -2
  10. [Dòng 152] r == 2
  11. [Dòng 152] c == -2
  12. [Dòng 152] c == 2
  13. [Dòng 153] || (r == 0
  14. [Dòng 153] c == 0) ) {
  15. [Dòng 169] ( (bits >> i) & 1) == 1);
  16. [Dòng 226] if (col == 6) col -= 1;
  17. [Dòng 232] if (_modules[row][col - c] == null) {
  18. [Dòng 237] dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
  19. [Dòng 249] if (bitIndex == -1) {
  20. [Dòng 458] margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
  21. [Dòng 498] if (typeof arguments[0] == 'object') {
  22. [Dòng 587] margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
  23. [Dòng 733] if (b == -1) throw 'eof';
  24. [Dòng 741] if (b0 == -1) break;
  25. [Dòng 767] if (typeof b == 'number') {
  26. [Dòng 768] if ( (b & 0xff) == b) {
  27. [Dòng 910] return function(i, j) { return (i + j) % 2 == 0
  28. [Dòng 912] return function(i, j) { return i % 2 == 0
  29. [Dòng 914] return function(i, j) { return j % 3 == 0
  30. [Dòng 916] return function(i, j) { return (i + j) % 3 == 0
  31. [Dòng 918] return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
  32. [Dòng 920] return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
  33. [Dòng 922] return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
  34. [Dòng 924] return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
  35. [Dòng 1011] if (r == 0
  36. [Dòng 1011] c == 0) {
  37. [Dòng 1015] if (dark == qrcode.isDark(row + r, col + c) ) {
  38. [Dòng 1036] if (count == 0
  39. [Dòng 1036] count == 4) {
  40. [Dòng 1149] if (typeof num.length == 'undefined') {
  41. [Dòng 1155] num[offset] == 0) {
  42. [Dòng 1495] if (typeof rsBlock == 'undefined') {
  43. [Dòng 1538] return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
  44. [Dòng 1543] _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
  45. [Dòng 1599] if (data.length - i == 1) {
  46. [Dòng 1601] } else if (data.length - i == 2) {
  47. [Dòng 1866] } else if (n == 62) {
  48. [Dòng 1868] } else if (n == 63) {
  49. [Dòng 1928] if (_buflen == 0) {
  50. [Dòng 1937] if (c == '=') {
  51. [Dòng 1961] } else if (c == 0x2b) {
  52. [Dòng 1963] } else if (c == 0x2f) {
  53. [Dòng 2133] if (table.size() == (1 << bitLength) ) {

!= (12 điều kiện):
  1. [Dòng 119] if (_modules[r][6] != null) {
  2. [Dòng 126] if (_modules[6][c] != null) {
  3. [Dòng 144] if (_modules[row][col] != null) {
  4. [Dòng 365] while (buffer.getLengthInBits() % 8 != 0) {
  5. [Dòng 750] if (count != numChars) {
  6. [Dòng 751] throw count + ' != ' + numChars
  7. [Dòng 878] while (data != 0) {
  8. [Dòng 1733] if (test.length != 2
  9. [Dòng 1733] ( (test[0] << 8) | test[1]) != code) {
  10. [Dòng 1894] if (_length % 3 != 0) {
  11. [Dòng 2067] if ( (data >>> length) != 0) {
  12. [Dòng 2178] return typeof _map[key] != 'undefined'

================================================================================

📁 FILE 87: environment.dev.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/environments/environment.dev.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 88: environment.mtf.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/environments/environment.mtf.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 89: environment.prod.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/environments/environment.prod.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 90: environment.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/environments/environment.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 91: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/index.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 1 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 38] if(M[1]=== 'Chrome'){

== (1 điều kiện):
  1. [Dòng 49] if(checkIE[0]=='MSIE'

!= (3 điều kiện):
  1. [Dòng 27] document.documentMode!=null
  2. [Dòng 40] if(tem!= null) return tem.slice(1).join(' ').replace('OPR'
  3. [Dòng 43] if((tem= ua.match(/version\/(\d+)/i))!= null) M.splice(1

================================================================================

📁 FILE 92: karma.conf.js
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/karma.conf.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 93: main.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/main.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 94: polyfills.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/polyfills.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 95: test.ts
📍 Đường dẫn: /root/projects/onepay/paygate-homecredit/src/test.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📋 TÓM TẮT TẤT CẢ ĐIỀU KIỆN DUY NHẤT:
====================================================================================================

=== (120 điều kiện duy nhất):
------------------------------------------------------------
1. return lang === this._translate.currentLang
2. <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number" class="form-control "
3. [class.red_border_no_background]="c_expdate && (valueDate.length === 0
4. valueDate.trim().length === 0)"
5. if (target.tagName === 'A'
6. if (isIE[0] === 'MSIE'
7. +isIE[1] === 10) {
8. if ((_val.value.substr(-1) === ' '
9. _val.value.length === 24) {
10. if (this.cardTypeBank === 'bank_card_number') {
11. } else if (this.cardTypeBank === 'bank_account_number') {
12. } else if (this.cardTypeBank === 'bank_username') {
13. } else if (this.cardTypeBank === 'bank_customer_code') {
14. this.cardTypeBank === 'bank_card_number'
15. if (this.cardTypeOcean === 'IB') {
16. } else if (this.cardTypeOcean === 'MB') {
17. if (_val.value.substr(0, 2) === '84') {
18. } else if (this.cardTypeOcean === 'ATM') {
19. if (this.cardTypeBank === 'bank_account_number') {
20. this.cardTypeBank === 'bank_card_number') {
21. if (this.cardTypeBank === 'bank_card_number'
22. if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
23. if (event.keyCode === 8
24. event.key === "Backspace"
25. if (v.length === 2
26. this.flag.length === 3
27. this.flag.charAt(this.flag.length - 1) === '/') {
28. if (v.length === 1) {
29. } else if (v.length === 2) {
30. v.length === 2) {
31. if (len === 2) {
32. if ((this.cardTypeBank === 'bank_account_number'
33. this.cardTypeBank === 'bank_username'
34. this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
35. this.cardTypeOcean === 'ATM')
36. || (this.cardTypeOcean === 'IB'
37. if (valIn === this._translate.instant('bank_card_number')) {
38. } else if (valIn === this._translate.instant('bank_account_number')) {
39. } else if (valIn === this._translate.instant('bank_username')) {
40. } else if (valIn === this._translate.instant('bank_customer_code')) {
41. if (_val.value === ''
42. _val.value === null
43. _val.value === undefined) {
44. if (_val.value && (this.cardTypeBank === 'bank_card_number'
45. if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
46. this.cardTypeOcean === 'MB') {
47. this.cardTypeOcean === 'IB'
48. if ((this.cardTypeBank === 'bank_card_number'
49. if (this.cardName === undefined
50. this.cardName === '') {
51. if (this.valueDate === undefined
52. this.valueDate === '') {
53. <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
54. c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
55. _inExpDate.trim().length === 0)"
56. if (this.timeLeft === 10) {
57. if (this.runTime === true) {
58. if (this.timeLeft === 0) {
59. if (this.runTime === true) this.submitCardBanking();
60. if (approval.method === 'REDIRECT') {
61. } else if (approval.method === 'POST_REDIRECT') {
62. if (this.timeLeft === 1) {
63. } else if (valIn === this._translate.instant('internet_banking')) {
64. if (_val.value && (this.cardTypeBank === 'bank_card_number')) {
65. if (focusElement === 'card_name') {
66. } else if (focusElement === 'exp_date'
67. focusExpDateElement === 'card_name') {
68. if (this.cardTypeBank === 'bank_account_number'
69. this.cardTypeBank === 'bank_card_number')) {
70. if ((this.cardTypeBank === 'bank_account_number') && cardNo.length !== 0) {
71. isPopupSupport === 'True'"
72. params.timeout === 'true') {
73. this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
74. _re.body.state === 'unpaid');
75. if (this.errorCode === 'overtime'
76. this.errorCode === '253') {
77. if (this.errorCode === 'overtime') {
78. if (YY % 400 === 0
79. YY % 4 === 0)) {
80. if (YYYY % 400 === 0
81. YYYY % 4 === 0)) {
82. if (valid.indexOf(temp) === -1) bNum = false;
83. filteredData.length === 0"
84. d_vrbank===true"
85. if (count === 1) {
86. if (offBanksArr[i] === this.lastDomescard) {
87. if (this._res.state === 'unpaid'
88. this._res.state === 'not_paid') {
89. this._res.merchant.max_payment === this._res.payments.length
90. if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
91. filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
92. if (valOut === 'auth') {
93. if (result === true) {
94. if (this._b === '1'
95. this._b === '20'
96. this._b === '64') {
97. if (this._b === '36'
98. this._b === '18'
99. this._b === '73'
100. if (this._b === '19'
101. this._b === '16'
102. this._b === '25'
103. this._b === '33'
104. this._b === '39'
105. this._b === '11'
106. this._b === '17'
107. this._b === '36'
108. this._b === '44'
109. this._b === '64'
110. if (this._b === '20'
111. if (this._b === '18') {
112. const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
113. item.method === method) : null;
114. if (param === key) {
115. if (M[1] === 'Chrome') {
116. if (cardTypeBank === 'bank_card_number') {
117. } else if (cardTypeBank === 'bank_account_number') {
118. if (typeof define === 'function'
119. } else if (typeof exports === 'object') {
120. if(M[1]=== 'Chrome'){

== (494 điều kiện duy nhất):
------------------------------------------------------------
1. 'vi'==params['locale']){
2. 'en'==params['locale']){
3. if ((_val.value.length == 16
4. _val.value.length == 19) && this.checkMod10(_val.value) == true) {
5. this.checkMod10(_val.value) == true) {
6. if ((_val.value.length == 4
7. _val.value.search('/') == -1) || _val.value.length == 5) {
8. _val.value.length == 5) {
9. if (temp.length == 0) {
10. return (counter % 10 == 0);
11. if (_re.status == '200'
12. _re.status == '201') {
13. if (this._res.state == 'approved'
14. this._res.state == 'failed') {
15. } else if (this._res.state == 'authorization_required') {
16. if (!((_formCard.card_number.length == 16
17. _formCard.card_number.length == 19) && this.checkMod10(_formCard.card_number) == true && _formCard.card_number.startsWith('970425'))) {
18. this.checkMod10(_formCard.card_number) == true
19. if (expdate.length == 5) {
20. if (this._b == 18
21. this._b == 19) {
22. if (this._b == 19) {//19BIDV
23. } else if (this._b == 3
24. this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
25. if (this._b == 27) {
26. } else if (this._b == 12) {// 12SHB
27. } else if (this._b == 18) { //18Oceanbank-ocb
28. if (this._b == 19
29. this._b == 3
30. this._b == 27
31. this._b == 12) {
32. } else if (this._b == 18) {
33. if (this.checkBin(_val.value) && (this._b == 3
34. this._b == 27)) {
35. if (this._b == 3) {
36. this.cardTypeOcean == 'ATM') {
37. if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
38. this._b == 18)) {
39. if (this.checkBin(v) && (this._b == 3
40. event.inputType == 'deleteContentBackward') {
41. if (event.target.name == 'exp_date'
42. event.inputType == 'insertCompositionText') {
43. if (((this.valueDate.length == 4
44. this.valueDate.search('/') == -1) || this.valueDate.length == 5)
45. this.valueDate.length == 5)
46. } else if (this._b == 19) {
47. } else if (this._b == 27) {
48. if (this._b == 12) {
49. if (this.cardTypeBank == 'bank_customer_code') {
50. } else if (this.cardTypeBank == 'bank_account_number') {
51. _formCard.exp_date.length == 5
52. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
53. this._b == 3)) {//27-pvcombank;3-TPB ;
54. if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
55. this._b == 19
56. this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
57. if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
58. if (this.cardTypeOcean == 'IB') {
59. } else if (this.cardTypeOcean == 'MB') {
60. } else if (this.cardTypeOcean == 'ATM') {
61. if (this._res_post.state == 'approved'
62. this._res_post.state == 'failed') {
63. } else if (this._res_post.state == 'authorization_required') {
64. if (this._b == 18) {
65. if (this._b == 27
66. this._b == 18) {
67. if (err.status == 400
68. err.error['name'] == 'INVALID_INPUT_BIN') {
69. if ((cardNo.length == 16
70. if ((cardNo.length == 16 || (cardNo.length == 19
71. && ((this._b == 18
72. cardNo.length == 19) || this._b != 18)
73. if (this._b == +e.id) {
74. if (valIn == 1) {
75. } else if (valIn == 2) {
76. this._b == 3) {
77. if (this._b == 19) {
78. if (cardType == this._translate.instant('internetbanking')
79. } else if (cardType == this._translate.instant('mobilebanking')
80. } else if (cardType == this._translate.instant('atm')
81. this._b == 18))) {
82. } else if (this._b == 18
83. this.c_expdate = !(((this.valueDate.length == 4
84. this.valueDate.length == 4
85. this.valueDate.search('/') == -1)
86. this.valueDate.length == 5))
87. <div [ngStyle]="{'margin-top': !(checkTwoEnabled && !isOffTechcombankNapas && !isOffTechcombank)  ? '15px' : '0px' }" *ngIf="(cardTypeBank == 'bank_card_number') &&(_b == 67
88. (cardTypeBank == 'bank_card_number') &&(_b == 67 || checkTwoEnabled)&& ready===1 && !isOffTechcombankNapas
89. <div [ngStyle]="{'margin-top': !(checkTwoEnabled && !isOffTechcombankNapas && !isOffTechcombank)  ? '15px' : '0px' }" class="nd-bank-card-two" *ngIf="(cardTypeBank == 'internet_banking')&&(_b == 2
90. if (this._b == 67
91. this._b == 2) {//19BIDV
92. if((this._b == 2
93. !this.checkTwoEnabled) || (this._b == 2
94. } else if ((this._b == 2
95. this._b == 67) {
96. return this._b == 2
97. this._b == 67
98. _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
99. this.checkMod10(cardNo) == true
100. if (this._b != 68 || (this._b == 68
101. return ((value.length == 4
102. value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
103. value.length == 5) && parseInt(value.split('/')[0]
104. || (this._util.checkValidExpireMonthYear(value) && (this._b == 2
105. this._b == 20
106. this._b == 33
107. this._b == 39
108. this._b == 43
109. this._b == 45
110. this._b == 64
111. this._b == 68
112. this._b == 72))) //sonnh them Vietbank 72
113. this._inExpDate.length == 4
114. this._inExpDate.search('/') == -1)
115. this._inExpDate.length == 5))
116. || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 20
117. this._b == 2
118. this._b == 72)));
119. if (this._b == 8) {//MB Bank
120. if (this._b == 18) {//Oceanbank
121. if (this._b == 8) {
122. if (this._b == 12) { //SHB
123. if (this._b == 18) {//8-MB Bank;18-oceanbank
124. <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_card_number')">
125. <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_account_number')">
126. if(this._b == 12) this.isShbGroup = true;
127. return this._b == 9
128. this._b == 11
129. this._b == 16
130. this._b == 17
131. this._b == 25
132. this._b == 44
133. this._b == 57
134. this._b == 59
135. this._b == 61
136. this._b == 63
137. this._b == 69
138. if (this._b == 12
139. this.cardTypeBank == 'bank_account_number') {
140. && (_re.body.payments[_re.body.payments.length - 1].reason.code == '25')) {
141. cardNo.length == 19) && cardNo.startsWith("970443") && this._util.checkMod10(cardNo) && this.checkBin(cardNo) ) {
142. if (this._b == 2
143. this._b == 5
144. this._b == 31) {
145. if (this._b == 2) {
146. } else if (this._b == 6) {
147. } else if (this._b == 31) {
148. transactionFlow=='internet_banking'"
149. transactionFlow=='bank_account'"
150. if (this._b == 5) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
151. if (this.transactionFlow=='internet_banking') {
152. if (this._b ==5) {
153. if (this._b == 5
154. if (this.checkBin(_val.value) && (this._b == 5)) {
155. if (this._b == 5) {//** check đúng số thẻ 5-vib
156. if (this._b == 5) {
157. if (this.checkBin(v) && (this._b == 5)) {
158. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 5
159. if (this.cardName != null && this.cardName.length > 0 && (this._b == 5)) {
160. _b==68"
161. if (this._b == 1
162. this._b == 36
163. this._b == 55
164. this._b == 47
165. this._b == 48) {
166. this._b == 9
167. return this._b == 11
168. this._b == 72
169. this._b == 73
170. this._b == 74
171. this._b == 75
172. this._b == 14
173. this._b == 15
174. this._b == 24
175. this._b == 8
176. this._b == 10
177. this._b == 22
178. this._b == 23
179. this._b == 30
180. this._b == 9) {
181. (cardNo.length == 19
182. (cardNo.length == 19 && (this._b == 1
183. this._b == 4
184. this._b == 48
185. this._b == 59))
186. this._util.checkMod10(cardNo) == true
187. errorCode == '11'"
188. isSent == false
189. timeLeft == 'Infinity'"
190. if (_re.body.currencies[0] == 'USD') {
191. _re.body.themes.theme == 'homecredit'){
192. if (_re2.status == '200'
193. _re2.status == '201') {
194. if(this.isTimePause == false) {
195. if ((dataPassed.status == '200'
196. dataPassed.status == '201') && dataPassed.body != null) {
197. (this._cookie.get('d_mpayvn')+"")=="true"
198. (this._cookie.get('d_bidvpayplus')+"")=="true"
199. (this._cookie.get('d_myvib')+"")=="true"
200. (this._cookie.get('d_momo')+"")=="true"
201. _auth==0&&(_b==1||_b==4||_b==7||_b==8||_b==9||_b==10||_b==11||_b==15||_b==16||_b==17||_b==20||_b==22||_b==23||_b==24||_b==25||_b==30)
202. _auth==0&&(_b==2||_b==5||_b==6)
203. _auth==0&&(_b==3||_b==12||_b==14||_b==18||_b==19||_b==27)
204. _auth==1"
205. if (bankId == 24) return true;
206. if (!isNaN(_re.status) && (_re.status == '200'
207. _re.status == '201') && _re.body != null) {
208. if (('closed' == this._res_polling.state
209. 'canceled' == this._res_polling.state
210. 'expired' == this._res_polling.state
211. 'paid' == this._res_polling.state)
212. this._res_polling.payments == null) {
213. this._res_polling.payments[0].state == 'pending') {
214. this._paymentService.getCurrentPage() == 'enter_card') {
215. } else if (this._res_polling.payments != null && (this._res_polling.payments[0].state == 'approved'
216. this._res_polling.payments[0].state == 'failed')
217. if (count == null
218. count == '') {
219. if (('closed' == this._res.state
220. 'canceled' == this._res.state
221. 'expired' == this._res.state
222. 'paid' == this._res.state)
223. this._res.payments == null) {
224. this._res.payments[0].state == 'pending') {
225. } else if (this._res.payments != null && (this._res.payments[0].state == 'approved'
226. this._res.payments[0].state == 'failed')
227. if (this._res.currencies[0] == 'USD') numfix = 2;
228. if (x == 1) {
229. else if (x == 2) {
230. else if (x == 3) {
231. v.length == 15) || (v.length == 16
232. v.length == 19))
233. this._util.checkMod10(v) == true) {
234. if (((_val.value.length == 4
235. _val.value.search('/') == -1) || _val.value.length == 5) && this._util.checkValidIssueMonthYear(_val.value)) {
236. _val.value.length == 5) && this._util.checkValidIssueMonthYear(_val.value)) {
237. _val.value.length == 4) ||
238. _val.value.length == 3)
239. if (len == 0) { /* nothing, field is blank */
240. if ((iTotal % 10) == 0) {
241. cardNo.length == 15)
242. cardNo.length == 16)
243. || (cardNo.startsWith('6') && (cardNo.length == 16
244. cardNo.length == 19))
245. _formCard.csc.length == 4) ||
246. _formCard.csc.length == 3)
247. if ('otp_page' == valOut) {
248. if ('qr_timeout' == valOut) {
249. if (e.id == "1") { this.d_1 = true; this.b_1 = e; }
250. if (e.id == "2") { this.d_2 = true; this.b_2 = e; }
251. if (e.id == "3") { this.d_3 = true; this.b_3 = e; }
252. if (e.id == "4") { this.d_4 = true; this.b_4 = e; }
253. if (e.id == "5") { this.d_5 = true; this.b_5 = e; }
254. if (e.id == "6") { this.d_6 = true; this.b_6 = e; }
255. if (e.id == "7") { this.d_7 = true; this.b_7 = e; }
256. if (e.id == "8") { this.d_8 = true; this.b_8 = e; }
257. if (e.id == "9") { this.d_9 = true; this.b_9 = e; }
258. if (e.id == "10") { this.d_10 = true; this.b_10 = e; }
259. if (e.id == "11") { this.d_11 = true; this.b_11 = e; }
260. if (e.id == "12") { this.d_12 = true; this.b_12 = e; }
261. if (e.id == "14") { this.d_14 = true; this.b_14 = e; }
262. if (e.id == "15") { this.d_15 = true; this.b_15 = e; }
263. if (e.id == "16") { this.d_16 = true; this.b_16 = e; }
264. if (e.id == "17") { this.d_17 = true; this.b_17 = e; }
265. if (e.id == "18") { this.d_18 = true; this.b_18 = e; }
266. if (e.id == "19") { this.d_19 = true; this.b_19 = e; }
267. if (e.id == "20") { this.d_20 = true; this.b_20 = e; }
268. if (e.id == "22") { this.d_22 = true; this.b_22 = e; }
269. if (e.id == "23") { this.d_23 = true; this.b_23 = e; }
270. if (e.id == "24") { this.d_24 = true; this.b_24 = e; }
271. if (e.id == "25") { this.d_25 = true; this.b_25 = e; }
272. if (e.id == "27") { this.d_27 = true; this.b_27 = e; }
273. if (e.id == "30") { this.d_30 = true; this.b_30 = e; }
274. if (valOut == 'auth') {
275. if (this._auth == 0
276. if (this._auth == 0 && (this._b == 1
277. this._b == 7
278. this._b == 30)) {
279. if (this._b == 20) {//seabank
280. } else if (this._b == 9
281. this._b == 25) {//9vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
282. if (this._b == 15
283. this._b == 25) {
284. this._b == 20)) {
285. cardNo.length == 19) && this._util.checkMod10(cardNo) == true && regex.test(cardNo)
286. return ((a.id == id
287. a.code == id) && a.type.includes(type));
288. if (this.locale == 'en') {
289. if (name == 'MAFC')
290. if (bankId == 3
291. bankId == 61
292. bankId == 8
293. bankId == 49
294. bankId == 48
295. bankId == 10
296. bankId == 53
297. bankId == 17
298. bankId == 65
299. bankId == 23
300. bankId == 52
301. bankId == 27
302. bankId == 66
303. bankId == 9
304. bankId == 54
305. bankId == 37
306. bankId == 38
307. bankId == 39
308. bankId == 40
309. bankId == 42
310. bankId == 44
311. bankId == 72
312. bankId == 59
313. bankId == 51
314. bankId == 64
315. bankId == 58
316. bankId == 56
317. bankId == 55
318. bankId == 60
319. bankId == 68
320. bankId == 74
321. bankId == 75
322. confirm == false"
323. (!token&&_auth==0 && vietcombankGroupSelected) || (token && _b == 16)
324. _b == 16)">
325. _auth==0 && techcombankGroupSelected
326. _auth==0 && shbGroupSelected
327. _auth==0 && onepaynapasGroupSelected
328. _auth==0 && bankaccountGroupSelected
329. _auth==0 && vibbankGroupSelected
330. _auth==1 && _b != 16
331. 'expired' == this._res_polling.state)
332. } else if ('paid' == this._res_polling.state) {
333. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
334. } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
335. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
336. this._auth == 0) {
337. if (count == 2
338. if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
339. this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
340. if (this._res.payments[this._res.payments.length - 1].state == 'approved'
341. } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
342. } else if (this._res.payments != null && (this._res.payments[0].state == 'authorization_required'
343. this._res.payments[0].state == 'more_info_required')
344. if (idBrand == 'atm'
345. if ('op' == auth
346. } else if ('bank' == auth
347. this._res.payments[this._res.payments.length - 1].state == 'pending'
348. if ('paid' == this._res.state) {
349. this._res.payments[this._res.payments.length - 1].state == 'pending') {
350. if (this._res.currencies[0] == 'USD') {
351. if (item.b.id == '2'
352. item.b.id == '67') {
353. $event == 'true') {
354. _b: this._b == '2' ? '67' : this._b
355. if (bankid == 2
356. bankid == 67) {
357. || (off && !this.enabledTwoBankTech && ((bankid == '2'
358. this.isOffTechcombank) || (bankid == '67'
359. bankid == 31) {
360. if (bankId == 1
361. bankId == 4
362. bankId == 7
363. bankId == 11
364. bankId == 14
365. bankId == 15
366. bankId == 16
367. bankId == 20
368. bankId == 22
369. bankId == 24
370. bankId == 25
371. bankId == 30
372. bankId == 33
373. bankId == 34
374. bankId == 35
375. bankId == 36
376. bankId == 41
377. bankId == 43
378. bankId == 45
379. bankId == 46
380. bankId == 47
381. bankId == 50
382. bankId == 57
383. bankId == 62
384. bankId == 63
385. bankId == 69
386. bankId == 70
387. bankId == 71
388. bankId == 73
389. bankId == 32
390. bankId == 75) {
391. } else if (bankId == 6
392. bankId == 31
393. bankId == 80) {
394. } else if (bankId == 2
395. bankId == 67) {
396. } else if (bankId == 3
397. bankId == 18
398. bankId == 19
399. bankId == 27) {
400. } else if (bankId == 5) {
401. } else if (bankId == 12) {
402. this._b == '55'
403. this._b == '47'
404. this._b == '48'
405. this._b == '59'
406. this._b == '19'
407. this._b == '12') {
408. this._b == '3'
409. this._b == '43'
410. this._b == '45'
411. this._b == '73'
412. this._b == '57'
413. this._b == '61'
414. this._b == '63'
415. this._b == '67'
416. this._b == '68'
417. this._b == '69'
418. this._b == '72'
419. this._b == '9'
420. this._b == '74'
421. this._b == '75') {
422. this._b == '36'
423. if (item['id'] == this._b) {
424. this._state == 'completed') return of([]);
425. if(this.currentLang=='en') curr_lang='LANG_EN_NAME';
426. else if(this.currentLang=='vi') curr_lang='LANG_VI_NAME';
427. if (+e.id == bankId) {
428. if (e.swiftCode == bankSwift) {
429. if (c.length == 3) {
430. return results == null ? null : results[1]
431. if (this.checkCount == 1) {
432. if (results == null) {
433. d = d == undefined ? '.' : d
434. t = t == undefined ? '
435. if (((_val.length == 4
436. _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
437. _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
438. iss_date.length == 4
439. iss_date.search('/') == -1)
440. iss_date.length == 5))
441. if (_dataCache == null) {
442. if ( (0 <= r && r <= 6 && (c == 0
443. c == 6) )
444. || (0 <= c && c <= 6 && (r == 0
445. r == 6) )
446. if (i == 0
447. _modules[r][6] = (r % 2 == 0);
448. _modules[6][c] = (c % 2 == 0);
449. if (r == -2
450. r == 2
451. c == -2
452. c == 2
453. || (r == 0
454. c == 0) ) {
455. ( (bits >> i) & 1) == 1);
456. if (col == 6) col -= 1;
457. if (_modules[row][col - c] == null) {
458. dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
459. if (bitIndex == -1) {
460. margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
461. if (typeof arguments[0] == 'object') {
462. margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
463. if (b == -1) throw 'eof';
464. if (b0 == -1) break;
465. if (typeof b == 'number') {
466. if ( (b & 0xff) == b) {
467. return function(i, j) { return (i + j) % 2 == 0
468. return function(i, j) { return i % 2 == 0
469. return function(i, j) { return j % 3 == 0
470. return function(i, j) { return (i + j) % 3 == 0
471. return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
472. return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
473. return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
474. return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
475. if (r == 0
476. c == 0) {
477. if (dark == qrcode.isDark(row + r, col + c) ) {
478. if (count == 0
479. count == 4) {
480. if (typeof num.length == 'undefined') {
481. num[offset] == 0) {
482. if (typeof rsBlock == 'undefined') {
483. return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
484. _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
485. if (data.length - i == 1) {
486. } else if (data.length - i == 2) {
487. } else if (n == 62) {
488. } else if (n == 63) {
489. if (_buflen == 0) {
490. if (c == '=') {
491. } else if (c == 0x2b) {
492. } else if (c == 0x2f) {
493. if (table.size() == (1 << bitLength) ) {
494. if(checkIE[0]=='MSIE'

!== (22 điều kiện duy nhất):
------------------------------------------------------------
1. event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
2. key !== '3') {
3. codeResponse.toString() !== '0') {
4. cardNo.length !== 0) {
5. if (this.cardTypeBank !== 'bank_card_number') {
6. if (this.cardTypeBank !== 'bank_account_number') {
7. if (this.cardTypeBank !== 'bank_username') {
8. if (this.cardTypeBank !== 'bank_customer_code') {
9. this.lb_card_account !== this._translate.instant('ocb_account')) {
10. this.lb_card_account !== this._translate.instant('ocb_phone')) {
11. this.lb_card_account !== this._translate.instant('ocb_card')) {
12. this._b !== 18) || (this.cardTypeOcean === 'ATM'
13. let _b = this._b !== 67 ? 67 : this._b
14. if (this.cardTypeBank !== 'internet_banking') {
15. this._b !== 18)) {
16. this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
17. this.lastValue !== $event.target.value)) {
18. if (YY % 400 === 0 || (YY % 100 !== 0
19. if (YYYY % 400 === 0 || (YYYY % 100 !== 0
20. this.bankList = this.bankList.filter(item => item.b.id !== '67');
21. queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
22. if (queryString !== '') {

!= (181 điều kiện duy nhất):
------------------------------------------------------------
1. if(params['locale']!=null
2. } else if(params['locale']!=null
3. if(userLang!=null
4. if (params['locale'] != null) {
5. if (this.current != 'card') {
6. if (this.current != 'date') {
7. if (this.current != 'name') {
8. if (this._res.authorization != null
9. this._res.authorization.links != null
10. if (this._res.links != null
11. this._res.links.cancel != null) {
12. if (!(_formCard.name != null
13. } else if (this._b != 18) {
14. if (this.htmlDesc != null
15. if (this._res_post != null
16. this._res_post.links != null
17. this._res_post.links.merchant_return != null
18. this._res_post.links.merchant_return.href != null) {
19. if (ua.indexOf('safari') != -1
20. if (_val.value != '') {
21. this.auth_method != null) {
22. if (this.valueDate.length != 3) {
23. if (_formCard.exp_date != null
24. if (this.cardName != null
25. if (this._res_post.links != null
26. if (this._res_post.authorization != null
27. this._res_post.authorization.links != null
28. this._res_post.authorization.links.approval != null) {
29. this._res_post.links.cancel != null) {
30. this._b != 27
31. this._b != 12
32. this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
33. this._b != 18)
34. if (this._b != 18
35. this._b != 19) {
36. if (this._inExpDate.length != 3) {
37. if (this._res_post.return_url != null) {
38. let userName = _formCard.name != null ? _formCard.name : ''
39. this._res_post.authorization.links.approval != null
40. this._res_post.authorization.links.approval.href != null) {
41. userName = paramUserName != null ? paramUserName : ''
42. this._b != 3))
43. if (this._b != 68
44. this._b != 2
45. this._b != 20
46. this._b != 33
47. this._b != 39
48. this._b != 43
49. this._b != 45
50. this._b != 64
51. this._b != 67
52. this._b != 68
53. this._b != 72)
54. this._res.links.merchant_return != null
55. this._res.links.merchant_return.href != null) {
56. if (!(_formCard.otp != null
57. if (!(_formCard.password != null
58. if ( this._b != 9
59. this._b != 16
60. this._b != 17
61. this._b != 25
62. this._b != 44
63. this._b != 57
64. this._b != 59
65. this._b != 61
66. this._b != 63
67. this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
68. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72' , '73', '74', '75'].indexOf(this._b.toString()) != -1) {
69. if(value!=null){
70. timeLeft != 'Infinity'"
71. if (_re.body != null
72. _re.body.links != null
73. _re.body.links.merchant_return != null
74. _re.body.links.merchant_return.href != null) {
75. if (this._idInvoice != null
76. this._idInvoice != 0) {
77. if (this._paymentService.getInvoiceDetail() != null) {
78. dataPassed.body != null) {
79. <img src="assets/img/banklogo/{{cbtest.value!=null?cbtest.value:combobankValue}}_logo.png" style="height:25px;margin-right: 10px;"/>{{cbtest.value!=null?getBankName(cbtest.value):getBankName(combobankValue)}}
80. if (this._idInvoice != null) {
81. _re.body != null) {
82. this._res_polling.links != null
83. this._res_polling.links.merchant_return != null //
84. } else if (this._res_polling.merchant != null
85. this._res_polling.merchant_invoice_reference != null
86. } else if (this._res_polling.payments != null
87. this._res_polling.links.merchant_return != null//
88. this.d_vrbank = (this._res.qr_data != null
89. if (this._res.merchant != null
90. this._res.merchant_invoice_reference != null) {
91. if (this._res.merchant.address_details != null) {
92. if (this._res.billing != null
93. this._res.billing.address != null) {
94. if (this._res.billing.address.city != null) this._i_city = this._res.billing.address.city;
95. if (this._res.billing.address.line1 != null) this._i_address = this._res.billing.address.line1;
96. if (this._res.billing.address.postal_code != null) this._i_postal_code = this._res.billing.address.postal_code;
97. if (this._res.billing.address.state != null) this._i_state = this._res.billing.address.state;
98. if (this._res.billing.address.country_code != null) {
99. this._res.links != null
100. this._res.links.merchant_return != null //
101. } else if (this._res.merchant != null
102. this._res.merchant_invoice_reference != null
103. } else if (this._res.payments != null
104. this._res.links.merchant_return != null//
105. if (imgCard != null) imgCard.src = imgCardSrc;
106. _val.value != null
107. this._res_post.links.merchant_return.href != null){
108. _formCard.csc != null
109. if (!(!isNaN(_formCard.country) != null
110. if (!(_formCard.address != null
111. if (!(_formCard.province != null
112. if (this.current != 'csc') {
113. if (bankid != null) {
114. if (!(strInstrument != null
115. if (this._b != 9
116. this._b != 11
117. this._b != 25)
118. if (this.current_f1 != 'card') {
119. if (this.current_f1 != 'date') {
120. if (this.current_f1 != 'name') {
121. this._b != 25) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
122. if (this._res_post.return_url != null)
123. this._res_post.authorization.links.approval != null)
124. this._res_post.links.cancel != null)
125. var userName = _formCard.name != null ? _formCard.name : ''
126. userName = myParam != null ? myParam : ''
127. this._b != 25) {//9vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
128. this._b != 20) || ((dCardExp.getTime() - dCurrent.getTime()) < 0
129. this._paymentService.getState() != 'error') {
130. if(this._res_polling.links != null
131. this._res_polling.links.merchant_return != null){
132. if ('otp' != this._paymentService.getCurrentPage()) {
133. this._res.merchant.id != 'OPTEST' ? this._res.on_off_bank : ''
134. if (count != 1) {
135. this._res.links != null//
136. this._res.payments[0].instrument != null
137. this._res.payments[0].instrument.issuer != null
138. this._res.payments[0].instrument.issuer.brand != null
139. this._res.payments[0].instrument.issuer.brand.id != null
140. this._res.payments[0].links != null
141. this._res.payments[0].links.cancel != null
142. this._res.payments[0].links.cancel.href != null
143. this._res.payments[0].instrument.issuer.swift_code != null
144. this._res.payments[0].authorization != null
145. this._res.payments[0].authorization.links != null) {
146. this._res.payments[0].authorization.links.approval != null
147. this._res.payments[0].authorization.links.approval.href != null) {
148. if (this._res.payments[0].authorization != null
149. this._res.payments[0].authorization.links != null
150. auth = paramUserName != null ? paramUserName : ''
151. if (strInstrument.substring(0, 1) != '^'
152. strInstrument.substr(strInstrument.length - 1) != '$') {
153. bankid != 31) {
154. if (idInvoice != null
155. idInvoice != 0)
156. if (this._merchantid != null
157. this._tranref != null
158. this._state != null
159. if (this._state != null
160. } else if (idInvoice != null
161. idInvoice != 0) {
162. let urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
163. urlCalcel = this.getUrlCancel() + (idInvoice != null ? idInvoice : '');
164. if (tem != null) {
165. if ((tem = ua.match(/version\/(\d+)/i)) != null) {
166. if (v.length != 3) {
167. if (_modules[r][6] != null) {
168. if (_modules[6][c] != null) {
169. if (_modules[row][col] != null) {
170. while (buffer.getLengthInBits() % 8 != 0) {
171. if (count != numChars) {
172. throw count + ' != ' + numChars
173. while (data != 0) {
174. if (test.length != 2
175. ( (test[0] << 8) | test[1]) != code) {
176. if (_length % 3 != 0) {
177. if ( (data >>> length) != 0) {
178. return typeof _map[key] != 'undefined'
179. document.documentMode!=null
180. if(tem!= null) return tem.slice(1).join(' ').replace('OPR'
181. if((tem= ua.match(/version\/(\d+)/i))!= null) M.splice(1

