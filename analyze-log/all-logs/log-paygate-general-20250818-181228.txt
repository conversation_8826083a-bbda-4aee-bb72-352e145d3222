====================================================================================================
TRÍCH XUẤT ĐIỀU KIỆN SO SÁNH TỪ CÁC FILE TYPESCRIPT/JAVASCRIPT/HTML
====================================================================================================
Thư mục/File nguồn: /root/projects/onepay/paygate-general/src
Thời gian: 18:12:28 18/8/2025
Tổng số file xử lý: 218
Tổng số file bị bỏ qua: 3
Tổng số điều kiện tìm thấy: 1841

THỐNG KÊ TỔNG QUAN THEO LOẠI TOÁN TỬ:
--------------------------------------------------
Strict equality (===): 378 lần
Loose equality (==): 1016 lần
Strict inequality (!==): 76 lần
Loose inequality (!=): 371 lần
--------------------------------------------------

DANH SÁCH FILE ĐÃ XỬ LÝ:
--------------------------------------------------
1. 4en.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/4en.html
2. 4vn.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/4vn.html
3. app-routing.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/app-routing.module.ts
4. app.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/app.component.html
5. app.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/app.component.spec.ts
6. app.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/app.component.ts
7. app.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/app.module.ts
8. PaymentType.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/constants/PaymentType.ts
9. counter.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/counter.directive.spec.ts
10. counter.directive.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/counter.directive.ts
11. format-carno-input.derective.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/directives/format-carno-input.derective.ts
12. uppercase-input.directive.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/directives/uppercase-input.directive.ts
13. error.component.html (18 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/error/error.component.html
14. error.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/error/error.component.spec.ts
15. error.component.ts (68 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/error/error.component.ts
16. support-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/error/support-dialog/support-dialog.html
17. support-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/error/support-dialog/support-dialog.ts
18. format-date.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/format-date.directive.spec.ts
19. format-date.directive.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/format-date.directive.ts
20. main.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/main.component.html
21. main.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/main.component.spec.ts
22. main.component.ts (13 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/main.component.ts
23. app-result.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/app-result/app-result.component.html
24. app-result.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/app-result/app-result.component.ts
25. bank-amount-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
26. bank-amount-dialog.component.ts (34 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
27. amigo-form.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/amigo/amigo-form.component.html
28. amigo-form.component.ts (24 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/amigo/amigo-form.component.ts
29. bnpl-main.component.html (19 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/bnpl-main.component.html
30. bnpl-main.component.ts (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/bnpl-main.component.ts
31. circle-process-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/dialog/circle-process-dialog.html
32. dialog-delete-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/fundiin/dialog-delete-dialog.html
33. dialog-policy.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/fundiin/dialog-policy/dialog-policy.component.html
34. dialog-policy.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/fundiin/dialog-policy/dialog-policy.component.spec.ts
35. dialog-policy.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/fundiin/dialog-policy/dialog-policy.component.ts
36. fundiin-form.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/fundiin/fundiin-form.component.html
37. fundiin-form.component.ts (23 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/fundiin/fundiin-form.component.ts
38. select-data.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/fundiin/select-data/select-data.html
39. select-data.ts (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/fundiin/select-data/select-data.ts
40. dialog-delete-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/homecredit/dialog-delete-dialog.html
41. dialog-policy.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/homecredit/dialog-policy/dialog-policy.component.html
42. dialog-policy.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/homecredit/dialog-policy/dialog-policy.component.spec.ts
43. dialog-policy.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/homecredit/dialog-policy/dialog-policy.component.ts
44. homecredit-form.component.html (10 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/homecredit/homecredit-form.component.html
45. homecredit-form.component.ts (40 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/homecredit/homecredit-form.component.ts
46. dialog-delete-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/kbank/dialog-delete-dialog.html
47. kbank-dialog-policy.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/kbank/kbank-dialog-policy/kbank-dialog-policy.component.html
48. kbank-dialog-policy.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/kbank/kbank-dialog-policy/kbank-dialog-policy.component.spec.ts
49. kbank-dialog-policy.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/kbank/kbank-dialog-policy/kbank-dialog-policy.component.ts
50. kbank-form.component.html (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/kbank/kbank-form.component.html
51. kbank-form.component.ts (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/kbank/kbank-form.component.ts
52. kredivo-form.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/kredivo/kredivo-form.component.html
53. kredivo-form.component.ts (24 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/kredivo/kredivo-form.component.ts
54. otp-auth-bnpl.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/otp-auth-bnpl/otp-auth-bnpl.component.html
55. otp-auth-bnpl.component.ts (12 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/otp-auth-bnpl/otp-auth-bnpl.component.ts
56. process-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/process-dialog/process-dialog.html
57. process-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/process-dialog/process-dialog.ts
58. select-bnpl.html (12 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/select-bnpl-dialog/select-bnpl.html
59. select-bnpl.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/select-bnpl-dialog/select-bnpl.ts
60. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/dialog-guide-dialog.html
61. bankaccount.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
62. bankaccount.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
63. bankaccount.component.ts (153 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
64. bank.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/model/bank.ts
65. onepay-napas.component.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.html
66. onepay-napas.component.ts (104 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.ts
67. otp-auth.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
68. otp-auth.component.ts (18 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
69. shb.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/shb/shb.component.html
70. shb.component.ts (66 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/shb/shb.component.ts
71. techcombank.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
72. techcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
73. techcombank.component.ts (17 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
74. vibbank.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.html
75. vibbank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.spec.ts
76. vibbank.component.ts (99 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.ts
77. vietcombank.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
78. vietcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
79. vietcombank.component.ts (91 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
80. vietqr-domes.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/vietqr-domes/vietqr-domes.component.html
81. vietqr-domes.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/vietqr-domes/vietqr-domes.component.ts
82. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
83. off-bank-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
84. off-bank-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
85. domescard-main.component.html (10 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/domescard-main.component.html
86. domescard-main.component.ts (151 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/domescard-main.component.ts
87. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/intercard-main/dialog-guide-dialog.html
88. intercard-main-form.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/intercard-main/form/intercard-main-form.component.html
89. intercard-main-form.component.ts (73 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/intercard-main/form/intercard-main-form.component.ts
90. intercard-main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/intercard-main/intercard-main.component.html
91. intercard-main.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/intercard-main/intercard-main.component.ts
92. menu.component.html (55 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/menu.component.html
93. menu.component.ts (189 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/menu.component.ts
94. applepay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/applepay/applepay.component.html
95. applepay.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/applepay/applepay.component.ts
96. dialog-network-not-supported.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/applepay/dialog-network-not-supported/dialog-network-not-supported.component.html
97. dialog-network-not-supported.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/applepay/dialog-network-not-supported/dialog-network-not-supported.component.ts
98. google-pay-button-op.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/googlepay/google-pay-button-op/google-pay-button-op.component.html
99. google-pay-button-op.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/googlepay/google-pay-button-op/google-pay-button-op.component.ts
100. types-google-pay.d.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/googlepay/google-pay-button-op/types-google-pay.d.ts
101. googlepay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/googlepay/googlepay.component.html
102. googlepay.component.ts (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/googlepay/googlepay.component.ts
103. mobile-wallet-main.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/mobile-wallet-main.component.html
104. mobile-wallet-main.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/mobile-wallet-main.component.ts
105. samsung-pay-button-op.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.html
106. samsung-pay-button-op.component.ts (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.ts
107. types-samsung-pay.d.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/types-samsung-pay.d.ts
108. samsungpay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/samsungpay/samsungpay.component.html
109. samsungpay.component.ts (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/samsungpay/samsungpay.component.ts
110. policy-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/policy-dialog/policy-dialog/policy-dialog.component.html
111. policy-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/policy-dialog/policy-dialog/policy-dialog.component.ts
112. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
113. qr-dialog.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
114. qr-dialog.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
115. qr-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main/qr-guide-dialog/qr-guide-dialog.html
116. qr-guide-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main/qr-guide-dialog/qr-guide-dialog.ts
117. qr-main.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main/qr-main.component.html
118. qr-main.component.ts (26 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main/qr-main.component.ts
119. safe-html.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main/safe-html.pipe.ts
120. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-v2/dialog/dialog-guide-dialog.html
121. qr-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-v2/qr-dialog-v2/qr-dialog.html
122. qr-dialog.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-v2/qr-dialog-v2/qr-dialog.ts
123. list-bank-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-v2/qr-listbank/list-bank-dialog.html
124. list-bank-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-v2/qr-listbank/list-bank-dialog.ts
125. qr-main-v2.component.html (13 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-v2/qr-main-v2.component.html
126. qr-main-v2.component.ts (35 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-v2/qr-main-v2.component.ts
127. qr-vnpay-dialog.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-v2/qr-vnpay-dialog-v2/qr-vnpay-dialog.html
128. qr-vnpay-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-v2/qr-vnpay-dialog-v2/qr-vnpay-dialog.ts
129. safe-html.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-v2/safe-html.pipe.ts
130. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/dialog/dialog-guide-dialog.html
131. qr-desktop.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.html
132. qr-desktop.component.ts (19 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.ts
133. qr-dialog-version2.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog-version2.html
134. qr-dialog-version2.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog-version2.ts
135. qr-guide-dialog-version2.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog-version2.html
136. qr-guide-dialog-version2.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog-version2.ts
137. list-bank-dialog-version2.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog-version2.html
138. list-bank-dialog-version2.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog-version2.ts
139. qr-main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-main.component.html
140. qr-main.component.ts (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-main.component.ts
141. qr-mobile.component.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.html
142. qr-mobile.component.ts (30 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.ts
143. safe-html.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/safe-html.pipe.ts
144. queuing.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/queuing/queuing.component.html
145. queuing.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/queuing/queuing.component.ts
146. bnpl-form.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/bnpl-form/bnpl-form.component.html
147. bnpl-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/bnpl-form/bnpl-form.component.ts
148. domescard-form.component.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.html
149. domescard-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.ts
150. intercard-form.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.html
151. intercard-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.ts
152. mobile-wallet-form.component.html (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/mobile-wallet-form/mobile-wallet-form.component.html
153. mobile-wallet-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/mobile-wallet-form/mobile-wallet-form.component.ts
154. paypal-form.component.html (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.html
155. paypal-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.ts
156. qr-form.component.html (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/qr-form/qr-form.component.html
157. qr-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/qr-form/qr-form.component.ts
158. vietqr-form.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/vietqr-form/vietqr-form.component.html
159. vietqr-form.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/vietqr-form/vietqr-form.component.ts
160. vietqr-deeplink.component.html (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/vietqr-main/vietqr-deeplink/vietqr-deeplink.component.html
161. vietqr-deeplink.component.ts (7 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/vietqr-main/vietqr-deeplink/vietqr-deeplink.component.ts
162. vietqr-guide.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/vietqr-main/vietqr-guide/vietqr-guide.component.html
163. vietqr-guide.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/vietqr-main/vietqr-guide/vietqr-guide.component.ts
164. vietqr-listbank.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/vietqr-main/vietqr-listbank/vietqr-listbank.component.html
165. vietqr-listbank.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/vietqr-main/vietqr-listbank/vietqr-listbank.component.ts
166. vietqr-main.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/vietqr-main/vietqr-main.component.html
167. vietqr-main.component.ts (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/vietqr-main/vietqr-main.component.ts
168. bnpl-management.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/model/bnpl-management.ts
169. fundiin-management.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/model/fundiin-management.ts
170. homecredit-management.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/model/homecredit-management.ts
171. kbank-management.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/model/kbank-management.ts
172. overlay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/overlay/overlay.component.html
173. overlay.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/overlay/overlay.component.spec.ts
174. overlay.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/overlay/overlay.component.ts
175. bank-amount.pipe.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/pipe/bank-amount.pipe.ts
176. auth.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/auth.service.ts
177. close-dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/close-dialog.service.ts
178. currency-service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/currency-service.ts
179. data.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/data.service.ts
180. deep_link.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/deep_link.service.ts
181. dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/dialog.service.ts
182. digital-wallet.service.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/digital-wallet.service.ts
183. handle_bnpl_token.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/handle_bnpl_token.service.ts
184. multiple_method.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/multiple_method.service.ts
185. payment.service.ts (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/payment.service.ts
186. qr.service.ts (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/qr.service.ts
187. time-stop.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/time-stop.service.ts
188. vietqr.service.ts (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/vietqr.service.ts
189. success.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/success/success.component.html
190. success.component.ts (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/success/success.component.ts
191. index.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/translate/index.ts
192. lang-en.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/translate/lang-en.ts
193. lang-vi.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/translate/lang-vi.ts
194. translate.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/translate/translate.pipe.ts
195. translate.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/translate/translate.service.ts
196. translations.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/translate/translations.ts
197. apps-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/util/apps-info.ts
198. apps-information.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/util/apps-information.ts
199. apps-infov2.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/util/apps-infov2.ts
200. banks-info.ts (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/util/banks-info.ts
201. error-handler.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/util/error-handler.ts
202. iso-ca-states.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/util/iso-ca-states.ts
203. iso-us-states.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/util/iso-us-states.ts
204. util.ts (39 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/app/util/util.ts
205. apple.js (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/assets/script/apple.js
206. google-pay-intergrate.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/assets/script/google-pay-intergrate.js
207. qrcode.js (67 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/assets/script/qrcode.js
208. environment.dev.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/environments/environment.dev.ts
209. environment.mtf.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/environments/environment.mtf.ts
210. environment.prod.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/environments/environment.prod.ts
211. environment.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/environments/environment.ts
212. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/index.html
213. karma.conf.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/karma.conf.js
214. main.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/main.ts
215. polyfills.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/polyfills.ts
216. kbank-policy.component.html (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/template/kbank-policy/kbank-policy.component.html
217. kbank-policy.component.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/template/kbank-policy/kbank-policy.component.ts
218. test.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-general/src/test.ts

DANH SÁCH FILE BỊ BỎ QUA:
--------------------------------------------------
1. apple-pay-sdk.js
   Đường dẫn: /root/projects/onepay/paygate-general/src/assets/script/apple-pay-sdk.js
2. google-pay-sdk.*************.js
   Đường dẫn: /root/projects/onepay/paygate-general/src/assets/script/google-pay-sdk.*************.js
3. samsung-pay-sdk.js
   Đường dẫn: /root/projects/onepay/paygate-general/src/assets/script/samsung-pay-sdk.js

====================================================================================================
CHI TIẾT TỪNG FILE:
====================================================================================================

📁 FILE 1: 4en.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/4en.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 2: 4vn.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/4vn.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 3: app-routing.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/app-routing.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 4: app.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/app.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 5: app.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/app.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 6: app.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/app.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 83] return lang === this._translate.currentLang

== (2 điều kiện):
  1. [Dòng 62] 'vi' == params['locale']) {
  2. [Dòng 64] 'en' == params['locale']) {

!= (3 điều kiện):
  1. [Dòng 62] if (params['locale'] != null
  2. [Dòng 64] } else if (params['locale'] != null
  3. [Dòng 71] if (userLang != null

================================================================================

📁 FILE 7: app.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/app.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 8: PaymentType.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/constants/PaymentType.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 9: counter.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/counter.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 10: counter.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/counter.directive.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 11: format-carno-input.derective.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/directives/format-carno-input.derective.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 44] this.lastValue !== $event.target.value)) {

!= (1 điều kiện):
  1. [Dòng 23] if(value!=null){

================================================================================

📁 FILE 12: uppercase-input.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/directives/uppercase-input.directive.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 0 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 23] this.lastValue !== $event.target.value)) {

================================================================================

📁 FILE 13: error.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/error/error.component.html
📊 Thống kê: 18 điều kiện duy nhất
   - === : 4 lần
   - == : 14 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 94] checkDupTran === false"
  2. [Dòng 140] isPopupSupport === 'True') || (rePayment
  3. [Dòng 141] isPopupSupport === 'True'"
  4. [Dòng 150] isPopupSupport === 'True')">

== (14 điều kiện):
  1. [Dòng 16] errorCode == '11'"
  2. [Dòng 34] isappleerror ==false
  3. [Dòng 37] isappleerror==true
  4. [Dòng 51] errorCode && (errorCode == '253' || errorCode == 'overtime')
  5. [Dòng 51] errorCode == 'overtime')">
  6. [Dòng 58] errorCode == 'INVALID_CARD_LIST'"
  7. [Dòng 64] errorCode == 'INVALID_CARD_FEE'"
  8. [Dòng 140] *ngIf="(isSent == false
  9. [Dòng 141] isSent == false
  10. [Dòng 150] [class.select_only]="!(isSent == false
  11. [Dòng 178] <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
  12. [Dòng 178] (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
  13. [Dòng 180] <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
  14. [Dòng 180] !(errorCode == 'overtime' || errorCode == '253') && isBack && !invoiceNearExpire && !paymentPending

================================================================================

📁 FILE 14: error.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/error/error.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 15: error.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/error/error.component.ts
📊 Thống kê: 68 điều kiện duy nhất
   - === : 15 lần
   - == : 37 lần
   - !== : 2 lần
   - != : 14 lần
--------------------------------------------------------------------------------

=== (15 điều kiện):
  1. [Dòng 209] params.timeout === 'true') {
  2. [Dòng 218] params.kbank === 'true') {
  3. [Dòng 235] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  4. [Dòng 235] _re.body.state === 'unpaid');
  5. [Dòng 376] if (this.errorCode === 'overtime'
  6. [Dòng 376] this.errorCode === '253') {
  7. [Dòng 493] params.name === 'CUSTOMER_INTIME'
  8. [Dòng 493] params.code === '09') {
  9. [Dòng 508] params.name === 'INVALID_CARD_LIST'
  10. [Dòng 508] params.code === '10') {
  11. [Dòng 518] params.name === 'INVALID_CARD_FEE'
  12. [Dòng 518] params.code === '8') {
  13. [Dòng 549] if (this.maxpayment === this.paymentsNum) {
  14. [Dòng 622] if (param === key) {
  15. [Dòng 672] if (this.timeLeft === 0) {

== (37 điều kiện):
  1. [Dòng 193] if (params && (params['bnpl'] == 'true')) {
  2. [Dòng 197] if (params && (params['bnpl'] == 'false')) {
  3. [Dòng 236] if (_re.body.state == 'closed')
  4. [Dòng 296] if (this.paymentInformation.type == "applepay_napas") {
  5. [Dòng 306] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'amigo') {
  6. [Dòng 313] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'kredivo') {
  7. [Dòng 318] if (this.paymentInformation.type == "applepay") {
  8. [Dòng 323] } else if (this.paymentInformation.type == "googlepay") {
  9. [Dòng 326] } else if (this.paymentInformation.type == "samsungpay") {
  10. [Dòng 347] this.res.themes.theme == 'general') {
  11. [Dòng 353] params.response_code == 'overtime') {
  12. [Dòng 399] if (_re.status == '200'
  13. [Dòng 399] _re.status == '201') {
  14. [Dòng 412] if (_re2.status == '200'
  15. [Dòng 412] _re2.status == '201') {
  16. [Dòng 429] if (this.errorCode == 'overtime'
  17. [Dòng 429] this.errorCode == '253') {
  18. [Dòng 433] } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
  19. [Dòng 436] } else if (this.errorCode == 'AM1') {
  20. [Dòng 440] if (this.paymentInformation.type == 'bnpl') {
  21. [Dòng 442] if (this.paymentInformation.provider == 'amigo'
  22. [Dòng 442] this.errorCode == '2') {
  23. [Dòng 445] else if (this.paymentInformation.provider == 'kbank'
  24. [Dòng 448] else if (this.paymentInformation.provider == 'homecredit'
  25. [Dòng 451] else if (this.paymentInformation.provider == 'kredivo'
  26. [Dòng 454] else if (this.paymentInformation.provider == 'fundiin'
  27. [Dòng 463] this.res.state == 'canceled') {
  28. [Dòng 487] if (lastPayment?.state == 'pending') {
  29. [Dòng 594] if(this.locale == 'vi')
  30. [Dòng 670] if (this.isTimePause == false) {
  31. [Dòng 689] if (this.isappleerror == true) {
  32. [Dòng 776] if (response.body.state == 'not_paid') {
  33. [Dòng 778] if (response.body.payments[response.body.payments.length - 1].state == "failed") {
  34. [Dòng 804] } else if (response.body.state == 'paid') {
  35. [Dòng 810] } else if (response.body.state == 'canceled'
  36. [Dòng 810] response.body.state == 'closed'
  37. [Dòng 810] response.body.state == 'expired') {

!== (2 điều kiện):
  1. [Dòng 617] queryString = (sourceURL.indexOf("?") !== -1) ? sourceURL.split("?")[1] : "";
  2. [Dòng 618] if (queryString !== "") {

!= (14 điều kiện):
  1. [Dòng 159] if (message != ''
  2. [Dòng 159] message != null
  3. [Dòng 159] message != undefined) {
  4. [Dòng 304] } else if (this.res.payments[this.res.payments.length - 1].instrument.issuer.brand != null
  5. [Dòng 305] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id != null
  6. [Dòng 753] if (_re != null
  7. [Dòng 753] _re.links != null
  8. [Dòng 753] _re.links.merchant_return != null
  9. [Dòng 754] _re.links.merchant_return.href != null) {
  10. [Dòng 756] } else if (_re.body != null
  11. [Dòng 756] _re.body.links != null
  12. [Dòng 756] _re.body.links.merchant_return != null
  13. [Dòng 757] _re.body.links.merchant_return.href != null) {
  14. [Dòng 826] if (this.url_new_invoice != null) {

================================================================================

📁 FILE 16: support-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/error/support-dialog/support-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 17: support-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/error/support-dialog/support-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 18: format-date.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/format-date.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 19: format-date.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/format-date.directive.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0
  2. [Dòng 123] YY % 4 === 0)) {
  3. [Dòng 138] if (YYYY % 400 === 0
  4. [Dòng 138] YYYY % 4 === 0)) {

!== (2 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0 || (YY % 100 !== 0
  2. [Dòng 138] if (YYYY % 400 === 0 || (YYYY % 100 !== 0

================================================================================

📁 FILE 20: main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/main.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 1 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 28] checkDupTran === false"

================================================================================

📁 FILE 21: main.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/main.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 22: main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/main.component.ts
📊 Thống kê: 13 điều kiện duy nhất
   - === : 2 lần
   - == : 5 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 146] params['code']==='09'){
  2. [Dòng 160] params.name === 'CUSTOMER_INTIME')) {

== (5 điều kiện):
  1. [Dòng 108] if ((dataPassed.status == '200'
  2. [Dòng 108] dataPassed.status == '201') && dataPassed.body != null) {
  3. [Dòng 112] dataPassed.body.themes.logo_full == 'True') {
  4. [Dòng 137] payments[payments.length - 1].state == 'pending'
  5. [Dòng 139] payments[payments.length - 1].instrument.issuer.brand.id == 'amigo') {

!= (6 điều kiện):
  1. [Dòng 99] if (this._idInvoice != null
  2. [Dòng 99] this._idInvoice != 0) {
  3. [Dòng 100] if (this._paymentService.getInvoiceDetail() != null) {
  4. [Dòng 108] dataPassed.body != null) {
  5. [Dòng 131] dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
  6. [Dòng 202] if (this._translate.currentLang != language) {

================================================================================

📁 FILE 23: app-result.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/app-result/app-result.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 24: app-result.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/app-result/app-result.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 25: bank-amount-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 26: bank-amount-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
📊 Thống kê: 34 điều kiện duy nhất
   - === : 0 lần
   - == : 34 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (34 điều kiện):
  1. [Dòng 41] if (this.locale == 'en') {
  2. [Dòng 55] if (name == 'MAFC')
  3. [Dòng 61] if (bankId == 3
  4. [Dòng 61] bankId == 61
  5. [Dòng 62] bankId == 8
  6. [Dòng 62] bankId == 49
  7. [Dòng 63] bankId == 48
  8. [Dòng 64] bankId == 10
  9. [Dòng 64] bankId == 53
  10. [Dòng 65] bankId == 17
  11. [Dòng 65] bankId == 65
  12. [Dòng 66] bankId == 23
  13. [Dòng 66] bankId == 52
  14. [Dòng 67] bankId == 27
  15. [Dòng 67] bankId == 66
  16. [Dòng 68] bankId == 9
  17. [Dòng 68] bankId == 54
  18. [Dòng 69] bankId == 37
  19. [Dòng 70] bankId == 38
  20. [Dòng 71] bankId == 39
  21. [Dòng 72] bankId == 40
  22. [Dòng 73] bankId == 42
  23. [Dòng 74] bankId == 44
  24. [Dòng 75] bankId == 72
  25. [Dòng 76] bankId == 59
  26. [Dòng 79] bankId == 51
  27. [Dòng 80] bankId == 64
  28. [Dòng 81] bankId == 58
  29. [Dòng 82] bankId == 56
  30. [Dòng 85] bankId == 55
  31. [Dòng 86] bankId == 60
  32. [Dòng 87] bankId == 68
  33. [Dòng 88] bankId == 74
  34. [Dòng 89] bankId == 75 //MAFC Napas

================================================================================

📁 FILE 27: amigo-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/amigo/amigo-form.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 31] bnplDetail.method == 'SP'"
  2. [Dòng 49] bnplDetail.method == 'PL'"

!= (3 điều kiện):
  1. [Dòng 3] selectedBnpl.status != 'disabled'"
  2. [Dòng 45] bnplDetail.method != 'SP'"
  3. [Dòng 63] bnplDetail.method != 'PL'"

================================================================================

📁 FILE 28: amigo-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/amigo/amigo-form.component.ts
📊 Thống kê: 24 điều kiện duy nhất
   - === : 2 lần
   - == : 17 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 196] return index === array.findIndex(obj => {
  2. [Dòng 197] return JSON.stringify(obj) === _value

== (17 điều kiện):
  1. [Dòng 90] _re.code == '0'
  2. [Dòng 127] packageItem.product_code == productCode) {
  3. [Dòng 145] a.product_code == 'SP') {
  4. [Dòng 158] a.product_code == 'PL') {
  5. [Dòng 207] if (this.selectedIndex == 0
  6. [Dòng 211] } else if ((this.selectedIndex == 1
  7. [Dòng 211] this.payLaterSubmit) || (this.selectedIndex == 0
  8. [Dòng 214] item.prepaid_percent == this.selectedPrePaid
  9. [Dòng 214] item.installment_month == this.selectedPayLater) {
  10. [Dòng 251] if (string == 'SP') {
  11. [Dòng 254] } else if (string == 'PL') {
  12. [Dòng 298] if(this._locale == 'en'){
  13. [Dòng 308] if (this._res_post.state == 'approved'
  14. [Dòng 308] this._res_post.state == 'failed') {
  15. [Dòng 314] if (this._res_post.state == 'failed') {
  16. [Dòng 339] } else if (this._res_post.state == 'authorization_required') {
  17. [Dòng 340] if ('REDIRECT' == this._res_post.authorization.links.approval.method) {

!= (5 điều kiện):
  1. [Dòng 238] 'total_paid': this.data?.amount ? (item.diff_amount !=0 ? this._util.formatMoney(parseFloat(item.diff_amount) + parseFloat(this.data?.amount), 0, '.', ',') : this._util.formatMoney(parseFloat(this.data?.amount), 0, '.', ',')) :0,
  2. [Dòng 309] if (this._res_post.return_url != null) {
  3. [Dòng 311] } else if (this._res_post.links != null
  4. [Dòng 311] this._res_post.links.merchant_return != null
  5. [Dòng 311] this._res_post.links.merchant_return.href != null) {

================================================================================

📁 FILE 29: bnpl-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/bnpl-main.component.html
📊 Thống kê: 19 điều kiện duy nhất
   - === : 2 lần
   - == : 14 lần
   - !== : 1 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 25] bnpl.code === 'kbank'"
  2. [Dòng 28] bnpl.code === 'homepaylater'"

== (14 điều kiện):
  1. [Dòng 22] bnpl.status == 'disabled'"
  2. [Dòng 23] bnpl.status == 'active'"
  3. [Dòng 23] [class.bnpl-active-item]="bnpl.status == 'active'" (click)="onClick(bnpl.status == 'disabled')">
  4. [Dòng 32] bnpl.code == 'kbank'
  5. [Dòng 35] bnpl.code == 'insta'"
  6. [Dòng 39] bnpl.code == 'homepaylater'"
  7. [Dòng 40] bnpl.status == 'disabled'
  8. [Dòng 48] bnpl.code == 'kredivo'
  9. [Dòng 63] selectedBnpl.code == 'insta'"
  10. [Dòng 68] selectedBnpl.code == 'kbank'"
  11. [Dòng 74] selectedBnpl.code == 'homepaylater'"
  12. [Dòng 78] selectedBnpl.code == 'kredivo'"
  13. [Dòng 83] selectedBnpl.code == 'fundiin'"
  14. [Dòng 147] _auth == 1"

!== (1 điều kiện):
  1. [Dòng 31] bnpl.code !== 'kbank'

!= (2 điều kiện):
  1. [Dòng 1] _auth != 1"
  2. [Dòng 31] bnpl.code != 'homepaylater'"

================================================================================

📁 FILE 30: bnpl-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/bnpl-main.component.ts
📊 Thống kê: 8 điều kiện duy nhất
   - === : 8 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 146] this.homecreditProvider = this.bankAmountArr.find(e => e.code === 'homepaylater');
  2. [Dòng 150] this.kredivoProvider = this.bankAmountArr.find(e => e.code === 'kredivo');
  3. [Dòng 153] this.fundiinProvider = this.bankAmountArr.find(e => e.code === 'fundiin');
  4. [Dòng 165] this.bnpls.push(this.sublist.find(e => e.name === 'KBank Pay Later'));
  5. [Dòng 168] this.bnpls.push(this.sublist.find(e => e.name === 'Home PayLater'));
  6. [Dòng 171] this.bnpls.push(this.sublist.find(e => e.name === 'Kredivo'));
  7. [Dòng 174] this.bnpls.push(this.sublist.find(e => e.name === 'Fundiin'));
  8. [Dòng 177] this.bnpls.push(this.sublist.find(e => e.name === 'Insta'));

================================================================================

📁 FILE 31: circle-process-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/dialog/circle-process-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 32: dialog-delete-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/fundiin/dialog-delete-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 33: dialog-policy.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/fundiin/dialog-policy/dialog-policy.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 34: dialog-policy.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/fundiin/dialog-policy/dialog-policy.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 35: dialog-policy.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/fundiin/dialog-policy/dialog-policy.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 36: fundiin-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/fundiin/fundiin-form.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

!= (3 điều kiện):
  1. [Dòng 3] selectedBnpl.status != 'disabled'"
  2. [Dòng 133] document.activeElement.id!='fullName'"
  3. [Dòng 151] document.activeElement.id!='phoneNumber'"

================================================================================

📁 FILE 37: fundiin-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/fundiin/fundiin-form.component.ts
📊 Thống kê: 23 điều kiện duy nhất
   - === : 6 lần
   - == : 7 lần
   - !== : 2 lần
   - != : 8 lần
--------------------------------------------------------------------------------

=== (6 điều kiện):
  1. [Dòng 93] if (target.tagName === 'A'
  2. [Dòng 181] params.key === 'Backspace') {
  3. [Dòng 182] if (type === 'email') {
  4. [Dòng 187] if (type === 'phoneNumber') {
  5. [Dòng 316] if (name === 'phoneNumber') {
  6. [Dòng 318] } else if (name === 'email') {

== (7 điều kiện):
  1. [Dòng 205] this.fullNameInvalidMessage = this.fundiinDetail.fullName?.length == 0 ? this._translate.instant('fill_full_name')
  2. [Dòng 213] this.fundiinDetail.email?.length == 0 ? this._translate.instant('fill_email')
  3. [Dòng 388] if (this._res_post.state == 'approved'
  4. [Dòng 388] this._res_post.state == 'failed') {
  5. [Dòng 394] if (this._res_post.state == 'failed') {
  6. [Dòng 419] } else if (this._res_post.state == 'authorization_required') {
  7. [Dòng 420] if ('REDIRECT' == this._res_post.authorization.links.approval.method) {

!== (2 điều kiện):
  1. [Dòng 149] if (this.fundiinDetail.phoneNumber && ((this.fundiinDetail.phoneNumber.length !== 10
  2. [Dòng 149] this.fundiinDetail.phoneNumber.length !== 11)

!= (8 điều kiện):
  1. [Dòng 222] if ((this.fundiinDetail.phoneNumber?.length != 10
  2. [Dòng 222] this.fundiinDetail.phoneNumber?.length != 11) || !this.fundiinDetail.phoneNumber.match(/^[*]{8,9
  3. [Dòng 227] (this.fundiinDetail.phoneNumber?.length != 10
  4. [Dòng 227] this.fundiinDetail.phoneNumber?.length != 11) ?
  5. [Dòng 389] if (this._res_post.return_url != null) {
  6. [Dòng 391] } else if (this._res_post.links != null
  7. [Dòng 391] this._res_post.links.merchant_return != null
  8. [Dòng 391] this._res_post.links.merchant_return.href != null) {

================================================================================

📁 FILE 38: select-data.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/fundiin/select-data/select-data.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 39: select-data.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/fundiin/select-data/select-data.ts
📊 Thống kê: 8 điều kiện duy nhất
   - === : 5 lần
   - == : 1 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 28] if (this.id === 'city') {
  2. [Dòng 31] this.priorityCity.push(this.dataSource.find(({ Id }) => Id === "01"));
  3. [Dòng 32] this.priorityCity.push(this.dataSource.find(({ Id }) => Id === "79"));
  4. [Dòng 34] } else if (this.id === 'district') {
  5. [Dòng 39] } else if (this.id === 'ward') {

== (1 điều kiện):
  1. [Dòng 63] if (bnpl.status == 'disabled') {

!== (2 điều kiện):
  1. [Dòng 33] this.filteredData = this.dataSource.filter(i => i.Id !== "01"
  2. [Dòng 33] i.Id !== "79").sort((a, b) => a.Name.localeCompare(b.Name));

================================================================================

📁 FILE 40: dialog-delete-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/homecredit/dialog-delete-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 41: dialog-policy.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/homecredit/dialog-policy/dialog-policy.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 42: dialog-policy.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/homecredit/dialog-policy/dialog-policy.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 43: dialog-policy.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/homecredit/dialog-policy/dialog-policy.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 44: homecredit-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/homecredit/homecredit-form.component.html
📊 Thống kê: 10 điều kiện duy nhất
   - === : 2 lần
   - == : 5 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 172] object.id === value ? 'select-option-active' : ''"
  2. [Dòng 174] object.id === value"

== (5 điều kiện):
  1. [Dòng 185] value == 'add' ? 'select-option-active' : ''"
  2. [Dòng 186] value == 'add'"
  3. [Dòng 193] value=='add'"
  4. [Dòng 302] value == 'add') || !onepayChecked.value" [disabled]="(isInvalid()
  5. [Dòng 302] value == 'add') || !onepayChecked.value">

!= (3 điều kiện):
  1. [Dòng 3] selectedBnpl.status != 'disabled'"
  2. [Dòng 228] homecreditDetail['phoneNumber'].length != 10
  3. [Dòng 228] homecreditDetail['phoneNumber'].length != 11)

================================================================================

📁 FILE 45: homecredit-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/homecredit/homecredit-form.component.ts
📊 Thống kê: 40 điều kiện duy nhất
   - === : 8 lần
   - == : 16 lần
   - !== : 1 lần
   - != : 15 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 84] if (target.tagName === 'A'
  2. [Dòng 175] params.key === 'Backspace') {
  3. [Dòng 176] if (type === 'email') {
  4. [Dòng 181] if (type === 'phoneNumber') {
  5. [Dòng 307] (key === 8
  6. [Dòng 307] key === 46
  7. [Dòng 310] (key === 46
  8. [Dòng 391] if (this.value === 'add') {

== (16 điều kiện):
  1. [Dòng 243] if (response.status == '200'
  2. [Dòng 243] response.status == '201') {
  3. [Dòng 246] this.listTokenBnpl.length == 0) {
  4. [Dòng 249] if (this.value == object.id) {
  5. [Dòng 338] if (name == 'email') {
  6. [Dòng 340] } else if (name == 'phoneNumber') {
  7. [Dòng 347] if (name == 'phoneNumber') {
  8. [Dòng 354] if (name == 'fullname') {
  9. [Dòng 357] if (name == 'email'
  10. [Dòng 429] if (this._res_post.state == 'approved'
  11. [Dòng 429] this._res_post.state == 'failed') {
  12. [Dòng 435] if (this._res_post.state == 'failed') {
  13. [Dòng 460] } else if (this._res_post.state == 'authorization_required') {
  14. [Dòng 461] if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  15. [Dòng 522] this._res_post.state == 'authorization_required') {
  16. [Dòng 592] this._res_post.code == 'KB-02') {

!== (1 điều kiện):
  1. [Dòng 544] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (15 điều kiện):
  1. [Dòng 122] this.value = (this.listTokenBnpl.length != 0) ? this.listTokenBnpl[0]['id'] : "add";
  2. [Dòng 154] if ((this.homecreditDetail['phoneNumber'].length != 10
  3. [Dòng 154] this.homecreditDetail['phoneNumber'].length != 11)
  4. [Dòng 245] this.listTokenBnpl = this.listTokenBnpl.filter(item => item.id != result.id);
  5. [Dòng 430] if (this._res_post.return_url != null) {
  6. [Dòng 432] } else if (this._res_post.links != null
  7. [Dòng 432] this._res_post.links.merchant_return != null
  8. [Dòng 432] this._res_post.links.merchant_return.href != null) {
  9. [Dòng 527] if (this._res_post.authorization != null
  10. [Dòng 527] this._res_post.authorization.links != null
  11. [Dòng 532] if (this._res_post.links != null
  12. [Dòng 532] this._res_post.links.cancel != null) {
  13. [Dòng 539] this._res_post.authorization.links.approval != null
  14. [Dòng 539] this._res_post.authorization.links.approval.href != null) {
  15. [Dòng 542] userName = paramUserName != null ? paramUserName : ''

================================================================================

📁 FILE 46: dialog-delete-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/kbank/dialog-delete-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 47: kbank-dialog-policy.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/kbank/kbank-dialog-policy/kbank-dialog-policy.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 48: kbank-dialog-policy.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/kbank/kbank-dialog-policy/kbank-dialog-policy.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 49: kbank-dialog-policy.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/kbank/kbank-dialog-policy/kbank-dialog-policy.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 50: kbank-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/kbank/kbank-form.component.html
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 5 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 98] object.id === value ? 'select-option-active' : ''"
  2. [Dòng 100] object.id === value"

== (5 điều kiện):
  1. [Dòng 110] value == 'add' ? 'select-option-active' : ''"
  2. [Dòng 111] value == 'add'"
  3. [Dòng 118] value=='add'"
  4. [Dòng 160] !cmndBlur ? 'no-border' : ((kbankDetail['citizen_id'] && !(kbankDetail['citizen_id'].length == 9 || kbankDetail['citizen_id'].length == 12) || !kbankDetail['citizen_id']) &&  bnplForm.controls['citizen_id'].touched) ? 'ng-invalid ng-dirty' : ''
  5. [Dòng 165] bnplForm.controls['citizen_id'].touched && kbankDetail['citizen_id'] && !(kbankDetail['citizen_id'].length == 9 || kbankDetail['citizen_id'].length == 12) && cmndBlur

!== (1 điều kiện):
  1. [Dòng 3] selectedBnpl.status !== 'disabled'"

!= (1 điều kiện):
  1. [Dòng 190] selectedBnpl.status != 'disabled'"

================================================================================

📁 FILE 51: kbank-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/kbank/kbank-form.component.ts
📊 Thống kê: 22 điều kiện duy nhất
   - === : 0 lần
   - == : 13 lần
   - !== : 1 lần
   - != : 8 lần
--------------------------------------------------------------------------------

== (13 điều kiện):
  1. [Dòng 81] this.value == 'add')){
  2. [Dòng 117] list = this.data.customer.tokens.filter((item) => item.instrument.issuer.brand.id == 'kbank')
  3. [Dòng 167] if (response.status == '200'
  4. [Dòng 167] response.status == '201') {
  5. [Dòng 175] this.listTokenBnpl.length == 0) {
  6. [Dòng 178] if (this.value == object.id) {
  7. [Dòng 198] return (this.bnplForm.invalid || (this.kbankDetail['citizen_id'] && !(this.kbankDetail['citizen_id'].length == 9) && !(this.kbankDetail['citizen_id'].length == 12)));
  8. [Dòng 238] if (name == 'citizen_id') {
  9. [Dòng 241] if (name == 'phoneNumber') {
  10. [Dòng 244] if (name == 'fullname') {
  11. [Dòng 301] this._res_post.state == 'authorization_required') {
  12. [Dòng 369] this._res_post.code == 'KB-02') {
  13. [Dòng 387] this._res_post.state == 'failed') {

!== (1 điều kiện):
  1. [Dòng 323] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (8 điều kiện):
  1. [Dòng 173] this.listTokenBnpl = this.listTokenBnpl.filter(item => item.id != result.id);
  2. [Dòng 306] if (this._res_post.authorization != null
  3. [Dòng 306] this._res_post.authorization.links != null
  4. [Dòng 311] if (this._res_post.links != null
  5. [Dòng 311] this._res_post.links.cancel != null) {
  6. [Dòng 318] this._res_post.authorization.links.approval != null
  7. [Dòng 318] this._res_post.authorization.links.approval.href != null) {
  8. [Dòng 321] userName = paramUserName != null ? paramUserName : ''

================================================================================

📁 FILE 52: kredivo-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/kredivo/kredivo-form.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 3 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 3] selectedBnpl.status !== 'disabled'"

!= (3 điều kiện):
  1. [Dòng 104] document.activeElement.id!='fullname'"
  2. [Dòng 121] document.activeElement.id!='phoneNumber'"
  3. [Dòng 137] document.activeElement.id!='email'"

================================================================================

📁 FILE 53: kredivo-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/kredivo/kredivo-form.component.ts
📊 Thống kê: 24 điều kiện duy nhất
   - === : 6 lần
   - == : 8 lần
   - !== : 2 lần
   - != : 8 lần
--------------------------------------------------------------------------------

=== (6 điều kiện):
  1. [Dòng 85] if (target.tagName === 'A'
  2. [Dòng 171] params.key === 'Backspace') {
  3. [Dòng 172] if (type === 'email') {
  4. [Dòng 177] if (type === 'phoneNumber') {
  5. [Dòng 200] if (name === 'phoneNumber') {
  6. [Dòng 202] } else if (name === 'email') {

== (8 điều kiện):
  1. [Dòng 240] if (this._locale == 'en') {
  2. [Dòng 251] if (this._res_post.state == 'approved'
  3. [Dòng 251] this._res_post.state == 'failed') {
  4. [Dòng 257] if (this._res_post.state == 'failed') {
  5. [Dòng 282] } else if (this._res_post.state == 'authorization_required') {
  6. [Dòng 283] if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  7. [Dòng 402] this.bnplDetail.fullname?.length == 0 ? this._translate.instant('kredivo_empty_fullname_message')
  8. [Dòng 426] this.bnplDetail.email?.length == 0 ? this._translate.instant('kredivo_empty_email_message')

!== (2 điều kiện):
  1. [Dòng 142] if ((this.bnplDetail.phoneNumber.length !== 10
  2. [Dòng 142] this.bnplDetail.phoneNumber.length !== 11)

!= (8 điều kiện):
  1. [Dòng 252] if (this._res_post.return_url != null) {
  2. [Dòng 254] } else if (this._res_post.links != null
  3. [Dòng 254] this._res_post.links.merchant_return != null
  4. [Dòng 254] this._res_post.links.merchant_return.href != null) {
  5. [Dòng 411] if ((this.bnplDetail.phoneNumber?.length != 10
  6. [Dòng 411] this.bnplDetail.phoneNumber?.length != 11) || !this.bnplDetail.phoneNumber.match(/^[*]{8,9
  7. [Dòng 416] (this.bnplDetail.phoneNumber?.length != 10
  8. [Dòng 416] this.bnplDetail.phoneNumber?.length != 11) ?

================================================================================

📁 FILE 54: otp-auth-bnpl.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/otp-auth-bnpl/otp-auth-bnpl.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 55: otp-auth-bnpl.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/otp-auth-bnpl/otp-auth-bnpl.component.ts
📊 Thống kê: 12 điều kiện duy nhất
   - === : 0 lần
   - == : 5 lần
   - !== : 1 lần
   - != : 6 lần
--------------------------------------------------------------------------------

== (5 điều kiện):
  1. [Dòng 147] if (_re.status == '200'
  2. [Dòng 147] _re.status == '201') {
  3. [Dòng 157] else if (this._res.code == '2') {
  4. [Dòng 201] _re.body.payments[_re.body.payments.length - 1].reason.code == 'B4') {
  5. [Dòng 332] if (this._b == 18) {//8-MB Bank;18-oceanbank

!== (1 điều kiện):
  1. [Dòng 177] codeResponse.toString() !== '0') {

!= (6 điều kiện):
  1. [Dòng 151] if (this._res.links != null
  2. [Dòng 151] this._res.links.merchant_return != null
  3. [Dòng 151] this._res.links.merchant_return.href != null) {
  4. [Dòng 327] if (!(_formCard.otp != null
  5. [Dòng 333] if (!(_formCard.password != null
  6. [Dòng 349] if (ua.indexOf('safari') != -1

================================================================================

📁 FILE 56: process-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/process-dialog/process-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 57: process-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/process-dialog/process-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 58: select-bnpl.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/select-bnpl-dialog/select-bnpl.html
📊 Thống kê: 12 điều kiện duy nhất
   - === : 1 lần
   - == : 9 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 18] bnpl.code === 'homepaylater'"

== (9 điều kiện):
  1. [Dòng 7] bnpl.status == 'disabled'"
  2. [Dòng 8] bnpl.status == 'active'"
  3. [Dòng 15] bnpl.code == 'kbank'"
  4. [Dòng 23] bnpl.code == 'kbank'
  5. [Dòng 26] bnpl.status == 'disabled'
  6. [Dòng 26] bnpl.code == 'insta'"
  7. [Dòng 30] bnpl.status == 'active'
  8. [Dòng 33] bnpl.code == 'homepaylater'
  9. [Dòng 36] bnpl.code == 'kredivo'

!= (2 điều kiện):
  1. [Dòng 21] bnpl.code != 'kbank'
  2. [Dòng 21] bnpl.code != 'homepaylater'"

================================================================================

📁 FILE 59: select-bnpl.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/bnpl-main/select-bnpl-dialog/select-bnpl.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 29] if (bnpl.status == 'disabled') {

================================================================================

📁 FILE 60: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 61: bankaccount.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 39] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 53] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 53] valueDate.trim().length === 0)"

================================================================================

📁 FILE 62: bankaccount.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 63: bankaccount.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/bankaccount/bankaccount.component.ts
📊 Thống kê: 153 điều kiện duy nhất
   - === : 48 lần
   - == : 71 lần
   - !== : 13 lần
   - != : 21 lần
--------------------------------------------------------------------------------

=== (48 điều kiện):
  1. [Dòng 104] if (target.tagName === 'A'
  2. [Dòng 128] if (isIE[0] === 'MSIE'
  3. [Dòng 128] +isIE[1] === 10) {
  4. [Dòng 212] if ((_val.value.substr(-1) === ' '
  5. [Dòng 212] _val.value.length === 24) {
  6. [Dòng 222] if (this.cardTypeBank === 'bank_card_number') {
  7. [Dòng 227] } else if (this.cardTypeBank === 'bank_account_number') {
  8. [Dòng 233] } else if (this.cardTypeBank === 'bank_username') {
  9. [Dòng 237] } else if (this.cardTypeBank === 'bank_customer_code') {
  10. [Dòng 243] this.cardTypeBank === 'bank_card_number'
  11. [Dòng 257] if (this.cardTypeOcean === 'IB') {
  12. [Dòng 261] } else if (this.cardTypeOcean === 'MB') {
  13. [Dòng 262] if (_val.value.substr(0, 2) === '84') {
  14. [Dòng 269] } else if (this.cardTypeOcean === 'ATM') {
  15. [Dòng 296] if (this.cardTypeBank === 'bank_account_number') {
  16. [Dòng 315] this.cardTypeBank === 'bank_card_number') {
  17. [Dòng 337] if (this.cardTypeBank === 'bank_card_number'
  18. [Dòng 337] if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  19. [Dòng 658] if (event.keyCode === 8
  20. [Dòng 658] event.key === "Backspace"
  21. [Dòng 698] if (v.length === 2
  22. [Dòng 698] this.flag.length === 3
  23. [Dòng 698] this.flag.charAt(this.flag.length - 1) === '/') {
  24. [Dòng 702] if (v.length === 1) {
  25. [Dòng 704] } else if (v.length === 2) {
  26. [Dòng 707] v.length === 2) {
  27. [Dòng 715] if (len === 2) {
  28. [Dòng 992] if ((this.cardTypeBank === 'bank_account_number'
  29. [Dòng 992] this.cardTypeBank === 'bank_username'
  30. [Dòng 992] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  31. [Dòng 997] this.cardTypeOcean === 'ATM')
  32. [Dòng 998] || (this.cardTypeOcean === 'IB'
  33. [Dòng 1057] if (valIn === this._translate.instant('bank_card_number')) {
  34. [Dòng 1082] } else if (valIn === this._translate.instant('bank_account_number')) {
  35. [Dòng 1101] } else if (valIn === this._translate.instant('bank_username')) {
  36. [Dòng 1117] } else if (valIn === this._translate.instant('bank_customer_code')) {
  37. [Dòng 1206] if (_val.value === ''
  38. [Dòng 1206] _val.value === null
  39. [Dòng 1206] _val.value === undefined) {
  40. [Dòng 1217] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  41. [Dòng 1217] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  42. [Dòng 1224] this.cardTypeOcean === 'MB') {
  43. [Dòng 1232] this.cardTypeOcean === 'IB'
  44. [Dòng 1238] if ((this.cardTypeBank === 'bank_card_number'
  45. [Dòng 1272] if (this.cardName === undefined
  46. [Dòng 1272] this.cardName === '') {
  47. [Dòng 1280] if (this.valueDate === undefined
  48. [Dòng 1280] this.valueDate === '') {

== (71 điều kiện):
  1. [Dòng 142] if (this._b == 18
  2. [Dòng 142] this._b == 19) {
  3. [Dòng 145] if (this._b == 19) {//19BIDV
  4. [Dòng 153] } else if (this._b == 3
  5. [Dòng 153] this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  6. [Dòng 158] if (this._b == 27) {
  7. [Dòng 163] } else if (this._b == 12) {// 12SHB
  8. [Dòng 168] } else if (this._b == 18) { //18Oceanbank-ocb
  9. [Dòng 221] if (this._b == 19
  10. [Dòng 221] this._b == 3
  11. [Dòng 221] this._b == 27
  12. [Dòng 221] this._b == 12) {
  13. [Dòng 256] } else if (this._b == 18) {
  14. [Dòng 287] if (this.checkBin(_val.value) && (this._b == 3
  15. [Dòng 287] this._b == 27)) {
  16. [Dòng 292] if (this._b == 3) {
  17. [Dòng 304] this.cardTypeOcean == 'ATM') {
  18. [Dòng 317] if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  19. [Dòng 337] this._b == 18)) {
  20. [Dòng 413] if (this.checkBin(v) && (this._b == 3
  21. [Dòng 658] event.inputType == 'deleteContentBackward') {
  22. [Dòng 659] if (event.target.name == 'exp_date'
  23. [Dòng 667] event.inputType == 'insertCompositionText') {
  24. [Dòng 682] if (((this.valueDate.length == 4
  25. [Dòng 682] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  26. [Dòng 682] this.valueDate.length == 5)
  27. [Dòng 762] if (temp.length == 0) {
  28. [Dòng 769] return (counter % 10 == 0);
  29. [Dòng 789] } else if (this._b == 19) {
  30. [Dòng 791] } else if (this._b == 27) {
  31. [Dòng 796] if (this._b == 12) {
  32. [Dòng 798] if (this.cardTypeBank == 'bank_customer_code') {
  33. [Dòng 800] } else if (this.cardTypeBank == 'bank_account_number') {
  34. [Dòng 817] _formCard.exp_date.length == 5
  35. [Dòng 817] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
  36. [Dòng 817] this._b == 3)) {//27-pvcombank;3-TPB ;
  37. [Dòng 822] if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
  38. [Dòng 822] this._b == 19
  39. [Dòng 822] this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
  40. [Dòng 825] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  41. [Dòng 828] if (this.cardTypeOcean == 'IB') {
  42. [Dòng 830] } else if (this.cardTypeOcean == 'MB') {
  43. [Dòng 832] } else if (this.cardTypeOcean == 'ATM') {
  44. [Dòng 877] if (_re.status == '200'
  45. [Dòng 877] _re.status == '201') {
  46. [Dòng 882] if (this._res_post.state == 'approved'
  47. [Dòng 882] this._res_post.state == 'failed') {
  48. [Dòng 889] } else if (this._res_post.state == 'authorization_required') {
  49. [Dòng 907] if (this._b == 18) {
  50. [Dòng 912] if (this._b == 27
  51. [Dòng 912] this._b == 18) {
  52. [Dòng 980] if (err.status == 400
  53. [Dòng 980] err.error['name'] == 'INVALID_INPUT_BIN') {
  54. [Dòng 1012] if ((cardNo.length == 16
  55. [Dòng 1012] if ((cardNo.length == 16 || (cardNo.length == 19
  56. [Dòng 1013] && ((this._b == 18
  57. [Dòng 1013] cardNo.length == 19) || this._b != 18)
  58. [Dòng 1026] if (this._b == +e.id) {
  59. [Dòng 1042] if (valIn == 1) {
  60. [Dòng 1044] } else if (valIn == 2) {
  61. [Dòng 1068] this._b == 3) {
  62. [Dòng 1075] if (this._b == 19) {
  63. [Dòng 1139] if (cardType == this._translate.instant('internetbanking')
  64. [Dòng 1147] } else if (cardType == this._translate.instant('mobilebanking')
  65. [Dòng 1155] } else if (cardType == this._translate.instant('atm')
  66. [Dòng 1217] this._b == 18))) {
  67. [Dòng 1224] } else if (this._b == 18
  68. [Dòng 1250] this.c_expdate = !(((this.valueDate.length == 4
  69. [Dòng 1283] this.valueDate.length == 4
  70. [Dòng 1283] this.valueDate.search('/') == -1)
  71. [Dòng 1284] this.valueDate.length == 5))

!== (13 điều kiện):
  1. [Dòng 212] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 863] key !== '3') {
  3. [Dòng 913] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 931] codeResponse.toString() !== '0') {
  5. [Dòng 992] cardNo.length !== 0) {
  6. [Dòng 1064] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 1085] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 1106] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 1126] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 1139] this.lb_card_account !== this._translate.instant('ocb_account')) {
  11. [Dòng 1147] this.lb_card_account !== this._translate.instant('ocb_phone')) {
  12. [Dòng 1155] this.lb_card_account !== this._translate.instant('ocb_card')) {
  13. [Dòng 1238] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (21 điều kiện):
  1. [Dòng 174] } else if (this._b != 18) {
  2. [Dòng 180] if (this.htmlDesc != null
  3. [Dòng 209] if (ua.indexOf('safari') != -1
  4. [Dòng 219] if (_val.value != '') {
  5. [Dòng 305] this.auth_method != null) {
  6. [Dòng 660] if (this.valueDate.length != 3) {
  7. [Dòng 817] if (_formCard.exp_date != null
  8. [Dòng 822] if (this.cardName != null
  9. [Dòng 885] if (this._res_post.links != null
  10. [Dòng 885] this._res_post.links.merchant_return != null
  11. [Dòng 885] this._res_post.links.merchant_return.href != null) {
  12. [Dòng 893] if (this._res_post.authorization != null
  13. [Dòng 893] this._res_post.authorization.links != null
  14. [Dòng 893] this._res_post.authorization.links.approval != null) {
  15. [Dòng 900] this._res_post.links.cancel != null) {
  16. [Dòng 1012] this._b != 27
  17. [Dòng 1012] this._b != 12
  18. [Dòng 1012] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  19. [Dòng 1013] this._b != 18)
  20. [Dòng 1059] if (this._b != 18
  21. [Dòng 1059] this._b != 19) {

================================================================================

📁 FILE 64: bank.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/model/bank.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 65: onepay-napas.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 3 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 27] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 42] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  3. [Dòng 42] _inExpDate.trim().length === 0)"

== (3 điều kiện):
  1. [Dòng 24] (cardTypeBank == 'bank_card_number') && (_b == 67 || (checkTwoEnabled && techcombankCard)) && !isOffTechcombankNapas
  2. [Dòng 68] (cardTypeBank == 'bank_card_number') &&(_b == 67 || (checkTwoEnabled && techcombankCard))&& ready===1 && !isOffTechcombankNapas
  3. [Dòng 95] (cardTypeBank == 'internet_banking')&&(_b == 2 || (checkTwoEnabled && !techcombankCard))

================================================================================

📁 FILE 66: onepay-napas.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/onepay-napas/onepay-napas.component.ts
📊 Thống kê: 104 điều kiện duy nhất
   - === : 22 lần
   - == : 47 lần
   - !== : 4 lần
   - != : 31 lần
--------------------------------------------------------------------------------

=== (22 điều kiện):
  1. [Dòng 101] if (target.tagName === 'A'
  2. [Dòng 128] if (isIE[0] === 'MSIE'
  3. [Dòng 128] +isIE[1] === 10) {
  4. [Dòng 151] if (this.cardListTech === "op") {
  5. [Dòng 158] if (this.timeLeft === 10) {
  6. [Dòng 162] if (this.runTime === true) {
  7. [Dòng 168] if (this.timeLeft === 0) {
  8. [Dòng 170] if (this.runTime === true) this.submitCardBanking();
  9. [Dòng 357] if (event.keyCode === 8
  10. [Dòng 357] event.key === "Backspace"
  11. [Dòng 569] if (approval.method === 'REDIRECT') {
  12. [Dòng 572] } else if (approval.method === 'POST_REDIRECT') {
  13. [Dòng 651] if (valIn === this._translate.instant('bank_card_number')) {
  14. [Dòng 653] if (this.timeLeft === 1) {
  15. [Dòng 670] } else if (valIn === this._translate.instant('internet_banking')) {
  16. [Dòng 750] if (_val.value === ''
  17. [Dòng 750] _val.value === null
  18. [Dòng 750] _val.value === undefined) {
  19. [Dòng 761] if (_val.value && (this.cardTypeBank === 'bank_card_number')) {
  20. [Dòng 771] if ((this.cardTypeBank === 'bank_card_number'
  21. [Dòng 809] if (this.cardName === undefined
  22. [Dòng 809] this.cardName === '') {

== (47 điều kiện):
  1. [Dòng 144] if (this._b == 67
  2. [Dòng 144] this._b == 2) {//19BIDV
  3. [Dòng 154] if ((this._b == 2
  4. [Dòng 154] !this.checkTwoEnabled) || (this._b == 2
  5. [Dòng 155] this._b == 2
  6. [Dòng 179] } else if (this._b == 2
  7. [Dòng 183] if (this._b == 67) {
  8. [Dòng 221] if (_re.status == '200'
  9. [Dòng 221] _re.status == '201') {
  10. [Dòng 226] if (this._res_post.state == 'approved'
  11. [Dòng 226] this._res_post.state == 'failed') {
  12. [Dòng 230] } else if (this._res_post.state == 'authorization_required') {
  13. [Dòng 342] return this._b == 2
  14. [Dòng 342] this._b == 67
  15. [Dòng 357] event.inputType == 'deleteContentBackward') {
  16. [Dòng 358] if (event.target.name == 'exp_date'
  17. [Dòng 366] event.inputType == 'insertCompositionText') {
  18. [Dòng 428] if (temp.length == 0) {
  19. [Dòng 435] return (counter % 10 == 0);
  20. [Dòng 451] _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
  21. [Dòng 600] if (err.status == 400
  22. [Dòng 600] err.error['name'] == 'INVALID_INPUT_BIN') {
  23. [Dòng 614] if ((cardNo.length == 16
  24. [Dòng 614] if ((cardNo.length == 16 || (cardNo.length == 19
  25. [Dòng 615] this.checkMod10(cardNo) == true
  26. [Dòng 628] if (this._b == +e.id) {
  27. [Dòng 704] if (this._b == 19) {
  28. [Dòng 708] if (this._b == 27
  29. [Dòng 708] this._b == 3) {
  30. [Dòng 783] if (this._b != 68 || (this._b == 68
  31. [Dòng 792] return ((value.length == 4
  32. [Dòng 792] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  33. [Dòng 792] value.length == 5) && parseInt(value.split('/')[0]
  34. [Dòng 796] || (this._util.checkValidExpireMonthYear(value) && (this._b == 2
  35. [Dòng 796] this._b == 20
  36. [Dòng 796] this._b == 33
  37. [Dòng 797] this._b == 39
  38. [Dòng 797] this._b == 43
  39. [Dòng 797] this._b == 45
  40. [Dòng 797] this._b == 64
  41. [Dòng 797] this._b == 68
  42. [Dòng 797] this._b == 72))) //sonnh them Vietbank 72
  43. [Dòng 818] this._inExpDate.length == 4
  44. [Dòng 818] this._inExpDate.search('/') == -1)
  45. [Dòng 819] this._inExpDate.length == 5))
  46. [Dòng 821] || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 20
  47. [Dòng 821] this._b == 72)));

!== (4 điều kiện):
  1. [Dòng 245] codeResponse.toString() !== '0') {
  2. [Dòng 612] let _b = this._b !== 67 ? 67 : this._b
  3. [Dòng 697] if (this.cardTypeBank !== 'internet_banking') {
  4. [Dòng 771] this._b !== 18)) {

!= (31 điều kiện):
  1. [Dòng 175] if (this.htmlDesc != null
  2. [Dòng 234] if (this._res_post.authorization != null
  3. [Dòng 234] this._res_post.authorization.links != null
  4. [Dòng 234] this._res_post.authorization.links.approval != null) {
  5. [Dòng 294] if (ua.indexOf('safari') != -1
  6. [Dòng 359] if (this._inExpDate.length != 3) {
  7. [Dòng 451] if (_formCard.exp_date != null
  8. [Dòng 456] if (this.cardName != null
  9. [Dòng 493] if (this._res_post.return_url != null) {
  10. [Dòng 496] if (this._res_post.links != null
  11. [Dòng 496] this._res_post.links.merchant_return != null
  12. [Dòng 496] this._res_post.links.merchant_return.href != null) {
  13. [Dòng 554] this._res_post.links.cancel != null) {
  14. [Dòng 559] let userName = _formCard.name != null ? _formCard.name : ''
  15. [Dòng 560] this._res_post.authorization.links.approval != null
  16. [Dòng 560] this._res_post.authorization.links.approval.href != null) {
  17. [Dòng 563] userName = paramUserName != null ? paramUserName : ''
  18. [Dòng 614] this._b != 27
  19. [Dòng 614] this._b != 12
  20. [Dòng 614] this._b != 3))
  21. [Dòng 783] if (this._b != 68
  22. [Dòng 794] this._b != 2
  23. [Dòng 794] this._b != 20
  24. [Dòng 794] this._b != 33
  25. [Dòng 794] this._b != 39
  26. [Dòng 795] this._b != 43
  27. [Dòng 795] this._b != 45
  28. [Dòng 795] this._b != 64
  29. [Dòng 795] this._b != 67
  30. [Dòng 795] this._b != 68
  31. [Dòng 795] this._b != 72)

================================================================================

📁 FILE 67: otp-auth.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 68: otp-auth.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
📊 Thống kê: 18 điều kiện duy nhất
   - === : 0 lần
   - == : 11 lần
   - !== : 1 lần
   - != : 6 lần
--------------------------------------------------------------------------------

== (11 điều kiện):
  1. [Dòng 100] if (this._b == 8) {//MB Bank
  2. [Dòng 104] if (this._b == 18) {//Oceanbank
  3. [Dòng 139] if (this._b == 8) {
  4. [Dòng 144] if (this._b == 18) {
  5. [Dòng 149] if (this._b == 12) { //SHB
  6. [Dòng 171] if (_re.status == '200'
  7. [Dòng 171] _re.status == '201') {
  8. [Dòng 180] } else if (this._res.state == 'authorization_required') {
  9. [Dòng 215] if (this.challengeCode == '') {
  10. [Dòng 309] if (this._b == 12) {
  11. [Dòng 365] if (this._b == 18) {//8-MB Bank;18-oceanbank

!== (1 điều kiện):
  1. [Dòng 186] codeResponse.toString() !== '0') {

!= (6 điều kiện):
  1. [Dòng 176] if (this._res.links != null
  2. [Dòng 176] this._res.links.merchant_return != null
  3. [Dòng 176] this._res.links.merchant_return.href != null) {
  4. [Dòng 360] if (!(_formCard.otp != null
  5. [Dòng 366] if (!(_formCard.password != null
  6. [Dòng 382] if (ua.indexOf('safari') != -1

================================================================================

📁 FILE 69: shb.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/shb/shb.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 25] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 42] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  3. [Dòng 42] _inExpDate.trim().length === 0)"

== (2 điều kiện):
  1. [Dòng 22] <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_card_number')">
  2. [Dòng 66] <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_account_number')">

================================================================================

📁 FILE 70: shb.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/shb/shb.component.ts
📊 Thống kê: 66 điều kiện duy nhất
   - === : 18 lần
   - == : 32 lần
   - !== : 5 lần
   - != : 11 lần
--------------------------------------------------------------------------------

=== (18 điều kiện):
  1. [Dòng 96] if (target.tagName === 'A'
  2. [Dòng 121] if (isIE[0] === 'MSIE'
  3. [Dòng 121] +isIE[1] === 10) {
  4. [Dòng 163] if (focusElement === 'card_name') {
  5. [Dòng 165] } else if (focusElement === 'exp_date'
  6. [Dòng 186] focusExpDateElement === 'card_name') {
  7. [Dòng 406] if (this.cardTypeBank === 'bank_account_number'
  8. [Dòng 451] if (valIn === this._translate.instant('bank_card_number')) {
  9. [Dòng 457] } else if (valIn === this._translate.instant('bank_account_number')) {
  10. [Dòng 499] if (_val.value === ''
  11. [Dòng 499] _val.value === null
  12. [Dòng 499] _val.value === undefined) {
  13. [Dòng 510] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  14. [Dòng 510] if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
  15. [Dòng 517] this.cardTypeOcean === 'MB') {
  16. [Dòng 525] this.cardTypeOcean === 'IB'
  17. [Dòng 531] if ((this.cardTypeBank === 'bank_card_number'
  18. [Dòng 554] if (this.cardTypeOcean === 'IB') {

== (32 điều kiện):
  1. [Dòng 130] if (this._b == 12) this.isShbGroup = true;
  2. [Dòng 151] return this._b == 9
  3. [Dòng 151] this._b == 11
  4. [Dòng 151] this._b == 16
  5. [Dòng 151] this._b == 17
  6. [Dòng 151] this._b == 25
  7. [Dòng 151] this._b == 44
  8. [Dòng 152] this._b == 57
  9. [Dòng 152] this._b == 59
  10. [Dòng 152] this._b == 61
  11. [Dòng 152] this._b == 63
  12. [Dòng 152] this._b == 69
  13. [Dòng 234] if (this.cardTypeBank == 'bank_account_number') {
  14. [Dòng 245] _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
  15. [Dòng 291] if (_re.status == '200'
  16. [Dòng 291] _re.status == '201') {
  17. [Dòng 295] if (this._res_post.state == 'approved'
  18. [Dòng 295] this._res_post.state == 'failed') {
  19. [Dòng 301] } else if (this._res_post.state == 'authorization_required') {
  20. [Dòng 393] if (err.status == 400
  21. [Dòng 393] err.error['name'] == 'INVALID_INPUT_BIN') {
  22. [Dòng 409] if ((cardNo.length == 16
  23. [Dòng 409] cardNo.length == 19) && cardNo.startsWith("970443") && this._util.checkMod10(cardNo) && this.checkBin(cardNo)) {
  24. [Dòng 421] if (this._b == +e.id) {
  25. [Dòng 437] if (valIn == 1) {
  26. [Dòng 439] } else if (valIn == 2) {
  27. [Dòng 510] this._b == 18))) {
  28. [Dòng 517] } else if (this._b == 18
  29. [Dòng 531] this._b == 18)) {
  30. [Dòng 543] this.c_expdate = !(((this.valueDate.length == 4
  31. [Dòng 543] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  32. [Dòng 543] this.valueDate.length == 5)

!== (5 điều kiện):
  1. [Dòng 279] key !== '3') {
  2. [Dòng 321] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  3. [Dòng 339] codeResponse.toString() !== '0') {
  4. [Dòng 406] cardNo.length !== 0) {
  5. [Dòng 531] this._b !== 18) || (this.cardTypeOcean === 'ATM'

!= (11 điều kiện):
  1. [Dòng 138] if (this.htmlDesc != null
  2. [Dòng 199] if (ua.indexOf('safari') != -1
  3. [Dòng 245] if (_formCard.exp_date != null
  4. [Dòng 250] if (this.cardName != null
  5. [Dòng 298] if (this._res_post.links != null
  6. [Dòng 298] this._res_post.links.merchant_return != null
  7. [Dòng 298] this._res_post.links.merchant_return.href != null) {
  8. [Dòng 304] if (this._res_post.authorization != null
  9. [Dòng 304] this._res_post.authorization.links != null
  10. [Dòng 304] this._res_post.authorization.links.approval != null) {
  11. [Dòng 311] this._res_post.links.cancel != null) {

================================================================================

📁 FILE 71: techcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 72: techcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 73: techcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/techcombank/techcombank.component.ts
📊 Thống kê: 17 điều kiện duy nhất
   - === : 1 lần
   - == : 12 lần
   - !== : 1 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 62] if (this.timeLeft === 0) {

== (12 điều kiện):
  1. [Dòng 53] if (this._b == 2
  2. [Dòng 53] this._b == 31
  3. [Dòng 53] this._b == 80) {
  4. [Dòng 90] if (this._b == 2) {
  5. [Dòng 92] } else if (this._b == 6) {
  6. [Dòng 94] } else if (this._b == 31) {
  7. [Dòng 96] } else if (this._b == 80) {
  8. [Dòng 126] if (_re.status == '200'
  9. [Dòng 126] _re.status == '201') {
  10. [Dòng 131] if (this._res_post.state == 'approved'
  11. [Dòng 131] this._res_post.state == 'failed') {
  12. [Dòng 135] } else if (this._res_post.state == 'authorization_required') {

!== (1 điều kiện):
  1. [Dòng 150] codeResponse.toString() !== '0') {

!= (3 điều kiện):
  1. [Dòng 139] if (this._res_post.authorization != null
  2. [Dòng 139] this._res_post.authorization.links != null
  3. [Dòng 139] this._res_post.authorization.links.approval != null) {

================================================================================

📁 FILE 74: vibbank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 27] <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
  2. [Dòng 41] [class.red_border_no_background]="c_expdate && (valueDate.length === 0
  3. [Dòng 41] valueDate.trim().length === 0)"

================================================================================

📁 FILE 75: vibbank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 76: vibbank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/vibbank/vibbank.component.ts
📊 Thống kê: 99 điều kiện duy nhất
   - === : 37 lần
   - == : 35 lần
   - !== : 10 lần
   - != : 17 lần
--------------------------------------------------------------------------------

=== (37 điều kiện):
  1. [Dòng 109] if (target.tagName === 'A'
  2. [Dòng 134] if (isIE[0] === 'MSIE'
  3. [Dòng 134] +isIE[1] === 10) {
  4. [Dòng 165] if (this.timeLeft === 0) {
  5. [Dòng 207] if ((_val.value.substr(-1) === ' '
  6. [Dòng 207] _val.value.length === 24) {
  7. [Dòng 217] if (this.cardTypeBank === 'bank_card_number') {
  8. [Dòng 222] } else if (this.cardTypeBank === 'bank_account_number') {
  9. [Dòng 228] } else if (this.cardTypeBank === 'bank_username') {
  10. [Dòng 232] } else if (this.cardTypeBank === 'bank_customer_code') {
  11. [Dòng 253] if (this.cardTypeBank === 'bank_account_number') {
  12. [Dòng 264] this.cardTypeBank === 'bank_card_number') {
  13. [Dòng 501] if (event.keyCode === 8
  14. [Dòng 501] event.key === "Backspace"
  15. [Dòng 541] if (v.length === 2
  16. [Dòng 541] this.flag.length === 3
  17. [Dòng 541] this.flag.charAt(this.flag.length - 1) === '/') {
  18. [Dòng 545] if (v.length === 1) {
  19. [Dòng 547] } else if (v.length === 2) {
  20. [Dòng 550] v.length === 2) {
  21. [Dòng 558] if (len === 2) {
  22. [Dòng 802] if ((this.cardTypeBank === 'bank_account_number'
  23. [Dòng 802] this.cardTypeBank === 'bank_username'
  24. [Dòng 802] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  25. [Dòng 852] if (valIn === this._translate.instant('bank_card_number')) {
  26. [Dòng 871] } else if (valIn === this._translate.instant('bank_account_number')) {
  27. [Dòng 883] } else if (valIn === this._translate.instant('bank_username')) {
  28. [Dòng 894] } else if (valIn === this._translate.instant('bank_customer_code')) {
  29. [Dòng 951] if (_val.value === ''
  30. [Dòng 951] _val.value === null
  31. [Dòng 951] _val.value === undefined) {
  32. [Dòng 962] if (_val.value && (this.cardTypeBank === 'bank_card_number'
  33. [Dòng 972] if ((this.cardTypeBank === 'bank_card_number'
  34. [Dòng 1004] if (this.cardName === undefined
  35. [Dòng 1004] this.cardName === '') {
  36. [Dòng 1012] if (this.valueDate === undefined
  37. [Dòng 1012] this.valueDate === '') {

== (35 điều kiện):
  1. [Dòng 148] if (this._b == 5) {//5-vib;
  2. [Dòng 216] if (this._b == 5) {
  3. [Dòng 250] if (this.checkBin(_val.value) && (this._b == 5)) {
  4. [Dòng 266] if (this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
  5. [Dòng 328] if (this.checkBin(v) && (this._b == 5)) {
  6. [Dòng 501] event.inputType == 'deleteContentBackward') {
  7. [Dòng 502] if (event.target.name == 'exp_date'
  8. [Dòng 510] event.inputType == 'insertCompositionText') {
  9. [Dòng 525] if (((this.valueDate.length == 4
  10. [Dòng 525] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  11. [Dòng 525] this.valueDate.length == 5)
  12. [Dòng 605] if (temp.length == 0) {
  13. [Dòng 612] return (counter % 10 == 0);
  14. [Dòng 643] _formCard.exp_date.length == 5
  15. [Dòng 643] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 5)) {//27-pvcombank;3-TPB ;
  16. [Dòng 648] if (this.cardName != null && this.cardName.length > 0 && (this._b == 5)) {//3-TPB ;19-BIDV;27-pvcombank;
  17. [Dòng 692] if (_re.status == '200'
  18. [Dòng 692] _re.status == '201') {
  19. [Dòng 697] if (this._res_post.state == 'approved'
  20. [Dòng 697] this._res_post.state == 'failed') {
  21. [Dòng 704] } else if (this._res_post.state == 'authorization_required') {
  22. [Dòng 790] if (err.status == 400
  23. [Dòng 790] err.error['name'] == 'INVALID_INPUT_BIN') {
  24. [Dòng 807] if ((cardNo.length == 16
  25. [Dòng 807] if ((cardNo.length == 16 || (cardNo.length == 19
  26. [Dòng 808] && ((this._b == 18
  27. [Dòng 808] cardNo.length == 19) || this._b != 18)
  28. [Dòng 821] if (this._b == +e.id) {
  29. [Dòng 837] if (valIn == 1) {
  30. [Dòng 839] } else if (valIn == 2) {
  31. [Dòng 962] this._b == 18)) {
  32. [Dòng 984] this.c_expdate = !(((this.valueDate.length == 4
  33. [Dòng 1015] this.valueDate.length == 4
  34. [Dòng 1015] this.valueDate.search('/') == -1)
  35. [Dòng 1016] this.valueDate.length == 5))

!== (10 điều kiện):
  1. [Dòng 207] event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
  2. [Dòng 678] key !== '3') {
  3. [Dòng 726] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 743] codeResponse.toString() !== '0') {
  5. [Dòng 802] cardNo.length !== 0) {
  6. [Dòng 859] if (this.cardTypeBank !== 'bank_card_number') {
  7. [Dòng 874] if (this.cardTypeBank !== 'bank_account_number') {
  8. [Dòng 888] if (this.cardTypeBank !== 'bank_username') {
  9. [Dòng 901] if (this.cardTypeBank !== 'bank_customer_code') {
  10. [Dòng 972] this._b !== 18) || (this._b == 18)) {

!= (17 điều kiện):
  1. [Dòng 175] if (this.htmlDesc != null
  2. [Dòng 204] if (ua.indexOf('safari') != -1
  3. [Dòng 214] if (_val.value != '') {
  4. [Dòng 503] if (this.valueDate.length != 3) {
  5. [Dòng 643] if (_formCard.exp_date != null
  6. [Dòng 648] if (this.cardName != null
  7. [Dòng 700] if (this._res_post.links != null
  8. [Dòng 700] this._res_post.links.merchant_return != null
  9. [Dòng 700] this._res_post.links.merchant_return.href != null) {
  10. [Dòng 708] if (this._res_post.authorization != null
  11. [Dòng 708] this._res_post.authorization.links != null
  12. [Dòng 708] this._res_post.authorization.links.approval != null) {
  13. [Dòng 715] this._res_post.links.cancel != null) {
  14. [Dòng 807] this._b != 27
  15. [Dòng 807] this._b != 12
  16. [Dòng 807] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  17. [Dòng 808] this._b != 18)

================================================================================

📁 FILE 77: vietcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 28] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  2. [Dòng 28] _inExpDate.trim().length === 0)"

== (1 điều kiện):
  1. [Dòng 58] _b == 68"

================================================================================

📁 FILE 78: vietcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 79: vietcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
📊 Thống kê: 91 điều kiện duy nhất
   - === : 5 lần
   - == : 59 lần
   - !== : 2 lần
   - != : 25 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 79] if (target.tagName === 'A'
  2. [Dòng 212] if (event.keyCode === 8
  3. [Dòng 212] event.key === "Backspace"
  4. [Dòng 449] if (approval.method === 'REDIRECT') {
  5. [Dòng 452] } else if (approval.method === 'POST_REDIRECT') {

== (59 điều kiện):
  1. [Dòng 118] if (this._b == 1
  2. [Dòng 118] this._b == 20
  3. [Dòng 118] this._b == 73
  4. [Dòng 118] this._b == 36
  5. [Dòng 118] this._b == 64
  6. [Dòng 118] this._b == 55
  7. [Dòng 118] this._b == 47
  8. [Dòng 118] this._b == 48
  9. [Dòng 118] this._b == 59) {
  10. [Dòng 131] return this._b == 11
  11. [Dòng 131] this._b == 33
  12. [Dòng 131] this._b == 39
  13. [Dòng 131] this._b == 43
  14. [Dòng 131] this._b == 45
  15. [Dòng 131] this._b == 67
  16. [Dòng 131] this._b == 68
  17. [Dòng 131] this._b == 72
  18. [Dòng 131] this._b == 74
  19. [Dòng 131] this._b == 75
  20. [Dòng 139] return this._b == 9
  21. [Dòng 139] this._b == 16
  22. [Dòng 139] this._b == 17
  23. [Dòng 139] this._b == 25
  24. [Dòng 139] this._b == 44
  25. [Dòng 140] this._b == 57
  26. [Dòng 140] this._b == 59
  27. [Dòng 140] this._b == 61
  28. [Dòng 140] this._b == 63
  29. [Dòng 140] this._b == 69
  30. [Dòng 212] event.inputType == 'deleteContentBackward') {
  31. [Dòng 213] if (event.target.name == 'exp_date'
  32. [Dòng 221] event.inputType == 'insertCompositionText') {
  33. [Dòng 336] if (this._res_post.state == 'approved'
  34. [Dòng 336] this._res_post.state == 'failed') {
  35. [Dòng 388] } else if (this._res_post.state == 'authorization_required') {
  36. [Dòng 410] this._b == 14
  37. [Dòng 410] this._b == 15
  38. [Dòng 410] this._b == 24
  39. [Dòng 410] this._b == 8
  40. [Dòng 410] this._b == 10
  41. [Dòng 410] this._b == 22
  42. [Dòng 410] this._b == 23
  43. [Dòng 410] this._b == 30
  44. [Dòng 410] this._b == 11
  45. [Dòng 410] this._b == 9) {
  46. [Dòng 480] if (err.status == 400
  47. [Dòng 480] err.error['name'] == 'INVALID_INPUT_BIN') {
  48. [Dòng 495] if ((cardNo.length == 16
  49. [Dòng 496] (cardNo.length == 19
  50. [Dòng 496] (cardNo.length == 19 && (this._b == 1
  51. [Dòng 496] this._b == 4
  52. [Dòng 496] this._b == 59))
  53. [Dòng 498] this._util.checkMod10(cardNo) == true
  54. [Dòng 534] return ((value.length == 4
  55. [Dòng 534] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  56. [Dòng 534] value.length == 5) && parseInt(value.split('/')[0]
  57. [Dòng 568] this._inExpDate.length == 4
  58. [Dòng 568] this._inExpDate.search('/') == -1)
  59. [Dòng 569] this._inExpDate.length == 5))

!== (2 điều kiện):
  1. [Dòng 349] codeResponse.toString() !== '0') {
  2. [Dòng 411] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (25 điều kiện):
  1. [Dòng 123] if (this.htmlDesc != null
  2. [Dòng 149] if (ua.indexOf('safari') != -1
  3. [Dòng 214] if (this._inExpDate.length != 3) {
  4. [Dòng 290] if (this._b != 9
  5. [Dòng 290] this._b != 16
  6. [Dòng 290] this._b != 17
  7. [Dòng 290] this._b != 25
  8. [Dòng 290] this._b != 44
  9. [Dòng 291] this._b != 57
  10. [Dòng 291] this._b != 59
  11. [Dòng 291] this._b != 61
  12. [Dòng 291] this._b != 63
  13. [Dòng 291] this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
  14. [Dòng 304] '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75'].indexOf(this._b.toString()) != -1) {
  15. [Dòng 338] if (this._res_post.return_url != null) {
  16. [Dòng 341] if (this._res_post.links != null
  17. [Dòng 341] this._res_post.links.merchant_return != null
  18. [Dòng 341] this._res_post.links.merchant_return.href != null) {
  19. [Dòng 393] if (this._res_post.authorization != null
  20. [Dòng 393] this._res_post.authorization.links != null
  21. [Dòng 398] this._res_post.links.cancel != null) {
  22. [Dòng 404] let userName = _formCard.name != null ? _formCard.name : ''
  23. [Dòng 405] this._res_post.authorization.links.approval != null
  24. [Dòng 405] this._res_post.authorization.links.approval.href != null) {
  25. [Dòng 408] userName = paramUserName != null ? paramUserName : ''

================================================================================

📁 FILE 80: vietqr-domes.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/vietqr-domes/vietqr-domes.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 81: vietqr-domes.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/banks/vietqr-domes/vietqr-domes.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 82: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 83: off-bank-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 84: off-bank-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 85: domescard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/domescard-main.component.html
📊 Thống kê: 10 điều kiện duy nhất
   - === : 1 lần
   - == : 9 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 17] filteredData.length === 0"

== (9 điều kiện):
  1. [Dòng 26] (!token&&_auth==0 && vietcombankGroupSelected) || (token && _b == 16)
  2. [Dòng 26] _b == 16)">
  3. [Dòng 30] _auth==0 && techcombankGroupSelected
  4. [Dòng 34] _auth==0 && shbGroupSelected
  5. [Dòng 39] _auth==0 && onepaynapasGroupSelected
  6. [Dòng 45] _auth==0 && bankaccountGroupSelected
  7. [Dòng 49] _auth==0 && vibbankGroupSelected
  8. [Dòng 53] _auth==0 && vietQRSelected
  9. [Dòng 57] (token || _auth==1) && _b != 16

================================================================================

📁 FILE 86: domescard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/domescard-main/domescard-main.component.ts
📊 Thống kê: 151 điều kiện duy nhất
   - === : 27 lần
   - == : 115 lần
   - !== : 2 lần
   - != : 7 lần
--------------------------------------------------------------------------------

=== (27 điều kiện):
  1. [Dòng 156] this.themeConfig.techcombankCard === false ? false : true
  2. [Dòng 303] if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
  3. [Dòng 304] filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
  4. [Dòng 349] if (valOut === 'auth') {
  5. [Dòng 513] if (this._b === '1'
  6. [Dòng 513] this._b === '20'
  7. [Dòng 513] this._b === '64') {
  8. [Dòng 516] if (this._b === '36'
  9. [Dòng 516] this._b === '18'
  10. [Dòng 519] if (this._b === '19'
  11. [Dòng 519] this._b === '16'
  12. [Dòng 519] this._b === '25'
  13. [Dòng 519] this._b === '33'
  14. [Dòng 520] this._b === '39'
  15. [Dòng 520] this._b === '9'
  16. [Dòng 520] this._b === '11'
  17. [Dòng 520] this._b === '17'
  18. [Dòng 521] this._b === '36'
  19. [Dòng 521] this._b === '44'
  20. [Dòng 522] this._b === '64'
  21. [Dòng 525] if (this._b === '20'
  22. [Dòng 528] if (this._b === '18') {
  23. [Dòng 534] return (bankId === '1'
  24. [Dòng 534] bankId === '20'
  25. [Dòng 534] bankId === '64');
  26. [Dòng 538] return (bankId === '36'
  27. [Dòng 538] bankId === '18'

== (115 điều kiện):
  1. [Dòng 183] this._auth == 0) {
  2. [Dòng 239] if (e.id == '31') {
  3. [Dòng 241] } else if (e.id == '80') {
  4. [Dòng 247] if (!(strTest == 'card
  5. [Dòng 264] if (d.b.card_list == s) {
  6. [Dòng 285] if (item.b.id == '2'
  7. [Dòng 285] item.b.id == '67') {
  8. [Dòng 326] $event == 'true') {
  9. [Dòng 333] _b: this._b == '2' ? '67' : this._b
  10. [Dòng 355] if (bankid == 2
  11. [Dòng 355] bankid == 67) {
  12. [Dòng 358] || (off && !this.enabledTwoBankTech && ((bankid == '2'
  13. [Dòng 358] this.isOffTechcombank) || (bankid == '67'
  14. [Dòng 439] if (bankId == 1
  15. [Dòng 439] bankId == 4
  16. [Dòng 439] bankId == 7
  17. [Dòng 439] bankId == 8
  18. [Dòng 439] bankId == 9
  19. [Dòng 439] bankId == 10
  20. [Dòng 439] bankId == 11
  21. [Dòng 439] bankId == 14
  22. [Dòng 439] bankId == 15
  23. [Dòng 440] bankId == 16
  24. [Dòng 440] bankId == 17
  25. [Dòng 440] bankId == 20
  26. [Dòng 440] bankId == 22
  27. [Dòng 440] bankId == 23
  28. [Dòng 440] bankId == 24
  29. [Dòng 440] bankId == 25
  30. [Dòng 440] bankId == 30
  31. [Dòng 440] bankId == 33
  32. [Dòng 441] bankId == 34
  33. [Dòng 441] bankId == 35
  34. [Dòng 441] bankId == 36
  35. [Dòng 441] bankId == 37
  36. [Dòng 441] bankId == 38
  37. [Dòng 441] bankId == 39
  38. [Dòng 441] bankId == 40
  39. [Dòng 441] bankId == 41
  40. [Dòng 441] bankId == 42
  41. [Dòng 442] bankId == 43
  42. [Dòng 442] bankId == 44
  43. [Dòng 442] bankId == 45
  44. [Dòng 442] bankId == 46
  45. [Dòng 442] bankId == 47
  46. [Dòng 442] bankId == 48
  47. [Dòng 442] bankId == 49
  48. [Dòng 442] bankId == 50
  49. [Dòng 442] bankId == 51
  50. [Dòng 443] bankId == 52
  51. [Dòng 443] bankId == 53
  52. [Dòng 443] bankId == 54
  53. [Dòng 443] bankId == 55
  54. [Dòng 443] bankId == 56
  55. [Dòng 443] bankId == 57
  56. [Dòng 443] bankId == 58
  57. [Dòng 443] bankId == 59
  58. [Dòng 443] bankId == 60
  59. [Dòng 444] bankId == 61
  60. [Dòng 444] bankId == 62
  61. [Dòng 444] bankId == 63
  62. [Dòng 444] bankId == 64
  63. [Dòng 444] bankId == 65
  64. [Dòng 444] bankId == 66
  65. [Dòng 444] bankId == 68
  66. [Dòng 444] bankId == 69
  67. [Dòng 444] bankId == 70
  68. [Dòng 445] bankId == 71
  69. [Dòng 445] bankId == 72
  70. [Dòng 445] bankId == 73
  71. [Dòng 445] bankId == 32
  72. [Dòng 445] bankId == 74
  73. [Dòng 445] bankId == 75) {
  74. [Dòng 447] } else if (bankId == 6
  75. [Dòng 447] bankId == 31
  76. [Dòng 447] bankId == 80) {
  77. [Dòng 449] } else if (bankId == 2
  78. [Dòng 449] bankId == 67) {
  79. [Dòng 451] } else if (bankId == 3
  80. [Dòng 451] bankId == 18
  81. [Dòng 451] bankId == 19
  82. [Dòng 451] bankId == 27) {
  83. [Dòng 453] } else if (bankId == 5) {
  84. [Dòng 455] } else if (bankId == 12) {
  85. [Dòng 457] } else if (bankId == 'vietqr') {
  86. [Dòng 516] this._b == '55'
  87. [Dòng 516] this._b == '47'
  88. [Dòng 516] this._b == '48'
  89. [Dòng 516] this._b == '19'
  90. [Dòng 516] this._b == '59'
  91. [Dòng 516] this._b == '73'
  92. [Dòng 516] this._b == '12') {
  93. [Dòng 519] this._b == '3'
  94. [Dòng 520] this._b == '43'
  95. [Dòng 520] this._b == '45'
  96. [Dòng 521] this._b == '57'
  97. [Dòng 522] this._b == '61'
  98. [Dòng 522] this._b == '63'
  99. [Dòng 522] this._b == '67'
  100. [Dòng 522] this._b == '68'
  101. [Dòng 522] this._b == '69'
  102. [Dòng 522] this._b == '72'
  103. [Dòng 522] this._b == '74'
  104. [Dòng 522] this._b == '75') {
  105. [Dòng 525] this._b == '36'
  106. [Dòng 525] this._b == '75') { //sonnh them 72 Vietbank
  107. [Dòng 538] bankId == '55'
  108. [Dòng 538] bankId == '47'
  109. [Dòng 538] bankId == '48'
  110. [Dòng 538] bankId == '19'
  111. [Dòng 538] bankId == '59'
  112. [Dòng 538] bankId == '73'
  113. [Dòng 538] bankId == '5'
  114. [Dòng 538] bankId == '12');
  115. [Dòng 553] if (item['id'] == this._b) {

!== (2 điều kiện):
  1. [Dòng 118] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  2. [Dòng 290] this.bankList = this.bankList.filter(item => item.b.id !== '67');

!= (7 điều kiện):
  1. [Dòng 169] if (params['locale'] != null) {
  2. [Dòng 175] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 179] this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
  4. [Dòng 206] if (!(strInstrument != null
  5. [Dòng 209] if (strInstrument.substring(0, 1) != '^'
  6. [Dòng 209] strInstrument.substr(strInstrument.length - 1) != '$') {
  7. [Dòng 417] if (bankid != null) {

================================================================================

📁 FILE 87: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/intercard-main/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 88: intercard-main-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/intercard-main/form/intercard-main-form.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 67] (!!exp_date + !!csc + !!card_name)==2"

================================================================================

📁 FILE 89: intercard-main-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/intercard-main/form/intercard-main-form.component.ts
📊 Thống kê: 73 điều kiện duy nhất
   - === : 14 lần
   - == : 37 lần
   - !== : 9 lần
   - != : 13 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 170] if (target.tagName === 'A'
  2. [Dòng 202] if (this.themeConfig && (this.themeConfig.csc_config === false)) {
  3. [Dòng 244] this._showName = (config.name === "0")? false : true;
  4. [Dòng 245] this._showEmailPhone = (config.email_phone === "0")? false : true;
  5. [Dòng 336] if (_formCard.country === 'default') {
  6. [Dòng 618] if (event.keyCode === 8
  7. [Dòng 618] event.key === "Backspace"
  8. [Dòng 702] if ((v.substr(-1) === ' '
  9. [Dòng 961] this._i_country_code === 'US') {
  10. [Dòng 999] const insertIndex = this._i_country_code === 'US' ? 5 : 3
  11. [Dòng 1001] if (temp[i] === '-'
  12. [Dòng 1001] temp[i] === ' ') {
  13. [Dòng 1008] insertIndex === 3 ? ' ' : itemRemoved)
  14. [Dòng 1064] this.c_country = _val.value === 'default'

== (37 điều kiện):
  1. [Dòng 404] if (this._res_post.state == 'approved'
  2. [Dòng 404] this._res_post.state == 'failed') {
  3. [Dòng 432] } else if(this._res_post.state == 'failed') { // lỗi mới kiểm tra sang trang lỗi
  4. [Dòng 461] } else if (this._res_post.state == 'authorization_required') {
  5. [Dòng 462] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  6. [Dòng 474] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  7. [Dòng 520] v.length == 15) || (v.length == 16
  8. [Dòng 520] v.length == 19))
  9. [Dòng 521] this._util.checkMod10(v) == true) {
  10. [Dòng 569] cardNo.length == 15)
  11. [Dòng 571] cardNo.length == 16)
  12. [Dòng 572] cardNo.startsWith('81')) && (cardNo.length == 16
  13. [Dòng 572] cardNo.length == 19))
  14. [Dòng 618] event.inputType == 'deleteContentBackward') {
  15. [Dòng 619] if (event.target.name == 'exp_date'
  16. [Dòng 627] event.inputType == 'insertCompositionText') {
  17. [Dòng 642] if (((this.valueDate.length == 4
  18. [Dòng 642] this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  19. [Dòng 642] this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
  20. [Dòng 702] v.length == 5) {
  21. [Dòng 710] v.length == 4
  22. [Dòng 714] v.length == 3)
  23. [Dòng 744] _val.value.length == 4
  24. [Dòng 748] _val.value.length == 3)
  25. [Dòng 787] this._i_first_name.trim().length == 0
  26. [Dòng 794] this._i_last_name.trim().length == 0
  27. [Dòng 1016] this.valueDate.search('/') == -1) || this.valueDate.length == 5)
  28. [Dòng 1016] this.valueDate.length == 5)
  29. [Dòng 1049] this.requireAvs = this.AVS_COUNTRIES.find(e => e.code == this._i_country_code) ? true : false;
  30. [Dòng 1114] this.valueDate.length == 4
  31. [Dòng 1114] this.valueDate.search('/') == -1)
  32. [Dòng 1115] this.valueDate.length == 5))
  33. [Dòng 1129] this._i_csc.length == 4) ||
  34. [Dòng 1133] this._i_csc.length == 3)
  35. [Dòng 1218] country = this.AVS_COUNTRIES.find(e => e.code == res.bin_country);
  36. [Dòng 1253] countryCode == 'US' ? US_STATES
  37. [Dòng 1254] : countryCode == 'CA' ? CA_STATES

!== (9 điều kiện):
  1. [Dòng 414] codeResponse.toString() !== '0'){
  2. [Dòng 702] event.inputType !== 'deleteContentBackward') || v.length == 5) {
  3. [Dòng 964] this._i_country_code !== 'US') {
  4. [Dòng 1007] itemRemoved !== '') {
  5. [Dòng 1041] if (deviceValue !== 'default') {
  6. [Dòng 1045] this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
  7. [Dòng 1141] this._i_country_code !== 'default'
  8. [Dòng 1176] this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
  9. [Dòng 1183] this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {

!= (13 điều kiện):
  1. [Dòng 216] if (params['locale'] != null) {
  2. [Dòng 222] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 406] if (this._res_post.return_url != null) {
  4. [Dòng 408] } else if (this._res_post.links != null
  5. [Dòng 408] this._res_post.links.merchant_return != null
  6. [Dòng 408] this._res_post.links.merchant_return.href != null) {
  7. [Dòng 502] if (ua.indexOf('safari') != -1
  8. [Dòng 569] cardNo != null
  9. [Dòng 620] if (this.valueDate.length != 3) {
  10. [Dòng 709] v != null
  11. [Dòng 743] this.c_csc = (!(_val.value != null
  12. [Dòng 1127] this._i_csc != null
  13. [Dòng 1220] this.requireAvs = this.isAvsCountry = country != undefined

================================================================================

📁 FILE 90: intercard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/intercard-main/intercard-main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 91: intercard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/intercard-main/intercard-main.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 90] if (_re.status == '200'
  2. [Dòng 90] _re.status == '201') {

================================================================================

📁 FILE 92: menu.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/menu.component.html
📊 Thống kê: 55 điều kiện duy nhất
   - === : 6 lần
   - == : 43 lần
   - !== : 2 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (6 điều kiện):
  1. [Dòng 251] bnpl.code === 'kbank'"
  2. [Dòng 254] bnpl.code === 'homepaylater'"
  3. [Dòng 334] *ngIf="(type === 5
  4. [Dòng 395] type === 5"
  5. [Dòng 396] [ngStyle]="{'border-color': (type === 5
  6. [Dòng 407] d_vrbank===true"

== (43 điều kiện):
  1. [Dòng 82] !token) || (type == 1
  2. [Dòng 100] !token) || (type == 6
  3. [Dòng 135] type == 2)"
  4. [Dòng 144] !token) || (type == 3
  5. [Dòng 157] !token) || (type == 5
  6. [Dòng 178] method.trim()=='International'"
  7. [Dòng 188] method.trim()=='ApplePay'"
  8. [Dòng 195] method.trim()=='GooglePay'"
  9. [Dòng 202] method.trim()=='SamsungPay'"
  10. [Dòng 208] method.trim()=='Domestic'"
  11. [Dòng 217] method.trim()=='QR'"
  12. [Dòng 225] method.trim()=='Paypal'"
  13. [Dòng 233] method.trim()=='VietQR'
  14. [Dòng 239] <div *ngIf="((onePaymentMethod == true
  15. [Dòng 239] d_bnpl) || (onePaymentMethod == false
  16. [Dòng 239] d_bnpl_number == 1
  17. [Dòng 240] method.trim()=='Bnpl'
  18. [Dòng 246] bnpl.status == 'disabled'"
  19. [Dòng 264] providerType == bnpl.code"
  20. [Dòng 265] bnpl.status == 'disabled'
  21. [Dòng 269] onePaymentMethod == false
  22. [Dòng 269] d_bnpl_number == 1"
  23. [Dòng 288] type==5 && providerType==bnpl.code
  24. [Dòng 288] providerType==bnpl.code"
  25. [Dòng 293] onePaymentMethod == true
  26. [Dòng 309] bnpl.code == 'kbank'
  27. [Dòng 313] bnpl.code == 'insta'
  28. [Dòng 318] bnpl.code == 'homepaylater'
  29. [Dòng 328] bnpl.code == 'kredivo'
  30. [Dòng 334] bnpl.status == 'active'
  31. [Dòng 335] providerType == bnpl.code)
  32. [Dòng 336] || (d_bnpl_number == 1
  33. [Dòng 336] providerType == bnpl.code))
  34. [Dòng 338] || (bnpl.status == 'active'
  35. [Dòng 338] d_bnpl_number == 1)">
  36. [Dòng 340] bnpl.code == 'insta'"
  37. [Dòng 349] bnpl.code == 'kbank'"
  38. [Dòng 356] bnpl.code == 'homepaylater'"
  39. [Dòng 364] bnpl.code == 'kredivo'"
  40. [Dòng 371] bnpl.code == 'fundiin'"
  41. [Dòng 397] _auth == 1
  42. [Dòng 397] [class.enable_form]="!onePaymentMethod || (onePaymentMethod && d_bnpl)" *ngIf="_auth == 1 && ((onePaymentMethod == true
  43. [Dòng 397] d_bnpl_number == 1)

!== (2 điều kiện):
  1. [Dòng 257] bnpl.code !== 'kbank'
  2. [Dòng 276] bnpl.code !== 'kbank'"

!= (4 điều kiện):
  1. [Dòng 240] _auth != '1'"
  2. [Dòng 257] bnpl.code != 'homepaylater'"
  3. [Dòng 313] <div *ngIf="bnpl.code == 'insta' && ((selectedBnpl != bnpl) || (d_bnpl_number == 1))"
  4. [Dòng 318] <div *ngIf="bnpl.code == 'homepaylater' && ((selectedBnpl != bnpl) || (d_bnpl_number == 1))"

================================================================================

📁 FILE 93: menu.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/menu.component.ts
📊 Thống kê: 189 điều kiện duy nhất
   - === : 21 lần
   - == : 98 lần
   - !== : 5 lần
   - != : 65 lần
--------------------------------------------------------------------------------

=== (21 điều kiện):
  1. [Dòng 302] if (document.visibilityState === 'visible') {
  2. [Dòng 688] this._res.merchant.international_card_title === '2') {
  3. [Dòng 741] oneBnplProvider === 1) {
  4. [Dòng 973] this.homecreditProvider = this.bankAmountArr.find(e => e.code === 'homepaylater');
  5. [Dòng 977] this.kredivoProvider = this.bankAmountArr.find(e => e.code === 'kredivo');
  6. [Dòng 980] this.fundiinProvider = this.bankAmountArr.find(e => e.code === 'fundiin');
  7. [Dòng 1032] this.bnpls.push(this.sublist.find(e => e.name === 'KBank Pay Later'));
  8. [Dòng 1035] this.bnpls.push(this.sublist.find(e => e.name === 'Home PayLater'));
  9. [Dòng 1038] this.bnpls.push(this.sublist.find(e => e.name === 'Kredivo'));
  10. [Dòng 1041] this.bnpls.push(this.sublist.find(e => e.name === 'Insta'));
  11. [Dòng 1044] this.bnpls.push(this.sublist.find(e => e.name === 'Fundiin'));
  12. [Dòng 1129] this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_bnpl + this.d_mobile_wallet + this.d_vietqr_1 === 1
  13. [Dòng 1182] if (count === 1
  14. [Dòng 1187] if (offBanksArr[i] === this.lastDomescard) {
  15. [Dòng 1204] if (this._res.state === 'unpaid'
  16. [Dòng 1204] this._res.state === 'not_paid') {
  17. [Dòng 1343] if ('op' === auth
  18. [Dòng 1384] } else if ('bank' === auth
  19. [Dòng 1389] if (approval.method === 'REDIRECT') {
  20. [Dòng 1392] } else if (approval.method === 'POST_REDIRECT') {
  21. [Dòng 1811] if (this.timeLeftPaypal === 0) {

== (98 điều kiện):
  1. [Dòng 309] if (el == 1) {
  2. [Dòng 311] } else if (el == 2) {
  3. [Dòng 313] } else if (el == 4) {
  4. [Dòng 315] } else if (el == 3) {
  5. [Dòng 351] if (!isNaN(_re.status) && (_re.status == '200'
  6. [Dòng 351] _re.status == '201') && _re.body != null) {
  7. [Dòng 356] if (('closed' == this._res_polling.state
  8. [Dòng 356] 'canceled' == this._res_polling.state
  9. [Dòng 356] 'expired' == this._res_polling.state)
  10. [Dòng 379] } else if ('paid' == this._res_polling.state) {
  11. [Dòng 395] this._res_polling.payments == null
  12. [Dòng 404] if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'authorization_required') {
  13. [Dòng 408] } else if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  14. [Dòng 415] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  15. [Dòng 417] this._paymentService.getCurrentPage() == 'enter_card'
  16. [Dòng 420] } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
  17. [Dòng 420] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
  18. [Dòng 437] } else if ('not_paid' == this._res_polling.state) {
  19. [Dòng 449] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
  20. [Dòng 625] if (auth == 'auth') {
  21. [Dòng 627] detail.merchant.id == 'AMWAY') {
  22. [Dòng 655] if (this.checkInvoiceState() == 1) {
  23. [Dòng 668] _re.body?.merchant?.qr_version == '2');
  24. [Dòng 673] if (_re.status == '200'
  25. [Dòng 673] _re.status == '201') {
  26. [Dòng 681] this._res.payments[this._res.payments.length - 1].state == 'pending'
  27. [Dòng 682] this._res.payments[this._res.payments.length - 1].instrument.type == 'applepay') {
  28. [Dòng 709] if (this.themeConfig.default_method == 'International'
  29. [Dòng 711] } else if (this.themeConfig.default_method == 'Domestic'
  30. [Dòng 713] } else if (this.themeConfig.default_method == 'QR'
  31. [Dòng 715] } else if (this.themeConfig.default_method == 'Paypal'
  32. [Dòng 819] if (value == true) {
  33. [Dòng 843] if (('canceled' == this._res.state
  34. [Dòng 843] 'paid' == this._res.state)
  35. [Dòng 847] if ('paid' == this._res.state
  36. [Dòng 889] this.stopCounter == 'stop')) {  // Check if X is true
  37. [Dòng 1048] if (this.onePaymentMethod == true
  38. [Dòng 1048] this.d_bnpl_number == 1
  39. [Dòng 1048] this.d_bnpl == 1) {
  40. [Dòng 1092] if (this.d_amigo == true
  41. [Dòng 1092] this.d_insta == false
  42. [Dòng 1092] this.d_instaplus == false) {
  43. [Dòng 1099] if (this.version2 == "2") {
  44. [Dòng 1121] if (this.d_amigo_number == 0
  45. [Dòng 1121] this.d_insta_number == 1) {
  46. [Dòng 1124] else if (this.d_amigo_number == 0
  47. [Dòng 1124] this.d_instaplus_number == 1) {
  48. [Dòng 1130] if ((this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_bnpl + this.d_mobile_wallet + this.d_vietqr_1) == 0
  49. [Dòng 1155] this._auth == 0) {
  50. [Dòng 1166] this._res.themes.techcombankCard == false ? false : true
  51. [Dòng 1168] if (count == 2
  52. [Dòng 1205] if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
  53. [Dòng 1205] this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
  54. [Dòng 1207] if (this._res.payments[this._res.payments.length - 1].state == 'approved'
  55. [Dòng 1213] } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
  56. [Dòng 1227] || (responseCode == '1'
  57. [Dòng 1227] instrumentType == 'vietqr'))
  58. [Dòng 1264] if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required') {
  59. [Dòng 1270] } else if (this._res.payments[this._res.payments.length - 1].state == 'pending') {
  60. [Dòng 1276] } else if (this._res.payments[this._res.payments.length - 1].state == 'canceled') {
  61. [Dòng 1280] this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  62. [Dòng 1282] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id == 'vietqr'
  63. [Dòng 1289] } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  64. [Dòng 1289] this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
  65. [Dòng 1326] if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  66. [Dòng 1330] } else if (idBrand == 'atm'
  67. [Dòng 1409] else if (idBrand == 'kbank'
  68. [Dòng 1513] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
  69. [Dòng 1528] if (this._res.payments[this._res.payments.length - 1].state == 'pending'
  70. [Dòng 1538] if ('paid' == this._res.state) {
  71. [Dòng 1562] if (('closed' == this._res.state
  72. [Dòng 1562] 'canceled' == this._res.state
  73. [Dòng 1562] 'expired' == this._res.state
  74. [Dòng 1565] 'canceled' == this._res.state) {
  75. [Dòng 1584] this._res.payments[this._res.payments.length - 1].state == 'pending') {
  76. [Dòng 1605] if (this.d_inter == 1) {
  77. [Dòng 1612] } else if (this.type == 1) {
  78. [Dòng 1719] if (type == 'qrv1') {
  79. [Dòng 1729] if (type == 'mobile') {
  80. [Dòng 1731] e.type == 'ewallet'
  81. [Dòng 1731] e.code == 'momo')) {
  82. [Dòng 1739] } else if (type == 'desktop') {
  83. [Dòng 1740] e.type == 'vnpayqr') || (regex.test(strTest)
  84. [Dòng 1861] if (this._res_post.state == 'approved'
  85. [Dòng 1861] this._res_post.state == 'failed') {
  86. [Dòng 1870] } else if (this._res_post.state == 'authorization_required') {
  87. [Dòng 1871] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  88. [Dòng 1885] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  89. [Dòng 1959] if (bankid == 2
  90. [Dòng 1959] bankid == 67) {
  91. [Dòng 1960] if (isBankOff && ((bankid == '2'
  92. [Dòng 1960] !this.isOffTechcombank) || (bankid == '67'
  93. [Dòng 2010] if (res.status == '200'
  94. [Dòng 2010] res.status == '201') {
  95. [Dòng 2039] return (this.currentMethod == 7) // PTTT riêng VietQR
  96. [Dòng 2040] || (this.currentMethod == 2
  97. [Dòng 2040] this._b == 'vietqr')    // PTTT domes vietqr
  98. [Dòng 2056] if (data._locale == 'en') {

!== (5 điều kiện):
  1. [Dòng 764] if (this.qr_version2 !== 'qrV1'
  2. [Dòng 764] this.qr_version2 !== '') {
  3. [Dòng 1359] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  4. [Dòng 1778] if (_val !== 3) {
  5. [Dòng 1782] this._util.getElementParams(this.currentUrl, 't_id') !== '') {

!= (65 điều kiện):
  1. [Dòng 329] this.currentMethod != selected) {
  2. [Dòng 344] if (this._idInvoice != null
  3. [Dòng 344] this._paymentService.getState() != 'error') {
  4. [Dòng 350] if (this._paymentService.getCurrentPage() != 'otp') {
  5. [Dòng 351] _re.body != null) {
  6. [Dòng 357] this._res_polling.links != null
  7. [Dòng 357] this._res_polling.links.merchant_return != null //
  8. [Dòng 360] this._util.getElementParams(url_redirect, 'vpc_TxnResponseCode') != '99') {
  9. [Dòng 395] } else if (this._res_polling.merchant != null
  10. [Dòng 395] this._res_polling.merchant_invoice_reference != null
  11. [Dòng 397] } else if (this._res_polling.payments != null
  12. [Dòng 417] this._res_polling.payments[this._res_polling.payments.length - 1].instrument.brand != 'amigo') {
  13. [Dòng 421] this._res_polling.links.merchant_return != null//
  14. [Dòng 440] this._res_polling.payments != null
  15. [Dòng 448] if (this._res_polling.payments != null
  16. [Dòng 452] t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
  17. [Dòng 634] this.type.toString().length != 0) {
  18. [Dòng 641] if (params['locale'] != null) {
  19. [Dòng 648] if ('otp' != this._paymentService.getCurrentPage()) {
  20. [Dòng 652] if (this._paymentService.getInvoiceDetail() != null) {
  21. [Dòng 681] if (this._res.payments != null
  22. [Dòng 684] this._res.payments[this._res.payments.length - 1].instrument.brand != 'amigo') {
  23. [Dòng 795] if (this._idInvoice != null) {
  24. [Dòng 814] const tokenListsFilter = tokenLists.filter(item => item.id != tokenData);
  25. [Dòng 844] this._res.links != null
  26. [Dòng 844] this._res.links.merchant_return != null
  27. [Dòng 1178] if (count != 1) {
  28. [Dòng 1194] if (this._res.merchant != null
  29. [Dòng 1194] this._res.merchant_invoice_reference != null) {
  30. [Dòng 1197] if (this._res.merchant.address_details != null) {
  31. [Dòng 1205] this._res.links != null//
  32. [Dòng 1259] } else if (this._res.payments != null
  33. [Dòng 1281] this._res.payments[this._res.payments.length - 1].instrument != null
  34. [Dòng 1281] this._res.payments[this._res.payments.length - 1].instrument.issuer != null
  35. [Dòng 1282] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
  36. [Dòng 1286] if (this.type != 7) {
  37. [Dòng 1291] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
  38. [Dòng 1292] this._res.payments[this._res.payments.length - 1].links != null
  39. [Dòng 1292] this._res.payments[this._res.payments.length - 1].links.cancel != null
  40. [Dòng 1292] this._res.payments[this._res.payments.length - 1].links.cancel.href != null
  41. [Dòng 1311] this._res.payments[this._res.payments.length - 1].links.update != null
  42. [Dòng 1311] this._res.payments[this._res.payments.length - 1].links.update.href != null) {
  43. [Dòng 1330] this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
  44. [Dòng 1331] this._res.payments[this._res.payments.length - 1].authorization != null
  45. [Dòng 1331] this._res.payments[this._res.payments.length - 1].authorization.links != null) {
  46. [Dòng 1343] this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
  47. [Dòng 1343] this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
  48. [Dòng 1346] if (this._res.payments[this._res.payments.length - 1].authorization != null
  49. [Dòng 1346] this._res.payments[this._res.payments.length - 1].authorization.links != null
  50. [Dòng 1352] auth = paramUserName != null ? paramUserName : ''
  51. [Dòng 1422] if (this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
  52. [Dòng 1563] this._res.links.merchant_return != null //
  53. [Dòng 1687] if (!(strInstrument != null
  54. [Dòng 1704] if (this._translate.currentLang != language) {
  55. [Dòng 1731] e.type != 'ewallet') || (regex.test(strTest)
  56. [Dòng 1784] } else if (this._res.payments != null) {
  57. [Dòng 1863] if (this._res_post.return_url != null) {
  58. [Dòng 1865] } else if (this._res_post.links != null
  59. [Dòng 1865] this._res_post.links.merchant_return != null
  60. [Dòng 1865] this._res_post.links.merchant_return.href != null) {
  61. [Dòng 1927] _re.body?.state != 'canceled')
  62. [Dòng 2011] if (res.body != null
  63. [Dòng 2011] res.body.links != null
  64. [Dòng 2011] res.body.links.merchant_return != null
  65. [Dòng 2012] res.body.links.merchant_return.href != null) {

================================================================================

📁 FILE 94: applepay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/applepay/applepay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 95: applepay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/applepay/applepay.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 39] if (event.data?.event_type == 'applepay_network_not_supported') {

!= (1 điều kiện):
  1. [Dòng 37] if (event.origin != window.origin) return; // chỉ nhận message từ OnePay

================================================================================

📁 FILE 96: dialog-network-not-supported.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/applepay/dialog-network-not-supported/dialog-network-not-supported.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 97: dialog-network-not-supported.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/applepay/dialog-network-not-supported/dialog-network-not-supported.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 98: google-pay-button-op.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/googlepay/google-pay-button-op/google-pay-button-op.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 99: google-pay-button-op.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/googlepay/google-pay-button-op/google-pay-button-op.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 185] if (GGPaySDKScript.readyState === "loaded"
  2. [Dòng 185] GGPaySDKScript.readyState === "complete") {

================================================================================

📁 FILE 100: types-google-pay.d.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/googlepay/google-pay-button-op/types-google-pay.d.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 101: googlepay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/googlepay/googlepay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 102: googlepay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/googlepay/googlepay.component.ts
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 25] isTop = window === window.top
  2. [Dòng 59] if (approval.method === 'REDIRECT') {
  3. [Dòng 62] } else if (approval.method === 'POST_REDIRECT') {

== (2 điều kiện):
  1. [Dòng 48] if (res?.body?.state == 'approved') {
  2. [Dòng 57] } else if (res?.body?.state == 'authorization_required') {

================================================================================

📁 FILE 103: mobile-wallet-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/mobile-wallet-main.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (3 điều kiện):
  1. [Dòng 7] paymentType == PaymentType.ApplePay"
  2. [Dòng 8] paymentType == PaymentType.GooglePay"
  3. [Dòng 9] paymentType == PaymentType.SamsungPay"

================================================================================

📁 FILE 104: mobile-wallet-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/mobile-wallet-main.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 86] return currency === 'VND'

== (2 điều kiện):
  1. [Dòng 47] if (this.paymentType == PaymentType.ApplePay) {
  2. [Dòng 74] if (network == 'napas'

!= (3 điều kiện):
  1. [Dòng 57] if (this.paymentType != PaymentType.ApplePay) return;
  2. [Dòng 69] if (this.paymentType != PaymentType.ApplePay) return false;
  3. [Dòng 75] if (network != 'napas'

================================================================================

📁 FILE 105: samsung-pay-button-op.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 106: samsung-pay-button-op.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/samsung-pay-button-op.component.ts
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 128] if (GGPaySDKScript.readyState === "loaded"
  2. [Dòng 128] GGPaySDKScript.readyState === "complete") {

== (1 điều kiện):
  1. [Dòng 63] serviceId: this.environment == 'PRODUCTION'? this.serviceIdProd : this.serviceIdTest

================================================================================

📁 FILE 107: types-samsung-pay.d.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/samsungpay/samsung-pay-button-op/types-samsung-pay.d.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 108: samsungpay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/samsungpay/samsungpay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 109: samsungpay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/mobile-wallet-main/samsungpay/samsungpay.component.ts
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 74] if (approval.method === 'REDIRECT') {
  2. [Dòng 77] } else if (approval.method === 'POST_REDIRECT') {

== (2 điều kiện):
  1. [Dòng 62] if (res?.body?.state == 'approved') {
  2. [Dòng 72] } else if (res?.body?.state == 'authorization_required') {

================================================================================

📁 FILE 110: policy-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/policy-dialog/policy-dialog/policy-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 111: policy-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/policy-dialog/policy-dialog/policy-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 112: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 113: qr-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main/qr-dialog/qr-dialog.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 2] screen=='qr'"
  2. [Dòng 115] screen=='confirm_close'"

================================================================================

📁 FILE 114: qr-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main/qr-dialog/qr-dialog.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 45] 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {

================================================================================

📁 FILE 115: qr-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main/qr-guide-dialog/qr-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 116: qr-guide-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main/qr-guide-dialog/qr-guide-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 117: qr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main/qr-main.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 6] filteredData.length === 0
  2. [Dòng 6] filteredDataOther.length === 0"
  3. [Dòng 47] filteredDataMobile.length === 0
  4. [Dòng 47] filteredDataOtherMobile.length === 0"

================================================================================

📁 FILE 118: qr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main/qr-main.component.ts
📊 Thống kê: 26 điều kiện duy nhất
   - === : 11 lần
   - == : 9 lần
   - !== : 0 lần
   - != : 6 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 228] this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
  2. [Dòng 229] this.filteredDataOther = this.appList.filter(item => item.type === 'other');
  3. [Dòng 230] this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
  4. [Dòng 231] this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
  5. [Dòng 257] this.appList.length === 1) {
  6. [Dòng 259] if ((this.filteredDataMobile.length === 1
  7. [Dòng 259] this.filteredDataOtherMobile.length === 1)
  8. [Dòng 314] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  9. [Dòng 315] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  10. [Dòng 321] if (item.type === 'mobile_banking') {
  11. [Dòng 703] this.appList.length === 1

== (9 điều kiện):
  1. [Dòng 158] this.themeConfig.deeplink_status == 'Off' ? false : true
  2. [Dòng 271] if (d.b.code == s) {
  3. [Dòng 320] if (item.available == true) {
  4. [Dòng 391] if (_re.status == '200'
  5. [Dòng 391] _re.status == '201') {
  6. [Dòng 394] if (appcode == 'grabpay'
  7. [Dòng 394] appcode == 'momo') {
  8. [Dòng 397] if (type == 2
  9. [Dòng 434] err.error.code == '04') {

!= (6 điều kiện):
  1. [Dòng 174] if (params['locale'] != null) {
  2. [Dòng 180] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 205] if (!(strInstrument != null
  4. [Dòng 350] if (appcode != null) {
  5. [Dòng 678] if (_re.status != '200'
  6. [Dòng 678] _re.status != '201')

================================================================================

📁 FILE 119: safe-html.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main/safe-html.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 120: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-v2/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 121: qr-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-v2/qr-dialog-v2/qr-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 122: qr-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-v2/qr-dialog-v2/qr-dialog.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 50] 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {

================================================================================

📁 FILE 123: list-bank-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-v2/qr-listbank/list-bank-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 124: list-bank-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-v2/qr-listbank/list-bank-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 125: qr-main-v2.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-v2/qr-main-v2.component.html
📊 Thống kê: 13 điều kiện duy nhất
   - === : 1 lần
   - == : 7 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 66] filteredSupportedApp.length === 0"

== (7 điều kiện):
  1. [Dòng 226] deeplink_status) || (qr_version2 == 'MSB'
  2. [Dòng 226] qr_version2 == 'Viettin')"
  3. [Dòng 236] _locale=='vi'"
  4. [Dòng 237] _locale=='en'"
  5. [Dòng 251] _locale == 'vi'
  6. [Dòng 253] _locale == 'en'
  7. [Dòng 291] qr_version2 == 'None'"

!= (5 điều kiện):
  1. [Dòng 223] filteredDataOtherMobile.length > 0 && ((filteredDataDeeplink.length > 0 && deeplink_status) && (qr_version2 != 'None'))
  2. [Dòng 232] qr_version2 != 'None'
  3. [Dòng 235] qr_version2 != 'None'"
  4. [Dòng 339] !(filteredDataOtherMobile.length > 0 && ((filteredDataDeeplink.length > 0 && deeplink_status) && (qr_version2 != 'None')))
  5. [Dòng 428] qr_version2 != 'None')">

================================================================================

📁 FILE 126: qr-main-v2.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-v2/qr-main-v2.component.ts
📊 Thống kê: 35 điều kiện duy nhất
   - === : 11 lần
   - == : 18 lần
   - !== : 1 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 321] this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
  2. [Dòng 323] this.filteredSupportedApp = this.listAppSupport.filter(item => item.type === 'mobile_banking'
  3. [Dòng 324] this.filteredDataOther = this.appListEWallet.filter(item => item.type === 'other');
  4. [Dòng 325] this.filteredDataOtherMobile = this.appListEWallet.filter(item => item.type === 'other');
  5. [Dòng 329] this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
  6. [Dòng 352] this.appList.length === 1) {
  7. [Dòng 354] if ((this.filteredDataMobile.length === 1
  8. [Dòng 354] this.filteredDataOtherMobile.length === 1)
  9. [Dòng 406] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  10. [Dòng 407] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  11. [Dòng 478] item.code === "vietinbankipay") {

== (18 điều kiện):
  1. [Dòng 199] this.themeConfig.deeplink_status == 'Off' ? false : true
  2. [Dòng 247] this.qr_version2 == 'MSB') {
  3. [Dòng 262] this.qr_version2 == 'Viettin') {//qrversion2 = Viettin
  4. [Dòng 322] this.filteredDataDeeplink = this.filteredData.filter(item => item.deep_link == true);
  5. [Dòng 330] this.qr_version2 == 'Viettin') {
  6. [Dòng 366] if (d.b.code == s) {
  7. [Dòng 412] if (item.available == true) {
  8. [Dòng 448] if (this.qr_version2 == 'MSB') {
  9. [Dòng 459] item.code == "msbmbank_970426") {
  10. [Dòng 491] if (_re.status == '200'
  11. [Dòng 491] _re.status == '201') {
  12. [Dòng 568] if (appcode == 'grabpay'
  13. [Dòng 568] appcode == 'momo') {
  14. [Dòng 571] if (type == 2
  15. [Dòng 607] err.error.code == '04') {
  16. [Dòng 832] if (type == 'both'
  17. [Dòng 834] } else if (type == 'both'
  18. [Dòng 836] this.qr_version2 == 'None'

!== (1 điều kiện):
  1. [Dòng 333] this.filteredDataOtherMobile = this.filteredDataOtherMobile.filter(item => item.code !== "smartpay");

!= (5 điều kiện):
  1. [Dòng 216] if (params['locale'] != null) {
  2. [Dòng 222] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 239] if (!(strInstrument != null
  4. [Dòng 530] if (appcode != null) {
  5. [Dòng 832] this.qr_version2 != 'None') {

================================================================================

📁 FILE 127: qr-vnpay-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-v2/qr-vnpay-dialog-v2/qr-vnpay-dialog.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (3 điều kiện):
  1. [Dòng 5] dialogType == 'vnpay'"
  2. [Dòng 6] dialogType == 'bankapp'"
  3. [Dòng 7] dialogType == 'both'"

================================================================================

📁 FILE 128: qr-vnpay-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-v2/qr-vnpay-dialog-v2/qr-vnpay-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 129: safe-html.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-v2/safe-html.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 130: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 131: qr-desktop.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 1 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 168] listVNPayQR.length === 0"

================================================================================

📁 FILE 132: qr-desktop.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-desktop/qr-desktop.component.ts
📊 Thống kê: 19 điều kiện duy nhất
   - === : 4 lần
   - == : 10 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 384] this.listWalletQR.length === 1) {
  2. [Dòng 434] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  3. [Dòng 435] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  4. [Dòng 797] this.listWalletQR.length === 1

== (10 điều kiện):
  1. [Dòng 272] e.type == 'vnpayqr') {
  2. [Dòng 279] e.type == 'ewallet') {
  3. [Dòng 331] if (_re.status == '200'
  4. [Dòng 331] _re.status == '201') {
  5. [Dòng 362] e.type == 'wallet')) {
  6. [Dòng 401] if (d.b.code == s) {
  7. [Dòng 440] if (item.available == true) {
  8. [Dòng 500] if (appcode == 'grabpay'
  9. [Dòng 500] appcode == 'momo') {
  10. [Dòng 535] err.error.code == '04') {

!= (5 điều kiện):
  1. [Dòng 225] if (params['locale'] != null) {
  2. [Dòng 262] if (!(strInstrument != null
  3. [Dòng 458] if (appcode != null) {
  4. [Dòng 770] if (_re.status != '200'
  5. [Dòng 770] _re.status != '201')

================================================================================

📁 FILE 133: qr-dialog-version2.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog-version2.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1] screen=='qr'"
  2. [Dòng 122] screen=='confirm_close'"

================================================================================

📁 FILE 134: qr-dialog-version2.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-dialog/qr-dialog-version2.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!= (1 điều kiện):
  1. [Dòng 49] 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {

================================================================================

📁 FILE 135: qr-guide-dialog-version2.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog-version2.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (3 điều kiện):
  1. [Dòng 5] type == 'vnpay'"
  2. [Dòng 6] type == 'bankapp'"
  3. [Dòng 7] type == 'both'"

================================================================================

📁 FILE 136: qr-guide-dialog-version2.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-guide-dialog/qr-guide-dialog-version2.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 137: list-bank-dialog-version2.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog-version2.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 138: list-bank-dialog-version2.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-listbank/list-bank-dialog-version2.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 139: qr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 140: qr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-main.component.ts
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 188] this.themeConfig.deeplink_status == 'Off' ? false : true

!= (2 điều kiện):
  1. [Dòng 204] if (params['locale'] != null) {
  2. [Dòng 210] if ('otp' != this._paymentService.getCurrentPage()) {

================================================================================

📁 FILE 141: qr-mobile.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 134] _locale=='vi'"
  2. [Dòng 135] _locale=='en'"
  3. [Dòng 145] _locale == 'vi'"
  4. [Dòng 147] _locale == 'en'"

!= (2 điều kiện):
  1. [Dòng 234] qr_version2 != 'None'"
  2. [Dòng 260] qr_version2 != 'None'

================================================================================

📁 FILE 142: qr-mobile.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/qr-mobile/qr-mobile.component.ts
📊 Thống kê: 30 điều kiện duy nhất
   - === : 6 lần
   - == : 19 lần
   - !== : 0 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (6 điều kiện):
  1. [Dòng 472] if (!this.d_vnpayqr_wallet && !this.d_vnpayqr_deeplink && (this.listWalletQR?.length === 1
  2. [Dòng 472] this.listWalletDeeplink?.length === 1)) {
  3. [Dòng 591] item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
  4. [Dòng 592] filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
  5. [Dòng 598] if (item.type === 'deeplink') {
  6. [Dòng 994] this.listWalletQR?.length === 1

== (19 điều kiện):
  1. [Dòng 280] e.type == 'deeplink') {
  2. [Dòng 291] e.type == 'ewallet'
  3. [Dòng 311] e.type == 'vnpayqr') {
  4. [Dòng 325] e.type == 'wallet')) {
  5. [Dòng 354] e.type == 'ewallet') {
  6. [Dòng 384] if (e.type == 'ewallet') {
  7. [Dòng 407] this.listWallet.length == 1
  8. [Dòng 407] this.listWallet[0].code == 'momo') {
  9. [Dòng 409] this.checkEWalletDeeplink.length == 0) {
  10. [Dòng 490] arrayWallet.length == 0) return false;
  11. [Dòng 492] if (arrayWallet[i].code == key) {
  12. [Dòng 526] if (_re.status == '200'
  13. [Dòng 526] _re.status == '201') {
  14. [Dòng 548] if (d.b.code == s) {
  15. [Dòng 597] if (item.available == true) {
  16. [Dòng 674] if (appcode == 'grabpay'
  17. [Dòng 674] appcode == 'momo') {
  18. [Dòng 677] if (type == 2
  19. [Dòng 718] err.error.code == '04') {

!= (5 điều kiện):
  1. [Dòng 232] if (params['locale'] != null) {
  2. [Dòng 261] if (!(strInstrument != null
  3. [Dòng 625] if (appcode != null) {
  4. [Dòng 965] if (_re.status != '200'
  5. [Dòng 965] _re.status != '201')

================================================================================

📁 FILE 143: safe-html.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/qr-main-version2/safe-html.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 144: queuing.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/queuing/queuing.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 145: queuing.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/queuing/queuing.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 62] if (this.locale == 'vi') {

!= (1 điều kiện):
  1. [Dòng 86] if (this.translate.currentLang != language) {

================================================================================

📁 FILE 146: bnpl-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/bnpl-form/bnpl-form.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 1] type === 5"
  2. [Dòng 2] [ngStyle]="{'border-color': (type === 5
  3. [Dòng 17] type === 5

== (2 điều kiện):
  1. [Dòng 4] !token) || (type == 5
  2. [Dòng 13] type == 5"

================================================================================

📁 FILE 147: bnpl-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/bnpl-form/bnpl-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 148: domescard-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 4 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 1] type === 2"
  2. [Dòng 3] [ngStyle]="{'border-color': (type === 2
  3. [Dòng 15] type === 2
  4. [Dòng 15] type === '2'

== (2 điều kiện):
  1. [Dòng 4] !token)  || (type == 2
  2. [Dòng 11] type == 2"/>

================================================================================

📁 FILE 149: domescard-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/domescard-form/domescard-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 150: intercard-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 1] type === 1"
  2. [Dòng 2] [ngStyle]="{'border-color': (type === 1
  3. [Dòng 20] type === 1

== (2 điều kiện):
  1. [Dòng 4] !token) || (type == 1
  2. [Dòng 16] type == 1"

================================================================================

📁 FILE 151: intercard-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/intercard-form/intercard-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 152: mobile-wallet-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/mobile-wallet-form/mobile-wallet-form.component.html
📊 Thống kê: 8 điều kiện duy nhất
   - === : 3 lần
   - == : 5 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 1] type === paymentType"
  2. [Dòng 2] [ngStyle]="{'border-color': (type === paymentType
  3. [Dòng 38] type === paymentType

== (5 điều kiện):
  1. [Dòng 5] *ngIf="(!token) || (type == paymentType)"
  2. [Dòng 8] paymentType == PaymentType.ApplePay"
  3. [Dòng 14] paymentType == PaymentType.GooglePay"
  4. [Dòng 20] paymentType == PaymentType.SamsungPay"
  5. [Dòng 35] type == paymentType"

================================================================================

📁 FILE 153: mobile-wallet-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/mobile-wallet-form/mobile-wallet-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 154: paypal-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.html
📊 Thống kê: 3 điều kiện duy nhất
   - === : 2 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 1] type === 3"
  2. [Dòng 3] [ngStyle]="{'border-color': (type === 3

== (1 điều kiện):
  1. [Dòng 10] type == 3"

================================================================================

📁 FILE 155: paypal-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/paypal-form/paypal-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 156: qr-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/qr-form/qr-form.component.html
📊 Thống kê: 11 điều kiện duy nhất
   - === : 3 lần
   - == : 7 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 1] type === 4"
  2. [Dòng 2] [ngStyle]="{'border-color': (type === 4
  3. [Dòng 14] type === 4

== (7 điều kiện):
  1. [Dòng 11] type == 4"
  2. [Dòng 16] [class.hrqr]="(version2 == '2') || (qr_version2 != 'qrV1'
  3. [Dòng 16] version2 == 'default')"
  4. [Dòng 20] <qr-main *ngIf="((qr_version2 == 'qrV1'
  5. [Dòng 20] version2 == 'default') || (version2 == '1')"
  6. [Dòng 24] version2 == 'default'"
  7. [Dòng 28] version2 == '2'"

!= (1 điều kiện):
  1. [Dòng 24] qr_version2 != 'qrV1'

================================================================================

📁 FILE 157: qr-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/qr-form/qr-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 158: vietqr-form.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/vietqr-form/vietqr-form.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 1] type === 7"
  2. [Dòng 2] [ngStyle]="{'border-color': (type === 7
  3. [Dòng 16] type === 7

== (2 điều kiện):
  1. [Dòng 5] !token) || (type == 7
  2. [Dòng 13] type == 7"

================================================================================

📁 FILE 159: vietqr-form.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/sorting-option/vietqr-form/vietqr-form.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 160: vietqr-deeplink.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/vietqr-main/vietqr-deeplink/vietqr-deeplink.component.html
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 24] vietQRData?.state=='created' else state_failed"

================================================================================

📁 FILE 161: vietqr-deeplink.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/vietqr-main/vietqr-deeplink/vietqr-deeplink.component.ts
📊 Thống kê: 7 điều kiện duy nhất
   - === : 0 lần
   - == : 5 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (5 điều kiện):
  1. [Dòng 115] if (res.status == '200'
  2. [Dòng 115] res.status == '201') {
  3. [Dòng 119] if (res.body?.state == 'created'){
  4. [Dòng 239] if (_re.status == '200'
  5. [Dòng 239] _re.status == '201') {

!= (2 điều kiện):
  1. [Dòng 173] if (params['locale'] != null) {
  2. [Dòng 225] if (appcode != null) {

================================================================================

📁 FILE 162: vietqr-guide.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/vietqr-main/vietqr-guide/vietqr-guide.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 163: vietqr-guide.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/vietqr-main/vietqr-guide/vietqr-guide.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 164: vietqr-listbank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/vietqr-main/vietqr-listbank/vietqr-listbank.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 165: vietqr-listbank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/vietqr-main/vietqr-listbank/vietqr-listbank.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 34] if (banksRes?.status == 200
  2. [Dòng 37] if (appRes?.status == 200

================================================================================

📁 FILE 166: vietqr-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/vietqr-main/vietqr-main.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 2] listVietQrDeeplink.length == 0"
  2. [Dòng 15] vietQRData?.state=='created' else state_failed"

================================================================================

📁 FILE 167: vietqr-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/main/menu/vietqr-main/vietqr-main.component.ts
📊 Thống kê: 9 điều kiện duy nhất
   - === : 0 lần
   - == : 8 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (8 điều kiện):
  1. [Dòng 82] this.listVietQrDeeplink.length == 0) {
  2. [Dòng 108] if (res.status == '200'
  3. [Dòng 108] res.status == '201') {
  4. [Dòng 112] if (res.body?.state == 'created'){
  5. [Dòng 141] e.type == 'vietqr_deeplink') {
  6. [Dòng 158] if (payment?.state == 'failed'
  7. [Dòng 158] payment?.state == 'canceled'
  8. [Dòng 158] payment?.state == 'expired') {

!= (1 điều kiện):
  1. [Dòng 133] if (!(strInstrument != null

================================================================================

📁 FILE 168: bnpl-management.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/model/bnpl-management.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 169: fundiin-management.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/model/fundiin-management.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 170: homecredit-management.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/model/homecredit-management.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 171: kbank-management.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/model/kbank-management.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 172: overlay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/overlay/overlay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 173: overlay.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/overlay/overlay.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 174: overlay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/overlay/overlay.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 175: bank-amount.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/pipe/bank-amount.pipe.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 11] return ((a.id == id
  2. [Dòng 11] a.code == id) && a.type.includes(type));

================================================================================

📁 FILE 176: auth.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/auth.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 177: close-dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/close-dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 178: currency-service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/currency-service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 179: data.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/data.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 131] const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
  2. [Dòng 131] item.method === method) : null;

================================================================================

📁 FILE 180: deep_link.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/deep_link.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 9] if (isIphone == true) {
  2. [Dòng 22] } else if (isAndroid == true) {

================================================================================

📁 FILE 181: dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 182: digital-wallet.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/digital-wallet.service.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 209] element.value == 'true'

================================================================================

📁 FILE 183: handle_bnpl_token.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/handle_bnpl_token.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 184: multiple_method.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/multiple_method.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 185: payment.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/payment.service.ts
📊 Thống kê: 15 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 11 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 634] if (res.status == '200'
  2. [Dòng 634] res.status == '201') {
  3. [Dòng 645] return countPayment == maxPayment
  4. [Dòng 683] if (this.getLatestPayment().state == 'canceled')

!= (11 điều kiện):
  1. [Dòng 118] if (idInvoice != null
  2. [Dòng 118] idInvoice != 0)
  3. [Dòng 128] idInvoice != 0) {
  4. [Dòng 309] let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
  5. [Dòng 323] if (this._merchantid != null
  6. [Dòng 323] this._tranref != null
  7. [Dòng 323] this._state != null
  8. [Dòng 408] urlCancel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
  9. [Dòng 447] if (paymentId != null) {
  10. [Dòng 545] if (res?.status != 200
  11. [Dòng 545] res?.status != 201) return;

================================================================================

📁 FILE 186: qr.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/qr.service.ts
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 59] if (res?.state == 'canceled') {
  2. [Dòng 100] state == 'authorization_required'

!= (3 điều kiện):
  1. [Dòng 41] if (_re.status != '200'
  2. [Dòng 41] _re.status != '201') {
  3. [Dòng 50] latestPayment?.state != "authorization_required") {

================================================================================

📁 FILE 187: time-stop.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/time-stop.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 188: vietqr.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/services/vietqr.service.ts
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 1 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 43] if (_re.status == '200'
  2. [Dòng 43] _re.status == '201') {
  3. [Dòng 58] } else if (latestPayment?.state == "failed") {
  4. [Dòng 64] if (res?.state == 'canceled') {

!= (1 điều kiện):
  1. [Dòng 55] if (latestPayment?.instrument?.type != "vietqr") {

================================================================================

📁 FILE 189: success.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/success/success.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 52] amigo_type == 'SP'"
  2. [Dòng 96] amigo_type == 'PL'"

================================================================================

📁 FILE 190: success.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/success/success.component.ts
📊 Thống kê: 9 điều kiện duy nhất
   - === : 5 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 139] params.timeout === 'true') {
  2. [Dòng 158] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  3. [Dòng 158] _re.body.state === 'unpaid');
  4. [Dòng 235] params.name === 'CUSTOMER_INTIME'
  5. [Dòng 235] params.code === '09') {

== (2 điều kiện):
  1. [Dòng 211] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
  2. [Dòng 222] this.res.themes.theme == 'general') {

!= (2 điều kiện):
  1. [Dòng 209] } else if (this.res.payments[this.res.payments.length - 1].instrument.issuer.brand != null
  2. [Dòng 210] this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id != null

================================================================================

📁 FILE 191: index.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/translate/index.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 192: lang-en.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/translate/lang-en.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 193: lang-vi.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/translate/lang-vi.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 194: translate.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/translate/translate.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 195: translate.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/translate/translate.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 37] if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
  2. [Dòng 38] else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';

================================================================================

📁 FILE 196: translations.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/translate/translations.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 197: apps-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/util/apps-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 712] if (e.name == bankSwift) { // TODO: get by swift
  2. [Dòng 720] return this.apps.find(e => e.code == appCode);

================================================================================

📁 FILE 198: apps-information.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/util/apps-information.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 199: apps-infov2.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/util/apps-infov2.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 555] if (e.name == bankSwift) { // TODO: get by swift

================================================================================

📁 FILE 200: banks-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/util/banks-info.ts
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (3 điều kiện):
  1. [Dòng 1104] if (e.id == bankId) {
  2. [Dòng 1114] if (+e.id == bankId) {
  3. [Dòng 1154] if (e.swiftCode == bankSwift) {

================================================================================

📁 FILE 201: error-handler.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/util/error-handler.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 9] err?.status === 400
  2. [Dòng 10] err?.error?.name === 'INVALID_CARD_FEE'

================================================================================

📁 FILE 202: iso-ca-states.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/util/iso-ca-states.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 203: iso-us-states.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/util/iso-us-states.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 204: util.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/app/util/util.ts
📊 Thống kê: 39 điều kiện duy nhất
   - === : 15 lần
   - == : 17 lần
   - !== : 3 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (15 điều kiện):
  1. [Dòng 46] if (v.length === 2
  2. [Dòng 46] this.flag.length === 3
  3. [Dòng 46] this.flag.charAt(this.flag.length - 1) === '/') {
  4. [Dòng 50] if (v.length === 1) {
  5. [Dòng 52] } else if (v.length === 2) {
  6. [Dòng 55] v.length === 2) {
  7. [Dòng 63] if (len === 2) {
  8. [Dòng 163] if (M[1] === 'Chrome') {
  9. [Dòng 333] if (param === key) {
  10. [Dòng 495] pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
  11. [Dòng 499] target === 0
  12. [Dòng 629] if (cardTypeBank === 'bank_card_number') {
  13. [Dòng 632] } else if (cardTypeBank === 'bank_account_number') {
  14. [Dòng 682] if (event.keyCode === 8
  15. [Dòng 682] event.key === "Backspace"

== (17 điều kiện):
  1. [Dòng 13] if (temp.length == 0) {
  2. [Dòng 20] return (counter % 10 == 0);
  3. [Dòng 137] if (this.checkCount == 1) {
  4. [Dòng 149] if (results == null) {
  5. [Dòng 182] if (c.length == 3) {
  6. [Dòng 195] d = d == undefined ? '.' : d
  7. [Dòng 196] t = t == undefined ? '
  8. [Dòng 321] return results == null ? null : results[1]
  9. [Dòng 682] event.inputType == 'deleteContentBackward') {
  10. [Dòng 683] if (event.target.name == 'exp_date'
  11. [Dòng 691] event.inputType == 'insertCompositionText') {
  12. [Dòng 705] if (((_val.length == 4
  13. [Dòng 705] _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  14. [Dòng 705] _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
  15. [Dòng 731] iss_date.length == 4
  16. [Dòng 731] iss_date.search('/') == -1)
  17. [Dòng 732] iss_date.length == 5))

!== (3 điều kiện):
  1. [Dòng 328] queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
  2. [Dòng 329] if (queryString !== '') {
  3. [Dòng 499] if (target !== 0

!= (4 điều kiện):
  1. [Dòng 165] if (tem != null) {
  2. [Dòng 170] if ((tem = ua.match(/version\/(\d+)/i)) != null) {
  3. [Dòng 627] if (ua.indexOf('safari') != -1
  4. [Dòng 684] if (v.length != 3) {

================================================================================

📁 FILE 205: apple.js
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/assets/script/apple.js
📊 Thống kê: 15 điều kiện duy nhất
   - === : 0 lần
   - == : 13 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

== (13 điều kiện):
  1. [Dòng 65] let applepayNapas = document.getElementById('applepay-napas')?.value == 'true'
  2. [Dòng 66] let applepayVisa = document.getElementById('applepay-visa')?.value == 'true'
  3. [Dòng 67] let applepayMasterCard = document.getElementById('applepay-masterCard')?.value == 'true'
  4. [Dòng 68] let applepayJCB = document.getElementById('applepay-jcb')?.value == 'true'
  5. [Dòng 72] if (applepayNapas == true) {
  6. [Dòng 76] if (applepayVisa == true) {
  7. [Dòng 80] if (applepayMasterCard == true) {
  8. [Dòng 84] if (applepayJCB == true) {
  9. [Dòng 139] if(document.getElementById('applepay-merchantAVS').value == 'true'){
  10. [Dòng 191] response.status == '400') {
  11. [Dòng 193] } else if (response.status == '500') {
  12. [Dòng 204] if (data.name == "CARD_TYPE_CAN_NOT_BE_PROCESSED") { // in case response.status == 400
  13. [Dòng 211] } else if (data.state == "approved"){ // in case response.ok

!= (2 điều kiện):
  1. [Dòng 127] if (network != "napas") return true;
  2. [Dòng 128] if (currency != "VND") return false; // napas accept VND only

================================================================================

📁 FILE 206: google-pay-intergrate.js
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/assets/script/google-pay-intergrate.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 207: qrcode.js
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/assets/script/qrcode.js
📊 Thống kê: 67 điều kiện duy nhất
   - === : 2 lần
   - == : 53 lần
   - !== : 0 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 2255] if (typeof define === 'function'
  2. [Dòng 2257] } else if (typeof exports === 'object') {

== (53 điều kiện):
  1. [Dòng 68] if (_dataCache == null) {
  2. [Dòng 85] if ( (0 <= r && r <= 6 && (c == 0
  3. [Dòng 85] c == 6) )
  4. [Dòng 86] || (0 <= c && c <= 6 && (r == 0
  5. [Dòng 86] r == 6) )
  6. [Dòng 107] if (i == 0
  7. [Dòng 122] _modules[r][6] = (r % 2 == 0);
  8. [Dòng 129] _modules[6][c] = (c % 2 == 0);
  9. [Dòng 152] if (r == -2
  10. [Dòng 152] r == 2
  11. [Dòng 152] c == -2
  12. [Dòng 152] c == 2
  13. [Dòng 153] || (r == 0
  14. [Dòng 153] c == 0) ) {
  15. [Dòng 169] ( (bits >> i) & 1) == 1);
  16. [Dòng 226] if (col == 6) col -= 1;
  17. [Dòng 232] if (_modules[row][col - c] == null) {
  18. [Dòng 237] dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
  19. [Dòng 249] if (bitIndex == -1) {
  20. [Dòng 458] margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
  21. [Dòng 498] if (typeof arguments[0] == 'object') {
  22. [Dòng 587] margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
  23. [Dòng 733] if (b == -1) throw 'eof';
  24. [Dòng 741] if (b0 == -1) break;
  25. [Dòng 767] if (typeof b == 'number') {
  26. [Dòng 768] if ( (b & 0xff) == b) {
  27. [Dòng 910] return function(i, j) { return (i + j) % 2 == 0
  28. [Dòng 912] return function(i, j) { return i % 2 == 0
  29. [Dòng 914] return function(i, j) { return j % 3 == 0
  30. [Dòng 916] return function(i, j) { return (i + j) % 3 == 0
  31. [Dòng 918] return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
  32. [Dòng 920] return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
  33. [Dòng 922] return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
  34. [Dòng 924] return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
  35. [Dòng 1011] if (r == 0
  36. [Dòng 1011] c == 0) {
  37. [Dòng 1015] if (dark == qrcode.isDark(row + r, col + c) ) {
  38. [Dòng 1036] if (count == 0
  39. [Dòng 1036] count == 4) {
  40. [Dòng 1149] if (typeof num.length == 'undefined') {
  41. [Dòng 1155] num[offset] == 0) {
  42. [Dòng 1495] if (typeof rsBlock == 'undefined') {
  43. [Dòng 1538] return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
  44. [Dòng 1543] _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
  45. [Dòng 1599] if (data.length - i == 1) {
  46. [Dòng 1601] } else if (data.length - i == 2) {
  47. [Dòng 1866] } else if (n == 62) {
  48. [Dòng 1868] } else if (n == 63) {
  49. [Dòng 1928] if (_buflen == 0) {
  50. [Dòng 1937] if (c == '=') {
  51. [Dòng 1961] } else if (c == 0x2b) {
  52. [Dòng 1963] } else if (c == 0x2f) {
  53. [Dòng 2133] if (table.size() == (1 << bitLength) ) {

!= (12 điều kiện):
  1. [Dòng 119] if (_modules[r][6] != null) {
  2. [Dòng 126] if (_modules[6][c] != null) {
  3. [Dòng 144] if (_modules[row][col] != null) {
  4. [Dòng 365] while (buffer.getLengthInBits() % 8 != 0) {
  5. [Dòng 750] if (count != numChars) {
  6. [Dòng 751] throw count + ' != ' + numChars
  7. [Dòng 878] while (data != 0) {
  8. [Dòng 1733] if (test.length != 2
  9. [Dòng 1733] ( (test[0] << 8) | test[1]) != code) {
  10. [Dòng 1894] if (_length % 3 != 0) {
  11. [Dòng 2067] if ( (data >>> length) != 0) {
  12. [Dòng 2178] return typeof _map[key] != 'undefined'

================================================================================

📁 FILE 208: environment.dev.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/environments/environment.dev.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 209: environment.mtf.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/environments/environment.mtf.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 210: environment.prod.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/environments/environment.prod.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 211: environment.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/environments/environment.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 212: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 213: karma.conf.js
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/karma.conf.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 214: main.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/main.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 215: polyfills.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/polyfills.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 216: kbank-policy.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/template/kbank-policy/kbank-policy.component.html
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 327] _locale == 'vi'"
  2. [Dòng 1407] _locale == 'en'"

================================================================================

📁 FILE 217: kbank-policy.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/template/kbank-policy/kbank-policy.component.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 22] if (params['locale'] == 'vn') {
  2. [Dòng 24] } else if (params['locale'] == 'en') {

================================================================================

📁 FILE 218: test.ts
📍 Đường dẫn: /root/projects/onepay/paygate-general/src/test.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📋 TÓM TẮT TẤT CẢ ĐIỀU KIỆN DUY NHẤT:
====================================================================================================

=== (235 điều kiện duy nhất):
------------------------------------------------------------
1. return lang === this._translate.currentLang
2. checkDupTran === false"
3. isPopupSupport === 'True') || (rePayment
4. isPopupSupport === 'True'"
5. isPopupSupport === 'True')">
6. params.timeout === 'true') {
7. params.kbank === 'true') {
8. this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
9. _re.body.state === 'unpaid');
10. if (this.errorCode === 'overtime'
11. this.errorCode === '253') {
12. params.name === 'CUSTOMER_INTIME'
13. params.code === '09') {
14. params.name === 'INVALID_CARD_LIST'
15. params.code === '10') {
16. params.name === 'INVALID_CARD_FEE'
17. params.code === '8') {
18. if (this.maxpayment === this.paymentsNum) {
19. if (param === key) {
20. if (this.timeLeft === 0) {
21. if (YY % 400 === 0
22. YY % 4 === 0)) {
23. if (YYYY % 400 === 0
24. YYYY % 4 === 0)) {
25. params['code']==='09'){
26. params.name === 'CUSTOMER_INTIME')) {
27. return index === array.findIndex(obj => {
28. return JSON.stringify(obj) === _value
29. bnpl.code === 'kbank'"
30. bnpl.code === 'homepaylater'"
31. this.homecreditProvider = this.bankAmountArr.find(e => e.code === 'homepaylater');
32. this.kredivoProvider = this.bankAmountArr.find(e => e.code === 'kredivo');
33. this.fundiinProvider = this.bankAmountArr.find(e => e.code === 'fundiin');
34. this.bnpls.push(this.sublist.find(e => e.name === 'KBank Pay Later'));
35. this.bnpls.push(this.sublist.find(e => e.name === 'Home PayLater'));
36. this.bnpls.push(this.sublist.find(e => e.name === 'Kredivo'));
37. this.bnpls.push(this.sublist.find(e => e.name === 'Fundiin'));
38. this.bnpls.push(this.sublist.find(e => e.name === 'Insta'));
39. if (target.tagName === 'A'
40. params.key === 'Backspace') {
41. if (type === 'email') {
42. if (type === 'phoneNumber') {
43. if (name === 'phoneNumber') {
44. } else if (name === 'email') {
45. if (this.id === 'city') {
46. this.priorityCity.push(this.dataSource.find(({ Id }) => Id === "01"));
47. this.priorityCity.push(this.dataSource.find(({ Id }) => Id === "79"));
48. } else if (this.id === 'district') {
49. } else if (this.id === 'ward') {
50. object.id === value ? 'select-option-active' : ''"
51. object.id === value"
52. (key === 8
53. key === 46
54. (key === 46
55. if (this.value === 'add') {
56. <input type="{{cardType}}" pattern="{{cardType === 'tel' ? '\d*' : ''}}" name="card_number"
57. [class.red_border_no_background]="c_expdate && (valueDate.length === 0
58. valueDate.trim().length === 0)"
59. if (isIE[0] === 'MSIE'
60. +isIE[1] === 10) {
61. if ((_val.value.substr(-1) === ' '
62. _val.value.length === 24) {
63. if (this.cardTypeBank === 'bank_card_number') {
64. } else if (this.cardTypeBank === 'bank_account_number') {
65. } else if (this.cardTypeBank === 'bank_username') {
66. } else if (this.cardTypeBank === 'bank_customer_code') {
67. this.cardTypeBank === 'bank_card_number'
68. if (this.cardTypeOcean === 'IB') {
69. } else if (this.cardTypeOcean === 'MB') {
70. if (_val.value.substr(0, 2) === '84') {
71. } else if (this.cardTypeOcean === 'ATM') {
72. if (this.cardTypeBank === 'bank_account_number') {
73. this.cardTypeBank === 'bank_card_number') {
74. if (this.cardTypeBank === 'bank_card_number'
75. if (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
76. if (event.keyCode === 8
77. event.key === "Backspace"
78. if (v.length === 2
79. this.flag.length === 3
80. this.flag.charAt(this.flag.length - 1) === '/') {
81. if (v.length === 1) {
82. } else if (v.length === 2) {
83. v.length === 2) {
84. if (len === 2) {
85. if ((this.cardTypeBank === 'bank_account_number'
86. this.cardTypeBank === 'bank_username'
87. this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
88. this.cardTypeOcean === 'ATM')
89. || (this.cardTypeOcean === 'IB'
90. if (valIn === this._translate.instant('bank_card_number')) {
91. } else if (valIn === this._translate.instant('bank_account_number')) {
92. } else if (valIn === this._translate.instant('bank_username')) {
93. } else if (valIn === this._translate.instant('bank_customer_code')) {
94. if (_val.value === ''
95. _val.value === null
96. _val.value === undefined) {
97. if (_val.value && (this.cardTypeBank === 'bank_card_number'
98. if (_val.value && (this.cardTypeBank === 'bank_card_number' || (this.cardTypeOcean === 'ATM'
99. this.cardTypeOcean === 'MB') {
100. this.cardTypeOcean === 'IB'
101. if ((this.cardTypeBank === 'bank_card_number'
102. if (this.cardName === undefined
103. this.cardName === '') {
104. if (this.valueDate === undefined
105. this.valueDate === '') {
106. c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
107. _inExpDate.trim().length === 0)"
108. if (this.cardListTech === "op") {
109. if (this.timeLeft === 10) {
110. if (this.runTime === true) {
111. if (this.runTime === true) this.submitCardBanking();
112. if (approval.method === 'REDIRECT') {
113. } else if (approval.method === 'POST_REDIRECT') {
114. if (this.timeLeft === 1) {
115. } else if (valIn === this._translate.instant('internet_banking')) {
116. if (_val.value && (this.cardTypeBank === 'bank_card_number')) {
117. if (focusElement === 'card_name') {
118. } else if (focusElement === 'exp_date'
119. focusExpDateElement === 'card_name') {
120. if (this.cardTypeBank === 'bank_account_number'
121. filteredData.length === 0"
122. this.themeConfig.techcombankCard === false ? false : true
123. if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
124. filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
125. if (valOut === 'auth') {
126. if (this._b === '1'
127. this._b === '20'
128. this._b === '64') {
129. if (this._b === '36'
130. this._b === '18'
131. if (this._b === '19'
132. this._b === '16'
133. this._b === '25'
134. this._b === '33'
135. this._b === '39'
136. this._b === '9'
137. this._b === '11'
138. this._b === '17'
139. this._b === '36'
140. this._b === '44'
141. this._b === '64'
142. if (this._b === '20'
143. if (this._b === '18') {
144. return (bankId === '1'
145. bankId === '20'
146. bankId === '64');
147. return (bankId === '36'
148. bankId === '18'
149. if (this.themeConfig && (this.themeConfig.csc_config === false)) {
150. this._showName = (config.name === "0")? false : true;
151. this._showEmailPhone = (config.email_phone === "0")? false : true;
152. if (_formCard.country === 'default') {
153. if ((v.substr(-1) === ' '
154. this._i_country_code === 'US') {
155. const insertIndex = this._i_country_code === 'US' ? 5 : 3
156. if (temp[i] === '-'
157. temp[i] === ' ') {
158. insertIndex === 3 ? ' ' : itemRemoved)
159. this.c_country = _val.value === 'default'
160. *ngIf="(type === 5
161. type === 5"
162. [ngStyle]="{'border-color': (type === 5
163. d_vrbank===true"
164. if (document.visibilityState === 'visible') {
165. this._res.merchant.international_card_title === '2') {
166. oneBnplProvider === 1) {
167. this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_bnpl + this.d_mobile_wallet + this.d_vietqr_1 === 1
168. if (count === 1
169. if (offBanksArr[i] === this.lastDomescard) {
170. if (this._res.state === 'unpaid'
171. this._res.state === 'not_paid') {
172. if ('op' === auth
173. } else if ('bank' === auth
174. if (this.timeLeftPaypal === 0) {
175. if (GGPaySDKScript.readyState === "loaded"
176. GGPaySDKScript.readyState === "complete") {
177. isTop = window === window.top
178. return currency === 'VND'
179. filteredData.length === 0
180. filteredDataOther.length === 0"
181. filteredDataMobile.length === 0
182. filteredDataOtherMobile.length === 0"
183. this.filteredData = this.appList.filter(item => item.type === 'mobile_banking');
184. this.filteredDataOther = this.appList.filter(item => item.type === 'other');
185. this.filteredDataMobile = this.appList.filter(item => item.type === 'mobile_banking');
186. this.filteredDataOtherMobile = this.appList.filter(item => item.type === 'other');
187. this.appList.length === 1) {
188. if ((this.filteredDataMobile.length === 1
189. this.filteredDataOtherMobile.length === 1)
190. item.available = filtered.toUpperCase() === item.name.toUpperCase().substring(0, filtered.length)
191. filtered.toUpperCase() === item.code.toUpperCase().substring(0, filtered.length)
192. if (item.type === 'mobile_banking') {
193. this.appList.length === 1
194. filteredSupportedApp.length === 0"
195. this.filteredSupportedApp = this.listAppSupport.filter(item => item.type === 'mobile_banking'
196. this.filteredDataOther = this.appListEWallet.filter(item => item.type === 'other');
197. this.filteredDataOtherMobile = this.appListEWallet.filter(item => item.type === 'other');
198. item.code === "vietinbankipay") {
199. listVNPayQR.length === 0"
200. this.listWalletQR.length === 1) {
201. this.listWalletQR.length === 1
202. if (!this.d_vnpayqr_wallet && !this.d_vnpayqr_deeplink && (this.listWalletQR?.length === 1
203. this.listWalletDeeplink?.length === 1)) {
204. if (item.type === 'deeplink') {
205. this.listWalletQR?.length === 1
206. type === 5
207. type === 2"
208. [ngStyle]="{'border-color': (type === 2
209. type === 2
210. type === '2'
211. type === 1"
212. [ngStyle]="{'border-color': (type === 1
213. type === 1
214. type === paymentType"
215. [ngStyle]="{'border-color': (type === paymentType
216. type === paymentType
217. type === 3"
218. [ngStyle]="{'border-color': (type === 3
219. type === 4"
220. [ngStyle]="{'border-color': (type === 4
221. type === 4
222. type === 7"
223. [ngStyle]="{'border-color': (type === 7
224. type === 7
225. const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
226. item.method === method) : null;
227. err?.status === 400
228. err?.error?.name === 'INVALID_CARD_FEE'
229. if (M[1] === 'Chrome') {
230. pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
231. target === 0
232. if (cardTypeBank === 'bank_card_number') {
233. } else if (cardTypeBank === 'bank_account_number') {
234. if (typeof define === 'function'
235. } else if (typeof exports === 'object') {

== (741 điều kiện duy nhất):
------------------------------------------------------------
1. 'vi' == params['locale']) {
2. 'en' == params['locale']) {
3. errorCode == '11'"
4. isappleerror ==false
5. isappleerror==true
6. errorCode && (errorCode == '253' || errorCode == 'overtime')
7. errorCode == 'overtime')">
8. errorCode == 'INVALID_CARD_LIST'"
9. errorCode == 'INVALID_CARD_FEE'"
10. *ngIf="(isSent == false
11. isSent == false
12. [class.select_only]="!(isSent == false
13. <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
14. (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
15. <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
16. !(errorCode == 'overtime' || errorCode == '253') && isBack && !invoiceNearExpire && !paymentPending
17. if (params && (params['bnpl'] == 'true')) {
18. if (params && (params['bnpl'] == 'false')) {
19. if (_re.body.state == 'closed')
20. if (this.paymentInformation.type == "applepay_napas") {
21. this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'amigo') {
22. this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'kredivo') {
23. if (this.paymentInformation.type == "applepay") {
24. } else if (this.paymentInformation.type == "googlepay") {
25. } else if (this.paymentInformation.type == "samsungpay") {
26. this.res.themes.theme == 'general') {
27. params.response_code == 'overtime') {
28. if (_re.status == '200'
29. _re.status == '201') {
30. if (_re2.status == '200'
31. _re2.status == '201') {
32. if (this.errorCode == 'overtime'
33. this.errorCode == '253') {
34. } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
35. } else if (this.errorCode == 'AM1') {
36. if (this.paymentInformation.type == 'bnpl') {
37. if (this.paymentInformation.provider == 'amigo'
38. this.errorCode == '2') {
39. else if (this.paymentInformation.provider == 'kbank'
40. else if (this.paymentInformation.provider == 'homecredit'
41. else if (this.paymentInformation.provider == 'kredivo'
42. else if (this.paymentInformation.provider == 'fundiin'
43. this.res.state == 'canceled') {
44. if (lastPayment?.state == 'pending') {
45. if(this.locale == 'vi')
46. if (this.isTimePause == false) {
47. if (this.isappleerror == true) {
48. if (response.body.state == 'not_paid') {
49. if (response.body.payments[response.body.payments.length - 1].state == "failed") {
50. } else if (response.body.state == 'paid') {
51. } else if (response.body.state == 'canceled'
52. response.body.state == 'closed'
53. response.body.state == 'expired') {
54. if ((dataPassed.status == '200'
55. dataPassed.status == '201') && dataPassed.body != null) {
56. dataPassed.body.themes.logo_full == 'True') {
57. payments[payments.length - 1].state == 'pending'
58. payments[payments.length - 1].instrument.issuer.brand.id == 'amigo') {
59. if (this.locale == 'en') {
60. if (name == 'MAFC')
61. if (bankId == 3
62. bankId == 61
63. bankId == 8
64. bankId == 49
65. bankId == 48
66. bankId == 10
67. bankId == 53
68. bankId == 17
69. bankId == 65
70. bankId == 23
71. bankId == 52
72. bankId == 27
73. bankId == 66
74. bankId == 9
75. bankId == 54
76. bankId == 37
77. bankId == 38
78. bankId == 39
79. bankId == 40
80. bankId == 42
81. bankId == 44
82. bankId == 72
83. bankId == 59
84. bankId == 51
85. bankId == 64
86. bankId == 58
87. bankId == 56
88. bankId == 55
89. bankId == 60
90. bankId == 68
91. bankId == 74
92. bankId == 75 //MAFC Napas
93. bnplDetail.method == 'SP'"
94. bnplDetail.method == 'PL'"
95. _re.code == '0'
96. packageItem.product_code == productCode) {
97. a.product_code == 'SP') {
98. a.product_code == 'PL') {
99. if (this.selectedIndex == 0
100. } else if ((this.selectedIndex == 1
101. this.payLaterSubmit) || (this.selectedIndex == 0
102. item.prepaid_percent == this.selectedPrePaid
103. item.installment_month == this.selectedPayLater) {
104. if (string == 'SP') {
105. } else if (string == 'PL') {
106. if(this._locale == 'en'){
107. if (this._res_post.state == 'approved'
108. this._res_post.state == 'failed') {
109. if (this._res_post.state == 'failed') {
110. } else if (this._res_post.state == 'authorization_required') {
111. if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
112. bnpl.status == 'disabled'"
113. bnpl.status == 'active'"
114. [class.bnpl-active-item]="bnpl.status == 'active'" (click)="onClick(bnpl.status == 'disabled')">
115. bnpl.code == 'kbank'
116. bnpl.code == 'insta'"
117. bnpl.code == 'homepaylater'"
118. bnpl.status == 'disabled'
119. bnpl.code == 'kredivo'
120. selectedBnpl.code == 'insta'"
121. selectedBnpl.code == 'kbank'"
122. selectedBnpl.code == 'homepaylater'"
123. selectedBnpl.code == 'kredivo'"
124. selectedBnpl.code == 'fundiin'"
125. _auth == 1"
126. this.fullNameInvalidMessage = this.fundiinDetail.fullName?.length == 0 ? this._translate.instant('fill_full_name')
127. this.fundiinDetail.email?.length == 0 ? this._translate.instant('fill_email')
128. if (bnpl.status == 'disabled') {
129. value == 'add' ? 'select-option-active' : ''"
130. value == 'add'"
131. value=='add'"
132. value == 'add') || !onepayChecked.value" [disabled]="(isInvalid()
133. value == 'add') || !onepayChecked.value">
134. if (response.status == '200'
135. response.status == '201') {
136. this.listTokenBnpl.length == 0) {
137. if (this.value == object.id) {
138. if (name == 'email') {
139. } else if (name == 'phoneNumber') {
140. if (name == 'phoneNumber') {
141. if (name == 'fullname') {
142. if (name == 'email'
143. this._res_post.state == 'authorization_required') {
144. this._res_post.code == 'KB-02') {
145. !cmndBlur ? 'no-border' : ((kbankDetail['citizen_id'] && !(kbankDetail['citizen_id'].length == 9 || kbankDetail['citizen_id'].length == 12) || !kbankDetail['citizen_id']) &&  bnplForm.controls['citizen_id'].touched) ? 'ng-invalid ng-dirty' : ''
146. bnplForm.controls['citizen_id'].touched && kbankDetail['citizen_id'] && !(kbankDetail['citizen_id'].length == 9 || kbankDetail['citizen_id'].length == 12) && cmndBlur
147. this.value == 'add')){
148. list = this.data.customer.tokens.filter((item) => item.instrument.issuer.brand.id == 'kbank')
149. return (this.bnplForm.invalid || (this.kbankDetail['citizen_id'] && !(this.kbankDetail['citizen_id'].length == 9) && !(this.kbankDetail['citizen_id'].length == 12)));
150. if (name == 'citizen_id') {
151. if (this._locale == 'en') {
152. this.bnplDetail.fullname?.length == 0 ? this._translate.instant('kredivo_empty_fullname_message')
153. this.bnplDetail.email?.length == 0 ? this._translate.instant('kredivo_empty_email_message')
154. else if (this._res.code == '2') {
155. _re.body.payments[_re.body.payments.length - 1].reason.code == 'B4') {
156. if (this._b == 18) {//8-MB Bank;18-oceanbank
157. bnpl.code == 'kbank'"
158. bnpl.status == 'active'
159. bnpl.code == 'homepaylater'
160. if (this._b == 18
161. this._b == 19) {
162. if (this._b == 19) {//19BIDV
163. } else if (this._b == 3
164. this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
165. if (this._b == 27) {
166. } else if (this._b == 12) {// 12SHB
167. } else if (this._b == 18) { //18Oceanbank-ocb
168. if (this._b == 19
169. this._b == 3
170. this._b == 27
171. this._b == 12) {
172. } else if (this._b == 18) {
173. if (this.checkBin(_val.value) && (this._b == 3
174. this._b == 27)) {
175. if (this._b == 3) {
176. this.cardTypeOcean == 'ATM') {
177. if (this._b == 27) {//** check đúng số thẻ 3-tpb;27-pvcombank;
178. this._b == 18)) {
179. if (this.checkBin(v) && (this._b == 3
180. event.inputType == 'deleteContentBackward') {
181. if (event.target.name == 'exp_date'
182. event.inputType == 'insertCompositionText') {
183. if (((this.valueDate.length == 4
184. this.valueDate.search('/') == -1) || this.valueDate.length == 5)
185. this.valueDate.length == 5)
186. if (temp.length == 0) {
187. return (counter % 10 == 0);
188. } else if (this._b == 19) {
189. } else if (this._b == 27) {
190. if (this._b == 12) {
191. if (this.cardTypeBank == 'bank_customer_code') {
192. } else if (this.cardTypeBank == 'bank_account_number') {
193. _formCard.exp_date.length == 5
194. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
195. this._b == 3)) {//27-pvcombank;3-TPB ;
196. if (this.cardName != null && this.cardName.length > 0 && (this._b == 3
197. this._b == 19
198. this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
199. if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
200. if (this.cardTypeOcean == 'IB') {
201. } else if (this.cardTypeOcean == 'MB') {
202. } else if (this.cardTypeOcean == 'ATM') {
203. if (this._b == 18) {
204. if (this._b == 27
205. this._b == 18) {
206. if (err.status == 400
207. err.error['name'] == 'INVALID_INPUT_BIN') {
208. if ((cardNo.length == 16
209. if ((cardNo.length == 16 || (cardNo.length == 19
210. && ((this._b == 18
211. cardNo.length == 19) || this._b != 18)
212. if (this._b == +e.id) {
213. if (valIn == 1) {
214. } else if (valIn == 2) {
215. this._b == 3) {
216. if (this._b == 19) {
217. if (cardType == this._translate.instant('internetbanking')
218. } else if (cardType == this._translate.instant('mobilebanking')
219. } else if (cardType == this._translate.instant('atm')
220. this._b == 18))) {
221. } else if (this._b == 18
222. this.c_expdate = !(((this.valueDate.length == 4
223. this.valueDate.length == 4
224. this.valueDate.search('/') == -1)
225. this.valueDate.length == 5))
226. (cardTypeBank == 'bank_card_number') && (_b == 67 || (checkTwoEnabled && techcombankCard)) && !isOffTechcombankNapas
227. (cardTypeBank == 'bank_card_number') &&(_b == 67 || (checkTwoEnabled && techcombankCard))&& ready===1 && !isOffTechcombankNapas
228. (cardTypeBank == 'internet_banking')&&(_b == 2 || (checkTwoEnabled && !techcombankCard))
229. if (this._b == 67
230. this._b == 2) {//19BIDV
231. if ((this._b == 2
232. !this.checkTwoEnabled) || (this._b == 2
233. this._b == 2
234. } else if (this._b == 2
235. if (this._b == 67) {
236. return this._b == 2
237. this._b == 67
238. _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;
239. this.checkMod10(cardNo) == true
240. if (this._b != 68 || (this._b == 68
241. return ((value.length == 4
242. value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
243. value.length == 5) && parseInt(value.split('/')[0]
244. || (this._util.checkValidExpireMonthYear(value) && (this._b == 2
245. this._b == 20
246. this._b == 33
247. this._b == 39
248. this._b == 43
249. this._b == 45
250. this._b == 64
251. this._b == 68
252. this._b == 72))) //sonnh them Vietbank 72
253. this._inExpDate.length == 4
254. this._inExpDate.search('/') == -1)
255. this._inExpDate.length == 5))
256. || (this._util.checkValidExpireMonthYear(this._inExpDate) && (this._b == 20
257. this._b == 72)));
258. if (this._b == 8) {//MB Bank
259. if (this._b == 18) {//Oceanbank
260. if (this._b == 8) {
261. if (this._b == 12) { //SHB
262. } else if (this._res.state == 'authorization_required') {
263. if (this.challengeCode == '') {
264. <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_card_number')">
265. <div [ngStyle]="{'margin-top': '15px'}" *ngIf="(cardTypeBank == 'bank_account_number')">
266. if (this._b == 12) this.isShbGroup = true;
267. return this._b == 9
268. this._b == 11
269. this._b == 16
270. this._b == 17
271. this._b == 25
272. this._b == 44
273. this._b == 57
274. this._b == 59
275. this._b == 61
276. this._b == 63
277. this._b == 69
278. if (this.cardTypeBank == 'bank_account_number') {
279. cardNo.length == 19) && cardNo.startsWith("970443") && this._util.checkMod10(cardNo) && this.checkBin(cardNo)) {
280. if (this._b == 2
281. this._b == 31
282. this._b == 80) {
283. if (this._b == 2) {
284. } else if (this._b == 6) {
285. } else if (this._b == 31) {
286. } else if (this._b == 80) {
287. if (this._b == 5) {//5-vib;
288. if (this._b == 5) {
289. if (this.checkBin(_val.value) && (this._b == 5)) {
290. if (this._b == 5) {//** check đúng số thẻ 3-tpb;27-pvcombank;
291. if (this.checkBin(v) && (this._b == 5)) {
292. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 5)) {//27-pvcombank;3-TPB ;
293. if (this.cardName != null && this.cardName.length > 0 && (this._b == 5)) {//3-TPB ;19-BIDV;27-pvcombank;
294. _b == 68"
295. if (this._b == 1
296. this._b == 73
297. this._b == 36
298. this._b == 55
299. this._b == 47
300. this._b == 48
301. this._b == 59) {
302. return this._b == 11
303. this._b == 72
304. this._b == 74
305. this._b == 75
306. this._b == 14
307. this._b == 15
308. this._b == 24
309. this._b == 8
310. this._b == 10
311. this._b == 22
312. this._b == 23
313. this._b == 30
314. this._b == 9) {
315. (cardNo.length == 19
316. (cardNo.length == 19 && (this._b == 1
317. this._b == 4
318. this._b == 59))
319. this._util.checkMod10(cardNo) == true
320. (!token&&_auth==0 && vietcombankGroupSelected) || (token && _b == 16)
321. _b == 16)">
322. _auth==0 && techcombankGroupSelected
323. _auth==0 && shbGroupSelected
324. _auth==0 && onepaynapasGroupSelected
325. _auth==0 && bankaccountGroupSelected
326. _auth==0 && vibbankGroupSelected
327. _auth==0 && vietQRSelected
328. (token || _auth==1) && _b != 16
329. this._auth == 0) {
330. if (e.id == '31') {
331. } else if (e.id == '80') {
332. if (!(strTest == 'card
333. if (d.b.card_list == s) {
334. if (item.b.id == '2'
335. item.b.id == '67') {
336. $event == 'true') {
337. _b: this._b == '2' ? '67' : this._b
338. if (bankid == 2
339. bankid == 67) {
340. || (off && !this.enabledTwoBankTech && ((bankid == '2'
341. this.isOffTechcombank) || (bankid == '67'
342. if (bankId == 1
343. bankId == 4
344. bankId == 7
345. bankId == 11
346. bankId == 14
347. bankId == 15
348. bankId == 16
349. bankId == 20
350. bankId == 22
351. bankId == 24
352. bankId == 25
353. bankId == 30
354. bankId == 33
355. bankId == 34
356. bankId == 35
357. bankId == 36
358. bankId == 41
359. bankId == 43
360. bankId == 45
361. bankId == 46
362. bankId == 47
363. bankId == 50
364. bankId == 57
365. bankId == 62
366. bankId == 63
367. bankId == 69
368. bankId == 70
369. bankId == 71
370. bankId == 73
371. bankId == 32
372. bankId == 75) {
373. } else if (bankId == 6
374. bankId == 31
375. bankId == 80) {
376. } else if (bankId == 2
377. bankId == 67) {
378. } else if (bankId == 3
379. bankId == 18
380. bankId == 19
381. bankId == 27) {
382. } else if (bankId == 5) {
383. } else if (bankId == 12) {
384. } else if (bankId == 'vietqr') {
385. this._b == '55'
386. this._b == '47'
387. this._b == '48'
388. this._b == '19'
389. this._b == '59'
390. this._b == '73'
391. this._b == '12') {
392. this._b == '3'
393. this._b == '43'
394. this._b == '45'
395. this._b == '57'
396. this._b == '61'
397. this._b == '63'
398. this._b == '67'
399. this._b == '68'
400. this._b == '69'
401. this._b == '72'
402. this._b == '74'
403. this._b == '75') {
404. this._b == '36'
405. this._b == '75') { //sonnh them 72 Vietbank
406. bankId == '55'
407. bankId == '47'
408. bankId == '48'
409. bankId == '19'
410. bankId == '59'
411. bankId == '73'
412. bankId == '5'
413. bankId == '12');
414. if (item['id'] == this._b) {
415. (!!exp_date + !!csc + !!card_name)==2"
416. } else if(this._res_post.state == 'failed') { // lỗi mới kiểm tra sang trang lỗi
417. if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
418. } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
419. v.length == 15) || (v.length == 16
420. v.length == 19))
421. this._util.checkMod10(v) == true) {
422. cardNo.length == 15)
423. cardNo.length == 16)
424. cardNo.startsWith('81')) && (cardNo.length == 16
425. cardNo.length == 19))
426. this.valueDate.search('/') == -1) || this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
427. this.valueDate.length == 5) && this._util.checkValidExpireMonthYear(this.valueDate)) {
428. v.length == 5) {
429. v.length == 4
430. v.length == 3)
431. _val.value.length == 4
432. _val.value.length == 3)
433. this._i_first_name.trim().length == 0
434. this._i_last_name.trim().length == 0
435. this.requireAvs = this.AVS_COUNTRIES.find(e => e.code == this._i_country_code) ? true : false;
436. this._i_csc.length == 4) ||
437. this._i_csc.length == 3)
438. country = this.AVS_COUNTRIES.find(e => e.code == res.bin_country);
439. countryCode == 'US' ? US_STATES
440. : countryCode == 'CA' ? CA_STATES
441. !token) || (type == 1
442. !token) || (type == 6
443. type == 2)"
444. !token) || (type == 3
445. !token) || (type == 5
446. method.trim()=='International'"
447. method.trim()=='ApplePay'"
448. method.trim()=='GooglePay'"
449. method.trim()=='SamsungPay'"
450. method.trim()=='Domestic'"
451. method.trim()=='QR'"
452. method.trim()=='Paypal'"
453. method.trim()=='VietQR'
454. <div *ngIf="((onePaymentMethod == true
455. d_bnpl) || (onePaymentMethod == false
456. d_bnpl_number == 1
457. method.trim()=='Bnpl'
458. providerType == bnpl.code"
459. onePaymentMethod == false
460. d_bnpl_number == 1"
461. type==5 && providerType==bnpl.code
462. providerType==bnpl.code"
463. onePaymentMethod == true
464. bnpl.code == 'insta'
465. providerType == bnpl.code)
466. || (d_bnpl_number == 1
467. providerType == bnpl.code))
468. || (bnpl.status == 'active'
469. d_bnpl_number == 1)">
470. bnpl.code == 'kredivo'"
471. bnpl.code == 'fundiin'"
472. _auth == 1
473. [class.enable_form]="!onePaymentMethod || (onePaymentMethod && d_bnpl)" *ngIf="_auth == 1 && ((onePaymentMethod == true
474. d_bnpl_number == 1)
475. if (el == 1) {
476. } else if (el == 2) {
477. } else if (el == 4) {
478. } else if (el == 3) {
479. if (!isNaN(_re.status) && (_re.status == '200'
480. _re.status == '201') && _re.body != null) {
481. if (('closed' == this._res_polling.state
482. 'canceled' == this._res_polling.state
483. 'expired' == this._res_polling.state)
484. } else if ('paid' == this._res_polling.state) {
485. this._res_polling.payments == null
486. if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'authorization_required') {
487. } else if (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
488. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
489. this._paymentService.getCurrentPage() == 'enter_card'
490. } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
491. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
492. } else if ('not_paid' == this._res_polling.state) {
493. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
494. if (auth == 'auth') {
495. detail.merchant.id == 'AMWAY') {
496. if (this.checkInvoiceState() == 1) {
497. _re.body?.merchant?.qr_version == '2');
498. this._res.payments[this._res.payments.length - 1].state == 'pending'
499. this._res.payments[this._res.payments.length - 1].instrument.type == 'applepay') {
500. if (this.themeConfig.default_method == 'International'
501. } else if (this.themeConfig.default_method == 'Domestic'
502. } else if (this.themeConfig.default_method == 'QR'
503. } else if (this.themeConfig.default_method == 'Paypal'
504. if (value == true) {
505. if (('canceled' == this._res.state
506. 'paid' == this._res.state)
507. if ('paid' == this._res.state
508. this.stopCounter == 'stop')) {  // Check if X is true
509. if (this.onePaymentMethod == true
510. this.d_bnpl_number == 1
511. this.d_bnpl == 1) {
512. if (this.d_amigo == true
513. this.d_insta == false
514. this.d_instaplus == false) {
515. if (this.version2 == "2") {
516. if (this.d_amigo_number == 0
517. this.d_insta_number == 1) {
518. else if (this.d_amigo_number == 0
519. this.d_instaplus_number == 1) {
520. if ((this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number + this.d_bnpl + this.d_mobile_wallet + this.d_vietqr_1) == 0
521. this._res.themes.techcombankCard == false ? false : true
522. if (count == 2
523. if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
524. this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
525. if (this._res.payments[this._res.payments.length - 1].state == 'approved'
526. } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
527. || (responseCode == '1'
528. instrumentType == 'vietqr'))
529. if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required') {
530. } else if (this._res.payments[this._res.payments.length - 1].state == 'pending') {
531. } else if (this._res.payments[this._res.payments.length - 1].state == 'canceled') {
532. this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
533. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id == 'vietqr'
534. } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
535. this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
536. if (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
537. } else if (idBrand == 'atm'
538. else if (idBrand == 'kbank'
539. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
540. if (this._res.payments[this._res.payments.length - 1].state == 'pending'
541. if ('paid' == this._res.state) {
542. if (('closed' == this._res.state
543. 'canceled' == this._res.state
544. 'expired' == this._res.state
545. 'canceled' == this._res.state) {
546. this._res.payments[this._res.payments.length - 1].state == 'pending') {
547. if (this.d_inter == 1) {
548. } else if (this.type == 1) {
549. if (type == 'qrv1') {
550. if (type == 'mobile') {
551. e.type == 'ewallet'
552. e.code == 'momo')) {
553. } else if (type == 'desktop') {
554. e.type == 'vnpayqr') || (regex.test(strTest)
555. if (isBankOff && ((bankid == '2'
556. !this.isOffTechcombank) || (bankid == '67'
557. if (res.status == '200'
558. res.status == '201') {
559. return (this.currentMethod == 7) // PTTT riêng VietQR
560. || (this.currentMethod == 2
561. this._b == 'vietqr')    // PTTT domes vietqr
562. if (data._locale == 'en') {
563. if (event.data?.event_type == 'applepay_network_not_supported') {
564. if (res?.body?.state == 'approved') {
565. } else if (res?.body?.state == 'authorization_required') {
566. paymentType == PaymentType.ApplePay"
567. paymentType == PaymentType.GooglePay"
568. paymentType == PaymentType.SamsungPay"
569. if (this.paymentType == PaymentType.ApplePay) {
570. if (network == 'napas'
571. serviceId: this.environment == 'PRODUCTION'? this.serviceIdProd : this.serviceIdTest
572. screen=='qr'"
573. screen=='confirm_close'"
574. this.themeConfig.deeplink_status == 'Off' ? false : true
575. if (d.b.code == s) {
576. if (item.available == true) {
577. if (appcode == 'grabpay'
578. appcode == 'momo') {
579. if (type == 2
580. err.error.code == '04') {
581. deeplink_status) || (qr_version2 == 'MSB'
582. qr_version2 == 'Viettin')"
583. _locale=='vi'"
584. _locale=='en'"
585. _locale == 'vi'
586. _locale == 'en'
587. qr_version2 == 'None'"
588. this.qr_version2 == 'MSB') {
589. this.qr_version2 == 'Viettin') {//qrversion2 = Viettin
590. this.filteredDataDeeplink = this.filteredData.filter(item => item.deep_link == true);
591. this.qr_version2 == 'Viettin') {
592. if (this.qr_version2 == 'MSB') {
593. item.code == "msbmbank_970426") {
594. if (type == 'both'
595. } else if (type == 'both'
596. this.qr_version2 == 'None'
597. dialogType == 'vnpay'"
598. dialogType == 'bankapp'"
599. dialogType == 'both'"
600. e.type == 'vnpayqr') {
601. e.type == 'ewallet') {
602. e.type == 'wallet')) {
603. type == 'vnpay'"
604. type == 'bankapp'"
605. type == 'both'"
606. _locale == 'vi'"
607. _locale == 'en'"
608. e.type == 'deeplink') {
609. if (e.type == 'ewallet') {
610. this.listWallet.length == 1
611. this.listWallet[0].code == 'momo') {
612. this.checkEWalletDeeplink.length == 0) {
613. arrayWallet.length == 0) return false;
614. if (arrayWallet[i].code == key) {
615. if (this.locale == 'vi') {
616. type == 5"
617. !token)  || (type == 2
618. type == 2"/>
619. type == 1"
620. *ngIf="(!token) || (type == paymentType)"
621. type == paymentType"
622. type == 3"
623. type == 4"
624. [class.hrqr]="(version2 == '2') || (qr_version2 != 'qrV1'
625. version2 == 'default')"
626. <qr-main *ngIf="((qr_version2 == 'qrV1'
627. version2 == 'default') || (version2 == '1')"
628. version2 == 'default'"
629. version2 == '2'"
630. !token) || (type == 7
631. type == 7"
632. vietQRData?.state=='created' else state_failed"
633. if (res.body?.state == 'created'){
634. if (banksRes?.status == 200
635. if (appRes?.status == 200
636. listVietQrDeeplink.length == 0"
637. this.listVietQrDeeplink.length == 0) {
638. e.type == 'vietqr_deeplink') {
639. if (payment?.state == 'failed'
640. payment?.state == 'canceled'
641. payment?.state == 'expired') {
642. return ((a.id == id
643. a.code == id) && a.type.includes(type));
644. if (isIphone == true) {
645. } else if (isAndroid == true) {
646. element.value == 'true'
647. return countPayment == maxPayment
648. if (this.getLatestPayment().state == 'canceled')
649. if (res?.state == 'canceled') {
650. state == 'authorization_required'
651. } else if (latestPayment?.state == "failed") {
652. amigo_type == 'SP'"
653. amigo_type == 'PL'"
654. this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id == 'amigo'
655. if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
656. else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';
657. if (e.name == bankSwift) { // TODO: get by swift
658. return this.apps.find(e => e.code == appCode);
659. if (e.id == bankId) {
660. if (+e.id == bankId) {
661. if (e.swiftCode == bankSwift) {
662. if (this.checkCount == 1) {
663. if (results == null) {
664. if (c.length == 3) {
665. d = d == undefined ? '.' : d
666. t = t == undefined ? '
667. return results == null ? null : results[1]
668. if (((_val.length == 4
669. _val.search('/') == -1) || _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
670. _val.length == 5) && this.checkValidIssueMonthYear(_val)) {
671. iss_date.length == 4
672. iss_date.search('/') == -1)
673. iss_date.length == 5))
674. let applepayNapas = document.getElementById('applepay-napas')?.value == 'true'
675. let applepayVisa = document.getElementById('applepay-visa')?.value == 'true'
676. let applepayMasterCard = document.getElementById('applepay-masterCard')?.value == 'true'
677. let applepayJCB = document.getElementById('applepay-jcb')?.value == 'true'
678. if (applepayNapas == true) {
679. if (applepayVisa == true) {
680. if (applepayMasterCard == true) {
681. if (applepayJCB == true) {
682. if(document.getElementById('applepay-merchantAVS').value == 'true'){
683. response.status == '400') {
684. } else if (response.status == '500') {
685. if (data.name == "CARD_TYPE_CAN_NOT_BE_PROCESSED") { // in case response.status == 400
686. } else if (data.state == "approved"){ // in case response.ok
687. if (_dataCache == null) {
688. if ( (0 <= r && r <= 6 && (c == 0
689. c == 6) )
690. || (0 <= c && c <= 6 && (r == 0
691. r == 6) )
692. if (i == 0
693. _modules[r][6] = (r % 2 == 0);
694. _modules[6][c] = (c % 2 == 0);
695. if (r == -2
696. r == 2
697. c == -2
698. c == 2
699. || (r == 0
700. c == 0) ) {
701. ( (bits >> i) & 1) == 1);
702. if (col == 6) col -= 1;
703. if (_modules[row][col - c] == null) {
704. dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
705. if (bitIndex == -1) {
706. margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
707. if (typeof arguments[0] == 'object') {
708. margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
709. if (b == -1) throw 'eof';
710. if (b0 == -1) break;
711. if (typeof b == 'number') {
712. if ( (b & 0xff) == b) {
713. return function(i, j) { return (i + j) % 2 == 0
714. return function(i, j) { return i % 2 == 0
715. return function(i, j) { return j % 3 == 0
716. return function(i, j) { return (i + j) % 3 == 0
717. return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
718. return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
719. return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
720. return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
721. if (r == 0
722. c == 0) {
723. if (dark == qrcode.isDark(row + r, col + c) ) {
724. if (count == 0
725. count == 4) {
726. if (typeof num.length == 'undefined') {
727. num[offset] == 0) {
728. if (typeof rsBlock == 'undefined') {
729. return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
730. _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
731. if (data.length - i == 1) {
732. } else if (data.length - i == 2) {
733. } else if (n == 62) {
734. } else if (n == 63) {
735. if (_buflen == 0) {
736. if (c == '=') {
737. } else if (c == 0x2b) {
738. } else if (c == 0x2f) {
739. if (table.size() == (1 << bitLength) ) {
740. if (params['locale'] == 'vn') {
741. } else if (params['locale'] == 'en') {

!== (49 điều kiện duy nhất):
------------------------------------------------------------
1. this.lastValue !== $event.target.value)) {
2. queryString = (sourceURL.indexOf("?") !== -1) ? sourceURL.split("?")[1] : "";
3. if (queryString !== "") {
4. if (YY % 400 === 0 || (YY % 100 !== 0
5. if (YYYY % 400 === 0 || (YYYY % 100 !== 0
6. bnpl.code !== 'kbank'
7. if (this.fundiinDetail.phoneNumber && ((this.fundiinDetail.phoneNumber.length !== 10
8. this.fundiinDetail.phoneNumber.length !== 11)
9. this.filteredData = this.dataSource.filter(i => i.Id !== "01"
10. i.Id !== "79").sort((a, b) => a.Name.localeCompare(b.Name));
11. this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
12. selectedBnpl.status !== 'disabled'"
13. if ((this.bnplDetail.phoneNumber.length !== 10
14. this.bnplDetail.phoneNumber.length !== 11)
15. codeResponse.toString() !== '0') {
16. event.inputType !== 'deleteContentBackward') || _val.value.length === 24) {
17. key !== '3') {
18. cardNo.length !== 0) {
19. if (this.cardTypeBank !== 'bank_card_number') {
20. if (this.cardTypeBank !== 'bank_account_number') {
21. if (this.cardTypeBank !== 'bank_username') {
22. if (this.cardTypeBank !== 'bank_customer_code') {
23. this.lb_card_account !== this._translate.instant('ocb_account')) {
24. this.lb_card_account !== this._translate.instant('ocb_phone')) {
25. this.lb_card_account !== this._translate.instant('ocb_card')) {
26. this._b !== 18) || (this.cardTypeOcean === 'ATM'
27. let _b = this._b !== 67 ? 67 : this._b
28. if (this.cardTypeBank !== 'internet_banking') {
29. this._b !== 18)) {
30. this._b !== 18) || (this._b == 18)) {
31. this.bankList = this.bankList.filter(item => item.b.id !== '67');
32. codeResponse.toString() !== '0'){
33. event.inputType !== 'deleteContentBackward') || v.length == 5) {
34. this._i_country_code !== 'US') {
35. itemRemoved !== '') {
36. if (deviceValue !== 'default') {
37. this.tracking['4'].charAt(this.tracking['4'].length - 1) !== 's')) {
38. this._i_country_code !== 'default'
39. this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
40. this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {
41. bnpl.code !== 'kbank'"
42. if (this.qr_version2 !== 'qrV1'
43. this.qr_version2 !== '') {
44. if (_val !== 3) {
45. this._util.getElementParams(this.currentUrl, 't_id') !== '') {
46. this.filteredDataOtherMobile = this.filteredDataOtherMobile.filter(item => item.code !== "smartpay");
47. queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
48. if (queryString !== '') {
49. if (target !== 0

!= (221 điều kiện duy nhất):
------------------------------------------------------------
1. if (params['locale'] != null
2. } else if (params['locale'] != null
3. if (userLang != null
4. if(value!=null){
5. if (message != ''
6. message != null
7. message != undefined) {
8. } else if (this.res.payments[this.res.payments.length - 1].instrument.issuer.brand != null
9. this.res.payments[this.res.payments.length - 1].instrument.issuer.brand.id != null
10. if (_re != null
11. _re.links != null
12. _re.links.merchant_return != null
13. _re.links.merchant_return.href != null) {
14. } else if (_re.body != null
15. _re.body.links != null
16. _re.body.links.merchant_return != null
17. _re.body.links.merchant_return.href != null) {
18. if (this.url_new_invoice != null) {
19. if (this._idInvoice != null
20. this._idInvoice != 0) {
21. if (this._paymentService.getInvoiceDetail() != null) {
22. dataPassed.body != null) {
23. dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
24. if (this._translate.currentLang != language) {
25. selectedBnpl.status != 'disabled'"
26. bnplDetail.method != 'SP'"
27. bnplDetail.method != 'PL'"
28. 'total_paid': this.data?.amount ? (item.diff_amount !=0 ? this._util.formatMoney(parseFloat(item.diff_amount) + parseFloat(this.data?.amount), 0, '.', ',') : this._util.formatMoney(parseFloat(this.data?.amount), 0, '.', ',')) :0,
29. if (this._res_post.return_url != null) {
30. } else if (this._res_post.links != null
31. this._res_post.links.merchant_return != null
32. this._res_post.links.merchant_return.href != null) {
33. _auth != 1"
34. bnpl.code != 'homepaylater'"
35. document.activeElement.id!='fullName'"
36. document.activeElement.id!='phoneNumber'"
37. if ((this.fundiinDetail.phoneNumber?.length != 10
38. this.fundiinDetail.phoneNumber?.length != 11) || !this.fundiinDetail.phoneNumber.match(/^[*]{8,9
39. (this.fundiinDetail.phoneNumber?.length != 10
40. this.fundiinDetail.phoneNumber?.length != 11) ?
41. homecreditDetail['phoneNumber'].length != 10
42. homecreditDetail['phoneNumber'].length != 11)
43. this.value = (this.listTokenBnpl.length != 0) ? this.listTokenBnpl[0]['id'] : "add";
44. if ((this.homecreditDetail['phoneNumber'].length != 10
45. this.homecreditDetail['phoneNumber'].length != 11)
46. this.listTokenBnpl = this.listTokenBnpl.filter(item => item.id != result.id);
47. if (this._res_post.authorization != null
48. this._res_post.authorization.links != null
49. if (this._res_post.links != null
50. this._res_post.links.cancel != null) {
51. this._res_post.authorization.links.approval != null
52. this._res_post.authorization.links.approval.href != null) {
53. userName = paramUserName != null ? paramUserName : ''
54. document.activeElement.id!='fullname'"
55. document.activeElement.id!='email'"
56. if ((this.bnplDetail.phoneNumber?.length != 10
57. this.bnplDetail.phoneNumber?.length != 11) || !this.bnplDetail.phoneNumber.match(/^[*]{8,9
58. (this.bnplDetail.phoneNumber?.length != 10
59. this.bnplDetail.phoneNumber?.length != 11) ?
60. if (this._res.links != null
61. this._res.links.merchant_return != null
62. this._res.links.merchant_return.href != null) {
63. if (!(_formCard.otp != null
64. if (!(_formCard.password != null
65. if (ua.indexOf('safari') != -1
66. bnpl.code != 'kbank'
67. } else if (this._b != 18) {
68. if (this.htmlDesc != null
69. if (_val.value != '') {
70. this.auth_method != null) {
71. if (this.valueDate.length != 3) {
72. if (_formCard.exp_date != null
73. if (this.cardName != null
74. this._res_post.authorization.links.approval != null) {
75. this._b != 27
76. this._b != 12
77. this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
78. this._b != 18)
79. if (this._b != 18
80. this._b != 19) {
81. if (this._inExpDate.length != 3) {
82. let userName = _formCard.name != null ? _formCard.name : ''
83. this._b != 3))
84. if (this._b != 68
85. this._b != 2
86. this._b != 20
87. this._b != 33
88. this._b != 39
89. this._b != 43
90. this._b != 45
91. this._b != 64
92. this._b != 67
93. this._b != 68
94. this._b != 72)
95. if (this._b != 9
96. this._b != 16
97. this._b != 17
98. this._b != 25
99. this._b != 44
100. this._b != 57
101. this._b != 59
102. this._b != 61
103. this._b != 63
104. this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
105. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75'].indexOf(this._b.toString()) != -1) {
106. if (params['locale'] != null) {
107. if ('otp' != this._paymentService.getCurrentPage()) {
108. this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
109. if (!(strInstrument != null
110. if (strInstrument.substring(0, 1) != '^'
111. strInstrument.substr(strInstrument.length - 1) != '$') {
112. if (bankid != null) {
113. cardNo != null
114. v != null
115. this.c_csc = (!(_val.value != null
116. this._i_csc != null
117. this.requireAvs = this.isAvsCountry = country != undefined
118. _auth != '1'"
119. <div *ngIf="bnpl.code == 'insta' && ((selectedBnpl != bnpl) || (d_bnpl_number == 1))"
120. <div *ngIf="bnpl.code == 'homepaylater' && ((selectedBnpl != bnpl) || (d_bnpl_number == 1))"
121. this.currentMethod != selected) {
122. this._paymentService.getState() != 'error') {
123. if (this._paymentService.getCurrentPage() != 'otp') {
124. _re.body != null) {
125. this._res_polling.links != null
126. this._res_polling.links.merchant_return != null //
127. this._util.getElementParams(url_redirect, 'vpc_TxnResponseCode') != '99') {
128. } else if (this._res_polling.merchant != null
129. this._res_polling.merchant_invoice_reference != null
130. } else if (this._res_polling.payments != null
131. this._res_polling.payments[this._res_polling.payments.length - 1].instrument.brand != 'amigo') {
132. this._res_polling.links.merchant_return != null//
133. this._res_polling.payments != null
134. if (this._res_polling.payments != null
135. t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
136. this.type.toString().length != 0) {
137. if (this._res.payments != null
138. this._res.payments[this._res.payments.length - 1].instrument.brand != 'amigo') {
139. if (this._idInvoice != null) {
140. const tokenListsFilter = tokenLists.filter(item => item.id != tokenData);
141. this._res.links != null
142. if (count != 1) {
143. if (this._res.merchant != null
144. this._res.merchant_invoice_reference != null) {
145. if (this._res.merchant.address_details != null) {
146. this._res.links != null//
147. } else if (this._res.payments != null
148. this._res.payments[this._res.payments.length - 1].instrument != null
149. this._res.payments[this._res.payments.length - 1].instrument.issuer != null
150. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
151. if (this.type != 7) {
152. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
153. this._res.payments[this._res.payments.length - 1].links != null
154. this._res.payments[this._res.payments.length - 1].links.cancel != null
155. this._res.payments[this._res.payments.length - 1].links.cancel.href != null
156. this._res.payments[this._res.payments.length - 1].links.update != null
157. this._res.payments[this._res.payments.length - 1].links.update.href != null) {
158. this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
159. this._res.payments[this._res.payments.length - 1].authorization != null
160. this._res.payments[this._res.payments.length - 1].authorization.links != null) {
161. this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
162. this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
163. if (this._res.payments[this._res.payments.length - 1].authorization != null
164. this._res.payments[this._res.payments.length - 1].authorization.links != null
165. auth = paramUserName != null ? paramUserName : ''
166. if (this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
167. this._res.links.merchant_return != null //
168. e.type != 'ewallet') || (regex.test(strTest)
169. } else if (this._res.payments != null) {
170. _re.body?.state != 'canceled')
171. if (res.body != null
172. res.body.links != null
173. res.body.links.merchant_return != null
174. res.body.links.merchant_return.href != null) {
175. if (event.origin != window.origin) return; // chỉ nhận message từ OnePay
176. if (this.paymentType != PaymentType.ApplePay) return;
177. if (this.paymentType != PaymentType.ApplePay) return false;
178. if (network != 'napas'
179. 'mPAYvn', 'VNPT PAY', 'VinID', 'Ví Vimass', 'TrueMoney', 'Ví Việt', 'YOLO', 'Ví VNPAY', 'ViettelPay'].indexOf(this.bank.toString()) != -1) {
180. if (appcode != null) {
181. if (_re.status != '200'
182. _re.status != '201')
183. filteredDataOtherMobile.length > 0 && ((filteredDataDeeplink.length > 0 && deeplink_status) && (qr_version2 != 'None'))
184. qr_version2 != 'None'
185. qr_version2 != 'None'"
186. !(filteredDataOtherMobile.length > 0 && ((filteredDataDeeplink.length > 0 && deeplink_status) && (qr_version2 != 'None')))
187. qr_version2 != 'None')">
188. this.qr_version2 != 'None') {
189. if (this.translate.currentLang != language) {
190. qr_version2 != 'qrV1'
191. if (idInvoice != null
192. idInvoice != 0)
193. idInvoice != 0) {
194. let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
195. if (this._merchantid != null
196. this._tranref != null
197. this._state != null
198. urlCancel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
199. if (paymentId != null) {
200. if (res?.status != 200
201. res?.status != 201) return;
202. _re.status != '201') {
203. latestPayment?.state != "authorization_required") {
204. if (latestPayment?.instrument?.type != "vietqr") {
205. if (tem != null) {
206. if ((tem = ua.match(/version\/(\d+)/i)) != null) {
207. if (v.length != 3) {
208. if (network != "napas") return true;
209. if (currency != "VND") return false; // napas accept VND only
210. if (_modules[r][6] != null) {
211. if (_modules[6][c] != null) {
212. if (_modules[row][col] != null) {
213. while (buffer.getLengthInBits() % 8 != 0) {
214. if (count != numChars) {
215. throw count + ' != ' + numChars
216. while (data != 0) {
217. if (test.length != 2
218. ( (test[0] << 8) | test[1]) != code) {
219. if (_length % 3 != 0) {
220. if ( (data >>> length) != 0) {
221. return typeof _map[key] != 'undefined'

