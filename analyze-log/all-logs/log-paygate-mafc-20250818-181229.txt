====================================================================================================
TRÍCH XUẤT ĐIỀU KIỆN SO SÁNH TỪ CÁC FILE TYPESCRIPT/JAVASCRIPT/HTML
====================================================================================================
Thư mục/File nguồn: /root/projects/onepay/paygate-mafc/src
Thời gian: 18:12:29 18/8/2025
Tổng số file xử lý: 145
Tổng số file bị bỏ qua: 48
Tổng số điều kiện tìm thấy: 2961

THỐNG KÊ TỔNG QUAN THEO LOẠI TOÁN TỬ:
--------------------------------------------------
Strict equality (===): 1738 lần
Loose equality (==): 400 lần
Strict inequality (!==): 636 lần
Loose inequality (!=): 187 lần
--------------------------------------------------

DANH SÁCH FILE ĐÃ XỬ LÝ:
--------------------------------------------------
1. 4en.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/4en.html
2. 4vn.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/4vn.html
3. app-routing.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/app-routing.module.ts
4. app.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/app.component.html
5. app.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/app.component.spec.ts
6. app.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/app.component.ts
7. app.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/app.module.ts
8. counter.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/counter.directive.spec.ts
9. counter.directive.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/counter.directive.ts
10. format-carno-input.derective.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/directives/format-carno-input.derective.ts
11. uppercase-input.directive.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/directives/uppercase-input.directive.ts
12. error.component.html (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/error/error.component.html
13. error.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/error/error.component.spec.ts
14. error.component.ts (23 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/error/error.component.ts
15. support-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/error/support-dialog/support-dialog.html
16. support-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/error/support-dialog/support-dialog.ts
17. format-date.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/format-date.directive.spec.ts
18. format-date.directive.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/format-date.directive.ts
19. main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/main.component.html
20. main.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/main.component.spec.ts
21. main.component.ts (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/main.component.ts
22. bank-amount-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/bank-amount-dialog/bank-amount-dialog.component.html
23. bank-amount-dialog.component.ts (34 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/bank-amount-dialog/bank-amount-dialog.component.ts
24. cancel-dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/cancel-dialog-guide-dialog.html
25. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/confirm-dialog/dialog-guide-dialog.html
26. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/dialog-guide-dialog.html
27. bank.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/domescard-main/banks/model/bank.ts
28. otp-auth.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
29. otp-auth.component.ts (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
30. vietcombank.component.html (12 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
31. vietcombank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
32. vietcombank.component.ts (129 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
33. dialog-guide-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
34. off-bank-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
35. off-bank-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
36. domescard-main.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/domescard-main/domescard-main.component.html
37. domescard-main.component.ts (54 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/domescard-main/domescard-main.component.ts
38. menu.component.html (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/menu.component.html
39. menu.component.ts (130 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/menu.component.ts
40. policy-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/policy-dialog/policy-dialog.component.html
41. policy-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/policy-dialog/policy-dialog.component.ts
42. remove-token-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/token-main/remove-token-dialog/remove-token-dialog.html
43. dialog-guide-dialog.html (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/token-main/token-dialog/dialog-guide-dialog.html
44. token-main.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/token-main/token-main.component.html
45. token-main.component.ts (70 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/token-main/token-main.component.ts
46. overlay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/overlay/overlay.component.html
47. overlay.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/overlay/overlay.component.spec.ts
48. overlay.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/overlay/overlay.component.ts
49. bank-amount.pipe.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/pipe/bank-amount.pipe.ts
50. close-dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/services/close-dialog.service.ts
51. data.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/services/data.service.ts
52. dialog.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/services/dialog.service.ts
53. fee.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/services/fee.service.ts
54. focus-input.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/services/focus-input.service.ts
55. multiple_method.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/services/multiple_method.service.ts
56. payment.service.ts (8 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/services/payment.service.ts
57. token-main.service.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/services/token-main.service.ts
58. index.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/translate/index.ts
59. lang-en.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/translate/lang-en.ts
60. lang-vi.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/translate/lang-vi.ts
61. translate.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/translate/translate.pipe.ts
62. translate.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/translate/translate.service.ts
63. translations.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/translate/translations.ts
64. apps-info.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/util/apps-info.ts
65. banks-info.ts (3 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/util/banks-info.ts
66. util.ts (24 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/util/util.ts
67. qrcode.js (67 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/assets/script/qrcode.js
68. environment.dev.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/environments/environment.dev.ts
69. environment.mtf.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/environments/environment.mtf.ts
70. environment.prod.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/environments/environment.prod.ts
71. environment.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/environments/environment.ts
72. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/acc_bidv/index.html
73. script.js (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/acc_bidv/script.js
74. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/acc_oceanbank/index.html
75. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/acc_oceanbank/script.js
76. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/acc_pvcombank/index.html
77. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/acc_pvcombank/script.js
78. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/acc_shb/index.html
79. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/acc_shb/script.js
80. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/acc_tpbank/index.html
81. script.js (4 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/acc_tpbank/script.js
82. common.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/common.js
83. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/domestic_card/index.html
84. script.js (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/domestic_card/script.js
85. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/domestic_token/index.html
86. script.js (25 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/domestic_token/script.js
87. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/index.html
88. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/international_card/index.html
89. script.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/international_card/script.js
90. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/international_token/index.html
91. script.js (36 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/international_token/script.js
92. script.js (54 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/script.js
93. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.js
94. bidv.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Bidv/assets/js/bidv.js
95. bidv2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Bidv/bidv2.js
96. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Bidv/index.html
97. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.js
98. ocean.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Ocean/assets/js/ocean.js
99. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Ocean/index.html
100. ocean2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Ocean/ocean2.js
101. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
102. pvbank.js (23 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Pv_Bank/assets/js/pvbank.js
103. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Pv_Bank/index.html
104. pvbank2.js (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Pv_Bank/pvbank2.js
105. Sea2.js (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/SeaBank/Sea2.js
106. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
107. Sea.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/SeaBank/assets/js/Sea.js
108. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/SeaBank/index.html
109. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.js
110. shb.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Shb/assets/js/shb.js
111. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Shb/index.html
112. shb2.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Shb/shb2.js
113. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
114. tpbank.js (23 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Tp_Bank/assets/js/tpbank.js
115. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Tp_Bank/index.html
116. tpbank2.js (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Tp_Bank/tpbank2.js
117. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.js
118. onepay.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Visa/assets/js/onepay.js
119. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Visa/index.html
120. index.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Visa/index.js
121. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
122. vpbank.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Vp_Bank/assets/js/vpbank.js
123. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Vp_Bank/index.html
124. vpbank2.js (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Vp_Bank/vpbank2.js
125. sha.js (49 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/assets/js/sha.js
126. sha256.js (28 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/assets/js/sha256.js
127. slick.js (182 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/assets/libraries/slick/slick.js
128. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.js
129. atm_b1.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_1/assets/js/atm_b1.js
130. atm_b1_2.js (11 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_1/atm_b1_2.js
131. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_1/index.html
132. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.js
133. atm_b2.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_2/assets/js/atm_b2.js
134. atm_b2_2.js (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_2/atm_b2_2.js
135. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_2/index.html
136. common.js (22 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/common.js
137. bootstrap.js (135 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.js
138. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/international_card/index.html
139. script.js (9 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/international_card/script.js
140. index.html (35 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/index.html
141. index.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/index.html
142. karma.conf.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/karma.conf.js
143. main.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/main.ts
144. polyfills.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/polyfills.ts
145. test.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/test.ts

DANH SÁCH FILE BỊ BỎ QUA:
--------------------------------------------------
1. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/bootstrap.min.js
2. jquery-3.0.0.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/jquery-3.0.0.min.js
3. popper.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/popper.min.js
4. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
5. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
6. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
7. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Bidv/assets/js/jquery-3.4.1.min.js
8. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
9. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
10. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
11. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Ocean/assets/js/jquery-3.4.1.min.js
12. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
13. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
14. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
15. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Pv_Bank/assets/js/jquery-3.4.1.min.js
16. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
17. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
18. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
19. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/SeaBank/assets/js/jquery-3.4.1.min.js
20. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
21. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
22. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
23. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Shb/assets/js/jquery-3.4.1.min.js
24. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
25. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
26. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
27. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Tp_Bank/assets/js/jquery-3.4.1.min.js
28. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
29. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
30. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
31. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Visa/assets/js/jquery-3.4.1.min.js
32. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
33. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
34. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
35. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Vp_Bank/assets/js/jquery-3.4.1.min.js
36. instafeed.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/assets/js/instafeed.min.js
37. slick.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/assets/libraries/slick/slick.min.js
38. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
39. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
40. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
41. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_1/assets/js/jquery-3.4.1.min.js
42. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
43. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
44. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js
45. jquery-3.4.1.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_2/assets/js/jquery-3.4.1.min.js
46. bootstrap.bundle.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.js
47. bootstrap.bundle.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.bundle.min.js
48. bootstrap.min.js
   Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.min.js

====================================================================================================
CHI TIẾT TỪNG FILE:
====================================================================================================

📁 FILE 1: 4en.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/4en.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 2: 4vn.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/4vn.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 3: app-routing.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/app-routing.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 4: app.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/app.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 5: app.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/app.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 6: app.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/app.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 76] return lang === this._translate.currentLang

== (2 điều kiện):
  1. [Dòng 55] 'vi' == params['locale']) {
  2. [Dòng 57] 'en' == params['locale']) {

!= (3 điều kiện):
  1. [Dòng 55] if (params['locale'] != null
  2. [Dòng 57] } else if (params['locale'] != null
  3. [Dòng 64] if (userLang != null

================================================================================

📁 FILE 7: app.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/app.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 8: counter.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/counter.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 9: counter.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/counter.directive.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 10: format-carno-input.derective.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/directives/format-carno-input.derective.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 44] this.lastValue !== $event.target.value)) {

!= (1 điều kiện):
  1. [Dòng 23] if(value!=null){

================================================================================

📁 FILE 11: uppercase-input.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/directives/uppercase-input.directive.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 0 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 23] this.lastValue !== $event.target.value)) {

================================================================================

📁 FILE 12: error.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/error/error.component.html
📊 Thống kê: 11 điều kiện duy nhất
   - === : 3 lần
   - == : 8 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 72] isPopupSupport === 'True') || (rePayment
  2. [Dòng 73] isPopupSupport === 'True'"
  3. [Dòng 79] isPopupSupport === 'True')">

== (8 điều kiện):
  1. [Dòng 3] errorCode == '11'"
  2. [Dòng 72] <div class="footer-button" *ngIf="(isSent == false
  3. [Dòng 73] isSent == false
  4. [Dòng 79] <div class="payment_again" *ngIf="rePayment && !timeOut" [class.select_only]="!(isSent == false
  5. [Dòng 92] errorCode == 'overtime'
  6. [Dòng 92] errorCode == '253'"
  7. [Dòng 94] <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
  8. [Dòng 94] errorCode == '253')">

================================================================================

📁 FILE 13: error.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/error/error.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 14: error.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/error/error.component.ts
📊 Thống kê: 23 điều kiện duy nhất
   - === : 8 lần
   - == : 11 lần
   - !== : 0 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 123] params.timeout === 'true') {
  2. [Dòng 142] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  3. [Dòng 142] _re.body.state === 'unpaid');
  4. [Dòng 210] if (this.errorCode === 'overtime'
  5. [Dòng 210] this.errorCode === '253') {
  6. [Dòng 274] params.name === 'CUSTOMER_INTIME'
  7. [Dòng 274] params.code === '09') {
  8. [Dòng 321] if (this.timeLeft === 0) {

== (11 điều kiện):
  1. [Dòng 150] if (this.res.currencies[0] == 'USD') {
  2. [Dòng 184] if(_re.body.themes.theme == 'mafc'){
  3. [Dòng 190] params.response_code == 'overtime') {
  4. [Dòng 229] if (_re.status == '200'
  5. [Dòng 229] _re.status == '201') {
  6. [Dòng 242] if (_re2.status == '200'
  7. [Dòng 242] _re2.status == '201') {
  8. [Dòng 255] if (this.errorCode == 'overtime'
  9. [Dòng 255] this.errorCode == '253') {
  10. [Dòng 260] this.res.state == 'canceled') {
  11. [Dòng 319] if (this.isTimePause == false) {

!= (4 điều kiện):
  1. [Dòng 379] if (this.res != null
  2. [Dòng 379] this.res.links != null
  3. [Dòng 379] this.res.links.merchant_return != null
  4. [Dòng 380] this.res.links.merchant_return.href != null) {

================================================================================

📁 FILE 15: support-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/error/support-dialog/support-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 16: support-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/error/support-dialog/support-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 17: format-date.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/format-date.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 18: format-date.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/format-date.directive.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0
  2. [Dòng 123] YY % 4 === 0)) {
  3. [Dòng 138] if (YYYY % 400 === 0
  4. [Dòng 138] YYYY % 4 === 0)) {

!== (2 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0 || (YY % 100 !== 0
  2. [Dòng 138] if (YYYY % 400 === 0 || (YYYY % 100 !== 0

================================================================================

📁 FILE 19: main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 20: main.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/main.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 21: main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/main.component.ts
📊 Thống kê: 11 điều kiện duy nhất
   - === : 0 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 7 lần
--------------------------------------------------------------------------------

== (4 điều kiện):
  1. [Dòng 93] if ((dataPassed.status == '200'
  2. [Dòng 93] dataPassed.status == '201') && dataPassed.body != null) {
  3. [Dòng 134] if (message == '1') {
  4. [Dòng 136] } else if (message == '0') {

!= (7 điều kiện):
  1. [Dòng 84] if (this._idInvoice != null
  2. [Dòng 84] this._idInvoice != 0) {
  3. [Dòng 85] if (this._paymentService.getInvoiceDetail() != null) {
  4. [Dòng 93] dataPassed.body != null) {
  5. [Dòng 113] dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
  6. [Dòng 114] dataPassed.body.themes.border_color != true ? dataPassed.body.themes.border_color : ''
  7. [Dòng 171] if (this._translate.currentLang != language) {

================================================================================

📁 FILE 22: bank-amount-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/bank-amount-dialog/bank-amount-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 23: bank-amount-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/bank-amount-dialog/bank-amount-dialog.component.ts
📊 Thống kê: 34 điều kiện duy nhất
   - === : 0 lần
   - == : 34 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (34 điều kiện):
  1. [Dòng 41] if (this.locale == 'en') {
  2. [Dòng 55] if (name == 'MAFC')
  3. [Dòng 61] if (bankId == 3
  4. [Dòng 61] bankId == 61
  5. [Dòng 62] bankId == 8
  6. [Dòng 62] bankId == 49
  7. [Dòng 63] bankId == 48
  8. [Dòng 64] bankId == 10
  9. [Dòng 64] bankId == 53
  10. [Dòng 65] bankId == 17
  11. [Dòng 65] bankId == 65
  12. [Dòng 66] bankId == 23
  13. [Dòng 66] bankId == 52
  14. [Dòng 67] bankId == 27
  15. [Dòng 67] bankId == 66
  16. [Dòng 68] bankId == 9
  17. [Dòng 68] bankId == 54
  18. [Dòng 69] bankId == 37
  19. [Dòng 70] bankId == 38
  20. [Dòng 71] bankId == 39
  21. [Dòng 72] bankId == 40
  22. [Dòng 73] bankId == 42
  23. [Dòng 74] bankId == 44
  24. [Dòng 75] bankId == 72
  25. [Dòng 76] bankId == 59
  26. [Dòng 79] bankId == 51
  27. [Dòng 80] bankId == 64
  28. [Dòng 81] bankId == 58
  29. [Dòng 82] bankId == 56
  30. [Dòng 85] bankId == 55
  31. [Dòng 86] bankId == 60
  32. [Dòng 87] bankId == 68
  33. [Dòng 88] bankId == 74
  34. [Dòng 89] bankId == 75

================================================================================

📁 FILE 24: cancel-dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/cancel-dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 25: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/confirm-dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 26: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 27: bank.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/domescard-main/banks/model/bank.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 28: otp-auth.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 29: otp-auth.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/domescard-main/banks/otp-auth/otp-auth.component.ts
📊 Thống kê: 22 điều kiện duy nhất
   - === : 0 lần
   - == : 11 lần
   - !== : 1 lần
   - != : 10 lần
--------------------------------------------------------------------------------

== (11 điều kiện):
  1. [Dòng 98] if (this._b == 8) {//MB Bank
  2. [Dòng 102] if (this._b == 18) {//Oceanbank
  3. [Dòng 139] if (this._b == 8) {
  4. [Dòng 144] if (this._b == 18) {
  5. [Dòng 149] if (this._b == 12) { //SHB
  6. [Dòng 170] if (_re.status == '200'
  7. [Dòng 170] _re.status == '201') {
  8. [Dòng 179] } else if (this._res.state == 'authorization_required') {
  9. [Dòng 211] if (reason.code == '25'
  10. [Dòng 306] if (this._b == 12) {
  11. [Dòng 357] if (this._b == 18) {//8-MB Bank;18-oceanbank

!== (1 điều kiện):
  1. [Dòng 185] codeResponse.toString() !== '0') {

!= (10 điều kiện):
  1. [Dòng 175] if (this._res.links != null
  2. [Dòng 175] this._res.links.merchant_return != null
  3. [Dòng 175] this._res.links.merchant_return.href != null) {
  4. [Dòng 211] this.challengeCode != '') {
  5. [Dòng 329] if (this._res_post != null
  6. [Dòng 329] this._res_post.links != null
  7. [Dòng 329] this._res_post.links.merchant_return != null
  8. [Dòng 329] this._res_post.links.merchant_return.href != null) {
  9. [Dòng 352] if (!(_formCard.otp != null
  10. [Dòng 358] if (!(_formCard.password != null

================================================================================

📁 FILE 30: vietcombank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.html
📊 Thống kê: 12 điều kiện duy nhất
   - === : 2 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 7 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 23] c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
  2. [Dòng 23] _inExpDate.trim().length === 0)"

== (3 điều kiện):
  1. [Dòng 53] _b == 6 "
  2. [Dòng 54] token_site == 'onepay'
  3. [Dòng 64] _b == 68"

!= (7 điều kiện):
  1. [Dòng 19] <div class="form-field form-field-small margin-right-2" [class.visible_hidden]="!valid_card" *ngIf="d_card_date && (_b != 3
  2. [Dòng 19] d_card_date && (_b != 3 && _b != 19 && _b != 18)
  3. [Dòng 19] _b != 18)">
  4. [Dòng 32] *ngIf="(_b != 18
  5. [Dòng 32] (_b != 18 && _b != 6 && _b != 2 )
  6. [Dòng 32] _b != 2 )">
  7. [Dòng 76] _b != 6 && _b != 2

================================================================================

📁 FILE 31: vietcombank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 32: vietcombank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/domescard-main/banks/vietcombank/vietcombank.component.ts
📊 Thống kê: 129 điều kiện duy nhất
   - === : 5 lần
   - == : 89 lần
   - !== : 2 lần
   - != : 33 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 120] if (target.tagName === 'A'
  2. [Dòng 395] if (event.keyCode === 8
  3. [Dòng 395] event.key === "Backspace"
  4. [Dòng 656] if (approval.method === 'REDIRECT') {
  5. [Dòng 659] } else if (approval.method === 'POST_REDIRECT') {

== (89 điều kiện):
  1. [Dòng 140] this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
  2. [Dòng 178] if (this._b == 1
  3. [Dòng 178] this._b == 20
  4. [Dòng 178] this._b == 36
  5. [Dòng 178] this._b == 64
  6. [Dòng 178] this._b == 55
  7. [Dòng 178] this._b == 47
  8. [Dòng 178] this._b == 48
  9. [Dòng 178] this._b == 59) {
  10. [Dòng 189] if (message == '1') {
  11. [Dòng 200] return this._b == 3
  12. [Dòng 200] this._b == 9
  13. [Dòng 200] this._b == 16
  14. [Dòng 200] this._b == 17
  15. [Dòng 200] this._b == 19
  16. [Dòng 200] this._b == 25
  17. [Dòng 200] this._b == 44
  18. [Dòng 201] this._b == 57
  19. [Dòng 201] this._b == 59
  20. [Dòng 201] this._b == 61
  21. [Dòng 201] this._b == 63
  22. [Dòng 201] this._b == 69
  23. [Dòng 205] return this._b == 6
  24. [Dòng 205] this._b == 2
  25. [Dòng 205] return this._b == 6 || this._b == 2 || (this._b == 5
  26. [Dòng 205] this.vibFlow=='internet_banking') || this._b == 57;
  27. [Dòng 209] return this._b == 18
  28. [Dòng 241] if (parseInt(b) == parseInt(v.substring(0, 6))) {
  29. [Dòng 395] event.inputType == 'deleteContentBackward') {
  30. [Dòng 396] if (event.target.name == 'exp_date'
  31. [Dòng 404] event.inputType == 'insertCompositionText') {
  32. [Dòng 481] this.vibFlow=='bank_account')) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
  33. [Dòng 487] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  34. [Dòng 498] } else if (this._b == 2) {
  35. [Dòng 500] } else if (this._b == 5
  36. [Dòng 500] this.vibFlow == 'internet_banking')  {
  37. [Dòng 502] }  else if (this._b == 5
  38. [Dòng 502] this.vibFlow == 'bank_account')  {
  39. [Dòng 504] } else if (this._b == 6) {
  40. [Dòng 506] } else if (this._b == 31) {
  41. [Dòng 529] this.token_site == 'onepay'
  42. [Dòng 532] if(this._b == 12){
  43. [Dòng 546] if (this._res_post.state == 'approved'
  44. [Dòng 546] this._res_post.state == 'failed') {
  45. [Dòng 596] } else if (this._res_post.state == 'authorization_required') {
  46. [Dòng 618] this._b == 14
  47. [Dòng 618] this._b == 15
  48. [Dòng 618] this._b == 24
  49. [Dòng 618] this._b == 8
  50. [Dòng 618] this._b == 10
  51. [Dòng 618] this._b == 18
  52. [Dòng 618] this._b == 22
  53. [Dòng 618] this._b == 23
  54. [Dòng 618] this._b == 27
  55. [Dòng 618] this._b == 30
  56. [Dòng 618] this._b == 11
  57. [Dòng 618] if (this._b == 1 || this._b == 14 || this._b == 15 || this._b == 24 || this._b == 8 || this._b == 10 || this._b == 18 || this._b == 20 || this._b == 22 || this._b == 23 || this._b == 27 || this._b == 30 || this._b == 11 || this._b == 17 || (this._b==5
  58. [Dòng 618] this.vibFlow=='bank_account') || this._b == 12 || this._b == 9) {
  59. [Dòng 618] this._b == 12
  60. [Dòng 618] this._b == 9) {
  61. [Dòng 687] if (err.status == 400
  62. [Dòng 687] err.status == 500) {
  63. [Dòng 688] if (err.error && (err.error.code == 13
  64. [Dòng 688] err.error.code == '13')) {
  65. [Dòng 691] err.error['name'] == 'INVALID_INPUT_BIN') {
  66. [Dòng 705] if ((cardNo.length == 16
  67. [Dòng 706] (cardNo.length == 19
  68. [Dòng 706] (cardNo.length == 19 && (this._b == 1
  69. [Dòng 706] this._b == 4
  70. [Dòng 706] this._b == 5
  71. [Dòng 706] this._b == 67))
  72. [Dòng 708] this._util.checkMod10(cardNo) == true
  73. [Dòng 795] return ((value.length == 4
  74. [Dòng 795] value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
  75. [Dòng 795] value.length == 5) && parseInt(value.split('/')[0]
  76. [Dòng 806] return this._b == 11
  77. [Dòng 806] this._b == 33
  78. [Dòng 806] this._b == 39
  79. [Dòng 806] this._b == 43
  80. [Dòng 806] this._b == 45
  81. [Dòng 807] this._b == 67
  82. [Dòng 807] this._b == 72
  83. [Dòng 807] this._b == 73
  84. [Dòng 807] this._b == 68
  85. [Dòng 807] this._b == 74
  86. [Dòng 807] this._b == 75
  87. [Dòng 838] this._inExpDate.length == 4
  88. [Dòng 838] this._inExpDate.search('/') == -1)
  89. [Dòng 839] this._inExpDate.length == 5))

!== (2 điều kiện):
  1. [Dòng 559] codeResponse.toString() !== '0') {
  2. [Dòng 619] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (33 điều kiện):
  1. [Dòng 150] this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
  2. [Dòng 183] if (this.htmlDesc != null
  3. [Dòng 218] if (ua.indexOf('safari') != -1
  4. [Dòng 397] if (this._inExpDate.length != 3) {
  5. [Dòng 480] if ((this._b != 3
  6. [Dòng 480] this._b != 9
  7. [Dòng 480] this._b != 16
  8. [Dòng 480] this._b != 17
  9. [Dòng 480] this._b != 18
  10. [Dòng 480] this._b != 19
  11. [Dòng 480] this._b != 25
  12. [Dòng 480] this._b != 44
  13. [Dòng 480] this._b != 54
  14. [Dòng 481] this._b != 57
  15. [Dòng 481] this._b != 59
  16. [Dòng 481] this._b != 61
  17. [Dòng 481] this._b != 63
  18. [Dòng 481] this._b != 69
  19. [Dòng 481] this._b != 6
  20. [Dòng 481] this._b != 2
  21. [Dòng 481] this._b != 5) || (this._b==5
  22. [Dòng 496] '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72' , '73', '74', '75'].indexOf(this._b.toString()) != -1) {
  23. [Dòng 548] if (this._res_post.return_url != null) {
  24. [Dòng 551] if (this._res_post.links != null
  25. [Dòng 551] this._res_post.links.merchant_return != null
  26. [Dòng 551] this._res_post.links.merchant_return.href != null) {
  27. [Dòng 601] if (this._res_post.authorization != null
  28. [Dòng 601] this._res_post.authorization.links != null
  29. [Dòng 606] this._res_post.links.cancel != null) {
  30. [Dòng 612] let userName = _formCard.name != null ? _formCard.name : ''
  31. [Dòng 613] this._res_post.authorization.links.approval != null
  32. [Dòng 613] this._res_post.authorization.links.approval.href != null) {
  33. [Dòng 616] userName = paramUserName != null ? paramUserName : ''

================================================================================

📁 FILE 33: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/domescard-main/dialog/dialog-guide-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 34: off-bank-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 35: off-bank-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/domescard-main/dialog/off-bank-dialog/off-bank-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 36: domescard-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/domescard-main/domescard-main.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 1 lần
   - == : 4 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 21] filteredData.length === 0"

== (4 điều kiện):
  1. [Dòng 5] _auth==1) || (token
  2. [Dòng 5] _b == 16)"
  3. [Dòng 7] (token || _auth==1) && _b != 16
  4. [Dòng 10] _auth==1)">

================================================================================

📁 FILE 37: domescard-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/domescard-main/domescard-main.component.ts
📊 Thống kê: 54 điều kiện duy nhất
   - === : 23 lần
   - == : 25 lần
   - !== : 1 lần
   - != : 5 lần
--------------------------------------------------------------------------------

=== (23 điều kiện):
  1. [Dòng 238] if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
  2. [Dòng 239] filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
  3. [Dòng 287] if (valOut === 'auth') {
  4. [Dòng 365] if (this._b === '1'
  5. [Dòng 365] this._b === '20'
  6. [Dòng 365] this._b === '64') {
  7. [Dòng 368] if (this._b === '36'
  8. [Dòng 368] this._b === '18'
  9. [Dòng 368] this._b === '19'
  10. [Dòng 371] if (this._b === '19'
  11. [Dòng 371] this._b === '16'
  12. [Dòng 371] this._b === '25'
  13. [Dòng 371] this._b === '33'
  14. [Dòng 372] this._b === '39'
  15. [Dòng 372] this._b === '11'
  16. [Dòng 372] this._b === '17'
  17. [Dòng 373] this._b === '36'
  18. [Dòng 373] this._b === '44'
  19. [Dòng 373] this._b === '12'
  20. [Dòng 374] this._b === '64'
  21. [Dòng 377] if (this._b === '20'
  22. [Dòng 380] if (this._b === '12'
  23. [Dòng 380] this._b === '18') {

== (25 điều kiện):
  1. [Dòng 166] this._auth == 0
  2. [Dòng 166] this.tokenList.length == 0) {
  3. [Dòng 225] this.filteredData.length == 1
  4. [Dòng 265] if ($event && ($event == 'true'
  5. [Dòng 368] this._b == '55'
  6. [Dòng 368] this._b == '47'
  7. [Dòng 368] this._b == '48'
  8. [Dòng 368] this._b == '59'
  9. [Dòng 368] this._b == '67'
  10. [Dòng 368] this._b == '73'
  11. [Dòng 368] this._b == '12') {
  12. [Dòng 371] this._b == '3'
  13. [Dòng 372] this._b == '43'
  14. [Dòng 372] this._b == '45'
  15. [Dòng 373] this._b == '57'
  16. [Dòng 374] this._b == '61'
  17. [Dòng 374] this._b == '63'
  18. [Dòng 374] this._b == '68'
  19. [Dòng 374] this._b == '69'
  20. [Dòng 374] this._b == '72'
  21. [Dòng 374] this._b == '9'
  22. [Dòng 374] this._b == '74'
  23. [Dòng 374] this._b == '75') {
  24. [Dòng 377] this._b == '36'
  25. [Dòng 397] if (item['id'] == this._b) {

!== (1 điều kiện):
  1. [Dòng 111] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (5 điều kiện):
  1. [Dòng 145] if (params['locale'] != null) {
  2. [Dòng 151] if ('otp' != this._paymentService.getCurrentPage()) {
  3. [Dòng 188] if (!(strInstrument != null
  4. [Dòng 191] if (strInstrument.substring(0, 1) != '^'
  5. [Dòng 191] strInstrument.substr(strInstrument.length - 1) != '$') {

================================================================================

📁 FILE 38: menu.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/menu.component.html
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 74] [ngStyle]="{'border-color': type === 2 ? this.themeColor.border_color : border_color}"
  2. [Dòng 91] d_vrbank===true"

== (2 điều kiện):
  1. [Dòng 39] _auth==1)">
  2. [Dòng 76] token || _auth==1

================================================================================

📁 FILE 39: menu.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/menu.component.ts
📊 Thống kê: 130 điều kiện duy nhất
   - === : 9 lần
   - == : 60 lần
   - !== : 3 lần
   - != : 58 lần
--------------------------------------------------------------------------------

=== (9 điều kiện):
  1. [Dòng 659] this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number === 1
  2. [Dòng 699] if (this._res.state === 'unpaid'
  3. [Dòng 699] this._res.state === 'not_paid') {
  4. [Dòng 793] if ('op' === auth
  5. [Dòng 830] } else if ('bank' === auth
  6. [Dòng 835] if (approval.method === 'REDIRECT') {
  7. [Dòng 838] } else if (approval.method === 'POST_REDIRECT') {
  8. [Dòng 1081] return id === 'amex' ? '1234' : '123'
  9. [Dòng 1244] if (this.timeLeftPaypal === 0) {

== (60 điều kiện):
  1. [Dòng 193] if (!isNaN(_re.status) && (_re.status == '200'
  2. [Dòng 193] _re.status == '201') && _re.body != null) {
  3. [Dòng 198] if (('closed' == this._res_polling.state
  4. [Dòng 198] 'canceled' == this._res_polling.state
  5. [Dòng 198] 'expired' == this._res_polling.state)
  6. [Dòng 218] } else if ('paid' == this._res_polling.state) {
  7. [Dòng 224] this._res_polling.payments == null
  8. [Dòng 226] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
  9. [Dòng 228] this._paymentService.getCurrentPage() == 'enter_card') {
  10. [Dòng 231] } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
  11. [Dòng 231] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
  12. [Dòng 249] } else if ('not_paid' == this._res_polling.state) {
  13. [Dòng 261] this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
  14. [Dòng 385] if (message == '1') {
  15. [Dòng 387] } else if (message == '0') {
  16. [Dòng 407] if (_re.status == '200'
  17. [Dòng 407] _re.status == '201') {
  18. [Dòng 433] if (this.type == 5
  19. [Dòng 436] } else if (this.type == 6
  20. [Dòng 439] } else if (this.type == 2
  21. [Dòng 442] } else if (this.type == 7
  22. [Dòng 445] } else if (this.type == 8
  23. [Dòng 448] } else if (this.type == 4
  24. [Dòng 451] } else if (this.type == 3
  25. [Dòng 665] this._auth == 0) {
  26. [Dòng 700] if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
  27. [Dòng 700] this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
  28. [Dòng 702] if (this._res.payments[this._res.payments.length - 1].state == 'approved'
  29. [Dòng 704] } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
  30. [Dòng 742] } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  31. [Dòng 742] this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
  32. [Dòng 779] } else if (idBrand == 'atm'
  33. [Dòng 858] if ('paid' == this._res.state) {
  34. [Dòng 859] this._res.merchant.token_site == 'onepay')) {
  35. [Dòng 880] if (('closed' == this._res.state
  36. [Dòng 880] 'canceled' == this._res.state
  37. [Dòng 880] 'expired' == this._res.state
  38. [Dòng 880] 'paid' == this._res.state)
  39. [Dòng 902] this._res.payments == null) {
  40. [Dòng 904] this._res.payments[this._res.payments.length - 1].state == 'pending') {
  41. [Dòng 914] if (this._res.currencies[0] == 'USD') {
  42. [Dòng 978] if (item.instrument.issuer.brand.id == 'atm') {
  43. [Dòng 980] } else if (item.instrument.issuer.brand.id == 'visa'
  44. [Dòng 980] item.instrument.issuer.brand.id == 'mastercard') {
  45. [Dòng 981] if (item.instrument.issuer_location == 'd') {
  46. [Dòng 986] } else if (item.instrument.issuer.brand.id == 'amex') {
  47. [Dòng 992] } else if (item.instrument.issuer.brand.id == 'jcb') {
  48. [Dòng 1008] uniq.length == 1) {
  49. [Dòng 1188] _val == 2) {
  50. [Dòng 1215] if (_val == 2
  51. [Dòng 1217] } else if (_val == 2
  52. [Dòng 1223] _val == 2
  53. [Dòng 1232] if (this.type == 4) {
  54. [Dòng 1312] if (this._res_post.state == 'approved'
  55. [Dòng 1312] this._res_post.state == 'failed') {
  56. [Dòng 1321] } else if (this._res_post.state == 'authorization_required') {
  57. [Dòng 1322] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  58. [Dòng 1336] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  59. [Dòng 1397] filteredData.length == 1) {
  60. [Dòng 1472] if (data._locale == 'en') {

!== (3 điều kiện):
  1. [Dòng 805] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
  2. [Dòng 1179] if (_val !== 3) {
  3. [Dòng 1183] this._util.getElementParams(this.currentUrl, 't_id') !== '') {

!= (58 điều kiện):
  1. [Dòng 186] if (this._idInvoice != null
  2. [Dòng 186] this._paymentService.getState() != 'error') {
  3. [Dòng 192] if (this._paymentService.getCurrentPage() != 'otp') {
  4. [Dòng 193] _re.body != null) {
  5. [Dòng 199] this._res_polling.links != null
  6. [Dòng 199] this._res_polling.links.merchant_return != null //
  7. [Dòng 224] } else if (this._res_polling.merchant != null
  8. [Dòng 224] this._res_polling.merchant_invoice_reference != null
  9. [Dòng 226] } else if (this._res_polling.payments != null
  10. [Dòng 232] this._res_polling.links.merchant_return != null//
  11. [Dòng 252] this._res_polling.payments != null
  12. [Dòng 260] if (this._res_polling.payments != null
  13. [Dòng 264] t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
  14. [Dòng 370] if (params['locale'] != null) {
  15. [Dòng 378] if ('otp' != this._paymentService.getCurrentPage()) {
  16. [Dòng 399] if (this._paymentService.getInvoiceDetail() != null) {
  17. [Dòng 679] if (count != 1) {
  18. [Dòng 689] if (this._res.merchant != null
  19. [Dòng 689] this._res.merchant_invoice_reference != null) {
  20. [Dòng 692] if (this._res.merchant.address_details != null) {
  21. [Dòng 700] this._res.links != null//
  22. [Dòng 742] } else if (this._res.payments != null
  23. [Dòng 743] this._res.payments[this._res.payments.length - 1].instrument != null
  24. [Dòng 743] this._res.payments[this._res.payments.length - 1].instrument.issuer != null
  25. [Dòng 744] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
  26. [Dòng 744] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
  27. [Dòng 745] this._res.payments[this._res.payments.length - 1].links != null
  28. [Dòng 745] this._res.payments[this._res.payments.length - 1].links.cancel != null
  29. [Dòng 745] this._res.payments[this._res.payments.length - 1].links.cancel.href != null
  30. [Dòng 764] this._res.payments[this._res.payments.length - 1].links.update != null
  31. [Dòng 764] this._res.payments[this._res.payments.length - 1].links.update.href != null) {
  32. [Dòng 779] this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
  33. [Dòng 780] this._res.payments[this._res.payments.length - 1].authorization != null
  34. [Dòng 780] this._res.payments[this._res.payments.length - 1].authorization.links != null) {
  35. [Dòng 793] this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
  36. [Dòng 793] this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
  37. [Dòng 796] if (this._res.payments[this._res.payments.length - 1].authorization != null
  38. [Dòng 796] this._res.payments[this._res.payments.length - 1].authorization.links != null
  39. [Dòng 802] auth = paramUserName != null ? paramUserName : ''
  40. [Dòng 881] this._res.links != null
  41. [Dòng 881] this._res.links.merchant_return != null //
  42. [Dòng 902] } else if (this._res.merchant != null
  43. [Dòng 902] this._res.merchant_invoice_reference != null
  44. [Dòng 1016] if (['mastercard', 'visa', 'amex', 'jcb', 'cup', 'card', 'np_card', 'atm'].indexOf(id) != -1) {
  45. [Dòng 1018] } else if (['techcombank_account', 'vib_account', 'dongabank_account', 'tpbank_account', 'bidv_account', 'pvcombank_account', 'shb_account', 'oceanbank_online_account'].indexOf(id) != -1) {
  46. [Dòng 1020] } else if (['oceanbank_mobile_account'].indexOf(id) != -1) {
  47. [Dòng 1022] } else if (['shb_customer_id'].indexOf(id) != -1) {
  48. [Dòng 1048] if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1) {
  49. [Dòng 1076] return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
  50. [Dòng 1113] if (!(strInstrument != null
  51. [Dòng 1130] if (this._translate.currentLang != language) {
  52. [Dòng 1185] } else if (this._res.payments != null) {
  53. [Dòng 1314] if (this._res_post.return_url != null) {
  54. [Dòng 1316] } else if (this._res_post.links != null
  55. [Dòng 1316] this._res_post.links.merchant_return != null
  56. [Dòng 1316] this._res_post.links.merchant_return.href != null) {
  57. [Dòng 1421] if (this._res_post != null
  58. [Dòng 1421] this._res_post.links != null

================================================================================

📁 FILE 40: policy-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/policy-dialog/policy-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 41: policy-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/policy-dialog/policy-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 42: remove-token-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/token-main/remove-token-dialog/remove-token-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 43: dialog-guide-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/token-main/token-dialog/dialog-guide-dialog.html
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 6 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (6 điều kiện):
  1. [Dòng 9] data['type'] == 'Visa'
  2. [Dòng 9] data['type'] == 'Master'
  3. [Dòng 9] data['type'] == 'JCB'"
  4. [Dòng 17] data['type'] == 'Visa'"
  5. [Dòng 17] data['type'] == 'Master'"
  6. [Dòng 24] data['type'] == 'Amex'"

================================================================================

📁 FILE 44: token-main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/token-main/token-main.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 9] style="{{item.brand_id === 'visa' ? 'height: 14.42px

== (2 điều kiện):
  1. [Dòng 3] i == tokenList.length - 1"
  2. [Dòng 29] token_main == '1'"

!= (2 điều kiện):
  1. [Dòng 22] item['feeService']['fee'] != 0
  2. [Dòng 24] item['feeService']['fee'] != 0"

================================================================================

📁 FILE 45: token-main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/main/menu/token-main/token-main.component.ts
📊 Thống kê: 70 điều kiện duy nhất
   - === : 7 lần
   - == : 42 lần
   - !== : 1 lần
   - != : 20 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 88] if (event.keyCode === 13) {
  2. [Dòng 110] if (target.tagName === 'A'
  3. [Dòng 259] && ((item.brand_id === 'amex'
  4. [Dòng 275] return id === 'amex' ? '1234' : '123'
  5. [Dòng 441] if (approval.method === 'REDIRECT') {
  6. [Dòng 444] } else if (approval.method === 'POST_REDIRECT') {
  7. [Dòng 516] return this._i_token_otp != null && !isNaN(+this._i_token_otp) && ((id === 'amex'

== (42 điều kiện):
  1. [Dòng 143] if (message == '0') {
  2. [Dòng 196] item.id == element.id ? element['active'] = true : element['active'] = false
  3. [Dòng 200] item.display_cvv == true ? this.flagToken = true : this.flagToken = false
  4. [Dòng 241] if (result == 'success') {
  5. [Dòng 246] if (this.tokenList.length == 0) {
  6. [Dòng 250] } else if (result == 'error') {
  7. [Dòng 259] _val.value.trim().length == 4) || (item.brand_id != 'amex'
  8. [Dòng 259] _val.value.trim().length == 3))
  9. [Dòng 268] _val.value.length == 4) || (item.brand_id != 'amex'
  10. [Dòng 268] _val.value.length == 3)));
  11. [Dòng 274] id == 'amex' ? this.maxLength = 4 : this.maxLength = 3
  12. [Dòng 320] if (_re.body.state == 'more_info_required') {
  13. [Dòng 335] if (this._res_post.state == 'approved'
  14. [Dòng 335] this._res_post.state == 'failed') {
  15. [Dòng 342] if (this._res_post.state == 'failed') {
  16. [Dòng 358] } else if (this._res_post.state == 'authorization_required') {
  17. [Dòng 359] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  18. [Dòng 371] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  19. [Dòng 387] } else if (_re.body.state == 'authorization_required') {
  20. [Dòng 404] if (this._b == 1
  21. [Dòng 404] this._b == 14
  22. [Dòng 404] this._b == 15
  23. [Dòng 404] this._b == 24
  24. [Dòng 404] this._b == 8
  25. [Dòng 404] this._b == 10
  26. [Dòng 404] this._b == 20
  27. [Dòng 404] this._b == 22
  28. [Dòng 404] this._b == 23
  29. [Dòng 404] this._b == 30
  30. [Dòng 404] this._b == 11
  31. [Dòng 404] this._b == 17
  32. [Dòng 404] this._b == 18
  33. [Dòng 404] this._b == 27
  34. [Dòng 404] this._b == 5
  35. [Dòng 404] this._b==12
  36. [Dòng 404] this._b == 9) {
  37. [Dòng 463] } else if (_re.body.state == 'failed') {
  38. [Dòng 508] if (action == 'blur') {
  39. [Dòng 516] this._i_token_otp.trim().length == 4)
  40. [Dòng 517] this._i_token_otp.trim().length == 3));
  41. [Dòng 570] if (_re.status == '200'
  42. [Dòng 570] _re.status == '201') {

!== (1 điều kiện):
  1. [Dòng 186] this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {

!= (20 điều kiện):
  1. [Dòng 89] this._res.merchant.id != 'OPTEST' ? this._res.on_off_bank : ''
  2. [Dòng 136] if (params['locale'] != null) {
  3. [Dòng 258] if (_val.value != null
  4. [Dòng 267] this.c_token_otp_csc = !(_val.value != null
  5. [Dòng 337] if (this._res_post.return_url != null) {
  6. [Dòng 339] } else if (this._res_post.links != null
  7. [Dòng 339] this._res_post.links.merchant_return != null
  8. [Dòng 339] this._res_post.links.merchant_return.href != null) {
  9. [Dòng 392] if (_re.body.authorization != null
  10. [Dòng 392] _re.body.authorization.links != null
  11. [Dòng 399] if (_re.body.links != null
  12. [Dòng 399] _re.body.links.cancel != null) {
  13. [Dòng 465] if (_re.body.return_url != null) {
  14. [Dòng 467] } else if (_re.body.links != null
  15. [Dòng 467] _re.body.links.merchant_return != null
  16. [Dòng 467] _re.body.links.merchant_return.href != null) {
  17. [Dòng 499] return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
  18. [Dòng 504] if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(item.brand_id) != -1
  19. [Dòng 516] return this._i_token_otp != null
  20. [Dòng 517] || (id != 'amex'

================================================================================

📁 FILE 46: overlay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/overlay/overlay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 47: overlay.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/overlay/overlay.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 48: overlay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/overlay/overlay.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 49: bank-amount.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/pipe/bank-amount.pipe.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 11] return ((a.id == id
  2. [Dòng 11] a.code == id) && a.type.includes(type));

================================================================================

📁 FILE 50: close-dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/services/close-dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 51: data.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/services/data.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 72] const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
  2. [Dòng 72] item.method === method) : null;

================================================================================

📁 FILE 52: dialog.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/services/dialog.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 53: fee.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/services/fee.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 54: focus-input.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/services/focus-input.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 55: multiple_method.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/services/multiple_method.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 56: payment.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/services/payment.service.ts
📊 Thống kê: 8 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 8 lần
--------------------------------------------------------------------------------

!= (8 điều kiện):
  1. [Dòng 106] if (idInvoice != null
  2. [Dòng 106] idInvoice != 0)
  3. [Dòng 116] idInvoice != 0) {
  4. [Dòng 253] if (this._merchantid != null
  5. [Dòng 253] this._tranref != null
  6. [Dòng 253] this._state != null
  7. [Dòng 324] let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
  8. [Dòng 357] urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');

================================================================================

📁 FILE 57: token-main.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/services/token-main.service.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 58: index.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/translate/index.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 59: lang-en.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/translate/lang-en.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 60: lang-vi.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/translate/lang-vi.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 61: translate.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/translate/translate.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 62: translate.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/translate/translate.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 37] if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
  2. [Dòng 38] else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';

================================================================================

📁 FILE 63: translations.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/translate/translations.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 64: apps-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/util/apps-info.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 149] if (e.name == bankSwift) { // TODO: get by swift

================================================================================

📁 FILE 65: banks-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/util/banks-info.ts
📊 Thống kê: 3 điều kiện duy nhất
   - === : 0 lần
   - == : 3 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (3 điều kiện):
  1. [Dòng 1013] if (+e.id == bankId) {
  2. [Dòng 1055] if (parseInt(b) == number) {
  3. [Dòng 1077] if (e.swiftCode == bankSwift) {

================================================================================

📁 FILE 66: util.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/app/util/util.ts
📊 Thống kê: 24 điều kiện duy nhất
   - === : 11 lần
   - == : 8 lần
   - !== : 3 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 64] if (v.length === 2
  2. [Dòng 64] this.flag.length === 3
  3. [Dòng 64] this.flag.charAt(this.flag.length - 1) === '/') {
  4. [Dòng 68] if (v.length === 1) {
  5. [Dòng 70] } else if (v.length === 2) {
  6. [Dòng 73] v.length === 2) {
  7. [Dòng 81] if (len === 2) {
  8. [Dòng 153] if (M[1] === 'Chrome') {
  9. [Dòng 278] if (param === key) {
  10. [Dòng 401] pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
  11. [Dòng 405] target === 0

== (8 điều kiện):
  1. [Dòng 14] if (temp.length == 0) {
  2. [Dòng 21] return (counter % 10 == 0);
  3. [Dòng 127] if (this.checkCount == 1) {
  4. [Dòng 139] if (results == null) {
  5. [Dòng 172] if (c.length == 3) {
  6. [Dòng 185] d = d == undefined ? '.' : d
  7. [Dòng 186] t = t == undefined ? '
  8. [Dòng 266] return results == null ? null : results[1]

!== (3 điều kiện):
  1. [Dòng 273] queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
  2. [Dòng 274] if (queryString !== '') {
  3. [Dòng 405] if (target !== 0

!= (2 điều kiện):
  1. [Dòng 155] if (tem != null) {
  2. [Dòng 160] if ((tem = ua.match(/version\/(\d+)/i)) != null) {

================================================================================

📁 FILE 67: qrcode.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/assets/script/qrcode.js
📊 Thống kê: 67 điều kiện duy nhất
   - === : 2 lần
   - == : 53 lần
   - !== : 0 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 2255] if (typeof define === 'function'
  2. [Dòng 2257] } else if (typeof exports === 'object') {

== (53 điều kiện):
  1. [Dòng 68] if (_dataCache == null) {
  2. [Dòng 85] if ( (0 <= r && r <= 6 && (c == 0
  3. [Dòng 85] c == 6) )
  4. [Dòng 86] || (0 <= c && c <= 6 && (r == 0
  5. [Dòng 86] r == 6) )
  6. [Dòng 107] if (i == 0
  7. [Dòng 122] _modules[r][6] = (r % 2 == 0);
  8. [Dòng 129] _modules[6][c] = (c % 2 == 0);
  9. [Dòng 152] if (r == -2
  10. [Dòng 152] r == 2
  11. [Dòng 152] c == -2
  12. [Dòng 152] c == 2
  13. [Dòng 153] || (r == 0
  14. [Dòng 153] c == 0) ) {
  15. [Dòng 169] ( (bits >> i) & 1) == 1);
  16. [Dòng 226] if (col == 6) col -= 1;
  17. [Dòng 232] if (_modules[row][col - c] == null) {
  18. [Dòng 237] dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
  19. [Dòng 249] if (bitIndex == -1) {
  20. [Dòng 458] margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
  21. [Dòng 498] if (typeof arguments[0] == 'object') {
  22. [Dòng 587] margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
  23. [Dòng 733] if (b == -1) throw 'eof';
  24. [Dòng 741] if (b0 == -1) break;
  25. [Dòng 767] if (typeof b == 'number') {
  26. [Dòng 768] if ( (b & 0xff) == b) {
  27. [Dòng 910] return function(i, j) { return (i + j) % 2 == 0
  28. [Dòng 912] return function(i, j) { return i % 2 == 0
  29. [Dòng 914] return function(i, j) { return j % 3 == 0
  30. [Dòng 916] return function(i, j) { return (i + j) % 3 == 0
  31. [Dòng 918] return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
  32. [Dòng 920] return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
  33. [Dòng 922] return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
  34. [Dòng 924] return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
  35. [Dòng 1011] if (r == 0
  36. [Dòng 1011] c == 0) {
  37. [Dòng 1015] if (dark == qrcode.isDark(row + r, col + c) ) {
  38. [Dòng 1036] if (count == 0
  39. [Dòng 1036] count == 4) {
  40. [Dòng 1149] if (typeof num.length == 'undefined') {
  41. [Dòng 1155] num[offset] == 0) {
  42. [Dòng 1495] if (typeof rsBlock == 'undefined') {
  43. [Dòng 1538] return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
  44. [Dòng 1543] _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
  45. [Dòng 1599] if (data.length - i == 1) {
  46. [Dòng 1601] } else if (data.length - i == 2) {
  47. [Dòng 1866] } else if (n == 62) {
  48. [Dòng 1868] } else if (n == 63) {
  49. [Dòng 1928] if (_buflen == 0) {
  50. [Dòng 1937] if (c == '=') {
  51. [Dòng 1961] } else if (c == 0x2b) {
  52. [Dòng 1963] } else if (c == 0x2f) {
  53. [Dòng 2133] if (table.size() == (1 << bitLength) ) {

!= (12 điều kiện):
  1. [Dòng 119] if (_modules[r][6] != null) {
  2. [Dòng 126] if (_modules[6][c] != null) {
  3. [Dòng 144] if (_modules[row][col] != null) {
  4. [Dòng 365] while (buffer.getLengthInBits() % 8 != 0) {
  5. [Dòng 750] if (count != numChars) {
  6. [Dòng 751] throw count + ' != ' + numChars
  7. [Dòng 878] while (data != 0) {
  8. [Dòng 1733] if (test.length != 2
  9. [Dòng 1733] ( (test[0] << 8) | test[1]) != code) {
  10. [Dòng 1894] if (_length % 3 != 0) {
  11. [Dòng 2067] if ( (data >>> length) != 0) {
  12. [Dòng 2178] return typeof _map[key] != 'undefined'

================================================================================

📁 FILE 68: environment.dev.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/environments/environment.dev.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 69: environment.mtf.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/environments/environment.mtf.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 70: environment.prod.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/environments/environment.prod.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 71: environment.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/environments/environment.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 72: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/acc_bidv/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 73: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/acc_bidv/script.js
📊 Thống kê: 5 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 9] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 12] if (inName.value === "") return err("MISSING_FIELD"
  3. [Dòng 21] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 23] if (e !== null) {
  2. [Dòng 55] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 74: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/acc_oceanbank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 75: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/acc_oceanbank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 10] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 19] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 21] if (e !== null) {
  2. [Dòng 53] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 76: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/acc_pvcombank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 77: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/acc_pvcombank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 7] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 16] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 18] if (e !== null) {
  2. [Dòng 50] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 78: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/acc_shb/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 79: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/acc_shb/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 10] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 19] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 21] if (e !== null) {
  2. [Dòng 53] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 80: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/acc_tpbank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 81: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/acc_tpbank/script.js
📊 Thống kê: 4 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 7] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 16] if ("PAY" === operation) {

!== (2 điều kiện):
  1. [Dòng 18] if (e !== null) {
  2. [Dòng 50] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 82: common.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/common.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 89] if (i % 2 === parity) d *= 2;
  2. [Dòng 93] return (sum % 10) === 0
  3. [Dòng 163] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 166] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 252] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 258] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 274] if (xhr.status === 200
  8. [Dòng 274] xhr.status === 201) {
  9. [Dòng 277] if (invoiceState === "unpaid"
  10. [Dòng 277] invoiceState === "not_paid") {
  11. [Dòng 282] if (paymentState === "authorization_required") {
  12. [Dòng 288] if (method === "REDIRECT") {
  13. [Dòng 291] } else if (method === "POST_REDIRECT") {
  14. [Dòng 330] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 141] this.oldValue !== this.value) {
  2. [Dòng 313] if (jLinks !== undefined
  3. [Dòng 313] jLinks !== null) {
  4. [Dòng 315] if (jMerchantReturn !== undefined
  5. [Dòng 315] jMerchantReturn !== null) {
  6. [Dòng 330] if (responseCode !== undefined
  7. [Dòng 330] responseCode !== null
  8. [Dòng 358] if (parentRes !== "{

================================================================================

📁 FILE 83: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/domestic_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 84: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/domestic_card/script.js
📊 Thống kê: 14 điều kiện duy nhất
   - === : 11 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 18] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 60] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 63] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 72] year === y
  5. [Dòng 74] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 106] if (inPhone.value === "") return err("MISSING_FIELD"
  7. [Dòng 136] if ("PAY" === operation) {
  8. [Dòng 525] } else if (value === "") {
  9. [Dòng 542] if (trPhone.style.display === "") {
  10. [Dòng 544] } else if (trName.style.display === "") {
  11. [Dòng 565] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 138] if (e !== null) {
  2. [Dòng 404] if (lang !== "vi") lang = "en";
  3. [Dòng 470] if (value !== "") {

================================================================================

📁 FILE 85: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/domestic_token/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 86: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/domestic_token/script.js
📊 Thống kê: 25 điều kiện duy nhất
   - === : 23 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (23 điều kiện):
  1. [Dòng 20] if ("PAY" === operation) {
  2. [Dòng 93] if (trName.style.display === "") {
  3. [Dòng 117] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  4. [Dòng 123] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  5. [Dòng 137] if (xhr.status === 200) {
  6. [Dòng 165] if (insType === "card") {
  7. [Dòng 166] if (insBrandId === "visa"
  8. [Dòng 166] insBrandId === "mastercard"
  9. [Dòng 166] insBrandId === "amex"
  10. [Dòng 166] insBrandId === "jcb"
  11. [Dòng 166] insBrandId === "cup") {
  12. [Dòng 170] } else if (insBrandId === "atm") {
  13. [Dòng 241] } else if (insType === "dongabank_account") {
  14. [Dòng 242] } else if (insType === "techcombank_account") {
  15. [Dòng 243] } else if (insType === "vib_account") {
  16. [Dòng 244] } else if (insType === "bidv_account") {
  17. [Dòng 245] } else if (insType === "tpbank_account") {
  18. [Dòng 246] } else if (insType === "shb_account") {
  19. [Dòng 247] } else if (insType === "shb_customer_id") {
  20. [Dòng 248] } else if (insType === "vpbank_account") {
  21. [Dòng 249] } else if (insType === "oceanbank_online_account") {
  22. [Dòng 250] } else if (insType === "oceanbank_mobile_account") {
  23. [Dòng 251] } else if (insType === "pvcombank_account") {

!== (2 điều kiện):
  1. [Dòng 54] if (lang !== "vi") lang = "en";
  2. [Dòng 273] if (parentRes !== "{

================================================================================

📁 FILE 87: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 88: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/international_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 89: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/international_card/script.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 13] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 23] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 26] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 38] year === y
  5. [Dòng 40] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 71] if ("PAY" === operation) {
  7. [Dòng 161] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 73] if (e !== null) {
  2. [Dòng 118] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 90: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/international_token/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 91: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/international_token/script.js
📊 Thống kê: 36 điều kiện duy nhất
   - === : 25 lần
   - == : 0 lần
   - !== : 11 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (25 điều kiện):
  1. [Dòng 32] year === y
  2. [Dòng 34] if (inCvv.value === "") {
  3. [Dòng 67] if ("PAY" === operation) {
  4. [Dòng 129] } else if (value === "") {
  5. [Dòng 190] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 196] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 209] if (xhr.status === 200) {
  8. [Dòng 245] if (insType === "card") {
  9. [Dòng 246] if (insBrandId === "visa"
  10. [Dòng 246] insBrandId === "mastercard"
  11. [Dòng 246] insBrandId === "amex"
  12. [Dòng 246] insBrandId === "jcb"
  13. [Dòng 246] insBrandId === "cup") {
  14. [Dòng 248] } else if (insBrandId === "atm") {
  15. [Dòng 319] } else if (insType === "dongabank_account") {
  16. [Dòng 320] } else if (insType === "techcombank_account") {
  17. [Dòng 321] } else if (insType === "vib_account") {
  18. [Dòng 322] } else if (insType === "bidv_account") {
  19. [Dòng 323] } else if (insType === "tpbank_account") {
  20. [Dòng 324] } else if (insType === "shb_account") {
  21. [Dòng 325] } else if (insType === "shb_customer_id") {
  22. [Dòng 326] } else if (insType === "vpbank_account") {
  23. [Dòng 327] } else if (insType === "oceanbank_online_account") {
  24. [Dòng 328] } else if (insType === "oceanbank_mobile_account") {
  25. [Dòng 329] } else if (insType === "pvcombank_account") {

!== (11 điều kiện):
  1. [Dòng 18] if (inMonth.value !== ""
  2. [Dòng 21] if (inYear.value !== ""
  3. [Dòng 23] var month = inMonth.value !== ""
  4. [Dòng 24] var year = parseInt("20" + (inYear.value !== ""
  5. [Dòng 35] if ("2D" !== order["vpc_VerType"]) return err("MISSING_FIELD"
  6. [Dòng 71] if (e !== null) {
  7. [Dòng 79] inMonth.value !== tokenInsMonth) instrument["month"] = inMonth.value;
  8. [Dòng 80] inYear.value !== tokenInsYear) instrument["year"] = inYear.value;
  9. [Dòng 81] if (inCvv.value !== "") instrument["cvv"] = inCvv.value;
  10. [Dòng 95] if (lang !== "vi") lang = "en";
  11. [Dòng 351] if (parentRes !== "{

================================================================================

📁 FILE 92: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/amway/script.js
📊 Thống kê: 54 điều kiện duy nhất
   - === : 41 lần
   - == : 1 lần
   - !== : 12 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (41 điều kiện):
  1. [Dòng 4] if ((cardno.length === 15
  2. [Dòng 4] cardno.length === 16
  3. [Dòng 4] cardno.length === 19) && isMode10(cardno) === true) {
  4. [Dòng 4] isMode10(cardno) === true) {
  5. [Dòng 23] if (i % 2 === parity) d *= 2;
  6. [Dòng 27] return (sum % 10) === 0
  7. [Dòng 48] if ("PAY" === operation) {
  8. [Dòng 69] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  9. [Dòng 75] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  10. [Dòng 91] if (xhr.status === 200
  11. [Dòng 91] xhr.status === 201) {
  12. [Dòng 94] if (invoiceState === "unpaid"
  13. [Dòng 94] invoiceState === "not_paid") {
  14. [Dòng 99] if (paymentState === "authorization_required") {
  15. [Dòng 105] if (method === "REDIRECT") {
  16. [Dòng 110] } else if (method === "POST_REDIRECT") {
  17. [Dòng 223] if (typeof queryParams[key] === "undefined") {
  18. [Dòng 226] } else if (typeof queryParams[key] === "string") {
  19. [Dòng 256] if (params["CardList"] === undefined
  20. [Dòng 256] params["CardList"] === null) return;
  21. [Dòng 302] if (xhr.status === 200) {
  22. [Dòng 312] if (insType === "card") {
  23. [Dòng 313] if (insBrandId === "visa"
  24. [Dòng 313] insBrandId === "mastercard"
  25. [Dòng 313] insBrandId === "amex"
  26. [Dòng 313] insBrandId === "jcb"
  27. [Dòng 313] insBrandId === "cup") {
  28. [Dòng 317] } else if (insBrandId === "atm") {
  29. [Dòng 318] if (insSwiftCode === "BFTVVNVX") { //Vietcombank
  30. [Dòng 322] } else if (insSwiftCode === "BIDVVNVX") { //BIDV
  31. [Dòng 329] } else if (insType === "dongabank_account") {
  32. [Dòng 331] } else if (insType === "techcombank_account") {
  33. [Dòng 333] } else if (insType === "vib_account") {
  34. [Dòng 335] } else if (insType === "bidv_account") {
  35. [Dòng 336] } else if (insType === "tpbank_account") {
  36. [Dòng 337] } else if (insType === "shb_account") {
  37. [Dòng 338] } else if (insType === "shb_customer_id") {
  38. [Dòng 339] } else if (insType === "vpbank_account") {
  39. [Dòng 340] } else if (insType === "oceanbank_online_account") {
  40. [Dòng 341] } else if (insType === "oceanbank_mobile_account") {
  41. [Dòng 342] } else if (insType === "pvcombank_account") {

== (1 điều kiện):
  1. [Dòng 270] if (cardList.length == 1) {//TOKEN, BIDV, SHB, OCEAN, PVCOM, TP Bank

!== (12 điều kiện):
  1. [Dòng 52] if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
  2. [Dòng 53] if (inNumber.style.display !== "none") instrument.number = inNumber.value;
  3. [Dòng 54] if (inDate.style.display !== "none") instrument.date = inDate.value;
  4. [Dòng 55] if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
  5. [Dòng 56] if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
  6. [Dòng 57] if (inName.style.display !== "none") instrument.name = inName.value;
  7. [Dòng 132] if (jLinks !== undefined
  8. [Dòng 132] jLinks !== null) {
  9. [Dòng 134] if (jMerchantReturn !== undefined
  10. [Dòng 134] jMerchantReturn !== null) {
  11. [Dòng 166] if (parentRes !== "{
  12. [Dòng 255] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 93: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Bidv/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 94: bidv.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Bidv/assets/js/bidv.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 233] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 239] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 255] if (xhr.status === 200
  8. [Dòng 255] xhr.status === 201) {
  9. [Dòng 258] if (invoiceState === "unpaid"
  10. [Dòng 258] invoiceState === "not_paid") {
  11. [Dòng 263] if (paymentState === "authorization_required") {
  12. [Dòng 269] if (method === "REDIRECT") {
  13. [Dòng 272] } else if (method === "POST_REDIRECT") {
  14. [Dòng 311] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 294] if (jLinks !== undefined
  3. [Dòng 294] jLinks !== null) {
  4. [Dòng 296] if (jMerchantReturn !== undefined
  5. [Dòng 296] jMerchantReturn !== null) {
  6. [Dòng 311] if (responseCode !== undefined
  7. [Dòng 311] responseCode !== null
  8. [Dòng 339] if (parentRes !== "{

================================================================================

📁 FILE 95: bidv2.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Bidv/bidv2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 76] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 88] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 78] if ( $('.circle_v1').css('display') == 'block'
  2. [Dòng 112] if ($('.circle_v2').css('display') == 'block'
  3. [Dòng 112] $('.circle_v3').css('display') == 'block' ) {
  4. [Dòng 302] $('.circle_v1').css('display') == 'block'

!== (3 điều kiện):
  1. [Dòng 90] if (e !== null) {
  2. [Dòng 273] if (lang !== "vi") lang = "en";
  3. [Dòng 305] if (value !== "") {

================================================================================

📁 FILE 96: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Bidv/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 97: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Ocean/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 98: ocean.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Ocean/assets/js/ocean.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 232] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 237] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 251] if (xhr.status === 200
  8. [Dòng 251] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 257] if (paymentState === "authorization_required") {
  12. [Dòng 261] if (method === "REDIRECT") {
  13. [Dòng 264] } else if (method === "POST_REDIRECT") {
  14. [Dòng 303] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 286] if (jLinks !== undefined
  3. [Dòng 286] jLinks !== null) {
  4. [Dòng 288] if (jMerchantReturn !== undefined
  5. [Dòng 288] jMerchantReturn !== null) {
  6. [Dòng 303] if (responseCode !== undefined
  7. [Dòng 303] responseCode !== null
  8. [Dòng 331] if (parentRes !== "{

================================================================================

📁 FILE 99: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Ocean/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 100: ocean2.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Ocean/ocean2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 72] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 92] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 74] document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM'
  2. [Dòng 116] if ( document.getElementsByClassName("select_online")[0].value == 'Easy Online Banking') {
  3. [Dòng 119] if ( document.getElementsByClassName("select_online")[0].value == 'Easy Mobile Banking') {
  4. [Dòng 304] if ( document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM') {

!== (3 điều kiện):
  1. [Dòng 94] if (e !== null) {
  2. [Dòng 276] if (lang !== "vi") lang = "en";
  3. [Dòng 306] if (value !== "") {

================================================================================

📁 FILE 101: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Pv_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 102: pvbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Pv_Bank/assets/js/pvbank.js
📊 Thống kê: 23 điều kiện duy nhất
   - === : 14 lần
   - == : 1 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 239] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 261] if (xhr.status === 200
  8. [Dòng 261] xhr.status === 201) {
  9. [Dòng 264] if (invoiceState === "unpaid"
  10. [Dòng 264] invoiceState === "not_paid") {
  11. [Dòng 269] if (paymentState === "authorization_required") {
  12. [Dòng 275] if (method === "REDIRECT") {
  13. [Dòng 278] } else if (method === "POST_REDIRECT") {
  14. [Dòng 317] responseCode === "0") {

== (1 điều kiện):
  1. [Dòng 167] if ($('.circle_v1').css('display') == 'block') {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 300] if (jLinks !== undefined
  3. [Dòng 300] jLinks !== null) {
  4. [Dòng 302] if (jMerchantReturn !== undefined
  5. [Dòng 302] jMerchantReturn !== null) {
  6. [Dòng 317] if (responseCode !== undefined
  7. [Dòng 317] responseCode !== null
  8. [Dòng 345] if (parentRes !== "{

================================================================================

📁 FILE 103: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Pv_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 104: pvbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Pv_Bank/pvbank2.js
📊 Thống kê: 15 điều kiện duy nhất
   - === : 8 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 71] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 76] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 79] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 88] year === y
  5. [Dòng 90] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 100] if ("PAY" === operation) {
  7. [Dòng 347] } else if (value === "") {
  8. [Dòng 364] if (trName.style.display === "") {

== (4 điều kiện):
  1. [Dòng 73] if ($('.circle_v1').css('display') == 'block'
  2. [Dòng 75] $('.circle_v1').css('display') == 'block'
  3. [Dòng 124] if ( $('.circle_v1').css('display') == 'block') {
  4. [Dòng 129] else if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 102] if (e !== null) {
  2. [Dòng 286] if (lang !== "vi") lang = "en";
  3. [Dòng 316] if (value !== "") {

================================================================================

📁 FILE 105: Sea2.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/SeaBank/Sea2.js
📊 Thống kê: 11 điều kiện duy nhất
   - === : 8 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 23] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 31] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 34] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 43] year === y
  5. [Dòng 45] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 55] if ("PAY" === operation) {
  7. [Dòng 287] } else if (value === "") {
  8. [Dòng 304] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 57] if (e !== null) {
  2. [Dòng 233] if (lang !== "vi") lang = "en";
  3. [Dòng 263] if (value !== "") {

================================================================================

📁 FILE 106: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/SeaBank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 107: Sea.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/SeaBank/assets/js/Sea.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 228] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 234] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 258] if (paymentState === "authorization_required") {
  12. [Dòng 264] if (method === "REDIRECT") {
  13. [Dòng 267] } else if (method === "POST_REDIRECT") {
  14. [Dòng 306] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 289] if (jLinks !== undefined
  3. [Dòng 289] jLinks !== null) {
  4. [Dòng 291] if (jMerchantReturn !== undefined
  5. [Dòng 291] jMerchantReturn !== null) {
  6. [Dòng 306] if (responseCode !== undefined
  7. [Dòng 306] responseCode !== null
  8. [Dòng 334] if (parentRes !== "{

================================================================================

📁 FILE 108: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/SeaBank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 109: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Shb/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 110: shb.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Shb/assets/js/shb.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 234] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 240] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 256] if (xhr.status === 200
  8. [Dòng 256] xhr.status === 201) {
  9. [Dòng 259] if (invoiceState === "unpaid"
  10. [Dòng 259] invoiceState === "not_paid") {
  11. [Dòng 264] if (paymentState === "authorization_required") {
  12. [Dòng 270] if (method === "REDIRECT") {
  13. [Dòng 273] } else if (method === "POST_REDIRECT") {
  14. [Dòng 312] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 295] if (jLinks !== undefined
  3. [Dòng 295] jLinks !== null) {
  4. [Dòng 297] if (jMerchantReturn !== undefined
  5. [Dòng 297] jMerchantReturn !== null) {
  6. [Dòng 312] if (responseCode !== undefined
  7. [Dòng 312] responseCode !== null
  8. [Dòng 340] if (parentRes !== "{

================================================================================

📁 FILE 111: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Shb/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 112: shb2.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Shb/shb2.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 2 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 73] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 85] if ("PAY" === operation) {

== (4 điều kiện):
  1. [Dòng 74] if ($('.circle_v2').css('display') == 'block'
  2. [Dòng 110] if ($('.circle_v1').css('display') == 'block'
  3. [Dòng 114] if ( $('.circle_v3').css('display') == 'block' ) {
  4. [Dòng 299] if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 87] if (e !== null) {
  2. [Dòng 271] if (lang !== "vi") lang = "en";
  3. [Dòng 301] if (value !== "") {

================================================================================

📁 FILE 113: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Tp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 114: tpbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Tp_Bank/assets/js/tpbank.js
📊 Thống kê: 23 điều kiện duy nhất
   - === : 14 lần
   - == : 1 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 239] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 261] if (xhr.status === 200
  8. [Dòng 261] xhr.status === 201) {
  9. [Dòng 264] if (invoiceState === "unpaid"
  10. [Dòng 264] invoiceState === "not_paid") {
  11. [Dòng 269] if (paymentState === "authorization_required") {
  12. [Dòng 275] if (method === "REDIRECT") {
  13. [Dòng 278] } else if (method === "POST_REDIRECT") {
  14. [Dòng 317] responseCode === "0") {

== (1 điều kiện):
  1. [Dòng 167] if ($('.circle_v1').css('display') == 'block') {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 300] if (jLinks !== undefined
  3. [Dòng 300] jLinks !== null) {
  4. [Dòng 302] if (jMerchantReturn !== undefined
  5. [Dòng 302] jMerchantReturn !== null) {
  6. [Dòng 317] if (responseCode !== undefined
  7. [Dòng 317] responseCode !== null
  8. [Dòng 345] if (parentRes !== "{

================================================================================

📁 FILE 115: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Tp_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 116: tpbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Tp_Bank/tpbank2.js
📊 Thống kê: 15 điều kiện duy nhất
   - === : 8 lần
   - == : 4 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 71] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 78] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 81] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 90] year === y
  5. [Dòng 92] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 102] if ("PAY" === operation) {
  7. [Dòng 349] } else if (value === "") {
  8. [Dòng 366] if (trName.style.display === "") {

== (4 điều kiện):
  1. [Dòng 73] if ($('.circle_v1').css('display') == 'block'
  2. [Dòng 76] $('.circle_v1').css('display') == 'block'
  3. [Dòng 126] if ( $('.circle_v1').css('display') == 'block') {
  4. [Dòng 131] else if ($('.circle_v2').css('display') == 'block') {

!== (3 điều kiện):
  1. [Dòng 104] if (e !== null) {
  2. [Dòng 288] if (lang !== "vi") lang = "en";
  3. [Dòng 318] if (value !== "") {

================================================================================

📁 FILE 117: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Visa/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 118: onepay.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Visa/assets/js/onepay.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 78] if (i % 2 === parity) d *= 2;
  2. [Dòng 82] return (sum % 10) === 0
  3. [Dòng 152] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 155] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 240] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 245] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 259] if (xhr.status === 200
  8. [Dòng 259] xhr.status === 201) {
  9. [Dòng 261] if (invoiceState === "unpaid"
  10. [Dòng 261] invoiceState === "not_paid") {
  11. [Dòng 265] if (paymentState === "authorization_required") {
  12. [Dòng 269] if (method === "REDIRECT") {
  13. [Dòng 272] } else if (method === "POST_REDIRECT") {
  14. [Dòng 311] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 130] this.oldValue !== this.value) {
  2. [Dòng 294] if (jLinks !== undefined
  3. [Dòng 294] jLinks !== null) {
  4. [Dòng 296] if (jMerchantReturn !== undefined
  5. [Dòng 296] jMerchantReturn !== null) {
  6. [Dòng 311] if (responseCode !== undefined
  7. [Dòng 311] responseCode !== null
  8. [Dòng 339] if (parentRes !== "{

================================================================================

📁 FILE 119: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Visa/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 120: index.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Visa/index.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 19] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 29] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 32] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 44] year === y
  5. [Dòng 46] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 77] if ("PAY" === operation) {
  7. [Dòng 168] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 79] if (e !== null) {
  2. [Dòng 125] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 121: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Vp_Bank/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 122: vpbank.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Vp_Bank/assets/js/vpbank.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 230] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 236] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 252] if (xhr.status === 200
  8. [Dòng 252] xhr.status === 201) {
  9. [Dòng 255] if (invoiceState === "unpaid"
  10. [Dòng 255] invoiceState === "not_paid") {
  11. [Dòng 260] if (paymentState === "authorization_required") {
  12. [Dòng 266] if (method === "REDIRECT") {
  13. [Dòng 269] } else if (method === "POST_REDIRECT") {
  14. [Dòng 308] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 291] if (jLinks !== undefined
  3. [Dòng 291] jLinks !== null) {
  4. [Dòng 293] if (jMerchantReturn !== undefined
  5. [Dòng 293] jMerchantReturn !== null) {
  6. [Dòng 308] if (responseCode !== undefined
  7. [Dòng 308] responseCode !== null
  8. [Dòng 336] if (parentRes !== "{

================================================================================

📁 FILE 123: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Vp_Bank/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 124: vpbank2.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/Vp_Bank/vpbank2.js
📊 Thống kê: 14 điều kiện duy nhất
   - === : 11 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (11 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 32] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 35] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 44] year === y
  5. [Dòng 46] if (inPhone.value === "") return err("MISSING_FIELD"
  6. [Dòng 49] if (inName.value === "") return err("MISSING_FIELD"
  7. [Dòng 59] if ("PAY" === operation) {
  8. [Dòng 292] } else if (value === "") {
  9. [Dòng 309] if (trPhone.style.display === "") {
  10. [Dòng 311] } else if (trName.style.display === "") {
  11. [Dòng 332] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 61] if (e !== null) {
  2. [Dòng 237] if (lang !== "vi") lang = "en";
  3. [Dòng 267] if (value !== "") {

================================================================================

📁 FILE 125: sha.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/assets/js/sha.js
📊 Thống kê: 49 điều kiện duy nhất
   - === : 38 lần
   - == : 0 lần
   - !== : 11 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (38 điều kiện):
  1. [Dòng 12] 1>t)throw Error("numRounds must a integer >= 1");if("SHA-1"===c)m=512,q=K,u=Z,f=160,r=function(a){return a.slice()};else if(0===c.lastIndexOf("SHA-",0))if(q=function(a,b){return L(a,b,c)
  2. [Dòng 12] c)},u=function(a,b,h,e){var k,f;if("SHA-224"===c
  3. [Dòng 12] "SHA-256"===c)k=(b+65>>>9<<4)+15,f=16;else if("SHA-384"===c
  4. [Dòng 12] "SHA-512"===c)k=(b+129>>>10<<
  5. [Dòng 13] c);if("SHA-224"===c)a=[e[0],e[1],e[2],e[3],e[4],e[5],e[6]];else if("SHA-256"===c)a=e;else if("SHA-384"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,e[4].b,e[5].a,e[5].b];else if("SHA-512"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,
  6. [Dòng 14] "SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)
  7. [Dòng 14] 0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
  8. [Dòng 14] return a},r=function(a){return a.slice()},"SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)||0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
  9. [Dòng 15] c)m=1152,f=224;else if("SHA3-256"===c)m=1088,f=256;else if("SHA3-384"===c)m=832,f=384;else if("SHA3-512"===c)m=576,f=512;else if("SHAKE128"===c)m=1344,f=-1,F=31,z=!0;else if("SHAKE256"===c)m=1088,f=-1,F=31,z=!0;else throw Error("Chosen SHA variant is not supported");u=function(a
  10. [Dòng 16] 0===64*l%e
  11. [Dòng 16] 5][l/5|0];g.push(a.b);if(32*g.length>=h)break;g.push(a.a);l+=1;0===64*l%e&&D(null,b)}return g}}else throw Error("Chosen SHA variant is not supported");d=M(a,g,x);l=A(c);this.setHMACKey=function(a,b,h){var k;if(!0===I)throw Error("HMAC key already set");if(!0===y)throw Error("Cannot set HMAC key after calling update");if(!0===z)throw Error("SHAKE is not supported for HMAC");g=(h
  12. [Dòng 17] f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
  13. [Dòng 17] )b.push(0);b[h]&=4294967040}for(a=0;a<=h;a+=1)v[a]=b[a]^909522486,w[a]=b[a]^1549556828;l=q(v,l);e=m;I=!0};this.update=function(a){var c,b,k,f=0,g=m>>>5;c=d(a,h,n);a=c.binLen;b=c.value;c=a>>>5;for(k=0;k<c;k+=g)f+m<=a&&(l=q(b.slice(k,k+g),l),f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
  14. [Dòng 18] for(g=1;g<t;g+=1)!0===z
  15. [Dòng 19] f);return k(m)};this.getHMAC=function(a,b){var k,g,d,p;if(!1===I)throw Error("Cannot call getHMAC without first setting HMAC key");d=N(b);switch(a){case "HEX":k=function(a){return O(a,f,x,d)};break;case "B64":k=function(a){return P(a,f,x,d)};break;case "BYTES":k=function(a){return Q(a,f,x)};break;case "ARRAYBUFFER":try{k=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}k=function(a){return R(a,f,x)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
  16. [Dòng 20] d=-1===b?3:0
  17. [Dòng 20] f=-1===b?3:0
  18. [Dòng 21] g=-1===b?3:0
  19. [Dòng 22] !0===c.hasOwnProperty("b64Pad")
  20. [Dòng 22] a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");
  21. [Dòng 23] u=-1===b?3:0
  22. [Dòng 23] l+=2){p=parseInt(a.substr(l,2),16);if(isNaN(p))throw Error("String of HEX type contains invalid characters");m=(l>>>1)+q;for(f=m>>>2;c.length<=f;)c.push(0);c[f]|=p<<8*(u+m%4*b)}return{value:c,binLen:4*g+d}};break;case "TEXT":c=function(c,h,d){var g,l,p=0,f,m,q,u,r,t;h=h||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(t=-1===
  23. [Dòng 24] m+=1){r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=l[m]<<8*(t+r%4*b);p+=1}else if("UTF16BE"===a
  24. [Dòng 24] "UTF16LE"===a)for(t=-1===b?2:0
  25. [Dòng 24] UTF16LE"===a
  26. [Dòng 24] 1===b
  27. [Dòng 25] !0===l
  28. [Dòng 25] !0===l&&(m=g&255,g=m<<8|g>>>8);r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=g<<8*(t+r%4*b);p+=2}return{value:h,binLen:8*p+d}};break;case "B64":c=function(a,c,d){var g=0,l,p,f,m,q,u,r,t;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");p=a.indexOf("=");a=a.replace(/\=/g
  29. [Dòng 25] t=-1===b?3:0
  30. [Dòng 26] q=-1===b?3:0
  31. [Dòng 27] m=-1===b?3:0
  32. [Dòng 32] c.b^a.b)}function A(c){var a=[],d;if("SHA-1"===c)a=[1732584193,4023233417,2562383102,271733878,3285377520];else if(0===c.lastIndexOf("SHA-",0))switch(a=
  33. [Dòng 34] new b(d[2],4271175723),new b(d[3],1595750129),new b(d[4],2917565137),new b(d[5],725511199),new b(d[6],4215389547),new b(d[7],327033209)];break;default:throw Error("Unknown SHA variant");}else if(0===c.lastIndexOf("SHA3-",0)
  34. [Dòng 34] 0===c.lastIndexOf("SHAKE",0))for(c=0
  35. [Dòng 36] a,k){var e,h,n,g,l,p,f,m,q,u,r,t,v,w,y,A,z,x,F,B,C,D,E=[],J;if("SHA-224"===k
  36. [Dòng 36] "SHA-256"===k)u=64,t=1,D=Number,v=G,w=la,y=H,A=ha,z=ja,x=da,F=fa,C=U,B=aa,J=d;else if("SHA-384"===k
  37. [Dòng 36] "SHA-512"===k)u=80,t=2,D=b,v=ma,w=na,y=oa,A=ia,z=ka,x=ea,F=ga,C=ca,B=ba,J=V;else throw Error("Unexpected error in SHA-2 implementation");k=a[0];e=a[1];h=a[2];n=a[3];g=a[4];l=a[5];p=a[6];f=a[7];for(r=0
  38. [Dòng 45] "function"===typeof define

!== (11 điều kiện):
  1. [Dòng 12] 'use strict';(function(Y){function C(c,a,b){var e=0,h=[],n=0,g,l,d,f,m,q,u,r,I=!1,v=[],w=[],t,y=!1,z=!1,x=-1;b=b||{};g=b.encoding||"UTF8";t=b.numRounds||1;if(t!==parseInt(t,10)
  2. [Dòng 18] 0!==f%32
  3. [Dòng 22] a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8
  4. [Dòng 23] }switch(c){case "HEX":c=function(a,c,d){var g=a.length,l,p,f,m,q,u;if(0!==g%2)throw Error("String of HEX type must be in byte increments");c=c||[0];d=d||0;q=d>>>3;u=-1===b?3:0;for(l=0
  5. [Dòng 24] 1!==b
  6. [Dòng 24] "UTF16LE"!==a
  7. [Dòng 25] "");if(-1!==p
  8. [Dòng 27] function(a,c,d){var g,l,p,f,m,q;c=c||[0];d=d||0;l=d>>>3;m=-1===b?3:0;q=new Uint8Array(a);for(g=0;g<a.byteLength;g+=1)f=g+l,p=f>>>2,c.length<=p&&c.push(0),c[p]|=q[g]<<8*(m+f%4*b);return{value:c,binLen:8*a.byteLength+d}};break;default:throw Error("format must be HEX, TEXT, B64, BYTES, or ARRAYBUFFER");}return c}function y(c,a){return c<<a|c>>>32-a}function S(c,a){return 32<a?(a-=32,new b(c.b<<a|c.a>>>32-a,c.a<<a|c.b>>>32-a)):0!==a?new b(c.a<<a|c.b>>>32-a,c.b<<a|c.a>>>32-a):c}function w(c,a){return c>>>
  9. [Dòng 37] a[7]);return a}function D(c,a){var d,e,h,n,g=[],l=[];if(null!==c)for(e=0
  10. [Dòng 45] define.amd?define(function(){return C}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=C),exports=C):Y.jsSHA=C})(this);
  11. [Dòng 45] return C}):"undefined"!==typeof exports?("undefined"!==typeof module

================================================================================

📁 FILE 126: sha256.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/assets/js/sha256.js
📊 Thống kê: 28 điều kiện duy nhất
   - === : 20 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (20 điều kiện):
  1. [Dòng 12] 1>u)throw Error("numRounds must a integer >= 1");if(0===c.lastIndexOf("SHA-",0))if(q=function(b,a){return A(b,a,c)
  2. [Dòng 12] c)},y=function(b,a,l,f){var g,e;if("SHA-224"===c
  3. [Dòng 12] "SHA-256"===c)g=(a+65>>>9<<4)+15,e=16;else throw Error("Unexpected error in SHA-2 implementation");for(
  4. [Dòng 13] c);if("SHA-224"===c)b=[f[0],f[1],f[2],f[3],f[4],f[5],f[6]];else if("SHA-256"===c)b=f;else throw Error("Unexpected error in SHA-2 implementation");return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
  5. [Dòng 13] "SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a
  6. [Dòng 13] return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
  7. [Dòng 14] if(!0===z)throw Error("Cannot set HMAC key after calling update");f=(g
  8. [Dòng 15] 5);g=a%h;z=!0};this.getHash=function(a,f){var d,h,k,q;if(!0===m)throw Error("Cannot call getHash after setting HMAC key");k=C(f);switch(a){case "HEX":d=function(a){return D(a,e,k)};break;case "B64":d=function(a){return E(a,e,k)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{h=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("format must be HEX, B64, BYTES, or ARRAYBUFFER");
  9. [Dòng 16] x(c));return d(q)};this.getHMAC=function(a,f){var d,k,t,u;if(!1===m)throw Error("Cannot call getHMAC without first setting HMAC key");t=C(f);switch(a){case "HEX":d=function(a){return D(a,e,t)};break;case "B64":d=function(a){return E(a,e,t)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{d=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
  10. [Dòng 18] !0===c.hasOwnProperty("b64Pad")
  11. [Dòng 20] h=(d>>>1)+q;for(e=h>>>2;b.length<=e;)b.push(0);b[e]|=k<<8*(3+h%4*-1)}return{value:b,binLen:4*f+c}};break;case "TEXT":d=function(c,b,d){var f,n,k=0,e,h,q,m,p,r;b=b||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(r=3
  12. [Dòng 21] q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=n[h]<<8*(r+p%4*-1);k+=1}else if("UTF16BE"===a
  13. [Dòng 21] "UTF16LE"===a)for(r=2
  14. [Dòng 21] UTF16LE"===a
  15. [Dòng 21] !0===n
  16. [Dòng 21] e+=1){f=c.charCodeAt(e);!0===n&&(h=f&255,f=h<<8|f>>>8);p=k+q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=f<<8*(r+p%4*-1);k+=2}return{value:b,binLen:8*k+d}};break;case "B64":d=function(a,b,c){var f=0,d,k,e,h,q,m,p;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");k=a.indexOf("=");a=a.replace(/\=/g
  17. [Dòng 25] 16)&65535)<<16|b&65535}function R(c,a,d,l,b){var g=(c&65535)+(a&65535)+(d&65535)+(l&65535)+(b&65535);return((c>>>16)+(a>>>16)+(d>>>16)+(l>>>16)+(b>>>16)+(g>>>16)&65535)<<16|g&65535}function x(c){var a=[],d;if(0===c.lastIndexOf("SHA-",0))switch(a=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428]
  18. [Dòng 26] new m,new m,new m,new m,new m,new m];break;case "SHA-512":a=[new m,new m,new m,new m,new m,new m,new m,new m];break;default:throw Error("Unknown SHA variant");}else throw Error("No SHA variants supported");return a}function A(c,a,d){var l,b,g,f,n,k,e,h,m,r,p,w,t,x,u,z,A,B,C,D,E,F,v=[],G;if("SHA-224"===d
  19. [Dòng 26] "SHA-256"===d)r=64,w=1,F=Number,t=P,x=Q,u=R,z=N,A=O,B=L,C=M,E=K,D=J,G=H;else throw Error("Unexpected error in SHA-2 implementation");d=a[0];l=a[1];b=a[2];g=a[3];f=a[4];n=a[5];k=a[6];e=a[7];for(p=
  20. [Dòng 29] "function"===typeof define

!== (8 điều kiện):
  1. [Dòng 12] 'use strict';(function(I){function w(c,a,d){var l=0,b=[],g=0,f,n,k,e,h,q,y,p,m=!1,t=[],r=[],u,z=!1;d=d||{};f=d.encoding||"UTF8";u=d.numRounds||1;if(u!==parseInt(u,10)
  2. [Dòng 18] l+=1)g[l]=c[l>>>2]>>>8*(3+l%4*-1)&255;return b}function C(c){var a={outputUpper:!1,b64Pad:"=",shakeLen:-1};c=c||{};a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");
  3. [Dòng 19] if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0;d<f;d+=2){k=parseInt(a.substr(d,2),16);if(isNaN(k))throw Error("String of HEX type contains invalid characters");
  4. [Dòng 19] if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0
  5. [Dòng 21] "UTF16LE"!==a
  6. [Dòng 22] "");if(-1!==k
  7. [Dòng 29] define.amd?define(function(){return w}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=w),exports=w):I.jsSHA=w})(this);
  8. [Dòng 29] return w}):"undefined"!==typeof exports?("undefined"!==typeof module

================================================================================

📁 FILE 127: slick.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/assets/libraries/slick/slick.js
📊 Thống kê: 182 điều kiện duy nhất
   - === : 126 lần
   - == : 5 lần
   - !== : 47 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (126 điều kiện):
  1. [Dòng 20] if (typeof define === 'function'
  2. [Dòng 206] if (typeof(index) === 'boolean') {
  3. [Dòng 215] if (typeof(index) === 'number') {
  4. [Dòng 216] if (index === 0
  5. [Dòng 216] _.$slides.length === 0) {
  6. [Dòng 224] if (addBefore === true) {
  7. [Dòng 249] if (_.options.slidesToShow === 1
  8. [Dòng 249] _.options.adaptiveHeight === true
  9. [Dòng 249] _.options.vertical === false) {
  10. [Dòng 264] if (_.options.rtl === true
  11. [Dòng 267] if (_.transformsEnabled === false) {
  12. [Dòng 268] if (_.options.vertical === false) {
  13. [Dòng 280] if (_.cssTransitions === false) {
  14. [Dòng 281] if (_.options.rtl === true) {
  15. [Dòng 355] typeof asNavFor === 'object' ) {
  16. [Dòng 371] if (_.options.fade === false) {
  17. [Dòng 414] if ( _.options.infinite === false ) {
  18. [Dòng 416] if ( _.direction === 1
  19. [Dòng 416] ( _.currentSlide + 1 ) === ( _.slideCount - 1 )) {
  20. [Dòng 420] else if ( _.direction === 0 ) {
  21. [Dòng 424] if ( _.currentSlide - 1 === 0 ) {
  22. [Dòng 442] if (_.options.arrows === true ) {
  23. [Dòng 487] if (_.options.dots === true
  24. [Dòng 524] _.$slideTrack = (_.slideCount === 0) ?
  25. [Dòng 532] if (_.options.centerMode === true
  26. [Dòng 532] _.options.swipeToSlide === true) {
  27. [Dòng 547] _.setSlideClasses(typeof _.currentSlide === 'number' ? _.currentSlide : 0);
  28. [Dòng 549] if (_.options.draggable === true) {
  29. [Dòng 602] if (_.respondTo === 'window') {
  30. [Dòng 604] } else if (_.respondTo === 'slider') {
  31. [Dòng 606] } else if (_.respondTo === 'min') {
  32. [Dòng 618] if (_.originalSettings.mobileFirst === false) {
  33. [Dòng 635] if (_.breakpointSettings[targetBreakpoint] === 'unslick') {
  34. [Dòng 641] if (initial === true) {
  35. [Dòng 705] slideOffset = indexOffset === 0 ? _.options.slidesToScroll : _.options.slidesToShow - indexOffset
  36. [Dòng 712] slideOffset = indexOffset === 0 ? _.options.slidesToScroll : indexOffset
  37. [Dòng 719] var index = event.data.index === 0 ? 0 :
  38. [Dòng 765] if (_.options.accessibility === true) {
  39. [Dòng 772] if (_.options.arrows === true
  40. [Dòng 797] if (_.options.focusOnSelect === true) {
  41. [Dòng 836] if (_.shouldClick === false) {
  42. [Dòng 1051] if (_.options.infinite === true) {
  43. [Dòng 1061] } else if (_.options.centerMode === true) {
  44. [Dòng 1094] if (_.options.vertical === true
  45. [Dòng 1094] _.options.centerMode === true) {
  46. [Dòng 1095] if (_.options.slidesToShow === 2) {
  47. [Dòng 1097] } else if (_.options.slidesToShow === 1) {
  48. [Dòng 1128] } else if (_.options.centerMode === true
  49. [Dòng 1128] _.options.infinite === true) {
  50. [Dòng 1141] if (_.options.variableWidth === true) {
  51. [Dòng 1143] _.options.infinite === false) {
  52. [Dòng 1159] if (_.options.centerMode === true) {
  53. [Dòng 1200] if (_.options.infinite === false) {
  54. [Dòng 1229] centerOffset = _.options.centerMode === true ? _.slideWidth * Math.floor(_.options.slidesToShow / 2) : 0
  55. [Dòng 1231] if (_.options.swipeToSlide === true) {
  56. [Dòng 1406] _.options.pauseOnDotsHover === true
  57. [Dòng 1498] if (event.keyCode === 37
  58. [Dòng 1498] _.options.accessibility === true) {
  59. [Dòng 1501] message: _.options.rtl === true ? 'next' :
  60. [Dòng 1504] } else if (event.keyCode === 39
  61. [Dòng 1507] message: _.options.rtl === true ? 'previous' : 'next'
  62. [Dòng 1585] if (_.options.fade === true) {
  63. [Dòng 1593] if (_.options.lazyLoad === 'anticipated') {
  64. [Dòng 1616] } else if (_.currentSlide === 0) {
  65. [Dòng 1637] if (_.options.lazyLoad === 'progressive') {
  66. [Dòng 1773] if ( _.options.adaptiveHeight === true ) {
  67. [Dòng 1864] if ( $.type(responsiveSettings) === 'array'
  68. [Dòng 1878] _.breakpoints[l] === currentBreakpoint ) {
  69. [Dòng 1969] index = removeBefore === true ? 0 : _.slideCount - 1
  70. [Dòng 1971] index = removeBefore === true ? --index : index
  71. [Dòng 1980] if (removeAll === true) {
  72. [Dòng 2050] if (_.options.vertical === false
  73. [Dòng 2050] _.options.variableWidth === false) {
  74. [Dòng 2054] } else if (_.options.variableWidth === true) {
  75. [Dòng 2062] if (_.options.variableWidth === false) _.$slideTrack.children('.slick-slide').width(_.slideWidth - offset);
  76. [Dòng 2128] if( $.type( arguments[0] ) === 'object' ) {
  77. [Dòng 2134] } else if ( $.type( arguments[0] ) === 'string' ) {
  78. [Dòng 2140] if ( arguments[0] === 'responsive'
  79. [Dòng 2140] $.type( arguments[1] ) === 'array' ) {
  80. [Dòng 2152] if ( type === 'single' ) {
  81. [Dòng 2157] } else if ( type === 'multiple' ) {
  82. [Dòng 2166] } else if ( type === 'responsive' ) {
  83. [Dòng 2181] if( _.options.responsive[l].breakpoint === value[item].breakpoint ) {
  84. [Dòng 2231] _.positionProp = _.options.vertical === true ? 'top' : 'left'
  85. [Dòng 2233] if (_.positionProp === 'top') {
  86. [Dòng 2242] if (_.options.useCSS === true) {
  87. [Dòng 2248] if ( typeof _.options.zIndex === 'number' ) {
  88. [Dòng 2261] if (bodyStyle.perspectiveProperty === undefined
  89. [Dòng 2261] bodyStyle.webkitPerspective === undefined) _.animType = false;
  90. [Dòng 2267] bodyStyle.MozPerspective === undefined) _.animType = false;
  91. [Dòng 2279] if (bodyStyle.msTransform === undefined) _.animType = false;
  92. [Dòng 2306] var evenCoef = _.options.slidesToShow % 2 === 0 ? 1 : 0
  93. [Dòng 2328] if (index === 0) {
  94. [Dòng 2334] } else if (index === _.slideCount - 1) {
  95. [Dòng 2366] indexOffset = _.options.infinite === true ? _.options.slidesToShow + index : index
  96. [Dòng 2388] if (_.options.lazyLoad === 'ondemand'
  97. [Dòng 2388] _.options.lazyLoad === 'anticipated') {
  98. [Dòng 2402] if (_.options.infinite === true
  99. [Dòng 2402] _.options.fade === false) {
  100. [Dòng 2479] if (_.animating === true
  101. [Dòng 2479] _.options.waitForAnimate === true) {
  102. [Dòng 2483] if (_.options.fade === true
  103. [Dòng 2483] _.currentSlide === index) {
  104. [Dòng 2487] if (sync === false) {
  105. [Dòng 2495] _.currentLeft = _.swipeLeft === null ? slideLeft : _.swipeLeft
  106. [Dòng 2497] if (_.options.infinite === false
  107. [Dòng 2497] _.options.centerMode === false
  108. [Dòng 2509] } else if (_.options.infinite === false
  109. [Dòng 2509] _.options.centerMode === true
  110. [Dòng 2627] return (_.options.rtl === false ? 'left' : 'right');
  111. [Dòng 2633] return (_.options.rtl === false ? 'right' : 'left');
  112. [Dòng 2635] if (_.options.verticalSwiping === true) {
  113. [Dòng 2664] if ( _.touchObject.curX === undefined ) {
  114. [Dòng 2668] if ( _.touchObject.edgeHit === true ) {
  115. [Dòng 2732] if ((_.options.swipe === false) || ('ontouchend' in document
  116. [Dòng 2732] _.options.swipe === false)) {
  117. [Dòng 2734] } else if (_.options.draggable === false
  118. [Dòng 2806] positionOffset = (_.options.rtl === false ? 1 : -1) * (_.touchObject.curX > _.touchObject.startX ? 1 : -1);
  119. [Dòng 2817] if ((_.currentSlide === 0
  120. [Dòng 2817] swipeDirection === 'right') || (_.currentSlide >= _.getDotCount()
  121. [Dòng 2817] swipeDirection === 'left')) {
  122. [Dòng 2832] _.options.touchMove === false) {
  123. [Dòng 2836] if (_.animating === true) {
  124. [Dòng 2926] if ( _.options.arrows === true
  125. [Dòng 2933] if (_.currentSlide === 0) {
  126. [Dòng 2938] _.options.centerMode === false) {

== (5 điều kiện):
  1. [Dòng 2007] x = _.positionProp == 'left' ? Math.ceil(position) + 'px' : '0px'
  2. [Dòng 2008] y = _.positionProp == 'top' ? Math.ceil(position) + 'px' : '0px'
  3. [Dòng 2368] if (_.options.slidesToShow == _.options.slidesToScroll
  4. [Dòng 3002] if (typeof opt == 'object'
  5. [Dòng 3002] typeof opt == 'undefined')

!== (47 điều kiện):
  1. [Dòng 22] } else if (typeof exports !== 'undefined') {
  2. [Dòng 155] if (typeof document.mozHidden !== 'undefined') {
  3. [Dòng 158] } else if (typeof document.webkitHidden !== 'undefined') {
  4. [Dòng 342] asNavFor !== null ) {
  5. [Dòng 355] if ( asNavFor !== null
  6. [Dòng 460] if (_.options.infinite !== true) {
  7. [Dòng 612] _.options.responsive !== null) {
  8. [Dòng 630] if (targetBreakpoint !== null) {
  9. [Dòng 631] if (_.activeBreakpoint !== null) {
  10. [Dòng 632] if (targetBreakpoint !== _.activeBreakpoint
  11. [Dòng 676] triggerBreakpoint !== false ) {
  12. [Dòng 699] unevenOffset = (_.slideCount % _.options.slidesToScroll !== 0);
  13. [Dòng 758] _.$dots !== null) {
  14. [Dòng 997] if (filter !== null) {
  15. [Dòng 1103] if (_.slideCount % _.options.slidesToScroll !== 0) {
  16. [Dòng 1314] if (_.$dots !== null) {
  17. [Dòng 1324] if (slideControlIndex !== -1) {
  18. [Dòng 1910] _.currentSlide !== 0) {
  19. [Dòng 1953] if ($(window).width() !== _.windowWidth) {
  20. [Dòng 2144] } else if ( typeof arguments[1] !== 'undefined' ) {
  21. [Dòng 2170] if( $.type( _.options.responsive ) !== 'array' ) {
  22. [Dòng 2239] if (bodyStyle.WebkitTransition !== undefined
  23. [Dòng 2240] bodyStyle.MozTransition !== undefined
  24. [Dòng 2241] bodyStyle.msTransition !== undefined) {
  25. [Dòng 2257] if (bodyStyle.OTransform !== undefined) {
  26. [Dòng 2263] if (bodyStyle.MozTransform !== undefined) {
  27. [Dòng 2269] if (bodyStyle.webkitTransform !== undefined) {
  28. [Dòng 2275] if (bodyStyle.msTransform !== undefined) {
  29. [Dòng 2281] if (bodyStyle.transform !== undefined
  30. [Dòng 2281] _.animType !== false) {
  31. [Dòng 2286] _.transformsEnabled = _.options.useTransform && (_.animType !== null
  32. [Dòng 2286] _.animType !== false);
  33. [Dòng 2500] if (dontAnimate !== true
  34. [Dòng 2567] if (dontAnimate !== true) {
  35. [Dòng 2717] if ( _.touchObject.startX !== _.touchObject.curX ) {
  36. [Dòng 2734] event.type.indexOf('mouse') !== -1) {
  37. [Dòng 2738] event.originalEvent.touches !== undefined ?
  38. [Dòng 2773] touches = event.originalEvent !== undefined ? event.originalEvent.touches : null
  39. [Dòng 2775] touches.length !== 1) {
  40. [Dòng 2781] _.touchObject.curX = touches !== undefined ? touches[0].pageX : event.clientX
  41. [Dòng 2782] _.touchObject.curY = touches !== undefined ? touches[0].pageY : event.clientY
  42. [Dòng 2801] if (event.originalEvent !== undefined
  43. [Dòng 2852] if (_.touchObject.fingerCount !== 1
  44. [Dòng 2857] event.originalEvent.touches !== undefined) {
  45. [Dòng 2861] _.touchObject.startX = _.touchObject.curX = touches !== undefined ? touches.pageX : event.clientX
  46. [Dòng 2862] _.touchObject.startY = _.touchObject.curY = touches !== undefined ? touches.pageY : event.clientY
  47. [Dòng 2872] if (_.$slidesCache !== null) {

!= (4 điều kiện):
  1. [Dòng 805] $('[draggable!=true]', _.$slideTrack).off('dragstart', _.preventDefault);
  2. [Dòng 1467] $('[draggable!=true]', _.$slideTrack).on('dragstart', _.preventDefault);
  3. [Dòng 2707] if( direction != 'vertical' ) {
  4. [Dòng 3006] if (typeof ret != 'undefined') return ret;

================================================================================

📁 FILE 128: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_1/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 129: atm_b1.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_1/assets/js/atm_b1.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 228] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 234] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 253] if (invoiceState === "unpaid"
  10. [Dòng 253] invoiceState === "not_paid") {
  11. [Dòng 258] if (paymentState === "authorization_required") {
  12. [Dòng 264] if (method === "REDIRECT") {
  13. [Dòng 267] } else if (method === "POST_REDIRECT") {
  14. [Dòng 306] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 289] if (jLinks !== undefined
  3. [Dòng 289] jLinks !== null) {
  4. [Dòng 291] if (jMerchantReturn !== undefined
  5. [Dòng 291] jMerchantReturn !== null) {
  6. [Dòng 306] if (responseCode !== undefined
  7. [Dòng 306] responseCode !== null
  8. [Dòng 334] if (parentRes !== "{

================================================================================

📁 FILE 130: atm_b1_2.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_1/atm_b1_2.js
📊 Thống kê: 11 điều kiện duy nhất
   - === : 8 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 52] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 55] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 64] year === y
  5. [Dòng 66] if (inName.value === "") return err("MISSING_FIELD"
  6. [Dòng 75] if ("PAY" === operation) {
  7. [Dòng 319] } else if (value === "") {
  8. [Dòng 336] if (trName.style.display === "") {

!== (3 điều kiện):
  1. [Dòng 77] if (e !== null) {
  2. [Dòng 253] if (lang !== "vi") lang = "en";
  3. [Dòng 283] if (value !== "") {

================================================================================

📁 FILE 131: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_1/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 132: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_2/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 133: atm_b2.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_2/assets/js/atm_b2.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 69] if (i % 2 === parity) d *= 2;
  2. [Dòng 73] return (sum % 10) === 0
  3. [Dòng 144] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 147] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 231] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 236] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 250] if (xhr.status === 200
  8. [Dòng 250] xhr.status === 201) {
  9. [Dòng 252] if (invoiceState === "unpaid"
  10. [Dòng 252] invoiceState === "not_paid") {
  11. [Dòng 256] if (paymentState === "authorization_required") {
  12. [Dòng 260] if (method === "REDIRECT") {
  13. [Dòng 263] } else if (method === "POST_REDIRECT") {
  14. [Dòng 302] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 122] this.oldValue !== this.value) {
  2. [Dòng 285] if (jLinks !== undefined
  3. [Dòng 285] jLinks !== null) {
  4. [Dòng 287] if (jMerchantReturn !== undefined
  5. [Dòng 287] jMerchantReturn !== null) {
  6. [Dòng 302] if (responseCode !== undefined
  7. [Dòng 302] responseCode !== null
  8. [Dòng 330] if (parentRes !== "{

================================================================================

📁 FILE 134: atm_b2_2.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_2/atm_b2_2.js
📊 Thống kê: 6 điều kiện duy nhất
   - === : 3 lần
   - == : 0 lần
   - !== : 3 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (3 điều kiện):
  1. [Dòng 24] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 41] if (inName.value === "") return err("MISSING_FIELD"
  3. [Dòng 51] if ("PAY" === operation) {

!== (3 điều kiện):
  1. [Dòng 53] if (e !== null) {
  2. [Dòng 229] if (lang !== "vi") lang = "en";
  3. [Dòng 259] if (value !== "") {

================================================================================

📁 FILE 135: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/atm_bank_2/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 136: common.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/common.js
📊 Thống kê: 22 điều kiện duy nhất
   - === : 14 lần
   - == : 0 lần
   - !== : 8 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (14 điều kiện):
  1. [Dòng 42] if (i % 2 === parity) d *= 2;
  2. [Dòng 46] return (sum % 10) === 0
  3. [Dòng 116] if (typeof queryParams[key] === "undefined") {
  4. [Dòng 119] } else if (typeof queryParams[key] === "string") {
  5. [Dòng 205] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  6. [Dòng 211] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  7. [Dòng 227] if (xhr.status === 200
  8. [Dòng 227] xhr.status === 201) {
  9. [Dòng 230] if (invoiceState === "unpaid"
  10. [Dòng 230] invoiceState === "not_paid") {
  11. [Dòng 235] if (paymentState === "authorization_required") {
  12. [Dòng 241] if (method === "REDIRECT") {
  13. [Dòng 244] } else if (method === "POST_REDIRECT") {
  14. [Dòng 283] responseCode === "0") {

!== (8 điều kiện):
  1. [Dòng 94] this.oldValue !== this.value) {
  2. [Dòng 266] if (jLinks !== undefined
  3. [Dòng 266] jLinks !== null) {
  4. [Dòng 268] if (jMerchantReturn !== undefined
  5. [Dòng 268] jMerchantReturn !== null) {
  6. [Dòng 283] if (responseCode !== undefined
  7. [Dòng 283] responseCode !== null
  8. [Dòng 311] if (parentRes !== "{

================================================================================

📁 FILE 137: bootstrap.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/international_card/assets/bootstrap-4.4.1-dist/js/bootstrap.js
📊 Thống kê: 135 điều kiện duy nhất
   - === : 100 lần
   - == : 0 lần
   - !== : 34 lần
   - != : 1 lần
--------------------------------------------------------------------------------

=== (100 điều kiện):
  1. [Dòng 7] typeof exports === 'object'
  2. [Dòng 8] typeof define === 'function'
  3. [Dòng 159] selector === '#') {
  4. [Dòng 222] if (typeof element.getRootNode === 'function') {
  5. [Dòng 239] if (typeof $ === 'undefined') {
  6. [Dòng 250] version[0] === minMajor
  7. [Dòng 250] version[1] === minMinor
  8. [Dòng 375] if (config === 'close') {
  9. [Dòng 479] if (input.type === 'radio') {
  10. [Dòng 489] } else if (input.type === 'checkbox') {
  11. [Dòng 490] if (this._element.tagName === 'LABEL'
  12. [Dòng 490] input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
  13. [Dòng 534] if (config === 'toggle') {
  14. [Dòng 602] if (_button.getAttribute('aria-pressed') === 'true') {
  15. [Dòng 802] if (activeIndex === index) {
  16. [Dòng 862] if (this._config.pause === 'hover') {
  17. [Dòng 906] if (_this3._config.pause === 'hover') {
  18. [Dòng 976] var isNextDirection = direction === Direction.NEXT
  19. [Dòng 977] var isPrevDirection = direction === Direction.PREV
  20. [Dòng 982] activeIndex === 0
  21. [Dòng 982] activeIndex === lastItemIndex
  22. [Dòng 988] var delta = direction === Direction.PREV ? -1 : 1
  23. [Dòng 990] return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
  24. [Dòng 1037] if (direction === Direction.NEXT) {
  25. [Dòng 1120] if (typeof config === 'object') {
  26. [Dòng 1124] var action = typeof config === 'string' ? config : _config.slide
  27. [Dòng 1131] if (typeof config === 'number') {
  28. [Dòng 1133] } else if (typeof action === 'string') {
  29. [Dòng 1134] if (typeof data[action] === 'undefined') {
  30. [Dòng 1282] return foundElem === element
  31. [Dòng 1327] if (typeof _this._config.parent === 'string') {
  32. [Dòng 1328] return elem.getAttribute('data-parent') === _this._config.parent
  33. [Dòng 1334] if (actives.length === 0) {
  34. [Dòng 1507] typeof config === 'object'
  35. [Dòng 1518] if (typeof config === 'string') {
  36. [Dòng 1519] if (typeof data[config] === 'undefined') {
  37. [Dòng 1551] if (event.currentTarget.tagName === 'A') {
  38. [Dòng 1698] if (usePopper === void 0) {
  39. [Dòng 1725] if (typeof Popper === 'undefined') {
  40. [Dòng 1731] if (this._config.reference === 'parent') {
  41. [Dòng 1755] $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
  42. [Dòng 1874] if (typeof this._config.offset === 'function') {
  43. [Dòng 1900] if (this._config.display === 'static') {
  44. [Dòng 1914] var _config = typeof config === 'object' ? config : null
  45. [Dòng 1932] if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
  46. [Dòng 1932] event.type === 'keyup'
  47. [Dòng 1946] event.type === 'click') {
  48. [Dòng 1960] if (event && (event.type === 'click'
  49. [Dòng 1960] event.which === TAB_KEYCODE) && $.contains(parent
  50. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
  51. [Dòng 2023] event.which === ESCAPE_KEYCODE) {
  52. [Dòng 2027] if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
  53. [Dòng 2027] event.which === SPACE_KEYCODE)) {
  54. [Dòng 2028] if (event.which === ESCAPE_KEYCODE) {
  55. [Dòng 2041] if (items.length === 0) {
  56. [Dòng 2047] if (event.which === ARROW_UP_KEYCODE
  57. [Dòng 2052] if (event.which === ARROW_DOWN_KEYCODE
  58. [Dòng 2327] if (this._config.backdrop === 'static') {
  59. [Dòng 2407] $(_this5._element).has(event.target).length === 0) {
  60. [Dòng 2418] if (event.which === ESCAPE_KEYCODE$1) {
  61. [Dòng 2681] if (this.tagName === 'A'
  62. [Dòng 2681] this.tagName === 'AREA') {
  63. [Dòng 2795] if (unsafeHtml.length === 0) {
  64. [Dòng 2799] typeof sanitizeFn === 'function') {
  65. [Dòng 2812] if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
  66. [Dòng 2829] if (_ret === "continue") continue;
  67. [Dòng 3027] if ($(this.element).css('display') === 'none') {
  68. [Dòng 3052] var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
  69. [Dòng 3086] if (prevHoverState === HoverState.OUT) {
  70. [Dòng 3180] if (typeof content === 'object'
  71. [Dòng 3208] title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
  72. [Dòng 3249] if (typeof this.config.offset === 'function') {
  73. [Dòng 3262] if (this.config.container === false) {
  74. [Dòng 3282] if (trigger === 'click') {
  75. [Dòng 3287] var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
  76. [Dòng 3288] var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
  77. [Dòng 3334] context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
  78. [Dòng 3337] context._hoverState === HoverState.SHOW) {
  79. [Dòng 3351] if (context._hoverState === HoverState.SHOW) {
  80. [Dòng 3367] context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
  81. [Dòng 3383] if (context._hoverState === HoverState.OUT) {
  82. [Dòng 3408] if (typeof config.delay === 'number') {
  83. [Dòng 3415] if (typeof config.title === 'number') {
  84. [Dòng 3419] if (typeof config.content === 'number') {
  85. [Dòng 3484] var _config = typeof config === 'object'
  86. [Dòng 3642] if (typeof content === 'function') {
  87. [Dòng 3805] this._scrollElement = element.tagName === 'BODY' ? window : element
  88. [Dòng 3827] var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
  89. [Dòng 3828] var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
  90. [Dòng 3829] var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
  91. [Dòng 3896] return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
  92. [Dòng 3904] return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
  93. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
  94. [Dòng 4106] this._element.parentNode.nodeType === Node.ELEMENT_NODE
  95. [Dòng 4116] var itemSelector = listElement.nodeName === 'UL'
  96. [Dòng 4116] listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
  97. [Dòng 4171] var activeElements = container && (container.nodeName === 'UL'
  98. [Dòng 4171] container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
  99. [Dòng 4196] if (active.getAttribute('role') === 'tab') {
  100. [Dòng 4203] if (element.getAttribute('role') === 'tab') {

!== (34 điều kiện):
  1. [Dòng 7] typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
  2. [Dòng 161] hrefAttr !== '#' ? hrefAttr.trim() : ''
  3. [Dòng 744] $(this._element).css('visibility') !== 'hidden') {
  4. [Dòng 1285] if (selector !== null
  5. [Dòng 1415] if (selector !== null) {
  6. [Dòng 1473] if (typeof this._config.parent.jquery !== 'undefined') {
  7. [Dòng 1736] if (typeof this._config.reference.jquery !== 'undefined') {
  8. [Dòng 1744] if (this._config.boundary !== 'scrollParent') {
  9. [Dòng 1799] if (this._popper !== null) {
  10. [Dòng 1932] event.which !== TAB_KEYCODE)) {
  11. [Dòng 2008] event.which !== ESCAPE_KEYCODE
  12. [Dòng 2008] if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
  13. [Dòng 2008] event.which !== ARROW_UP_KEYCODE
  14. [Dòng 2354] this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
  15. [Dòng 2407] if (document !== event.target
  16. [Dòng 2407] _this5._element !== event.target
  17. [Dòng 2488] if (event.target !== event.currentTarget) {
  18. [Dòng 2604] if (typeof margin !== 'undefined') {
  19. [Dòng 2773] if (allowedAttributeList.indexOf(attrName) !== -1) {
  20. [Dòng 2774] if (uriAttrs.indexOf(attrName) !== -1) {
  21. [Dòng 3036] var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
  22. [Dòng 3107] if (_this2._hoverState !== HoverState.SHOW
  23. [Dòng 3117] if (_this2._popper !== null) {
  24. [Dòng 3233] if (data.originalPlacement !== data.placement) {
  25. [Dòng 3286] } else if (trigger !== Trigger.MANUAL) {
  26. [Dòng 3318] titleType !== 'string') {
  27. [Dòng 3402] if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
  28. [Dòng 3437] if (this.constructor.Default[key] !== this.config[key]) {
  29. [Dòng 3450] if (tabClass !== null
  30. [Dòng 3468] if (tip.getAttribute('x-placement') !== null) {
  31. [Dòng 3880] if (typeof config.target !== 'string') {
  32. [Dòng 3914] if (this._scrollHeight !== scrollHeight) {
  33. [Dòng 3921] if (this._activeTarget !== target) {
  34. [Dòng 3939] var isActiveTarget = this._activeTarget !== this._targets[i]

!= (1 điều kiện):
  1. [Dòng 62] var source = arguments[i] != null ? arguments[i] : {

================================================================================

📁 FILE 138: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/international_card/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 139: script.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/begodi/international_card/script.js
📊 Thống kê: 9 điều kiện duy nhất
   - === : 7 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 12] if (number === "") return err("MISSING_FIELD"
  2. [Dòng 22] if (inMonth.value === "") return err("MISSING_FIELD"
  3. [Dòng 25] if (inYear.value === "") return err("MISSING_FIELD"
  4. [Dòng 37] year === y
  5. [Dòng 39] if (inCvv.value === "") return err("MISSING_FIELD"
  6. [Dòng 70] if ("PAY" === operation) {
  7. [Dòng 144] } else if (value === "") {

!== (2 điều kiện):
  1. [Dòng 72] if (e !== null) {
  2. [Dòng 117] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 140: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/iframe/index.html
📊 Thống kê: 35 điều kiện duy nhất
   - === : 21 lần
   - == : 0 lần
   - !== : 14 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (21 điều kiện):
  1. [Dòng 10] if ((cardno.length === 15
  2. [Dòng 10] cardno.length === 16
  3. [Dòng 10] cardno.length === 19) && isMode10(cardno) === true) {
  4. [Dòng 10] isMode10(cardno) === true) {
  5. [Dòng 29] if (i % 2 === parity) d *= 2;
  6. [Dòng 33] return (sum % 10) === 0
  7. [Dòng 54] if ("PAY" === operation) {
  8. [Dòng 75] if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
  9. [Dòng 81] } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
  10. [Dòng 97] if (xhr.status === 200
  11. [Dòng 97] xhr.status === 201) {
  12. [Dòng 100] if (invoiceState === "unpaid"
  13. [Dòng 100] invoiceState === "not_paid") {
  14. [Dòng 105] if (paymentState === "authorization_required") {
  15. [Dòng 111] if (method === "REDIRECT") {
  16. [Dòng 116] } else if (method === "POST_REDIRECT") {
  17. [Dòng 183] //Customizable =================================================================================================
  18. [Dòng 229] if (typeof queryParams[key] === "undefined") {
  19. [Dòng 232] } else if (typeof queryParams[key] === "string") {
  20. [Dòng 246] if (params["CardList"] === undefined
  21. [Dòng 246] params["CardList"] === null) return;

!== (14 điều kiện):
  1. [Dòng 40] //if (event.origin !== "http://example.com:8080") return;
  2. [Dòng 58] if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
  3. [Dòng 59] if (inNumber.style.display !== "none") instrument.number = inNumber.value;
  4. [Dòng 60] if (inDate.style.display !== "none") instrument.date = inDate.value;
  5. [Dòng 61] if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
  6. [Dòng 62] if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
  7. [Dòng 63] if (inName.style.display !== "none") instrument.name = inName.value;
  8. [Dòng 78] /*if (contentType !== my_expected_type) {
  9. [Dòng 138] if (jLinks !== undefined
  10. [Dòng 138] jLinks !== null) {
  11. [Dòng 140] if (jMerchantReturn !== undefined
  12. [Dòng 140] jMerchantReturn !== null) {
  13. [Dòng 172] if (parentRes !== "{
  14. [Dòng 245] if (lang !== "vi") lang = "en";

================================================================================

📁 FILE 141: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/index.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 142: karma.conf.js
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/karma.conf.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 143: main.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/main.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 144: polyfills.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/polyfills.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 145: test.ts
📍 Đường dẫn: /root/projects/onepay/paygate-mafc/src/test.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📋 TÓM TẮT TẤT CẢ ĐIỀU KIỆN DUY NHẤT:
====================================================================================================

=== (410 điều kiện duy nhất):
------------------------------------------------------------
1. return lang === this._translate.currentLang
2. isPopupSupport === 'True') || (rePayment
3. isPopupSupport === 'True'"
4. isPopupSupport === 'True')">
5. params.timeout === 'true') {
6. this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
7. _re.body.state === 'unpaid');
8. if (this.errorCode === 'overtime'
9. this.errorCode === '253') {
10. params.name === 'CUSTOMER_INTIME'
11. params.code === '09') {
12. if (this.timeLeft === 0) {
13. if (YY % 400 === 0
14. YY % 4 === 0)) {
15. if (YYYY % 400 === 0
16. YYYY % 4 === 0)) {
17. c_expdate && (_inExpDate.length === 0 || _inExpDate.trim().length === 0)
18. _inExpDate.trim().length === 0)"
19. if (target.tagName === 'A'
20. if (event.keyCode === 8
21. event.key === "Backspace"
22. if (approval.method === 'REDIRECT') {
23. } else if (approval.method === 'POST_REDIRECT') {
24. filteredData.length === 0"
25. if (filtered.toUpperCase() === item.b.name.toUpperCase().substring(0, filtered.length)
26. filtered.toUpperCase() === item.b.code.toUpperCase().substring(0, filtered.length)
27. if (valOut === 'auth') {
28. if (this._b === '1'
29. this._b === '20'
30. this._b === '64') {
31. if (this._b === '36'
32. this._b === '18'
33. this._b === '19'
34. if (this._b === '19'
35. this._b === '16'
36. this._b === '25'
37. this._b === '33'
38. this._b === '39'
39. this._b === '11'
40. this._b === '17'
41. this._b === '36'
42. this._b === '44'
43. this._b === '12'
44. this._b === '64'
45. if (this._b === '20'
46. if (this._b === '12'
47. this._b === '18') {
48. [ngStyle]="{'border-color': type === 2 ? this.themeColor.border_color : border_color}"
49. d_vrbank===true"
50. this.onePaymentMethod = this.d_domestic + this.d_inter + this.d_qr + this.d_paypal_number === 1
51. if (this._res.state === 'unpaid'
52. this._res.state === 'not_paid') {
53. if ('op' === auth
54. } else if ('bank' === auth
55. return id === 'amex' ? '1234' : '123'
56. if (this.timeLeftPaypal === 0) {
57. style="{{item.brand_id === 'visa' ? 'height: 14.42px
58. if (event.keyCode === 13) {
59. && ((item.brand_id === 'amex'
60. return this._i_token_otp != null && !isNaN(+this._i_token_otp) && ((id === 'amex'
61. const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
62. item.method === method) : null;
63. if (v.length === 2
64. this.flag.length === 3
65. this.flag.charAt(this.flag.length - 1) === '/') {
66. if (v.length === 1) {
67. } else if (v.length === 2) {
68. v.length === 2) {
69. if (len === 2) {
70. if (M[1] === 'Chrome') {
71. if (param === key) {
72. pos = target === 0 ? firstPos - firstPos * easeInPercentage : firstPos + target * easeInPercentage
73. target === 0
74. if (typeof define === 'function'
75. } else if (typeof exports === 'object') {
76. if (number === "") return err("MISSING_FIELD"
77. if (inName.value === "") return err("MISSING_FIELD"
78. if ("PAY" === operation) {
79. if (i % 2 === parity) d *= 2;
80. return (sum % 10) === 0
81. if (typeof queryParams[key] === "undefined") {
82. } else if (typeof queryParams[key] === "string") {
83. if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {
84. } else if (xhr.readyState === XMLHttpRequest.DONE) { //4
85. if (xhr.status === 200
86. xhr.status === 201) {
87. if (invoiceState === "unpaid"
88. invoiceState === "not_paid") {
89. if (paymentState === "authorization_required") {
90. if (method === "REDIRECT") {
91. } else if (method === "POST_REDIRECT") {
92. responseCode === "0") {
93. if (inMonth.value === "") return err("MISSING_FIELD"
94. if (inYear.value === "") return err("MISSING_FIELD"
95. year === y
96. if (inPhone.value === "") return err("MISSING_FIELD"
97. } else if (value === "") {
98. if (trPhone.style.display === "") {
99. } else if (trName.style.display === "") {
100. if (trName.style.display === "") {
101. if (xhr.status === 200) {
102. if (insType === "card") {
103. if (insBrandId === "visa"
104. insBrandId === "mastercard"
105. insBrandId === "amex"
106. insBrandId === "jcb"
107. insBrandId === "cup") {
108. } else if (insBrandId === "atm") {
109. } else if (insType === "dongabank_account") {
110. } else if (insType === "techcombank_account") {
111. } else if (insType === "vib_account") {
112. } else if (insType === "bidv_account") {
113. } else if (insType === "tpbank_account") {
114. } else if (insType === "shb_account") {
115. } else if (insType === "shb_customer_id") {
116. } else if (insType === "vpbank_account") {
117. } else if (insType === "oceanbank_online_account") {
118. } else if (insType === "oceanbank_mobile_account") {
119. } else if (insType === "pvcombank_account") {
120. if (inCvv.value === "") return err("MISSING_FIELD"
121. if (inCvv.value === "") {
122. if ((cardno.length === 15
123. cardno.length === 16
124. cardno.length === 19) && isMode10(cardno) === true) {
125. isMode10(cardno) === true) {
126. if (params["CardList"] === undefined
127. params["CardList"] === null) return;
128. if (insSwiftCode === "BFTVVNVX") { //Vietcombank
129. } else if (insSwiftCode === "BIDVVNVX") { //BIDV
130. typeof exports === 'object'
131. typeof define === 'function'
132. selector === '#') {
133. if (typeof element.getRootNode === 'function') {
134. if (typeof $ === 'undefined') {
135. version[0] === minMajor
136. version[1] === minMinor
137. if (config === 'close') {
138. if (input.type === 'radio') {
139. } else if (input.type === 'checkbox') {
140. if (this._element.tagName === 'LABEL'
141. input.checked === this._element.classList.contains(ClassName$1.ACTIVE)) {
142. if (config === 'toggle') {
143. if (_button.getAttribute('aria-pressed') === 'true') {
144. if (activeIndex === index) {
145. if (this._config.pause === 'hover') {
146. if (_this3._config.pause === 'hover') {
147. var isNextDirection = direction === Direction.NEXT
148. var isPrevDirection = direction === Direction.PREV
149. activeIndex === 0
150. activeIndex === lastItemIndex
151. var delta = direction === Direction.PREV ? -1 : 1
152. return itemIndex === -1 ? this._items[this._items.length - 1] : this._items[itemIndex]
153. if (direction === Direction.NEXT) {
154. if (typeof config === 'object') {
155. var action = typeof config === 'string' ? config : _config.slide
156. if (typeof config === 'number') {
157. } else if (typeof action === 'string') {
158. if (typeof data[action] === 'undefined') {
159. return foundElem === element
160. if (typeof _this._config.parent === 'string') {
161. return elem.getAttribute('data-parent') === _this._config.parent
162. if (actives.length === 0) {
163. typeof config === 'object'
164. if (typeof config === 'string') {
165. if (typeof data[config] === 'undefined') {
166. if (event.currentTarget.tagName === 'A') {
167. if (usePopper === void 0) {
168. if (typeof Popper === 'undefined') {
169. if (this._config.reference === 'parent') {
170. $(parent).closest(Selector$4.NAVBAR_NAV).length === 0) {
171. if (typeof this._config.offset === 'function') {
172. if (this._config.display === 'static') {
173. var _config = typeof config === 'object' ? config : null
174. if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH
175. event.type === 'keyup'
176. event.type === 'click') {
177. if (event && (event.type === 'click'
178. event.which === TAB_KEYCODE) && $.contains(parent
179. if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE
180. event.which === ESCAPE_KEYCODE) {
181. if (!isActive || isActive && (event.which === ESCAPE_KEYCODE
182. event.which === SPACE_KEYCODE)) {
183. if (event.which === ESCAPE_KEYCODE) {
184. if (items.length === 0) {
185. if (event.which === ARROW_UP_KEYCODE
186. if (event.which === ARROW_DOWN_KEYCODE
187. if (this._config.backdrop === 'static') {
188. $(_this5._element).has(event.target).length === 0) {
189. if (event.which === ESCAPE_KEYCODE$1) {
190. if (this.tagName === 'A'
191. this.tagName === 'AREA') {
192. if (unsafeHtml.length === 0) {
193. typeof sanitizeFn === 'function') {
194. if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {
195. if (_ret === "continue") continue;
196. if ($(this.element).css('display') === 'none') {
197. var placement = typeof this.config.placement === 'function' ? this.config.placement.call(this, tip, this.element) : this.config.placement
198. if (prevHoverState === HoverState.OUT) {
199. if (typeof content === 'object'
200. title = typeof this.config.title === 'function' ? this.config.title.call(this.element) : this.config.title
201. if (typeof this.config.offset === 'function') {
202. if (this.config.container === false) {
203. if (trigger === 'click') {
204. var eventIn = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSEENTER : _this5.constructor.Event.FOCUSIN
205. var eventOut = trigger === Trigger.HOVER ? _this5.constructor.Event.MOUSELEAVE : _this5.constructor.Event.FOCUSOUT
206. context._activeTrigger[event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER] = true;
207. context._hoverState === HoverState.SHOW) {
208. if (context._hoverState === HoverState.SHOW) {
209. context._activeTrigger[event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER] = false;
210. if (context._hoverState === HoverState.OUT) {
211. if (typeof config.delay === 'number') {
212. if (typeof config.title === 'number') {
213. if (typeof config.content === 'number') {
214. var _config = typeof config === 'object'
215. if (typeof content === 'function') {
216. this._scrollElement = element.tagName === 'BODY' ? window : element
217. var autoMethod = this._scrollElement === this._scrollElement.window ? OffsetMethod.OFFSET : OffsetMethod.POSITION
218. var offsetMethod = this._config.method === 'auto' ? autoMethod : this._config.method
219. var offsetBase = offsetMethod === OffsetMethod.POSITION ? this._getScrollTop() : 0
220. return this._scrollElement === window ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop
221. return this._scrollElement === window ? window.innerHeight : this._scrollElement.getBoundingClientRect().height
222. var isActiveTarget = this._activeTarget !== this._targets[i] && scrollTop >= this._offsets[i] && (typeof this._offsets[i + 1] === 'undefined'
223. this._element.parentNode.nodeType === Node.ELEMENT_NODE
224. var itemSelector = listElement.nodeName === 'UL'
225. listElement.nodeName === 'OL' ? Selector$9.ACTIVE_UL : Selector$9.ACTIVE
226. var activeElements = container && (container.nodeName === 'UL'
227. container.nodeName === 'OL') ? $(container).find(Selector$9.ACTIVE_UL) : $(container).children(Selector$9.ACTIVE);
228. if (active.getAttribute('role') === 'tab') {
229. if (element.getAttribute('role') === 'tab') {
230. 1>t)throw Error("numRounds must a integer >= 1");if("SHA-1"===c)m=512,q=K,u=Z,f=160,r=function(a){return a.slice()};else if(0===c.lastIndexOf("SHA-",0))if(q=function(a,b){return L(a,b,c)
231. c)},u=function(a,b,h,e){var k,f;if("SHA-224"===c
232. "SHA-256"===c)k=(b+65>>>9<<4)+15,f=16;else if("SHA-384"===c
233. "SHA-512"===c)k=(b+129>>>10<<
234. c);if("SHA-224"===c)a=[e[0],e[1],e[2],e[3],e[4],e[5],e[6]];else if("SHA-256"===c)a=e;else if("SHA-384"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,e[4].b,e[5].a,e[5].b];else if("SHA-512"===c)a=[e[0].a,e[0].b,e[1].a,e[1].b,e[2].a,e[2].b,e[3].a,e[3].b,e[4].a,
235. "SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)
236. 0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
237. return a},r=function(a){return a.slice()},"SHA-224"===c)m=512,f=224;else if("SHA-256"===c)m=512,f=256;else if("SHA-384"===c)m=1024,f=384;else if("SHA-512"===c)m=1024,f=512;else throw Error("Chosen SHA variant is not supported");else if(0===c.lastIndexOf("SHA3-",0)||0===c.lastIndexOf("SHAKE",0)){var F=6;q=D;r=function(a){var c=[],e;for(e=0;5>e;e+=1)c[e]=a[e].slice();return c};x=1;if("SHA3-224"===
238. c)m=1152,f=224;else if("SHA3-256"===c)m=1088,f=256;else if("SHA3-384"===c)m=832,f=384;else if("SHA3-512"===c)m=576,f=512;else if("SHAKE128"===c)m=1344,f=-1,F=31,z=!0;else if("SHAKE256"===c)m=1088,f=-1,F=31,z=!0;else throw Error("Chosen SHA variant is not supported");u=function(a
239. 0===64*l%e
240. 5][l/5|0];g.push(a.b);if(32*g.length>=h)break;g.push(a.a);l+=1;0===64*l%e&&D(null,b)}return g}}else throw Error("Chosen SHA variant is not supported");d=M(a,g,x);l=A(c);this.setHMACKey=function(a,b,h){var k;if(!0===I)throw Error("HMAC key already set");if(!0===y)throw Error("Cannot set HMAC key after calling update");if(!0===z)throw Error("SHAKE is not supported for HMAC");g=(h
241. f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
242. )b.push(0);b[h]&=4294967040}for(a=0;a<=h;a+=1)v[a]=b[a]^909522486,w[a]=b[a]^1549556828;l=q(v,l);e=m;I=!0};this.update=function(a){var c,b,k,f=0,g=m>>>5;c=d(a,h,n);a=c.binLen;b=c.value;c=a>>>5;for(k=0;k<c;k+=g)f+m<=a&&(l=q(b.slice(k,k+g),l),f+=m);e+=f;h=b.slice(f>>>5);n=a%m;y=!0};this.getHash=function(a,b){var k,g,d,m;if(!0===I)throw Error("Cannot call getHash after setting HMAC key");d=N(b);if(!0===z){if(-1===d.shakeLen)throw Error("shakeLen must be specified in options");
243. for(g=1;g<t;g+=1)!0===z
244. f);return k(m)};this.getHMAC=function(a,b){var k,g,d,p;if(!1===I)throw Error("Cannot call getHMAC without first setting HMAC key");d=N(b);switch(a){case "HEX":k=function(a){return O(a,f,x,d)};break;case "B64":k=function(a){return P(a,f,x,d)};break;case "BYTES":k=function(a){return Q(a,f,x)};break;case "ARRAYBUFFER":try{k=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}k=function(a){return R(a,f,x)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
245. d=-1===b?3:0
246. f=-1===b?3:0
247. g=-1===b?3:0
248. !0===c.hasOwnProperty("b64Pad")
249. a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");
250. u=-1===b?3:0
251. l+=2){p=parseInt(a.substr(l,2),16);if(isNaN(p))throw Error("String of HEX type contains invalid characters");m=(l>>>1)+q;for(f=m>>>2;c.length<=f;)c.push(0);c[f]|=p<<8*(u+m%4*b)}return{value:c,binLen:4*g+d}};break;case "TEXT":c=function(c,h,d){var g,l,p=0,f,m,q,u,r,t;h=h||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(t=-1===
252. m+=1){r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=l[m]<<8*(t+r%4*b);p+=1}else if("UTF16BE"===a
253. "UTF16LE"===a)for(t=-1===b?2:0
254. UTF16LE"===a
255. 1===b
256. !0===l
257. !0===l&&(m=g&255,g=m<<8|g>>>8);r=p+q;for(u=r>>>2;h.length<=u;)h.push(0);h[u]|=g<<8*(t+r%4*b);p+=2}return{value:h,binLen:8*p+d}};break;case "B64":c=function(a,c,d){var g=0,l,p,f,m,q,u,r,t;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");p=a.indexOf("=");a=a.replace(/\=/g
258. t=-1===b?3:0
259. q=-1===b?3:0
260. m=-1===b?3:0
261. c.b^a.b)}function A(c){var a=[],d;if("SHA-1"===c)a=[1732584193,4023233417,2562383102,271733878,3285377520];else if(0===c.lastIndexOf("SHA-",0))switch(a=
262. new b(d[2],4271175723),new b(d[3],1595750129),new b(d[4],2917565137),new b(d[5],725511199),new b(d[6],4215389547),new b(d[7],327033209)];break;default:throw Error("Unknown SHA variant");}else if(0===c.lastIndexOf("SHA3-",0)
263. 0===c.lastIndexOf("SHAKE",0))for(c=0
264. a,k){var e,h,n,g,l,p,f,m,q,u,r,t,v,w,y,A,z,x,F,B,C,D,E=[],J;if("SHA-224"===k
265. "SHA-256"===k)u=64,t=1,D=Number,v=G,w=la,y=H,A=ha,z=ja,x=da,F=fa,C=U,B=aa,J=d;else if("SHA-384"===k
266. "SHA-512"===k)u=80,t=2,D=b,v=ma,w=na,y=oa,A=ia,z=ka,x=ea,F=ga,C=ca,B=ba,J=V;else throw Error("Unexpected error in SHA-2 implementation");k=a[0];e=a[1];h=a[2];n=a[3];g=a[4];l=a[5];p=a[6];f=a[7];for(r=0
267. "function"===typeof define
268. 1>u)throw Error("numRounds must a integer >= 1");if(0===c.lastIndexOf("SHA-",0))if(q=function(b,a){return A(b,a,c)
269. c)},y=function(b,a,l,f){var g,e;if("SHA-224"===c
270. "SHA-256"===c)g=(a+65>>>9<<4)+15,e=16;else throw Error("Unexpected error in SHA-2 implementation");for(
271. c);if("SHA-224"===c)b=[f[0],f[1],f[2],f[3],f[4],f[5],f[6]];else if("SHA-256"===c)b=f;else throw Error("Unexpected error in SHA-2 implementation");return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
272. "SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a
273. return b},p=function(b){return b.slice()},"SHA-224"===c)h=512,e=224;else if("SHA-256"===c)h=512,e=256;else throw Error("Chosen SHA variant is not supported");else throw Error("Chosen SHA variant is not supported");k=B(a,f);n=x(c);this.setHMACKey=function(b,a,g){var e;if(!0===m)throw Error("HMAC key already set");
274. if(!0===z)throw Error("Cannot set HMAC key after calling update");f=(g
275. 5);g=a%h;z=!0};this.getHash=function(a,f){var d,h,k,q;if(!0===m)throw Error("Cannot call getHash after setting HMAC key");k=C(f);switch(a){case "HEX":d=function(a){return D(a,e,k)};break;case "B64":d=function(a){return E(a,e,k)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{h=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("format must be HEX, B64, BYTES, or ARRAYBUFFER");
276. x(c));return d(q)};this.getHMAC=function(a,f){var d,k,t,u;if(!1===m)throw Error("Cannot call getHMAC without first setting HMAC key");t=C(f);switch(a){case "HEX":d=function(a){return D(a,e,t)};break;case "B64":d=function(a){return E(a,e,t)};break;case "BYTES":d=function(a){return F(a,e)};break;case "ARRAYBUFFER":try{d=new ArrayBuffer(0)}catch(v){throw Error("ARRAYBUFFER not supported by this environment");}d=function(a){return G(a,e)};break;default:throw Error("outputFormat must be HEX, B64, BYTES, or ARRAYBUFFER");
277. h=(d>>>1)+q;for(e=h>>>2;b.length<=e;)b.push(0);b[e]|=k<<8*(3+h%4*-1)}return{value:b,binLen:4*f+c}};break;case "TEXT":d=function(c,b,d){var f,n,k=0,e,h,q,m,p,r;b=b||[0];d=d||0;q=d>>>3;if("UTF8"===a)for(r=3
278. q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=n[h]<<8*(r+p%4*-1);k+=1}else if("UTF16BE"===a
279. "UTF16LE"===a)for(r=2
280. !0===n
281. e+=1){f=c.charCodeAt(e);!0===n&&(h=f&255,f=h<<8|f>>>8);p=k+q;for(m=p>>>2;b.length<=m;)b.push(0);b[m]|=f<<8*(r+p%4*-1);k+=2}return{value:b,binLen:8*k+d}};break;case "B64":d=function(a,b,c){var f=0,d,k,e,h,q,m,p;if(-1===a.search(/^[a-zA-Z0-9=+\/]+$/))throw Error("Invalid character in base-64 string");k=a.indexOf("=");a=a.replace(/\=/g
282. 16)&65535)<<16|b&65535}function R(c,a,d,l,b){var g=(c&65535)+(a&65535)+(d&65535)+(l&65535)+(b&65535);return((c>>>16)+(a>>>16)+(d>>>16)+(l>>>16)+(b>>>16)+(g>>>16)&65535)<<16|g&65535}function x(c){var a=[],d;if(0===c.lastIndexOf("SHA-",0))switch(a=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428]
283. new m,new m,new m,new m,new m,new m];break;case "SHA-512":a=[new m,new m,new m,new m,new m,new m,new m,new m];break;default:throw Error("Unknown SHA variant");}else throw Error("No SHA variants supported");return a}function A(c,a,d){var l,b,g,f,n,k,e,h,m,r,p,w,t,x,u,z,A,B,C,D,E,F,v=[],G;if("SHA-224"===d
284. "SHA-256"===d)r=64,w=1,F=Number,t=P,x=Q,u=R,z=N,A=O,B=L,C=M,E=K,D=J,G=H;else throw Error("Unexpected error in SHA-2 implementation");d=a[0];l=a[1];b=a[2];g=a[3];f=a[4];n=a[5];k=a[6];e=a[7];for(p=
285. if (typeof(index) === 'boolean') {
286. if (typeof(index) === 'number') {
287. if (index === 0
288. _.$slides.length === 0) {
289. if (addBefore === true) {
290. if (_.options.slidesToShow === 1
291. _.options.adaptiveHeight === true
292. _.options.vertical === false) {
293. if (_.options.rtl === true
294. if (_.transformsEnabled === false) {
295. if (_.options.vertical === false) {
296. if (_.cssTransitions === false) {
297. if (_.options.rtl === true) {
298. typeof asNavFor === 'object' ) {
299. if (_.options.fade === false) {
300. if ( _.options.infinite === false ) {
301. if ( _.direction === 1
302. ( _.currentSlide + 1 ) === ( _.slideCount - 1 )) {
303. else if ( _.direction === 0 ) {
304. if ( _.currentSlide - 1 === 0 ) {
305. if (_.options.arrows === true ) {
306. if (_.options.dots === true
307. _.$slideTrack = (_.slideCount === 0) ?
308. if (_.options.centerMode === true
309. _.options.swipeToSlide === true) {
310. _.setSlideClasses(typeof _.currentSlide === 'number' ? _.currentSlide : 0);
311. if (_.options.draggable === true) {
312. if (_.respondTo === 'window') {
313. } else if (_.respondTo === 'slider') {
314. } else if (_.respondTo === 'min') {
315. if (_.originalSettings.mobileFirst === false) {
316. if (_.breakpointSettings[targetBreakpoint] === 'unslick') {
317. if (initial === true) {
318. slideOffset = indexOffset === 0 ? _.options.slidesToScroll : _.options.slidesToShow - indexOffset
319. slideOffset = indexOffset === 0 ? _.options.slidesToScroll : indexOffset
320. var index = event.data.index === 0 ? 0 :
321. if (_.options.accessibility === true) {
322. if (_.options.arrows === true
323. if (_.options.focusOnSelect === true) {
324. if (_.shouldClick === false) {
325. if (_.options.infinite === true) {
326. } else if (_.options.centerMode === true) {
327. if (_.options.vertical === true
328. _.options.centerMode === true) {
329. if (_.options.slidesToShow === 2) {
330. } else if (_.options.slidesToShow === 1) {
331. } else if (_.options.centerMode === true
332. _.options.infinite === true) {
333. if (_.options.variableWidth === true) {
334. _.options.infinite === false) {
335. if (_.options.centerMode === true) {
336. if (_.options.infinite === false) {
337. centerOffset = _.options.centerMode === true ? _.slideWidth * Math.floor(_.options.slidesToShow / 2) : 0
338. if (_.options.swipeToSlide === true) {
339. _.options.pauseOnDotsHover === true
340. if (event.keyCode === 37
341. _.options.accessibility === true) {
342. message: _.options.rtl === true ? 'next' :
343. } else if (event.keyCode === 39
344. message: _.options.rtl === true ? 'previous' : 'next'
345. if (_.options.fade === true) {
346. if (_.options.lazyLoad === 'anticipated') {
347. } else if (_.currentSlide === 0) {
348. if (_.options.lazyLoad === 'progressive') {
349. if ( _.options.adaptiveHeight === true ) {
350. if ( $.type(responsiveSettings) === 'array'
351. _.breakpoints[l] === currentBreakpoint ) {
352. index = removeBefore === true ? 0 : _.slideCount - 1
353. index = removeBefore === true ? --index : index
354. if (removeAll === true) {
355. if (_.options.vertical === false
356. _.options.variableWidth === false) {
357. } else if (_.options.variableWidth === true) {
358. if (_.options.variableWidth === false) _.$slideTrack.children('.slick-slide').width(_.slideWidth - offset);
359. if( $.type( arguments[0] ) === 'object' ) {
360. } else if ( $.type( arguments[0] ) === 'string' ) {
361. if ( arguments[0] === 'responsive'
362. $.type( arguments[1] ) === 'array' ) {
363. if ( type === 'single' ) {
364. } else if ( type === 'multiple' ) {
365. } else if ( type === 'responsive' ) {
366. if( _.options.responsive[l].breakpoint === value[item].breakpoint ) {
367. _.positionProp = _.options.vertical === true ? 'top' : 'left'
368. if (_.positionProp === 'top') {
369. if (_.options.useCSS === true) {
370. if ( typeof _.options.zIndex === 'number' ) {
371. if (bodyStyle.perspectiveProperty === undefined
372. bodyStyle.webkitPerspective === undefined) _.animType = false;
373. bodyStyle.MozPerspective === undefined) _.animType = false;
374. if (bodyStyle.msTransform === undefined) _.animType = false;
375. var evenCoef = _.options.slidesToShow % 2 === 0 ? 1 : 0
376. if (index === 0) {
377. } else if (index === _.slideCount - 1) {
378. indexOffset = _.options.infinite === true ? _.options.slidesToShow + index : index
379. if (_.options.lazyLoad === 'ondemand'
380. _.options.lazyLoad === 'anticipated') {
381. if (_.options.infinite === true
382. _.options.fade === false) {
383. if (_.animating === true
384. _.options.waitForAnimate === true) {
385. if (_.options.fade === true
386. _.currentSlide === index) {
387. if (sync === false) {
388. _.currentLeft = _.swipeLeft === null ? slideLeft : _.swipeLeft
389. if (_.options.infinite === false
390. _.options.centerMode === false
391. } else if (_.options.infinite === false
392. _.options.centerMode === true
393. return (_.options.rtl === false ? 'left' : 'right');
394. return (_.options.rtl === false ? 'right' : 'left');
395. if (_.options.verticalSwiping === true) {
396. if ( _.touchObject.curX === undefined ) {
397. if ( _.touchObject.edgeHit === true ) {
398. if ((_.options.swipe === false) || ('ontouchend' in document
399. _.options.swipe === false)) {
400. } else if (_.options.draggable === false
401. positionOffset = (_.options.rtl === false ? 1 : -1) * (_.touchObject.curX > _.touchObject.startX ? 1 : -1);
402. if ((_.currentSlide === 0
403. swipeDirection === 'right') || (_.currentSlide >= _.getDotCount()
404. swipeDirection === 'left')) {
405. _.options.touchMove === false) {
406. if (_.animating === true) {
407. if ( _.options.arrows === true
408. if (_.currentSlide === 0) {
409. _.options.centerMode === false) {
410. //Customizable =================================================================================================

== (358 điều kiện duy nhất):
------------------------------------------------------------
1. 'vi' == params['locale']) {
2. 'en' == params['locale']) {
3. errorCode == '11'"
4. <div class="footer-button" *ngIf="(isSent == false
5. isSent == false
6. <div class="payment_again" *ngIf="rePayment && !timeOut" [class.select_only]="!(isSent == false
7. errorCode == 'overtime'
8. errorCode == '253'"
9. <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
10. errorCode == '253')">
11. if (this.res.currencies[0] == 'USD') {
12. if(_re.body.themes.theme == 'mafc'){
13. params.response_code == 'overtime') {
14. if (_re.status == '200'
15. _re.status == '201') {
16. if (_re2.status == '200'
17. _re2.status == '201') {
18. if (this.errorCode == 'overtime'
19. this.errorCode == '253') {
20. this.res.state == 'canceled') {
21. if (this.isTimePause == false) {
22. if ((dataPassed.status == '200'
23. dataPassed.status == '201') && dataPassed.body != null) {
24. if (message == '1') {
25. } else if (message == '0') {
26. if (this.locale == 'en') {
27. if (name == 'MAFC')
28. if (bankId == 3
29. bankId == 61
30. bankId == 8
31. bankId == 49
32. bankId == 48
33. bankId == 10
34. bankId == 53
35. bankId == 17
36. bankId == 65
37. bankId == 23
38. bankId == 52
39. bankId == 27
40. bankId == 66
41. bankId == 9
42. bankId == 54
43. bankId == 37
44. bankId == 38
45. bankId == 39
46. bankId == 40
47. bankId == 42
48. bankId == 44
49. bankId == 72
50. bankId == 59
51. bankId == 51
52. bankId == 64
53. bankId == 58
54. bankId == 56
55. bankId == 55
56. bankId == 60
57. bankId == 68
58. bankId == 74
59. bankId == 75
60. if (this._b == 8) {//MB Bank
61. if (this._b == 18) {//Oceanbank
62. if (this._b == 8) {
63. if (this._b == 18) {
64. if (this._b == 12) { //SHB
65. } else if (this._res.state == 'authorization_required') {
66. if (reason.code == '25'
67. if (this._b == 12) {
68. if (this._b == 18) {//8-MB Bank;18-oceanbank
69. _b == 6 "
70. token_site == 'onepay'
71. _b == 68"
72. this.data.themes.allow_save_token == false ? this.data.themes.allow_save_token : true
73. if (this._b == 1
74. this._b == 20
75. this._b == 36
76. this._b == 64
77. this._b == 55
78. this._b == 47
79. this._b == 48
80. this._b == 59) {
81. return this._b == 3
82. this._b == 9
83. this._b == 16
84. this._b == 17
85. this._b == 19
86. this._b == 25
87. this._b == 44
88. this._b == 57
89. this._b == 59
90. this._b == 61
91. this._b == 63
92. this._b == 69
93. return this._b == 6
94. this._b == 2
95. return this._b == 6 || this._b == 2 || (this._b == 5
96. this.vibFlow=='internet_banking') || this._b == 57;
97. return this._b == 18
98. if (parseInt(b) == parseInt(v.substring(0, 6))) {
99. event.inputType == 'deleteContentBackward') {
100. if (event.target.name == 'exp_date'
101. event.inputType == 'insertCompositionText') {
102. this.vibFlow=='bank_account')) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
103. if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
104. } else if (this._b == 2) {
105. } else if (this._b == 5
106. this.vibFlow == 'internet_banking')  {
107. }  else if (this._b == 5
108. this.vibFlow == 'bank_account')  {
109. } else if (this._b == 6) {
110. } else if (this._b == 31) {
111. this.token_site == 'onepay'
112. if(this._b == 12){
113. if (this._res_post.state == 'approved'
114. this._res_post.state == 'failed') {
115. } else if (this._res_post.state == 'authorization_required') {
116. this._b == 14
117. this._b == 15
118. this._b == 24
119. this._b == 8
120. this._b == 10
121. this._b == 18
122. this._b == 22
123. this._b == 23
124. this._b == 27
125. this._b == 30
126. this._b == 11
127. if (this._b == 1 || this._b == 14 || this._b == 15 || this._b == 24 || this._b == 8 || this._b == 10 || this._b == 18 || this._b == 20 || this._b == 22 || this._b == 23 || this._b == 27 || this._b == 30 || this._b == 11 || this._b == 17 || (this._b==5
128. this.vibFlow=='bank_account') || this._b == 12 || this._b == 9) {
129. this._b == 12
130. this._b == 9) {
131. if (err.status == 400
132. err.status == 500) {
133. if (err.error && (err.error.code == 13
134. err.error.code == '13')) {
135. err.error['name'] == 'INVALID_INPUT_BIN') {
136. if ((cardNo.length == 16
137. (cardNo.length == 19
138. (cardNo.length == 19 && (this._b == 1
139. this._b == 4
140. this._b == 5
141. this._b == 67))
142. this._util.checkMod10(cardNo) == true
143. return ((value.length == 4
144. value.search('/') == -1) || value.length == 5) && parseInt(value.split('/')[0], 0) > 0
145. value.length == 5) && parseInt(value.split('/')[0]
146. return this._b == 11
147. this._b == 33
148. this._b == 39
149. this._b == 43
150. this._b == 45
151. this._b == 67
152. this._b == 72
153. this._b == 73
154. this._b == 68
155. this._b == 74
156. this._b == 75
157. this._inExpDate.length == 4
158. this._inExpDate.search('/') == -1)
159. this._inExpDate.length == 5))
160. _auth==1) || (token
161. _b == 16)"
162. (token || _auth==1) && _b != 16
163. _auth==1)">
164. this._auth == 0
165. this.tokenList.length == 0) {
166. this.filteredData.length == 1
167. if ($event && ($event == 'true'
168. this._b == '55'
169. this._b == '47'
170. this._b == '48'
171. this._b == '59'
172. this._b == '67'
173. this._b == '73'
174. this._b == '12') {
175. this._b == '3'
176. this._b == '43'
177. this._b == '45'
178. this._b == '57'
179. this._b == '61'
180. this._b == '63'
181. this._b == '68'
182. this._b == '69'
183. this._b == '72'
184. this._b == '9'
185. this._b == '74'
186. this._b == '75') {
187. this._b == '36'
188. if (item['id'] == this._b) {
189. token || _auth==1
190. if (!isNaN(_re.status) && (_re.status == '200'
191. _re.status == '201') && _re.body != null) {
192. if (('closed' == this._res_polling.state
193. 'canceled' == this._res_polling.state
194. 'expired' == this._res_polling.state)
195. } else if ('paid' == this._res_polling.state) {
196. this._res_polling.payments == null
197. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'pending') {
198. this._paymentService.getCurrentPage() == 'enter_card') {
199. } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res_polling.payments.length - 1].state == 'approved'
200. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed')
201. } else if ('not_paid' == this._res_polling.state) {
202. this._res_polling.payments[this._res_polling.payments.length - 1].state == 'failed') {
203. if (this.type == 5
204. } else if (this.type == 6
205. } else if (this.type == 2
206. } else if (this.type == 7
207. } else if (this.type == 8
208. } else if (this.type == 4
209. } else if (this.type == 3
210. this._auth == 0) {
211. if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
212. this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
213. if (this._res.payments[this._res.payments.length - 1].state == 'approved'
214. } else if (this._res.payments[this._res.payments.length - 1].state == 'failed') {
215. } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
216. this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
217. } else if (idBrand == 'atm'
218. if ('paid' == this._res.state) {
219. this._res.merchant.token_site == 'onepay')) {
220. if (('closed' == this._res.state
221. 'canceled' == this._res.state
222. 'expired' == this._res.state
223. 'paid' == this._res.state)
224. this._res.payments == null) {
225. this._res.payments[this._res.payments.length - 1].state == 'pending') {
226. if (this._res.currencies[0] == 'USD') {
227. if (item.instrument.issuer.brand.id == 'atm') {
228. } else if (item.instrument.issuer.brand.id == 'visa'
229. item.instrument.issuer.brand.id == 'mastercard') {
230. if (item.instrument.issuer_location == 'd') {
231. } else if (item.instrument.issuer.brand.id == 'amex') {
232. } else if (item.instrument.issuer.brand.id == 'jcb') {
233. uniq.length == 1) {
234. _val == 2) {
235. if (_val == 2
236. } else if (_val == 2
237. _val == 2
238. if (this.type == 4) {
239. if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
240. } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
241. filteredData.length == 1) {
242. if (data._locale == 'en') {
243. data['type'] == 'Visa'
244. data['type'] == 'Master'
245. data['type'] == 'JCB'"
246. data['type'] == 'Visa'"
247. data['type'] == 'Master'"
248. data['type'] == 'Amex'"
249. i == tokenList.length - 1"
250. token_main == '1'"
251. if (message == '0') {
252. item.id == element.id ? element['active'] = true : element['active'] = false
253. item.display_cvv == true ? this.flagToken = true : this.flagToken = false
254. if (result == 'success') {
255. if (this.tokenList.length == 0) {
256. } else if (result == 'error') {
257. _val.value.trim().length == 4) || (item.brand_id != 'amex'
258. _val.value.trim().length == 3))
259. _val.value.length == 4) || (item.brand_id != 'amex'
260. _val.value.length == 3)));
261. id == 'amex' ? this.maxLength = 4 : this.maxLength = 3
262. if (_re.body.state == 'more_info_required') {
263. if (this._res_post.state == 'failed') {
264. } else if (_re.body.state == 'authorization_required') {
265. this._b==12
266. } else if (_re.body.state == 'failed') {
267. if (action == 'blur') {
268. this._i_token_otp.trim().length == 4)
269. this._i_token_otp.trim().length == 3));
270. return ((a.id == id
271. a.code == id) && a.type.includes(type));
272. if (this.currentLang == 'en') curr_lang = 'LANG_EN_NAME';
273. else if (this.currentLang == 'vi') curr_lang = 'LANG_VI_NAME';
274. if (e.name == bankSwift) { // TODO: get by swift
275. if (+e.id == bankId) {
276. if (parseInt(b) == number) {
277. if (e.swiftCode == bankSwift) {
278. if (temp.length == 0) {
279. return (counter % 10 == 0);
280. if (this.checkCount == 1) {
281. if (results == null) {
282. if (c.length == 3) {
283. d = d == undefined ? '.' : d
284. t = t == undefined ? '
285. return results == null ? null : results[1]
286. if (_dataCache == null) {
287. if ( (0 <= r && r <= 6 && (c == 0
288. c == 6) )
289. || (0 <= c && c <= 6 && (r == 0
290. r == 6) )
291. if (i == 0
292. _modules[r][6] = (r % 2 == 0);
293. _modules[6][c] = (c % 2 == 0);
294. if (r == -2
295. r == 2
296. c == -2
297. c == 2
298. || (r == 0
299. c == 0) ) {
300. ( (bits >> i) & 1) == 1);
301. if (col == 6) col -= 1;
302. if (_modules[row][col - c] == null) {
303. dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
304. if (bitIndex == -1) {
305. margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
306. if (typeof arguments[0] == 'object') {
307. margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
308. if (b == -1) throw 'eof';
309. if (b0 == -1) break;
310. if (typeof b == 'number') {
311. if ( (b & 0xff) == b) {
312. return function(i, j) { return (i + j) % 2 == 0
313. return function(i, j) { return i % 2 == 0
314. return function(i, j) { return j % 3 == 0
315. return function(i, j) { return (i + j) % 3 == 0
316. return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
317. return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
318. return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
319. return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
320. if (r == 0
321. c == 0) {
322. if (dark == qrcode.isDark(row + r, col + c) ) {
323. if (count == 0
324. count == 4) {
325. if (typeof num.length == 'undefined') {
326. num[offset] == 0) {
327. if (typeof rsBlock == 'undefined') {
328. return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
329. _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
330. if (data.length - i == 1) {
331. } else if (data.length - i == 2) {
332. } else if (n == 62) {
333. } else if (n == 63) {
334. if (_buflen == 0) {
335. if (c == '=') {
336. } else if (c == 0x2b) {
337. } else if (c == 0x2f) {
338. if (table.size() == (1 << bitLength) ) {
339. if (cardList.length == 1) {//TOKEN, BIDV, SHB, OCEAN, PVCOM, TP Bank
340. if ( $('.circle_v1').css('display') == 'block'
341. if ($('.circle_v2').css('display') == 'block'
342. $('.circle_v3').css('display') == 'block' ) {
343. $('.circle_v1').css('display') == 'block'
344. document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM'
345. if ( document.getElementsByClassName("select_online")[0].value == 'Easy Online Banking') {
346. if ( document.getElementsByClassName("select_online")[0].value == 'Easy Mobile Banking') {
347. if ( document.getElementsByClassName("select_online")[0].value == 'Thẻ ATM') {
348. if ($('.circle_v1').css('display') == 'block') {
349. if ($('.circle_v1').css('display') == 'block'
350. if ( $('.circle_v1').css('display') == 'block') {
351. else if ($('.circle_v2').css('display') == 'block') {
352. if ( $('.circle_v3').css('display') == 'block' ) {
353. if ($('.circle_v2').css('display') == 'block') {
354. x = _.positionProp == 'left' ? Math.ceil(position) + 'px' : '0px'
355. y = _.positionProp == 'top' ? Math.ceil(position) + 'px' : '0px'
356. if (_.options.slidesToShow == _.options.slidesToScroll
357. if (typeof opt == 'object'
358. typeof opt == 'undefined')

!== (136 điều kiện duy nhất):
------------------------------------------------------------
1. this.lastValue !== $event.target.value)) {
2. if (YY % 400 === 0 || (YY % 100 !== 0
3. if (YYYY % 400 === 0 || (YYYY % 100 !== 0
4. codeResponse.toString() !== '0') {
5. this._util.getElementParams(this.currentUrl, 'try_again') !== '0') {
6. if (_val !== 3) {
7. this._util.getElementParams(this.currentUrl, 't_id') !== '') {
8. queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
9. if (queryString !== '') {
10. if (target !== 0
11. if (e !== null) {
12. if (lang !== "vi") lang = "en";
13. this.oldValue !== this.value) {
14. if (jLinks !== undefined
15. jLinks !== null) {
16. if (jMerchantReturn !== undefined
17. jMerchantReturn !== null) {
18. if (responseCode !== undefined
19. responseCode !== null
20. if (parentRes !== "{
21. if (value !== "") {
22. if (inMonth.value !== ""
23. if (inYear.value !== ""
24. var month = inMonth.value !== ""
25. var year = parseInt("20" + (inYear.value !== ""
26. if ("2D" !== order["vpc_VerType"]) return err("MISSING_FIELD"
27. inMonth.value !== tokenInsMonth) instrument["month"] = inMonth.value;
28. inYear.value !== tokenInsYear) instrument["year"] = inYear.value;
29. if (inCvv.value !== "") instrument["cvv"] = inCvv.value;
30. if (inType.style.display !== "none") instrument.type = inType.options[inType.selectedIndex].value;
31. if (inNumber.style.display !== "none") instrument.number = inNumber.value;
32. if (inDate.style.display !== "none") instrument.date = inDate.value;
33. if (inCsc.style.display !== "none") instrument.csc = inCsc.value;
34. if (inPhone.style.display !== "none") instrument.phone = inPhone.value;
35. if (inName.style.display !== "none") instrument.name = inName.value;
36. typeof module !== 'undefined' ? factory(exports, require('jquery'), require('popper.js')) :
37. hrefAttr !== '#' ? hrefAttr.trim() : ''
38. $(this._element).css('visibility') !== 'hidden') {
39. if (selector !== null
40. if (selector !== null) {
41. if (typeof this._config.parent.jquery !== 'undefined') {
42. if (typeof this._config.reference.jquery !== 'undefined') {
43. if (this._config.boundary !== 'scrollParent') {
44. if (this._popper !== null) {
45. event.which !== TAB_KEYCODE)) {
46. event.which !== ESCAPE_KEYCODE
47. if (/input|textarea/i.test(event.target.tagName) ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE && (event.which !== ARROW_DOWN_KEYCODE
48. event.which !== ARROW_UP_KEYCODE
49. this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {
50. if (document !== event.target
51. _this5._element !== event.target
52. if (event.target !== event.currentTarget) {
53. if (typeof margin !== 'undefined') {
54. if (allowedAttributeList.indexOf(attrName) !== -1) {
55. if (uriAttrs.indexOf(attrName) !== -1) {
56. var isInTheDom = $.contains(shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement
57. if (_this2._hoverState !== HoverState.SHOW
58. if (_this2._popper !== null) {
59. if (data.originalPlacement !== data.placement) {
60. } else if (trigger !== Trigger.MANUAL) {
61. titleType !== 'string') {
62. if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {
63. if (this.constructor.Default[key] !== this.config[key]) {
64. if (tabClass !== null
65. if (tip.getAttribute('x-placement') !== null) {
66. if (typeof config.target !== 'string') {
67. if (this._scrollHeight !== scrollHeight) {
68. if (this._activeTarget !== target) {
69. var isActiveTarget = this._activeTarget !== this._targets[i]
70. 'use strict';(function(Y){function C(c,a,b){var e=0,h=[],n=0,g,l,d,f,m,q,u,r,I=!1,v=[],w=[],t,y=!1,z=!1,x=-1;b=b||{};g=b.encoding||"UTF8";t=b.numRounds||1;if(t!==parseInt(t,10)
71. 0!==f%32
72. a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if(!0===c.hasOwnProperty("shakeLen")){if(0!==c.shakeLen%8)throw Error("shakeLen must be a multiple of 8");a.shakeLen=c.shakeLen}if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function M(c,a,b){switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8
73. }switch(c){case "HEX":c=function(a,c,d){var g=a.length,l,p,f,m,q,u;if(0!==g%2)throw Error("String of HEX type must be in byte increments");c=c||[0];d=d||0;q=d>>>3;u=-1===b?3:0;for(l=0
74. 1!==b
75. "UTF16LE"!==a
76. "");if(-1!==p
77. function(a,c,d){var g,l,p,f,m,q;c=c||[0];d=d||0;l=d>>>3;m=-1===b?3:0;q=new Uint8Array(a);for(g=0;g<a.byteLength;g+=1)f=g+l,p=f>>>2,c.length<=p&&c.push(0),c[p]|=q[g]<<8*(m+f%4*b);return{value:c,binLen:8*a.byteLength+d}};break;default:throw Error("format must be HEX, TEXT, B64, BYTES, or ARRAYBUFFER");}return c}function y(c,a){return c<<a|c>>>32-a}function S(c,a){return 32<a?(a-=32,new b(c.b<<a|c.a>>>32-a,c.a<<a|c.b>>>32-a)):0!==a?new b(c.a<<a|c.b>>>32-a,c.b<<a|c.a>>>32-a):c}function w(c,a){return c>>>
78. a[7]);return a}function D(c,a){var d,e,h,n,g=[],l=[];if(null!==c)for(e=0
79. define.amd?define(function(){return C}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=C),exports=C):Y.jsSHA=C})(this);
80. return C}):"undefined"!==typeof exports?("undefined"!==typeof module
81. 'use strict';(function(I){function w(c,a,d){var l=0,b=[],g=0,f,n,k,e,h,q,y,p,m=!1,t=[],r=[],u,z=!1;d=d||{};f=d.encoding||"UTF8";u=d.numRounds||1;if(u!==parseInt(u,10)
82. l+=1)g[l]=c[l>>>2]>>>8*(3+l%4*-1)&255;return b}function C(c){var a={outputUpper:!1,b64Pad:"=",shakeLen:-1};c=c||{};a.outputUpper=c.outputUpper||!1;!0===c.hasOwnProperty("b64Pad")&&(a.b64Pad=c.b64Pad);if("boolean"!==typeof a.outputUpper)throw Error("Invalid outputUpper formatting option");
83. if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0;d<f;d+=2){k=parseInt(a.substr(d,2),16);if(isNaN(k))throw Error("String of HEX type contains invalid characters");
84. if("string"!==typeof a.b64Pad)throw Error("Invalid b64Pad formatting option");return a}function B(c,a){var d;switch(a){case "UTF8":case "UTF16BE":case "UTF16LE":break;default:throw Error("encoding must be UTF8, UTF16BE, or UTF16LE");}switch(c){case "HEX":d=function(a,b,c){var f=a.length,d,k,e,h,q;if(0!==f%2)throw Error("String of HEX type must be in byte increments");b=b||[0];c=c||0;q=c>>>3;for(d=0
85. "");if(-1!==k
86. define.amd?define(function(){return w}):"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(module.exports=w),exports=w):I.jsSHA=w})(this);
87. return w}):"undefined"!==typeof exports?("undefined"!==typeof module
88. } else if (typeof exports !== 'undefined') {
89. if (typeof document.mozHidden !== 'undefined') {
90. } else if (typeof document.webkitHidden !== 'undefined') {
91. asNavFor !== null ) {
92. if ( asNavFor !== null
93. if (_.options.infinite !== true) {
94. _.options.responsive !== null) {
95. if (targetBreakpoint !== null) {
96. if (_.activeBreakpoint !== null) {
97. if (targetBreakpoint !== _.activeBreakpoint
98. triggerBreakpoint !== false ) {
99. unevenOffset = (_.slideCount % _.options.slidesToScroll !== 0);
100. _.$dots !== null) {
101. if (filter !== null) {
102. if (_.slideCount % _.options.slidesToScroll !== 0) {
103. if (_.$dots !== null) {
104. if (slideControlIndex !== -1) {
105. _.currentSlide !== 0) {
106. if ($(window).width() !== _.windowWidth) {
107. } else if ( typeof arguments[1] !== 'undefined' ) {
108. if( $.type( _.options.responsive ) !== 'array' ) {
109. if (bodyStyle.WebkitTransition !== undefined
110. bodyStyle.MozTransition !== undefined
111. bodyStyle.msTransition !== undefined) {
112. if (bodyStyle.OTransform !== undefined) {
113. if (bodyStyle.MozTransform !== undefined) {
114. if (bodyStyle.webkitTransform !== undefined) {
115. if (bodyStyle.msTransform !== undefined) {
116. if (bodyStyle.transform !== undefined
117. _.animType !== false) {
118. _.transformsEnabled = _.options.useTransform && (_.animType !== null
119. _.animType !== false);
120. if (dontAnimate !== true
121. if (dontAnimate !== true) {
122. if ( _.touchObject.startX !== _.touchObject.curX ) {
123. event.type.indexOf('mouse') !== -1) {
124. event.originalEvent.touches !== undefined ?
125. touches = event.originalEvent !== undefined ? event.originalEvent.touches : null
126. touches.length !== 1) {
127. _.touchObject.curX = touches !== undefined ? touches[0].pageX : event.clientX
128. _.touchObject.curY = touches !== undefined ? touches[0].pageY : event.clientY
129. if (event.originalEvent !== undefined
130. if (_.touchObject.fingerCount !== 1
131. event.originalEvent.touches !== undefined) {
132. _.touchObject.startX = _.touchObject.curX = touches !== undefined ? touches.pageX : event.clientX
133. _.touchObject.startY = _.touchObject.curY = touches !== undefined ? touches.pageY : event.clientY
134. if (_.$slidesCache !== null) {
135. //if (event.origin !== "http://example.com:8080") return;
136. /*if (contentType !== my_expected_type) {

!= (158 điều kiện duy nhất):
------------------------------------------------------------
1. if (params['locale'] != null
2. } else if (params['locale'] != null
3. if (userLang != null
4. if(value!=null){
5. if (this.res != null
6. this.res.links != null
7. this.res.links.merchant_return != null
8. this.res.links.merchant_return.href != null) {
9. if (this._idInvoice != null
10. this._idInvoice != 0) {
11. if (this._paymentService.getInvoiceDetail() != null) {
12. dataPassed.body != null) {
13. dataPassed.body.themes.contact != true ? dataPassed.body.themes.contact : true
14. dataPassed.body.themes.border_color != true ? dataPassed.body.themes.border_color : ''
15. if (this._translate.currentLang != language) {
16. if (this._res.links != null
17. this._res.links.merchant_return != null
18. this._res.links.merchant_return.href != null) {
19. this.challengeCode != '') {
20. if (this._res_post != null
21. this._res_post.links != null
22. this._res_post.links.merchant_return != null
23. this._res_post.links.merchant_return.href != null) {
24. if (!(_formCard.otp != null
25. if (!(_formCard.password != null
26. <div class="form-field form-field-small margin-right-2" [class.visible_hidden]="!valid_card" *ngIf="d_card_date && (_b != 3
27. d_card_date && (_b != 3 && _b != 19 && _b != 18)
28. _b != 18)">
29. *ngIf="(_b != 18
30. (_b != 18 && _b != 6 && _b != 2 )
31. _b != 2 )">
32. _b != 6 && _b != 2
33. this.data.merchant.id != 'OPTEST' ? this.data.on_off_bank : ''
34. if (this.htmlDesc != null
35. if (ua.indexOf('safari') != -1
36. if (this._inExpDate.length != 3) {
37. if ((this._b != 3
38. this._b != 9
39. this._b != 16
40. this._b != 17
41. this._b != 18
42. this._b != 19
43. this._b != 25
44. this._b != 44
45. this._b != 54
46. this._b != 57
47. this._b != 59
48. this._b != 61
49. this._b != 63
50. this._b != 69
51. this._b != 6
52. this._b != 2
53. this._b != 5) || (this._b==5
54. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72' , '73', '74', '75'].indexOf(this._b.toString()) != -1) {
55. if (this._res_post.return_url != null) {
56. if (this._res_post.links != null
57. if (this._res_post.authorization != null
58. this._res_post.authorization.links != null
59. this._res_post.links.cancel != null) {
60. let userName = _formCard.name != null ? _formCard.name : ''
61. this._res_post.authorization.links.approval != null
62. this._res_post.authorization.links.approval.href != null) {
63. userName = paramUserName != null ? paramUserName : ''
64. if (params['locale'] != null) {
65. if ('otp' != this._paymentService.getCurrentPage()) {
66. if (!(strInstrument != null
67. if (strInstrument.substring(0, 1) != '^'
68. strInstrument.substr(strInstrument.length - 1) != '$') {
69. this._paymentService.getState() != 'error') {
70. if (this._paymentService.getCurrentPage() != 'otp') {
71. _re.body != null) {
72. this._res_polling.links != null
73. this._res_polling.links.merchant_return != null //
74. } else if (this._res_polling.merchant != null
75. this._res_polling.merchant_invoice_reference != null
76. } else if (this._res_polling.payments != null
77. this._res_polling.links.merchant_return != null//
78. this._res_polling.payments != null
79. if (this._res_polling.payments != null
80. t_id != this._util.getElementParams(this.currentUrl, 't_id')) {
81. if (count != 1) {
82. if (this._res.merchant != null
83. this._res.merchant_invoice_reference != null) {
84. if (this._res.merchant.address_details != null) {
85. this._res.links != null//
86. } else if (this._res.payments != null
87. this._res.payments[this._res.payments.length - 1].instrument != null
88. this._res.payments[this._res.payments.length - 1].instrument.issuer != null
89. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
90. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
91. this._res.payments[this._res.payments.length - 1].links != null
92. this._res.payments[this._res.payments.length - 1].links.cancel != null
93. this._res.payments[this._res.payments.length - 1].links.cancel.href != null
94. this._res.payments[this._res.payments.length - 1].links.update != null
95. this._res.payments[this._res.payments.length - 1].links.update.href != null) {
96. this._res.payments[this._res.payments.length - 1].instrument.issuer.swift_code != null
97. this._res.payments[this._res.payments.length - 1].authorization != null
98. this._res.payments[this._res.payments.length - 1].authorization.links != null) {
99. this._res.payments[this._res.payments.length - 1].authorization.links.approval != null
100. this._res.payments[this._res.payments.length - 1].authorization.links.approval.href != null) {
101. if (this._res.payments[this._res.payments.length - 1].authorization != null
102. this._res.payments[this._res.payments.length - 1].authorization.links != null
103. auth = paramUserName != null ? paramUserName : ''
104. this._res.links != null
105. this._res.links.merchant_return != null //
106. } else if (this._res.merchant != null
107. this._res.merchant_invoice_reference != null
108. if (['mastercard', 'visa', 'amex', 'jcb', 'cup', 'card', 'np_card', 'atm'].indexOf(id) != -1) {
109. } else if (['techcombank_account', 'vib_account', 'dongabank_account', 'tpbank_account', 'bidv_account', 'pvcombank_account', 'shb_account', 'oceanbank_online_account'].indexOf(id) != -1) {
110. } else if (['oceanbank_mobile_account'].indexOf(id) != -1) {
111. } else if (['shb_customer_id'].indexOf(id) != -1) {
112. if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1) {
113. return ['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(id) != -1
114. } else if (this._res.payments != null) {
115. } else if (this._res_post.links != null
116. item['feeService']['fee'] != 0
117. item['feeService']['fee'] != 0"
118. this._res.merchant.id != 'OPTEST' ? this._res.on_off_bank : ''
119. if (_val.value != null
120. this.c_token_otp_csc = !(_val.value != null
121. if (_re.body.authorization != null
122. _re.body.authorization.links != null
123. if (_re.body.links != null
124. _re.body.links.cancel != null) {
125. if (_re.body.return_url != null) {
126. } else if (_re.body.links != null
127. _re.body.links.merchant_return != null
128. _re.body.links.merchant_return.href != null) {
129. if (['mastercard', 'visa', 'amex', 'jcb', 'cup'].indexOf(item.brand_id) != -1
130. return this._i_token_otp != null
131. || (id != 'amex'
132. if (idInvoice != null
133. idInvoice != 0)
134. idInvoice != 0) {
135. if (this._merchantid != null
136. this._tranref != null
137. this._state != null
138. let urlCalcel = this._url_post_token_id + (idInvoice != null ? idInvoice : '') + '/token/' + tokenId;
139. urlCalcel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
140. if (tem != null) {
141. if ((tem = ua.match(/version\/(\d+)/i)) != null) {
142. if (_modules[r][6] != null) {
143. if (_modules[6][c] != null) {
144. if (_modules[row][col] != null) {
145. while (buffer.getLengthInBits() % 8 != 0) {
146. if (count != numChars) {
147. throw count + ' != ' + numChars
148. while (data != 0) {
149. if (test.length != 2
150. ( (test[0] << 8) | test[1]) != code) {
151. if (_length % 3 != 0) {
152. if ( (data >>> length) != 0) {
153. return typeof _map[key] != 'undefined'
154. var source = arguments[i] != null ? arguments[i] : {
155. $('[draggable!=true]', _.$slideTrack).off('dragstart', _.preventDefault);
156. $('[draggable!=true]', _.$slideTrack).on('dragstart', _.preventDefault);
157. if( direction != 'vertical' ) {
158. if (typeof ret != 'undefined') return ret;

