====================================================================================================
TRÍCH XUẤT ĐIỀU KIỆN SO SÁNH TỪ CÁC FILE TYPESCRIPT/JAVASCRIPT/HTML
====================================================================================================
Thư mục/File nguồn: /root/projects/onepay/paygate-dienmayxanh/src
Thời gian: 18:12:28 18/8/2025
Tổng số file xử lý: 70
Tổng số file bị bỏ qua: 0
Tổng số điều kiện tìm thấy: 1244

THỐNG KÊ TỔNG QUAN THEO LOẠI TOÁN TỬ:
--------------------------------------------------
Strict equality (===): 166 lần
Loose equality (==): 728 lần
Strict inequality (!==): 26 lần
Loose inequality (!=): 324 lần
--------------------------------------------------

DANH SÁCH FILE ĐÃ XỬ LÝ:
--------------------------------------------------
1. 4en.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/4en.html
2. 4vn.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/4vn.html
3. app-routing.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/app-routing.module.ts
4. app.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/app.component.html
5. app.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/app.component.spec.ts
6. app.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/app.component.ts
7. app.module.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/app.module.ts
8. dmx-anbinhbank.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-anbinhbank/dmx-anbinhbank.component.html
9. dmx-anbinhbank.component.ts (31 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-anbinhbank/dmx-anbinhbank.component.ts
10. dmx-anbinhbank-auth.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-anbinhbank-auth/dmx-anbinhbank-auth.component.html
11. dmx-anbinhbank-auth.component.ts (18 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-anbinhbank-auth/dmx-anbinhbank-auth.component.ts
12. dmx-bankaccount-banks.component.html (14 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-bankaccount-banks/dmx-bankaccount-banks.component.html
13. dmx-bankaccount-banks.component.ts (183 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-bankaccount-banks/dmx-bankaccount-banks.component.ts
14. dmx-international-banks.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-international/dmx-international-banks.component.html
15. dmx-international-banks.component.ts (55 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-international/dmx-international-banks.component.ts
16. dmx-onepay-napas.component.html (16 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-onepay-napas/dmx-onepay-napas.component.html
17. dmx-onepay-napas.component.ts (117 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-onepay-napas/dmx-onepay-napas.component.ts
18. dmx-shb.component.html (7 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-shb-banks/dmx-shb.component.html
19. dmx-shb.component.ts (101 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-shb-banks/dmx-shb.component.ts
20. dmx-techcombank-banks.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-techcombank-banks/dmx-techcombank-banks.component.html
21. dmx-techcombank-banks.component.ts (15 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-techcombank-banks/dmx-techcombank-banks.component.ts
22. dmx-vibbank.component.html (12 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-vibbank/dmx-vibbank.component.html
23. dmx-vibbank.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-vibbank/dmx-vibbank.component.spec.ts
24. dmx-vibbank.component.ts (103 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-vibbank/dmx-vibbank.component.ts
25. dmx-vietcombank-banks.component.html (13 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-vietcombank-banks/dmx-vietcombank-banks.component.html
26. dmx-vietcombank-banks.component.ts (104 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-vietcombank-banks/dmx-vietcombank-banks.component.ts
27. counter.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/counter.directive.spec.ts
28. counter.directive.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/counter.directive.ts
29. bank-amount-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/dien-may-xanh/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
30. bank-amount-dialog.component.ts (34 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/dien-may-xanh/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
31. dien-may-xanh.component.html (7 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/dien-may-xanh/dien-may-xanh.component.html
32. dien-may-xanh.component.ts (252 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/dien-may-xanh/dien-may-xanh.component.ts
33. off-bank-dialog.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/dien-may-xanh/off-bank-dialog/off-bank-dialog.html
34. off-bank-dialog.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/dien-may-xanh/off-bank-dialog/off-bank-dialog.ts
35. policy-dialog.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/dien-may-xanh/policy-dialog/policy-dialog/policy-dialog.component.html
36. policy-dialog.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/dien-may-xanh/policy-dialog/policy-dialog/policy-dialog.component.ts
37. format-carno-input.derective.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/directives/format-carno-input.derective.ts
38. uppercase-input.directive.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/directives/uppercase-input.directive.ts
39. error.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/error/error.component.html
40. error.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/error/error.component.spec.ts
41. error.component.ts (1 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/error/error.component.ts
42. error_international.component.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/error_international/error_international.component.html
43. error_international.component.ts (25 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/error_international/error_international.component.ts
44. format-date.directive.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/format-date.directive.spec.ts
45. format-date.directive.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/format-date.directive.ts
46. main.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/main/main.component.html
47. main.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/main/main.component.spec.ts
48. main.component.ts (6 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/main/main.component.ts
49. overlay.component.html (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/overlay/overlay.component.html
50. overlay.component.spec.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/overlay/overlay.component.spec.ts
51. overlay.component.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/overlay/overlay.component.ts
52. bank-amount.pipe.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/pipe/bank-amount.pipe.ts
53. data.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/services/data.service.ts
54. payment.service.ts (10 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/services/payment.service.ts
55. index.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/translate/index.ts
56. lang-en.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/translate/lang-en.ts
57. lang-vi.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/translate/lang-vi.ts
58. translate.pipe.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/translate/translate.pipe.ts
59. translate.service.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/translate/translate.service.ts
60. translations.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/translate/translations.ts
61. banks-info.ts (2 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/util/banks-info.ts
62. util.ts (20 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/util/util.ts
63. qrcode.js (67 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/assets/script/qrcode.js
64. environment.prod.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/environments/environment.prod.ts
65. environment.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/environments/environment.ts
66. index.html (5 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/index.html
67. karma.conf.js (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/karma.conf.js
68. main.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/main.ts
69. polyfills.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/polyfills.ts
70. test.ts (0 điều kiện)
   Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/test.ts

====================================================================================================
CHI TIẾT TỪNG FILE:
====================================================================================================

📁 FILE 1: 4en.html
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/4en.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 2: 4vn.html
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/4vn.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 3: app-routing.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/app-routing.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 4: app.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/app.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 5: app.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/app.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 6: app.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/app.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 1 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 76] return lang === this._translate.currentLang

== (2 điều kiện):
  1. [Dòng 57] 'vi'==params['locale']){
  2. [Dòng 59] 'en'==params['locale']){

!= (3 điều kiện):
  1. [Dòng 57] if(params['locale']!=null
  2. [Dòng 59] }else if(params['locale']!=null
  3. [Dòng 66] if(userLang!=null

================================================================================

📁 FILE 7: app.module.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/app.module.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 8: dmx-anbinhbank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-anbinhbank/dmx-anbinhbank.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 9: dmx-anbinhbank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-anbinhbank/dmx-anbinhbank.component.ts
📊 Thống kê: 31 điều kiện duy nhất
   - === : 0 lần
   - == : 17 lần
   - !== : 1 lần
   - != : 13 lần
--------------------------------------------------------------------------------

== (17 điều kiện):
  1. [Dòng 58] if (_re.status == '200'
  2. [Dòng 58] _re.status == '201') {
  3. [Dòng 82] if ((_val.value.length == 16
  4. [Dòng 82] _val.value.length == 19) && this.checkMod10(_val.value) == true) {
  5. [Dòng 82] this.checkMod10(_val.value) == true) {
  6. [Dòng 90] _val.value.length == 4) && _val.value.search('/') == -1) || (_val.value && _val.value.length == 5)) {
  7. [Dòng 90] _val.value.search('/') == -1) || (_val.value
  8. [Dòng 90] _val.value.length == 5)) {
  9. [Dòng 102] if (temp.length == 0) {
  10. [Dòng 109] return (counter % 10 == 0);
  11. [Dòng 184] if (this._res.state == 'approved'
  12. [Dòng 184] this._res.state == 'failed') {
  13. [Dòng 215] } else if (this._res.state == 'authorization_required') {
  14. [Dòng 269] if (!((_formCard.card_number.length == 16
  15. [Dòng 269] _formCard.card_number.length == 19) && this.checkMod10(_formCard.card_number) == true && _formCard.card_number.startsWith('970425'))) {
  16. [Dòng 269] this.checkMod10(_formCard.card_number) == true
  17. [Dòng 275] if (expdate.length == 5) {

!== (1 điều kiện):
  1. [Dòng 190] codeResponse.toString() !== '0') {

!= (13 điều kiện):
  1. [Dòng 44] if (params['locale'] != null) {
  2. [Dòng 71] if (this._res != null
  3. [Dòng 71] this._res.links != null
  4. [Dòng 71] this._res.links.merchant_return != null
  5. [Dòng 72] this._res.links.merchant_return.href != null) {
  6. [Dòng 121] if (this.current != 'card') {
  7. [Dòng 130] if (this.current != 'date') {
  8. [Dòng 139] if (this.current != 'name') {
  9. [Dòng 219] if (this._res.authorization != null
  10. [Dòng 219] this._res.authorization.links != null
  11. [Dòng 222] if (this._res.links != null
  12. [Dòng 222] this._res.links.cancel != null) {
  13. [Dòng 287] if (!(_formCard.name != null

================================================================================

📁 FILE 10: dmx-anbinhbank-auth.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-anbinhbank-auth/dmx-anbinhbank-auth.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 11: dmx-anbinhbank-auth.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-anbinhbank-auth/dmx-anbinhbank-auth.component.ts
📊 Thống kê: 18 điều kiện duy nhất
   - === : 0 lần
   - == : 10 lần
   - !== : 1 lần
   - != : 7 lần
--------------------------------------------------------------------------------

== (10 điều kiện):
  1. [Dòng 81] if (this._b == 8) {//MB Bank
  2. [Dòng 86] if (this._b == 18) {//Oceanbank
  3. [Dòng 112] if (this._b == 8) {
  4. [Dòng 117] if (this._b == 18) {
  5. [Dòng 122] if (this._b == 12) { //SHB
  6. [Dòng 139] if (_re.status == '200'
  7. [Dòng 139] _re.status == '201') {
  8. [Dòng 148] } else if (this._res.state == 'authorization_required') {
  9. [Dòng 244] if (this._b == 18) {//18-oceanbank
  10. [Dòng 263] if (this._b == 12) {

!== (1 điều kiện):
  1. [Dòng 159] codeResponse.toString() !== '0') {

!= (7 điều kiện):
  1. [Dòng 144] if (this._res.links != null
  2. [Dòng 144] this._res.links.merchant_return != null
  3. [Dòng 144] this._res.links.merchant_return.href != null) {
  4. [Dòng 218] if (this._res != null
  5. [Dòng 218] this._res.links != null
  6. [Dòng 239] if (!(_formCard.otp != null
  7. [Dòng 245] if (!(_formCard.password != null

================================================================================

📁 FILE 12: dmx-bankaccount-banks.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-bankaccount-banks/dmx-bankaccount-banks.component.html
📊 Thống kê: 14 điều kiện duy nhất
   - === : 7 lần
   - == : 7 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (7 điều kiện):
  1. [Dòng 15] cardTypeOcean === 'ATM' ? 'pg_selected': 'pg_un_selected'"
  2. [Dòng 21] cardTypeOcean === 'MB' ? 'pg_selected': 'pg_un_selected'"
  3. [Dòng 27] cardTypeOcean === 'IB' ? 'pg_selected': 'pg_un_selected'"
  4. [Dòng 75] cardTypeBank === 'bank_card_number' ? 'pg_selected': 'pg_un_selected'"
  5. [Dòng 81] cardTypeBank === 'bank_account_number' ? 'pg_selected': 'pg_un_selected'"
  6. [Dòng 87] cardTypeBank === 'bank_username' ? 'pg_selected': 'pg_un_selected'"
  7. [Dòng 93] cardTypeBank === 'bank_customer_code' ? 'pg_selected': 'pg_un_selected'"

== (7 điều kiện):
  1. [Dòng 6] <div [ngClass]="(this._b == 19
  2. [Dòng 6] this._b == 12
  3. [Dòng 6] this._b == 18
  4. [Dòng 188] this._b == 27
  5. [Dòng 188] this._b == 3 ? 'dmx-form-card margin-top-62' : 'dmx-form-card'"
  6. [Dòng 189] this._b == 14
  7. [Dòng 189] this._b == 3 ? 'd_text_err top-60' : 'd_text_err'"

================================================================================

📁 FILE 13: dmx-bankaccount-banks.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-bankaccount-banks/dmx-bankaccount-banks.component.ts
📊 Thống kê: 183 điều kiện duy nhất
   - === : 44 lần
   - == : 102 lần
   - !== : 3 lần
   - != : 34 lần
--------------------------------------------------------------------------------

=== (44 điều kiện):
  1. [Dòng 100] if (target.tagName === 'A'
  2. [Dòng 138] if (isIE[0] === 'MSIE'
  3. [Dòng 138] +isIE[1] === 10) {
  4. [Dòng 258] if (this.cardTypeBank === 'bank_card_number') {
  5. [Dòng 263] } else if (this.cardTypeBank === 'bank_account_number') {
  6. [Dòng 269] } else if (this.cardTypeBank === 'bank_username') {
  7. [Dòng 273] } else if (this.cardTypeBank === 'bank_customer_code') {
  8. [Dòng 279] this.cardTypeBank === 'bank_card_number'
  9. [Dòng 285] if (this.cardTypeOcean === 'IB') {
  10. [Dòng 289] } else if (this.cardTypeOcean === 'MB') {
  11. [Dòng 293] } else if (this.cardTypeOcean === 'ATM') {
  12. [Dòng 416] if (_val.value === ''
  13. [Dòng 416] _val.value === null
  14. [Dòng 416] _val.value === undefined) {
  15. [Dòng 418] this.cardTypeOcean === 'MB') {
  16. [Dòng 426] this.cardTypeOcean === 'IB'
  17. [Dòng 432] if ((this.cardTypeBank === 'bank_card_number'
  18. [Dòng 469] if (event.keyCode === 8
  19. [Dòng 469] event.key === "Backspace"
  20. [Dòng 523] if (v.length === 2
  21. [Dòng 523] this.flag.length === 3
  22. [Dòng 523] this.flag.charAt(this.flag.length - 1) === '/') {
  23. [Dòng 527] if (v.length === 1) {
  24. [Dòng 529] } else if (v.length === 2) {
  25. [Dòng 532] v.length === 2) {
  26. [Dòng 540] if (len === 2) {
  27. [Dòng 696] this.cardTypeBank === 'bank_account_number') {//check rieng voi shb
  28. [Dòng 911] if ((this._b == 27) && ((this.cardTypeBank === 'bank_card_number'
  29. [Dòng 912] (this.cardTypeBank === 'bank_account_number'
  30. [Dòng 916] if ((this._b == 3) && ((this.cardTypeBank === 'bank_card_number'
  31. [Dòng 921] if ((this._b == 12) && ((this.cardTypeBank === 'bank_card_number'
  32. [Dòng 923] (this.cardTypeBank === 'bank_customer_code'
  33. [Dòng 923] cardNo.length === 0)))) {
  34. [Dòng 927] if ((this._b == 19) && ((this.cardTypeBank === 'bank_card_number'
  35. [Dòng 929] (this.cardTypeBank === 'bank_username'
  36. [Dòng 977] if ((this.cardTypeBank === 'bank_account_number'
  37. [Dòng 977] this.cardTypeBank === 'bank_username'
  38. [Dòng 977] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  39. [Dòng 982] this.cardTypeOcean === 'ATM')
  40. [Dòng 983] || (this.cardTypeOcean === 'IB'
  41. [Dòng 1040] if (valIn === 1) {
  42. [Dòng 1062] } else if (valIn === 2) {
  43. [Dòng 1080] } else if (valIn === 3) {
  44. [Dòng 1091] } else if (valIn === 4) {

== (102 điều kiện):
  1. [Dòng 152] if (this._b == 19) {//19BIDV
  2. [Dòng 160] } else if (this._b == 3
  3. [Dòng 160] this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  4. [Dòng 168] } else if (this._b == 12) {// 12SHB
  5. [Dòng 173] } else if (this._b == 14) {// 14VPB
  6. [Dòng 180] } else if (this._b == 18) { //18Oceanbank-ocb
  7. [Dòng 209] if (_re.status == '200'
  8. [Dòng 209] _re.status == '201') {
  9. [Dòng 244] if (this._b == 14) {//** reset check hiển thị theo card date 14-vpbank ; update để mặc định hiển thị date
  10. [Dòng 257] if (this._b == 19
  11. [Dòng 257] this._b == 3
  12. [Dòng 257] this._b == 27
  13. [Dòng 257] this._b == 12) {
  14. [Dòng 284] } else if (this._b == 18) {
  15. [Dòng 311] if (this.checkBin(_val.value) && (this._b == 14)) {
  16. [Dòng 315] if (this._b == 18
  17. [Dòng 315] this.cardTypeOcean == 'ATM') {
  18. [Dòng 321] if ((this._b == 3
  19. [Dòng 321] this._b == 14)) {//** check đúng số thẻ 3-tpb;27-pvcombank;14-vpbank chuyển qua ô nhập date
  20. [Dòng 418] } else if (this._b == 18
  21. [Dòng 432] this._b == 18)) {
  22. [Dòng 469] event.inputType == 'deleteContentBackward') {
  23. [Dòng 470] if (event.target.name == 'exp_date'
  24. [Dòng 478] event.inputType == 'insertCompositionText') {
  25. [Dòng 493] _val.value.length == 4) && _val.value.search('/') == -1) || (_val.value && _val.value.length == 5)) && this._util.checkValidMonthYear(_val.value)) {
  26. [Dòng 493] _val.value.search('/') == -1) || (_val.value
  27. [Dòng 493] _val.value.length == 5)) && this._util.checkValidMonthYear(_val.value)) {
  28. [Dòng 514] valueDate.length == 4) && valueDate.search('/') == -1) || (valueDate && valueDate.length == 5))
  29. [Dòng 514] valueDate.search('/') == -1) || (valueDate
  30. [Dòng 514] valueDate.length == 5))
  31. [Dòng 517] || (((this._b == 20
  32. [Dòng 517] this._b == 33
  33. [Dòng 518] this._b == 39
  34. [Dòng 518] this._b == 43
  35. [Dòng 518] this._b == 45
  36. [Dòng 518] this._b == 73
  37. [Dòng 518] this._b == 64
  38. [Dòng 518] this._b == 67) && this._util.checkValidMonthYearSeaBank(valueDate)))))
  39. [Dòng 611] if (temp.length == 0) {
  40. [Dòng 618] return (counter % 10 == 0);
  41. [Dòng 629] if (this.current != 'card' && (this._b == 3
  42. [Dòng 629] this._b == 14) && this.d_card_demo) {
  43. [Dòng 642] if (this.current != 'date' && (this._b == 3
  44. [Dòng 655] if (this.current != 'name' && (this._b == 3
  45. [Dòng 668] if (this.current != 'phone' && (this._b == 14) && this.d_card_demo) {
  46. [Dòng 688] if (this._b == 3) {
  47. [Dòng 690] } else if (this._b == 14) {
  48. [Dòng 692] } else if (this._b == 19) {
  49. [Dòng 694] } else if (this._b == 27) {
  50. [Dòng 696] } else if (this._b == 12
  51. [Dòng 708] _formCard.exp_date.length == 5
  52. [Dòng 708] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
  53. [Dòng 708] this._b == 14)) {//27-pvcombank;3-TPB ;14-VPB
  54. [Dòng 713] if (_formCard.name != null && _formCard.name.length > 0 && (this._b == 3
  55. [Dòng 713] this._b == 14
  56. [Dòng 713] this._b == 19
  57. [Dòng 713] this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
  58. [Dòng 716] if (this._b == 14) {//;14-VPB thêm số phone
  59. [Dòng 719] if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
  60. [Dòng 723] if (this.cardTypeOcean == 'IB') {
  61. [Dòng 725] } else if (this.cardTypeOcean == 'MB') {
  62. [Dòng 727] } else if (this.cardTypeOcean == 'ATM') {
  63. [Dòng 764] if (this._res_post.state == 'approved'
  64. [Dòng 764] this._res_post.state == 'failed') {
  65. [Dòng 771] } else if (this._res_post.state == 'authorization_required') {
  66. [Dòng 787] if (this._b == 18) {
  67. [Dòng 792] if (this._b == 27
  68. [Dòng 792] this._b == 18
  69. [Dòng 792] this._b == 14) {
  70. [Dòng 904] if ((!this.validCardNumber(cardNo) && this.d_card_demo && (this._b == 3
  71. [Dòng 904] this._b == 14))
  72. [Dòng 905] || (this._b == 18
  73. [Dòng 905] this.cardTypeOcean == 'ATM'
  74. [Dòng 906] this.cardTypeOcean == 'IB'
  75. [Dòng 907] this.cardTypeOcean == 'MB'
  76. [Dòng 911] if ((this._b == 27) && ((this.cardTypeBank === 'bank_card_number' && (!this.validCardNumber(cardNo) || !this.checkMod10(cardNo)))
  77. [Dòng 916] if ((this._b == 3) && ((this.cardTypeBank === 'bank_card_number' && (!this.validCardNumber(cardNo) || !this.checkMod10(cardNo)))
  78. [Dòng 921] if ((this._b == 12) && ((this.cardTypeBank === 'bank_card_number' && (!this.validCardNumber(cardNo) || !this.checkMod10(cardNo)))
  79. [Dòng 923] (this.cardTypeBank === 'bank_customer_code' && (cardNo == null
  80. [Dòng 927] if ((this._b == 19) && ((this.cardTypeBank === 'bank_card_number' && (!this.validCardNumber(cardNo) || !this.checkMod10(cardNo)))
  81. [Dòng 929] (this.cardTypeBank === 'bank_username' && (cardNo == null
  82. [Dòng 936] if (this.d_card_demo && (this._b == 14
  83. [Dòng 936] this._b == 3)) {
  84. [Dòng 937] if ((_formCard && _formCard.exp_date) && ((_formCard.exp_date.length == 4
  85. [Dòng 937] _formCard.exp_date.search('/') == -1)
  86. [Dòng 938] _formCard.exp_date.length == 5) && this._util.checkValidMonthYear(_formCard.exp_date)) {
  87. [Dòng 947] if (!this._util.validateCardFullName(this.cardName) && (this._b == 19
  88. [Dòng 947] if (!this._util.validateCardFullName(this.cardName) && (this._b == 19 || ((this._b == 3
  89. [Dòng 947] this._b == 14) && this.d_card_demo))) {
  90. [Dòng 997] if ((cardNo.length == 16
  91. [Dòng 997] if ((cardNo.length == 16 || (cardNo.length == 19
  92. [Dòng 998] && ((this._b == 18
  93. [Dòng 998] cardNo.length == 19) || this._b != 18)
  94. [Dòng 1010] if (this._b == +e.id) {
  95. [Dòng 1025] if (valIn == 1) {
  96. [Dòng 1027] } else if (valIn == 2) {
  97. [Dòng 1054] if (this._b == 3
  98. [Dòng 1054] this._b == 27) {
  99. [Dòng 1104] if (cardTypeOcean == 'IB') {
  100. [Dòng 1107] } else if (cardTypeOcean == 'MB') {
  101. [Dòng 1110] } else if (cardTypeOcean == 'ATM') {
  102. [Dòng 1127] if (item['id'] == this._b.toString()) {

!== (3 điều kiện):
  1. [Dòng 432] this._b !== 18) || (this.cardTypeOcean === 'ATM'
  2. [Dòng 818] this._util.getElementParams(url_redirect, 'vpc_TxnResponseCode').toString() !== '0') {
  3. [Dòng 977] cardNo.length !== 0) {

!= (34 điều kiện):
  1. [Dòng 185] } else if (this._b != 18) {
  2. [Dòng 193] if (this.htmlDesc != null
  3. [Dòng 222] if (this._res != null
  4. [Dòng 222] this._res.links != null
  5. [Dòng 222] this._res.links.merchant_return != null
  6. [Dòng 223] this._res.links.merchant_return.href != null) {
  7. [Dòng 253] if (ua.indexOf('safari') != -1
  8. [Dòng 255] if (_val.value != null) {
  9. [Dòng 471] if (this.valueDate.length != 3) {
  10. [Dòng 515] && ((this._b != 20
  11. [Dòng 515] this._b != 33
  12. [Dòng 515] this._b != 39
  13. [Dòng 516] this._b != 43
  14. [Dòng 516] this._b != 45
  15. [Dòng 516] this._b != 73
  16. [Dòng 629] if (this.current != 'card'
  17. [Dòng 642] if (this.current != 'date'
  18. [Dòng 655] if (this.current != 'name'
  19. [Dòng 668] if (this.current != 'phone'
  20. [Dòng 708] if (_formCard.exp_date != null
  21. [Dòng 713] if (_formCard.name != null
  22. [Dòng 767] if (this._res_post.links != null
  23. [Dòng 767] this._res_post.links.merchant_return != null
  24. [Dòng 767] this._res_post.links.merchant_return.href != null) {
  25. [Dòng 775] if (this._res_post.authorization != null
  26. [Dòng 775] this._res_post.authorization.links != null
  27. [Dòng 775] this._res_post.authorization.links.approval != null) {
  28. [Dòng 781] this._res_post.links.cancel != null) {
  29. [Dòng 898] if (!(_formCard.card_number != null
  30. [Dòng 912] (this.cardTypeBank === 'bank_account_number' && !(cardNo != null
  31. [Dòng 997] this._b != 27
  32. [Dòng 997] this._b != 12
  33. [Dòng 997] this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
  34. [Dòng 998] this._b != 18)

================================================================================

📁 FILE 14: dmx-international-banks.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-international/dmx-international-banks.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 15: dmx-international-banks.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-international/dmx-international-banks.component.ts
📊 Thống kê: 55 điều kiện duy nhất
   - === : 4 lần
   - == : 33 lần
   - !== : 5 lần
   - != : 13 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 100] if (target.tagName === 'A'
  2. [Dòng 527] if (event.keyCode === 8
  3. [Dòng 527] event.key === "Backspace"
  4. [Dòng 614] if ((v.substr(-1) === ' '

== (33 điều kiện):
  1. [Dòng 225] if (_re.status == '200'
  2. [Dòng 225] _re.status == '201') {
  3. [Dòng 230] if (this._res_post.state == 'approved'
  4. [Dòng 230] this._res_post.state == 'failed') {
  5. [Dòng 267] } else if (this._res_post.state == 'authorization_required') {
  6. [Dòng 268] if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
  7. [Dòng 282] } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
  8. [Dòng 350] v.length == 15) || (v.length == 16
  9. [Dòng 350] v.length == 19))
  10. [Dòng 351] this._util.checkMod10(v) == true) {
  11. [Dòng 358] this._i_exp_date.length == 4
  12. [Dòng 358] this._i_exp_date.search('/') == -1)
  13. [Dòng 359] this._i_exp_date.length == 5))
  14. [Dòng 373] csc.length == 4) ||
  15. [Dòng 377] csc.length == 3)
  16. [Dòng 477] cardNo.length == 15)
  17. [Dòng 479] cardNo.length == 16)
  18. [Dòng 480] cardNo.startsWith('81'))&& (cardNo.length == 16
  19. [Dòng 480] cardNo.length == 19))
  20. [Dòng 527] event.inputType == 'deleteContentBackward') {
  21. [Dòng 528] if (event.target.name == 'exp_date'
  22. [Dòng 536] event.inputType == 'insertCompositionText') {
  23. [Dòng 551] _val.value.length == 4) && _val.value.search('/') == -1) || (_val.value && _val.value.length == 5)) && this._util.checkValidMonthYearSeaBank(_val.value)) {
  24. [Dòng 551] _val.value.search('/') == -1) || (_val.value
  25. [Dòng 551] _val.value.length == 5)) && this._util.checkValidMonthYearSeaBank(_val.value)) {
  26. [Dòng 566] if (((this._i_exp_date.length == 4
  27. [Dòng 566] this._i_exp_date.search('/') == -1) || this._i_exp_date.length == 5)
  28. [Dòng 566] this._i_exp_date.length == 5)
  29. [Dòng 614] v.length == 5) {
  30. [Dòng 622] v.length == 4
  31. [Dòng 626] v.length == 3)
  32. [Dòng 648] _val.value.length == 4
  33. [Dòng 652] _val.value.length == 3)

!== (5 điều kiện):
  1. [Dòng 214] key !== '8') {
  2. [Dòng 241] codeResponse.toString() !== '0') {
  3. [Dòng 614] event.inputType !== 'deleteContentBackward') || v.length == 5) {
  4. [Dòng 719] this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
  5. [Dòng 726] this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {

!= (13 điều kiện):
  1. [Dòng 121] if (dataPassed.body != null) {
  2. [Dòng 232] if (this._res_post.return_url != null) {
  3. [Dòng 234] } else if (this._res_post.links != null
  4. [Dòng 234] this._res_post.links.merchant_return != null
  5. [Dòng 234] this._res_post.links.merchant_return.href != null) {
  6. [Dòng 371] csc != null
  7. [Dòng 398] if (ua.indexOf('safari') != -1
  8. [Dòng 477] cardNo != null
  9. [Dòng 529] if (this._i_exp_date.length != 3) {
  10. [Dòng 621] v != null
  11. [Dòng 647] this.c_csc = (!(_val.value != null
  12. [Dòng 757] if (this._res_post != null
  13. [Dòng 757] this._res_post.links != null

================================================================================

📁 FILE 16: dmx-onepay-napas.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-onepay-napas/dmx-onepay-napas.component.html
📊 Thống kê: 16 điều kiện duy nhất
   - === : 2 lần
   - == : 14 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 17] cardTypeBank === 'bank_card_number' ? 'pg_selected': 'pg_un_selected'"
  2. [Dòng 23] cardTypeBank === 'bank_account_number' ? 'pg_selected': 'pg_un_selected'"

== (14 điều kiện):
  1. [Dòng 7] [ngClass]="(this._b == 19
  2. [Dòng 7] this._b == 67
  3. [Dòng 7] this._b == 2
  4. [Dòng 7] this._b == 12
  5. [Dòng 7] this._b == 18
  6. [Dòng 54] cardTypeBank == 'bank_card_number'"
  7. [Dòng 89] _b==33||_b==35||_b==38||_b==39||_b==42||_b==45
  8. [Dòng 89] _b==40"
  9. [Dòng 90] _b==34||_b==37
  10. [Dòng 90] _b==36||_b==41||_b==43
  11. [Dòng 91] _b==44||_b==46||_b==72
  12. [Dòng 103] <div *ngIf="!(cardTypeBank == 'bank_card_number')" class="" style="clear: both; padding-top:15px;color:#888888;font-size: 14px;">{{'txt_redirect_to' | translate }} Techcombank {{'txt_to_pay' | translate }}</div>
  13. [Dòng 107] <div *ngIf="!(cardTypeBank == 'bank_card_number')" class="text_desc" [innerHTML]="htmlDesc" style="float: left;clear: both;color:#888888"></div>
  14. [Dòng 108] <div *ngIf="(cardTypeBank == 'bank_card_number')" class="text_desc" innerHTML="{{'67_html_desc' | translate}}" style="float: left;clear: both;color:#888888"></div>

================================================================================

📁 FILE 17: dmx-onepay-napas.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-onepay-napas/dmx-onepay-napas.component.ts
📊 Thống kê: 117 điều kiện duy nhất
   - === : 16 lần
   - == : 56 lần
   - !== : 1 lần
   - != : 44 lần
--------------------------------------------------------------------------------

=== (16 điều kiện):
  1. [Dòng 103] if (target.tagName === 'A'
  2. [Dòng 140] if (isIE[0] === 'MSIE'
  3. [Dòng 140] +isIE[1] === 10) {
  4. [Dòng 300] if (event.keyCode === 8
  5. [Dòng 300] event.key === "Backspace"
  6. [Dòng 364] if (v.length === 2
  7. [Dòng 364] this.flag.length === 3
  8. [Dòng 364] this.flag.charAt(this.flag.length - 1) === '/') {
  9. [Dòng 368] if (v.length === 1) {
  10. [Dòng 370] } else if (v.length === 2) {
  11. [Dòng 373] v.length === 2) {
  12. [Dòng 381] if (len === 2) {
  13. [Dòng 700] if (approval.method === 'REDIRECT') {
  14. [Dòng 703] } else if (approval.method === 'POST_REDIRECT') {
  15. [Dòng 917] if (valIn === 1) {
  16. [Dòng 941] if (valIn === 2) {

== (56 điều kiện):
  1. [Dòng 178] if (_re.status == '200'
  2. [Dòng 178] _re.status == '201') {
  3. [Dòng 300] event.inputType == 'deleteContentBackward') {
  4. [Dòng 301] if (event.target.name == 'exp_date'
  5. [Dòng 309] event.inputType == 'insertCompositionText') {
  6. [Dòng 324] _val.value.length == 4) && _val.value.search('/') == -1) || (_val.value && _val.value.length == 5))
  7. [Dòng 324] _val.value.search('/') == -1) || (_val.value
  8. [Dòng 324] _val.value.length == 5))
  9. [Dòng 327] || (((this._b == 20
  10. [Dòng 327] this._b == 33
  11. [Dòng 327] this._b == 2
  12. [Dòng 327] this._b == 39
  13. [Dòng 327] this._b == 43
  14. [Dòng 327] this._b == 45
  15. [Dòng 327] this._b == 64
  16. [Dòng 327] this._b == 67) && this._util.checkValidMonthYearSeaBank(_val.value))))) {
  17. [Dòng 355] valueDate.length == 4) && valueDate.search('/') == -1) || (valueDate && valueDate.length == 5))
  18. [Dòng 355] valueDate.search('/') == -1) || (valueDate
  19. [Dòng 355] valueDate.length == 5))
  20. [Dòng 359] this._b == 67
  21. [Dòng 359] this._b == 72) && this._util.checkValidMonthYearSeaBank(valueDate)))))
  22. [Dòng 453] if (temp.length == 0) {
  23. [Dòng 460] return (counter % 10 == 0);
  24. [Dòng 501] if (this.cardTypeBank == 'bank_account_number') {
  25. [Dòng 508] if (this._b == 2) {
  26. [Dòng 542] if (this._res_post.state == 'approved'
  27. [Dòng 542] this._res_post.state == 'failed') {
  28. [Dòng 562] } else if (this._res_post.state == 'authorization_required') {
  29. [Dòng 596] if (this.cardTypeBank == 'bank_card_number') {
  30. [Dòng 610] _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;14-VPB
  31. [Dòng 684] if (this._b == 1
  32. [Dòng 684] this._b == 15
  33. [Dòng 684] this._b == 24
  34. [Dòng 684] this._b == 8
  35. [Dòng 684] this._b == 10
  36. [Dòng 684] this._b == 20
  37. [Dòng 684] this._b == 22
  38. [Dòng 684] this._b == 23
  39. [Dòng 684] this._b == 30
  40. [Dòng 684] this._b == 11
  41. [Dòng 684] this._b == 17) {
  42. [Dòng 746] if(this._res_post.state == 'failed'){
  43. [Dòng 818] expdate.length == 5) {
  44. [Dòng 837] if (((dCardExp.getTime() - dCurrent.getTime()) > 0 && this._b != 20 && this._b != 2 && this._b != 33 && this._b != 39 && this._b != 43 && this._b != 45 && this._b != 72) || ((dCardExp.getTime() - dCurrent.getTime()) < 0 && (this._b == 20
  45. [Dòng 837] this._b == 72))) {
  46. [Dòng 876] if (cardNo && (cardNo.length == 16
  47. [Dòng 876] cardNo.length == 19)
  48. [Dòng 888] if (this._b == +e.id) {
  49. [Dòng 903] if (valIn == 1) {
  50. [Dòng 905] } else if (valIn == 2) {
  51. [Dòng 924] if (this._b == 19
  52. [Dòng 924] this._b == 3
  53. [Dòng 924] this._b == 27
  54. [Dòng 924] this._b == 12) {
  55. [Dòng 932] if (this._b == 3
  56. [Dòng 932] this._b == 27) {

!== (1 điều kiện):
  1. [Dòng 729] codeResponse.toString() !== '0') {

!= (44 điều kiện):
  1. [Dòng 160] if (this.htmlDesc != null
  2. [Dòng 191] if (this._res != null
  3. [Dòng 191] this._res.links != null
  4. [Dòng 191] this._res.links.merchant_return != null
  5. [Dòng 192] this._res.links.merchant_return.href != null) {
  6. [Dòng 211] if (ua.indexOf('safari') != -1
  7. [Dòng 213] if (_val.value != null) {
  8. [Dòng 221] if (this._b != 9
  9. [Dòng 221] this._b != 11
  10. [Dòng 221] this._b != 16
  11. [Dòng 221] this._b != 17
  12. [Dòng 221] this._b != 25
  13. [Dòng 221] this._b != 36
  14. [Dòng 221] this._b != 44) {
  15. [Dòng 302] if (this.valueDate.length != 3) {
  16. [Dòng 325] && ((this._b != 20
  17. [Dòng 325] this._b != 2
  18. [Dòng 325] this._b != 33
  19. [Dòng 325] this._b != 39
  20. [Dòng 326] this._b != 43
  21. [Dòng 326] this._b != 45
  22. [Dòng 357] this._b != 72)
  23. [Dòng 470] if (this.current != 'card') {
  24. [Dòng 480] if (this.current != 'date') {
  25. [Dòng 491] if (this.current != 'name') {
  26. [Dòng 565] if (this._res_post.authorization != null
  27. [Dòng 565] this._res_post.authorization.links != null
  28. [Dòng 565] this._res_post.authorization.links.approval != null) {
  29. [Dòng 610] if (_formCard.exp_date != null
  30. [Dòng 615] if (_formCard.name != null
  31. [Dòng 653] if (this._res_post.return_url != null) {
  32. [Dòng 656] if (this._res_post.links != null
  33. [Dòng 656] this._res_post.links.merchant_return != null
  34. [Dòng 656] this._res_post.links.merchant_return.href != null) {
  35. [Dòng 669] this._res_post.links.cancel != null) {
  36. [Dòng 674] var userName = _formCard.name != null ? _formCard.name : ''
  37. [Dòng 675] this._res_post.authorization.links.approval != null
  38. [Dòng 675] this._res_post.authorization.links.approval.href != null) {
  39. [Dòng 681] userName = paramUserName != null ? paramUserName : ''
  40. [Dòng 816] this._b != 44) {//9vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  41. [Dòng 818] if (expdate != null
  42. [Dòng 837] this._b != 20
  43. [Dòng 837] this._b != 72) || ((dCardExp.getTime() - dCurrent.getTime()) < 0
  44. [Dòng 853] if (!(formCardName != null

================================================================================

📁 FILE 18: dmx-shb.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-shb-banks/dmx-shb.component.html
📊 Thống kê: 7 điều kiện duy nhất
   - === : 5 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 19] cardTypeOcean === 'ATM' ? 'pg_selected': 'pg_un_selected'"
  2. [Dòng 25] cardTypeOcean === 'MB' ? 'pg_selected': 'pg_un_selected'"
  3. [Dòng 31] cardTypeOcean === 'IB' ? 'pg_selected': 'pg_un_selected'"
  4. [Dòng 79] cardTypeBank === 'bank_card_number' ? 'pg_selected': 'pg_un_selected'"
  5. [Dòng 85] cardTypeBank === 'bank_account_number' ? 'pg_selected': 'pg_un_selected'"

== (2 điều kiện):
  1. [Dòng 149] this._b == 12? 'dmx-form-card margin-top-62' : 'dmx-form-card'"
  2. [Dòng 150] this._b == 12 ? 'd_text_err top-60' : 'd_text_err'"

================================================================================

📁 FILE 19: dmx-shb.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-shb-banks/dmx-shb.component.ts
📊 Thống kê: 101 điều kiện duy nhất
   - === : 16 lần
   - == : 53 lần
   - !== : 3 lần
   - != : 29 lần
--------------------------------------------------------------------------------

=== (16 điều kiện):
  1. [Dòng 100] if (target.tagName === 'A'
  2. [Dòng 145] if (isIE[0] === 'MSIE'
  3. [Dòng 145] +isIE[1] === 10) {
  4. [Dòng 301] if (this.cardTypeBank === 'bank_card_number') {
  5. [Dòng 306] } else if (this.cardTypeBank === 'bank_account_number') {
  6. [Dòng 387] if (_val.value === ''
  7. [Dòng 387] _val.value === null
  8. [Dòng 387] _val.value === undefined) {
  9. [Dòng 393] if ((this.cardTypeBank === 'bank_card_number'
  10. [Dòng 430] if (event.keyCode === 8
  11. [Dòng 430] event.key === "Backspace"
  12. [Dòng 793] if ((this._b == 12) && ((this.cardTypeBank === 'bank_card_number'
  13. [Dòng 794] (this.cardTypeBank === 'bank_account_number'
  14. [Dòng 843] if (this.cardTypeBank === 'bank_account_number'
  15. [Dòng 883] if (valIn === 1) { //bank_card_number
  16. [Dòng 898] } else if (valIn === 2) { //bank_account_number

== (53 điều kiện):
  1. [Dòng 136] if (this._b == 12) {
  2. [Dòng 152] if (this._b == 12) {// shb
  3. [Dòng 194] if (_re.status == '200'
  4. [Dòng 194] _re.status == '201') {
  5. [Dòng 249] if (this._res.state == 'approved'
  6. [Dòng 249] this._res.state == 'failed') {
  7. [Dòng 252] } else if (this._res.state == 'authorization_required') {
  8. [Dòng 300] if (this._b == 12) { // 12 shb
  9. [Dòng 328] if (this._b == 12) {//** check đúng số thẻ shb chuyển qua ô nhập date
  10. [Dòng 345] if (this._b == 12) { // 5 shb
  11. [Dòng 373] if ((this._b == 12)) {//** check đúng số thẻ 12 shb chuyển qua ô nhập date
  12. [Dòng 430] event.inputType == 'deleteContentBackward') {
  13. [Dòng 431] if (event.target.name == 'exp_date'
  14. [Dòng 439] event.inputType == 'insertCompositionText') {
  15. [Dòng 454] _val.value.length == 4) && _val.value.search('/') == -1) || (_val.value && _val.value.length == 5)) && this._util.checkValidMonthYear(_val.value)) {
  16. [Dòng 454] _val.value.search('/') == -1) || (_val.value
  17. [Dòng 454] _val.value.length == 5)) && this._util.checkValidMonthYear(_val.value)) {
  18. [Dòng 476] valueDate.length == 4) && valueDate.search('/') == -1)
  19. [Dòng 476] valueDate.search('/') == -1)
  20. [Dòng 477] valueDate.length == 5))
  21. [Dòng 479] ((this._b == 20
  22. [Dòng 479] this._b == 33
  23. [Dòng 479] this._b == 39
  24. [Dòng 479] this._b == 43
  25. [Dòng 479] this._b == 45
  26. [Dòng 479] this._b == 73
  27. [Dòng 479] this._b == 64
  28. [Dòng 479] this._b == 67) ?
  29. [Dòng 542] if (temp.length == 0) {
  30. [Dòng 549] return (counter % 10 == 0);
  31. [Dòng 556] if (this.current != 'card' && (this._b == 12) && this.d_card_demo) {
  32. [Dòng 569] if (this.current != 'date' && (this._b == 12) && this.d_card_demo) {
  33. [Dòng 582] if (this.current != 'name' && (this._b == 12) && this.d_card_demo) {
  34. [Dòng 595] if (this.current != 'phone' && (this._b == 14) && this.d_card_demo) {
  35. [Dòng 613] if (this.cardTypeBank == 'bank_account_number') {
  36. [Dòng 615] } else if (this.cardTypeBank == 'bank_card_number') {
  37. [Dòng 630] _formCard.exp_date.length == 5
  38. [Dòng 630] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 12)) { //12 shb
  39. [Dòng 635] if (_formCard.name != null && _formCard.name.length > 0 && (this._b == 12)) { //12 shb
  40. [Dòng 673] if (this._res_post.state == 'approved'
  41. [Dòng 673] this._res_post.state == 'failed') {
  42. [Dòng 680] } else if (this._res_post.state == 'authorization_required') {
  43. [Dòng 793] if ((this._b == 12) && ((this.cardTypeBank === 'bank_card_number' && (!this.validCardNumber(cardNo) || !this.checkMod10(cardNo)))
  44. [Dòng 802] if (this.d_card_demo && (this._b == 12)) {
  45. [Dòng 803] if ((_formCard && _formCard.exp_date) && ((_formCard.exp_date.length == 4
  46. [Dòng 803] _formCard.exp_date.search('/') == -1)
  47. [Dòng 804] _formCard.exp_date.length == 5) && this._util.checkValidMonthYear(_formCard.exp_date)) {
  48. [Dòng 814] if (!this._util.validateCardFullName(this.cardName) && (this._b == 12) && this.d_card_demo) {
  49. [Dòng 849] (cardNo.length == 16
  50. [Dòng 849] (cardNo.length == 16 || (cardNo.length == 19
  51. [Dòng 851] && ((this._b == 18
  52. [Dòng 851] cardNo.length == 19) || this._b != 18)
  53. [Dòng 863] if (this._b == +e.id) {

!== (3 điều kiện):
  1. [Dòng 393] this._b !== 18)) {
  2. [Dòng 722] this._util.getElementParams(url_redirect, 'vpc_TxnResponseCode').toString() !== '0') {
  3. [Dòng 843] cardNo.length !== 0) {

!= (29 điều kiện):
  1. [Dòng 207] if (this._res != null
  2. [Dòng 207] this._res.links != null
  3. [Dòng 207] this._res.links.merchant_return != null
  4. [Dòng 208] this._res.links.merchant_return.href != null) {
  5. [Dòng 255] if (this._res.authorization != null
  6. [Dòng 255] this._res.authorization.links != null
  7. [Dòng 255] this._res.authorization.links.approval != null) {
  8. [Dòng 296] if (ua.indexOf('safari') != -1
  9. [Dòng 298] if (_val.value != null) {
  10. [Dòng 432] if (this.valueDate.length != 3) {
  11. [Dòng 556] if (this.current != 'card'
  12. [Dòng 569] if (this.current != 'date'
  13. [Dòng 582] if (this.current != 'name'
  14. [Dòng 595] if (this.current != 'phone'
  15. [Dòng 630] if (_formCard.exp_date != null
  16. [Dòng 635] if (_formCard.name != null
  17. [Dòng 676] if (this._res_post.links != null
  18. [Dòng 676] this._res_post.links.merchant_return != null
  19. [Dòng 676] this._res_post.links.merchant_return.href != null) {
  20. [Dòng 684] if (this._res_post.authorization != null
  21. [Dòng 684] this._res_post.authorization.links != null
  22. [Dòng 684] this._res_post.authorization.links.approval != null) {
  23. [Dòng 690] this._res_post.links.cancel != null) {
  24. [Dòng 787] if (!(_formCard.card_number != null
  25. [Dòng 794] (this.cardTypeBank === 'bank_account_number' && !(cardNo != null
  26. [Dòng 849] this._b != 27
  27. [Dòng 849] this._b != 12
  28. [Dòng 849] this._b != 3))
  29. [Dòng 851] this._b != 18)

================================================================================

📁 FILE 20: dmx-techcombank-banks.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-techcombank-banks/dmx-techcombank-banks.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 21: dmx-techcombank-banks.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-techcombank-banks/dmx-techcombank-banks.component.ts
📊 Thống kê: 15 điều kiện duy nhất
   - === : 0 lần
   - == : 8 lần
   - !== : 0 lần
   - != : 7 lần
--------------------------------------------------------------------------------

== (8 điều kiện):
  1. [Dòng 71] if (this._b == 2) {
  2. [Dòng 73] } else if (this._b == 5) {
  3. [Dòng 75] } else if (this._b == 6) {
  4. [Dòng 104] if (_re.status == '200'
  5. [Dòng 104] _re.status == '201') {
  6. [Dòng 162] if (this._res_post.state == 'approved'
  7. [Dòng 162] this._res_post.state == 'failed') {
  8. [Dòng 165] } else if (this._res_post.state == 'authorization_required') {

!= (7 điều kiện):
  1. [Dòng 117] if (this._res_post != null
  2. [Dòng 117] this._res_post.links != null
  3. [Dòng 117] this._res_post.links.merchant_return != null
  4. [Dòng 118] this._res_post.links.merchant_return.href != null) {
  5. [Dòng 168] if (this._res_post.authorization != null
  6. [Dòng 168] this._res_post.authorization.links != null
  7. [Dòng 168] this._res_post.authorization.links.approval != null) {

================================================================================

📁 FILE 22: dmx-vibbank.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-vibbank/dmx-vibbank.component.html
📊 Thống kê: 12 điều kiện duy nhất
   - === : 5 lần
   - == : 7 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (5 điều kiện):
  1. [Dòng 36] cardTypeOcean === 'ATM' ? 'pg_selected': 'pg_un_selected'"
  2. [Dòng 42] cardTypeOcean === 'MB' ? 'pg_selected': 'pg_un_selected'"
  3. [Dòng 48] cardTypeOcean === 'IB' ? 'pg_selected': 'pg_un_selected'"
  4. [Dòng 96] cardTypeBank === 'bank_card_number' ? 'pg_selected': 'pg_un_selected'"
  5. [Dòng 102] cardTypeBank === 'bank_account_number' ? 'pg_selected': 'pg_un_selected'"

== (7 điều kiện):
  1. [Dòng 2] transactionFlow=='internet_banking'"
  2. [Dòng 21] transactionFlow=='bank_account'"
  3. [Dòng 27] <div [ngClass]="(this._b == 19
  4. [Dòng 27] this._b == 12
  5. [Dòng 27] this._b == 18
  6. [Dòng 166] this._b == 5? 'dmx-form-card margin-top-62' : 'dmx-form-card'"
  7. [Dòng 167] this._b == 5 ? 'd_text_err top-60' : 'd_text_err'"

================================================================================

📁 FILE 23: dmx-vibbank.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-vibbank/dmx-vibbank.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 24: dmx-vibbank.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-vibbank/dmx-vibbank.component.ts
📊 Thống kê: 103 điều kiện duy nhất
   - === : 20 lần
   - == : 51 lần
   - !== : 3 lần
   - != : 29 lần
--------------------------------------------------------------------------------

=== (20 điều kiện):
  1. [Dòng 101] if (target.tagName === 'A'
  2. [Dòng 147] if (isIE[0] === 'MSIE'
  3. [Dòng 147] +isIE[1] === 10) {
  4. [Dòng 303] if (this.cardTypeBank === 'bank_card_number') {
  5. [Dòng 308] } else if (this.cardTypeBank === 'bank_account_number') {
  6. [Dòng 314] } else if (this.cardTypeBank === 'bank_username') {
  7. [Dòng 318] } else if (this.cardTypeBank === 'bank_customer_code') {
  8. [Dòng 405] if (_val.value === ''
  9. [Dòng 405] _val.value === null
  10. [Dòng 405] _val.value === undefined) {
  11. [Dòng 411] if ((this.cardTypeBank === 'bank_card_number'
  12. [Dòng 448] if (event.keyCode === 8
  13. [Dòng 448] event.key === "Backspace"
  14. [Dòng 812] if ((this._b == 5) && ((this.cardTypeBank === 'bank_card_number'
  15. [Dòng 813] (this.cardTypeBank === 'bank_account_number'
  16. [Dòng 863] if ((this.cardTypeBank === 'bank_account_number'
  17. [Dòng 863] this.cardTypeBank === 'bank_username'
  18. [Dòng 863] this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
  19. [Dòng 903] if (valIn === 1) { //bank_card_number
  20. [Dòng 919] } else if (valIn === 2) { //bank_account_number

== (51 điều kiện):
  1. [Dòng 139] if (this._b == 5) {
  2. [Dòng 154] if (this._b == 5) {// vib
  3. [Dòng 196] if (_re.status == '200'
  4. [Dòng 196] _re.status == '201') {
  5. [Dòng 251] if (this._res.state == 'approved'
  6. [Dòng 251] this._res.state == 'failed') {
  7. [Dòng 254] } else if (this._res.state == 'authorization_required') {
  8. [Dòng 302] if (this._b == 5) { // 5 vib
  9. [Dòng 338] if (this._b == 5) {//** check đúng số thẻ vib chuyển qua ô nhập date
  10. [Dòng 391] if ((this._b == 5)) {//** check đúng số thẻ 5 vib chuyển qua ô nhập date
  11. [Dòng 411] this._b == 18)) {
  12. [Dòng 448] event.inputType == 'deleteContentBackward') {
  13. [Dòng 449] if (event.target.name == 'exp_date'
  14. [Dòng 457] event.inputType == 'insertCompositionText') {
  15. [Dòng 472] _val.value.length == 4) && _val.value.search('/') == -1) || (_val.value && _val.value.length == 5)) && this._util.checkValidMonthYear(_val.value)) {
  16. [Dòng 472] _val.value.search('/') == -1) || (_val.value
  17. [Dòng 472] _val.value.length == 5)) && this._util.checkValidMonthYear(_val.value)) {
  18. [Dòng 495] valueDate.length == 4) && valueDate.search('/') == -1)
  19. [Dòng 495] valueDate.search('/') == -1)
  20. [Dòng 496] valueDate.length == 5)
  21. [Dòng 500] (this._b == 20
  22. [Dòng 500] this._b == 33
  23. [Dòng 500] this._b == 39
  24. [Dòng 500] this._b == 43
  25. [Dòng 500] this._b == 45
  26. [Dòng 500] this._b == 73
  27. [Dòng 500] this._b == 64
  28. [Dòng 500] this._b == 67) ?
  29. [Dòng 564] if (temp.length == 0) {
  30. [Dòng 571] return (counter % 10 == 0);
  31. [Dòng 578] if (this.current != 'card' && (this._b == 5) && this.d_card_demo) {
  32. [Dòng 591] if (this.current != 'date' && (this._b == 5) && this.d_card_demo) {
  33. [Dòng 604] if (this.current != 'name' && (this._b == 5) && this.d_card_demo) {
  34. [Dòng 617] if (this.current != 'phone' && (this._b == 14) && this.d_card_demo) {
  35. [Dòng 649] _formCard.exp_date.length == 5
  36. [Dòng 649] if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 5)) { //5 vib
  37. [Dòng 654] if (_formCard.name != null && _formCard.name.length > 0 && (this._b == 5)) { //5 vib
  38. [Dòng 691] if (this._res_post.state == 'approved'
  39. [Dòng 691] this._res_post.state == 'failed') {
  40. [Dòng 698] } else if (this._res_post.state == 'authorization_required') {
  41. [Dòng 812] if ((this._b == 5) && ((this.cardTypeBank === 'bank_card_number' && (!this.validCardNumber(cardNo) || !this.checkMod10(cardNo)))
  42. [Dòng 822] if (this.d_card_demo && (this._b == 5)) {
  43. [Dòng 823] if ((_formCard && _formCard.exp_date) && ((_formCard.exp_date.length == 4
  44. [Dòng 823] _formCard.exp_date.search('/') == -1)
  45. [Dòng 824] _formCard.exp_date.length == 5) && this._util.checkValidMonthYear(_formCard.exp_date)) {
  46. [Dòng 834] if (!this._util.validateCardFullName(this.cardName) && (this._b == 5) && this.d_card_demo) {
  47. [Dòng 869] (cardNo.length == 16
  48. [Dòng 869] (cardNo.length == 16 || (cardNo.length == 19
  49. [Dòng 871] && ((this._b == 18
  50. [Dòng 871] cardNo.length == 19) || this._b != 18)
  51. [Dòng 883] if (this._b == +e.id) {

!== (3 điều kiện):
  1. [Dòng 411] this._b !== 18) || (this.cardTypeOcean === 'ATM'
  2. [Dòng 740] this._util.getElementParams(url_redirect, 'vpc_TxnResponseCode').toString() !== '0') {
  3. [Dòng 863] cardNo.length !== 0) {

!= (29 điều kiện):
  1. [Dòng 209] if (this._res != null
  2. [Dòng 209] this._res.links != null
  3. [Dòng 209] this._res.links.merchant_return != null
  4. [Dòng 210] this._res.links.merchant_return.href != null) {
  5. [Dòng 257] if (this._res.authorization != null
  6. [Dòng 257] this._res.authorization.links != null
  7. [Dòng 257] this._res.authorization.links.approval != null) {
  8. [Dòng 298] if (ua.indexOf('safari') != -1
  9. [Dòng 300] if (_val.value != null) {
  10. [Dòng 450] if (this.valueDate.length != 3) {
  11. [Dòng 578] if (this.current != 'card'
  12. [Dòng 591] if (this.current != 'date'
  13. [Dòng 604] if (this.current != 'name'
  14. [Dòng 617] if (this.current != 'phone'
  15. [Dòng 649] if (_formCard.exp_date != null
  16. [Dòng 654] if (_formCard.name != null
  17. [Dòng 694] if (this._res_post.links != null
  18. [Dòng 694] this._res_post.links.merchant_return != null
  19. [Dòng 694] this._res_post.links.merchant_return.href != null) {
  20. [Dòng 702] if (this._res_post.authorization != null
  21. [Dòng 702] this._res_post.authorization.links != null
  22. [Dòng 702] this._res_post.authorization.links.approval != null) {
  23. [Dòng 708] this._res_post.links.cancel != null) {
  24. [Dòng 805] if (!(_formCard.card_number != null
  25. [Dòng 813] (this.cardTypeBank === 'bank_account_number' && !(cardNo != null
  26. [Dòng 869] this._b != 27
  27. [Dòng 869] this._b != 12
  28. [Dòng 869] this._b != 3))
  29. [Dòng 871] this._b != 18)

================================================================================

📁 FILE 25: dmx-vietcombank-banks.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-vietcombank-banks/dmx-vietcombank-banks.component.html
📊 Thống kê: 13 điều kiện duy nhất
   - === : 0 lần
   - == : 13 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (13 điều kiện):
  1. [Dòng 42] _b==33||_b==35||_b==38||_b==39||_b==42||_b==45 
  2. [Dòng 42] _b==40 "
  3. [Dòng 43] _b==34||_b==37
  4. [Dòng 43] _b==36||_b==41||_b==43
  5. [Dòng 44] _b==44||this._b==46||this._b==72 || this._b==73
  6. [Dòng 44] this._b==46
  7. [Dòng 44] this._b==72
  8. [Dòng 44] this._b==73"
  9. [Dòng 45] _b==74"
  10. [Dòng 45] _b==59"
  11. [Dòng 45] _b==30 || _b==71
  12. [Dòng 46] _b==55 || _b==57
  13. [Dòng 66] _b==68"

================================================================================

📁 FILE 26: dmx-vietcombank-banks.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/banks/dmx/dmx-vietcombank-banks/dmx-vietcombank-banks.component.ts
📊 Thống kê: 104 điều kiện duy nhất
   - === : 12 lần
   - == : 54 lần
   - !== : 1 lần
   - != : 37 lần
--------------------------------------------------------------------------------

=== (12 điều kiện):
  1. [Dòng 87] if (target.tagName === 'A'
  2. [Dòng 301] if (event.keyCode === 8
  3. [Dòng 301] event.key === "Backspace"
  4. [Dòng 341] if (v.length === 2
  5. [Dòng 341] this.flag.length === 3
  6. [Dòng 341] this.flag.charAt(this.flag.length - 1) === '/') {
  7. [Dòng 345] if (v.length === 1) {
  8. [Dòng 347] } else if (v.length === 2) {
  9. [Dòng 350] v.length === 2) {
  10. [Dòng 358] if (len === 2) {
  11. [Dòng 575] if (approval.method === 'REDIRECT') {
  12. [Dòng 578] } else if (approval.method === 'POST_REDIRECT') {

== (54 điều kiện):
  1. [Dòng 145] return this._b == 9
  2. [Dòng 145] this._b == 16
  3. [Dòng 145] this._b == 17
  4. [Dòng 145] this._b == 25
  5. [Dòng 145] this._b == 44
  6. [Dòng 146] this._b == 57
  7. [Dòng 146] this._b == 59
  8. [Dòng 146] this._b == 61
  9. [Dòng 146] this._b == 63
  10. [Dòng 146] this._b == 69
  11. [Dòng 157] if (_re.status == '200'
  12. [Dòng 157] _re.status == '201') {
  13. [Dòng 265] valueDate.length == 4) && valueDate.search('/') == -1) || (valueDate && valueDate.length == 5))
  14. [Dòng 265] valueDate.search('/') == -1) || (valueDate
  15. [Dòng 265] valueDate.length == 5))
  16. [Dòng 301] event.inputType == 'deleteContentBackward') {
  17. [Dòng 302] if (event.target.name == 'exp_date'
  18. [Dòng 310] event.inputType == 'insertCompositionText') {
  19. [Dòng 325] _val.value.length == 4) && _val.value.search('/') == -1) || (_val.value && _val.value.length == 5))
  20. [Dòng 325] _val.value.search('/') == -1) || (_val.value
  21. [Dòng 325] _val.value.length == 5))
  22. [Dòng 418] if (temp.length == 0) {
  23. [Dòng 425] return (counter % 10 == 0);
  24. [Dòng 526] if (this._res_post.state == 'approved'
  25. [Dòng 526] this._res_post.state == 'failed') {
  26. [Dòng 534] } else if (this._res_post.state == 'authorization_required') {
  27. [Dòng 559] if (this._b == 1
  28. [Dòng 559] this._b == 15
  29. [Dòng 559] this._b == 24
  30. [Dòng 559] this._b == 8
  31. [Dòng 559] this._b == 10
  32. [Dòng 559] this._b == 20
  33. [Dòng 559] this._b == 22
  34. [Dòng 559] this._b == 23
  35. [Dòng 559] this._b == 30
  36. [Dòng 559] this._b == 11
  37. [Dòng 559] this._b == 9) {
  38. [Dòng 695] expdate.length == 5) {
  39. [Dòng 752] return this._b == 11
  40. [Dòng 752] this._b == 33
  41. [Dòng 752] this._b == 39
  42. [Dòng 752] this._b == 43
  43. [Dòng 752] this._b == 45
  44. [Dòng 752] this._b == 72
  45. [Dòng 752] this._b == 67
  46. [Dòng 752] this._b == 68
  47. [Dòng 752] this._b == 73
  48. [Dòng 752] this._b == 36
  49. [Dòng 753] this._b == 74
  50. [Dòng 753] this._b == 75
  51. [Dòng 753] this._b == 64
  52. [Dòng 765] if (cardNo && (cardNo.length == 16
  53. [Dòng 765] cardNo.length == 19)
  54. [Dòng 782] if (item['id'] == this._b.toString()) {

!== (1 điều kiện):
  1. [Dòng 604] codeResponse.toString() !== '0') {

!= (37 điều kiện):
  1. [Dòng 137] if (this.htmlDesc != null
  2. [Dòng 170] if (this._res_post != null
  3. [Dòng 170] this._res_post.links != null
  4. [Dòng 170] this._res_post.links.merchant_return != null
  5. [Dòng 171] this._res_post.links.merchant_return.href != null) {
  6. [Dòng 188] if (ua.indexOf('safari') != -1
  7. [Dòng 190] if (_val.value != null) {
  8. [Dòng 198] if (this._b != 9
  9. [Dòng 198] this._b != 16
  10. [Dòng 198] this._b != 17
  11. [Dòng 198] this._b != 25
  12. [Dòng 198] this._b != 44
  13. [Dòng 198] this._b != 57
  14. [Dòng 198] this._b != 59
  15. [Dòng 198] this._b != 61
  16. [Dòng 198] this._b != 63
  17. [Dòng 198] this._b != 69) {
  18. [Dòng 272] if (value != null) {
  19. [Dòng 303] if (this.valueDate.length != 3) {
  20. [Dòng 436] if (this.current != 'card') {
  21. [Dòng 449] if (this.current != 'date') {
  22. [Dòng 461] if (this.current != 'name') {
  23. [Dòng 485] this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
  24. [Dòng 495] '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75'].indexOf(this._b.toString()) != -1) {
  25. [Dòng 528] if (this._res_post.return_url != null) {
  26. [Dòng 531] if (this._res_post.links != null
  27. [Dòng 538] if (this._res_post.authorization != null
  28. [Dòng 538] this._res_post.authorization.links != null
  29. [Dòng 538] this._res_post.authorization.links.approval != null) {
  30. [Dòng 544] this._res_post.links.cancel != null) {
  31. [Dòng 549] var userName = _formCard.name != null ? _formCard.name : ''
  32. [Dòng 550] this._res_post.authorization.links.approval != null
  33. [Dòng 550] this._res_post.authorization.links.approval.href != null) {
  34. [Dòng 556] userName = paramUserName != null ? paramUserName : ''
  35. [Dòng 693] this._b != 69) {//9vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  36. [Dòng 695] if (expdate != null
  37. [Dòng 729] if (!(formCardName != null

================================================================================

📁 FILE 27: counter.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/counter.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 28: counter.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/counter.directive.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 29: bank-amount-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/dien-may-xanh/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 30: bank-amount-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/dien-may-xanh/bank-amount-dialog/bank-amount-dialog/bank-amount-dialog.component.ts
📊 Thống kê: 34 điều kiện duy nhất
   - === : 0 lần
   - == : 34 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (34 điều kiện):
  1. [Dòng 41] if (this.locale == 'en') {
  2. [Dòng 55] if (name == 'MAFC')
  3. [Dòng 61] if (bankId == 3
  4. [Dòng 61] bankId == 61
  5. [Dòng 62] bankId == 8
  6. [Dòng 62] bankId == 49
  7. [Dòng 63] bankId == 48
  8. [Dòng 64] bankId == 10
  9. [Dòng 64] bankId == 53
  10. [Dòng 65] bankId == 17
  11. [Dòng 65] bankId == 65
  12. [Dòng 66] bankId == 23
  13. [Dòng 66] bankId == 52
  14. [Dòng 67] bankId == 27
  15. [Dòng 67] bankId == 66
  16. [Dòng 68] bankId == 9
  17. [Dòng 68] bankId == 54
  18. [Dòng 69] bankId == 37
  19. [Dòng 70] bankId == 38
  20. [Dòng 71] bankId == 39
  21. [Dòng 72] bankId == 40
  22. [Dòng 73] bankId == 42
  23. [Dòng 74] bankId == 44
  24. [Dòng 75] bankId == 72
  25. [Dòng 76] bankId == 59
  26. [Dòng 79] bankId == 51
  27. [Dòng 80] bankId == 64
  28. [Dòng 81] bankId == 58
  29. [Dòng 82] bankId == 56
  30. [Dòng 85] bankId == 55
  31. [Dòng 86] bankId == 60
  32. [Dòng 87] bankId == 68
  33. [Dòng 88] bankId == 74
  34. [Dòng 89] bankId == 75

================================================================================

📁 FILE 31: dien-may-xanh.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/dien-may-xanh/dien-may-xanh.component.html
📊 Thống kê: 7 điều kiện duy nhất
   - === : 0 lần
   - == : 7 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (7 điều kiện):
  1. [Dòng 48] !token && _auth==0 && vietcombankGroupSelected
  2. [Dòng 52] !token && _auth==0 && techcombankGroupSelected
  3. [Dòng 55] !token && _auth==0 && bankaccountGroupSelected
  4. [Dòng 59] !token && _auth==0 && shbGroupSelected
  5. [Dòng 63] !token && _auth==0 && onepaynapasGroupSelected
  6. [Dòng 67] !token && _auth==0 && vibGroupSelected
  7. [Dòng 70] _auth==1)">

================================================================================

📁 FILE 32: dien-may-xanh.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/dien-may-xanh/dien-may-xanh.component.ts
📊 Thống kê: 252 điều kiện duy nhất
   - === : 8 lần
   - == : 169 lần
   - !== : 2 lần
   - != : 73 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 484] if (count === 1) {
  2. [Dòng 491] if (offBanksArr[i] === this.lastDomescard) {
  3. [Dòng 506] if (this._res.state === 'unpaid'
  4. [Dòng 506] this._res.state === 'not_paid') {
  5. [Dòng 903] if (valid.indexOf(temp) === -1) {
  6. [Dòng 1297] if (this.filterText.toUpperCase() === item.b.name.toUpperCase().substring(0, this.filterText.length)
  7. [Dòng 1298] this.filterText.toUpperCase() === item.b.code.toUpperCase().substring(0, this.filterText.length)) {
  8. [Dòng 1308] if (valOut === 'auth') {

== (169 điều kiện):
  1. [Dòng 120] if (bankId == 24) {
  2. [Dòng 163] if (!isNaN(_re.status) && (_re.status == '200'
  3. [Dòng 163] _re.status == '201') && _re.body != null) {
  4. [Dòng 168] if (('closed' == this._res_polling.state
  5. [Dòng 168] 'canceled' == this._res_polling.state
  6. [Dòng 168] 'expired' == this._res_polling.state
  7. [Dòng 168] 'paid' == this._res_polling.state)
  8. [Dòng 198] this._res_polling.payments == null) {
  9. [Dòng 200] this._res_polling.payments[this._res.payments.length - 1].state == 'pending') {
  10. [Dòng 202] this._paymentService.getCurrentPage() == 'enter_card') {
  11. [Dòng 205] } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res.payments.length - 1].state == 'approved'
  12. [Dòng 205] this._res_polling.payments[this._res.payments.length - 1].state == 'failed')
  13. [Dòng 295] if (count == null
  14. [Dòng 295] count == '') {
  15. [Dòng 463] this._auth == 0) {
  16. [Dòng 475] if (count == 2
  17. [Dòng 507] if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
  18. [Dòng 507] this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
  19. [Dòng 510] if (this._res.payments[this._res.payments.length - 1].state == 'approved'
  20. [Dòng 525] } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
  21. [Dòng 525] this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
  22. [Dòng 540] if ('paid' == this._res.state) {
  23. [Dòng 554] if (('closed' == this._res.state
  24. [Dòng 554] 'canceled' == this._res.state
  25. [Dòng 554] 'expired' == this._res.state
  26. [Dòng 554] 'paid' == this._res.state)
  27. [Dòng 571] this._res.payments == null) {
  28. [Dòng 573] this._res.payments[this._res.payments.length - 1].state == 'pending') {
  29. [Dòng 583] if (this._res.currencies[0] == 'USD') {
  30. [Dòng 706] if (_re.status == '200'
  31. [Dòng 706] _re.status == '201') {
  32. [Dòng 710] if (this._res_post.state == 'approved'
  33. [Dòng 710] this._res_post.state == 'failed') {
  34. [Dòng 713] } else if (this._res_post.state == 'authorization_required') {
  35. [Dòng 781] if (x == 1) {
  36. [Dòng 784] } else if (x == 2) {
  37. [Dòng 787] } else if (x == 3) {
  38. [Dòng 816] v.length == 15) || (v.length == 16
  39. [Dòng 816] v.length == 19))
  40. [Dòng 817] this._util.checkMod10(v) == true) {
  41. [Dòng 848] _val.value.length == 4) && _val.value.search('/') == -1) || (_val.value && _val.value.length == 5)) {
  42. [Dòng 848] _val.value.search('/') == -1) || (_val.value
  43. [Dòng 848] _val.value.length == 5)) {
  44. [Dòng 862] _val.value.length == 4) ||
  45. [Dòng 866] _val.value.length == 3)
  46. [Dòng 911] if (len == 0) { /* nothing, field is blank */
  47. [Dòng 961] if ((iTotal % 10) == 0) {
  48. [Dòng 999] cardNo.length == 15)
  49. [Dòng 1001] cardNo.length == 16)
  50. [Dòng 1002] cardNo.startsWith('81')) && (cardNo.length == 16
  51. [Dòng 1002] cardNo.length == 19))
  52. [Dòng 1004] this._util.checkMod10(cardNo) == true
  53. [Dòng 1024] if (expdate.length == 5) {
  54. [Dòng 1056] _formCard.csc.length == 4) ||
  55. [Dòng 1060] _formCard.csc.length == 3)
  56. [Dòng 1135] if ('otp_page' == valOut) {
  57. [Dòng 1138] if ('qr_timeout' == valOut) {
  58. [Dòng 1157] if (bankid == 2
  59. [Dòng 1157] bankid == 67) {
  60. [Dòng 1160] || (off && !this.enabledTwoBankTech && ((bankid == '2'
  61. [Dòng 1160] this.isOffTechcombank) || (bankid == '67'
  62. [Dòng 1278] if (item.b.id == '2'
  63. [Dòng 1278] item.b.id == '67') {
  64. [Dòng 1338] if (this._auth == 0
  65. [Dòng 1338] if (this._auth == 0 && (this._b == 1
  66. [Dòng 1338] this._b == 4
  67. [Dòng 1338] this._b == 7
  68. [Dòng 1338] this._b == 8
  69. [Dòng 1338] this._b == 9
  70. [Dòng 1338] this._b == 10
  71. [Dòng 1339] this._b == 11
  72. [Dòng 1339] this._b == 15
  73. [Dòng 1339] this._b == 16
  74. [Dòng 1339] this._b == 17
  75. [Dòng 1339] this._b == 20
  76. [Dòng 1339] this._b == 22
  77. [Dòng 1340] this._b == 23
  78. [Dòng 1340] this._b == 2
  79. [Dòng 1340] this._b == 24
  80. [Dòng 1340] this._b == 25
  81. [Dòng 1340] this._b == 30
  82. [Dòng 1340] this._b == 67)) {
  83. [Dòng 1352] if (this._b == 20) {//seabank
  84. [Dòng 1355] } else if (this._b == 9
  85. [Dòng 1355] this._b == 25) {//9vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  86. [Dòng 1577] if (this._b == 15
  87. [Dòng 1577] this._b == 67
  88. [Dòng 1577] this._b == 17) {
  89. [Dòng 1591] if (this._b == 1
  90. [Dòng 1591] this._b == 25) {
  91. [Dòng 1657] this._b == 20)) {
  92. [Dòng 1682] if ((cardNo.length == 16
  93. [Dòng 1682] cardNo.length == 19) && this._util.checkMod10(cardNo) == true && regex.test(cardNo)
  94. [Dòng 1691] if (bankId == 2) {
  95. [Dòng 1711] if (bankId == 1
  96. [Dòng 1711] bankId == 4
  97. [Dòng 1711] bankId == 7
  98. [Dòng 1711] bankId == 8
  99. [Dòng 1711] bankId == 9
  100. [Dòng 1711] bankId == 10
  101. [Dòng 1711] bankId == 11
  102. [Dòng 1711] bankId == 15
  103. [Dòng 1711] bankId == 16
  104. [Dòng 1712] bankId == 17
  105. [Dòng 1712] bankId == 20
  106. [Dòng 1712] bankId == 22
  107. [Dòng 1712] bankId == 23
  108. [Dòng 1712] bankId == 24
  109. [Dòng 1712] bankId == 25
  110. [Dòng 1712] bankId == 30
  111. [Dòng 1712] bankId == 33
  112. [Dòng 1712] bankId == 34
  113. [Dòng 1713] bankId == 35
  114. [Dòng 1713] bankId == 36
  115. [Dòng 1713] bankId == 37
  116. [Dòng 1713] bankId == 38
  117. [Dòng 1713] bankId == 39
  118. [Dòng 1713] bankId == 40
  119. [Dòng 1713] bankId == 41
  120. [Dòng 1713] bankId == 42
  121. [Dòng 1713] bankId == 43
  122. [Dòng 1714] bankId == 44
  123. [Dòng 1714] bankId == 45
  124. [Dòng 1714] bankId == 46
  125. [Dòng 1714] bankId == 47
  126. [Dòng 1714] bankId == 48
  127. [Dòng 1714] bankId == 49
  128. [Dòng 1714] bankId == 50
  129. [Dòng 1714] bankId == 51
  130. [Dòng 1715] bankId == 52
  131. [Dòng 1715] bankId == 53
  132. [Dòng 1715] bankId == 54
  133. [Dòng 1715] bankId == 55
  134. [Dòng 1715] bankId == 56
  135. [Dòng 1715] bankId == 57
  136. [Dòng 1715] bankId == 58
  137. [Dòng 1715] bankId == 59
  138. [Dòng 1715] bankId == 60
  139. [Dòng 1716] bankId == 61
  140. [Dòng 1716] bankId == 62
  141. [Dòng 1716] bankId == 63
  142. [Dòng 1716] bankId == 64
  143. [Dòng 1716] bankId == 65
  144. [Dòng 1716] bankId == 66
  145. [Dòng 1716] bankId == 68
  146. [Dòng 1716] bankId == 69
  147. [Dòng 1716] bankId == 70
  148. [Dòng 1717] bankId == 71
  149. [Dòng 1717] bankId == 72
  150. [Dòng 1717] bankId == 73
  151. [Dòng 1717] bankId == 32
  152. [Dòng 1717] || bankId == 71 || bankId == 72 || bankId == 73 || bankId == 32 || (this._b == 67
  153. [Dòng 1718] ||(bankId == 2
  154. [Dòng 1718] techcombank == 2
  155. [Dòng 1719] bankId == 74
  156. [Dòng 1719] bankId == 75) {
  157. [Dòng 1721] } else if (bankId == 6
  158. [Dòng 1721] bankId == 31
  159. [Dòng 1721] bankId == 80
  160. [Dòng 1721] } else if (bankId == 6 || bankId == 31 || bankId == 80 || (bankId == 2
  161. [Dòng 1723] } else if ((bankId == 2
  162. [Dòng 1723] bankId == 67) {
  163. [Dòng 1725] } else if (bankId == 3
  164. [Dòng 1725] bankId == 14
  165. [Dòng 1725] bankId == 18
  166. [Dòng 1725] bankId == 19
  167. [Dòng 1725] bankId == 27) {
  168. [Dòng 1727] } else if (bankId == 12) {
  169. [Dòng 1729] } else if (bankId == 5) {

!== (2 điều kiện):
  1. [Dòng 176] codeResponse.toString() !== '0') {
  2. [Dòng 1283] this.bankList = this.bankList.filter(item => item.b.id !== '67');

!= (73 điều kiện):
  1. [Dòng 157] if (this._idInvoice != null) {
  2. [Dòng 162] if (this._paymentService.getCurrentPage() != 'otp') {
  3. [Dòng 163] _re.body != null) {
  4. [Dòng 169] this._res_polling.links != null
  5. [Dòng 169] this._res_polling.links.merchant_return != null //
  6. [Dòng 198] } else if (this._res_polling.merchant != null
  7. [Dòng 198] this._res_polling.merchant_invoice_reference != null
  8. [Dòng 200] } else if (this._res_polling.payments != null
  9. [Dòng 206] this._res_polling.links.merchant_return != null//
  10. [Dòng 303] if (params['locale'] != null) {
  11. [Dòng 313] if (this._paymentService.getInvoiceDetail() != null) {
  12. [Dòng 315] this._res.merchant.id != 'OPTEST' ? this._res.on_off_bank : ''
  13. [Dòng 410] this.d_vrbank = (this._res.qr_data != null
  14. [Dòng 443] if (this._res.billing != null
  15. [Dòng 443] this._res.billing.address != null) {
  16. [Dòng 444] if (this._res.billing.address.city != null) {
  17. [Dòng 447] if (this._res.billing.address.line1 != null) {
  18. [Dòng 450] if (this._res.billing.address.postal_code != null) {
  19. [Dòng 453] if (this._res.billing.address.state != null) {
  20. [Dòng 456] if (this._res.billing.address.country_code != null) {
  21. [Dòng 480] if (count != 1) {
  22. [Dòng 507] this._res.links != null//
  23. [Dòng 525] } else if (this._res.payments != null
  24. [Dòng 526] this._res.payments[this._res.payments.length - 1].instrument != null
  25. [Dòng 526] this._res.payments[this._res.payments.length - 1].instrument.issuer != null
  26. [Dòng 527] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
  27. [Dòng 527] this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
  28. [Dòng 528] this._res.payments[this._res.payments.length - 1].links != null
  29. [Dòng 528] this._res.payments[this._res.payments.length - 1].links.cancel != null
  30. [Dòng 528] this._res.payments[this._res.payments.length - 1].links.cancel.href != null
  31. [Dòng 555] this._res.links != null
  32. [Dòng 555] this._res.links.merchant_return != null //
  33. [Dòng 571] } else if (this._res.merchant != null
  34. [Dòng 571] this._res.merchant_invoice_reference != null
  35. [Dòng 798] if (imgCard != null) {
  36. [Dòng 861] _val.value != null
  37. [Dòng 1055] _formCard.csc != null
  38. [Dòng 1068] if (!(!isNaN(_formCard.country) != null
  39. [Dòng 1073] if (!(_formCard.address != null
  40. [Dòng 1078] if (!(_formCard.province != null
  41. [Dòng 1093] if (this.current != 'card') {
  42. [Dòng 1106] if (this.current != 'date') {
  43. [Dòng 1121] if (this.current != 'csc') {
  44. [Dòng 1191] if (bankid != null) {
  45. [Dòng 1240] if (!(strInstrument != null
  46. [Dòng 1386] if (this._res_post != null
  47. [Dòng 1386] this._res_post.links != null
  48. [Dòng 1386] this._res_post.links.merchant_return != null
  49. [Dòng 1387] this._res_post.links.merchant_return.href != null) {
  50. [Dòng 1396] if (this.htmlDesc != null
  51. [Dòng 1413] if (this._b != 9
  52. [Dòng 1413] this._b != 11
  53. [Dòng 1413] this._b != 16
  54. [Dòng 1413] this._b != 17
  55. [Dòng 1413] this._b != 25) {
  56. [Dòng 1474] if (this.current_f1 != 'card') {
  57. [Dòng 1483] if (this.current_f1 != 'date') {
  58. [Dòng 1492] if (this.current_f1 != 'name') {
  59. [Dòng 1512] this._b != 25) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
  60. [Dòng 1548] if (this._res_post.return_url != null) {
  61. [Dòng 1551] if (this._res_post.links != null
  62. [Dòng 1557] if (this._res_post.authorization != null
  63. [Dòng 1557] this._res_post.authorization.links != null
  64. [Dòng 1557] this._res_post.authorization.links.approval != null) {
  65. [Dòng 1563] this._res_post.links.cancel != null) {
  66. [Dòng 1568] var userName = _formCard.name != null ? _formCard.name : ''
  67. [Dòng 1569] this._res_post.authorization.links.approval != null
  68. [Dòng 1569] this._res_post.authorization.links.approval.href != null) {
  69. [Dòng 1574] userName = myParam != null ? myParam : ''
  70. [Dòng 1636] this._b != 25) {//9vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
  71. [Dòng 1657] this._b != 20) || ((dCardExp.getTime() - dCurrent.getTime()) < 0
  72. [Dòng 1668] if (!(_formCard.name != null
  73. [Dòng 1721] techcombank != 2) || (bankId == 2

================================================================================

📁 FILE 33: off-bank-dialog.html
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/dien-may-xanh/off-bank-dialog/off-bank-dialog.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 34: off-bank-dialog.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/dien-may-xanh/off-bank-dialog/off-bank-dialog.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 35: policy-dialog.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/dien-may-xanh/policy-dialog/policy-dialog/policy-dialog.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 36: policy-dialog.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/dien-may-xanh/policy-dialog/policy-dialog/policy-dialog.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 37: format-carno-input.derective.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/directives/format-carno-input.derective.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 1 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 44] this.lastValue !== $event.target.value)) {

!= (1 điều kiện):
  1. [Dòng 23] if(value!=null){

================================================================================

📁 FILE 38: uppercase-input.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/directives/uppercase-input.directive.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 1 lần
   - != : 0 lần
--------------------------------------------------------------------------------

!== (1 điều kiện):
  1. [Dòng 23] this.lastValue !== $event.target.value)) {

================================================================================

📁 FILE 39: error.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/error/error.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 40: error.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/error/error.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 41: error.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/error/error.component.ts
📊 Thống kê: 1 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 26] if('timeout'==type) this.errorMess = this._translate.instant('tran_timeout');

================================================================================

📁 FILE 42: error_international.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/error_international/error_international.component.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 0 lần
   - == : 5 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (5 điều kiện):
  1. [Dòng 3] errorCode == '11'"
  2. [Dòng 99] <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
  3. [Dòng 99] (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
  4. [Dòng 101] <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
  5. [Dòng 101] !(errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending

================================================================================

📁 FILE 43: error_international.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/error_international/error_international.component.ts
📊 Thống kê: 25 điều kiện duy nhất
   - === : 8 lần
   - == : 13 lần
   - !== : 0 lần
   - != : 4 lần
--------------------------------------------------------------------------------

=== (8 điều kiện):
  1. [Dòng 116] params.timeout === 'true') {
  2. [Dòng 135] this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
  3. [Dòng 135] _re.body.state === 'unpaid');
  4. [Dòng 207] if (this.errorCode === 'overtime'
  5. [Dòng 207] this.errorCode === '253') {
  6. [Dòng 288] params.name === 'CUSTOMER_INTIME'
  7. [Dòng 288] params.code === '09') {
  8. [Dòng 306] if (this.timeLeft === 0) {

== (13 điều kiện):
  1. [Dòng 143] if (_re.body.currencies[0] == 'USD') {
  2. [Dòng 177] _re.body.themes.theme == 'general') {
  3. [Dòng 183] params.response_code == 'overtime') {
  4. [Dòng 230] if (_re.status == '200'
  5. [Dòng 230] _re.status == '201') {
  6. [Dòng 243] if (_re2.status == '200'
  7. [Dòng 243] _re2.status == '201') {
  8. [Dòng 256] if (this.errorCode == 'overtime'
  9. [Dòng 256] this.errorCode == '253') {
  10. [Dòng 259] } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
  11. [Dòng 264] _re.body.state == 'canceled') {
  12. [Dòng 282] if (lastPayment?.state == 'pending') {
  13. [Dòng 304] if (this.isTimePause == false) {

!= (4 điều kiện):
  1. [Dòng 361] if (_re.body != null
  2. [Dòng 361] _re.body.links != null
  3. [Dòng 361] _re.body.links.merchant_return != null
  4. [Dòng 362] _re.body.links.merchant_return.href != null) {

================================================================================

📁 FILE 44: format-date.directive.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/format-date.directive.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 45: format-date.directive.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/format-date.directive.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 4 lần
   - == : 0 lần
   - !== : 2 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (4 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0
  2. [Dòng 123] YY % 4 === 0)) {
  3. [Dòng 138] if (YYYY % 400 === 0
  4. [Dòng 138] YYYY % 4 === 0)) {

!== (2 điều kiện):
  1. [Dòng 123] if (YY % 400 === 0 || (YY % 100 !== 0
  2. [Dòng 138] if (YYYY % 400 === 0 || (YYYY % 100 !== 0

================================================================================

📁 FILE 46: main.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/main/main.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 47: main.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/main/main.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 48: main.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/main/main.component.ts
📊 Thống kê: 6 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 4 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 38] if ((dataPassed.status == '200'
  2. [Dòng 38] dataPassed.status == '201') && dataPassed.body != null) {

!= (4 điều kiện):
  1. [Dòng 33] if (this._idInvoice != null
  2. [Dòng 33] this._idInvoice != 0) {
  3. [Dòng 34] if (this._paymentService.getInvoiceDetail() != null) {
  4. [Dòng 38] dataPassed.body != null) {

================================================================================

📁 FILE 49: overlay.component.html
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/overlay/overlay.component.html
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 50: overlay.component.spec.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/overlay/overlay.component.spec.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 51: overlay.component.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/overlay/overlay.component.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 52: bank-amount.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/pipe/bank-amount.pipe.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 11] return ((a.id == id
  2. [Dòng 11] a.code == id) && a.type.includes(type));

================================================================================

📁 FILE 53: data.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/services/data.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 2 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 47] const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
  2. [Dòng 47] item.method === method) : null;

================================================================================

📁 FILE 54: payment.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/services/payment.service.ts
📊 Thống kê: 10 điều kiện duy nhất
   - === : 0 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 9 lần
--------------------------------------------------------------------------------

== (1 điều kiện):
  1. [Dòng 272] this._state == 'completed') return of([]);

!= (9 điều kiện):
  1. [Dòng 104] if (idInvoice != null
  2. [Dòng 104] idInvoice != 0)
  3. [Dòng 244] if (this._merchantid != null
  4. [Dòng 244] this._tranref != null
  5. [Dòng 244] this._state != null
  6. [Dòng 272] if (this._state != null
  7. [Dòng 280] } else if (idInvoice != null
  8. [Dòng 280] idInvoice != 0) {
  9. [Dòng 365] urlCancel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');

================================================================================

📁 FILE 55: index.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/translate/index.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 56: lang-en.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/translate/lang-en.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 57: lang-vi.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/translate/lang-vi.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 58: translate.pipe.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/translate/translate.pipe.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 59: translate.service.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/translate/translate.service.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 31] if(this.currentLang=='en') curr_lang='LANG_EN_NAME';
  2. [Dòng 32] else if(this.currentLang=='vi') curr_lang='LANG_VI_NAME';

================================================================================

📁 FILE 60: translations.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/translate/translations.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 61: banks-info.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/util/banks-info.ts
📊 Thống kê: 2 điều kiện duy nhất
   - === : 0 lần
   - == : 2 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

== (2 điều kiện):
  1. [Dòng 1195] if (+e.id == bankId) {
  2. [Dòng 1235] if (e.swiftCode == bankSwift) {

================================================================================

📁 FILE 62: util.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/app/util/util.ts
📊 Thống kê: 20 điều kiện duy nhất
   - === : 9 lần
   - == : 7 lần
   - !== : 2 lần
   - != : 2 lần
--------------------------------------------------------------------------------

=== (9 điều kiện):
  1. [Dòng 49] if (param === key) {
  2. [Dòng 61] if (v.length === 2
  3. [Dòng 61] this.flag.length === 3
  4. [Dòng 61] this.flag.charAt(this.flag.length - 1) === '/') {
  5. [Dòng 65] if (v.length === 1) {
  6. [Dòng 67] } else if (v.length === 2) {
  7. [Dòng 70] v.length === 2) {
  8. [Dòng 78] if (len === 2) {
  9. [Dòng 122] if (M[1] === 'Chrome') {

== (7 điều kiện):
  1. [Dòng 11] if (temp.length == 0) {
  2. [Dòng 18] return (counter % 10 == 0);
  3. [Dòng 96] if (this.checkCount == 1) {
  4. [Dòng 108] if (results == null) {
  5. [Dòng 139] d = d == undefined ? '.' : d
  6. [Dòng 140] t = t == undefined ? '
  7. [Dòng 313] return results == null ? null : results[1]

!== (2 điều kiện):
  1. [Dòng 44] queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
  2. [Dòng 45] if (queryString !== '') {

!= (2 điều kiện):
  1. [Dòng 124] if (tem != null) {
  2. [Dòng 129] if ((tem = ua.match(/version\/(\d+)/i)) != null) {

================================================================================

📁 FILE 63: qrcode.js
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/assets/script/qrcode.js
📊 Thống kê: 67 điều kiện duy nhất
   - === : 2 lần
   - == : 53 lần
   - !== : 0 lần
   - != : 12 lần
--------------------------------------------------------------------------------

=== (2 điều kiện):
  1. [Dòng 2255] if (typeof define === 'function'
  2. [Dòng 2257] } else if (typeof exports === 'object') {

== (53 điều kiện):
  1. [Dòng 68] if (_dataCache == null) {
  2. [Dòng 85] if ( (0 <= r && r <= 6 && (c == 0
  3. [Dòng 85] c == 6) )
  4. [Dòng 86] || (0 <= c && c <= 6 && (r == 0
  5. [Dòng 86] r == 6) )
  6. [Dòng 107] if (i == 0
  7. [Dòng 122] _modules[r][6] = (r % 2 == 0);
  8. [Dòng 129] _modules[6][c] = (c % 2 == 0);
  9. [Dòng 152] if (r == -2
  10. [Dòng 152] r == 2
  11. [Dòng 152] c == -2
  12. [Dòng 152] c == 2
  13. [Dòng 153] || (r == 0
  14. [Dòng 153] c == 0) ) {
  15. [Dòng 169] ( (bits >> i) & 1) == 1);
  16. [Dòng 226] if (col == 6) col -= 1;
  17. [Dòng 232] if (_modules[row][col - c] == null) {
  18. [Dòng 237] dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
  19. [Dòng 249] if (bitIndex == -1) {
  20. [Dòng 458] margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
  21. [Dòng 498] if (typeof arguments[0] == 'object') {
  22. [Dòng 587] margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
  23. [Dòng 733] if (b == -1) throw 'eof';
  24. [Dòng 741] if (b0 == -1) break;
  25. [Dòng 767] if (typeof b == 'number') {
  26. [Dòng 768] if ( (b & 0xff) == b) {
  27. [Dòng 910] return function(i, j) { return (i + j) % 2 == 0
  28. [Dòng 912] return function(i, j) { return i % 2 == 0
  29. [Dòng 914] return function(i, j) { return j % 3 == 0
  30. [Dòng 916] return function(i, j) { return (i + j) % 3 == 0
  31. [Dòng 918] return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
  32. [Dòng 920] return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
  33. [Dòng 922] return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
  34. [Dòng 924] return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
  35. [Dòng 1011] if (r == 0
  36. [Dòng 1011] c == 0) {
  37. [Dòng 1015] if (dark == qrcode.isDark(row + r, col + c) ) {
  38. [Dòng 1036] if (count == 0
  39. [Dòng 1036] count == 4) {
  40. [Dòng 1149] if (typeof num.length == 'undefined') {
  41. [Dòng 1155] num[offset] == 0) {
  42. [Dòng 1495] if (typeof rsBlock == 'undefined') {
  43. [Dòng 1538] return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
  44. [Dòng 1543] _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
  45. [Dòng 1599] if (data.length - i == 1) {
  46. [Dòng 1601] } else if (data.length - i == 2) {
  47. [Dòng 1866] } else if (n == 62) {
  48. [Dòng 1868] } else if (n == 63) {
  49. [Dòng 1928] if (_buflen == 0) {
  50. [Dòng 1937] if (c == '=') {
  51. [Dòng 1961] } else if (c == 0x2b) {
  52. [Dòng 1963] } else if (c == 0x2f) {
  53. [Dòng 2133] if (table.size() == (1 << bitLength) ) {

!= (12 điều kiện):
  1. [Dòng 119] if (_modules[r][6] != null) {
  2. [Dòng 126] if (_modules[6][c] != null) {
  3. [Dòng 144] if (_modules[row][col] != null) {
  4. [Dòng 365] while (buffer.getLengthInBits() % 8 != 0) {
  5. [Dòng 750] if (count != numChars) {
  6. [Dòng 751] throw count + ' != ' + numChars
  7. [Dòng 878] while (data != 0) {
  8. [Dòng 1733] if (test.length != 2
  9. [Dòng 1733] ( (test[0] << 8) | test[1]) != code) {
  10. [Dòng 1894] if (_length % 3 != 0) {
  11. [Dòng 2067] if ( (data >>> length) != 0) {
  12. [Dòng 2178] return typeof _map[key] != 'undefined'

================================================================================

📁 FILE 64: environment.prod.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/environments/environment.prod.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 65: environment.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/environments/environment.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 66: index.html
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/index.html
📊 Thống kê: 5 điều kiện duy nhất
   - === : 1 lần
   - == : 1 lần
   - !== : 0 lần
   - != : 3 lần
--------------------------------------------------------------------------------

=== (1 điều kiện):
  1. [Dòng 38] if(M[1]=== 'Chrome'){

== (1 điều kiện):
  1. [Dòng 49] if(checkIE[0]=='MSIE'

!= (3 điều kiện):
  1. [Dòng 27] document.documentMode!=null
  2. [Dòng 40] if(tem!= null) return tem.slice(1).join(' ').replace('OPR'
  3. [Dòng 43] if((tem= ua.match(/version\/(\d+)/i))!= null) M.splice(1

================================================================================

📁 FILE 67: karma.conf.js
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/karma.conf.js
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 68: main.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/main.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 69: polyfills.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/polyfills.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📁 FILE 70: test.ts
📍 Đường dẫn: /root/projects/onepay/paygate-dienmayxanh/src/test.ts
📊 Thống kê: 0 điều kiện duy nhất
   - === : 0 lần
   - == : 0 lần
   - !== : 0 lần
   - != : 0 lần
--------------------------------------------------------------------------------

❌ Không tìm thấy điều kiện so sánh nào trong file này.

================================================================================

📋 TÓM TẮT TẤT CẢ ĐIỀU KIỆN DUY NHẤT:
====================================================================================================

=== (87 điều kiện duy nhất):
------------------------------------------------------------
1. return lang === this._translate.currentLang
2. cardTypeOcean === 'ATM' ? 'pg_selected': 'pg_un_selected'"
3. cardTypeOcean === 'MB' ? 'pg_selected': 'pg_un_selected'"
4. cardTypeOcean === 'IB' ? 'pg_selected': 'pg_un_selected'"
5. cardTypeBank === 'bank_card_number' ? 'pg_selected': 'pg_un_selected'"
6. cardTypeBank === 'bank_account_number' ? 'pg_selected': 'pg_un_selected'"
7. cardTypeBank === 'bank_username' ? 'pg_selected': 'pg_un_selected'"
8. cardTypeBank === 'bank_customer_code' ? 'pg_selected': 'pg_un_selected'"
9. if (target.tagName === 'A'
10. if (isIE[0] === 'MSIE'
11. +isIE[1] === 10) {
12. if (this.cardTypeBank === 'bank_card_number') {
13. } else if (this.cardTypeBank === 'bank_account_number') {
14. } else if (this.cardTypeBank === 'bank_username') {
15. } else if (this.cardTypeBank === 'bank_customer_code') {
16. this.cardTypeBank === 'bank_card_number'
17. if (this.cardTypeOcean === 'IB') {
18. } else if (this.cardTypeOcean === 'MB') {
19. } else if (this.cardTypeOcean === 'ATM') {
20. if (_val.value === ''
21. _val.value === null
22. _val.value === undefined) {
23. this.cardTypeOcean === 'MB') {
24. this.cardTypeOcean === 'IB'
25. if ((this.cardTypeBank === 'bank_card_number'
26. if (event.keyCode === 8
27. event.key === "Backspace"
28. if (v.length === 2
29. this.flag.length === 3
30. this.flag.charAt(this.flag.length - 1) === '/') {
31. if (v.length === 1) {
32. } else if (v.length === 2) {
33. v.length === 2) {
34. if (len === 2) {
35. this.cardTypeBank === 'bank_account_number') {//check rieng voi shb
36. if ((this._b == 27) && ((this.cardTypeBank === 'bank_card_number'
37. (this.cardTypeBank === 'bank_account_number'
38. if ((this._b == 3) && ((this.cardTypeBank === 'bank_card_number'
39. if ((this._b == 12) && ((this.cardTypeBank === 'bank_card_number'
40. (this.cardTypeBank === 'bank_customer_code'
41. cardNo.length === 0)))) {
42. if ((this._b == 19) && ((this.cardTypeBank === 'bank_card_number'
43. (this.cardTypeBank === 'bank_username'
44. if ((this.cardTypeBank === 'bank_account_number'
45. this.cardTypeBank === 'bank_username'
46. this.cardTypeBank === 'bank_customer_code') && cardNo.length !== 0) {
47. this.cardTypeOcean === 'ATM')
48. || (this.cardTypeOcean === 'IB'
49. if (valIn === 1) {
50. } else if (valIn === 2) {
51. } else if (valIn === 3) {
52. } else if (valIn === 4) {
53. if ((v.substr(-1) === ' '
54. if (approval.method === 'REDIRECT') {
55. } else if (approval.method === 'POST_REDIRECT') {
56. if (valIn === 2) {
57. if (this.cardTypeBank === 'bank_account_number'
58. if (valIn === 1) { //bank_card_number
59. } else if (valIn === 2) { //bank_account_number
60. if ((this._b == 5) && ((this.cardTypeBank === 'bank_card_number'
61. if (count === 1) {
62. if (offBanksArr[i] === this.lastDomescard) {
63. if (this._res.state === 'unpaid'
64. this._res.state === 'not_paid') {
65. if (valid.indexOf(temp) === -1) {
66. if (this.filterText.toUpperCase() === item.b.name.toUpperCase().substring(0, this.filterText.length)
67. this.filterText.toUpperCase() === item.b.code.toUpperCase().substring(0, this.filterText.length)) {
68. if (valOut === 'auth') {
69. params.timeout === 'true') {
70. this.rePayment = _re && _re.body && _re.body.state && (_re.body.state === 'not_paid'
71. _re.body.state === 'unpaid');
72. if (this.errorCode === 'overtime'
73. this.errorCode === '253') {
74. params.name === 'CUSTOMER_INTIME'
75. params.code === '09') {
76. if (this.timeLeft === 0) {
77. if (YY % 400 === 0
78. YY % 4 === 0)) {
79. if (YYYY % 400 === 0
80. YYYY % 4 === 0)) {
81. const reasonConfig = Array.isArray(this.response_map) ? this.response_map.find(item => item.reason === reason
82. item.method === method) : null;
83. if (param === key) {
84. if (M[1] === 'Chrome') {
85. if (typeof define === 'function'
86. } else if (typeof exports === 'object') {
87. if(M[1]=== 'Chrome'){

== (490 điều kiện duy nhất):
------------------------------------------------------------
1. 'vi'==params['locale']){
2. 'en'==params['locale']){
3. if (_re.status == '200'
4. _re.status == '201') {
5. if ((_val.value.length == 16
6. _val.value.length == 19) && this.checkMod10(_val.value) == true) {
7. this.checkMod10(_val.value) == true) {
8. _val.value.length == 4) && _val.value.search('/') == -1) || (_val.value && _val.value.length == 5)) {
9. _val.value.search('/') == -1) || (_val.value
10. _val.value.length == 5)) {
11. if (temp.length == 0) {
12. return (counter % 10 == 0);
13. if (this._res.state == 'approved'
14. this._res.state == 'failed') {
15. } else if (this._res.state == 'authorization_required') {
16. if (!((_formCard.card_number.length == 16
17. _formCard.card_number.length == 19) && this.checkMod10(_formCard.card_number) == true && _formCard.card_number.startsWith('970425'))) {
18. this.checkMod10(_formCard.card_number) == true
19. if (expdate.length == 5) {
20. if (this._b == 8) {//MB Bank
21. if (this._b == 18) {//Oceanbank
22. if (this._b == 8) {
23. if (this._b == 18) {
24. if (this._b == 12) { //SHB
25. if (this._b == 18) {//18-oceanbank
26. if (this._b == 12) {
27. <div [ngClass]="(this._b == 19
28. this._b == 12
29. this._b == 18
30. this._b == 27
31. this._b == 3 ? 'dmx-form-card margin-top-62' : 'dmx-form-card'"
32. this._b == 14
33. this._b == 3 ? 'd_text_err top-60' : 'd_text_err'"
34. if (this._b == 19) {//19BIDV
35. } else if (this._b == 3
36. this._b == 27) {// 3vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
37. } else if (this._b == 12) {// 12SHB
38. } else if (this._b == 14) {// 14VPB
39. } else if (this._b == 18) { //18Oceanbank-ocb
40. if (this._b == 14) {//** reset check hiển thị theo card date 14-vpbank ; update để mặc định hiển thị date
41. if (this._b == 19
42. this._b == 3
43. this._b == 12) {
44. } else if (this._b == 18) {
45. if (this.checkBin(_val.value) && (this._b == 14)) {
46. if (this._b == 18
47. this.cardTypeOcean == 'ATM') {
48. if ((this._b == 3
49. this._b == 14)) {//** check đúng số thẻ 3-tpb;27-pvcombank;14-vpbank chuyển qua ô nhập date
50. } else if (this._b == 18
51. this._b == 18)) {
52. event.inputType == 'deleteContentBackward') {
53. if (event.target.name == 'exp_date'
54. event.inputType == 'insertCompositionText') {
55. _val.value.length == 4) && _val.value.search('/') == -1) || (_val.value && _val.value.length == 5)) && this._util.checkValidMonthYear(_val.value)) {
56. _val.value.length == 5)) && this._util.checkValidMonthYear(_val.value)) {
57. valueDate.length == 4) && valueDate.search('/') == -1) || (valueDate && valueDate.length == 5))
58. valueDate.search('/') == -1) || (valueDate
59. valueDate.length == 5))
60. || (((this._b == 20
61. this._b == 33
62. this._b == 39
63. this._b == 43
64. this._b == 45
65. this._b == 73
66. this._b == 64
67. this._b == 67) && this._util.checkValidMonthYearSeaBank(valueDate)))))
68. if (this.current != 'card' && (this._b == 3
69. this._b == 14) && this.d_card_demo) {
70. if (this.current != 'date' && (this._b == 3
71. if (this.current != 'name' && (this._b == 3
72. if (this.current != 'phone' && (this._b == 14) && this.d_card_demo) {
73. if (this._b == 3) {
74. } else if (this._b == 14) {
75. } else if (this._b == 19) {
76. } else if (this._b == 27) {
77. } else if (this._b == 12
78. _formCard.exp_date.length == 5
79. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 27
80. this._b == 14)) {//27-pvcombank;3-TPB ;14-VPB
81. if (_formCard.name != null && _formCard.name.length > 0 && (this._b == 3
82. this._b == 19
83. this._b == 27)) {//3-TPB ;19-BIDV;27-pvcombank;
84. if (this._b == 14) {//;14-VPB thêm số phone
85. if (this._b == 18) {//;18-Oceanbank thêm số auth method ;
86. if (this.cardTypeOcean == 'IB') {
87. } else if (this.cardTypeOcean == 'MB') {
88. } else if (this.cardTypeOcean == 'ATM') {
89. if (this._res_post.state == 'approved'
90. this._res_post.state == 'failed') {
91. } else if (this._res_post.state == 'authorization_required') {
92. if (this._b == 27
93. this._b == 14) {
94. if ((!this.validCardNumber(cardNo) && this.d_card_demo && (this._b == 3
95. this._b == 14))
96. || (this._b == 18
97. this.cardTypeOcean == 'ATM'
98. this.cardTypeOcean == 'IB'
99. this.cardTypeOcean == 'MB'
100. if ((this._b == 27) && ((this.cardTypeBank === 'bank_card_number' && (!this.validCardNumber(cardNo) || !this.checkMod10(cardNo)))
101. if ((this._b == 3) && ((this.cardTypeBank === 'bank_card_number' && (!this.validCardNumber(cardNo) || !this.checkMod10(cardNo)))
102. if ((this._b == 12) && ((this.cardTypeBank === 'bank_card_number' && (!this.validCardNumber(cardNo) || !this.checkMod10(cardNo)))
103. (this.cardTypeBank === 'bank_customer_code' && (cardNo == null
104. if ((this._b == 19) && ((this.cardTypeBank === 'bank_card_number' && (!this.validCardNumber(cardNo) || !this.checkMod10(cardNo)))
105. (this.cardTypeBank === 'bank_username' && (cardNo == null
106. if (this.d_card_demo && (this._b == 14
107. this._b == 3)) {
108. if ((_formCard && _formCard.exp_date) && ((_formCard.exp_date.length == 4
109. _formCard.exp_date.search('/') == -1)
110. _formCard.exp_date.length == 5) && this._util.checkValidMonthYear(_formCard.exp_date)) {
111. if (!this._util.validateCardFullName(this.cardName) && (this._b == 19
112. if (!this._util.validateCardFullName(this.cardName) && (this._b == 19 || ((this._b == 3
113. this._b == 14) && this.d_card_demo))) {
114. if ((cardNo.length == 16
115. if ((cardNo.length == 16 || (cardNo.length == 19
116. && ((this._b == 18
117. cardNo.length == 19) || this._b != 18)
118. if (this._b == +e.id) {
119. if (valIn == 1) {
120. } else if (valIn == 2) {
121. if (this._b == 3
122. this._b == 27) {
123. if (cardTypeOcean == 'IB') {
124. } else if (cardTypeOcean == 'MB') {
125. } else if (cardTypeOcean == 'ATM') {
126. if (item['id'] == this._b.toString()) {
127. if ('POST_REDIRECT' == this._res_post.authorization.links.approval.method) {
128. } else if ('REDIRECT' == this._res_post.authorization.links.approval.method) {
129. v.length == 15) || (v.length == 16
130. v.length == 19))
131. this._util.checkMod10(v) == true) {
132. this._i_exp_date.length == 4
133. this._i_exp_date.search('/') == -1)
134. this._i_exp_date.length == 5))
135. csc.length == 4) ||
136. csc.length == 3)
137. cardNo.length == 15)
138. cardNo.length == 16)
139. cardNo.startsWith('81'))&& (cardNo.length == 16
140. cardNo.length == 19))
141. _val.value.length == 4) && _val.value.search('/') == -1) || (_val.value && _val.value.length == 5)) && this._util.checkValidMonthYearSeaBank(_val.value)) {
142. _val.value.length == 5)) && this._util.checkValidMonthYearSeaBank(_val.value)) {
143. if (((this._i_exp_date.length == 4
144. this._i_exp_date.search('/') == -1) || this._i_exp_date.length == 5)
145. this._i_exp_date.length == 5)
146. v.length == 5) {
147. v.length == 4
148. v.length == 3)
149. _val.value.length == 4
150. _val.value.length == 3)
151. [ngClass]="(this._b == 19
152. this._b == 67
153. this._b == 2
154. cardTypeBank == 'bank_card_number'"
155. _b==33||_b==35||_b==38||_b==39||_b==42||_b==45
156. _b==40"
157. _b==34||_b==37
158. _b==36||_b==41||_b==43
159. _b==44||_b==46||_b==72
160. <div *ngIf="!(cardTypeBank == 'bank_card_number')" class="" style="clear: both; padding-top:15px;color:#888888;font-size: 14px;">{{'txt_redirect_to' | translate }} Techcombank {{'txt_to_pay' | translate }}</div>
161. <div *ngIf="!(cardTypeBank == 'bank_card_number')" class="text_desc" [innerHTML]="htmlDesc" style="float: left;clear: both;color:#888888"></div>
162. <div *ngIf="(cardTypeBank == 'bank_card_number')" class="text_desc" innerHTML="{{'67_html_desc' | translate}}" style="float: left;clear: both;color:#888888"></div>
163. _val.value.length == 4) && _val.value.search('/') == -1) || (_val.value && _val.value.length == 5))
164. _val.value.length == 5))
165. this._b == 67) && this._util.checkValidMonthYearSeaBank(_val.value))))) {
166. this._b == 72) && this._util.checkValidMonthYearSeaBank(valueDate)))))
167. if (this.cardTypeBank == 'bank_account_number') {
168. if (this._b == 2) {
169. if (this.cardTypeBank == 'bank_card_number') {
170. _formCard.exp_date.length == 5) {//27-pvcombank;3-TPB ;14-VPB
171. if (this._b == 1
172. this._b == 15
173. this._b == 24
174. this._b == 8
175. this._b == 10
176. this._b == 20
177. this._b == 22
178. this._b == 23
179. this._b == 30
180. this._b == 11
181. this._b == 17) {
182. if(this._res_post.state == 'failed'){
183. expdate.length == 5) {
184. if (((dCardExp.getTime() - dCurrent.getTime()) > 0 && this._b != 20 && this._b != 2 && this._b != 33 && this._b != 39 && this._b != 43 && this._b != 45 && this._b != 72) || ((dCardExp.getTime() - dCurrent.getTime()) < 0 && (this._b == 20
185. this._b == 72))) {
186. if (cardNo && (cardNo.length == 16
187. cardNo.length == 19)
188. this._b == 12? 'dmx-form-card margin-top-62' : 'dmx-form-card'"
189. this._b == 12 ? 'd_text_err top-60' : 'd_text_err'"
190. if (this._b == 12) {// shb
191. if (this._b == 12) { // 12 shb
192. if (this._b == 12) {//** check đúng số thẻ shb chuyển qua ô nhập date
193. if (this._b == 12) { // 5 shb
194. if ((this._b == 12)) {//** check đúng số thẻ 12 shb chuyển qua ô nhập date
195. valueDate.length == 4) && valueDate.search('/') == -1)
196. valueDate.search('/') == -1)
197. ((this._b == 20
198. this._b == 67) ?
199. if (this.current != 'card' && (this._b == 12) && this.d_card_demo) {
200. if (this.current != 'date' && (this._b == 12) && this.d_card_demo) {
201. if (this.current != 'name' && (this._b == 12) && this.d_card_demo) {
202. } else if (this.cardTypeBank == 'bank_card_number') {
203. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 12)) { //12 shb
204. if (_formCard.name != null && _formCard.name.length > 0 && (this._b == 12)) { //12 shb
205. if (this.d_card_demo && (this._b == 12)) {
206. if (!this._util.validateCardFullName(this.cardName) && (this._b == 12) && this.d_card_demo) {
207. (cardNo.length == 16
208. (cardNo.length == 16 || (cardNo.length == 19
209. } else if (this._b == 5) {
210. } else if (this._b == 6) {
211. transactionFlow=='internet_banking'"
212. transactionFlow=='bank_account'"
213. this._b == 5? 'dmx-form-card margin-top-62' : 'dmx-form-card'"
214. this._b == 5 ? 'd_text_err top-60' : 'd_text_err'"
215. if (this._b == 5) {
216. if (this._b == 5) {// vib
217. if (this._b == 5) { // 5 vib
218. if (this._b == 5) {//** check đúng số thẻ vib chuyển qua ô nhập date
219. if ((this._b == 5)) {//** check đúng số thẻ 5 vib chuyển qua ô nhập date
220. valueDate.length == 5)
221. (this._b == 20
222. if (this.current != 'card' && (this._b == 5) && this.d_card_demo) {
223. if (this.current != 'date' && (this._b == 5) && this.d_card_demo) {
224. if (this.current != 'name' && (this._b == 5) && this.d_card_demo) {
225. if (_formCard.exp_date != null && _formCard.exp_date.length == 5 && (this._b == 5)) { //5 vib
226. if (_formCard.name != null && _formCard.name.length > 0 && (this._b == 5)) { //5 vib
227. if ((this._b == 5) && ((this.cardTypeBank === 'bank_card_number' && (!this.validCardNumber(cardNo) || !this.checkMod10(cardNo)))
228. if (this.d_card_demo && (this._b == 5)) {
229. if (!this._util.validateCardFullName(this.cardName) && (this._b == 5) && this.d_card_demo) {
230. _b==33||_b==35||_b==38||_b==39||_b==42||_b==45 
231. _b==40 "
232. _b==44||this._b==46||this._b==72 || this._b==73
233. this._b==46
234. this._b==72
235. this._b==73"
236. _b==74"
237. _b==59"
238. _b==30 || _b==71
239. _b==55 || _b==57
240. _b==68"
241. return this._b == 9
242. this._b == 16
243. this._b == 17
244. this._b == 25
245. this._b == 44
246. this._b == 57
247. this._b == 59
248. this._b == 61
249. this._b == 63
250. this._b == 69
251. this._b == 9) {
252. return this._b == 11
253. this._b == 72
254. this._b == 68
255. this._b == 36
256. this._b == 74
257. this._b == 75
258. if (this.locale == 'en') {
259. if (name == 'MAFC')
260. if (bankId == 3
261. bankId == 61
262. bankId == 8
263. bankId == 49
264. bankId == 48
265. bankId == 10
266. bankId == 53
267. bankId == 17
268. bankId == 65
269. bankId == 23
270. bankId == 52
271. bankId == 27
272. bankId == 66
273. bankId == 9
274. bankId == 54
275. bankId == 37
276. bankId == 38
277. bankId == 39
278. bankId == 40
279. bankId == 42
280. bankId == 44
281. bankId == 72
282. bankId == 59
283. bankId == 51
284. bankId == 64
285. bankId == 58
286. bankId == 56
287. bankId == 55
288. bankId == 60
289. bankId == 68
290. bankId == 74
291. bankId == 75
292. !token && _auth==0 && vietcombankGroupSelected
293. !token && _auth==0 && techcombankGroupSelected
294. !token && _auth==0 && bankaccountGroupSelected
295. !token && _auth==0 && shbGroupSelected
296. !token && _auth==0 && onepaynapasGroupSelected
297. !token && _auth==0 && vibGroupSelected
298. _auth==1)">
299. if (bankId == 24) {
300. if (!isNaN(_re.status) && (_re.status == '200'
301. _re.status == '201') && _re.body != null) {
302. if (('closed' == this._res_polling.state
303. 'canceled' == this._res_polling.state
304. 'expired' == this._res_polling.state
305. 'paid' == this._res_polling.state)
306. this._res_polling.payments == null) {
307. this._res_polling.payments[this._res.payments.length - 1].state == 'pending') {
308. this._paymentService.getCurrentPage() == 'enter_card') {
309. } else if (this._res_polling.payments != null && (this._res_polling.payments[this._res.payments.length - 1].state == 'approved'
310. this._res_polling.payments[this._res.payments.length - 1].state == 'failed')
311. if (count == null
312. count == '') {
313. this._auth == 0) {
314. if (count == 2
315. if ((this._res.payments[this._res.payments.length - 1].state == 'approved'
316. this._res.payments[this._res.payments.length - 1].state == 'failed') && this._res.links != null// && !this._checkRedirect
317. if (this._res.payments[this._res.payments.length - 1].state == 'approved'
318. } else if (this._res.payments != null && (this._res.payments[this._res.payments.length - 1].state == 'authorization_required'
319. this._res.payments[this._res.payments.length - 1].state == 'more_info_required')
320. if ('paid' == this._res.state) {
321. if (('closed' == this._res.state
322. 'canceled' == this._res.state
323. 'expired' == this._res.state
324. 'paid' == this._res.state)
325. this._res.payments == null) {
326. this._res.payments[this._res.payments.length - 1].state == 'pending') {
327. if (this._res.currencies[0] == 'USD') {
328. if (x == 1) {
329. } else if (x == 2) {
330. } else if (x == 3) {
331. _val.value.length == 4) ||
332. if (len == 0) { /* nothing, field is blank */
333. if ((iTotal % 10) == 0) {
334. cardNo.startsWith('81')) && (cardNo.length == 16
335. this._util.checkMod10(cardNo) == true
336. _formCard.csc.length == 4) ||
337. _formCard.csc.length == 3)
338. if ('otp_page' == valOut) {
339. if ('qr_timeout' == valOut) {
340. if (bankid == 2
341. bankid == 67) {
342. || (off && !this.enabledTwoBankTech && ((bankid == '2'
343. this.isOffTechcombank) || (bankid == '67'
344. if (item.b.id == '2'
345. item.b.id == '67') {
346. if (this._auth == 0
347. if (this._auth == 0 && (this._b == 1
348. this._b == 4
349. this._b == 7
350. this._b == 9
351. this._b == 67)) {
352. if (this._b == 20) {//seabank
353. } else if (this._b == 9
354. this._b == 25) {//9vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
355. if (this._b == 15
356. this._b == 25) {
357. this._b == 20)) {
358. cardNo.length == 19) && this._util.checkMod10(cardNo) == true && regex.test(cardNo)
359. if (bankId == 2) {
360. if (bankId == 1
361. bankId == 4
362. bankId == 7
363. bankId == 11
364. bankId == 15
365. bankId == 16
366. bankId == 20
367. bankId == 22
368. bankId == 24
369. bankId == 25
370. bankId == 30
371. bankId == 33
372. bankId == 34
373. bankId == 35
374. bankId == 36
375. bankId == 41
376. bankId == 43
377. bankId == 45
378. bankId == 46
379. bankId == 47
380. bankId == 50
381. bankId == 57
382. bankId == 62
383. bankId == 63
384. bankId == 69
385. bankId == 70
386. bankId == 71
387. bankId == 73
388. bankId == 32
389. || bankId == 71 || bankId == 72 || bankId == 73 || bankId == 32 || (this._b == 67
390. ||(bankId == 2
391. techcombank == 2
392. bankId == 75) {
393. } else if (bankId == 6
394. bankId == 31
395. bankId == 80
396. } else if (bankId == 6 || bankId == 31 || bankId == 80 || (bankId == 2
397. } else if ((bankId == 2
398. bankId == 67) {
399. } else if (bankId == 3
400. bankId == 14
401. bankId == 18
402. bankId == 19
403. bankId == 27) {
404. } else if (bankId == 12) {
405. } else if (bankId == 5) {
406. if('timeout'==type) this.errorMess = this._translate.instant('tran_timeout');
407. errorCode == '11'"
408. <div class="col-md-12 d_btn_cancel_select" *ngIf="(errorCode == 'overtime'
409. (errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
410. <a (click)="leaveNow()" class="leave_now" *ngIf="!(errorCode == 'overtime'
411. !(errorCode == 'overtime' || errorCode == '253') && !invoiceNearExpire && !paymentPending
412. if (_re.body.currencies[0] == 'USD') {
413. _re.body.themes.theme == 'general') {
414. params.response_code == 'overtime') {
415. if (_re2.status == '200'
416. _re2.status == '201') {
417. if (this.errorCode == 'overtime'
418. this.errorCode == '253') {
419. } else if (this._paymentService.getLastPaymentMethod() == "international") { // ENH_20250423_065
420. _re.body.state == 'canceled') {
421. if (lastPayment?.state == 'pending') {
422. if (this.isTimePause == false) {
423. if ((dataPassed.status == '200'
424. dataPassed.status == '201') && dataPassed.body != null) {
425. return ((a.id == id
426. a.code == id) && a.type.includes(type));
427. this._state == 'completed') return of([]);
428. if(this.currentLang=='en') curr_lang='LANG_EN_NAME';
429. else if(this.currentLang=='vi') curr_lang='LANG_VI_NAME';
430. if (+e.id == bankId) {
431. if (e.swiftCode == bankSwift) {
432. if (this.checkCount == 1) {
433. if (results == null) {
434. d = d == undefined ? '.' : d
435. t = t == undefined ? '
436. return results == null ? null : results[1]
437. if (_dataCache == null) {
438. if ( (0 <= r && r <= 6 && (c == 0
439. c == 6) )
440. || (0 <= c && c <= 6 && (r == 0
441. r == 6) )
442. if (i == 0
443. _modules[r][6] = (r % 2 == 0);
444. _modules[6][c] = (c % 2 == 0);
445. if (r == -2
446. r == 2
447. c == -2
448. c == 2
449. || (r == 0
450. c == 0) ) {
451. ( (bits >> i) & 1) == 1);
452. if (col == 6) col -= 1;
453. if (_modules[row][col - c] == null) {
454. dark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);
455. if (bitIndex == -1) {
456. margin = (typeof margin == 'undefined')? cellSize * 4 : margin;
457. if (typeof arguments[0] == 'object') {
458. margin = (typeof margin == 'undefined')? cellSize * 2 : margin;
459. if (b == -1) throw 'eof';
460. if (b0 == -1) break;
461. if (typeof b == 'number') {
462. if ( (b & 0xff) == b) {
463. return function(i, j) { return (i + j) % 2 == 0
464. return function(i, j) { return i % 2 == 0
465. return function(i, j) { return j % 3 == 0
466. return function(i, j) { return (i + j) % 3 == 0
467. return function(i, j) { return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0
468. return function(i, j) { return (i * j) % 2 + (i * j) % 3 == 0
469. return function(i, j) { return ( (i * j) % 2 + (i * j) % 3) % 2 == 0
470. return function(i, j) { return ( (i * j) % 3 + (i + j) % 2) % 2 == 0
471. if (r == 0
472. c == 0) {
473. if (dark == qrcode.isDark(row + r, col + c) ) {
474. if (count == 0
475. count == 4) {
476. if (typeof num.length == 'undefined') {
477. num[offset] == 0) {
478. if (typeof rsBlock == 'undefined') {
479. return ( (_buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1
480. _this.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);
481. if (data.length - i == 1) {
482. } else if (data.length - i == 2) {
483. } else if (n == 62) {
484. } else if (n == 63) {
485. if (_buflen == 0) {
486. if (c == '=') {
487. } else if (c == 0x2b) {
488. } else if (c == 0x2f) {
489. if (table.size() == (1 << bitLength) ) {
490. if(checkIE[0]=='MSIE'

!== (15 điều kiện duy nhất):
------------------------------------------------------------
1. codeResponse.toString() !== '0') {
2. this._b !== 18) || (this.cardTypeOcean === 'ATM'
3. this._util.getElementParams(url_redirect, 'vpc_TxnResponseCode').toString() !== '0') {
4. cardNo.length !== 0) {
5. key !== '8') {
6. event.inputType !== 'deleteContentBackward') || v.length == 5) {
7. this.tracking[id].charAt(this.tracking[id].length - 1) !== 't')) {
8. this.tracking['1'].charAt(this.tracking[id].length - 1) !== 'p')) {
9. this._b !== 18)) {
10. this.bankList = this.bankList.filter(item => item.b.id !== '67');
11. this.lastValue !== $event.target.value)) {
12. if (YY % 400 === 0 || (YY % 100 !== 0
13. if (YYYY % 400 === 0 || (YYYY % 100 !== 0
14. queryString = (sourceURL.indexOf('?') !== -1) ? sourceURL.split('?')[1] : '';
15. if (queryString !== '') {

!= (172 điều kiện duy nhất):
------------------------------------------------------------
1. if(params['locale']!=null
2. }else if(params['locale']!=null
3. if(userLang!=null
4. if (params['locale'] != null) {
5. if (this._res != null
6. this._res.links != null
7. this._res.links.merchant_return != null
8. this._res.links.merchant_return.href != null) {
9. if (this.current != 'card') {
10. if (this.current != 'date') {
11. if (this.current != 'name') {
12. if (this._res.authorization != null
13. this._res.authorization.links != null
14. if (this._res.links != null
15. this._res.links.cancel != null) {
16. if (!(_formCard.name != null
17. if (!(_formCard.otp != null
18. if (!(_formCard.password != null
19. } else if (this._b != 18) {
20. if (this.htmlDesc != null
21. if (ua.indexOf('safari') != -1
22. if (_val.value != null) {
23. if (this.valueDate.length != 3) {
24. && ((this._b != 20
25. this._b != 33
26. this._b != 39
27. this._b != 43
28. this._b != 45
29. this._b != 73
30. if (this.current != 'card'
31. if (this.current != 'date'
32. if (this.current != 'name'
33. if (this.current != 'phone'
34. if (_formCard.exp_date != null
35. if (_formCard.name != null
36. if (this._res_post.links != null
37. this._res_post.links.merchant_return != null
38. this._res_post.links.merchant_return.href != null) {
39. if (this._res_post.authorization != null
40. this._res_post.authorization.links != null
41. this._res_post.authorization.links.approval != null) {
42. this._res_post.links.cancel != null) {
43. if (!(_formCard.card_number != null
44. (this.cardTypeBank === 'bank_account_number' && !(cardNo != null
45. this._b != 27
46. this._b != 12
47. this._b != 3)) && this.checkMod10(cardNo) && this.checkBin(cardNo)
48. this._b != 18)
49. if (dataPassed.body != null) {
50. if (this._res_post.return_url != null) {
51. } else if (this._res_post.links != null
52. csc != null
53. cardNo != null
54. if (this._i_exp_date.length != 3) {
55. v != null
56. this.c_csc = (!(_val.value != null
57. if (this._res_post != null
58. this._res_post.links != null
59. if (this._b != 9
60. this._b != 11
61. this._b != 16
62. this._b != 17
63. this._b != 25
64. this._b != 36
65. this._b != 44) {
66. this._b != 2
67. this._b != 72)
68. var userName = _formCard.name != null ? _formCard.name : ''
69. this._res_post.authorization.links.approval != null
70. this._res_post.authorization.links.approval.href != null) {
71. userName = paramUserName != null ? paramUserName : ''
72. this._b != 44) {//9vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
73. if (expdate != null
74. this._b != 20
75. this._b != 72) || ((dCardExp.getTime() - dCurrent.getTime()) < 0
76. if (!(formCardName != null
77. this._res.authorization.links.approval != null) {
78. this._b != 3))
79. this._b != 44
80. this._b != 57
81. this._b != 59
82. this._b != 61
83. this._b != 63
84. this._b != 69) {
85. if (value != null) {
86. this._b != 69) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
87. '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75'].indexOf(this._b.toString()) != -1) {
88. this._b != 69) {//9vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
89. if (this._idInvoice != null) {
90. if (this._paymentService.getCurrentPage() != 'otp') {
91. _re.body != null) {
92. this._res_polling.links != null
93. this._res_polling.links.merchant_return != null //
94. } else if (this._res_polling.merchant != null
95. this._res_polling.merchant_invoice_reference != null
96. } else if (this._res_polling.payments != null
97. this._res_polling.links.merchant_return != null//
98. if (this._paymentService.getInvoiceDetail() != null) {
99. this._res.merchant.id != 'OPTEST' ? this._res.on_off_bank : ''
100. this.d_vrbank = (this._res.qr_data != null
101. if (this._res.billing != null
102. this._res.billing.address != null) {
103. if (this._res.billing.address.city != null) {
104. if (this._res.billing.address.line1 != null) {
105. if (this._res.billing.address.postal_code != null) {
106. if (this._res.billing.address.state != null) {
107. if (this._res.billing.address.country_code != null) {
108. if (count != 1) {
109. this._res.links != null//
110. } else if (this._res.payments != null
111. this._res.payments[this._res.payments.length - 1].instrument != null
112. this._res.payments[this._res.payments.length - 1].instrument.issuer != null
113. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand != null
114. this._res.payments[this._res.payments.length - 1].instrument.issuer.brand.id != null
115. this._res.payments[this._res.payments.length - 1].links != null
116. this._res.payments[this._res.payments.length - 1].links.cancel != null
117. this._res.payments[this._res.payments.length - 1].links.cancel.href != null
118. this._res.links.merchant_return != null //
119. } else if (this._res.merchant != null
120. this._res.merchant_invoice_reference != null
121. if (imgCard != null) {
122. _val.value != null
123. _formCard.csc != null
124. if (!(!isNaN(_formCard.country) != null
125. if (!(_formCard.address != null
126. if (!(_formCard.province != null
127. if (this.current != 'csc') {
128. if (bankid != null) {
129. if (!(strInstrument != null
130. this._b != 25) {
131. if (this.current_f1 != 'card') {
132. if (this.current_f1 != 'date') {
133. if (this.current_f1 != 'name') {
134. this._b != 25) {//9vieta;11exim;16sacom-scb;17nama;25-saigonbank(scb)
135. userName = myParam != null ? myParam : ''
136. this._b != 25) {//9vietabank;11Exim;16Sacombank-scb;17NAB-namabank;25saigonbank(scb);
137. this._b != 20) || ((dCardExp.getTime() - dCurrent.getTime()) < 0
138. techcombank != 2) || (bankId == 2
139. if(value!=null){
140. if (_re.body != null
141. _re.body.links != null
142. _re.body.links.merchant_return != null
143. _re.body.links.merchant_return.href != null) {
144. if (this._idInvoice != null
145. this._idInvoice != 0) {
146. dataPassed.body != null) {
147. if (idInvoice != null
148. idInvoice != 0)
149. if (this._merchantid != null
150. this._tranref != null
151. this._state != null
152. if (this._state != null
153. } else if (idInvoice != null
154. idInvoice != 0) {
155. urlCancel = this._url_cancel_webtest + (idInvoice != null ? idInvoice : '');
156. if (tem != null) {
157. if ((tem = ua.match(/version\/(\d+)/i)) != null) {
158. if (_modules[r][6] != null) {
159. if (_modules[6][c] != null) {
160. if (_modules[row][col] != null) {
161. while (buffer.getLengthInBits() % 8 != 0) {
162. if (count != numChars) {
163. throw count + ' != ' + numChars
164. while (data != 0) {
165. if (test.length != 2
166. ( (test[0] << 8) | test[1]) != code) {
167. if (_length % 3 != 0) {
168. if ( (data >>> length) != 0) {
169. return typeof _map[key] != 'undefined'
170. document.documentMode!=null
171. if(tem!= null) return tem.slice(1).join(' ').replace('OPR'
172. if((tem= ua.match(/version\/(\d+)/i))!= null) M.splice(1

