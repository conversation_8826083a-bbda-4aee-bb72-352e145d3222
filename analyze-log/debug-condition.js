const fs = require('fs');

// Test với điều kiện cụ thể
const testContent = `
=== (100 điều kiện):
1. some condition === 'value'
2. countryCode == 'US' ? US_STATES
3. another condition === 'test'
`;

console.log('Testing condition extraction...');

// Regex để match điều kiện: số. điều_kiện
const conditionRegex = /^\s*\d+\.\s*(.+)$/gm;
let match;

while ((match = conditionRegex.exec(testContent)) !== null) {
    const conditionLine = match[1].trim();
    console.log(`\nFound line: "${conditionLine}"`);
    
    // Tìm operator trong điều kiện (bao gồm cả ternary operator)
    const operatorMatch = conditionLine.match(/(.+?)\s*(===|!==|==|!=)\s*(.+?)(\s*\?\s*.+)?$/);
    if (operatorMatch) {
        const leftOperand = operatorMatch[1].trim();
        const operator = operatorMatch[2];
        let rightOperand = operatorMatch[3].trim();
        const ternaryPart = operatorMatch[4] || '';
        
        console.log(`  Left: "${leftOperand}"`);
        console.log(`  Operator: "${operator}"`);
        console.log(`  Right: "${rightOperand}"`);
        console.log(`  Ternary: "${ternaryPart}"`);
        
        // Nếu có ternary operator, gộp vào rightOperand
        if (ternaryPart) {
            rightOperand = rightOperand + ternaryPart;
            console.log(`  Final Right: "${rightOperand}"`);
        }
        
        const condition = `${leftOperand} ${operator} ${rightOperand}`;
        console.log(`  ✅ Final condition: "${condition}"`);
    } else {
        console.log(`  ❌ No operator match`);
    }
}

// Test với nội dung thực từ file log
console.log('\n' + '='.repeat(50));
console.log('Testing with real log content...');

const logFile = 'all-logs/log-paygate-token-20250818-181230.txt';
if (fs.existsSync(logFile)) {
    const content = fs.readFileSync(logFile, 'utf8');
    
    // Tìm phần tổng hợp cuối file
    const summaryMarkers = [
        '=== (',
        '== (',
        '!== (',
        '!= ('
    ];
    
    let summaryStart = -1;
    for (const marker of summaryMarkers) {
        const index = content.lastIndexOf(marker);
        if (index > summaryStart) {
            summaryStart = index;
        }
    }
    
    console.log(`Summary start index: ${summaryStart}`);
    
    if (summaryStart > 0) {
        const summaryContent = content.substring(summaryStart);
        console.log(`Summary content length: ${summaryContent.length}`);
        
        // Tìm điều kiện countryCode
        const countryCodeLines = summaryContent.split('\n').filter(line => 
            line.includes('countryCode') && line.includes('US')
        );
        
        console.log(`Found ${countryCodeLines.length} countryCode lines:`);
        countryCodeLines.forEach((line, index) => {
            console.log(`  ${index + 1}. "${line.trim()}"`);
        });
    }
} else {
    console.log(`File not found: ${logFile}`);
}
