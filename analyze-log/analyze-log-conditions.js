const fs = require('fs');
const path = require('path');

/**
 * Class để phân tích và merge điều kiện so sánh từ các file log
 * Đ<PERSON>c tất cả file log trong folder all-logs, tìm các điều kiện duy nhất
 * và liệt kê các file mà mỗi điều kiện xuất hiện
 */
class ConditionMerger {
    constructor(logDirectory = './all-logs', outputDirectory = null) {
        this.logDirectory = path.resolve(logDirectory);
        this.outputDirectory = outputDirectory || path.resolve(__dirname, '..', 'update_sheet');

        // Map: điều kiện chuẩn hóa -> thông tin chi tiết
        this.conditionMap = new Map();

        // Thống kê
        this.stats = {
            totalFiles: 0,
            totalConditions: 0,
            uniqueConditions: 0,
            operatorCounts: {
                '===': 0,
                '==': 0,
                '!==': 0,
                '!=': 0
            }
        };
    }

    /**
     * T<PERSON><PERSON> tất cả file log trong thư mục
     */
    findLogFiles() {
        try {
            const files = fs.readdirSync(this.logDirectory);
            return files
                .filter(file => file.startsWith('log-') && file.endsWith('.txt'))
                .map(file => path.join(this.logDirectory, file));
        } catch (error) {
            console.error(`Lỗi khi đọc thư mục ${this.logDirectory}: ${error.message}`);
            return [];
        }
    }

    /**
     * Trích xuất tên project từ tên file log
     */
    extractProjectName(fileName) {
        const baseName = path.basename(fileName);
        const match = baseName.match(/^log-(.+)-\d{8}-\d{6}\.txt$/);
        return match ? match[1] : 'unknown';
    }

    /**
     * Chuẩn hóa điều kiện để merge
     */
    normalizeCondition(condition) {
        return condition
            .replace(/\s+/g, ' ')  // Chuẩn hóa khoảng trắng
            .trim();
    }

    /**
     * Trích xuất điều kiện từ nội dung file log
     * Chỉ đọc phần tổng hợp cuối file, bỏ qua phần chi tiết
     */
    extractConditions(content, fileName) {
        const conditions = [];

        // Tìm phần tổng hợp cuối file
        const summaryMarkers = [
            '=== (',
            '== (',
            '!== (',
            '!= ('
        ];

        let summaryStart = -1;
        for (const marker of summaryMarkers) {
            const index = content.lastIndexOf(marker);
            if (index > summaryStart) {
                summaryStart = index;
            }
        }

        // Nếu không tìm thấy phần tổng hợp, tìm từ cuối file
        if (summaryStart === -1) {
            const lines = content.split('\n');
            const totalLines = lines.length;
            summaryStart = content.indexOf(lines[Math.max(0, totalLines - 1000)]);
        }

        const summaryContent = content.substring(summaryStart);

        // Tách thành các dòng và xử lý
        const lines = summaryContent.split('\n');

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            // Match điều kiện: số. điều_kiện
            const conditionMatch = line.match(/^\s*\d+\.\s*(.+)$/);
            if (conditionMatch) {
                let conditionLine = conditionMatch[1].trim();

                // Kiểm tra dòng tiếp theo có phải là phần tiếp theo của ternary operator không
                if (i + 1 < lines.length) {
                    const nextLine = lines[i + 1].trim();
                    if (nextLine.startsWith(':')) {
                        // Gộp dòng tiếp theo vào điều kiện hiện tại
                        conditionLine += ' ' + nextLine;
                        i++; // Bỏ qua dòng tiếp theo
                    }
                }

                // Tìm operator trong điều kiện
                const operatorMatch = conditionLine.match(/(.+?)\s*(===|!==|==|!=)\s*(.+)/);
                if (operatorMatch) {
                    const leftOperand = operatorMatch[1].trim();
                    const operator = operatorMatch[2];
                    const rightOperand = operatorMatch[3].trim();

                    // Kiểm tra tính hợp lệ của operands
                    if (this.isValidOperand(leftOperand) && this.isValidOperand(rightOperand)) {
                        const condition = `${leftOperand} ${operator} ${rightOperand}`;
                        conditions.push({
                            condition: this.normalizeCondition(condition),
                            operator: operator,
                            left: leftOperand,
                            right: rightOperand,
                            original: conditionLine,
                            fileName: fileName
                        });
                    }
                }
            }
        }

        return conditions;
    }

    /**
     * Kiểm tra tính hợp lệ của operand
     */
    isValidOperand(operand) {
        if (!operand || operand.length === 0) return false;

        // Loại bỏ những operand rõ ràng không hợp lệ
        const invalidPatterns = [
            /^[{};,\s]*$/,             // Chỉ chứa ký tự phân cách
            /^[&|]{2,}$/,              // Chỉ chứa && hoặc ||
            /^\s*$/,                   // Chỉ chứa khoảng trắng
            /^[=!<>]+$/                // Chỉ chứa operators
        ];

        if (invalidPatterns.some(pattern => pattern.test(operand))) {
            return false;
        }

        // Chấp nhận hầu hết các operand có ít nhất 1 ký tự có nghĩa
        // Bao gồm cả ternary operator với dấu ?
        return /[a-zA-Z0-9_$'"\(\)\[\].-?]/.test(operand);
    }

    /**
     * Xử lý một file log
     */
    processLogFile(filePath) {
        try {
            const fileName = path.basename(filePath);
            console.log(`Đang xử lý: ${fileName}`);

            const content = fs.readFileSync(filePath, 'utf8');
            const projectName = this.extractProjectName(filePath);
            const conditions = this.extractConditions(content, fileName);

            console.log(`  → Tìm thấy ${conditions.length} điều kiện (Project: ${projectName})`);

            // Merge điều kiện vào map chung
            conditions.forEach(conditionObj => {
                const normalizedCondition = conditionObj.condition;

                if (!this.conditionMap.has(normalizedCondition)) {
                    this.conditionMap.set(normalizedCondition, {
                        operator: conditionObj.operator,
                        left: conditionObj.left,
                        right: conditionObj.right,
                        files: new Set(),
                        projects: new Set(),
                        count: 0
                    });
                }

                const conditionInfo = this.conditionMap.get(normalizedCondition);
                conditionInfo.files.add(fileName);
                conditionInfo.projects.add(projectName);
                conditionInfo.count++;

                // Cập nhật thống kê
                this.stats.operatorCounts[conditionObj.operator]++;
            });

            this.stats.totalConditions += conditions.length;
            return true;

        } catch (error) {
            console.error(`Lỗi khi xử lý file ${filePath}: ${error.message}`);
            return false;
        }
    }

    /**
     * Xử lý tất cả file log
     */
    processAllLogs() {
        console.log(`Tìm kiếm file log trong: ${this.logDirectory}`);
        
        const logFiles = this.findLogFiles();
        if (logFiles.length === 0) {
            console.error('Không tìm thấy file log nào!');
            return false;
        }
        
        console.log(`Tìm thấy ${logFiles.length} file log\n`);
        
        let successCount = 0;
        logFiles.forEach(filePath => {
            if (this.processLogFile(filePath)) {
                successCount++;
            }
        });
        
        this.stats.totalFiles = successCount;
        this.stats.uniqueConditions = this.conditionMap.size;
        
        console.log(`\nĐã xử lý thành công ${successCount}/${logFiles.length} file`);
        return successCount > 0;
    }

    /**
     * Tạo tên file output với timestamp trong thư mục update_sheet
     */
    generateOutputFileName() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');

        const fileName = `unique-conditions-${year}${month}${day}-${hours}${minutes}${seconds}.txt`;

        // Đảm bảo thư mục output tồn tại
        if (!fs.existsSync(this.outputDirectory)) {
            try {
                fs.mkdirSync(this.outputDirectory, { recursive: true });
                console.log(`📁 Đã tạo thư mục: ${this.outputDirectory}`);
            } catch (error) {
                console.log(`⚠️  Không thể tạo thư mục ${this.outputDirectory}: ${error.message}`);
                console.log(`📁 Sử dụng thư mục hiện tại thay thế`);
                return fileName;
            }
        }

        return path.join(this.outputDirectory, fileName);
    }

    /**
     * Ghi kết quả ra file với danh sách file cho mỗi điều kiện
     */
    writeResults() {
        try {
            const outputFileName = this.generateOutputFileName();
            console.log(`\nĐang ghi kết quả vào file: ${outputFileName}`);

            let content = '';

            // Header
            content += '='.repeat(80) + '\n';
            content += 'PHÂN TÍCH ĐIỀU KIỆN DUY NHẤT TỪ CÁC FILE LOG\n';
            content += '='.repeat(80) + '\n';
            content += `Thư mục log: ${this.logDirectory}\n`;
            content += `Thư mục kết quả: ${this.outputDirectory}\n`;
            content += `Thời gian: ${new Date().toLocaleString('vi-VN')}\n`;
            content += `Tổng số file xử lý: ${this.stats.totalFiles}\n`;
            content += `Tổng số điều kiện: ${this.stats.totalConditions}\n`;
            content += `Điều kiện duy nhất: ${this.stats.uniqueConditions}\n\n`;

            // Thống kê theo operator
            content += 'THỐNG KÊ THEO LOẠI TOÁN TỬ:\n';
            content += '-'.repeat(50) + '\n';
            Object.entries(this.stats.operatorCounts).forEach(([op, count]) => {
                content += `${op}: ${count} lần\n`;
            });
            content += '-'.repeat(50) + '\n\n';

            // Ghi tất cả điều kiện theo operator với danh sách file
            ['===', '==', '!==', '!='].forEach(operator => {
                const conditions = Array.from(this.conditionMap.entries())
                    .filter(([, info]) => info.operator === operator)
                    .sort(([a], [b]) => a.localeCompare(b));

                if (conditions.length > 0) {
                    content += `${operator.toUpperCase()} (${conditions.length} điều kiện duy nhất):\n`;
                    content += '-'.repeat(80) + '\n';

                    conditions.forEach(([condition, info], index) => {
                        const files = Array.from(info.files).sort().join(', ');
                        content += `${index + 1}. ${condition}\n`;
                        content += `   Files: ${files}\n\n`;
                    });
                }
            });

            fs.writeFileSync(outputFileName, content, 'utf8');
            console.log(`✅ Đã ghi thành công vào file: ${outputFileName}`);

            return outputFileName;

        } catch (error) {
            console.error(`❌ Lỗi khi ghi file: ${error.message}`);
            return null;
        }
    }

    /**
     * Chạy toàn bộ quá trình phân tích
     */
    run() {
        console.log('🚀 Bắt đầu phân tích điều kiện duy nhất từ file log...\n');

        if (this.processAllLogs()) {
            const outputFile = this.writeResults();
            if (outputFile) {
                console.log('\n🎉 Hoàn thành thành công!');
                console.log(`📊 Thống kê:`);
                console.log(`   - File xử lý: ${this.stats.totalFiles}`);
                console.log(`   - Tổng điều kiện: ${this.stats.totalConditions}`);
                console.log(`   - Điều kiện duy nhất: ${this.stats.uniqueConditions}`);
                console.log(`   - Tỷ lệ trùng lặp: ${((this.stats.totalConditions - this.stats.uniqueConditions) / this.stats.totalConditions * 100).toFixed(1)}%`);
                console.log(`📄 File kết quả: ${outputFile}`);
                return true;
            }
        }

        console.log('\n❌ Quá trình thực hiện thất bại!');
        return false;
    }
}

// Chạy script khi được gọi trực tiếp
if (require.main === module) {
    // Đường dẫn tới thư mục all-logs (cùng cấp với file này)
    const logDirectory = path.join(__dirname, 'all-logs');

    // Đường dẫn tới thư mục update_sheet (ở cấp cha)
    const outputDirectory = path.join(__dirname, '..', 'update_sheet');

    const analyzer = new ConditionMerger(logDirectory, outputDirectory);
    analyzer.run();
}

module.exports = ConditionMerger;
