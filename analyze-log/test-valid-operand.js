// Test script để kiểm tra isValidOperand
const fs = require('fs');
const path = require('path');

// Copy class từ analyze-log-conditions.js
class ConditionAnalyzer {
    isValidOperand(operand) {
        if (!operand || operand.length === 0) return false;

        const patterns = [
            /^-?\d+(\.\d+)?$/,                    // Số
            /^'[^']*'$/,                          // String với dấu ngoặc đơn
            /^"[^"]*"$/,                          // String với dấu ngoặc kép
            /^[a-zA-Z_$][a-zA-Z0-9_$]*(\.[a-zA-Z_$][a-zA-Z0-9_$]*)*$/, // Identifier/property
            /^(true|false|null|undefined)$/,      // Literals
            /^this\.[a-zA-Z_$][a-zA-Z0-9_$.]*$/,  // this.property
            /^'undefined'$/,                      // String 'undefined' với dấu ngoặc đơn
            /^"undefined"$/,                      // String "undefined" với dấu ngoặc kép
            // Patterns mở rộng cho expressions phức tạp
            /^[a-zA-Z_$][a-zA-Z0-9_$]*(\.[a-zA-Z_$][a-zA-Z0-9_$]*)*\([^)]*\)(\.[a-zA-Z_$][a-zA-Z0-9_$]*)*$/, // method calls
            /^this\.[a-zA-Z_$][a-zA-Z0-9_$.]*\([^)]*\)(\.[a-zA-Z_$][a-zA-Z0-9_$]*)*$/, // this.method()
            /^[a-zA-Z_$][a-zA-Z0-9_$]*(\.[a-zA-Z_$][a-zA-Z0-9_$]*)*\[[^\]]*\](\.[a-zA-Z_$][a-zA-Z0-9_$]*)*$/, // array access
            /^this\.[a-zA-Z_$][a-zA-Z0-9_$.]*\[[^\]]*\](\.[a-zA-Z_$][a-zA-Z0-9_$]*)*$/, // this.array[index]
            /^[a-zA-Z_$][a-zA-Z0-9_$]*(\.[a-zA-Z_$][a-zA-Z0-9_$]*)*\([^)]*\)\[[^\]]*\]$/, // method().property
            /^[a-zA-Z_$][a-zA-Z0-9_$]*(\.[a-zA-Z_$][a-zA-Z0-9_$]*)*\[[^\]]*\]\([^)]*\)$/, // array[index].method()
            // Expressions với phép toán đơn giản
            /^[a-zA-Z_$][a-zA-Z0-9_$]*(\.[a-zA-Z_$][a-zA-Z0-9_$]*)*\s*[-+]\s*\d+$/, // obj.prop - 1
            /^this\.[a-zA-Z_$][a-zA-Z0-9_$.]*\s*[-+]\s*\d+$/  // this.prop - 1
        ];

        return patterns.some(pattern => pattern.test(operand));
    }
}

const analyzer = new ConditionAnalyzer();

// Test cases
const testCases = [
    "this.flag.charAt(this.flag.length - 1)",
    "'/'",
    "this.flag.length",
    "3",
    "this.flag.charAt",
    "this.flag.length - 1"
];

console.log('Testing isValidOperand:');
console.log('='.repeat(50));

testCases.forEach((test, index) => {
    const isValid = analyzer.isValidOperand(test);
    console.log(`${index + 1}. "${test}" → ${isValid ? '✅ VALID' : '❌ INVALID'}`);
});
